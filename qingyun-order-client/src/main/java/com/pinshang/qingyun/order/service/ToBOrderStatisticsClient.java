package com.pinshang.qingyun.order.service;

import com.github.pagehelper.PageInfo;
import com.pinshang.qingyun.base.configure.FeignClientConfiguration;
import com.pinshang.qingyun.base.constant.ApplicationNameConstant;
import com.pinshang.qingyun.order.dto.tob.*;
import com.pinshang.qingyun.order.hystrix.ToBOrderStatisticsClientHystrix;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import java.util.List;


@FeignClient(value = ApplicationNameConstant.QINGYUN_ORDER_SERVICE, fallbackFactory = ToBOrderStatisticsClientHystrix.class, configuration = FeignClientConfiguration.class)
public interface ToBOrderStatisticsClient {
    /**
     * 数据修复
     * @param repairIDTOList
     * @return
     */
    @RequestMapping(value = "/tob/order/web/repairData",method = RequestMethod.POST)
    void repairData(@RequestBody List<ToBOrderStatisticsRepairIDTO> repairIDTOList);


    /**
     * 查询订单数量
     * @param idto
     */
    @RequestMapping(value = "/tob/order/web/queryOrderStatisticsCount",method = RequestMethod.POST)
    Integer queryOrderStatisticsCount(@RequestBody ToBQueryOrderStatisticsIDTO idto);

    /**
     * 查询订单数据
     * @param idto
     * @return
     */
    @RequestMapping(value = "/tob/order/web/queryOrderStatisticsList",method = RequestMethod.POST)
    PageInfo<ToBQueryOrderStatisticsODTO> queryOrderStatisticsList(@RequestBody ToBQueryOrderStatisticsIDTO idto);


    /**
     * 查询B端订单数据和提货卡数据
     * @param dataIDTO
     * @return
     */
    @RequestMapping(value = "/tob/order/web/queryToBOrderAndTakeAppointmentData",method = RequestMethod.POST)
    ToBOrderAndTakeAppointmentDataODTO queryToBOrderAndTakeAppointmentData(@RequestBody ToBOrderAndTakeAppointmentDataIDTO dataIDTO);

}
