package com.pinshang.qingyun.order.service;

import com.github.pagehelper.PageInfo;
import com.pinshang.qingyun.base.configure.FeignClientConfiguration;
import com.pinshang.qingyun.base.constant.ApplicationNameConstant;
import com.pinshang.qingyun.order.dto.commodity.CommodityPurchaseStatusIDTO;
import com.pinshang.qingyun.order.dto.purchaseStatus.ShopCommodityModifyPurchaseStatusIDTO;
import com.pinshang.qingyun.order.dto.purchaseStatus.ShopCommodityPurchaseStatusIDTO;
import com.pinshang.qingyun.order.dto.purchaseStatus.ShopCommodityPurchaseStatusODTO;
import com.pinshang.qingyun.order.dto.shop.*;
import com.pinshang.qingyun.order.hystrix.ShopCommodityPurchaseStatusHystrix;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

@FeignClient(value = ApplicationNameConstant.QINGYUN_ORDER_SERVICE, fallbackFactory = ShopCommodityPurchaseStatusHystrix.class, configuration = FeignClientConfiguration.class)
public interface ShopCommodityPurchaseStatusClient {
    /**
     * 修改门店商品是否可采状态
     * @param commodityPurchaseStatusList
     * @return
     */
    @RequestMapping(value = "/shopCommodityPurchaseStatus/updateShopCommodityPurchaseStatus", method = RequestMethod.POST)
    public int updateShopCommodityPurchaseStatus(@RequestBody List<CommodityPurchaseStatusIDTO> commodityPurchaseStatusList);

    /**
     * 保存门店商品是否可采状态
     * @param commodityPurchaseStatusList
     * @return
     */
    @RequestMapping(value = "/shopCommodityPurchaseStatus/saveShopCommodityPurchaseStatus", method = RequestMethod.POST)
    public int saveShopCommodityPurchaseStatus(@RequestBody List<CommodityPurchaseStatusIDTO> commodityPurchaseStatusList);

    @RequestMapping(value = "/shopCommodityPurchaseStatus/selectShopQtOrderProduct", method = RequestMethod.POST)
    public List<ShopQtOrderProductODTO> selectShopQtOrderProduct(@RequestBody ShopQtOrderProductIDTO shopQtOrderProductIDTO);

//    @RequestMapping(value = "/shopCommodityPurchaseStatus/selectProductPriceModel", method = RequestMethod.POST)
//    public List<PriceModelLogODTO> selectProductPriceModel(@RequestBody PriceModelLogSearchIDTO idto);

    @RequestMapping(value = "/shopCommodityPurchaseStatus/selectProductPriceModelListByPriceModeClode",method = RequestMethod.GET)
    public List<CommodityPriceODTO> selectProductPriceModelListByPriceModeClode(@RequestParam("priceModeClode") String priceModeClode);

    @RequestMapping(value = "/shopCommodityPurchaseStatus/selectCommodityByProductPriceModelId",method = RequestMethod.GET)
    List<CommodityListODTO> selectCommodityByProductPriceModelId(@RequestParam("productPriceModelId") Long productPriceModelId);

    @RequestMapping(value = "/shopCommodityPurchaseStatus/priceModelLogList", method = RequestMethod.POST)
    PageInfo<PriceModelLogODTO> list(@RequestBody PriceModelLogSearchIDTO idto);

    @RequestMapping(value = "/shopCommodityPurchaseStatus/selectXsShopCommodityPurchase", method = RequestMethod.POST)
    public List<XsShopCommodityPurchaseStatusODTO> selectXsShopCommodityPurchaseStatus(XsShopCommodityPurchaseStatusIDTO idto);

    @RequestMapping(value = "/shopCommodityPurchaseStatus/selectShopCommodityPurchaseStatusList", method = RequestMethod.POST)
    public PageInfo<ShopCommodityPurchaseStatusODTO> selectShopCommodityPurchaseStatusList(@RequestBody ShopCommodityPurchaseStatusIDTO shopCommodityPurchaseStatusIDTO);

    @RequestMapping(value = "/shopCommodityPurchaseStatus/modifyShopCommodityPurchaseStatusList", method = RequestMethod.POST)
    public Integer modifyShopCommodityPurchaseStatusList(@RequestBody ShopCommodityModifyPurchaseStatusIDTO shopCommodityModifyPurchaseStatusIDTO);
}
