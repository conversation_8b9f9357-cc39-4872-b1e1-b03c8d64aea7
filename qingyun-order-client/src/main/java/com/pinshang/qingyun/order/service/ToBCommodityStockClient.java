package com.pinshang.qingyun.order.service;

import com.github.pagehelper.PageInfo;
import com.pinshang.qingyun.base.configure.FeignClientConfiguration;
import com.pinshang.qingyun.base.constant.ApplicationNameConstant;
import com.pinshang.qingyun.order.dto.tob.*;
import com.pinshang.qingyun.order.hystrix.ToBCommodityStockClientHystrix;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;


@FeignClient(value = ApplicationNameConstant.QINGYUN_ORDER_SERVICE, fallbackFactory = ToBCommodityStockClientHystrix.class, configuration = FeignClientConfiguration.class)
public interface ToBCommodityStockClient {
    /**
     * 数据修复
     * @param repairIDTOList
     * @return
     */
    @RequestMapping(value = "/tob/order/stock/stockRepair",method = RequestMethod.POST)
    void stockRepair(@RequestBody List<ToBStockRepairIDTO> repairIDTOList);


    /**
     * 查询库存数量
     * @return
     */
    @RequestMapping(value = "/tob/order/stock/queryStockCount",method = RequestMethod.GET)
    Integer queryStockCount();


    /**
     * 查询库存数据
     * @return
     */
    @RequestMapping(value = "/tob/order/stock/queryStockList",method = RequestMethod.GET)
    PageInfo<ToBStockRepairODTO> queryStockList(@RequestParam(value = "pageNo") Integer pageNo, @RequestParam(value = "pageSize") Integer pageSize);


    /**
     * xda es查询库存类型下的商品id
     * @param stockType
     * @return
     */
    @ApiOperation("xda es查询大仓库存")
    @RequestMapping(value = "/tob/order/stock/selectCommodityIdByStockType", method = RequestMethod.GET)
    List<Long> selectCommodityIdByStockType(@RequestParam("stockType") Integer stockType);

    /**
     * xda es查询大仓库存
     * @param idto
     * @return
     */
    @RequestMapping(value = "/tob/order/stock/queryCommodityInventory", method = RequestMethod.POST)
    @ApiOperation("查询大仓库存")
    List<EsXdaCommoditySoldOutODTO> queryCommodityInventory(@RequestBody EsXdaCommoditySoldOutIDTO idto);


    /**
     * 鲜达app详情页时候，刷新库存（xda-product调用）
     * @param idto
     * @return
     */
    @RequestMapping(value = "/tob/order/stock/updateXdaCommodityStock", method = RequestMethod.POST)
    @ApiOperation("鲜达app详情页时候，刷新库存")
    Boolean updateXdaCommodityStock(@RequestBody TobCommodityStockIDTO idto);
}
