package com.pinshang.qingyun.order.service;

import com.github.pagehelper.PageInfo;
import com.pinshang.qingyun.base.configure.FeignClientConfiguration;
import com.pinshang.qingyun.base.constant.ApplicationNameConstant;
import com.pinshang.qingyun.order.dto.orderStatistics.OrderMirrorSyncODTO;
import com.pinshang.qingyun.order.dto.orderStatistics.OrderStatisticsMonitorIDTO;
import com.pinshang.qingyun.order.dto.orderStatistics.OrderStatisticsODTO;
import com.pinshang.qingyun.order.dto.orderStatistics.OrderSyncODTO;
import com.pinshang.qingyun.order.hystrix.OrderStatisticsMonitorClientHystrix;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import java.util.List;

/**
 * 统计查询--订单监控
 */
@FeignClient(value = ApplicationNameConstant.QINGYUN_ORDER_SERVICE, fallbackFactory = OrderStatisticsMonitorClientHystrix.class, configuration = FeignClientConfiguration.class)
public interface OrderStatisticsMonitorClient {
    //订单比较
    @RequestMapping(value = "/orderStatisticsMonitor/queryOrderStatisticsInfo", method = RequestMethod.POST)
    OrderStatisticsODTO queryOrderStatisticsInfo(@RequestBody OrderStatisticsMonitorIDTO idto);

    //差异订单
    @RequestMapping(value = "/orderStatisticsMonitor/queryOrderDiffList", method = RequestMethod.POST)
    List<String> queryOrderDiffList(@RequestBody OrderStatisticsMonitorIDTO idto);

    //订单同步
    @RequestMapping(value = "/orderStatisticsMonitor/queryOrderSyncList", method = RequestMethod.POST)
    PageInfo<OrderSyncODTO> queryOrderSyncList(@RequestBody OrderStatisticsMonitorIDTO idto);

    //订单落地数据补偿同步
    @RequestMapping(value = "/orderStatisticsMonitor/queryOrderMirrorSyncList", method = RequestMethod.POST)
    PageInfo<OrderMirrorSyncODTO> queryOrderMirrorSyncList(@RequestBody OrderStatisticsMonitorIDTO idto);

}
