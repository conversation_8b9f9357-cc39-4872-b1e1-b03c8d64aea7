package com.pinshang.qingyun.order.service;

import com.github.pagehelper.PageInfo;
import com.pinshang.qingyun.base.configure.FeignClientConfiguration;
import com.pinshang.qingyun.base.constant.ApplicationNameConstant;
import com.pinshang.qingyun.order.dto.shop.*;
import com.pinshang.qingyun.order.hystrix.MdShopOrderSettingClientHystrix;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@FeignClient(value = ApplicationNameConstant.QINGYUN_ORDER_SERVICE, fallbackFactory = MdShopOrderSettingClientHystrix.class, configuration = FeignClientConfiguration.class)
public interface MdShopOrderSettingClient {

    /**
     * 根据商品IDList，通用客户id -1 查询门店订货通用设置
     * @return
     */
    @RequestMapping(value = "/mdShopOrderSetting/queryMdShopOrderSettingListByIds", method = RequestMethod.POST)
    public List<MdShopOrderSettingODTO> queryMdShopOrderSettingListByIds(@RequestBody List<String> commodityIds);

    /**
     * 根据商品IDList，门店类型 查询门店订货通用设置
     * @return
     */
    @RequestMapping(value = "/mdShopOrderSetting/queryMdShopOrderSettingListByShopType", method = RequestMethod.POST)
    public List<MdShopOrderSettingODTO> queryMdShopOrderSettingListByShopType(@RequestBody MdShopOrderSettingQueryIDTO queryIDTO);

    /**
     * 分页查询门店下单设置表
     * @param
     * @return
     */
    @RequestMapping(value = "/mdShopOrderSetting/queryMdShopOrderSettingListByParams", method = RequestMethod.POST)
    PageInfo<MdShopOrderSettingODTO>  queryMdShopOrderSettingListByParams(@RequestBody MdShopOrderSettingIDTO mdShopOrderSettingIDTO);
    /**
     * 根据主键ID查询门店订货通用设置
     * @param id
     * @return
     */
    @RequestMapping(value = "/mdShopOrderSetting/findMdShopOrderSettingById", method = RequestMethod.POST)
    MdShopOrderSettingODTO findMdShopOrderSettingById(@RequestParam("id") Long id);
    /**
     * 更新门店订货通用设置
     * @param
     * @return
     */
    @RequestMapping(value = "/mdShopOrderSetting/updateMdShopOrderSettingById", method = RequestMethod.POST)
    Integer updateMdShopOrderSettingById(@RequestBody MdShopOrderSettingIDTO mdShopOrderSettingIDTO);
    /**
     * 删除门店订货通用设置
     * @param id
     * @return
     */
    @RequestMapping(value = "/mdShopOrderSetting/deleteMdShopOrderSettingById", method = RequestMethod.POST)
    Integer deleteMdShopOrderSettingById(@RequestParam("id") Long id, @RequestParam("createId") Long createId);

    /**
     * 批量导入新增
     * @param list
     * @return
     */
    @RequestMapping(value = {"/mdShopOrderSetting/saveMdShopOrderSettingList"}, method = {RequestMethod.POST})
    Integer saveMdShopOrderSettingList(@RequestBody List<MdShopOrderSettingIDTO> list);

    /**
     *  分页查询门店下单设置日志表
     * @param
     * @return
     */
    @RequestMapping(value = "/mdShopOrderSetting/queryMdShopOrderSettingLogListByParams", method = RequestMethod.POST)
    PageInfo<MdShopOrderSettingLogODTO>  queryMdShopOrderSettingLogListByParams(@RequestBody MdShopOrderSettingLogIDTO mdShopOrderSettingLogIDTO);

    /**
     * 处理门店订货通用设置
     * @param
     * @return
     */
    @RequestMapping(value = "/mdShopOrderSetting/dealMdShopOrderSetting", method = RequestMethod.POST)
    Integer dealMdShopOrderSetting(@RequestBody ShopOrderSettingDealIDTO dto);

    /**
     * 批量处理门店订货通用设置(商品和供应商详细信息)
     * @param
     * @return
     */
    @RequestMapping(value = "/mdShopOrderSetting/batchDealMdShopOrderSettingDetails", method = RequestMethod.POST)
    Integer batchDealMdShopOrderSettingDetails(@RequestBody List<ShopOrderSettingDetailsDealIDTO> list);

    /**
     * 根据商品id和供应商id查询配置
     * @param commodityIds
     * @return
     */
    @RequestMapping(value = "/mdShopOrderSetting/queryMdShopOrderSettingListByCommodityIds", method = RequestMethod.POST)
    List<MdShopOrderSettingODTO> queryMdShopOrderSettingListByCommodityIds(@RequestParam("commodityIds") List<String> commodityIds, @RequestParam("storeId") Long storeId);

    /**
     * 上个月有销售品项
     * @return
     */
    @GetMapping("/autoShopCommodity/lastMonthSale")
    boolean lastMonthSale();

    /**
     * 删除直送商品
     * @return
     */
    @GetMapping("/autoShopCommodity/deleteDirectSendingCommodity")
    boolean deleteDirectSendingCommodity();
}
