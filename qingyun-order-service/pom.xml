<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.pinshang.qingyun</groupId>
        <artifactId>qingyun-order-parent</artifactId>
        <version>3.6.7-UP-SNAPSHOT</version>
    </parent>
    <artifactId>qingyun-order-service</artifactId>
    <properties>
        <java.version>1.8</java.version>
        <mybatis.spring.version>1.2.4</mybatis.spring.version>
    </properties>
    <dependencies>
        <dependency>
            <groupId>org.reflections</groupId>
            <artifactId>reflections</artifactId>
            <version>0.9.11</version>
        </dependency>
        <dependency>
            <groupId>com.pinshang.qingyun</groupId>
            <artifactId>qingyun-xd-report-client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.pinshang.qingyun</groupId>
            <artifactId>qingyun-bigdata-client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.pinshang.qingyun</groupId>
            <artifactId>qingyun-order-manage-client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.pinshang.qingyun</groupId>
            <artifactId>qingyun-print-client</artifactId>
        </dependency>

        <dependency>
            <groupId>com.pinshang.qingyun</groupId>
            <artifactId>qingyun-tms-client</artifactId>
        </dependency>
        <dependency>
            <groupId>io.github.openfeign.form</groupId>
            <artifactId>feign-form</artifactId>
        </dependency>
        <dependency>
            <groupId>com.pinshang.qingyun</groupId>
            <artifactId>qingyun-upload-client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.pinshang.qingyun</groupId>
            <artifactId>qingyun-settlement-interface-base</artifactId>
        </dependency>
        <dependency>
            <groupId>com.pinshang.qingyun</groupId>
            <artifactId>qingyun-pf-product-client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.pinshang.qingyun</groupId>
            <artifactId>qingyun-renderer</artifactId>
        </dependency>

        <dependency>
            <groupId>io.springfox</groupId>
            <artifactId>springfox-boot-starter</artifactId>
            <version>${springfox-swagger.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>io.swagger</groupId>
                    <artifactId>swagger-models</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>io.swagger</groupId>
            <artifactId>swagger-models</artifactId>
            <version>${swagger-models.version}</version>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
            <!--<exclusions>-->
            <!--<exclusion>-->
            <!--<groupId>org.springframework.boot</groupId>-->
            <!--<artifactId>spring-boot-starter-tomcat</artifactId>-->
            <!--</exclusion>-->
            <!--</exclusions>-->
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-json</artifactId>
        </dependency>
        <dependency>
            <groupId>com.pinshang.qingyun</groupId>
            <artifactId>qingyun-store-client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.pinshang.qingyun</groupId>
            <artifactId>qingyun-store-sh-client</artifactId>
        </dependency>
        <!--<dependency>-->
        <!--<groupId>org.springframework.boot</groupId>-->
        <!--<artifactId>spring-boot-starter-jetty</artifactId>-->
        <!--</dependency>-->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-jdbc</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-aop</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-freemarker</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-netflix-eureka-client</artifactId>
        </dependency>
        <!--<dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-config</artifactId>
        </dependency>-->
        <dependency>
            <groupId>com.ctrip.framework.apollo</groupId>
            <artifactId>apollo-client</artifactId>
        </dependency>
        <dependency>
            <groupId>org.reflections</groupId>
            <artifactId>reflections</artifactId>
            <version>0.9.11</version>
        </dependency>
        <dependency>
            <groupId>qingyun-infrastructure</groupId>
            <artifactId>qingyun-infrastructure-test</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>org.mybatis</groupId>
                    <artifactId>mybatis</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.mybatis</groupId>
                    <artifactId>mybatis-spring</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>tk.mybatis</groupId>
                    <artifactId>mapper</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.github.pagehelper</groupId>
                    <artifactId>pagehelper</artifactId>
                </exclusion>
            </exclusions>
            <scope>test</scope>
        </dependency>

        <dependency>
            <groupId>qingyun-infrastructure</groupId>
            <artifactId>qingyun-infrastructure-metrics-client</artifactId>
        </dependency>
        <dependency>
            <groupId>qingyun-infrastructure</groupId>
            <artifactId>qingyun-infrastructure-health-check</artifactId>
        </dependency>
        <dependency>
            <groupId>qingyun-infrastructure</groupId>
            <artifactId>qingyun-infrastructure-mq</artifactId>
        </dependency>
        <dependency>
            <groupId>qingyun-infrastructure</groupId>
            <artifactId>qingyun-infrastructure-components-inventory</artifactId>
        </dependency>
        <dependency>
            <groupId>qingyun-infrastructure</groupId>
            <artifactId>qingyun-infrastructure-common</artifactId>
        </dependency>
        <dependency>
            <groupId>qingyun-infrastructure</groupId>
            <artifactId>qingyun-infrastructure-springcloud-common</artifactId>
        </dependency>
        <dependency>
            <groupId>qingyun-infrastructure</groupId>
            <artifactId>qingyun-infrastructure-test</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>org.mybatis</groupId>
                    <artifactId>mybatis</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.mybatis</groupId>
                    <artifactId>mybatis-spring</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>tk.mybatis</groupId>
                    <artifactId>mapper</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.github.pagehelper</groupId>
                    <artifactId>pagehelper</artifactId>
                </exclusion>
            </exclusions>
            <scope>test</scope>
        </dependency>

        <dependency>
            <groupId>qingyun-infrastructure</groupId>
            <artifactId>qingyun-infrastructure-loadBalancer</artifactId>
        </dependency>
        <dependency>
            <groupId>qingyun-infrastructure</groupId>
            <artifactId>qingyun-infrastructure-switch</artifactId>
        </dependency>
        <dependency>
            <groupId>qingyun-infrastructure</groupId>
            <artifactId>qingyun-infrastructure-apmCat-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>qingyun-infrastructure</groupId>
            <artifactId>qingyun-infrastructure-cache</artifactId>
        </dependency>
        <dependency>
            <groupId>io.micrometer</groupId>
            <artifactId>micrometer-registry-prometheus</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-actuator</artifactId>
        </dependency>
        <!--<dependency>-->
        <!--<groupId>org.springframework.cloud</groupId>-->
        <!--<artifactId>spring-cloud-starter-zipkin</artifactId>-->
        <!--</dependency>-->
        <dependency>
            <groupId>mysql</groupId>
            <artifactId>mysql-connector-java</artifactId>
        </dependency>

        <!--<dependency>-->
        <!--<groupId>com.fasterxml.jackson.core</groupId>-->
        <!--<artifactId>jackson-core</artifactId>-->
        <!--</dependency>-->
        <!--<dependency>-->
        <!--<groupId>com.fasterxml.jackson.core</groupId>-->
        <!--<artifactId>jackson-databind</artifactId>-->
        <!--</dependency>-->
        <dependency>
            <groupId>com.fasterxml.jackson.datatype</groupId>
            <artifactId>jackson-datatype-joda</artifactId>
        </dependency>
        <!--<dependency>-->
        <!--<groupId>com.fasterxml.jackson.module</groupId>-->
        <!--<artifactId>jackson-module-parameter-names</artifactId>-->
        <!--</dependency>-->
        <!--<dependency>-->
        <!--<groupId>org.springframework.boot</groupId>-->
        <!--<artifactId>spring-boot-starter-security</artifactId>-->
        <!--</dependency>-->

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-configuration-processor</artifactId>
            <optional>true</optional>
        </dependency>

        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-openfeign</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-openfeign-core</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-netflix-hystrix</artifactId>
        </dependency>
        <dependency>
            <groupId>com.pinshang.qingyun</groupId>
            <artifactId>qingyun-xda-product-client</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>com.pinshang.qingyun</groupId>
                    <artifactId>qingyun-base</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.pinshang.qingyun</groupId>
            <artifactId>qingyun-pay-client</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>com.pinshang.qingyun</groupId>
                    <artifactId>qingyun-base</artifactId>
                </exclusion>
            </exclusions>
        </dependency>


        <!--<dependency>-->
        <!--<groupId>org.springframework.cloud</groupId>-->
        <!--<artifactId>spring-cloud-starter-feign</artifactId>-->
        <!--</dependency>-->
        <dependency>
            <groupId>com.pinshang.qingyun</groupId>
            <artifactId>qingyun-xd-promotion-client</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>com.pinshang.qingyun</groupId>
                    <artifactId>qingyun-base</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.pinshang.qingyun</groupId>
            <artifactId>qingyun-marketing-client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.pinshang.qingyun</groupId>
            <artifactId>qingyun-xd-wms-client</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>com.baomidou</groupId>
                    <artifactId>mybatis-plus-boot-starter</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.pinshang.qingyun</groupId>
                    <artifactId>qingyun-base</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.pinshang.qingyun</groupId>
            <artifactId>qingyun-xd-order-client</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>com.pinshang.qingyun</groupId>
                    <artifactId>qingyun-base</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.pinshang.qingyun</groupId>
            <artifactId>qingyun-xd-cms-client</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>com.pinshang.qingyun</groupId>
                    <artifactId>qingyun-base</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.pinshang.qingyun</groupId>
            <artifactId>qingyun-xd-product-client</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>com.pinshang.qingyun</groupId>
                    <artifactId>qingyun-base</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.pinshang.qingyun</groupId>
            <artifactId>qingyun-report-client</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>com.pinshang.qingyun</groupId>
                    <artifactId>qingyun-base</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.pinshang.qingyun</groupId>
            <artifactId>qingyun-msg-client</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>com.pinshang.qingyun</groupId>
                    <artifactId>qingyun-base</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.pinshang.qingyun</groupId>
            <artifactId>qingyun-base-mvc</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>org.apache.commons</groupId>
                    <artifactId>commons-email</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.pinshang.qingyun</groupId>
            <artifactId>qingyun-basic</artifactId>
        </dependency>
        <dependency>
            <groupId>com.pinshang.qingyun</groupId>
            <artifactId>qingyun-base-db</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>org.apache.commons</groupId>
                    <artifactId>commons-email</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.pinshang.qingyun</groupId>
            <artifactId>qingyun-box</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>org.apache.commons</groupId>
                    <artifactId>commons-email</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.pinshang.qingyun</groupId>
            <artifactId>qingyun-common-client</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>com.pinshang.qingyun</groupId>
                    <artifactId>qingyun-base</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.pinshang.qingyun</groupId>
            <artifactId>qingyun-price-client</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>com.pinshang.qingyun</groupId>
                    <artifactId>qingyun-base</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.pinshang.qingyun</groupId>
            <artifactId>qingyun-weixin-client</artifactId>
        </dependency>
        <dependency>
            <groupId>tk.mybatis</groupId>
            <artifactId>mapper-spring-boot-starter</artifactId>
        </dependency>

        <dependency>
            <groupId>com.github.pagehelper</groupId>
            <artifactId>pagehelper-spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>org.mybatis.spring.boot</groupId>
            <artifactId>mybatis-spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>org.quartz-scheduler</groupId>
            <artifactId>quartz</artifactId>
        </dependency>
        <dependency>
            <groupId>org.mybatis</groupId>
            <artifactId>mybatis-typehandlers-jsr310</artifactId>
            <version>1.0.1</version>
        </dependency>
        <dependency>
            <groupId>com.pinshang.qingyun</groupId>
            <artifactId>qingyun-purchase-client</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>com.pinshang.qingyun</groupId>
                    <artifactId>qingyun-base</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.pinshang.qingyun</groupId>
            <artifactId>qingyun-product-client</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>com.pinshang.qingyun</groupId>
                    <artifactId>qingyun-base</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.pinshang.qingyun</groupId>
            <artifactId>qingyun-order-client</artifactId>
        </dependency>
        <!--<dependency>-->
        <!--<groupId>com.pinshang.qingyun</groupId>-->
        <!--<artifactId>qingyun-starter-feign-httpclient</artifactId>-->
        <!--</dependency>-->
        <dependency>
            <groupId>com.pinshang.qingyun</groupId>
            <artifactId>qingyun-shop-client</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>com.pinshang.qingyun</groupId>
                    <artifactId>qingyun-base</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.pinshang.qingyun</groupId>
            <artifactId>qingyun-shop-admin-client</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>com.pinshang.qingyun</groupId>
                    <artifactId>qingyun-base</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.springframework.kafka</groupId>
            <artifactId>spring-kafka</artifactId>
        </dependency>
        <dependency>
            <groupId>com.pinshang.qingyun</groupId>
            <artifactId>qingyun-mq</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>com.pinshang.qingyun</groupId>
                    <artifactId>qingyun-base</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.jolokia</groupId>
            <artifactId>jolokia-core</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>druid</artifactId>
        </dependency>
        <dependency>
            <groupId>org.redisson</groupId>
            <artifactId>redisson</artifactId>
        </dependency>
        <dependency>
            <groupId>com.pinshang.qingyun</groupId>
            <artifactId>qingyun-cache</artifactId>
        </dependency>
        <dependency>
            <groupId>com.pinshang.qingyun</groupId>
            <artifactId>qingyun-storage-client</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>com.pinshang.qingyun</groupId>
                    <artifactId>qingyun-base</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.pinshang.qingyun</groupId>
            <artifactId>qingyun-storage-admin-client</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>com.pinshang.qingyun</groupId>
                    <artifactId>qingyun-base</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.itextpdf</groupId>
            <artifactId>itextpdf</artifactId>
            <version>5.5.10</version>
            <scope>compile</scope>
        </dependency>
        <!--发布到外面tomcat所需要的配置
        <dependency>
		    <groupId>javax.servlet</groupId>
		    <artifactId>javax.servlet-api</artifactId>
		    <scope>provided</scope>
		</dependency>-->
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-test</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>com.pinshang.qingyun</groupId>
            <artifactId>qingyun-supplier-client</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>com.pinshang.qingyun</groupId>
                    <artifactId>qingyun-base</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.pinshang.qingyun</groupId>
            <artifactId>qingyun-sync</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>com.pinshang.qingyun</groupId>
                    <artifactId>qingyun-base</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.pinshang.qingyun</groupId>
            <artifactId>qingyun-settlementTb-client</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>com.pinshang.qingyun</groupId>
                    <artifactId>qingyun-base</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>cn.jpush.api</groupId>
            <artifactId>jpush-client</artifactId>
            <version>3.3.9</version>
        </dependency>
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>easyexcel</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.poi</groupId>
            <artifactId>poi</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.poi</groupId>
            <artifactId>poi-ooxml</artifactId>
        </dependency>

        <dependency>
            <groupId>org.apache.httpcomponents</groupId>
            <artifactId>httpmime</artifactId>
        </dependency>
        <dependency>
            <groupId>net.sf.json-lib</groupId>
            <artifactId>json-lib</artifactId>
            <version>0.9</version>
        </dependency>
        <dependency>
            <groupId>qingyun-infrastructure</groupId>
            <artifactId>qingyun-infrastructure-data-query</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>com.baomidou</groupId>
                    <artifactId>mybatis-plus-boot-starter</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.pinshang.qingyun</groupId>
            <artifactId>qingyun-smm-client</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-mail</artifactId>
        </dependency>
        <dependency>
            <groupId>qingyun-infrastructure</groupId>
            <artifactId>qingyun-infrastructure-file-export-cache</artifactId>
        </dependency>
        <dependency>
            <groupId>com.pinshang.qingyun</groupId>
            <artifactId>qingyun-bigdata-client</artifactId>
        </dependency>
    </dependencies>
    <build>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <version>2.6.6</version>
                <executions>
                    <execution>
                        <goals>
                            <goal>repackage</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin><!--编译跳过测试文件检查的生命周期-->
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
                <configuration>
                    <skip>true</skip>
                </configuration>
            </plugin>
        </plugins>
    </build>
</project>