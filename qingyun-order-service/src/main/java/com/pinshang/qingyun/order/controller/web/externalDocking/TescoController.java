package com.pinshang.qingyun.order.controller.web.externalDocking;

import com.github.pagehelper.PageInfo;
import com.pinshang.qingyun.box.utils.TimeUtil;
import com.pinshang.qingyun.common.dto.DictionaryODTO;
import com.pinshang.qingyun.common.service.DictionaryClient;
import com.pinshang.qingyun.order.mapper.entry.externalDocking.TescoEntry;
import com.pinshang.qingyun.order.service.externalDocking.TescoService;
import com.pinshang.qingyun.order.util.FileUtil;
import com.pinshang.qingyun.order.util.SettleConfig;
import com.pinshang.qingyun.order.vo.externalDocking.EmailVo;
import com.pinshang.qingyun.order.vo.externalDocking.TescoVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.hssf.usermodel.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

/**
 * 乐购台账
 * <AUTHOR>
 * @date 2023年2月15日10:00:09
 */
@Slf4j
@RestController
@RequestMapping("/rtTesco")
@Api(value = "高校订单相关接口", tags = "TescoController", description = "高校订单相关接口")
public class TescoController {

    private final TescoService tescoService;

    private final DictionaryClient dictionaryClient;

    @Autowired
    private SettleConfig settleConfig;


    public TescoController(TescoService tescoService,DictionaryClient dictionaryClient){
        this.tescoService = tescoService;
        this.dictionaryClient = dictionaryClient;
    }

    @ApiOperation(value = "乐购台账列表分页查询", notes = "乐购台账列表分页查询")
    @ApiImplicitParam(name = "vo", value = "", required = true, paramType = "body", dataTypeClass = TescoVo.class)
    @RequestMapping(value = "/findTescoPageInfo",method = RequestMethod.POST)
    public PageInfo<TescoEntry> findTescoPageInfo(@RequestBody TescoVo vo){
        return tescoService.findTescoPageInfo(vo);
    }

    @ApiOperation(value = "乐购台账批量发送邮件", notes = "乐购台账批量发送邮件")
    @ApiImplicitParam(name = "vo", value = "", required = true, paramType = "body", dataTypeClass = TescoVo.class)
    @RequestMapping(value = "/sendEmail",method = RequestMethod.POST)
    public void sendEmail(@RequestBody TescoVo vo){
        HashMap<String, List<TescoEntry>> stringListHashMap = tescoService.cvtTescoEntry(vo);
        String ymd = TimeUtil.parseSimpleDateTime(new Date());
        DictionaryODTO tesco_email_to = dictionaryClient.getDictionaryByCode("TESCO_EMAIL_TO");
        DictionaryODTO tesco_email_cc = dictionaryClient.getDictionaryByCode("TESCO_EMAIL_CC");
        try {
            for (Map.Entry<String, List<TescoEntry>> entry : stringListHashMap.entrySet()) {
                String[] keyArray = entry.getKey().split("#");
                String key = keyArray[1]+"_"+keyArray[0];
                File newFile = this.createNewFile(ymd, key);
                generateExcel(entry, newFile);
                EmailVo emailVo = new EmailVo();
                emailVo.setTo(tesco_email_to.getOptionValue());
                if(tesco_email_cc!=null && tesco_email_cc.getOptionState()==1){
                    String cc = tesco_email_cc.getOptionValue();
                    emailVo.setCc(cc.split(","));
                }
                emailVo.setFile(newFile);
                emailVo.setSubject(key+" 乐购台账");
                emailVo.setContent("【"+keyArray[2]+"】： 《"+key+"》 系统邮件! 请勿回复！ 生成时间 ("+TimeUtil.formatFullDate(new Date())+")");
                emailVo.setFileName(newFile.getName());
                tescoService.sendEmail(emailVo);
            }
        }catch (Exception e){
            log.error("exec excel",e);
        }
    }

    @ApiOperation(value = "导出列表", notes = "导出列表")
    @RequestMapping(value = "/export", method = RequestMethod.GET)
    public void exportExcel(HttpServletResponse response, TescoVo dto) throws UnsupportedEncodingException {
        //1.设置文件ContentType类型，这样设置，会自动判断下载文件类型
        String s = TimeUtil.FORMAT_FULL_DATETIME_LONG.format(new Date());
        String fileName = new String(("乐购台账"+s+".zip").getBytes(),"ISO8859-1");
        FileUtil.initialization(response,fileName);
        HashMap<String, List<TescoEntry>> tescoMaps = tescoService.cvtTescoEntry(dto);

        try{
            ZipOutputStream zipstream=new ZipOutputStream(response.getOutputStream());
            String ymd = TimeUtil.parseSimpleDateTime(new Date());
            for (Map.Entry<String, List<TescoEntry>> entry : tescoMaps.entrySet()) {
                String[] keyArray = entry.getKey().split("#");
                String key = keyArray[1];
                File newFile = this.createNewFile(ymd, key);
                generateExcel(entry, newFile);
                FileInputStream  instream2 =new FileInputStream(newFile.getPath());
                ZipEntry zipEntry = new ZipEntry(key+"_"+TimeUtil.toString(new Date(),"MMdd")+".xls");
                zipstream.putNextEntry(zipEntry);
                byte[] buffer = new byte[1024];
                int len = 0;
                while ((len=instream2.read(buffer) )!= -1){
                    zipstream.write(buffer,0,len);
                }
                instream2.close();
                zipstream.closeEntry();
                zipstream.flush();
            }
            zipstream.finish();
            zipstream.close();
        }catch (Exception e){
            log.error("exec excel",e);
        }

    }
    /**
     * 生成 excel 文件
     * @param entry
     * @param newFile
     * @throws IOException
     */
    private void generateExcel(Map.Entry<String, List<TescoEntry>> entry, File newFile) throws IOException {
        List<TescoEntry> tescoRspList = entry.getValue();
        // sheet名
        String sheetName = "乐购台账导出表";

        // 标题
        String[] title = new String[]{"产品名称", "产品代码", "采购数量", "数量单位", "进货日期",
                "供货单位名称", "供货单位地址", "供货单位联系方式", "摊位号", "经营者名称",
                "生产厂商", "生产日期", "生产批次", "产地证明编号", "检验检疫证书编号", "质量安全检测", "产地"};
        String[][] values = new String[0][0];
        if(tescoRspList!=null){
            values = new String[tescoRspList.size()][];
            for (int i = 0; i < tescoRspList.size(); i++) {
                values[i] = new String[title.length];
                // 将对象内容转换成string
                TescoEntry vo = tescoRspList.get(i);
                values[i][0] = vo.getRtCommodityName();
                values[i][1] = vo.getRtCommodityCode();
                values[i][2] = vo.getCommodityNum();
                values[i][3] = vo.getUnit();
                values[i][4] = vo.getOrderTime();
                values[i][5] = vo.getCompany();
                values[i][6] = vo.getAddress();
                values[i][7] = vo.getRtcAdddress();
                values[i][8] = vo.getRtShopCode();
                values[i][9] = vo.getRtShopName();
                values[i][10] = vo.getMadeCompany();
                values[i][11] = vo.getDeliveryDate();
                values[i][12] = vo.getDeliveryDatePc();
                values[i][16] = vo.getA4();
            }
        }
        // 将excel文件转为输入流
        InputStream is = new FileInputStream(newFile);
        //创建个workbook
        HSSFWorkbook workbook = new HSSFWorkbook();
        FileOutputStream fos = new FileOutputStream(newFile);
        FileUtil.getHSSFWorkbook(sheetName, title, values, workbook);
        workbook.write(fos);
        fos.flush();
        fos.close();
        is.close();
    }

    public File createNewFile(String ymd, String fileName) {
        String path = settleConfig.getUpPath() + "tesco/" + ymd;
        // 读取模板，并赋值到新文件************************************************************
        File newFile = null;
        try {
            // 文件模板路径
            // 保存文件的路径
            // 新的文件名
            String newFileName = path ;
            // 判断路径是否存在
            newFile = new File(newFileName);
            if (!newFile.exists()) {
                newFile.mkdirs();
            }
            newFile = new File(newFileName+ "/"+ fileName + ".xls");
            newFile.createNewFile();
        } catch (Exception e) {
            e.printStackTrace();
        }
        return newFile;
    }
}
