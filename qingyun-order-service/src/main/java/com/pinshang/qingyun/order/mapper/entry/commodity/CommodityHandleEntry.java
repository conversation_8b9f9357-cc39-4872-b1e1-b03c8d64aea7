package com.pinshang.qingyun.order.mapper.entry.commodity;

import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

@Data
public class CommodityHandleEntry {
    private String supplyStartTime; //供货开始时间
    private String supplyEndTime;//供货截止时间

    private BigDecimal retailPrice;//门店零售价
    private BigDecimal promotionPrice;//特价
    private String grossProfitRate;//毛利率

    private String inStorageDate;//第一次入库日期
    private BigDecimal avgMonthSalesQuantity;//月均销量

    private BigDecimal onlineQuantity;//在途


    private String commodityId;
    private String commodityCode;
    private String commodityName;//商品名称

    private String commoditySpec;//规格
    /** 包装规格 */
    private BigDecimal commodityPackageSpec;
    private String commodityUnit;//单位

    private boolean isPromotionProduct;//是否促销

    private String newProductFlag;//新品标识
    private boolean isNewProduct;//是否新品

    private Integer frozen;//速冻 1:是 0：否

    private BigDecimal salesBoxCapacity;//箱规

    private BigDecimal commodityPrice;//进货价

    private BigDecimal cartNum;//购物车数量

    private List<CommodityOrderSalesEntry> orderSaleList;//近7日进销

    private boolean addToShoppingCart;//是否可以加入购物车:true:可以 , false：不能

    private  Integer isWeight;//是否称重0-不称量,1-称重

    private BigDecimal stockQuantity;//库存数量

    private Integer stockNumber;//库存份数

    /** 货号 */
    private String shelfNo;

    /** 已订未收数量 */
    private BigDecimal orderedQuantity;

    private Boolean autoCommodity; // 是否自动订货
    private BigDecimal boxCapacity;

    /**
     * 1 当前时间可订货的商品——在订货时间段内，且有库存（商品库存依据且有库存的商品、不限量供应的商品、限量供应且有余量的商品）
     * 2 已抢光的商品——商品库存依据且库存≤0或限量供应且已满限量的商品，标记“已抢光”效果。
     * 3 当前时间不在订货时间段的商品
     */
    private Integer orderedStatus;
}
