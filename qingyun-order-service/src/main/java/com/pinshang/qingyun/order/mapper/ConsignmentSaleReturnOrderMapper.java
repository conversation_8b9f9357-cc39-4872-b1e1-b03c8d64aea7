package com.pinshang.qingyun.order.mapper;

import com.pinshang.qingyun.base.mybatis.MyMapper;
import com.pinshang.qingyun.order.dto.consignment.ConsignmentReturnOrderPageODTO;
import com.pinshang.qingyun.order.dto.consignment.ConsignmentReturnOrderQueryIDTO;
import com.pinshang.qingyun.order.dto.consignment.ConsignmentSaleReturnOrderODTO;
import com.pinshang.qingyun.order.model.order.ConsignmentSaleReturnOrder;
import com.pinshang.qingyun.order.vo.order.ConsignmentSaleReturnOrderRespVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

/**
 * Created by honway on 2017/11/2 16:21.
 */
@Mapper
@Repository
public interface ConsignmentSaleReturnOrderMapper extends MyMapper<ConsignmentSaleReturnOrder> {
    /**
     * 查询 代销退货单列表
     * @param params 查询列表的参数
     * @return 返回 门店代销退货单列表
     */
    List<ConsignmentSaleReturnOrderRespVo> list(Map<String, Object> params);

    ConsignmentSaleReturnOrderODTO selectById(@Param("id") Long saleReturnOrderId);

    Integer countNotConfirmSaleReturnOrder(@Param("storeId") Long storeId, @Param("beginDate") String beginDate, @Param("endDate") String endDate);

    List<ConsignmentReturnOrderPageODTO> consignmentReturnOrderReport(@Param("vo") ConsignmentReturnOrderQueryIDTO vo);
}
