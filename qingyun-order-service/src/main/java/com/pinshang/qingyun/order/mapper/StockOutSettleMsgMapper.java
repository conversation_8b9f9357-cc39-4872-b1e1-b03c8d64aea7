package com.pinshang.qingyun.order.mapper;


import com.pinshang.qingyun.base.mybatis.MyMapper;
import com.pinshang.qingyun.order.model.order.StockOutSettleMsg;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface StockOutSettleMsgMapper extends MyMapper<StockOutSettleMsg> {

    List<StockOutSettleMsg> selectstockOutSettleMsgByTime(@Param("beginTime") String beginTime, @Param("endTime") String endTime);
}