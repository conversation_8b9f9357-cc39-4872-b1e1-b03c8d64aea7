package com.pinshang.qingyun.order.manage.delivery.strategy.impl;


import com.pinshang.qingyun.base.enums.StoreBillTypeEnums;
import com.pinshang.qingyun.base.enums.settlement.PayMethodIdEnum;
import com.pinshang.qingyun.base.enums.settlement.ReceiptTypeIdEnum;
import com.pinshang.qingyun.common.service.DictionaryClient;
import com.pinshang.qingyun.order.bo.StoreRechargeBO;
import com.pinshang.qingyun.order.manage.delivery.alert.AlertSwitch;
import com.pinshang.qingyun.order.manage.delivery.dto.DeliveryOrderDTO;
import com.pinshang.qingyun.order.manage.delivery.setting.StoreDeliverySetting;
import com.pinshang.qingyun.order.manage.delivery.strategy.DeliveryStrategy;
import com.pinshang.qingyun.order.manage.delivery.utils.PfDeliverySettingUtils;
import com.pinshang.qingyun.order.service.StoreRechargeService;
import com.pinshang.qingyun.order.service.WeChatSendMessageService;
import com.pinshang.qingyun.settlementTb.service.StoreRechargeClient;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.transaction.support.TransactionSynchronizationAdapter;
import org.springframework.transaction.support.TransactionSynchronizationManager;

import java.math.BigDecimal;
import java.util.Date;

import static com.pinshang.qingyun.order.manage.delivery.constant.DeliveryConstants.*;

@Component
@Slf4j
public class RealTimeUnderDeliveryRefundStrategy implements DeliveryStrategy {

    private final StoreRechargeClient storeRechargeClient;

    private final DictionaryClient dictionaryClient;

    private final WeChatSendMessageService weChatSendMessageService;

    private final AlertSwitch alertSwitch;

    private final StoreRechargeService storeRechargeService;

    public RealTimeUnderDeliveryRefundStrategy(StoreRechargeClient storeRechargeClient, DictionaryClient dictionaryClient, WeChatSendMessageService weChatSendMessageService, AlertSwitch alertSwitch, StoreRechargeService storeRechargeService) {
        this.storeRechargeClient = storeRechargeClient;
        this.dictionaryClient = dictionaryClient;
        this.weChatSendMessageService = weChatSendMessageService;
        this.alertSwitch = alertSwitch;
        this.storeRechargeService = storeRechargeService;
    }

    @Override
    public void handle(DeliveryOrderDTO deliveryOrderDTO, StoreDeliverySetting storeDeliverySetting) {
        if(isUnderDeliveryRefundApplicable(deliveryOrderDTO,storeDeliverySetting)){
            TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronizationAdapter() {
                @Override
                public void afterCommit() {
                    triggerRefund(deliveryOrderDTO,storeDeliverySetting);
                }
            });
        }
    }

    //少发条件 ---- 预付 & 【【【（结算类型：B端-判断订单与实发，最小值结算 || 客户类型： 鲜食加盟）】 + 结算时处理短交： 否】 || 【批发】】
    private boolean isUnderDeliveryRefundApplicable(DeliveryOrderDTO deliveryOrderDTO,StoreDeliverySetting storeDeliverySetting) {
        boolean isPrePayAccountFlag = storeDeliverySetting.getIsPrePayStoreAccount()==null?false:storeDeliverySetting.getIsPrePayStoreAccount().booleanValue();

        return isPrePayAccountFlag && (underDeliveryFlag(storeDeliverySetting) || PfDeliverySettingUtils.ifPfUnderDeliveryConfition(deliveryOrderDTO,storeDeliverySetting));
    }

    private boolean  underDeliveryFlag(StoreDeliverySetting storeDeliverySetting){
        boolean isXsjm = storeDeliverySetting.getIsXsjm()==null?false: storeDeliverySetting.getIsXsjm().booleanValue();
        boolean settleBillReturnAmountStateFlag = storeDeliverySetting.getSettleBillReturnAmountState() == null ? false : (0 == storeDeliverySetting.getSettleBillReturnAmountState());
        boolean settleBillCalcTypeFlag = storeDeliverySetting.getSettleBillCalcType() == null ? false : (1 == storeDeliverySetting.getSettleBillCalcType());
        return  settleBillReturnAmountStateFlag && (settleBillCalcTypeFlag || isXsjm);

    }

    private void triggerRefund(DeliveryOrderDTO deliveryOrderDTO,StoreDeliverySetting storeDeliverySetting) {

        BigDecimal money = deliveryOrderDTO.getUnderDeliveryMoney();
        if (money != null && money.compareTo(BigDecimal.ZERO) > 0) {
            log.warn("监听出库消息 订单：{} 子单出库完后 存在短交【money={}】 进行退款 短交退款执行开始...", deliveryOrderDTO.getOrderCode(), money);

            if(alertSwitch.isAlertEnabled() && !(XSJM_STORETYPE_CODE.equals(storeDeliverySetting.getStoreTypeCode()) || PFS_STORETYPE_CODE.equals(storeDeliverySetting.getStoreTypeCode()))){
                weChatSendMessageService.sendWeChatMessage("非鲜食加盟或批发客户处理短交，订单id【"
                        + deliveryOrderDTO.getOrderId()
                        +"】，订单号【"+deliveryOrderDTO.getOrderCode()
                        +"】，短交金额【"+money+"】。");
            }
            StoreRechargeBO rechargeBO = StoreRechargeBO.builder()
                    .orderCode(deliveryOrderDTO.getOrderCode())
                    .money(money.doubleValue())
                    .storeId(deliveryOrderDTO.getStoreInfo().getStoreId())
                    .tradeTime(new Date())
                    .receiptDate(deliveryOrderDTO.getOrderTime())
                    .billType((StoreBillTypeEnums.PF_DISCREPANCY.getCode()))
                    .tradeCode(UNDER_DELIVERY_FLAG + deliveryOrderDTO.getOrderCode())
                    .build();
            boolean pfsStoreTypeFlag = PFS_STORETYPE_CODE.equals(storeDeliverySetting.getStoreTypeCode());
            if(pfsStoreTypeFlag){
                rechargeBO.setBankDate(deliveryOrderDTO.getOrderTime());
                rechargeBO.setReceiptDate(deliveryOrderDTO.getOrderTime());
                rechargeBO.setPaymentMethod(PayMethodIdEnum.转账.getId());
                rechargeBO.setReceiptType(ReceiptTypeIdEnum.货款.getCode());
                rechargeBO.setRemark("<--批发短交退款：" + deliveryOrderDTO.getOrderCode() + "-->");
                rechargeBO.setBillType(StoreBillTypeEnums.PF_DISCREPANCY.getCode());
            }else{
                rechargeBO.setRemark("<--短交退款：" + deliveryOrderDTO.getOrderCode() + "-->");
                rechargeBO.setBillType(StoreBillTypeEnums.SHOP_DISCREPANCY_DEPOSIT.getCode());
            }
            try {
                storeRechargeService.storeRecharge(rechargeBO);
            } catch (Exception e) {
                log.warn("is method triggerRefund storeRechargeClient.addRecharge(dto) fegin远程调用失败 error:{}", e);
            }
            log.warn("监听出库消息 订单：{} 子单出库完后 存在短交【money={}】 进行退款 短交退款执行结束...", deliveryOrderDTO.getOrderCode(), money);
        }
    }

}
