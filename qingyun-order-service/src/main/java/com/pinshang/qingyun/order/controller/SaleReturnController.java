package com.pinshang.qingyun.order.controller;

import com.alibaba.excel.EasyExcel;
import com.github.pagehelper.PageInfo;
import com.pinshang.qingyun.base.annotations.RepeatSubmitAnno;
import com.pinshang.qingyun.base.api.TokenInfo;
import com.pinshang.qingyun.base.configure.expand.QYAssert;
import com.pinshang.qingyun.base.enums.IogisticsModelEnums;
import com.pinshang.qingyun.base.enums.RepeatSubmitBusinessCode;
import com.pinshang.qingyun.base.enums.shop.ManagementModeEnums;
import com.pinshang.qingyun.base.util.ExcelUtil;
import com.pinshang.qingyun.base.util.FastThreadLocalUtil;
import com.pinshang.qingyun.common.dto.DictionaryODTO;
import com.pinshang.qingyun.common.service.DictionaryClient;
import com.pinshang.qingyun.kafka.KafkaConstant;
import com.pinshang.qingyun.order.dto.XdSaleReturnIDTO;
import com.pinshang.qingyun.order.dto.XdSaleReturnODTO;
import com.pinshang.qingyun.order.dto.order.SaleReturnCancelIDTO;
import com.pinshang.qingyun.order.dto.order.SaleReturnDetailNoStallPageODTO;
import com.pinshang.qingyun.order.dto.order.SaleReturnDetailPageIDTO;
import com.pinshang.qingyun.order.dto.order.SaleReturnDetailPageODTO;
import com.pinshang.qingyun.order.mapper.entry.order.*;
import com.pinshang.qingyun.order.model.order.SaleReturn;
import com.pinshang.qingyun.order.service.ConsigneeCommonService;
import com.pinshang.qingyun.order.service.ConsignmentSupplierService;
import com.pinshang.qingyun.order.service.OrderService;
import com.pinshang.qingyun.order.service.SaleReturnService;
import com.pinshang.qingyun.order.vo.order.SaleReturnAddVo;
import com.pinshang.qingyun.order.vo.order.SaleReturnCommodityVo;
import com.pinshang.qingyun.order.vo.order.SaleReturnVo;
import com.pinshang.qingyun.renderer.annotation.MethodRender;
import com.pinshang.qingyun.renderer.service.IRenderService;
import com.pinshang.qingyun.supplier.dto.SupplierODTO;
import com.pinshang.qingyun.supplier.service.SupplierClient;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.joda.time.LocalDate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

/**
 * Created by crell on 2017/11/6.
 */
@Slf4j
@RestController
@RequestMapping("/order/saleReturn")
public class SaleReturnController {

    @Autowired
    SaleReturnService saleReturnService;

    @Autowired
    OrderService orderService;

    @Autowired
    private SupplierClient supplierClient;

    @Autowired
    private IRenderService renderService;
    @Autowired
    private DictionaryClient dictionaryClient;
    @Autowired
    private ConsigneeCommonService consigneeCommonService;
    @Autowired
    private ConsignmentSupplierService consignmentSupplierService;

    /**
     * 门店退库、门店少货列表
     * @param saleReturnVo
     * @return
     */
    @RequestMapping(value = "/list", method = RequestMethod.POST)
    public PageInfo <SaleReturnEntry> list(@RequestBody SaleReturnVo saleReturnVo) {
        PageInfo <SaleReturnEntry> pageInfo = saleReturnService.getSaleReturnListByCondition(saleReturnVo);
        DictionaryODTO dictionaryODTO = dictionaryClient.getDictionaryByCode("supplierCode");
        SupplierODTO supplier = supplierClient.getSupplierByCode(dictionaryODTO.getOptionValue());

        List<SaleReturnEntry> list = pageInfo.getList();
        if(null !=list && !list.isEmpty()){
            list.forEach( i ->{
                if(i.getLogisticsModel().intValue()== IogisticsModelEnums.DIRECT_CONNECTION.getCode() || i.getLogisticsModel().intValue()== IogisticsModelEnums.DISPATCHING.getCode()){
                    i.setSupplierName(supplier != null ? supplier.getSupplierName() : "");
                }
            });
        }
        return pageInfo;
    }

    @RequestMapping(value = "/show/{orderCode}", method = RequestMethod.GET)
    public SaleReturnInfoEntry showSaleReturn(@PathVariable String orderCode) {
        return  saleReturnService.showSaleReturn(orderCode);
    }

    @MethodRender
    @RequestMapping(value = "/copy/{orderCode}", method = RequestMethod.GET)
    public List<SaleReturnItemEntry> copySaleReturn(@PathVariable String orderCode) {
        return  saleReturnService.copySaleReturn(orderCode);
    }

    @PostMapping(value = "/findCommodityByCode")
    public List<SaleReturnItemEntry> findCommodityByCode(@RequestBody SaleReturnCommodityVo vo){
    	if(!StringUtils.isNotBlank(vo.getCommodityCode())){
    		return new ArrayList<SaleReturnItemEntry>();
    	}
        QYAssert.isTrue(null != vo.getEnterpriseId(), "enterpriseId is not null !");
        consignmentSupplierService.checkStallId(vo);
        return saleReturnService.findCommodityByCode(vo);
    }

    @PostMapping(value = "/findCommodityByCommodityCode")
    public List<SaleReturnItemEntry> findCommodityByCommodityCode(@RequestBody SaleReturnCommodityVo vo){
        if(!StringUtils.isNotBlank(vo.getCommodityCode())){
            return new ArrayList<SaleReturnItemEntry>();
        }
        QYAssert.isTrue(null != vo.getEnterpriseId(), "enterpriseId is not null !");
        consignmentSupplierService.checkStallId(vo);
        return saleReturnService.findCommodityByCommodityCode(vo);
    }
    /*
     * 添加退货单
     */
    @RequestMapping(value = "/add",method = RequestMethod.POST)
    public String addSaleReturn(@RequestBody SaleReturnAddVo saleReturnAddVo) throws Throwable{
        consignmentSupplierService.checkStallId(saleReturnAddVo);
        return saleReturnService.addSaleReturn(saleReturnAddVo);
    }

    @RequestMapping(value = "/cancel/{orderCode}/{userId}",method = RequestMethod.POST)
    public void cancelSaleReturn(@PathVariable("orderCode") String orderCode,@PathVariable("userId") Long userId) throws Throwable{
        saleReturnService.cancelSaleReturn(orderCode,userId);
    }

    @RequestMapping(value = "/cancelWithReason",method = RequestMethod.POST)
    public void cancelSaleReturnWithReason(@RequestBody SaleReturnCancelIDTO idto) throws Throwable{
        saleReturnService.cancelSaleReturnWithReason(idto.getOrderCode(),idto.getUserId(), idto.getRemark());
    }

    @RequestMapping(value = "/sending/list", method = RequestMethod.POST)
    public PageInfo <SaleReturnEntry> sendingList(@RequestBody SaleReturnVo saleReturnVo) {
        PageInfo <SaleReturnEntry> pageInfo = saleReturnService.getSaleReturnSendList(saleReturnVo);
        return pageInfo;
    }

    /*
     * 确认退货单
     */
    @RequestMapping(value = "/confirm",method = RequestMethod.POST)
    public void confirmSaleReturn(@RequestBody SaleReturnAddVo saleReturnAddVo){
        saleReturnService.confirmSaleReturn(saleReturnAddVo);

        // 发送结算信息kafka消息
        SaleReturn saleReturnOrder = saleReturnService.getSaleReturnOrder(saleReturnAddVo.getSaleReturnOrderId());
        String orderCode = saleReturnOrder.getOrderCode();
        orderService.fixedSettleOrder(orderCode, "RT_DIRECT_SENDING", KafkaConstant.SETTLE_ORDER_ALL_TOPIC);
    }

    @ApiOperation(value = "退库明细", notes = "退库明细", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @RequestMapping(value = "/xdretrun/list", method = RequestMethod.POST)
    public PageInfo <XdSaleReturnODTO> xdReturnList(@RequestBody XdSaleReturnIDTO xdSaleReturnIDTO) {
        PageInfo <XdSaleReturnODTO> pageInfo = saleReturnService.xdReturnList(xdSaleReturnIDTO);
        return pageInfo;
    }


    @ApiOperation(value = "pda退货查询", notes = "pda退货查询", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @RequestMapping(value = "/pdaReturnByBarcode", method = RequestMethod.GET)
    public PdaReturnItemEntry pdaReturnByBarcode(@RequestParam(value = "shopId",required = false)Long shopId, @RequestParam(value = "barCode",required = false)String barCode,
                                                 @RequestParam(value = "stallId",required = false) Long stallId ) throws Exception{
        return saleReturnService.pdaReturnByBarcode(shopId, barCode, stallId);
    }
    @ApiOperation(value = "pda少货查询", notes = "pda少货查询", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @RequestMapping(value = "/pdaShortByBarcode", method = RequestMethod.GET)
    public PdaReturnItemEntry pdaShortByBarcode(@RequestParam(value = "shopId",required = false)Long shopId, @RequestParam(value = "barCode",required = false)String barCode,
                                                @RequestParam(value = "stallId",required = false) Long stallId) throws Exception{
        return saleReturnService.pdaShortByBarcode(shopId, barCode, stallId);
    }

    @ApiOperation(value = "pda退货新增", notes = "pda退货新增", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @RequestMapping(value = "/pdaReturnAdd",method = RequestMethod.POST)
    @RepeatSubmitAnno(value = RepeatSubmitBusinessCode.SALE_RETURN_ADD_ORDER,expireTime = 5 )
    public Boolean pdaReturnAdd(@RequestBody SaleReturnAddVo saleReturnAddVo) throws Throwable{
        TokenInfo ti = FastThreadLocalUtil.getQY();
        if(ti != null){
            saleReturnAddVo.setShopId(ti.getShopId());
            saleReturnAddVo.setStoreId(ti.getStoreId());
            saleReturnAddVo.setCreateId(ti.getUserId());
            saleReturnAddVo.setXdQuality(false);
            consignmentSupplierService.checkStallId(saleReturnAddVo);
        }
        return saleReturnService.pdaReturnAdd(saleReturnAddVo);
    }
    @ApiOperation(value = "pda少货新增", notes = "pda少货新增", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @RequestMapping(value = "/pdaShortAdd",method = RequestMethod.POST)
    @RepeatSubmitAnno(value = RepeatSubmitBusinessCode.SALE_RETURN_ADD_ORDER,expireTime = 5 )
    public Boolean pdaShortAdd(@RequestBody SaleReturnAddVo saleReturnAddVo) throws Throwable{
        TokenInfo ti = FastThreadLocalUtil.getQY();
        if(ti != null){
            saleReturnAddVo.setShopId(ti.getShopId());
            saleReturnAddVo.setStoreId(ti.getStoreId());
            saleReturnAddVo.setCreateId(ti.getUserId());
            saleReturnAddVo.setXdQuality(false);
            consignmentSupplierService.checkStallId(saleReturnAddVo);
        }
        return saleReturnService.pdaShortAdd(saleReturnAddVo);
    }

    @RequestMapping(value = "/showForAudit/{orderCode}", method = RequestMethod.GET)
    public SaleReturnInfoForAuditEntry showSaleReturnForAudit(@PathVariable String orderCode) {
        return  saleReturnService.showSaleReturnForAudit(orderCode);
    }

    /**
     * 门店退库明细
     * @param idto
     * @return
     * 迁移 order-manage
     */
    @Deprecated
    @MethodRender
    @RequestMapping(value = "/saleReturnDetailPage", method = RequestMethod.POST)
    public PageInfo<SaleReturnDetailPageODTO> saleReturnDetailPage(@RequestBody SaleReturnDetailPageIDTO idto) {
        return  saleReturnService.saleReturnDetailPage(idto);
    }

    /**
     * 迁移 order-manage
     * @param idto
     * @param response
     * @throws IOException
     */
    @Deprecated
    @ApiOperation(value = "导出", notes = "导出门店退货明细")
    @RequestMapping(value = "/export/saleReturnDetailPage", method = RequestMethod.GET)
    public void saleReturnDetailPageExport(SaleReturnDetailPageIDTO idto, HttpServletResponse response) throws IOException {
        idto.initExportPage();
        PageInfo<SaleReturnDetailPageODTO> pageInfo = saleReturnService.saleReturnDetailPage(idto);
        List<SaleReturnDetailPageODTO> list = pageInfo.getList();
        if(null == list){
            list = new ArrayList<>();
        }
        renderService.render(pageInfo.getList(),"/package/packageTrackPage");

        TokenInfo ti = FastThreadLocalUtil.getQY();
        Boolean bigShop = ManagementModeEnums.档口分包.getCode().equals(ti.getManagementMode());
        Boolean isZb = (ti.getShopId() == null);
        try {
            ExcelUtil.setFileNameAndHead(response, "门店退货明细" + LocalDate.now().toString("yyyyMMdd"));

            // 总部和大店返回档口列
            if(bigShop || isZb) {
                EasyExcel.write(response.getOutputStream(), SaleReturnDetailPageODTO.class).autoCloseStream(Boolean.FALSE).sheet("门店退货明细")
                        .doWrite(list);
            }else {
                EasyExcel.write(response.getOutputStream(), SaleReturnDetailNoStallPageODTO.class).autoCloseStream(Boolean.FALSE).sheet("门店退货明细")
                        .doWrite(list);
            }

        }catch (Exception e){
            log.error("门店退货明细", e);
            ExcelUtil.setExceptionResponse( response );
        }
    }



}
