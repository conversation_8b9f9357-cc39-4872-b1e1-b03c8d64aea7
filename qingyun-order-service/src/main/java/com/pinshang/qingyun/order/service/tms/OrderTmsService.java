package com.pinshang.qingyun.order.service.tms;

import java.util.Collections;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.pinshang.qingyun.order.dto.tms.RefOrderForWaybillODTO;
import com.pinshang.qingyun.order.dto.tms.RefOrderItemForWaybillODTO;
import com.pinshang.qingyun.order.dto.tms.SelectRefOrderForWaybillIDTO;
import com.pinshang.qingyun.order.dto.tms.WarehouseConfirmInfoODTO;
import com.pinshang.qingyun.order.mapper.tms.OrderTmsMapper;

/**
 * 【订单系统 <-> 物流系统】
 */
@Service
public class OrderTmsService {
	
    @Autowired
    private OrderTmsMapper orderTmsMapper;
    
    /**
     * 查询【源单-用于运单】
     * 
     * @param idto
     * @return
     */
    public RefOrderForWaybillODTO selectRefOrder(SelectRefOrderForWaybillIDTO idto) {
    	if (null == idto) {
    		return null;
    	}
    	
    	Integer refType = idto.getRefType();
    	Long refOrderId = idto.getRefOrderId();
    	if (null == refType || null == refOrderId) {
    		return null;
    	}
    	
    	RefOrderForWaybillODTO refOrder = null;
    	if (refType.intValue() == 11) {
    		refOrder = orderTmsMapper.selectOrder(idto);
    		if (null != refOrder) {
    			List<RefOrderItemForWaybillODTO> refOrderItemList = orderTmsMapper.selectOrderItemList(idto);
    			refOrder.setRefOrderItemList(refOrderItemList);
    		}
    	} else if (refType.intValue() == 21) {
    		refOrder = orderTmsMapper.selectReturnOrder(idto);
    		if (null != refOrder) {
    			List<RefOrderItemForWaybillODTO> refOrderItemList = orderTmsMapper.selectReturnOrderItemList(idto);
    			refOrder.setRefOrderItemList(refOrderItemList);
    		}
    	}
    	
    	return refOrder;
    }
    
    /**
	 * 查询【大仓确认信息】列表
	 * 
	 * @param refOrderId
	 * @return
	 */
	public List<WarehouseConfirmInfoODTO> selectWarehouseConfirmInfoList(Long refOrderId) {
		if (null == refOrderId) {
			return Collections.emptyList();
		}
		
		return orderTmsMapper.selectWarehouseConfirmInfoList(refOrderId);
	}
    
}
