package com.pinshang.qingyun.order.mapper.giftLimit;

import com.pinshang.qingyun.base.mybatis.MyMapper;
import com.pinshang.qingyun.order.dto.giftLimit.GiftLeftQuantityODTO;
import com.pinshang.qingyun.order.model.giftLimit.GiftLimitQuantity;
import com.pinshang.qingyun.order.model.user.AppUser;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Set;

@Repository
public interface GiftLimitQuantityMapper extends MyMapper<GiftLimitQuantity> {

    List<GiftLeftQuantityODTO> queryGiftUsedQuantityList(@Param("promotionIdList") List<Long> promotionIdList, @Param("commodityIdList") List<Long> commodityIdList, @Param("orderId") Long orderId);
}
