package com.pinshang.qingyun.order.service;

import com.alibaba.fastjson.JSON;
import com.pinshang.qingyun.base.configure.expand.QYAssert;
import com.pinshang.qingyun.base.constant.QingyunConstant;
import com.pinshang.qingyun.base.enums.*;
import com.pinshang.qingyun.base.enums.order.OrderTypeEnum;
import com.pinshang.qingyun.base.enums.storage.StockTypeEnum;
import com.pinshang.qingyun.box.utils.DateTimeUtil;
import com.pinshang.qingyun.box.utils.DateUtil;
import com.pinshang.qingyun.box.utils.SpringUtil;
import com.pinshang.qingyun.infrastructure.components.IMqSenderComponent;
import com.pinshang.qingyun.infrastructure.components.params.mq.MqMessage;
import com.pinshang.qingyun.kafka.KafkaConstant;
import com.pinshang.qingyun.kafka.enums.KafkaMessageOperationTypeEnum;
import com.pinshang.qingyun.kafka.enums.KafkaMessageTypeEnum;
import com.pinshang.qingyun.order.constant.DictionaryCodeConstant;
import com.pinshang.qingyun.order.enums.CombTypeEnum;
import com.pinshang.qingyun.order.enums.ProductTypeEnums;
import com.pinshang.qingyun.order.mapper.*;
import com.pinshang.qingyun.order.mapper.entry.MdShopOrderSettingEntry;
import com.pinshang.qingyun.order.mapper.entry.splitOrder.SplitOrderListEntry;
import com.pinshang.qingyun.order.mapper.entry.splitOrder.SubOrder2DeliveryOrderItemEntry;
import com.pinshang.qingyun.order.mapper.entry.splitOrder.SubOrder2DeliveryOrderListEntry;
import com.pinshang.qingyun.order.model.commodity.Commodity;
import com.pinshang.qingyun.order.model.order.*;
import com.pinshang.qingyun.order.vo.orderMonitor.NonSplitCommodityDetail;
import com.pinshang.qingyun.order.vo.orderMonitor.NonSplitOrderQueryVo;
import com.pinshang.qingyun.order.vo.orderMonitor.NonSplitOrderVo;
import com.pinshang.qingyun.order.vo.splitOrder.SplitOrderKafkaVo;
import com.pinshang.qingyun.order.vo.store.StoreCompanyVo;
import com.pinshang.qingyun.storage.dto.CommodityDefaultDcODto;
import com.pinshang.qingyun.storage.dto.td.QueryTdDefaultWarehouseReqIDTO;
import com.pinshang.qingyun.storage.dto.tob.CommodityInventoryODTO;
import com.pinshang.qingyun.storage.dto.warehouse.WarehouseODTO;
import com.pinshang.qingyun.storage.service.CommodityWarehouseClient;
import com.pinshang.qingyun.storage.service.DeliveryOrderClient;
import com.pinshang.qingyun.storage.service.td.TdClient;
import jodd.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionSynchronizationAdapter;
import org.springframework.transaction.support.TransactionSynchronizationManager;
import tk.mybatis.mapper.entity.Example;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.BlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/*
 * 拆单
 */
@Slf4j
@Service(value = "splitOrderService")
public class SplitOrderService {

    @Autowired
    private OrderMapper orderMapper;
    @Autowired
    private SplitOrderMapper splitOrderMapper;
    @Autowired
    private SubOrderMapper subOrderMapper;
    @Autowired
    private SubOrderItemMapper subOrderItemMapper;
    @Autowired
    private DeliveryTimeMapper deliveryTimeMapper;
    @Autowired
    private DeliveryOrderClient deliveryOrderClient;
    @Autowired
    private CommodityWarehouseClient commodityWarehouseClient;
    @Autowired
    private SubOrderService subOrderService;
    @Autowired
    private IMqSenderComponent mqSenderComponent;
    @Value("${application.name.switch}")
    private String applicationNameSwitch;
    @Autowired
    private SubOrderConfirmMapper subOrderConfirmMapper;
    @Autowired
    private DictionaryService dictionaryService;
    @Lazy
    @Autowired
    private OrderShelvesRecommendService orderShelvesRecommendService;
    @Autowired
    private OrderAsyncKafkaService orderAsyncKafkaService;
    @Lazy
    @Autowired
    private BStockService bStockService;
    @Autowired
    private TdClient tdClient;
    @Autowired
    private StoreMapper storeMapper;
    @Autowired
    private WeChatSendMessageService weChatSendMessageService;
    @Autowired
    private OrderListService orderListService;
    @Autowired
    @Lazy
    private MdShopOrderSettingService mdShopOrderSettingService;

    /**
     * 拆单主方法
     * @param vo
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public Boolean splitOrder(SplitOrderKafkaVo vo){

        if(null == vo.getOrderId() || null == vo.getType()){
            log.warn("接收到拆单消息,数据异常不能处理, {}:", JSON.toJSONString(vo));
            return false;
        }

        // 鲜达、生鲜、批发、一期.如果storeTypeId、settleOrderTime为空，重新赋值
        updateOrderStoreTypeId(vo.getOrderId());

        List<SubOrder2DeliveryOrderListEntry> so2DoList = new ArrayList<>();
        if (vo.getType().equals(KafkaMessageOperationTypeEnum.INSERT) || vo.getType().equals(KafkaMessageOperationTypeEnum.REPLENISHMENT)) {
            //新增订单或者补货
            so2DoList = processInsert(vo);
        } else if (vo.getType().equals(KafkaMessageOperationTypeEnum.UPDATE)) {
            //修改订单
            so2DoList = processUpdate(vo);
        } else if (vo.getType().equals(KafkaMessageOperationTypeEnum.CANCEL)) {
            //取消订单
            processCancel(vo);
        }
        return Boolean.TRUE;
    }


    /**
     * 鲜达、生鲜、批发、一期.如果storeTypeId、settleOrderTime为空，重新赋值
     * @param orderId
     */
    @Transactional(rollbackFor = Exception.class)
    public void updateOrderStoreTypeId(Long orderId){
       Order order = orderMapper.selectByPrimaryKey(orderId);
       if(order != null){
           StoreCompanyVo storeCompanyByStoreId = storeMapper.getStoreCompanyByStoreId(order.getStoreId());

           Boolean isUpdate = false;
            if(order.getStoreTypeId() == null){
                order.setStoreTypeId(storeCompanyByStoreId.getStoreTypeId());
                isUpdate = true;
            }

            if(order.getCompanyId() == null){
               order.setCompanyId(storeCompanyByStoreId.getCompanyId());
                isUpdate = true;
            }

           if(order.getSettleOrderTime() == null){
               order.setSettleOrderTime(order.getOrderTime());
               isUpdate = true;
           }

           if(isUpdate){
               orderMapper.updateByPrimaryKeySelective(order);
           }
       }
    }

    /**
     * 新增订单
     * 1.生成子单(子单状态:未生成DO单)、子单明细
     * 2.修改splitOrder状态为已拆单
     * 3.返回子单生成DO单所需要的数据
     *
     * @param vo
     * @return
     * @throws Throwable
     */
    @Transactional(rollbackFor = Exception.class)
    public List<SubOrder2DeliveryOrderListEntry> processInsert(SplitOrderKafkaVo vo)  {
        if (null == vo || null == vo.getOrderId()) {
            return null;
        }
        Order order = orderMapper.selectByPrimaryKey(vo.getOrderId());
        //1-拆单，生成subOrder
        this.createSubOrder(order, SubOrderStatusEnums.SUB_ORDER_UNDELIVERY.getCode());
        //2-修改拆单状态
        //updateSplitOrderStatus(orderId, SplitOrderStatusEnums.PICKED);
        //3-如果是当天的 或者 明天且过了覆盖时间的 订单，返回生成发货单需要的数据
        List<SubOrder2DeliveryOrderListEntry> entryList = new ArrayList<>();
        if (checkOrderCoverTime(order)) {
            entryList = this.getDeliveryOrderEntry(new ArrayList<Long>() {{
                add(vo.getOrderId());
            }},DeliveryOrderTypeEnums.SALE.getCode());
        }

        //事务完成发送消息
        TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronizationAdapter() {
            @Override
            public void afterCommit() {
                //异步 维护在途数量
                orderShelvesRecommendService.insertOrUpdateShopOrderedQuantity(order.getOrderCode(), OperateTypeEnums.新增.getCode());
            }
        });
        return entryList;
    }

    /**
     * 修改订单
     * 1.验证订单是否已经生成了拣货单
     * 如果未生成拣货单，则取消原来的SO和DO，生成新的SO(新SO状态为0)和DO单
     * 如果已生成拣货单，则原来的SO和DO状态不变，只生成新的SO(新SO状态为3)
     *
     * @param vo
     * @return
     * @throws Throwable
     */
    @Transactional(rollbackFor = Exception.class)
    public List<SubOrder2DeliveryOrderListEntry> processUpdate(SplitOrderKafkaVo vo)  {
        if (null == vo || null == vo.getOrderId()) {
            return null;
        }
        List<SubOrder2DeliveryOrderListEntry> entryList = new ArrayList<>();
        Long orderId = vo.getOrderId();
        Order order = orderMapper.selectByPrimaryKey(orderId);
        //1-验证当没有splitOrder时，新增splitOrder
        //addSplitOrder(orderId);
        //验证订单是否已经生成拣货单，true=订单已经生成拣货单,false=未生成拣货单
        boolean pickOrderFlag = judgeCheckPickOrder(orderId);
        if (pickOrderFlag) {
            //如果已经生成拣货单：原来的SO和DO不变，生成新的SO（状态为3=过了覆盖时间修改已生成拣货单的订单）
            createSubOrder(order, SubOrderStatusEnums.SUB_ORDER_UPDATE_AFTER_PICK.getCode());
        } else {
            //如果没有生成拣货单：取消原来的SO和DO，生成新的SO（状态为0=未生成do单）
            cancelSubOrderAndDeliveryOrder(orderId);
            createSubOrder(order, SubOrderStatusEnums.SUB_ORDER_UNDELIVERY.getCode());
            if (checkOrderCoverTime(order)) {
                entryList = this.getDeliveryOrderEntry(new ArrayList<Long>() {{
                    add(orderId);
                }},DeliveryOrderTypeEnums.SALE.getCode());
            }
        }
        return entryList;
    }

    /**
     * 取消订单
     * 如果订单未生成拣货单，取消原来的SO和DO；已生成拣货单不处理
     *
     * @param vo
     */
    @Transactional(rollbackFor = Exception.class)
    public void processCancel(SplitOrderKafkaVo vo) {
        if (null == vo || null == vo.getOrderId()) {
            return;
        }
        Long orderId = vo.getOrderId();
        boolean pickOrderFlag = judgeCheckPickOrder(orderId);
        //如果未生成拣货单，取消原来的子单和DO单
        if (!pickOrderFlag) {
            cancelSubOrderAndDeliveryOrder(orderId);

            //事务完成发送消息
            TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronizationAdapter() {
                @Override
                public void afterCommit() {
                    Order order = orderMapper.selectByPrimaryKey(orderId);
                    //异步 维护在途数量
                    orderShelvesRecommendService.insertOrUpdateShopOrderedQuantity(order.getOrderCode(), OperateTypeEnums.删除.getCode());
                }
            });
        }
    }

    /**
     * 修改或取消订单时，如果订单未生成拣货单，取消原来的SO和DO
     *
     * @param orderId
     */
    @Transactional(rollbackFor = Exception.class)
    public void cancelSubOrderAndDeliveryOrder(Long orderId) {
        Example ex = new Example(SubOrder.class);
        ex.createCriteria().andEqualTo("orderId", orderId);
        ex.selectProperties("id");
        List<SubOrder> subOrderList = subOrderMapper.selectByExample(ex);
        if (SpringUtil.isNotEmpty(subOrderList)) {
            List<Long> subOrderIdList = subOrderList.stream().map(SubOrder::getId).distinct().collect(Collectors.toList());
            //取消SO单
            subOrderService.updateSubOrderStatus(subOrderIdList, SubOrderStatusEnums.SUB_ORDER_CANCEL.getCode());
            //取消DO单
            Integer count = deliveryOrderClient.cancelDeliveryOrder(subOrderIdList);
            QYAssert.notNull(count, "DO单取消失败，subOrderIdList：" + subOrderIdList.toString());
        }
    }

    /**
     * 订单拆单，生成subOrder
     *
     * @param order
     * @param subOrderStatus :用于设置新增SO的状态: 如果为true表示订单已经生成拣货单，SO状态为3(过了覆盖时间修改已生成拣货单的订单)；如果为false：新增SO状态为0(未生成do单)。
     */
    @Transactional(rollbackFor = Exception.class)
    public void createSubOrder(Order order, Integer subOrderStatus) {
        if (order == null || order.getOrderStatus().intValue() != OrderStatusEnums.NORMAL.getCode()) {
            log.warn("拆单异常：订单不存在或状态不正常，orderId：{} orderStatus {}" , order.getId(), order.getOrderStatus());
            return;
        }

        //查询最终订单明细，包括商品的物流模式，用于拆单
        List<SplitOrderListEntry> list = splitOrderMapper.querySplitOrderDetail(order.getId());
        if (null == list || list.isEmpty()) {
            log.warn("拆单异常：订单明细不存在，orderId：{}", order.getId());
            return;
        }

        // 查询t_order_list_gift 里面的配货商品，如果不在 t_order_list 里面，则加入t_order_list里面，进行拆单
        List<SplitOrderListEntry> giftRationlist = splitOrderMapper.queryGiftOrderRationDetail(order.getId());
        if(CollectionUtils.isNotEmpty(giftRationlist)){
            Map<String, List<SplitOrderListEntry>> orderListMap = list.stream().collect(Collectors.groupingBy(item -> {
                return item.getCommodityId() + "_" + item.getType();
            }));

            for(SplitOrderListEntry giftRation: giftRationlist){
                String key = giftRation.getCommodityId() + "_" + giftRation.getType();
                if(!orderListMap.containsKey(key)){
                    list.add(giftRation);
                }
            }
        }

        // 1.客服下单和生鲜下单遇到配比并且特价拆行的情况下，也要给拆单子单里面
        // 2.配比的数量也拆单子单里面
        // 配比查询的是正常商品
        setRatioOrderList(order, list);

        // 查询通达的默认仓库
        Map<Long, WarehouseODTO> tdDefaultWarehouseMap = new HashMap<>();
        List<Long> commodityIds = list.stream().mapToLong(SplitOrderListEntry::getCommodityId).boxed().distinct().collect(Collectors.toList());
        Boolean isTdaStore = DeliveryOrderTypeEnums.TD_SALE.getCode().equals(order.getBusinessType());
        if(isTdaStore){
            QueryTdDefaultWarehouseReqIDTO reqVo = new QueryTdDefaultWarehouseReqIDTO();
            reqVo.setLogisticsCenterId(order.getLogisticsCenterId());
            reqVo.setCommodityIds(commodityIds);
            tdDefaultWarehouseMap = tdClient.getTdDefaultWarehouse(reqVo);
        }

        //查商品的默认仓库和供应商
        Map<Long, CommodityDefaultDcODto> commodityDefaultMap = commodityWarehouseClient.queryCommodityDefaultWarehouseAndSupplier(commodityIds);
        if (commodityDefaultMap.isEmpty()) {
            log.warn("拆单异常：商品默认仓库或者供应商不存在，订单id {} commodityIds：{}", order.getId(), commodityIds);
            return;
        }

        // B端库存依据,设置库存依据、预售非预售、多箱规转换
        setBStockType(order.getPresaleStatus(), list);

        List<SplitOrderListEntry> splitOrderList = new ArrayList<>(); // 符合拆单的明细list
        for (SplitOrderListEntry item : list) {
            if(isTdaStore){
                WarehouseODTO warehouseODTO = tdDefaultWarehouseMap.get(item.getCommodityId());
                if(warehouseODTO != null){
                    item.setWarehouseId(warehouseODTO.getWarehouseId());
                }

                if(item.getWarehouseId() == null){
                    log.warn("拆单异常：商品没有设置默认仓库，订单id {} commodityId：{}", order.getId(), item.getCommodityId());
                    continue;
                }

                if(CombTypeEnum.COMB.getCode().equals(item.getCombType())){ // 组合商品不进subOrder表
                    continue;
                }

                CommodityDefaultDcODto commodityDefaultODTO = commodityDefaultMap.get(item.getCommodityId());
                if (commodityDefaultODTO != null) {
                    item.setSupplierId(commodityDefaultODTO.getSupplierId());
                }

                // 符合拆单的明细
                splitOrderList.add(item);

            }else {
                //设置商品的默认仓库和默认供应商
                CommodityDefaultDcODto commodityDefaultODTO = commodityDefaultMap.get(item.getCommodityId());
                if (commodityDefaultODTO != null) {
                    item.setWarehouseId(commodityDefaultODTO.getWarehouseId());
                    item.setSupplierId(commodityDefaultODTO.getSupplierId());
                }
                Integer logicModel = item.getLogisticsModel();
                //进大仓的物流模式
                Boolean isDcModel = IogisticsModelEnums.isDcLogisticsModel(logicModel);
                if (isDcModel && null == item.getWarehouseId()) {
                    log.warn("拆单异常：商品没有设置默认仓库，订单id {} commodityId：{}", order.getId(), item.getCommodityId());
                    continue;
                }
                if (!isDcModel && null == item.getSupplierId()) {
                    log.warn("拆单异常：商品没有设置默认供应商，订单id {} commodityId：{}", order.getId(), item.getCommodityId());
                    continue;
                }

                if(CombTypeEnum.COMB.getCode().equals(item.getCombType())){ // 组合商品不进subOrder表
                    continue;
                }

                // 符合拆单的明细
                splitOrderList.add(item);
            }
        }

        if(CollectionUtils.isEmpty(splitOrderList)){
            log.warn("拆单异常：订单明细不符合拆单条件，orderId：{}", order.getId());
            return;
        }

        // 拆单 //Map<物流模式+库存依据,<(配送|直通)仓库|(直送)供应商|, List<vo>> map;
        Map<String, List<SplitOrderListEntry>> splitMap = new HashMap<>();
        String splitCode = "_";
        if(isTdaStore){
            // 通达按照首选通达仓库+库存依据+配送批次+送货日期拆单
            splitMap = splitOrderList.stream().collect(Collectors.groupingBy(item -> {
                // 同一个订单配送批次和送货日期都是一样的
                String groupByField = item.getWarehouseId() + splitCode + item.getStockType();
                return groupByField;
            }));
        }else {
            splitMap = splitOrderList.stream().collect(Collectors.groupingBy(item -> {
                // 拆单逻辑，(配送|直通)仓库 | (直送)供应商
                String groupByField = item.getLogisticsModel() + splitCode + item.getStockType();
                if (item.getLogisticsModel() == IogisticsModelEnums.DISPATCHING.getCode()) {
                    return groupByField + splitCode + item.getWarehouseId();
                } else if (item.getLogisticsModel() == IogisticsModelEnums.DIRECT_CONNECTION.getCode()) {
                    return groupByField + splitCode + item.getWarehouseId() + splitCode + item.getSupplierId();
                }  else {
                    return groupByField + splitCode + item.getSupplierId();
                }
            }));
        }
        insertSubOrderSplit(order, splitMap, subOrderStatus, isTdaStore);
        orderAsyncKafkaService.sendKafkaAsyncOrderToDCMessage(order);
    }

    private void setRatioOrderList(Order order, List<SplitOrderListEntry> list) {
        if(OrderTypeEnum.PC_ORDER.getCode().equals(order.getOrderType())
                || OrderTypeEnum.APP_ORDER.getCode().equals(order.getOrderType())) {
            // 查询orderList和orderGiftList,相同commodity + price一致，但是数量不一致。说明存在配比
            List<SplitOrderListEntry> giftProductList = splitOrderMapper.queryGiftOrderProductDetail(order.getId());
            List<OrderList>  productList = orderListService.getOrderListsProductByOrderId(order.getId());

            // 获取库存依据
            Map<Long, BigDecimal> orderQuantityGiftMap = new HashMap<>();
            productList.forEach(dto ->{
                orderQuantityGiftMap.put(dto.getCommodityId(), dto.getCommodityNum());
            });
            Map<Long, CommodityInventoryODTO> getbStockMap = bStockService.getbStockMap(DateUtil.parseDate(DateTimeUtil.defaultDeliveryDate(), "yyyy-MM-dd"), orderQuantityGiftMap);

            Map<String, List<OrderList>> orderListMap = productList.stream().collect(Collectors.groupingBy(item -> {
                return item.getCommodityId() + "_" + item.getCommodityPrice().stripTrailingZeros().toPlainString();
            }));

            // orderGiftList有的，orderList没有的正常需要拆单
            giftProductList.forEach(giftDto ->{
                CommodityInventoryODTO inventoryODTO = getbStockMap.get(giftDto.getCommodityId());
                // 只有不限量的才会配比
                if(inventoryODTO != null && StockTypeEnum.UN_LIMIT.getCode().equals(inventoryODTO.getStockType())) {
                    String key = giftDto.getCommodityId() + "_" + giftDto.getPrice().stripTrailingZeros().toPlainString();
                    if(!orderListMap.containsKey(key)) {
                        // 此种情况很少，基本上忽略
                        list.add(giftDto);
                    }
                }
            });

            // 配比的商品进行商品数量覆盖
            Map<String, List<SplitOrderListEntry>> orderGiftListMap = giftProductList.stream().collect(Collectors.groupingBy(item -> {
                return item.getCommodityId() + "_" + item.getPrice().stripTrailingZeros().toPlainString();
            }));

            list.forEach(listEntry ->{
                CommodityInventoryODTO inventoryODTO = getbStockMap.get(listEntry.getCommodityId());
                // 只有不限量的才会配比
                if(ProductTypeEnums.PRODUCT.getCode().equals(listEntry.getType()) &&
                        inventoryODTO != null && StockTypeEnum.UN_LIMIT.getCode().equals(inventoryODTO.getStockType())) {
                    String key = listEntry.getCommodityId() + "_" + listEntry.getPrice().stripTrailingZeros().toPlainString();
                    if(orderGiftListMap.containsKey(key)) {
                        SplitOrderListEntry orderListEntry = orderGiftListMap.get(key).get(0);
                        if(listEntry.getQuantity().compareTo(orderListEntry.getQuantity()) != 0){
                            listEntry.setQuantity(orderListEntry.getQuantity());
                            listEntry.setTotalPrice(listEntry.getQuantity().multiply(listEntry.getPrice()));
                        }
                    }
                }
            });
        }
    }


    @Transactional(rollbackFor = Exception.class)
    public void insertSubOrderSplit(Order order, Map<String, List<SplitOrderListEntry>> splitMap, Integer subOrderStatus, Boolean isTdaStore) {
        Date date = new Date();
        splitMap.forEach((key, value) -> {
            List<SplitOrderListEntry> splitOrderList = value;
            SplitOrderListEntry entry = splitOrderList.get(0);
            SubOrder subOrder = new SubOrder();
            subOrder.setOrderId(order.getId());
            subOrder.setOrderTime(order.getOrderTime());
            subOrder.setStatus(subOrderStatus);
            subOrder.setLogisticsModel(entry.getLogisticsModel());
            subOrder.setVarietyTotal(splitOrderList.size());
            subOrder.setCreateId(order.getCreateId());
            subOrder.setCreateTime(date);
            subOrder.setEnterpriseId(QingyunConstant.ENTERPRISE_ID);
            //subOrder.setWarehouseId(entry.getWarehouseId());
            //subOrder.setSupplierId(entry.getSupplierId());
            subOrder.setWarehouseId(entry.getWarehouseId());
            subOrder.setSupplierId(entry.getSupplierId());

            subOrder.setPresaleStatus(order.getPresaleStatus() != null ? order.getPresaleStatus() : YesOrNoEnums.NO.getCode());
            subOrder.setStockType(entry.getStockType());
            subOrderMapper.insertSelective(subOrder);


            List<SubOrderItem> subOrderItems = new ArrayList<SubOrderItem>();
            for (SplitOrderListEntry item : splitOrderList) {
                SubOrderItem subOrderItem = new SubOrderItem();
                subOrderItem.setCommodityId(item.getCommodityId());
                subOrderItem.setCreateId(order.getCreateId());
                subOrderItem.setCreateTime(date);
                subOrderItem.setUpdateTime(new Date());
                subOrderItem.setPrice(item.getPrice());
                subOrderItem.setQuantity(item.getQuantity());
                subOrderItem.setSubOrderId(subOrder.getId());
                subOrderItem.setTotalPrice(item.getTotalPrice());

                subOrderItem.setSourceRatio(item.getSourceRatio());
                subOrderItem.setTargetRatio(item.getTargetRatio());
                subOrderItem.setConvertStatus(item.getConvertStatus());
                subOrderItem.setTargetQuantity(item.getTargetQuantity());
                subOrderItem.setTargetCommodityId(item.getTargetCommodityId());

                //QYAssert.isTrue(settings != null && !settings.isEmpty(), "没有查询到相应的店铺订单设置!"+itemVo.getCommodityId()+"-"+subOrderAndItemVo.getStoreId());
                subOrderItem.setType(item.getType());
                subOrderItem.setCombType(item.getCombType());

                // 只有组合子品才有 属于组合商品id 和 t_order_list表组合商品的主键id
                if(CombTypeEnum.COMB_CHILD.getCode().equals(item.getCombType())){
                    subOrderItem.setCombCommodityId(item.getCombCommodityId());
                }

                subOrderItem.setCombOrderListId(item.getOrderListId());
                subOrderItem.setPricePromotionId(item.getPricePromotionId());
                subOrderItems.add(subOrderItem);
            }

            /*// 根据商品id、价格排序。赠品放最后
            List<SubOrderItem> normalList = subOrderItems.stream().filter(p -> p.getPrice().compareTo(BigDecimal.ZERO) > 0).collect(Collectors.toList());
            Comparator<SubOrderItem> byPrice = Comparator.comparing(SubOrderItem::getPrice);
            List<SubOrderItem> sortItems = normalList.stream().sorted(Comparator.comparing(SubOrderItem::getCommodityId)
                    .thenComparing(byPrice)).collect(Collectors.toList());

            List<SubOrderItem> giftList = subOrderItems.stream().filter(p -> p.getPrice().compareTo(BigDecimal.ZERO) == 0).collect(Collectors.toList());
            if(CollectionUtils.isNotEmpty(giftList)){
                sortItems.addAll(giftList);
            }*/
            subOrderItemMapper.insertList(subOrderItems);
        });
    }

    /**
     * 设置库存依据、预售非预售、多箱规转换
     * @param presaleStatus
     * @param list
     */
    private void setBStockType(Integer presaleStatus, List<SplitOrderListEntry> list) {
        Map<Long, BigDecimal> orderQuantityGiftMap = new HashMap<>();
        list.forEach(dto ->{
            orderQuantityGiftMap.put(dto.getCommodityId(), dto.getQuantity());
        });
        Map<Long, CommodityInventoryODTO> getbStockMap = bStockService.getbStockMap(DateUtil.parseDate(DateTimeUtil.defaultDeliveryDate(), "yyyy-MM-dd"), orderQuantityGiftMap);
        list.forEach(dto ->{
            dto.setPresaleStatus(presaleStatus != null ? presaleStatus : YesOrNoEnums.NO.getCode());
            CommodityInventoryODTO commodityInventoryODTO = getbStockMap.get(dto.getCommodityId());
            dto.setStockType(commodityInventoryODTO != null ? commodityInventoryODTO.getStockType() : StockTypeEnum.UN_LIMIT.getCode());
            if (Objects.nonNull(commodityInventoryODTO)) {
                Integer sourceRatio = commodityInventoryODTO.getSourceRatio();
                dto.setSourceRatio(sourceRatio);
                Integer targetRatio = commodityInventoryODTO.getTargetRatio();
                dto.setTargetRatio(targetRatio);
                Long targetCommodityId = commodityInventoryODTO.getTargetCommodityId();
                dto.setTargetCommodityId(targetCommodityId);
                Integer convertStatus = commodityInventoryODTO.getCanConvert() ? 1 : 0;
                dto.setConvertStatus(convertStatus);
                BigDecimal quantity = dto.getQuantity();
                // 组合商品目标数量 : 向上取整 ( quantity / source_ratio * target_ratio)
                if (commodityInventoryODTO.getCanConvert()) {
                    BigDecimal targetQuantity = quantity.divide(new BigDecimal(sourceRatio), 0, RoundingMode.UP).multiply(new BigDecimal(targetRatio));
                    dto.setTargetQuantity(targetQuantity);
                }
            }

        });
    }


    //处理subOrder
    @Transactional(rollbackFor = Exception.class)
    public void insertSubOrder(Order order, Map<String, Map<Long, List<SplitOrderListEntry>>> map, Integer subOrderStatus,Map<Long, SplitOrderListEntry> splitOrderListEntryMap) {
        if (null == map || map.isEmpty()) {
            log.warn("执行拆单insertSubOrder异常：map is null");
            return;
        }
        //注释：新增SO时不需要再验证t_split_order的状态
//        //校验是否已经拆过
//        if (!checkSplitOrderStatus(vo.getOrderId())) {
//            log.info("执行拆单checkSplitOrderStatus未通过");
//            return;
//        }
        Set<String> sets = map.keySet();
        Iterator<String> its = sets.iterator();
        Date date = new Date();
        while (its.hasNext()) {
            String logisticsAndStockType = its.next();
            String [] str = logisticsAndStockType.split(",");
            Integer logisticsModel = Integer.parseInt(str[0]);
            Integer stockType = Integer.parseInt(str[1]);

            Map<Long, List<SplitOrderListEntry>> childMap = map.get(logisticsAndStockType);
            Set<Long> childSets = childMap.keySet();
            Iterator<Long> childIts = childSets.iterator();
            while (childIts.hasNext()) {
                Long key = childIts.next();
                SubOrder subOrder = new SubOrder();
                subOrder.setOrderId(order.getId());
                subOrder.setOrderTime(order.getOrderTime());
                subOrder.setStatus(subOrderStatus);
                subOrder.setLogisticsModel(logisticsModel);
                subOrder.setVarietyTotal(childMap.get(key).size());
                subOrder.setCreateId(order.getCreateId());
                subOrder.setCreateTime(date);
                subOrder.setEnterpriseId(QingyunConstant.ENTERPRISE_ID);
                if (logisticsModel.intValue() == IogisticsModelEnums.DISPATCHING.getCode() || logisticsModel.intValue() == IogisticsModelEnums.DIRECT_CONNECTION.getCode()) {
                    subOrder.setWarehouseId(key);
                } else if (logisticsModel.intValue() == IogisticsModelEnums.DIRECT_SENDING.getCode()) {
                    subOrder.setSupplierId(key);
                }
                subOrder.setPresaleStatus(order.getPresaleStatus() != null ? order.getPresaleStatus() : YesOrNoEnums.NO.getCode());
                subOrder.setStockType(stockType);
                subOrderMapper.insertSelective(subOrder);
                List<SplitOrderListEntry> list = childMap.get(key);
                List<SubOrderItem> items = new ArrayList<SubOrderItem>();
                for (SplitOrderListEntry item : list) {
                    SplitOrderListEntry splitOrder = splitOrderListEntryMap.get(item.getCommodityId());
                    SubOrderItem subOrderItem = new SubOrderItem();
                    subOrderItem.setCommodityId(item.getCommodityId());
                    subOrderItem.setCreateId(order.getCreateId());
                    subOrderItem.setCreateTime(date);
                    subOrderItem.setUpdateTime(new Date());
                    subOrderItem.setPrice(item.getPrice());
                    subOrderItem.setQuantity(item.getQuantity());
                    subOrderItem.setSubOrderId(subOrder.getId());
                    subOrderItem.setTotalPrice(item.getTotalPrice());
                    if (Objects.nonNull(splitOrder)){
                        subOrderItem.setSourceRatio(splitOrder.getSourceRatio());
                        subOrderItem.setTargetRatio(splitOrder.getTargetRatio());
                        subOrderItem.setConvertStatus(splitOrder.getConvertStatus());
                        subOrderItem.setTargetQuantity(splitOrder.getTargetQuantity());
                        subOrderItem.setTargetCommodityId(splitOrder.getTargetCommodityId());
                    }
                    items.add(subOrderItem);
                }
                // 同一个商品正常品排前面，其次特惠，赠品
                Comparator<SubOrderItem> byPrice = Comparator.comparing(SubOrderItem::getPrice).reversed();
                List<SubOrderItem> sortItems = items.stream().sorted(Comparator.comparing(SubOrderItem::getCommodityId)
                                        .thenComparing(byPrice)).collect(Collectors.toList());
                subOrderItemMapper.insertList(sortItems);
            }
        }
    }


    /**
     * 检查给定的订单ID是否已经存在拣货单
     *
     * @param orderId 订单ID
     * @return 如果该订单已经生成了拣货单返回true, 否则返回false
     */
    private boolean judgeCheckPickOrder(Long orderId) {
        List<Long> orderIds = Arrays.asList(orderId);
        Map<Long, Boolean> resultMap = deliveryOrderClient.checkOrderGeneratePickOrder(orderIds);
        return resultMap.get(orderId);
    }

    /**
     * 验证订单是否是 当天 或 明天的过了覆盖时间
     * true=当天或明天已过覆盖时间的订单，需要生成发货单
     *
     * @param order
     * @return
     */
    public Boolean checkOrderCoverTime(Order order) {
        if (order == null || order.getOrderStatus().intValue() != OrderStatusEnums.NORMAL.getCode()) {
            return false;
        }
        String orderTime = DateUtil.get4yMd(order.getOrderTime());
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(new Date());
        String today = DateUtil.get4yMd(calendar.getTime());
        calendar.add(Calendar.DAY_OF_MONTH, 1);
        String nextDay = DateUtil.get4yMd(calendar.getTime());
        if (orderTime.equals(today)) {
            //订单时间等于当天
            return true;
        }
        if (orderTime.equals(nextDay)) {
            //订单时间等于明天，需要校验当前时间是否已经过了覆盖时间，如果过了覆盖时间：1-生成发货单，2-改SO状态
            String currentTime = new SimpleDateFormat("HH:mm").format(calendar.getTime());

            DeliveryTime deliveryTime = deliveryTimeMapper.getDeliveryTimeByStoreId(order.getStoreId());
            if (deliveryTime == null) {
                return false;
            }
            if (currentTime.compareTo(deliveryTime.getCoverTime()) > 0) {
                return true;
            }
        }
        return false;
    }

    /**
     * 查询SO转DO单的数据
     *
     * @param orderIdList
     * @return
     * @throws Throwable
     */
    public List<SubOrder2DeliveryOrderListEntry> getDeliveryOrderEntry(List<Long> orderIdList,Integer businessType)  {
        if (SpringUtil.isEmpty(orderIdList)) {
            return null;
        }
        List<SubOrder2DeliveryOrderListEntry> resultList = new ArrayList<>();
        //SQL已过滤 物流模式为 配送 或鲜食客户的直通
//        Map<String, List<Long>> dictionaryMap = new HashMap<>();
//        if(DeliveryOrderTypeEnums.SALE.getCode().equals(businessType)){
//            dictionaryMap = dictionaryService.queryDirectDcConfig();
//        }
        //获取订单下的所有子单
        List<SubOrder2DeliveryOrderListEntry> subOrderList = subOrderMapper.listSubOrder2DeliveryOrder(orderIdList, null,businessType);
        if (SpringUtil.isNotEmpty(subOrderList)) {
            //获取所有子单明细
            List<Long> subOrderIdList = subOrderList.stream().map(SubOrder2DeliveryOrderListEntry::getSubOrderId).distinct().collect(Collectors.toList());
            List<SubOrder2DeliveryOrderItemEntry> subOrderItemList = subOrderMapper.listSubOrder2DeliveryOrderItem(subOrderIdList, SubOrderStatusEnums.SUB_ORDER_UNDELIVERY.getCode(),businessType);
            //根据子单进行分组，一个子单对应该子单下的所有子单明细
            Map<Long, List<SubOrder2DeliveryOrderItemEntry>> subOrderItemMap = new HashMap<>();
            if (SpringUtil.isNotEmpty(subOrderItemList)) {
                subOrderItemMap = subOrderItemList.stream().collect(Collectors.groupingBy(SubOrder2DeliveryOrderItemEntry::getSubOrderId));
            }
            //设置子单与子单明细关系
            for (SubOrder2DeliveryOrderListEntry subOrder : subOrderList) {
                List<SubOrder2DeliveryOrderItemEntry> subOrderItems = subOrderItemMap.get(subOrder.getSubOrderId());
                if (SpringUtil.isNotEmpty(subOrderItems)) {
                    subOrder.setItemList(subOrderItems);
                    resultList.add(subOrder);
                }
            }
        }
        return resultList;
    }

    /**
     * 发送消息，so转DO
     *
     * @param so2DoList
     */
    public void sendSo2DoKafkaMessage(List<SubOrder2DeliveryOrderListEntry> so2DoList) {
        if (SpringUtil.isNotEmpty(so2DoList)) {
            List<Long> subOrderIds = so2DoList.stream().map(SubOrder2DeliveryOrderListEntry::getSubOrderId).distinct().collect(Collectors.toList());
            subOrderConfirmMapper.insertList(SubOrderConfirm.initList(subOrderIds));
            //发消息，处理子单生成发货单
            //MessageWrapper message = new MessageWrapper(MessageType.SUB_TO_DELIVERY, so2DoList, MessageOperationType.INSERT, UUID.randomUUID().toString());
            mqSenderComponent.send(applicationNameSwitch + KafkaConstant.SUB_TO_DELIVERY,
                    so2DoList,
                    MqMessage.MQ_KAFKA,
                    KafkaMessageTypeEnum.SUB_TO_DELIVERY.name(),
                    KafkaMessageOperationTypeEnum.INSERT.name());
        }
    }


    /**
     * 手动--未拆单查询列表
     *
     * @param nonSplitOrderQueryVo
     * @return
     */
    public List<NonSplitOrderVo> findNonSplitOrderList(NonSplitOrderQueryVo nonSplitOrderQueryVo) {
        return splitOrderMapper.findNonSplitOrderList(nonSplitOrderQueryVo);
    }

    /**
     * 根据订单ID，查询未拆单详情(物流模式，仓库，供应商)
     *
     * @param orderId
     * @return
     */
    public List<NonSplitCommodityDetail> findNonSplitByOrderId(Long orderId) {
        List<NonSplitCommodityDetail> detailList = splitOrderMapper.findNonSplitByOrderId(orderId);
        if (SpringUtil.isNotEmpty(detailList)) {
            List<Long> commodityIds = detailList.stream().mapToLong(NonSplitCommodityDetail::getCommodityId).boxed().distinct().collect(Collectors.toList());
            Map<Long, CommodityDefaultDcODto> commodityDefaultMap = commodityWarehouseClient.queryCommodityDefaultWarehouseAndSupplier(commodityIds);
            for (NonSplitCommodityDetail item : detailList) {
                //设置商品的默认仓库和默认供应商
                CommodityDefaultDcODto defaultDTO = commodityDefaultMap.get(item.getCommodityId());
                if (defaultDTO != null) {
                    item.setWarehouseId(defaultDTO.getWarehouseId());
                    item.setWarehouseName(defaultDTO.getWarehouseName());
                    item.setSupplierId(defaultDTO.getSupplierId());
                    item.setSupplierName(defaultDTO.getSupplierName());
                }
            }
        }
        return detailList;
    }

    /**
     * 手动执行拆单
     *
     * @param nonSplitOrderQueryVo
     */
    @Transactional(rollbackFor = Exception.class)
    public void manualSplitOrder(NonSplitOrderQueryVo nonSplitOrderQueryVo) {
        QYAssert.isTrue(nonSplitOrderQueryVo.getOrderTime() != null, "送货日期不能为空");
        List<Long> notSplitOrderIdList = splitOrderMapper.findNonSplitOrder(nonSplitOrderQueryVo);
        if (SpringUtil.isNotEmpty(notSplitOrderIdList)) {
            notSplitOrderIdList.forEach(orderId -> {
                SplitOrderKafkaVo dto = new SplitOrderKafkaVo();
                dto.setOrderId(orderId);
                dto.setType(KafkaMessageOperationTypeEnum.UPDATE);
                dto.setEnterpriseId(QingyunConstant.ENTERPRISE_ID);
                try {
                    updateOrderStoreTypeId(dto.getOrderId());
                    List<SubOrder2DeliveryOrderListEntry> list = this.processUpdate(dto);
                    if (SpringUtil.isNotEmpty(list)) {
                        //发消息，处理子单生成发货单
                        this.sendSo2DoKafkaMessage(list);
                    }
                } catch (Throwable throwable) {
                    log.error("手动拆单异常");
                }
            });
        }
    }

    /**
     * 定时任务执行未拆单或者少拆单的单子
     * 拆单补偿只针对客服下单、生鲜下单、鲜达APP下单
     * @param createTime yyyy-MM-dd
     * @return
     */
    public Boolean splitOrderByJob(String createTime){
        String startTime = null;
        String endTime = null;
        if(StringUtil.isNotBlank(createTime)) {
            startTime = createTime + " 00:00:01";
            endTime = createTime + " 23:59:59";
        }else {
            String nowTime = DateUtil.getDateFormate(new Date(), "yyyy-MM-dd");
            startTime = nowTime + " 00:00:01";
            endTime = nowTime + " 23:59:59";
        }

        List<String> splitOrderIdList = splitOrderMapper.findUnSplitOrderList(startTime, endTime);
        // 存在未拆单的订单
        if(CollectionUtils.isNotEmpty(splitOrderIdList)) {
            // 发消息告警
            weChatSendMessageService.sendWeChatMessage("有未拆单的订单，请排查原因！" + String.join(",",splitOrderIdList));

            splitOrderIdList.forEach(orderId -> {
                SplitOrderKafkaVo dto = new SplitOrderKafkaVo();
                dto.setOrderId(Long.valueOf(orderId));
                dto.setType(KafkaMessageOperationTypeEnum.UPDATE);
                dto.setEnterpriseId(QingyunConstant.ENTERPRISE_ID);
                try {
                    updateOrderStoreTypeId(dto.getOrderId());
                    List<SubOrder2DeliveryOrderListEntry> list = this.processUpdate(dto);
                    if (SpringUtil.isNotEmpty(list)) {
                        //发消息，处理子单生成发货单
                        this.sendSo2DoKafkaMessage(list);
                    }
                } catch (Exception e) {
                    log.warn("定时任务执行未拆单或者少拆单的单子异常, orderId {}", orderId, e);
                }
            });
        }
        return Boolean.TRUE;
    }
}
