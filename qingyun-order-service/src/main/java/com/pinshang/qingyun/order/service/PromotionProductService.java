package com.pinshang.qingyun.order.service;

import com.pinshang.qingyun.order.mapper.PromotionMapper;
import com.pinshang.qingyun.order.mapper.PromotionProductMapper;
import com.pinshang.qingyun.order.model.promotion.Promotion;
import com.pinshang.qingyun.order.model.promotion.PromotionProduct;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;

import java.util.List;

/**
 * @Author: sk
 * @Date: 2024/7/18
 */
@Slf4j
@Service
public class PromotionProductService {

    @Autowired
    PromotionProductMapper promotionProductMapper;

    public List<PromotionProduct> findListByPromotionId(Long promotionId){
        Example example = new Example(PromotionProduct.class);
        example.createCriteria().andEqualTo("promotionId",promotionId);
        return promotionProductMapper.selectByExample(example);
    }


}
