package com.pinshang.qingyun.order.service.externalDocking;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.pinshang.qingyun.base.configure.expand.QYAssert;
import com.pinshang.qingyun.box.utils.ConcurrentDateUtil;
import com.pinshang.qingyun.box.utils.SpringUtil;
import com.pinshang.qingyun.order.mapper.GhOrderMapper;
import com.pinshang.qingyun.order.mapper.entry.externalDocking.GhOrderEntry;
import com.pinshang.qingyun.order.vo.externalDocking.GhOrderVo;
import org.springframework.stereotype.Service;

import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;

/**
 * 高校订单
 * <AUTHOR>
 * @date 2023年2月15日10:00:09
 */
@Service
public class GhOrderService {

    private final GhOrderMapper ghOrderMapper;

    public GhOrderService(GhOrderMapper ghOrderMapper) {
        this.ghOrderMapper = ghOrderMapper;
    }

    /**
     * 根据条件查询高校订单列表
     * @param vo
     * @return
     */
    public PageInfo<GhOrderEntry> findGhOrderPageInfo(GhOrderVo vo){
        QYAssert.isTrue(null != vo, "参数异常");
        PageInfo<GhOrderEntry> pageDate = PageHelper.startPage(vo.getPageNo(), vo.getPageSize()).doSelectPageInfo(() -> {
            ghOrderMapper.findGhOrderPageInfo(vo);
        });
        DateFormat dateFormat = ConcurrentDateUtil.SDF_FULL_DATE_WITHOUT_UNDERLINE.get();

        DateFormat dateFormat1 = ConcurrentDateUtil.SDF_FULL_DATE.get();

        if(SpringUtil.isNotEmpty(pageDate.getList())){
            for(GhOrderEntry entry :pageDate.getList()){
                try {
                Date date= dateFormat1.parse(entry.getOrderTime());
                Calendar calendar = Calendar.getInstance();
                calendar.setTime(date);
                calendar.add(Calendar.DAY_OF_MONTH, -1);
                date = calendar.getTime();
                entry.setManufactureDate(dateFormat1.format(date));
                entry.setSupplierBatch(dateFormat.format(date));
                } catch (ParseException e) {
                    throw new RuntimeException(e);
                }
            }
        }
        return pageDate;
    }
}
