package com.pinshang.qingyun.order.mapper;

import com.pinshang.qingyun.order.mapper.entry.store.StoreInviceEntry;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface StoreInvoiceMapper{


    List<StoreInviceEntry> findListByStoreIds(@Param("storeIds") List<Long> storeIds);

    List<StoreInviceEntry> findListByIds(@Param("uid") String uid);
}
