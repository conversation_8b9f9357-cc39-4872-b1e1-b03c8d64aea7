package com.pinshang.qingyun.order.controller.client;

import com.pinshang.qingyun.order.constant.DictionaryCodeConstant;
import com.pinshang.qingyun.order.service.DictionaryService;
import com.pinshang.qingyun.order.vo.DictionaryQueryDCTpyeVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;

/**
 * @auther dy
 * @date 2023/12/21  9:44
 **/
@Slf4j
@RestController
@RequestMapping("/dictionary/client")
public class DictionaryClientController {
    @Autowired
    private DictionaryService dictionaryService;

    @PostMapping("/queryDirectDcConfig")
    public DictionaryQueryDCTpyeVO queryDirectDcConfig() {
        //SQL已过滤 物流模式为 配送 或鲜食客户的直通
        Map<String, List<Long>> dictionaryMap = dictionaryService.queryDirectDcConfig();
        return new DictionaryQueryDCTpyeVO(dictionaryMap.get(DictionaryCodeConstant.STORETYPE_DIRECT_LIST),
                dictionaryMap.get(DictionaryCodeConstant.STORETYPE_ToB_LIST),
                dictionaryMap.get(DictionaryCodeConstant.WAREHOUSE_ToB_LIST));
    }


}
