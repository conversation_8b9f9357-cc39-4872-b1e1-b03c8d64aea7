package com.pinshang.qingyun.order.service.cup.factory;

import com.pinshang.qingyun.order.dto.cup.EditOrderDto;
import com.pinshang.qingyun.order.service.cup.strategy.OrderDetailStrategy;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

@Component
public class OrderDetailStrategyFactory implements ApplicationContextAware {


    private final Map<String, OrderDetailStrategy> map = new ConcurrentHashMap<>();

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        Map<String, OrderDetailStrategy> tempMap = applicationContext.getBeansOfType(OrderDetailStrategy.class);
        tempMap.values().forEach(orderDetailStrategy -> map.put(orderDetailStrategy.getEditType(),orderDetailStrategy));
    }

    public EditOrderDto detail(Long orderId,String editType){
        OrderDetailStrategy orderDetailStrategy = map.get(editType);
        if(null != orderDetailStrategy){
            return orderDetailStrategy.detail(orderId);
        }
        return null;
    }
}
