package com.pinshang.qingyun.order.mapper;


import com.pinshang.qingyun.base.mybatis.MyMapper;
import com.pinshang.qingyun.order.mapper.entry.recharge.XdaPayBillEntry;
import com.pinshang.qingyun.order.mapper.entry.recharge.XdaPayBillJobEntry;
import com.pinshang.qingyun.order.model.recharge.XDAPayBill;
import com.pinshang.qingyun.order.vo.recharge.RechargeSuccessVo;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface XdaPayBillMapper extends MyMapper<XDAPayBill> {

    /**
     * 新增鲜达第三方交易支付
     * @param bill
     * @return
     */
    int insertXdaPayBill(XDAPayBill bill);

    /**
     * 修改鲜达第三方交易支付
     * @param vo
     * @return
     */
    int updateXdaPayBill(RechargeSuccessVo vo);

    /**
     * 新增鲜达第三方交易支付定时补偿
     * @param payBillId
     * @return
     */
    int insertXdaPayBillJob(@Param("payBillId") Long payBillId);

    /**
     * 删除鲜达第三方交易支付定时补偿
     * @param payBillId
     * @return
     */
    int deleteXdaPayBillJob(@Param("payBillId") Long payBillId);

    List<XdaPayBillJobEntry> findXdaPayBillJob();

    /**
     * 查询鲜达支付流水
     * */
    List<XdaPayBillEntry> findXdaPayBillByIds(@Param("billCodes") List<String> billCodes);
}

