package com.pinshang.qingyun.order.manage.delivery.dto;

import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2023/4/10 15:23
 */
@Data
public class OrderDeliveryFinishDTO {
    /*** 订单id */
    private Long orderId;
    /*** 订单号 */
    private String orderCode;

    private Long storeTypeId;
    /*** 客户id */
    private Long storeId;

    /*** 订单记录的客户类型*/
    private Long orderStoreTypeId;

    /**
     * 订单类型(1=PC下单,2=APP下单, 8=鲜达APP 9-批发app 10-门店订货下单 12-清美订单 13-mini团购订单 21-团购订单 22-门店订货PC版 23-门店订货PDA版 24-门店订货代理补货 25-大仓配货自动下单 26-门店自动订货 27-代理订货 28-直送补单 29-直送审核通过 30-提货卡自动下单 31-门店加货申请单 32-日日鲜商品自动下单 33-提货卡预约订单 34-云超团购自动下单 35-玖琅自动下单
     */
    private Integer orderType;

    private Date orderTime;

}
