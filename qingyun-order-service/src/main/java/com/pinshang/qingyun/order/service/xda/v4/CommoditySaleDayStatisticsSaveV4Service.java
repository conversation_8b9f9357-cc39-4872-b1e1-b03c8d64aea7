package com.pinshang.qingyun.order.service.xda.v4;

import com.pinshang.qingyun.base.configure.expand.QYAssert;
import com.pinshang.qingyun.box.utils.DateUtil;
import com.pinshang.qingyun.box.utils.SpringUtil;
import com.pinshang.qingyun.order.dto.xda.v4.CreOrderItemV4DTO;
import com.pinshang.qingyun.order.mapper.CommoditySaleDayStatisticsMapper;
import com.pinshang.qingyun.order.model.order.XdaCommoditySaleDayStatistics;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.entity.Example;

import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

import static java.util.stream.Collectors.groupingBy;

/**
 * <AUTHOR>
 * @Date 2023/12/26 16:33
 */
@Service
@Slf4j
public class CommoditySaleDayStatisticsSaveV4Service {

    @Autowired
    private CommoditySaleDayStatisticsMapper commoditySaleDayStatisticsMapper;


    @Transactional(rollbackFor = Exception.class)
    public Boolean saveOrUpdateSaleDayStatistics(String orderTime, XdaCommoditySaleDayStatistics dayStatistics){
        Example example = new Example(XdaCommoditySaleDayStatistics.class);
        example.createCriteria().andEqualTo("orderTime", orderTime)
                .andEqualTo("commodityId", dayStatistics.getCommodityId());
        List<XdaCommoditySaleDayStatistics> dbList = commoditySaleDayStatisticsMapper.selectByExample(example);

        if(SpringUtil.isNotEmpty(dbList)){
            commoditySaleDayStatisticsMapper.batchUpdate(Collections.singletonList(dayStatistics));
        }else{
            commoditySaleDayStatisticsMapper.insertList(Collections.singletonList(dayStatistics));
        }
        return Boolean.TRUE;
    }
}
