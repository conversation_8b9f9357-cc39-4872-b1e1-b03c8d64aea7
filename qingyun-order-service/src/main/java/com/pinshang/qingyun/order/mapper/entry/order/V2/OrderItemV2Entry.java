package com.pinshang.qingyun.order.mapper.entry.order.V2;

import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 23/6/26/026 18:06
 */
@Data
public class OrderItemV2Entry {
    private Long commodityId;

    private String commodityCode;

    private String commodityName;

    private String commoditySpec;

    private BigDecimal commodityPrice;

    private Integer commodityType;

    private String remark;

    private BigDecimal commodityNum;

    private BigDecimal totalPrice;

    private String commodityUnit;

    // 包装规格
    private BigDecimal commodityPackageSpec;

    // 份数
    private Long shares;

}
