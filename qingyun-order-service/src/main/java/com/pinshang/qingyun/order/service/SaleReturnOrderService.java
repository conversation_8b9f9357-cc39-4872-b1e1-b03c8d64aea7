package com.pinshang.qingyun.order.service;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.pinshang.qingyun.base.configure.expand.QYAssert;
import com.pinshang.qingyun.base.constant.QingyunConstant;
import com.pinshang.qingyun.base.enums.SaleReturnOrderStatusEnums;
import com.pinshang.qingyun.base.enums.SaleReturnReasonEnums;
import com.pinshang.qingyun.base.enums.StoreBillTypeEnums;
import com.pinshang.qingyun.base.enums.xd.StockInOutTypeEnums;
import com.pinshang.qingyun.base.enums.xd.StockOutTypeEnums;
import com.pinshang.qingyun.base.enums.xd.StorageAreaEnum;
import com.pinshang.qingyun.base.util.FastThreadLocalUtil;
import com.pinshang.qingyun.box.utils.*;
import com.pinshang.qingyun.box.utils.DateUtil;
import com.pinshang.qingyun.common.service.CodeClient;
import com.pinshang.qingyun.order.bo.StoreRechargeBO;
import com.pinshang.qingyun.order.dto.*;
import com.pinshang.qingyun.order.dto.consignment.*;
import com.pinshang.qingyun.order.enums.ConsignmentSaleReturnOrderStatusEnum;
import com.pinshang.qingyun.order.mapper.*;
import com.pinshang.qingyun.order.mapper.entry.StoreEntry;
import com.pinshang.qingyun.order.mapper.entry.commodity.CommodityBasicEntry;
import com.pinshang.qingyun.order.mapper.entry.order.*;
import com.pinshang.qingyun.order.model.order.ConsignmentSaleReturnOrder;
import com.pinshang.qingyun.order.model.order.ConsignmentSaleReturnOrderItem;
import com.pinshang.qingyun.order.model.order.SaleReturnOrder;
import com.pinshang.qingyun.order.model.order.SaleReturnOrderItem;
import com.pinshang.qingyun.order.model.shop.Shop;
import com.pinshang.qingyun.order.model.store.Store;
import com.pinshang.qingyun.order.vo.commodity.CommodityVO;
import com.pinshang.qingyun.order.vo.consignment.BatchImportConsignmentSaleReturnOrderResultVO;
import com.pinshang.qingyun.order.vo.consignment.ImportConsignmentSaleReturnOrderVO;
import com.pinshang.qingyun.order.vo.order.*;
import com.pinshang.qingyun.order.vo.shop.ShopCommodityWeightPriceInfoVO;
import com.pinshang.qingyun.renderer.service.IRenderService;
import com.pinshang.qingyun.shop.admin.dto.ConsignmentSupplierInfoIDTO;
import com.pinshang.qingyun.shop.admin.dto.ConsignmentSupplierInfoODTO;
import com.pinshang.qingyun.shop.admin.service.ConsignmentSupplierClient;
import com.pinshang.qingyun.shop.dto.ShopDto;
import com.pinshang.qingyun.shop.service.ShopClient;
import com.pinshang.qingyun.smm.dto.user.SelectUserShopIdListIDTO;
import com.pinshang.qingyun.smm.service.SMMUserClient;
import com.pinshang.qingyun.storage.dto.SaleReturnOrderItemODTO;
import com.pinshang.qingyun.storage.dto.WarehouseODto;
import com.pinshang.qingyun.storage.service.WarehouseClient;
import com.pinshang.qingyun.xd.wms.dto.*;
import com.pinshang.qingyun.xd.wms.dto.bigShop.DdStockInOutExtraIDTO;
import com.pinshang.qingyun.xd.wms.service.XdStockClient;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.*;
import org.joda.time.DateTime;
import org.joda.time.format.DateTimeFormat;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.entity.Example;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.function.Function;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import static java.util.stream.Collectors.toList;
import static java.util.stream.Collectors.toMap;

/**
 * Created by honway on 2017/11/2 15:10.
 * 门店退货入库 服务类
 */
@Service
@Slf4j
public class SaleReturnOrderService {

    private final SaleReturnOrderMapper saleReturnOrderMapper;
    private final SaleReturnOrderItemMapper saleReturnOrderItemMapper;
    private final WarehouseClient warehouseClient;
    private final XdStockClient xdStockClient;
    private final CommonService commonService;

    @Autowired
    private ShopService shopService;
    @Lazy
    @Autowired
    private OrderService orderService;

    public SaleReturnOrderService(SaleReturnOrderMapper saleReturnOrderMapper, SaleReturnOrderItemMapper saleReturnOrderItemMapper, WarehouseClient warehouseClient, XdStockClient xdStockClient, CommonService commonService) {
        this.saleReturnOrderMapper = saleReturnOrderMapper;
        this.saleReturnOrderItemMapper = saleReturnOrderItemMapper;
        this.warehouseClient = warehouseClient;
        this.xdStockClient = xdStockClient;
        this.commonService = commonService;
    }

    @Autowired
    SMMUserClient smmUserClient;

    @Autowired
    ShopClient shopClient;

    @Autowired
    ConsignmentSaleReturnOrderMapper consignmentSaleReturnOrderMapper;

    @Autowired
    CommodityMapper commodityMapper;

    @Autowired
    ConsignmentSaleReturnOrderItemMapper consignmentSaleReturnOrderItemMapper;

    @Autowired
    IRenderService renderService;

    @Autowired
    ShopReceiveService shopReceiveService;

    @Autowired
    ConsignmentSupplierClient consignmentSupplierClient;

    @Autowired
    StoreMapper storeMapper;

    @Autowired
    ShopMapper shopMapper;

    @Autowired
    private SMMUserClient sMMUserClient;

    @Autowired
    private ShopCommodityMapper shopCommodityMapper;

    @Autowired
    private CodeClient codeClient;

    @Autowired
    private StoreService storeService;

    @Autowired
    private StoreRechargeService storeRechargeService;

    /**
     * 查询 门店退货列表
     *
     * @param reqVo 查询列表的参数
     * @return 返回 门店退货单列表
     */
    public PageInfo<SaleReturnOrderRespVo> list(SaleReturnOrderReqVo reqVo) {
        reqVo.setIfReturnShort(reqVo.getIfReturnShort() == null ? Boolean.FALSE : reqVo.getIfReturnShort());
        reqVo.setReturnReason(SaleReturnReasonEnums.少货.getCode());

        Map<String, Object> sqlParams = buildSqlParams(reqVo);
        PageInfo<SaleReturnOrderRespVo> result = PageHelper.startPage(reqVo.getPageNo(), reqVo.getPageSize()).doSelectPageInfo(() -> saleReturnOrderMapper.list(sqlParams));
        List<Long> warehouseIds = result.getList().stream().map(SaleReturnOrderRespVo::getWarehouseId).collect(toList());
        Map<Long, WarehouseODto> warehouseMapping = warehouseClient.queryWarehouseListByIds(warehouseIds);
        result.getList().forEach(item -> {
            WarehouseODto warehouseODto = warehouseMapping.get(item.getWarehouseId());
            if (warehouseODto != null) {
                item.setWarehouseName(warehouseODto.getWarehouseName());
            }
        });
        return result;
    }

    /**
     * 查询 门店退货列表(PDA)
     *
     * @return 返回 门店退货单列表
     */
    public List<SaleReturnOrderODTO> querySaleReturnOrderList(SaleReturnOrderIDTO idto) {
        return saleReturnOrderMapper.querySaleReturnOrderList(idto);
    }

    /**
     * 构建 门店退货入库单列表 SQL参数
     *
     * @param reqVo 请求参数VO
     * @return 返回map
     */
    private Map<String, Object> buildSqlParams(SaleReturnOrderReqVo reqVo) {
        Map<String, Object> result = new HashMap<>();
        Date returnOrderStartDate = reqVo.getReturnOrderStartDate();
        Date returnOrderEndDate = reqVo.getReturnOrderEndDate();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        if (returnOrderStartDate != null && returnOrderEndDate != null) {
            result.put("startDate", sdf.format(returnOrderStartDate));
            result.put("endDate", sdf.format(returnOrderEndDate));
        }
        if (StringUtils.isNotBlank(reqVo.getStoreFuzzy())) {
            result.put("storeFuzzy", reqVo.getStoreFuzzy());
        }
        if (StringUtils.isNotBlank(reqVo.getReturnOrderCode())) {
            result.put("returnOrderCode", reqVo.getReturnOrderCode());
        }
        if (reqVo.getStatus() != null) {
            result.put("status", reqVo.getStatus());
        }
        if (reqVo.getWarehouseId() != null) {
            result.put("warehouseId", reqVo.getWarehouseId());
        }
        result.put("ifReturnShort", reqVo.getIfReturnShort());
        result.put("returnReason", reqVo.getReturnReason());
        return result;
    }

    /**
     * 收货页面查询退货单详情
     *
     * @param code
     * @return
     */
    public SaleReturnOrderDetailEntry querySaleReturnDetail(String code, Long enterpriseId) {
        SaleReturnOrderDetailEntry entry = saleReturnOrderMapper.detail(code, enterpriseId);
        if (entry != null) {
            if (entry.getWarehouseId() != null) {
                Map<Long, WarehouseODto> mapWarehouse = warehouseClient.queryWarehouseListByIds(Arrays.asList(entry.getWarehouseId()));
                if (SpringUtil.isNotEmpty(mapWarehouse) && mapWarehouse.get(entry.getWarehouseId()) != null) {
                    entry.setWarehouseName(mapWarehouse.get(entry.getWarehouseId()).getWarehouseName());
                }
            }
            List<SaleReturnOrderItemEntry> itemList = saleReturnOrderMapper.itemList(code, enterpriseId);
            List<Long> ids = itemList.stream().mapToLong(SaleReturnOrderItemEntry::getCommodityId).boxed().collect(toList());
            //返回商品的拣货位及拣货位的锁库状态
            Map<Long, SaleReturnOrderItemODTO> map = warehouseClient.queryCommodityLockStatus(ids, entry.getWarehouseId());
            QYAssert.isTrue(map != null, "查询商品拣货位及拣货位锁库状态时异常.");
            // 把对应的数据组合到原list当中
            for (SaleReturnOrderItemEntry item : itemList) {
                SaleReturnOrderItemODTO lock = map.get(item.getCommodityId());
                if (lock != null) {
                    item.setLockStatus(lock.getLockStatus());
                    item.setPickShelfId(lock.getPickShelfId());
                    item.setPickShelfNo(lock.getPickShelfNo());
                }
            }
            entry.setItemList(itemList);
        }
        return entry;
    }

    /**
     * 验证退货单是否被其他人收货
     *
     * @return
     */
    public CheckReceiverODTO checkReceiver(CheckReceiverIDTO idto) {
        CheckReceiverODTO checkReceiver = new CheckReceiverODTO();
        SaleReturnOrderDetailEntry detail = saleReturnOrderMapper.detail(idto.getCode(), idto.getEnterpriseId());
        // 只有待收货或者收货中  且 操作人和之前操作人相同 可以继续收货
        boolean statusTrue = detail != null && null != detail.getStatus() && (detail.getStatus() == 1 || detail.getStatus() == 3)
                && (detail.getUpdateId() == null || detail.getUpdateId().equals(idto.getReceiverId()));
        if (statusTrue) {
            checkReceiver.setFlag(true);
        } else {
            checkReceiver.setFlag(false);
        }
        checkReceiver.setName(detail.getUpdateName());
        return checkReceiver;
    }

    public PageInfo<SaleReturnReportODTO> listReturnReport(SaleReturnReportVo vo) {
        PageInfo<SaleReturnReportODTO> pageInfo = PageHelper.startPage(vo.getPageNo(), vo.getPageSize()).doSelectPageInfo(() -> {
            saleReturnOrderMapper.listReturnReport(vo);
        });
        if (pageInfo.getList().size() > 0) {
            List<Long> commodityIds = pageInfo.getList().stream().map(SaleReturnReportODTO::getCommodityId).collect(toList());
            Map<Long, String> barCodeMap = commonService.getCommodityBarCodeMap(commodityIds, null);
            pageInfo.getList().forEach(e -> {
                if (barCodeMap.containsKey(e.getCommodityId())) {
                    e.setBarCodes(barCodeMap.get(e.getCommodityId()));
                }
                e.setRealTotalPrice(e.getRealReturnQuantity() != null && e.getPrice() != null ?
                        new BigDecimal(e.getRealReturnQuantity()).multiply(e.getPrice()).setScale(2, RoundingMode.HALF_UP).stripTrailingZeros() : null);
            });
        }
        return pageInfo;
    }

    public SaleReturnReportSumEntry queryReturnReportSumEntry(SaleReturnReportVo vo) {
        return saleReturnOrderMapper.queryReturnReportSumEntry(vo);

    }

    public boolean updateOrderStatus(SaleReturnOrderUpdateReqVo reqVo) {
        SaleReturnOrder record = new SaleReturnOrder();
        record.setId(reqVo.getId());
        record.setStatus(reqVo.getStatus());
        record.setUpdateId(reqVo.getUpdateId());
        record.setUpdateTime(new Date());
        int updateNum = saleReturnOrderMapper.updateByPrimaryKeySelective(record);
        return updateNum > 0 ? true : false;
    }

    public SaleReturnOrder selectByOrderCodeAndLogisticsModel(String returnOrderCode, List<Integer> logistModle) {
        Example example = new Example(SaleReturnOrder.class);
        example.createCriteria().andEqualTo("orderCode", returnOrderCode).andIn("logisticsModel", logistModle);
        List<SaleReturnOrder> orders = saleReturnOrderMapper.selectByExample(example);
        QYAssert.isTrue(orders != null && orders.size() == 1, "退货单不存在.");
        Example itemEx = new Example(SaleReturnOrderItem.class);
        SaleReturnOrder saleReturnOrder = orders.get(0);
        itemEx.createCriteria().andEqualTo("saleReturnOrderId", saleReturnOrder.getId());
        List<SaleReturnOrderItem> items = saleReturnOrderItemMapper.selectByExample(itemEx);
        saleReturnOrder.setItemList(items);
        return saleReturnOrder;
    }

    @Transactional(rollbackFor = Exception.class)
    public boolean processReturnOrderItemQuantity(SaleReturnOrderUpdateQuantityWrapperVo updateQuantityWrapper) {
        Long id = updateQuantityWrapper.getId();
        // 查询退货单退货数量
        List<SaleReturnOrderODTO> returnOrderList = saleReturnOrderMapper.querySaleReturnOrderListById(id);
        QYAssert.isTrue(CollectionUtils.isNotEmpty(returnOrderList), "退货单不存在");

        Map<Long, SaleReturnOrderODTO> returnOrderMap = returnOrderList.stream().collect(Collectors.toMap(SaleReturnOrderODTO::getCommodityId, Function.identity()));
        SaleReturnOrderODTO saleReturnOrderODTO = returnOrderList.get(0);
        List<StockReceiptItemDTO> commodityList = new ArrayList<>();

        Long stallId = saleReturnOrderODTO.getStallId();
        Boolean isBigShop = (stallId != null && stallId > 0);

        Boolean isXsJmShop = shopService.isJmShop(saleReturnOrderODTO.getStoreId());
        BigDecimal returnAmount = BigDecimal.ZERO;
        List<SaleReturnOrderODTO> filterList = returnOrderList.stream().filter(p -> Integer.valueOf(SaleReturnReasonEnums.少货.getCode()).equals(p.getReturnReason())).collect(Collectors.toList());
        Boolean isShort = CollectionUtils.isNotEmpty(filterList);

        for (SaleReturnOrderItemQuantityWrapperVo wrapperVo : updateQuantityWrapper.getItemList()) {
            SaleReturnOrderItem orderItem = new SaleReturnOrderItem();
            orderItem.setBreakageQuantity(wrapperVo.getBreakageQuantity());
            orderItem.setRealReturnQuantity(wrapperVo.getRealReturnQuantity());
            orderItem.setRpType(wrapperVo.getRpType());
            if (null != wrapperVo.getCompensatePrice()) {
                orderItem.setCompensatePrice(wrapperVo.getCompensatePrice());
            }
            if (StringUtils.isNotBlank(wrapperVo.getAuditRemark())) {
                orderItem.setAuditRemark(wrapperVo.getAuditRemark());
            }
            Example example = new Example(SaleReturnOrderItem.class);
            example.createCriteria().andEqualTo("saleReturnOrderId", id).andEqualTo("commodityId", wrapperVo.getCommodityId());
            saleReturnOrderItemMapper.updateByExampleSelective(orderItem, example);

            // 申请退货数量- 实退数量 ＞ 0
            SaleReturnOrderODTO returnODto = returnOrderMap.get(wrapperVo.getCommodityId());
            if (returnODto != null && wrapperVo.getRealReturnQuantity().compareTo(returnODto.getReturnQuantity()) < 0) {
                StockReceiptItemDTO stockReceiptItemDTO = new StockReceiptItemDTO();
                stockReceiptItemDTO.setCommodityId(Long.valueOf(returnODto.getCommodityId()));
                stockReceiptItemDTO.setQuantity(returnODto.getReturnQuantity().subtract(wrapperVo.getRealReturnQuantity()));
                stockReceiptItemDTO.setNormalQuantity(stockReceiptItemDTO.getQuantity());
                stockReceiptItemDTO.setAbnormalQuantity(new BigDecimal(0));
                stockReceiptItemDTO.setPrice(returnODto.getPrice());
                stockReceiptItemDTO.setTotalPrice(returnODto.getPrice().multiply(stockReceiptItemDTO.getQuantity()));
                stockReceiptItemDTO.setNumber(stockReceiptItemDTO.getQuantity().divide(returnODto.getCommodityPackageSpec(), 0, BigDecimal.ROUND_UP).intValue());
                stockReceiptItemDTO.setNormalNumber(stockReceiptItemDTO.getNumber());
                stockReceiptItemDTO.setAbnormalNumber(0);
                commodityList.add(stockReceiptItemDTO);

                if(isBigShop){
                    DdStockInOutExtraIDTO ddStockInOutExtraIDTO = new DdStockInOutExtraIDTO();
                    ddStockInOutExtraIDTO.setCommodityId(Long.valueOf(returnODto.getCommodityId()));
                    ddStockInOutExtraIDTO.setStallId(stallId);
                    ddStockInOutExtraIDTO.setStorageArea(StorageAreaEnum.SHELF_AREA.getCode());
                    stockReceiptItemDTO.setDdStockInOutExtraVO(ddStockInOutExtraIDTO);
                }
            }

            // 退货、少货如果非门店责任则进行退款
            if(wrapperVo.getRpType() != null && !wrapperVo.getRpType().equals(3) && returnODto != null){
                returnAmount = returnAmount.add(returnODto.getPrice().multiply(wrapperVo.getRealReturnQuantity()));
            }

        }

        SaleReturnOrder record = new SaleReturnOrder();
        record.setId(id);
        record.setUpdateId(updateQuantityWrapper.getUpdateId());
        record.setUpdateTime(new Date());
        record.setStatus(SaleReturnOrderStatusEnums.STOCK_IN.getCode());
        saleReturnOrderMapper.updateByPrimaryKeySelective(record);

        // 如果是鲜食加盟(钱大妈),退货、少货审核通过时候，非门店责任则进行退款
        if (isXsJmShop && returnAmount.compareTo(BigDecimal.ZERO) > 0) {
            String remark;
            int billType;
            if (isShort) {
                remark = "<--门店少货：" + saleReturnOrderODTO.getReturnOrderCode() + " -->";
                billType = StoreBillTypeEnums.SHOP_LACK_DEPOSIT.getCode();
            } else {
                remark = "<--门店退货：" + saleReturnOrderODTO.getReturnOrderCode() + " -->";
                billType = StoreBillTypeEnums.SHOP_RETURN_DEPOSIT.getCode();
            }
            //回款
            StoreRechargeBO rechargeBO = StoreRechargeBO.builder()
                    .orderCode(saleReturnOrderODTO.getReturnOrderCode())
                    .tradeCode(saleReturnOrderODTO.getReturnOrderCode())
                    .money(returnAmount.doubleValue())
                    .storeId(saleReturnOrderODTO.getStoreId())
                    .tradeTime(new Date())
                    .receiptDate(new Date())
                    .billType(billType)
                    .remark(remark)
                    .userId(-1L)
                    .build();
            storeRechargeService.storeRecharge(rechargeBO);
        }

        // 申请退货数量-实退数量＞0，则将这部分库存（申请退货数量-实退数量）还给门店，即相应门店、相应商品的库存要加回去
        // 门店进销存报表，记录这个入库，业务类型=退货审核入库、相关单据=退货单号、操作人=完成收货的操作人、操作时间=完成收货时间
        if (CollectionUtils.isNotEmpty(commodityList)) {
            StockReceiptIDTO stockReceiptIDTO = new StockReceiptIDTO();
            stockReceiptIDTO.setWarehouseId(saleReturnOrderODTO.getShopId());
            stockReceiptIDTO.setReferId(Long.valueOf(saleReturnOrderODTO.getReturnOrderId()));
            stockReceiptIDTO.setReferCode(saleReturnOrderODTO.getReturnOrderCode());
            stockReceiptIDTO.setUserId(updateQuantityWrapper.getUpdateId());
            stockReceiptIDTO.setStockEnums(StockInOutTypeEnums.IN_RETURN_CHECK);
            stockReceiptIDTO.setCommodityList(commodityList);
            xdStockClient.stockReceipt(stockReceiptIDTO);
        }
        return true;
    }

    /**
     * 查询待收货/收货中的客户
     */
    public List<StoreInfoODTO> queryStoreInfo(StoreInfoIDTO idto) {
        return saleReturnOrderMapper.queryStoreInfo(idto);
    }

    public PageInfo<ConsignmentSaleReturnOrderRespVo> consignmentReturnOrderlist(ConsignmentSaleReturnOrderReqVo reqVo) {

        PageInfo<ConsignmentSaleReturnOrderRespVo> pageInfo = new PageInfo<>();
        pageInfo.setList(null);

        //权限设置
        List<Long> shopIdList = smmUserClient.selectUserShopIdList(SelectUserShopIdListIDTO.onlyDb(FastThreadLocalUtil.getQY().getUserId()));
        if (CollectionUtils.isEmpty(shopIdList)) {
            return pageInfo;
        }

        if (StringUtils.isNotBlank(reqVo.getOrgCode())) {
            List<ShopDto> shopDtos = shopClient.selectShopListByParentOrgCode(reqVo.getOrgCode());
            if (CollectionUtils.isNotEmpty(shopDtos)) {
                List<Long> orgShopIdList = shopDtos.stream().map(ShopDto::getId).collect(toList());
                shopIdList.retainAll(orgShopIdList);

                if (CollectionUtils.isEmpty(shopIdList)) {
                    return pageInfo;
                }
            } else {
                return pageInfo;
            }

        }
        reqVo.setShopIdList(shopIdList);
        Map<String, Object> sqlParams = buildSqlParams(reqVo);
        PageInfo<ConsignmentSaleReturnOrderRespVo> result = PageHelper.startPage(reqVo.getPageNo(), reqVo.getPageSize()).doSelectPageInfo(() -> consignmentSaleReturnOrderMapper.list(sqlParams));
        for (ConsignmentSaleReturnOrderRespVo consignmentSaleReturnOrderRespVo : result.getList()) {
            consignmentSaleReturnOrderRespVo.setStatusName(ConsignmentSaleReturnOrderStatusEnum.getByCode(consignmentSaleReturnOrderRespVo.getStatus()).getName());
        }
        renderService.render(result, "/consignmentReturnOrderlist");
        return result;
    }

    /**
     * 构建 代销退货单列表 SQL参数
     *
     * @param reqVo 请求参数VO
     * @return 返回map
     */
    private Map<String, Object> buildSqlParams(ConsignmentSaleReturnOrderReqVo reqVo) {
        Map<String, Object> result = new HashMap<>();
        String returnOrderStartDate = reqVo.getReturnOrderStartDate();
        String returnOrderEndDate = reqVo.getReturnOrderEndDate();
        String confirmOrderStartDate = reqVo.getConfirmOrderStartDate();
        String confirmOrderEndDate = reqVo.getConfirmOrderEndDate();
        String auditOrderStartDate = reqVo.getAuditOrderStartDate();
        String auditOrderEndDate = reqVo.getAuditOrderEndDate();
//        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        if (returnOrderStartDate != null && returnOrderEndDate != null) {
            result.put("returnOrderStartDate", returnOrderStartDate);
            result.put("returnOrderEndDate", returnOrderEndDate);
        }
        if (confirmOrderStartDate != null && confirmOrderEndDate != null) {
            result.put("confirmOrderStartDate", confirmOrderStartDate);
            result.put("confirmOrderEndDate", confirmOrderEndDate);
        }
        if (auditOrderStartDate != null && auditOrderEndDate != null) {
            result.put("auditOrderStartDate", auditOrderStartDate);
            result.put("auditOrderEndDate", auditOrderEndDate);
        }
        if (StringUtils.isNotBlank(reqVo.getShopId())) {
            result.put("shopId", reqVo.getShopId());
        }
        if (StringUtils.isNotBlank(reqVo.getReturnOrderCode())) {
            result.put("returnOrderCode", reqVo.getReturnOrderCode());
        }
        if (reqVo.getStatus() != null) {
            result.put("status", reqVo.getStatus());
        }
        if (StringUtils.isNotBlank(reqVo.getStoreId())) {
            result.put("storeId", reqVo.getStoreId());
        }

        if (reqVo.getCreateId() != null) {
            result.put("createId", reqVo.getCreateId());
        }

        if (reqVo.getConfirmId() != null) {
            result.put("confirmId", reqVo.getConfirmId());
        }

        if (StringUtils.isNotBlank(reqVo.getSupplierId())) {
            result.put("supplierId", reqVo.getSupplierId());
        }

        if (CollectionUtils.isNotEmpty(reqVo.getShopIdList())) {
            result.put("shopIdList", reqVo.getShopIdList());
        }

        return result;
    }

    //导入
    public BatchImportConsignmentSaleReturnOrderResultVO consignmentReturnOrderImport(Workbook wb, Long supplierId) {
        BatchImportConsignmentSaleReturnOrderResultVO ret = new BatchImportConsignmentSaleReturnOrderResultVO();
        QYAssert.notNull(wb, "模板不正确");
        Sheet sheet = wb.getSheetAt(0);
        QYAssert.notNull(sheet, "模板不正确");

        List<ImportConsignmentSaleReturnOrderVO> consignmentSaleReturnImportList = Lists.newArrayList();

        //校验模板信息
        validateImportTemplate(sheet);

        //获取excel数据
        setConsignmentSaleReturnImportData(sheet, consignmentSaleReturnImportList);

        //数量大于1000
        QYAssert.isTrue(consignmentSaleReturnImportList.size() <= 1000, "导入数量大于1000!");
        QYAssert.isTrue(consignmentSaleReturnImportList.size() > 0, "导入数量为0!");


        //获取重复项代销供应商id+客户编码+商品编码
        List<String> distinctList = consignmentSaleReturnImportList.stream().collect(Collectors.groupingBy(item -> item.getSupplierId() + "" + item.getStoreCode() + "" + item.getCommodityCode(),
                        Collectors.counting())).entrySet().stream().filter(e -> e.getValue() > 1)
                .map(Map.Entry::getKey).collect(Collectors.toList());

        //重复数据
        if (CollectionUtils.isNotEmpty(distinctList)) {
            final String[] error = {"第"};
            distinctList.forEach(item -> {
                int i = 0;
                for (ImportConsignmentSaleReturnOrderVO consignmentSaleReturnOrderVO : consignmentSaleReturnImportList) {
                    i++;
                    if (item.equals((consignmentSaleReturnOrderVO.getSupplierId() + "" + consignmentSaleReturnOrderVO.getStoreCode() + "" + consignmentSaleReturnOrderVO.getCommodityCode()))) {
                        error[0] = error[0] + (i + 1) + "、";
                    }
                }
            });
            QYAssert.isFalse(error[0].substring(0, error[0].length() - 1) + "行重复;");
        }

        List<String> importStoreCodes = consignmentSaleReturnImportList.stream().map(ImportConsignmentSaleReturnOrderVO::getStoreCode).distinct().collect(toList());
        List<String> importCommodityCodes = consignmentSaleReturnImportList.stream().map(ImportConsignmentSaleReturnOrderVO::getCommodityCode).distinct().collect(toList());
        List<Store> dbStores = storeMapper.findInStoreCodes(importStoreCodes);
        List<XDCommodityODTO> dbCommodities = commodityMapper.findCommodityInfoByCodes(importCommodityCodes);

        Map<String, XDCommodityODTO> commoditiesMap = dbCommodities.stream().collect(toMap(XDCommodityODTO::getCommodityCode,Function.identity()));

        Map<String, StoreEntry> storeShopsMap = new HashMap<>();
        List<Long> storeIds = dbStores.stream().map(Store::getId).collect(toList());
        List<Long> shopIds = Lists.newArrayList();
        if(CollectionUtils.isNotEmpty(storeIds)){
            List<StoreEntry> storeEntries = shopMapper.selectStoreList(storeIds);
            shopIds = storeEntries.stream().map(StoreEntry::getShopId).collect(toList());
            storeShopsMap = storeEntries.stream().collect(toMap(StoreEntry::getStoreCode, Function.identity()));
        }
        List<Long> commodityIdList = dbCommodities.stream().map(XDCommodityODTO::getCommodityId).map(Long::parseLong).collect(toList());
        List<ShopCommodityWeightPriceInfoVO> shopCommodityWeightPriceInfos = new ArrayList<>();
        if(CollectionUtils.isNotEmpty(commodityIdList) && CollectionUtils.isNotEmpty(shopIds)){
            shopCommodityWeightPriceInfos = shopCommodityMapper.queryShopCommodityWeightPrice(commodityIdList, shopIds);

        }
        Map<String, List<ShopCommodityWeightPriceInfoVO>> shopCommodityWeightPriceMap = shopCommodityWeightPriceInfos.stream().collect(Collectors.groupingBy(shopCommodityWeightPriceInfo -> shopCommodityWeightPriceInfo.getShopId() + "_" + shopCommodityWeightPriceInfo.getCommodityId()));

        //组装导入的商品id，客户id
        List<String> errors = new ArrayList<>();
        int i = 0;
        for (ImportConsignmentSaleReturnOrderVO consignmentSaleReturnOrderVO : consignmentSaleReturnImportList) {
            i++;
            QYAssert.isTrue(storeShopsMap.get(consignmentSaleReturnOrderVO.getStoreCode())!=null,"客户编码"+consignmentSaleReturnOrderVO.getStoreCode()+"不存在");
            QYAssert.isTrue(commoditiesMap.get(consignmentSaleReturnOrderVO.getCommodityCode())!=null,"商品编码"+consignmentSaleReturnOrderVO.getCommodityCode()+"不存在");
            String storeCode = consignmentSaleReturnOrderVO.getStoreCode();
            StoreEntry storeEntry = storeShopsMap.get(storeCode);
            QYAssert.isTrue(storeEntry.getShopId()!=null,"客户编码"+consignmentSaleReturnOrderVO.getStoreCode()+"不存在对应门店");
            consignmentSaleReturnOrderVO.setStoreId(String.valueOf(storeEntry.getStoreId()));
            consignmentSaleReturnOrderVO.setShopId(storeEntry.getShopId());
            consignmentSaleReturnOrderVO.setShopCode(storeEntry.getShopCode());
            consignmentSaleReturnOrderVO.setShopName(storeEntry.getShopName());
            XDCommodityODTO xdCommodityODTO = commoditiesMap.get(consignmentSaleReturnOrderVO.getCommodityCode());
            consignmentSaleReturnOrderVO.setCommodityId(xdCommodityODTO.getCommodityId());
            consignmentSaleReturnOrderVO.setCommodityName(xdCommodityODTO.getCommodityName());
            consignmentSaleReturnOrderVO.setCommodityUnit(xdCommodityODTO.getCommodityUnit());
            consignmentSaleReturnOrderVO.setCommoditySpec(xdCommodityODTO.getCommoditySpec());
            consignmentSaleReturnOrderVO.setSupplierId(supplierId);
            List<ShopCommodityWeightPriceInfoVO> shopCommodityWeightPriceInfoVOS = shopCommodityWeightPriceMap.get(storeEntry.getShopId() + "_" + consignmentSaleReturnOrderVO.getCommodityId());
            if(CollectionUtils.isNotEmpty(shopCommodityWeightPriceInfoVOS)){
                consignmentSaleReturnOrderVO.setPrice(shopCommodityWeightPriceInfoVOS.get(0).getWeightPrice());
            }else{
                errors.add("第"+(i+1)+"行客户编码【"+consignmentSaleReturnOrderVO.getStoreCode()+"】商品编码【"+consignmentSaleReturnOrderVO.getCommodityCode()+"】不存在商品库存");
            }
        }
        if(CollectionUtils.isNotEmpty(errors)){
            ret.setErrors(errors);
            return ret;
        }

        //是否代销门店代销商品校验
        ConsignmentSupplierInfoIDTO idto = new ConsignmentSupplierInfoIDTO();
        idto.setSupplierIdList(Collections.singletonList(supplierId));
        List<Long> consignmentStoreIds = consignmentSaleReturnImportList
                .stream().map(ImportConsignmentSaleReturnOrderVO::getStoreId)
                .map(Long::parseLong).collect(toList());
        idto.setStoreIdList(consignmentStoreIds);
        idto.setStatus(1);

        List<ConsignmentSupplierInfoODTO> consignmentSupplierInfoODTOS = consignmentSupplierClient.batchQueryByStoreIds(idto);
        Map<Long, Map<Long, ConsignmentSupplierInfoODTO>> resultMap =
                consignmentSupplierInfoODTOS.stream().collect(
                        Collectors.groupingBy(
                                ConsignmentSupplierInfoODTO::getStoreId,
                                Collectors.toMap(
                                        ConsignmentSupplierInfoODTO::getCommodityId,
                                        Function.identity()
                                )
                        )
                );
        for (ImportConsignmentSaleReturnOrderVO consignmentSaleReturnOrderVO : consignmentSaleReturnImportList) {
            Long storeId = Long.parseLong(consignmentSaleReturnOrderVO.getStoreId());
            Long commodityId = Long.parseLong(consignmentSaleReturnOrderVO.getCommodityId());
            QYAssert.isTrue(resultMap.containsKey(storeId),consignmentSaleReturnOrderVO.getStoreCode()+"客户编码没有代销门店");
            Map<Long, ConsignmentSupplierInfoODTO> longConsignmentSupplierInfoODTOMap = resultMap.get(storeId);
            QYAssert.isTrue(longConsignmentSupplierInfoODTOMap.containsKey(commodityId),consignmentSaleReturnOrderVO.getCommodityCode()+"商品编码不是代销商品");

        }
        ret.setList(consignmentSaleReturnImportList);

        return ret;
    }

    @Transactional(rollbackFor = Exception.class)
    public Boolean batchSaveConsignmentReturnOrder(List<SaveConsignmentSaleReturnOrderODTO> saveConsignmentSaleReturnOrderODTOS,Long createId) {

        //同一个门店的商品，汇总生成一张退货单
        if (CollectionUtils.isEmpty(saveConsignmentSaleReturnOrderODTOS)) {
            QYAssert.isFalse("批量退货单明细不能为空");
        }

        //是否代销门店代销商品校验
        ConsignmentSupplierInfoIDTO idto = new ConsignmentSupplierInfoIDTO();
        idto.setSupplierIdList(Collections.singletonList(saveConsignmentSaleReturnOrderODTOS.get(0).getSupplierId()));
        List<Long> consignmentStoreIds = saveConsignmentSaleReturnOrderODTOS
                .stream().map(SaveConsignmentSaleReturnOrderODTO::getStoreId)
                .collect(toList());
        idto.setStoreIdList(consignmentStoreIds);
        idto.setStatus(1);

        List<ConsignmentSupplierInfoODTO> consignmentSupplierInfoODTOS = consignmentSupplierClient.batchQueryByStoreIds(idto);
        Map<Long, Map<Long, ConsignmentSupplierInfoODTO>> resultMap =
                consignmentSupplierInfoODTOS.stream().collect(
                        Collectors.groupingBy(
                                ConsignmentSupplierInfoODTO::getStoreId,
                                Collectors.toMap(
                                        ConsignmentSupplierInfoODTO::getCommodityId,
                                        Function.identity()
                                )
                        )
                );
        List<Store> dbStoreList = storeService.findStoreListByStoreIdList(consignmentStoreIds);
        Map<Long, String> storeMap = dbStoreList.stream().collect(toMap(Store::getId, Store::getStoreCode));

        List<Long> consignmentCommodityIds = saveConsignmentSaleReturnOrderODTOS
                .stream().map(SaveConsignmentSaleReturnOrderODTO::getCommodityId)
                .distinct()
                .collect(toList());
        List<CommodityInfoEntry> dbCommodities = commodityMapper.findCommodityInfoIds(consignmentCommodityIds);
        Map<Long, String> commoditiesMap = dbCommodities.stream().collect(toMap(CommodityInfoEntry::getId,CommodityInfoEntry::getCommodityCode));

        for (SaveConsignmentSaleReturnOrderODTO saveConsignmentSaleReturnOrderODTO : saveConsignmentSaleReturnOrderODTOS) {
            Long storeId = saveConsignmentSaleReturnOrderODTO.getStoreId();
            Long commodityId = saveConsignmentSaleReturnOrderODTO.getCommodityId();
            QYAssert.isTrue(resultMap.containsKey(storeId),storeMap.get(storeId)+"客户编码没有代销门店");
            Map<Long, ConsignmentSupplierInfoODTO> longConsignmentSupplierInfoODTOMap = resultMap.get(storeId);
            QYAssert.isTrue(longConsignmentSupplierInfoODTOMap.containsKey(commodityId),commoditiesMap.get(commodityId)+"商品编码不是代销商品");
        }

        Map<Long, List<SaveConsignmentSaleReturnOrderODTO>> saveReturnOrderMapsByStoreId = saveConsignmentSaleReturnOrderODTOS.stream()
                .collect(Collectors.groupingBy(SaveConsignmentSaleReturnOrderODTO::getStoreId));

        Date date = new Date();

        saveReturnOrderMapsByStoreId.forEach((k, v) -> {
            SaveConsignmentSaleReturnOrderODTO saveConsignmentSaleReturnOrderODTO = v.get(0);
            Long supplierId = saveConsignmentSaleReturnOrderODTO.getSupplierId();
            Long storeId = k;
            ConsignmentSaleReturnOrder saleReturnOrder = new ConsignmentSaleReturnOrder();
            saleReturnOrder.setSupplierId(supplierId);
            saleReturnOrder.setStoreId(storeId);

            String orderCode = generateConsignmentReturnCode();
            QYAssert.notNull(orderCode, " 代销退货单号获取异常");
            saleReturnOrder.setOrderCode(orderCode);
            saleReturnOrder.setStatus(ConsignmentSaleReturnOrderStatusEnum.NEED_CONFIRM.getCode());//待确认
            saleReturnOrder.setEnterpriseId(QingyunConstant.ENTERPRISE_ID);
            saleReturnOrder.setCreateId(createId);
            saleReturnOrder.setCreateTime(date);
            int flag = consignmentSaleReturnOrderMapper.insertSelective(saleReturnOrder);
            if(flag <=0){
                QYAssert.isFalse("保存退货单失败");
            }

            List<ConsignmentSaleReturnOrderItem> saleReturnOrderItems = Lists.newArrayList();
            for (SaveConsignmentSaleReturnOrderODTO consignmentSaleReturnOrderODTO : v) {
                ConsignmentSaleReturnOrderItem saleReturnOrderItem = new ConsignmentSaleReturnOrderItem();
                saleReturnOrderItem.setConsignmentSaleReturnOrderId(saleReturnOrder.getId());
                saleReturnOrderItem.setCommodityId(consignmentSaleReturnOrderODTO.getCommodityId());
                saleReturnOrderItem.setReturnQuantity(consignmentSaleReturnOrderODTO.getTotalQuantity());
                saleReturnOrderItem.setPrice(consignmentSaleReturnOrderODTO.getPrice());
                saleReturnOrderItems.add(saleReturnOrderItem);
            }
            consignmentSaleReturnOrderItemMapper.insertList(saleReturnOrderItems);
        });

        return true;
    }

    private String generateConsignmentReturnCode(){
        String cnsignmentReturnCode = "";
        try {
            cnsignmentReturnCode = codeClient.createCode("CONSIGNMENT_SHOP_RETURN_CODE");
        } catch (Throwable e) {
            cnsignmentReturnCode = null;
        }
        return cnsignmentReturnCode;
    }


    private void validateImportTemplate(Sheet sheet) {

        for (Row row : sheet) {
            int rowNum = row.getRowNum();
            if (rowNum == 0) {
                QYAssert.isTrue(row.getLastCellNum() == 3, "模板不正确");
                QYAssert.isTrue(checkRowNumZero(row, 0, "客户编码"), "模板不正确");
                QYAssert.isTrue(checkRowNumZero(row, 1, "商品编码"), "模板不正确");
                QYAssert.isTrue(checkRowNumZero(row, 2, "退货数量"), "模板不正确");
                break;
            }
        }
    }

    /**
     * 判断模板格式：根据Excel表头来判断
     *
     * @param row
     * @param index
     * @param cellName
     * @return
     */
    private boolean checkRowNumZero(Row row, int index, String cellName) {
        boolean result = true;
        if (row.getCell(index) == null || !cellName.equals(row.getCell(index).getStringCellValue())) {
            result = false;
        }
        return result;
    }


    /**
     * 获取excel数据
     *
     * @return
     */
    private void setConsignmentSaleReturnImportData(Sheet sheet, List<ImportConsignmentSaleReturnOrderVO> consignmentSaleReturnImportList) {

        for (int i = 1; i <= sheet.getLastRowNum(); i++) {
            ImportConsignmentSaleReturnOrderVO consignmentSaleReturnOrderVO = new ImportConsignmentSaleReturnOrderVO();
            Row row = sheet.getRow(i);

            if (row == null
                    || row.getCell(0) == null || row.getCell(0).equals("")
                    || row.getCell(1) == null || row.getCell(1).equals("")
                    || row.getCell(2) == null || row.getCell(2).equals("")
                    || (row.getCell(0).getCellType().name().equals("STRING") && (row.getCell(0).getStringCellValue() == null || row.getCell(0).getStringCellValue().trim().equals("")))
                    || (row.getCell(1).getCellType().name().equals("STRING") && (row.getCell(1).getStringCellValue() == null || row.getCell(1).getStringCellValue().trim().equals("")))
                    || (row.getCell(2).getCellType().name().equals("STRING") && (row.getCell(2).getStringCellValue() == null || row.getCell(2).getStringCellValue().trim().equals("")))) {
                QYAssert.isFalse("第" + (i + 1) + "行客户编码、商品编码或退货数量不能为空");
            }

            // 客户编码
            Cell storeCodeCell = row.getCell(0);
            storeCodeCell.setCellType(CellType.STRING);
            String storeCode = storeCodeCell.getStringCellValue().trim();

            // 商品编码
            Cell commodityCodeCell = row.getCell(1);
            commodityCodeCell.setCellType(CellType.STRING);
            String commodityCode = commodityCodeCell.getStringCellValue().trim();

            // 数量
            Cell quantityCell = row.getCell(2);
            quantityCell.setCellType(CellType.STRING);
            String quantity = quantityCell.getStringCellValue().trim();
            if (!isNumber(quantity)) {
                QYAssert.isFalse("第" + (i + 1) + "行退货数量超出范围");
            }
            BigDecimal commodityQuantity = new BigDecimal(quantity);
            QYAssert.isTrue(commodityQuantity.compareTo(BigDecimal.ZERO) >0 && commodityQuantity.compareTo(new BigDecimal("999999.999")) <=0,"退货数量必须大于0<=999999.999");
            consignmentSaleReturnOrderVO.setStoreCode(storeCode.trim());
            consignmentSaleReturnOrderVO.setCommodityCode(commodityCode.trim());
            consignmentSaleReturnOrderVO.setTotalQuantity(new BigDecimal(quantity.trim()));
            consignmentSaleReturnImportList.add(consignmentSaleReturnOrderVO);
        }

    }


    private boolean isNumber(String str) {
        // 长度最多15位，允许2位小数
        Pattern pattern = Pattern.compile("^(\\d{1,6}|\\d{1,6}\\.\\d{1,3})$");
        Matcher match = pattern.matcher(str);
        if (match.matches() == false) {
            return false;
        } else {
            return true;
        }
    }

    public ConsignmentSaleReturnOrderDetailODTO queryConsignmentSaleReturnOrderItemList(Long saleReturnOrderId) {
        QYAssert.isTrue(Objects.nonNull(saleReturnOrderId), "代销退货单详情-退货单ID不能为空! ");
        ConsignmentSaleReturnOrderODTO saleReturnOrderODTO = consignmentSaleReturnOrderMapper.selectById(saleReturnOrderId);
        QYAssert.isTrue(null != saleReturnOrderODTO, "代销退货单详情-退货单不存在! ");
        renderService.render(saleReturnOrderODTO, "/queryConsignmentSaleReturnOrderItemList/{saleReturnOrderId}");
        ConsignmentSaleReturnOrderDetailODTO odto = BeanCloneUtils.copyTo(saleReturnOrderODTO, ConsignmentSaleReturnOrderDetailODTO.class);

        odto.setStatusName(ConsignmentSaleReturnOrderStatusEnum.getByCode(odto.getStatus()).getName());
        List<ConsignmentSaleReturnOrderItemODTO> returnOrderItemList = consignmentSaleReturnOrderItemMapper.queryBySaleReturnOrderId(saleReturnOrderId);
        if (CollectionUtils.isNotEmpty(returnOrderItemList)) {
            odto.setItems(returnOrderItemList);
        }

        return odto;
    }

    @Transactional(rollbackFor = Exception.class)
    public Boolean confirmSaleReturnOrder(ConfirmConsignmentReturnOrderIDTO dto) {

        ConsignmentSaleReturnOrder saleReturnOrder = consignmentSaleReturnOrderMapper.selectByPrimaryKey(dto.getId());
        List<ConsignmentSaleReturnOrderItemODTO> returnOrderItemList = consignmentSaleReturnOrderItemMapper.queryBySaleReturnOrderId(saleReturnOrder.getId());
        //校验参数
        checkParam(dto, saleReturnOrder, returnOrderItemList);

        // 确认通过=》待审核
        saleReturnOrder.setStatus(ConsignmentSaleReturnOrderStatusEnum.NEED_CHECK.getCode());
        saleReturnOrder.setConfirmId(dto.getConfirmId());
        saleReturnOrder.setConfirmTime(new Date());

        //更新明细表
        consignmentSaleReturnOrderItemMapper.batchUpdateByConfirm(dto.getConfirmSaleReturnOrderItems());
        int flag = consignmentSaleReturnOrderMapper.updateByPrimaryKeySelective(saleReturnOrder);


        returnOrderItemList = consignmentSaleReturnOrderItemMapper.queryBySaleReturnOrderId(saleReturnOrder.getId());
        //扣减门店库存（含门店库存校验）
        reduceShopStock(saleReturnOrder,returnOrderItemList);


        return flag > 0;
    }

    /**
     * 扣减门店库存
     * @param saleReturnOrder
     * @param returnOrderItemList
     */
    private void reduceShopStock(ConsignmentSaleReturnOrder saleReturnOrder, List<ConsignmentSaleReturnOrderItemODTO> returnOrderItemList) {
        //过滤确认数为0的待确认商品明细
        returnOrderItemList = returnOrderItemList
                .stream()
                .filter(returnOrderItem->returnOrderItem.getConfirmQuantity().compareTo(BigDecimal.ZERO)>0).collect(toList());

        if(CollectionUtils.isEmpty(returnOrderItemList)){
            return;
        }

        Shop shop = shopService.getShopByStoreId(saleReturnOrder.getStoreId());

        // 检查库存
        String commodityName = checkShopStock(shop,returnOrderItemList);
        QYAssert.isTrue(StringUtil.isBlank(commodityName), "门店[" + commodityName + "]库存数量不足，无法确认");

        List<StockIDTO> stockList = new ArrayList<>();
        StockIDTO stockIDTO = doReduceStock(shop,saleReturnOrder,returnOrderItemList);
        stockList.add(stockIDTO);

        // 调用xd-wms扣减门店库存
        if(CollectionUtils.isNotEmpty(stockList)){
            xdStockClient.stockShopReturnList(stockList);
        }

    }

    private String checkShopStock(Shop shop, List<ConsignmentSaleReturnOrderItemODTO> returnOrderItemList) {
        List<ShopStockEntry> shopCommodities = new ArrayList<>();

        List<Long> commodityIdList = returnOrderItemList
                .stream()
                .map(ConsignmentSaleReturnOrderItemODTO::getCommodityId).collect(Collectors.toList());

        // 查询鲜道门店库存
        StockQueryIDTO stockQueryIDTO = new StockQueryIDTO();
        stockQueryIDTO.setWarehouseId(shop.getId());
        stockQueryIDTO.setCommodityList(commodityIdList);
        List<ShopCommodityStockDTO> stockList = xdStockClient.queryShopCommodityStock2(stockQueryIDTO);
        if(CollectionUtils.isNotEmpty(stockList)){
            for(ShopCommodityStockDTO odto : stockList){
                ShopStockEntry entry = new ShopStockEntry();
                entry.setCommodityId(odto.getCommodityId()+"");
                entry.setCommodityName(odto.getCommodityName());
                entry.setQuantity(odto.getStockQuantity());
                shopCommodities.add(entry);
            }
        }

        String commodityName = "";
        for (ShopStockEntry shopCommodity : shopCommodities) {
            for (ConsignmentSaleReturnOrderItemODTO item : returnOrderItemList) {
                if(item.getCommodityId().equals(shopCommodity.getCommodityId()) && (shopCommodity.getQuantity().compareTo(item.getConfirmQuantity()) == -1)){
                    commodityName = shopCommodity.getCommodityName();
                    break;
                }
            }
        }
        return commodityName;
    }

    private StockIDTO doReduceStock(Shop shop,ConsignmentSaleReturnOrder saleReturnOrder,List<ConsignmentSaleReturnOrderItemODTO> returnOrderItemList) {

        StockIDTO stockIDTO = new StockIDTO();
        stockIDTO.setOutTypeEnum(StockOutTypeEnums.SHOP);
        stockIDTO.setWarehouseId(shop.getId());
        stockIDTO.setReferId(saleReturnOrder.getId());
        stockIDTO.setReferCode(saleReturnOrder.getOrderCode());
        stockIDTO.setUserId(saleReturnOrder.getCreateId());
        List<StockItemIDTO> commodityList = new ArrayList<>();
        List<String> commodityIdList = returnOrderItemList.stream()
                .map(item -> item.getCommodityId())
                .map(String::valueOf).collect(Collectors.toList());

        Map<Long,BigDecimal> commMap = shopReceiveService.getCommodityPackageSpecMap(commodityIdList);
        BigDecimal commodityPackageSpec = BigDecimal.ONE;
        for (ConsignmentSaleReturnOrderItemODTO dto : returnOrderItemList) {
            StockItemIDTO stockItemIDTO = new StockItemIDTO();
            stockItemIDTO.setCommodityId(dto.getCommodityId());
            if(null != commMap && commMap.get(dto.getCommodityId()) != null){
                commodityPackageSpec = commMap.get(dto.getCommodityId());
            }
            stockItemIDTO.setStockNumber(dto.getConfirmQuantity().divide(commodityPackageSpec,0, RoundingMode.UP).intValue());
            stockItemIDTO.setQuantity(dto.getConfirmQuantity());
            commodityList.add(stockItemIDTO);
        }
        stockIDTO.setCommodityList(commodityList);
        return stockIDTO;
    }

    private void checkParam(ConfirmConsignmentReturnOrderIDTO dto, ConsignmentSaleReturnOrder saleReturnOrder, List<ConsignmentSaleReturnOrderItemODTO> returnOrderItemList) {
        QYAssert.isTrue(null != saleReturnOrder, " 代销退货单确认-退货单不存在! ");

        QYAssert.isTrue(CollectionUtils.isNotEmpty(dto.getConfirmSaleReturnOrderItems()), " 退货单确认-确认参数不能为空! ");
        QYAssert.isTrue(CollectionUtils.isNotEmpty(returnOrderItemList), " 退货单确认-退货单明细不存在! ");
        Map<Long, ConsignmentSaleReturnOrderItemODTO> map = returnOrderItemList.stream().collect(Collectors.toMap(ConsignmentSaleReturnOrderItemODTO::getId, Function.identity()));
        for (ConfirmConsignmentReturnOrdeItemIDTO confirmReturnOrderItem : dto.getConfirmSaleReturnOrderItems()) {
            BigDecimal confirmQuantity = confirmReturnOrderItem.getConfirmQuantity();
            ConsignmentSaleReturnOrderItemODTO returnOrderItem = map.get(confirmReturnOrderItem.getId());
            QYAssert.isTrue(Objects.nonNull(confirmQuantity) && confirmQuantity.compareTo(BigDecimal.ZERO) >=0 && confirmQuantity.compareTo(returnOrderItem.getReturnQuantity())<=0, " 退货单确认-确认份数需小于等于退货份数大于等于0！ ");
        }

        //待确认状态的可以进行确认
        QYAssert.isTrue(saleReturnOrder.getStatus() == ConsignmentSaleReturnOrderStatusEnum.NEED_CONFIRM.getCode() || saleReturnOrder.getStatus() ==ConsignmentSaleReturnOrderStatusEnum.REJECTED.getCode() , " 退货单确认-退货单状态不为待确认或已驳回! ");

    }

    /**
     * 总部审核代销退货单
     * @param dto
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public Boolean auditSaleReturnOrder(AuditConsignmentReturnOrderIDTO dto) {
        ConsignmentSaleReturnOrder saleReturnOrder = consignmentSaleReturnOrderMapper.selectByPrimaryKey(dto.getId());
        List<ConsignmentSaleReturnOrderItemODTO> returnOrderItemList = consignmentSaleReturnOrderItemMapper.queryBySaleReturnOrderId(saleReturnOrder.getId());
        //校验参数
        checkAuditParam(dto, saleReturnOrder, returnOrderItemList);

        if(dto.getAuditResult() == 1){
            // 审核通过=》已完成
            saleReturnOrder.setStatus(ConsignmentSaleReturnOrderStatusEnum.COMPLETED.getCode());
        }else if(dto.getAuditResult() == 0){
            //驳回 =》 yi驳回
            saleReturnOrder.setStatus(ConsignmentSaleReturnOrderStatusEnum.REJECTED.getCode());
            dto.getAuditSaleReturnOrderItems().forEach(
                    auditConsignmentReturnOrdeItemIDTO -> auditConsignmentReturnOrdeItemIDTO.setCheckQuantity(null)
            );
        }

        saleReturnOrder.setCheckUserId(dto.getCheckUserId());
        saleReturnOrder.setCheckTime(new Date());

        //更新明细表
        consignmentSaleReturnOrderItemMapper.batchUpdateByAudit(dto.getAuditSaleReturnOrderItems());
        int flag = consignmentSaleReturnOrderMapper.updateByPrimaryKeySelective(saleReturnOrder);

        //返还门店库存
        //重新查询退货单明细获取审核数量
        returnOrderItemList = consignmentSaleReturnOrderItemMapper.queryBySaleReturnOrderId(saleReturnOrder.getId());
        if(dto.getAuditResult() == 0){
            returnOrderItemList.forEach(
                    returnOrderItem -> returnOrderItem.setCheckQuantity(BigDecimal.ZERO)
            );
        }
        replusShopStock(saleReturnOrder,returnOrderItemList);

        return flag > 0;

    }

    private void replusShopStock(ConsignmentSaleReturnOrder saleReturnOrder, List<ConsignmentSaleReturnOrderItemODTO> returnOrderItemList) {
        //筛选审核数量和确认数量有差异的记录
        returnOrderItemList = returnOrderItemList
                .stream()
                .filter(returnOrderItem->returnOrderItem.getCheckQuantity().compareTo(returnOrderItem.getConfirmQuantity())!=0).collect(toList());

        //没有差异直接结束
        if(CollectionUtils.isEmpty(returnOrderItemList)){
            return;
        }

        Shop shop = shopService.getShopByStoreId(saleReturnOrder.getStoreId());

        doReplusStock(shop,saleReturnOrder,returnOrderItemList);


    }

    private void doReplusStock(Shop shop, ConsignmentSaleReturnOrder saleReturnOrder, List<ConsignmentSaleReturnOrderItemODTO> returnOrderItemList) {

        List<StockReceiptItemDTO> commodityList = new ArrayList<>();
        for (ConsignmentSaleReturnOrderItemODTO returnOrderItemODTO : returnOrderItemList) {

            // 申请退货数量- 实退数量 ＞ 0
            if ( returnOrderItemODTO.getCheckQuantity().compareTo(returnOrderItemODTO.getConfirmQuantity()) < 0) {
                StockReceiptItemDTO stockReceiptItemDTO = new StockReceiptItemDTO();
                stockReceiptItemDTO.setCommodityId(returnOrderItemODTO.getCommodityId());
                stockReceiptItemDTO.setQuantity(returnOrderItemODTO.getConfirmQuantity().subtract(returnOrderItemODTO.getCheckQuantity()));
                stockReceiptItemDTO.setNormalQuantity(stockReceiptItemDTO.getQuantity());
                stockReceiptItemDTO.setAbnormalQuantity(new BigDecimal(0));
                stockReceiptItemDTO.setPrice(returnOrderItemODTO.getPrice());
                stockReceiptItemDTO.setTotalPrice(returnOrderItemODTO.getPrice().multiply(stockReceiptItemDTO.getQuantity()));
                stockReceiptItemDTO.setNumber(stockReceiptItemDTO.getQuantity().divide(returnOrderItemODTO.getCommodityPackageSpec(), 0, BigDecimal.ROUND_UP).intValue());
                stockReceiptItemDTO.setNormalNumber(stockReceiptItemDTO.getNumber());
                stockReceiptItemDTO.setAbnormalNumber(0);
                commodityList.add(stockReceiptItemDTO);
            }
        }

        // 申请退货数量-实退数量＞0，则将这部分库存（申请退货数量-实退数量）还给门店，即相应门店、相应商品的库存要加回去
        // 门店进销存报表，记录这个入库，业务类型=退货审核入库、相关单据=退货单号、操作人=完成收货的操作人、操作时间=完成收货时间
        if (CollectionUtils.isNotEmpty(commodityList)) {
            StockReceiptIDTO stockReceiptIDTO = new StockReceiptIDTO();
            stockReceiptIDTO.setWarehouseId(shop.getId());
            stockReceiptIDTO.setReferId(saleReturnOrder.getId());
            stockReceiptIDTO.setReferCode(saleReturnOrder.getOrderCode());
            stockReceiptIDTO.setUserId(saleReturnOrder.getCheckUserId());
            stockReceiptIDTO.setStockEnums(StockInOutTypeEnums.AUDIT_RETURN_CHECK);
            stockReceiptIDTO.setCommodityList(commodityList);
            xdStockClient.stockReceipt(stockReceiptIDTO);
        }
    }

    private void checkAuditParam(AuditConsignmentReturnOrderIDTO dto, ConsignmentSaleReturnOrder saleReturnOrder, List<ConsignmentSaleReturnOrderItemODTO> returnOrderItemList) {
        QYAssert.isTrue(null != saleReturnOrder, " 代销退货单审核-退货单不存在! ");
        QYAssert.isTrue(CollectionUtils.isNotEmpty(dto.getAuditSaleReturnOrderItems()), " 退货单审核-审核参数不能为空! ");
        QYAssert.isTrue(CollectionUtils.isNotEmpty(returnOrderItemList), " 退货单审核-退货单明细不存在! ");
        QYAssert.isTrue(saleReturnOrder.getStatus() == ConsignmentSaleReturnOrderStatusEnum.NEED_CHECK.getCode(), " 退货单审核-退货单状态不为待审核! ");  //待审核状态的可以进行审核、驳回
        if (dto.getAuditResult() == 1) {
            Map<Long, ConsignmentSaleReturnOrderItemODTO> map = returnOrderItemList.stream().collect(Collectors.toMap(ConsignmentSaleReturnOrderItemODTO::getId, Function.identity()));
            for (AuditConsignmentReturnOrdeItemIDTO auditReturnOrderItem : dto.getAuditSaleReturnOrderItems()) {
                BigDecimal checkQuantity = auditReturnOrderItem.getCheckQuantity();
                ConsignmentSaleReturnOrderItemODTO returnOrderItem = map.get(auditReturnOrderItem.getId());
                QYAssert.isTrue(Objects.nonNull(checkQuantity) &&  checkQuantity.compareTo(BigDecimal.ZERO) >=0 && checkQuantity.compareTo(returnOrderItem.getConfirmQuantity())<=0, " 退货单审核-审核份数需小于等于确认份数大于等于0! ");
            }
        }
    }

    /**
     * 查询最近半年未确认的代销退货单(直送订单, pos调用)
     * @return
     */
    public Boolean existNotConfirmSaleReturnOrder(Long shopId){
        Shop shop = shopMapper.selectByPrimaryKey(shopId);
        Long storeId = shop.getStoreId();
        String  beginDate = DateUtil.getDateFormate(DateUtil.addDay(new Date(), -180), "yyyy-MM-dd")+" 00:00:00";
        String  endDate = DateUtil.getDateFormate(DateUtil.addDay(new Date(), -1), "yyyy-MM-dd")+" 23:59:59";

        int count = consignmentSaleReturnOrderMapper.countNotConfirmSaleReturnOrder(storeId,beginDate,endDate);
        return count > 0;
    }


    /**
     * 代销退货明细
     * @param vo
     * @return
     */
    public PageInfo<ConsignmentReturnOrderPageODTO> consignmentReturnOrderReport(ConsignmentReturnOrderQueryIDTO vo) {

        PageInfo<ConsignmentReturnOrderPageODTO> pageInfo = new PageInfo();

        QYAssert.isTrue(StringUtils.isNotBlank(vo.getBeginDate()), "请选择退货日期");
        QYAssert.isTrue(StringUtils.isNotBlank(vo.getEndDate()), "请选择退货日期");

        DateTime beginDate = DateTime.parse(vo.getBeginDate(), DateTimeFormat.forPattern("yyyy-MM-dd"));
        DateTime endDate = DateTime.parse(vo.getEndDate(), DateTimeFormat.forPattern("yyyy-MM-dd"));
        QYAssert.isTrue(!beginDate.plusDays(30).isBefore(endDate), "退货日期不能大于31天");

        vo.setBeginDate(vo.getBeginDate() + " 00:00:00");
        vo.setEndDate(vo.getEndDate() + " 23:59:59");

        if(vo.getCommodityId() != null || StringUtils.isNotBlank(vo.getBarCode())
                || vo.getCateId1() != null || vo.getCateId2() != null || vo.getCateId3() != null){
            CommodityVO commodityVO = BeanCloneUtils.copyTo(vo, CommodityVO.class);
            List<CommodityBasicEntry> basicEntryList = commodityMapper.findCommodityBasicListByParam(commodityVO);
            if(org.apache.commons.collections.CollectionUtils.isNotEmpty(basicEntryList)){
                List<Long> commodityIdList = basicEntryList.stream().map(item -> Long.valueOf(item.getCommodityId())).collect(Collectors.toList());
                vo.setCommodityIdList(commodityIdList);
            }else {
                return pageInfo;
            }
        }


        List<Long> shopIdList = sMMUserClient.selectUserShopIdList( SelectUserShopIdListIDTO.onlyDb(FastThreadLocalUtil.getQY().getUserId()));
        if (org.apache.commons.collections.CollectionUtils.isEmpty(shopIdList)){
            return pageInfo;
        }

        if (StringUtils.isNotBlank(vo.getOrgCode())) {
            List<ShopDto> shopDtos = shopClient.selectShopListByParentOrgCode(vo.getOrgCode());
            if (org.apache.commons.collections.CollectionUtils.isNotEmpty(shopDtos)) {
                List<Long> orgShopIdList = shopDtos.stream().map(ShopDto::getId).collect(Collectors.toList());
                shopIdList.retainAll(orgShopIdList);

                if (org.apache.commons.collections.CollectionUtils.isEmpty(shopIdList)) {
                    return pageInfo;
                }
            }else {
                return pageInfo;
            }
        }
        vo.setShopIdList(shopIdList);

        pageInfo= PageHelper.startPage(vo.getPageNo(), vo.getPageSize()).doSelectPageInfo(() -> {
            consignmentSaleReturnOrderMapper.consignmentReturnOrderReport(vo);
        });

        List<ConsignmentReturnOrderPageODTO> list = pageInfo.getList();
        for(ConsignmentReturnOrderPageODTO odto : list){
            if(odto.getCheckQuantity() != null){
                odto.setDifferQuantity(odto.getReturnQuantity().subtract(odto.getCheckQuantity()));
            }
        }
        return pageInfo;
    }

    @Transactional(rollbackFor = Exception.class)
    public Boolean cancelSaleReturnOrder(Long saleReturnOrderId) {
        int flag = 0;

        ConsignmentSaleReturnOrder saleReturnOrder = consignmentSaleReturnOrderMapper.selectByPrimaryKey(saleReturnOrderId);
        QYAssert.isTrue(null != saleReturnOrder, " 取消代销退货单-退货单不存在! ");
        QYAssert.isTrue(Objects.equals(saleReturnOrder.getStatus(), ConsignmentSaleReturnOrderStatusEnum.NEED_CONFIRM.getCode())
                ||Objects.equals(saleReturnOrder.getStatus(), ConsignmentSaleReturnOrderStatusEnum.REJECTED.getCode()), " 取消代销退货单-待确认或已驳回的代销退货单可以进行取消! ");

        ConsignmentSaleReturnOrder updateSaleReturnOrder = new ConsignmentSaleReturnOrder();
        updateSaleReturnOrder.setId(saleReturnOrder.getId());
        updateSaleReturnOrder.setStatus(ConsignmentSaleReturnOrderStatusEnum.CANCELLED.getCode());
        flag = consignmentSaleReturnOrderMapper.updateByPrimaryKeySelective(updateSaleReturnOrder);

        return flag > 0;
    }
}
