package com.pinshang.qingyun.order.controller;

import com.github.pagehelper.PageInfo;
import com.pinshang.qingyun.base.api.TokenInfo;
import com.pinshang.qingyun.base.configure.expand.QYAssert;
import com.pinshang.qingyun.base.util.FastThreadLocalUtil;
import com.pinshang.qingyun.order.dto.OrderReferenceXsjmDetailODTO;
import com.pinshang.qingyun.order.mapper.entry.commodity.CommodityHandleEntry;
import com.pinshang.qingyun.order.mapper.entry.commodity.CommodityResultEntry;
import com.pinshang.qingyun.order.mapper.entry.commodity.PreCommodityEntry;
import com.pinshang.qingyun.order.service.ConsignmentSupplierService;
import com.pinshang.qingyun.order.service.ProductService;
import com.pinshang.qingyun.order.vo.commodity.CommodityListRequestVO;
import com.pinshang.qingyun.order.vo.commodity.PreCommodityRequestVo;
import com.pinshang.qingyun.renderer.annotation.MethodRender;
import com.pinshang.qingyun.shop.dto.shopCommodity.CouponCodeQueryODTO;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;


@RestController
@RequestMapping("/product")
public class ProductController {
	
	@Autowired
	private ProductService productService;
	@Autowired
	private ConsignmentSupplierService consignmentSupplierService;

	/**
	 * 获取订货列表->带条件查询(web端门店订货查询)
	 * @param vo
	 * @return
	 */
	@MethodRender
	@PostMapping("/findCommodityListByPage")
	public PageInfo<CommodityResultEntry> findCommodityListByPage(@RequestBody CommodityListRequestVO vo){
		TokenInfo ti = FastThreadLocalUtil.getQY();
		vo.setEnterpriseId(ti.getEnterpriseId());
		if(ti.getStoreId()!= null){
			vo.setStoreId(ti.getStoreId().toString());
		}
		vo.setShopId(ti.getShopId());
		consignmentSupplierService.checkStallId(vo);
		return productService.findCommodityListByPage(vo);
	}

	@GetMapping("/findStoreIdBySubOrderCode")
	public String findStoreIdBySubOrderCode(@RequestParam(value = "subOrderCode",required = false)String subOrderCode){
		return productService.findStoreIdBySubOrderCode(subOrderCode);
	}
	
	
	@PostMapping("/findPreCommodityByParam")
	public List<PreCommodityEntry> findPreCommodityByParam(@RequestBody PreCommodityRequestVo vo){
		return productService.findPreCommodityByParam(vo);
	}

	/**
	 *无码订货(手持订货pda)
	 * @param vo
	 * @return
	 */
	@MethodRender
	@PostMapping("/findCommodityHandListPage")
	public PageInfo<CommodityResultEntry> findCommodityHandListPage(@RequestBody CommodityListRequestVO vo){
		TokenInfo ti = FastThreadLocalUtil.getQY();
		vo.setEnterpriseId(ti.getEnterpriseId());
		if(ti.getStoreId() != null){
			vo.setStoreId(ti.getStoreId().toString());
		}
		consignmentSupplierService.checkStallId(vo);
		return productService.findCommodityHandListPage(vo);
	}

	/**
	 * 扫码订货》商品条码,商品ID查询(手持订货)
	 * @param vo
	 * @return
	 */
	@MethodRender
	@PostMapping("/findCommodityByBarcodeOrId")
	public CommodityHandleEntry findCommodityByBarcodeOrId(@RequestBody CommodityListRequestVO vo){
		TokenInfo ti = FastThreadLocalUtil.getQY();
		vo.setEnterpriseId(ti.getEnterpriseId());
		if(ti.getStoreId() != null){
			vo.setStoreId(ti.getStoreId().toString());
		}
		vo.setShopId(ti.getShopId());
		consignmentSupplierService.checkStallId(vo);
		return productService.findCommodityByBarcodeOrId(vo);
	}

	/**
	 * 获取toB的特价
	 * @param storeId
	 * @return
	 */
	@GetMapping("/getToBPromotionPrice")
	public Map<String, CommodityResultEntry> getToBPromotionPrice(@RequestParam(value = "storeId",required = false)String storeId){
		return productService.getToBPromotionPrice(storeId);
	}

	/**
	 *pos折扣码查询使用
	 * @param barCode
	 * @param storeId
	 * @return
	 */
	@GetMapping("/couponQuery")
	public CouponCodeQueryODTO couponQuery(@RequestParam(value = "barCode",required = false)String barCode, @RequestParam(value = "storeId",required = false) Long storeId){
		return productService.couponQuery(barCode,storeId);
	}

	/**
	 * 鲜食加盟订货参考
	 * @param commodityId
	 */
	@ApiOperation(value = "鲜食加盟订货参考", notes = "鲜食加盟订货参考", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
	@RequestMapping(value = "/xsjmOrderReference", method = RequestMethod.GET)
	public OrderReferenceXsjmDetailODTO xsjmOrderReference(@RequestParam(value = "commodityId",required = false) Long commodityId){
		TokenInfo ti = FastThreadLocalUtil.getQY();
		Long storeId = ti.getStoreId();
		Long shopId = ti.getShopId();
		QYAssert.isTrue(storeId != null && shopId != null, "该用户未关联门店及客户");
		return productService.xsjmOrderReference(storeId,shopId,commodityId);
	}
}
