package com.pinshang.qingyun.order.mapper.entry.orderStatistics;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * @ClassName OrderCompanyAndStoreTypeEntry
 * @Description TODO
 * <AUTHOR>
 * @Date 2021/3/26 13:49
 **/
@Data
public class OrderCompanyAndStoreTypeEntry {
    @ExcelProperty(value = "公司")
    private String companyName;
    @ExcelProperty(value = "客户类型")
    private String storeTypeName;
    @ExcelProperty(value = "实发金额")
    private BigDecimal totalAmount;
}
