package com.pinshang.qingyun.order.controller.tob;

import com.github.pagehelper.PageInfo;
import com.pinshang.qingyun.order.dto.tob.*;
import com.pinshang.qingyun.order.service.tob.ToBOrderStatisticsService;
import com.pinshang.qingyun.order.vo.tob.AdminToBOrderStatisticReqVo;
import com.pinshang.qingyun.order.vo.tob.AdminToBOrderStatisticRespVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * @Description：b端下单统计
 * @Author：ZhangHui
 * @Package：com.pinshang.qingyun.order.controller.tob
 * @Date: 2024/4/7
 */
@Log4j2
@RestController
@RequestMapping("/tob/order/web")
@Api(tags = "b端下单统计")
public class ToBOrderStatisticsController {


    @Autowired
    private ToBOrderStatisticsService toBOrderStatisticsService;

    @ApiOperation(value = "b端下单统计列表分页")
    @PostMapping("/queryToBOrderStatisticPage")
    public PageInfo<AdminToBOrderStatisticRespVo> queryToBOrderStatisticPage(@RequestBody AdminToBOrderStatisticReqVo reqVo) {
        return toBOrderStatisticsService.queryToBOrderStatisticPage(reqVo);
    }



    @ApiOperation(value = "修复数据")
    @PostMapping("/repairData")
    public void repairData(@RequestBody List<ToBOrderStatisticsRepairIDTO> repairIDTOList) {
        toBOrderStatisticsService.repairData(repairIDTOList);
    }


    @ApiOperation(value = "查询订单数量")
    @PostMapping("/queryOrderStatisticsCount")
    public Integer queryOrderStatisticsCount(@RequestBody ToBQueryOrderStatisticsIDTO statisticsIDTO) {
        return toBOrderStatisticsService.queryOrderStatisticsCount(statisticsIDTO);
    }


    @ApiOperation(value = "查询订单数据")
    @PostMapping("/queryOrderStatisticsList")
    public PageInfo<ToBQueryOrderStatisticsODTO> queryOrderStatisticsList(@RequestBody ToBQueryOrderStatisticsIDTO statisticsIDTO) {
        return toBOrderStatisticsService.queryOrderStatisticsList(statisticsIDTO);
    }


    @ApiOperation(value = "查询B端订单数据和提货卡数据")
    @PostMapping("/queryToBOrderAndTakeAppointmentData")
    public ToBOrderAndTakeAppointmentDataODTO queryToBOrderAndTakeAppointmentData(@RequestBody ToBOrderAndTakeAppointmentDataIDTO dataIDTO) {

        return toBOrderStatisticsService.queryToBOrderAndTakeAppointmentData(dataIDTO);

    }
}
