package com.pinshang.qingyun.order.constant;


public class RedissKeyConstant {

    /**
     * 根据队列自动收货
     */
    public static final String AUTO_REVEIVE_BY_QUEUE = "order:autoReceiveByQueue";

    /**
     * 自动收货异常锁
     */
    public static final String AUTO_REVEIVE_ERROR_MSG_LOCK = "order:autoReceive:errorMsg:lock";

    /**
     * 自动收货异常信息
     */
    public static final String AUTO_REVEIVE_ERROR_MSG = "order:autoReceive:errorMsg";

    /**
     * 预订单转订单加索
     */
    public static final String PAY_CALL_BACK = "order:payCallBack";


    /**
     * B端订单同步到大仓消息监听开关
     */
    public final static String TOB_ORDER_SYNC_DC_KAFKA = "TOB_ORDER_SYNC_DC_KAFKA";
}
