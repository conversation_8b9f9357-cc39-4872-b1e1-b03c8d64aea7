package com.pinshang.qingyun.order.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Param;

import com.pinshang.qingyun.base.mybatis.MyMapper;
import com.pinshang.qingyun.order.model.pf.PfStoreCommodityDailyOrderQty;

public interface PfStoreCommodityDailyOrderQtyMapper extends MyMapper<PfStoreCommodityDailyOrderQty> {

	/**
	 * 插入数据
	 * 当已存在数据时（storeId，commodityId，orderDate相同），累加orderQuantity
	 * @param list
	 * @return
	 */
	int insertOrUpdate(@Param("list") List<PfStoreCommodityDailyOrderQty> list);

}
