package com.pinshang.qingyun.order.mapper;

import com.pinshang.qingyun.base.mybatis.MyMapper;
import com.pinshang.qingyun.order.dto.ShopReceiveSettingIDTO;
import com.pinshang.qingyun.order.dto.ShopReceiveSettingODTO;
import com.pinshang.qingyun.order.model.shop.ShopReceiveSetting;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface ShopReceiveSettingMapper extends MyMapper<ShopReceiveSetting>{

    List<ShopReceiveSettingODTO> getShopReceiveSettingPage(@Param("vo") ShopReceiveSettingIDTO shopReceiveSettingIDTO);

    List<ShopReceiveSettingODTO> getShopReceiveSettingLogPage(@Param("vo")ShopReceiveSettingIDTO shopReceiveSettingIDTO);
}