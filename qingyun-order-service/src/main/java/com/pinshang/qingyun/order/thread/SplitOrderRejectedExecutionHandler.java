/*
package com.pinshang.qingyun.order.thread;

import java.util.concurrent.RejectedExecutionHandler;
import java.util.concurrent.ThreadPoolExecutor;

public class SplitOrderRejectedExecutionHandler implements RejectedExecutionHandler{
	
	@Override
	public void rejectedExecution(Runnable r, ThreadPoolExecutor executor) {
		//队列拒绝,则新增线程处理
		if(r instanceof SplitOrderRunnable){
			SplitOrderRunnable sor =(SplitOrderRunnable) r;
			Thread t =new Thread(r);
			t.setName("SplitOrderRejectedThread-" +sor.getVo().getOrderId());
			new Thread(r).start();
		}
	}

}
*/
