package com.pinshang.qingyun.order.mapper.entry.order.syncOrder;

import lombok.Data;

import java.util.Date;

@Data
public class SubOrder2DeliveryOrderListEntry {
	 //生成的DO单编号
	private String orderCode;

	private Long subOrderId;
	private Long enterpriseId;
	private Long warehouseId;
	private Integer supplierId;
	private Integer logisticsModel;
	private Integer varietyTotal;

	private Long referOrderId;
	private String referOrderCode;
	private Long storeId;
	private Date orderTime;
	private Integer deliveryBatch;

	private Integer status=0;
	private Integer type=0;
	private Long createId = -1L;
	private Long updateId = -1L;





	
}
