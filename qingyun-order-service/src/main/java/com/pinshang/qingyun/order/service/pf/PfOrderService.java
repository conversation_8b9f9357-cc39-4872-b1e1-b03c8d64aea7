package com.pinshang.qingyun.order.service.pf;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.pinshang.qingyun.base.api.ApiErrorCodeEnum;
import com.pinshang.qingyun.base.api.PfTokenInfo;
import com.pinshang.qingyun.base.configure.expand.BizLogicException;
import com.pinshang.qingyun.base.configure.expand.QYAssert;
import com.pinshang.qingyun.base.constant.QingyunConstant;
import com.pinshang.qingyun.base.constant.SystemUserConstant;
import com.pinshang.qingyun.base.enums.SubOrderStatusEnums;
import com.pinshang.qingyun.base.enums.*;
import com.pinshang.qingyun.base.enums.order.OrderTypeEnum;
import com.pinshang.qingyun.base.enums.settlement.PayMethodIdEnum;
import com.pinshang.qingyun.base.enums.settlement.ReceiptTypeIdEnum;
import com.pinshang.qingyun.base.enums.xda.XdaOrderProcessStatusEunm;
import com.pinshang.qingyun.base.enums.xda.XdaStoreTypeEnum;
import com.pinshang.qingyun.base.spring.MockMultipartFile;
import com.pinshang.qingyun.base.util.FastThreadLocalUtil;
import com.pinshang.qingyun.box.utils.*;
import com.pinshang.qingyun.order.bo.StoreDeductionBO;
import com.pinshang.qingyun.order.bo.StoreRechargeBO;
import com.pinshang.qingyun.order.config.FileConfigProperties;
import com.pinshang.qingyun.order.config.LockConstants;
import com.pinshang.qingyun.order.dto.file.FileODTO;
import com.pinshang.qingyun.order.dto.pf.*;
import com.pinshang.qingyun.order.dto.shopcart.ShoppingCartCommodityODTO;
import com.pinshang.qingyun.order.dto.shopcart.ShoppingCartODTO;
import com.pinshang.qingyun.order.enums.*;
import com.pinshang.qingyun.order.mapper.*;
import com.pinshang.qingyun.order.mapper.common.CompanyMapper;
import com.pinshang.qingyun.order.mapper.common.EmployeeUserMapper;
import com.pinshang.qingyun.order.mapper.entry.order.CommodityInfoEntry;
import com.pinshang.qingyun.order.mapper.entry.order.OrderItemEntry;
import com.pinshang.qingyun.order.mapper.entry.order.OrderRealDeliveryFinishEntry;
import com.pinshang.qingyun.order.mapper.entry.store.StoreDurationEntry;
import com.pinshang.qingyun.order.model.common.Company;
import com.pinshang.qingyun.order.model.common.EmployeeUser;
import com.pinshang.qingyun.order.model.order.*;
import com.pinshang.qingyun.order.model.pf.PfOrder;
import com.pinshang.qingyun.order.model.pf.PfShoppingCart;
import com.pinshang.qingyun.order.model.store.Store;
import com.pinshang.qingyun.order.pdf.OrderPdfA4Creator;
import com.pinshang.qingyun.order.pdf.PfOrderPdfA4Creator;
import com.pinshang.qingyun.order.service.*;
import com.pinshang.qingyun.order.service.pay.prepaidacct.PrePaidAccountLockedException;
import com.pinshang.qingyun.order.service.pay.prepaidacct.PrePaidAccountService;
import com.pinshang.qingyun.order.service.pf.saledaystatistics.PfCommoditySaleDayStatisticsService;
import com.pinshang.qingyun.order.service.recharge.RechargeService;
import com.pinshang.qingyun.order.util.OrderTimeUtil;
import com.pinshang.qingyun.order.util.UploadFileUtil;
import com.pinshang.qingyun.order.vo.kafka.SubOrderRealQtyWrittenBackMessage;
import com.pinshang.qingyun.order.vo.prepayacct.PrePaidAccountReqVo;
import com.pinshang.qingyun.order.vo.prepayacct.PrePayAccountVo;
import com.pinshang.qingyun.order.vo.store.StoreCompanyVo;
import com.pinshang.qingyun.pf.product.dto.commodityText.PfCommodityInfoODTO;
import com.pinshang.qingyun.pf.product.dto.commodityText.SelectPfCommodityInfoListIDTO;
import com.pinshang.qingyun.pf.product.service.PfCommodityFrontClient;
import com.pinshang.qingyun.pf.product.service.PfCommodityTextClient;
import com.pinshang.qingyun.settlementTb.service.StoreRechargeClient;
import com.pinshang.qingyun.storage.dto.CommodityDefaultDcODto;
import com.pinshang.qingyun.storage.service.CommodityWarehouseClient;
import com.pinshang.qingyun.store.dto.customer.StoreDetailsODTO;
import com.pinshang.qingyun.store.dto.pf.PfPayPasswordDTO;
import com.pinshang.qingyun.store.dto.pf.PfPayResultDTO;
import com.pinshang.qingyun.store.dto.pf.PfUserAccountDTO;
import com.pinshang.qingyun.store.dto.pf.QueryPfUserAccountDTO;
import com.pinshang.qingyun.store.dto.storeFreightSetting.StoreFreightSettingIDTO;
import com.pinshang.qingyun.store.dto.storeFreightSetting.StoreFreightSettingODTO;
import com.pinshang.qingyun.store.service.PfStoreUserClient;
import com.pinshang.qingyun.store.service.StoreCompanyClient;
import com.pinshang.qingyun.store.service.StoreFreightSettingClient;
import com.pinshang.qingyun.store.service.StoreManageClient;
import com.pinshang.qingyun.upload.dto.odto.FileUploadRestSingleODTO;
import com.pinshang.qingyun.upload.dto.odto.FileUploadResultODTO;
import com.pinshang.qingyun.upload.service.FileUploadClientFactory;
import com.pinshang.qingyun.upload.service.XsFileUploadClient;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.time.DateUtils;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionSynchronization;
import org.springframework.transaction.support.TransactionSynchronizationManager;
import org.springframework.web.multipart.MultipartFile;
import tk.mybatis.mapper.entity.Example;

import java.io.File;
import java.io.FileInputStream;
import java.io.InputStream;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 批发服务
 * 
 * <AUTHOR>
 *
 */
@Service
@Slf4j
public class PfOrderService {
	
	@Autowired
    private StoreCompanyClient storeCompanyClient;
	
	@Autowired
	private PfShoppingCartMapper pfShoppingCartMapper;
    
	@Autowired
    private OrderMapper orderMapper;
	
	@Autowired
    private OrderListMapper orderListMapper;
    
	@Autowired
    private PfCommodityFrontClient pfCommodityFrontClient;
	
	@Autowired
    private StoreService storeService;
	
	@Autowired
    private StoreDurationMapper storeDurationMapper;
	
	@Autowired
	private PfStoreUserClient pfStoreUserClient;
	
	@Autowired
    private StoreMapper storeMapper;
	
	@Autowired
	private PfFrequentPurchaseService pfFrequentPurchaseService;
	
	@Autowired
    private PfCommoditySaleDayStatisticsService pfCommoditySaleDayStatisticsService;
	
	@Autowired
    private CommodityMapper commodityMapper;
	
	@Autowired
    private CommodityWarehouseClient commodityWarehouseClient;
	
	@Autowired
    private OrderListGiftMapper orderListGiftMapper;
	
	@Autowired
    private OrderService orderService;
	
	@Autowired
    private SubOrderMapper subOrderMapper;
	
	@Autowired
    private SubOrderItemMapper subOrderItemMapper;
	
//	@Autowired
//    private StoreSettlementMapper storeSettlementMapper;
	
	@Autowired
    private OrderBillMapper orderBillMapper;
	
	@Autowired
    private OrderAsyncKafkaService orderAsyncKafkaService;
	
	@Autowired
	private PfShoppingCartService pfShoppingCartService;
	
	@Autowired
    private OrderHistoryService orderHistoryService;
	
	@Autowired
	private StoreFreightSettingClient storeFreightSettingClient;
	
	private final BigDecimal HUNDRED = new BigDecimal("100");
	
//	@Autowired
//	private PfCommodityLimitClient pfCommodityLimitClient;
	
	@Autowired
    private StoreManageClient storeManageClient;
	
	@Autowired
    private RechargeService rechargeService;
	
	@Autowired
	private PfCommodityTextClient pfCommodityTextClient;
	
	@Autowired
	private PrePaidAccountService prePayAccountService;
	
	@Autowired
	private PfOrderMapper pfOrderMapper;
	
	@Autowired
    private RedissonClient redissonClient;
	
	@Autowired
	private PrePaidAccountService prePaidAccountService;
	
	@Autowired
    private OrderFileMapper orderFileMapper;

    private StoreRechargeClient storeRechargeClient;
	@Autowired
    private FileConfigProperties fileConfigProperties;
	
	@Autowired
    private CompanyMapper companyMapper;
	
	@Autowired
    private OrderMirrorMapper orderMirrorMapper;
	
	@Autowired
    private EmployeeUserMapper employeeUserMapper;
	
	@Autowired
	private PfOrderPdfA4Creator pfOrderPdfA4Creator;
	
	@Autowired
    private FileUploadClientFactory fileUploadClientFactory;

    @Autowired
    private StoreRechargeService storeRechargeService;


	
	/**
	 * 创建订单
	 * @param idto 订单信息
	 * @return 创建成功，返回true
	 * @throws PrePaidAccountLockedException 预付客户付款账户被锁定
	 */
	@Transactional(rollbackFor = Exception.class)
	public boolean creatOrder(PfCreatePrePayOrderIDTO idto) {
		Long storeId = idto.getStoreId();
		Date orderTime = idto.getOrderTime();
		// 0. 检查客户状态
		Integer storeStatus = storeCompanyClient.selectStoreCompanyByStoreId(storeId);
        if(null == storeStatus || storeStatus != 1){
            QYAssert.isTrue(false, "客户已停用!");
        }
		
		// 1. 创建购物车信息
        BuildPfShoppingCart buildPfShoppingCart = new BuildPfShoppingCart(storeId, orderTime, pfShoppingCartMapper, orderMapper, orderListMapper, pfCommodityFrontClient, storeService, storeDurationMapper);
        ShoppingCartODTO shopCart = buildPfShoppingCart.shopCartList();
        
        // 2. 计算配送费
        BigDecimal freightAmount = calculateFreightAmount(idto.getStoreId(),orderTime, shopCart);
        
        // 3. 业务验证
        bizValidateAndMakeup(shopCart, idto, orderTime, freightAmount);
        
        Map<String, List<PfCreOrderItemDTO>> orderClassifyMap = new HashMap<>();
        Date maxBeginDeliveryTime = new Date();

        Date minBeginDeliveryTime = new Date();

        // 4. 拆单 按配送模式+仓库+供应商
        List<PfCreOrderItemDTO> creOrderItemDTOList = assembleProductData(shopCart, orderClassifyMap, maxBeginDeliveryTime, storeId, orderTime);

        //    获取商品最大 最早可订货时间
//        maxBeginDeliveryTime = getMaxBeginDeliveryTime(shopCart,maxBeginDeliveryTime);
//
//        minBeginDeliveryTime = getMinBeginDeliveryTime(shopCart,minBeginDeliveryTime);

        // 5. 计算订单截止时间
        Date orderDurationTime = calculateOrderDeadline(orderTime, minBeginDeliveryTime,maxBeginDeliveryTime, idto.getStoreEndTime());

        
        // 6. 保存订单
        Order order = saveOrder(idto, creOrderItemDTOList, shopCart.getSummation(), freightAmount, orderDurationTime);
        
        // 7. 保存拆单明细
        saveSubOrder(orderClassifyMap, order);
        
        // 8. 扣款 预付款用户保存付款单
        if(XdaStoreTypeEnum.PRE_PAY.equals(idto.getStoreType())){
            saveOrderDeductions(order, storeId, idto.getUserId(), PfPayTypeEnum.DEDUCTION);
        }
        
        // 10. 发送kafka消息
        TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronization() {
        	@Override
        	public void afterCommit() {
    			orderAsyncKafkaService.sendKafkaSaveOrderMessage(order);
        	}
        });
        // 9. 清空购物车
        pfShoppingCartService.clear(storeId, Collections.emptyList());
        // 10. 记录订单日志
        orderHistoryService.insertOrderHistoryOnCreateOrder(order);
        return true;
	}
    /**
     * 获取最早可订货时间(最大)
     * @param shopCart
     * @param maxBeginDeliveryTime
     * @return
     */
    private Date getMaxBeginDeliveryTime(ShoppingCartODTO shopCart,Date maxBeginDeliveryTime){
        List<ShoppingCartCommodityODTO> normalCommList = shopCart.getNormalGroup().getCommodities();
        List<ShoppingCartCommodityODTO> freezeCommList = shopCart.getFreezeGroups().getCommodities();
        normalCommList.addAll(freezeCommList);
        for (ShoppingCartCommodityODTO odto : normalCommList){
            // 获取最大 起始送货时间
            boolean timeChange = maxBeginDeliveryTime.compareTo(odto.getBeginDeliveryTime()) < 0;
            maxBeginDeliveryTime = timeChange ? odto.getBeginDeliveryTime() : maxBeginDeliveryTime;
        }
        return maxBeginDeliveryTime;
    }
    /**
     * 获取最早可订货时间(最小)
     * @param shopCart
     * @param minBeginDeliveryTime
     * @return
     */
    private Date getMinBeginDeliveryTime(ShoppingCartODTO shopCart,Date minBeginDeliveryTime){
        List<ShoppingCartCommodityODTO> normalCommList = shopCart.getNormalGroup().getCommodities();
        List<ShoppingCartCommodityODTO> freezeCommList = shopCart.getFreezeGroups().getCommodities();
        normalCommList.addAll(freezeCommList);

        final Date[] date = {null};
        Optional<ShoppingCartCommodityODTO> max = normalCommList.stream().min(Comparator.comparing(item->{
            if(null != item.getBeginDeliveryTime()){
                date[0] = item.getBeginDeliveryTime();
                return item.getBeginDeliveryTime();
            }
            return date[0];
        }));
        if(max.isPresent() &&  null != max.get().getBeginDeliveryTime()){
            minBeginDeliveryTime = max.get().getBeginDeliveryTime();
        }
        return minBeginDeliveryTime;
    }

    /**
     * 计算配送费
     * @param storeId
     * @param shopCart
     * @return
     */
	private BigDecimal calculateFreightAmount(Long storeId,Date orderTime, ShoppingCartODTO shopCart) {
        BigDecimal orderAmount = shopCart.getSummation();
        //当日自提不收取配送费
        if(checkSameDay(shopCart,orderTime)){
            return BigDecimal.ZERO;
        }
		// 取得配送费比例
		StoreFreightSettingODTO freightSetting = queryFreightSetting(storeId);
		
		// 如果未设置，则直接返回0
		if (freightSetting == null) {
			return BigDecimal.ZERO;
		}
		
		int storeFreightTypeCode = freightSetting.getFreightType();
		StoreFreightTypeEnums storeFreightType = StoreFreightTypeEnums.fromName(storeFreightTypeCode);
		
		// 类型为无
		if (storeFreightType == StoreFreightTypeEnums.NO) {
			return BigDecimal.ZERO;
		}
		
		// 类型为固定比例
		if (storeFreightType == StoreFreightTypeEnums.PERCENT) {
			BigDecimal freightPercent = freightSetting.getFreightPercent();
			// 订单金额*固定比例/100 保留2位精度（四舍五入）
			return orderAmount.multiply(freightPercent).divide(HUNDRED).setScale(2, RoundingMode.HALF_UP);
		}
		
		// 其他异常情况
		log.error(String.format("store[%s]配送类型[%s]未知，配送费设置为0", storeId, storeFreightTypeCode));
		return BigDecimal.ZERO;
	}

    public Boolean checkSameDay(ShoppingCartODTO shopCart,Date orderTime){
        //判断订货日期是否为今日
        if(DateUtils.isSameDay(orderTime,DateUtil.getNowDate())){
            List<ShoppingCartCommodityODTO> commodityODTOS = new ArrayList<>();
            List<ShoppingCartCommodityODTO> normalGroupCommodities = shopCart.getNormalGroup().getCommodities();
            if(SpringUtil.isNotEmpty(normalGroupCommodities)) commodityODTOS.addAll(normalGroupCommodities);
            List<ShoppingCartCommodityODTO> freezeGroupsCommodities = shopCart.getFreezeGroups().getCommodities();
            if(SpringUtil.isNotEmpty(freezeGroupsCommodities)) commodityODTOS.addAll(freezeGroupsCommodities);
            if(SpringUtil.isNotEmpty(commodityODTOS)){
                //判断最早送货日期是否包含今日
                return commodityODTOS.stream().anyMatch(item -> {
                    if (null != item.getBeginDeliveryTime() && item.getBeginDeliveryTime().equals(DateUtil.getNowDate())) {
                        return Boolean.TRUE;
                    }
                    return Boolean.FALSE;
                });
            }
        }
        return Boolean.FALSE;
    }

	private StoreFreightSettingODTO queryFreightSetting(Long storeId) {
		List<Long> storeIds = new ArrayList<>();
		storeIds.add(storeId);
		StoreFreightSettingIDTO query = new StoreFreightSettingIDTO();
		query.setStoreIdList(storeIds);
		List<StoreFreightSettingODTO> settings = storeFreightSettingClient.findStoreFreightSettingByStoreIdList(query);
		
		for (StoreFreightSettingODTO setting : settings) {
			if (setting.getStoreId().equals(storeId)) {
				return setting;
			}
		}
		
		return null;
	}

	/**
     * 订单扣款/回款
     * @param order
     */
	private void saveOrderDeductions(Order order, Long storeId, Long userId, PfPayTypeEnum typeEnum) {
		PrePaidAccountReqVo req;
		if (PfPayTypeEnum.DEDUCTION.equals(typeEnum)) {
			req = prePaidAccountService.paySync(storeId, order.getActualPaid(), userId, "<--批发app扣款：" + order.getOrderCode() + " -->", 
										PrePaidAccountReqRefBillTypeEnum.ORDER, order.getId(), order.getOrderTime());
			
		} else {
			req = prePaidAccountService.receiveSync(storeId, order.getActualPaid(), userId, "<--批发app退款：" + order.getOrderCode() + " -->", 
					PrePaidAccountReqRefBillTypeEnum.ORDER, order.getId(), order.getOrderTime());
		}
		
		OrderBill ob = new OrderBill();
        ob.setStoreId(storeId);
        ob.setOrderId(req.getRefBillId());
        ob.setOrderTime(req.getRefBillTime());
        if (PfPayTypeEnum.DEDUCTION.equals(typeEnum)) { // 扣款
        	ob.setArAmount(req.getAmount()); // 应收
        	ob.setPaAmount(BigDecimal.ZERO);
        } else { // 退款
        	ob.setArAmount(BigDecimal.ZERO);
        	ob.setPaAmount(req.getAmount()); // 应付
        }
        
        ob.setBillRemark(req.getRemarks());
        ob.setStoreBalance(req.getBalance());
        ob.setCreateTime(new Date());
        ob.setCrateId(storeId);
        orderBillMapper.insertSelective(ob);
	}

	/**
     * 保存拆单明细
     * @param orderClassifyMap 订单分类
     * @param order 订单
     */
	private void saveSubOrder(Map<String, List<PfCreOrderItemDTO>> orderClassifyMap, Order order) {
		for (Map.Entry<String, List<PfCreOrderItemDTO>> entry : orderClassifyMap.entrySet()){
            if("classifyKey".equals(entry.getKey())){
                continue;
            }
            List<PfCreOrderItemDTO> itemList = entry.getValue();
            Integer logisticsModel = itemList.get(0).getLogisticsModel();
            Long warehouseId = itemList.get(0).getWarehouseId();
            Long supplierId = itemList.get(0).getSupplierId();
            SubOrder subOrder = new SubOrder();
            subOrder.setOrderId(order.getId());
            subOrder.setOrderTime(order.getOrderTime());
            subOrder.setStatus(SubOrderStatusEnums.SUB_ORDER_UNDELIVERY.getCode());
            subOrder.setLogisticsModel(logisticsModel);
            subOrder.setVarietyTotal(itemList.size());
            subOrder.setCreateId(order.getCreateId());
            subOrder.setCreateTime(new Date());
            subOrder.setEnterpriseId(QingyunConstant.ENTERPRISE_ID);
            subOrder.setWarehouseId(warehouseId);
            subOrder.setSupplierId(supplierId);
            subOrder.setPresaleStatus(1);

            List<SubOrderItem> items = new ArrayList<SubOrderItem>(itemList.size());
            BigDecimal totalPriceForSubOrder = BigDecimal.ZERO;
            for(PfCreOrderItemDTO item : itemList){
                SubOrderItem subOrderItem = new SubOrderItem();
                subOrderItem.setCommodityId(item.getCommodityId());
                subOrderItem.setCreateId(order.getCreateId());
                subOrderItem.setCreateTime(new Date());
                subOrderItem.setUpdateTime(new Date());
                subOrderItem.setPrice(item.salePrice());
                subOrderItem.setQuantity(item.getQuantity());
                subOrderItem.setTotalPrice(item.saleAmount());
                totalPriceForSubOrder = totalPriceForSubOrder.add(item.saleAmount()).setScale(2, BigDecimal.ROUND_HALF_UP);
                items.add(subOrderItem);
            }
            subOrder.setSubOrderCode(IDGenerator.newOrderCode());
            subOrder.setTotalPrice(totalPriceForSubOrder);
            subOrderMapper.insertSelective(subOrder);
            for(SubOrderItem item : items){
                item.setSubOrderId(subOrder.getId());
            }
            subOrderItemMapper.insertList(items);
        }
	}

	/**
	 * 保存订单
	 * @param idto 订单信息
	 * @param creOrderItemDTOList 订单明细
	 * @param orderAmount 订单金额
	 * @param freightAmount 配送费
	 * @param orderDurationTime 截止时间
	 * @return 订单
	 */
	private Order saveOrder(PfCreatePrePayOrderIDTO idto, List<PfCreOrderItemDTO> creOrderItemDTOList,
			BigDecimal orderAmount, BigDecimal freightAmount, Date orderDurationTime) {
		Order order = new Order();
        Date date = new Date();
        order.setCreateId(idto.getStoreId());
        order.setUpdateId(idto.getStoreId());
        order.setCreateTime(date);
        order.setUpdateTime(date);
        order.setOrderAmount(orderAmount);
        order.setFinalAmount(orderAmount);
        order.setFreightAmount(freightAmount);
        order.setStoreId(idto.getStoreId());
        order.setOrderTime(idto.getOrderTime());
        order.setOrderCode(IDGenerator.newOrderCode());
        order.setPrintNum(1);
        order.setOrderType(OrderTypeEnum.PF_APP_ORDER.getCode());
        order.setModeType(OrderModeType.ORDER.getCode());
        order.setPrintType(OrderPrintTypeEnum.NOPRInteger);
        order.setOrderRemark(idto.getRemark());
        order.setOrderStatus(0);
        order.setSyncStatus(YesOrNoEnums.NO.getCode());
        order.setOrderDurationTime(orderDurationTime);
        order.setProcessStatus(XdaOrderProcessStatusEunm.WAITING_SHIP.getCode());
        StoreCompanyVo storeCompanyByStoreId = storeMapper.getStoreCompanyByStoreId(order.getStoreId());
        order.setCompanyId(storeCompanyByStoreId.getCompanyId());
        order.setStoreTypeId(storeCompanyByStoreId.getStoreTypeId());
        order.setPresaleStatus(1);
        order.setSettleOrderTime(order.getOrderTime());
        this.orderMapper.insertSelective(order);

        List<OrderList> orderLists = new ArrayList<OrderList>();
        creOrderItemDTOList.forEach(i->{
            OrderList orderList = new OrderList();
            orderList.setCommodityId(i.getCommodityId());
            orderList.setCommodityNum(i.saleQuantity());
            orderList.setType(i.getType().getCode());
            if (ProductTypeEnums.PRODUCT.equals(i.getType())) {
                orderList.setRemark("订单商品");
            }
            orderList.setCommodityPrice(i.salePrice());
            orderList.setTotalPrice(i.saleAmount());
            orderList.setOrderId(order.getId());
            orderList.setGiftModelId(i.getConditionId());
            orderList.setPresaleStatus(1);
            orderList.setCombType(1);
            this.orderListMapper.insertUseGeneratedKeys(orderList);
            orderLists.add(orderList);
            OrderListGift gift = BeanCloneUtils.copyTo(orderList, OrderListGift.class);
            gift.setId(null);
            this.orderListGiftMapper.insertSelective(gift);
            
            // 》》》》》》 发消息到 统计库，订单明细赋值是OrderListGift的ID 
            orderList.setId(gift.getId());
        });
        order.setOrderList(orderLists);
        orderService.crateOrderMirror(order);
        return order;
	}

    /**
     * 计算订单最晚可取消时间
     * 当前日期 + 订货日期与商品最大最早起送日期之间的天数 + 客户截单时间
     * 订单截止时间逻辑修改
     * http://192.168.0.213/zentao/story-view-8197.html
     *  二次调整
     * http://192.168.0.213/zentao/story-view-9168.html
     * @param orderTime
     * @param maxBeginDeliveryTime
     * @param storeEndTime
     * @return
     */
    private Date calculateOrderDeadline(Date orderTime,Date minBeginDeliveryTime, Date maxBeginDeliveryTime, String storeEndTime) {
        String datestr = null;
        if(OrderTimeUtil.isTheSameDay(orderTime,new Date())){
            datestr = DateUtil.get4yMdHms(new Date());
        }else {
            datestr = DateUtil.get4yMd(new Date()) + " "+storeEndTime;
        }
        return DateUtil.parseDate(datestr, "yyyy-MM-dd HH:mm");

//        String time = null;
//        //判断最早订货时间是否为今日
//        if(OrderTimeUtil.isTheSameDay(new Date(),minBeginDeliveryTime)){
//            //判断送货时间是否为今日
//            if(OrderTimeUtil.isTheSameDay(orderTime,minBeginDeliveryTime)){
//                return new Date();
//            }else {
//                Date orderDurationTime = DateUtil.addDay(orderTime, -1);
//                time = DateUtil.getDateFormate(orderDurationTime, "yyyy-MM-dd") + " " + storeEndTime;
//            }
//        }else {
//            // 计算T+n
//            int days = DateUtil.getDayDif(minBeginDeliveryTime,Date.from(LocalDate.now().atStartOfDay(ZoneId.systemDefault()).toInstant()) );
//            QYAssert.isTrue(days >= 0, "商品最晚订货时间发生改变，请刷新购物车确认");
//            //计算订单截止时间(送货日期 - n)
//            Date orderDurationTime = DateUtil.addDay(orderTime, 0-days);
//            time = DateUtil.getDateFormate(orderDurationTime, "yyyy-MM-dd") + " " + storeEndTime;
//        }
//        return DateUtil.parseDate(time, "yyyy-MM-dd HH:mm");
    }


	private void bizValidateAndMakeup(ShoppingCartODTO shopCart, PfCreatePrePayOrderIDTO idto, Date orderTime, BigDecimal freightAmount) {
		// 检查是否复合结算条件
        if(!shopCart.getCanSettlement()){
            throw new BizLogicException(shopCart.getWarnMessage() , ApiErrorCodeEnum.PF_PRE_ORDER_WARN);
        }
        
        // 检查购物车是否有业务告警
        boolean shoppingCartHasWarnings = false;
        StringBuilder shopCartWarinigs = new StringBuilder();
        List<ShoppingCartCommodityODTO> commodities = shopCart.getNormalGroup().getCommodities();
        for (ShoppingCartCommodityODTO item : commodities) {
        	if (item.getStockWarningTips() != null) {
        		shoppingCartHasWarnings = true;
        		shopCartWarinigs.append(item.getStockWarningTips())
        			.append("-")
        			.append("[").append(item.getCommodityName()).append("]")
        			.append("; ");
        	}
            
        };
        if (shoppingCartHasWarnings) {
        	throw new BizLogicException(shopCartWarinigs.toString() , ApiErrorCodeEnum.PF_ORDER_OVER_LIMIT);
        }
        
        // 支付密码
        Long storeId = idto.getStoreId();

        // 1.检查客户是否在订货时间内
        StoreDurationEntry sd = this.orderMapper.findStoreDurationByStoreId(storeId.toString());
        if(null != sd){
            int difDay = DateUtil.getDayDif(orderTime, DateUtil.getNowDate());
            if (StringUtils.isNotBlank(sd.getBeginTime()) && StringUtils.isNotBlank(sd.getEndTime())){
                idto.setStoreEndTime(sd.getEndTime());
                if(difDay >= 1){
                    QYAssert.isTrue(DateTimeUtil.compareNewDate(sd.getBeginTime(), "23:59"), "超出订货时间,无法操作!");
                }else {
                    QYAssert.isTrue(DateTimeUtil.compareNewDate(sd.getBeginTime(), sd.getEndTime()), "超出订货时间,无法操作!");
                }
            }
        }
        BigDecimal summation = shopCart.getSummation();
        BigDecimal actualPaid = summation.add(freightAmount); // 批发的实付金额=订单总金额+运费
        BigDecimal orderAmount = idto.getOrderAmount();
        if(!shopCart.getVarietySum().equals(idto.getVarietySum()) || actualPaid.compareTo(orderAmount) != 0){
            throw new BizLogicException(ApiErrorCodeEnum.PF_PRE_ORDER_WARN.getRemark() , ApiErrorCodeEnum.PF_PRE_ORDER_WARN);
            // 校验商品金额，品种数量以及商品数量
        }
        // 客户账户类型
        QueryPfUserAccountDTO queryPfUserAccountDTO = new QueryPfUserAccountDTO();
        queryPfUserAccountDTO.setStoreId(storeId);
        PfUserAccountDTO pfUserAccountDTO = pfStoreUserClient.queryUserAccountInfo(queryPfUserAccountDTO);
        QYAssert.notNull(pfUserAccountDTO, "客户账户不存在");
        QYAssert.isTrue(pfUserAccountDTO.getHasPayPassword(), "请设置支付密码");
        QYAssert.hasLength(idto.getPayPassword(), "密码为空！");
        PfPayPasswordDTO pfPayPasswordDTO = new PfPayPasswordDTO();
        pfPayPasswordDTO.setPayPassword(idto.getPayPassword());
        pfPayPasswordDTO.setUserId(idto.getUserId());
        pfPayPasswordDTO.setTerminalSourceTypeEnum(idto.getTypeEnum());
        PfPayResultDTO payResultODTO = pfStoreUserClient.pfPay(pfPayPasswordDTO);
        if(!payResultODTO.getPaySuccess()){
            throw new BizLogicException("密码错误。", ApiErrorCodeEnum.ACCOUNT_OR_PASSWORD_FAILURE);
        }
        Boolean amountFlag = actualPaid.compareTo(pfUserAccountDTO.getCurrentAmount()) < 0;
        QYAssert.isTrue(amountFlag, "余额不足，无法下单");
        idto.setStoreType(pfUserAccountDTO.getStoreType());

        // 计算下单次数
        Integer storeLimit = storeMapper.findOrderNumberLimit(storeId);
        if(storeLimit != null){
            List<Order> orderList = orderMapper.queryOrderByOrderTime(storeId, orderTime);
            QYAssert.isTrue(storeLimit > orderList.size(), "每天限制"+storeLimit+"单，建议取消1单。");
        }
        
	}

//	/**
//	 * 校验库存限量
//	 * @param shopCart
//	 * @param idto
//	 * @param orderTime
//	 * @throws BizLogicException 校验失败
//	 * @deprecated
//	 */
//	private void validateCommodityLimit(ShoppingCartODTO shopCart, PfCreatePrePayOrderIDTO idto, Date orderTime) {
//		// 收集订单商品id（库存限量只对普通商品和冷冻商品有效，赠品无效）
//		List<ShoppingCartCommodityODTO> nomalCommodityList = shopCart.getNormalGroup().getCommodities();
//        List<ShoppingCartCommodityODTO> freezeCommodityList = shopCart.getFreezeGroups().getCommodities();
//        List<Long> currentOrderCommodityIds = new ArrayList<>();
//        currentOrderCommodityIds.addAll(nomalCommodityList.stream().map(ShoppingCartCommodityODTO::getCommodityId).collect(Collectors.toList()));
//        currentOrderCommodityIds.addAll(freezeCommodityList.stream().map(ShoppingCartCommodityODTO::getCommodityId).collect(Collectors.toList()));
//        
//        // 根据订单日期，商品id列表查询库存限量设置
//        CommodityLimitQueryIDTO limitSetsQuery = new CommodityLimitQueryIDTO(orderTime, currentOrderCommodityIds);
//        List<CommodityLimitQueryODTO> limitSetsResult = pfCommodityLimitClient.selectCommodityLimitList(limitSetsQuery);
//        
//        // 根据库存限量设置（商品+设置的起止日期），查询商品销量
//        List<CommoditySaleStatisticsQueryDto> saleStatisticsQueryList = new ArrayList<>();
//        for (CommodityLimitQueryODTO limitSet : limitSetsResult) {
//        	CommoditySaleStatisticsQueryDto statisticQuery = new CommoditySaleStatisticsQueryDto();
//        	statisticQuery.setCommodityId(limitSet.getCommodityId());
//        	statisticQuery.setBeginDate(limitSet.getBeginDate());
//        	statisticQuery.setEndDate(limitSet.getEndDate());
//        	saleStatisticsQueryList.add(statisticQuery);
//        }
//        // 商品销量数据 
//        List<CommoditySaleStatisticsDto> statisticsList = pfCommoditySaleDayStatisticsService.queryCommoditySaleStatistics(saleStatisticsQueryList);
//        
//        // 校验商品数量（当前订单数量+往期销量）是否超过限量值
//        CommodityLimitValidator validator = new CommodityLimitValidator();
//        validator.addStatistics(statisticsList);
//        validator.addOrderCommodityList(nomalCommodityList);
//        validator.addOrderCommodityList(freezeCommodityList);
//        validator.setCommodityLimitSets(limitSetsResult);
//        validator.validate();
//	}
	
//	/**
//	 * 商品库存限量较预器
//	 * <AUTHOR>
//	 *
//	 */
//	private static final class CommodityLimitValidator {
//
//		/**
//		 * 商品销量统计（key=商品id）
//		 * 这里只校验销售数量，所以不会设置销售金额
//		 */
//		private final Map<Long, CommoditySaleStatisticsDto> statisticsMap = new HashMap<>();
//		
//		private final List<CommodityLimitQueryODTO> limitSetList = new ArrayList<>();
//		
//		/**
//		 * 往期商品销量统计
//		 * @param statisticsList
//		 */
//		public void addStatistics(List<CommoditySaleStatisticsDto> statisticsList) {
//			for (CommoditySaleStatisticsDto st : statisticsList) {
//				Long commodityId = st.getCommodityId();
//				CommoditySaleStatisticsDto cs = statisticsMap.get(commodityId);
//				if (cs == null) {
//					cs = new CommoditySaleStatisticsDto();
//					cs.setCommodityId(commodityId);
//					cs.setCommodityCode(st.getCommodityCode());
//					cs.setCommodityName(st.getCommodityName());
//					cs.setTotalQuantity(BigDecimal.ZERO);
//					statisticsMap.put(commodityId, cs);
//				}
//				cs.setTotalQuantity(cs.getTotalQuantity().add(st.getTotalQuantity()));
//			}
//		}
//
//		/**
//		 * 限量设置
//		 * @param limitSets
//		 */
//		public void setCommodityLimitSets(List<CommodityLimitQueryODTO> limitSets) {
//			limitSetList.addAll(limitSets);
//		}
//
//		/**
//		 * @throws BizLogicException 校验失败
//		 */
//		public void validate() {
//			boolean beyoundLimit = false;
//			List<BeyondLimitCommodityInfo> beyoundLimitCommodityInfoList = new ArrayList<>();
//			for (CommodityLimitQueryODTO limitSet : limitSetList) {
//				Long commodityId = limitSet.getCommodityId();
//				BigDecimal limitNum = limitSet.getLimitNum();
//				CommoditySaleStatisticsDto st = statisticsMap.get(commodityId);
//				if (st == null) { // 无销售记录
//					continue;
//				}
//				BigDecimal saleQuantity = st.getTotalQuantity();
//				if (saleQuantity.compareTo(limitNum) > 0) {
//					beyoundLimit = true;
//					BeyondLimitCommodityInfo blc = new BeyondLimitCommodityInfo(commodityId, st.getCommodityCode(), st.getCommodityName(), 
//																					saleQuantity, limitNum);
//					beyoundLimitCommodityInfoList.add(blc);
//				}
//			}
//			
//			if (beyoundLimit) {
//				StringBuilder errorMsg = new StringBuilder();
//				for (BeyondLimitCommodityInfo info : beyoundLimitCommodityInfoList) {
//					errorMsg.append(String.format("商品[%s]下单总量[%s]超过库存限制[%s]；", info.getCommodityName(), info.getSaleQuantity(), info.getLimitNum()));
//				}
//				throw new BizLogicException(errorMsg.toString() , ApiErrorCodeEnum.PF_ORDER_OVER_LIMIT);
//			}
//		}
//		
//		/**
//		 * 当前订单商品信息（商品+商品数量）
//		 * @param commodityList
//		 */
//		public void addOrderCommodityList(List<ShoppingCartCommodityODTO> commodityList) {
//			for (ShoppingCartCommodityODTO orderItem : commodityList) {
//				Long commodityId = orderItem.getCommodityId();
//				CommoditySaleStatisticsDto cs = statisticsMap.get(commodityId);
//				if (cs == null) {
//					cs = new CommoditySaleStatisticsDto();
//					cs.setCommodityId(commodityId);
//					cs.setCommodityCode(orderItem.getCommodityCode());
//					cs.setCommodityName(orderItem.getCommodityName());
//					cs.setTotalQuantity(BigDecimal.ZERO);
//					statisticsMap.put(commodityId, cs);
//				}
//				cs.setTotalQuantity(cs.getTotalQuantity().add(orderItem.getQuantity()));
//			}
//		}
//		
//	}
	
//	static final class BeyondLimitCommodityInfo {
//
//		private Long commodityId;
//		private String commodityCode; 
//		private String commodityName;
//		private BigDecimal saleQuantity;
//		private BigDecimal limitNum;
//		
//		public BeyondLimitCommodityInfo(Long commodityId, String commodityCode, String commodityName,
//				BigDecimal saleQuantity, BigDecimal limitNum) {
//			this.commodityId = commodityId;
//			this.commodityCode = commodityCode;
//			this.commodityName = commodityName;
//			this.saleQuantity = saleQuantity;
//			this.limitNum = limitNum;
//		}
//
//		public Long getCommodityId() {
//			return commodityId;
//		}
//
//		public BigDecimal getSaleQuantity() {
//			return saleQuantity;
//		}
//
//		public BigDecimal getLimitNum() {
//			return limitNum;
//		}
//
//		public String getCommodityCode() {
//			return commodityCode;
//		}
//
//		public String getCommodityName() {
//			return commodityName;
//		}
//		
//	}

	/**
     * 组装商品信息，
     * 商品分组按照物流模式+仓库id+采购商id
     * 保存商品常购清单
     * @return 商品信息
     */
    private List<PfCreOrderItemDTO> assembleProductData(ShoppingCartODTO shopCart, Map<String, List<PfCreOrderItemDTO>> orderClassifyMap, Date maxBeginDeliveryTime, Long storeId, Date orderTime){
        List<ShoppingCartCommodityODTO> normalCommList = shopCart.getNormalGroup().getCommodities();
        List<ShoppingCartCommodityODTO> freezeCommList = shopCart.getFreezeGroups().getCommodities();
        Map<Long, List<ShoppingCartCommodityODTO>> conditionMap = shopCart.getConditionMap();
        normalCommList.addAll(freezeCommList);

        List<PfCreOrderItemDTO> orderCommodityList = new ArrayList<>();
        Set<Long> purchaseCommodityIds =new HashSet<>(normalCommList.size());
        for (ShoppingCartCommodityODTO odto : normalCommList){
            // 获取最大 起始送货时间
            boolean timeChange = maxBeginDeliveryTime.compareTo(odto.getBeginDeliveryTime()) < 0;
            maxBeginDeliveryTime = timeChange ? odto.getBeginDeliveryTime() : maxBeginDeliveryTime;

            PfCreOrderItemDTO itemDTO = BeanCloneUtils.copyTo(odto, PfCreOrderItemDTO.class);
            itemDTO.setType(ProductTypeEnums.PRODUCT);
            purchaseCommodityIds.add(odto.getCommodityId());
            orderCommodityList.add(itemDTO);
        }

        // 保存客户常购商品
        pfFrequentPurchaseService.saveSaleDayStatistics(storeId, orderCommodityList);
        // toB订单销量日统计
        pfCommoditySaleDayStatisticsService.saveSaleDayStatistics(orderTime, orderCommodityList);

        for(Map.Entry<Long, List<ShoppingCartCommodityODTO>> odtos : conditionMap.entrySet()){
            Long conditionId = odtos.getKey();
            for(ShoppingCartCommodityODTO odto : odtos.getValue()){
                PfCreOrderItemDTO itemDTO = BeanCloneUtils.copyTo(odto, PfCreOrderItemDTO.class);
                itemDTO.setType(ProductTypeEnums.GIFT);
                itemDTO.setConditionId(conditionId);
                orderCommodityList.add(itemDTO);
            }
        }
        List<Long> commodityIds = orderCommodityList.stream().map(PfCreOrderItemDTO :: getCommodityId).collect(Collectors.toList());
        List<CommodityInfoEntry> commodityInfoEntryList = commodityMapper.findCommodityInfoIds(commodityIds);
        Map<Long,CommodityInfoEntry> commodityInfoEntryMap = commodityInfoEntryList.stream().collect(Collectors.toMap(CommodityInfoEntry::getId, Function.identity()));
        Map<Long, CommodityDefaultDcODto> dcODtoMap = commodityWarehouseClient.queryCommodityDefaultWarehouseAndSupplier(commodityIds);

        for(PfCreOrderItemDTO dto : orderCommodityList){
            CommodityInfoEntry entry = commodityInfoEntryMap.get(dto.getCommodityId());
            dto.setLogisticsModel(entry.getLogisticsModel());
            // 获取商品 仓库id和供应商id
            CommodityDefaultDcODto oDto = dcODtoMap.get(dto.getCommodityId());
            QYAssert.notNull(oDto, "商品没有默认供应商或默认仓库");

            dto.setWarehouseId(oDto.getWarehouseId());
            dto.setSupplierId(oDto.getSupplierId());
            // 商品分类
            String key = dto.getClassifyKey();
            if(orderClassifyMap.containsKey(key)){
                orderClassifyMap.get(key).add(dto);
            }else {
                List<PfCreOrderItemDTO> itemDTOS = new ArrayList<>();
                itemDTOS.add(dto);
                orderClassifyMap.put(key, itemDTOS);
            }

        }
        return orderCommodityList;
    }

    /**
     * 取消订单
     * @param idto
     * @return
     * @throws PrePaidAccountLockedException 
     */
    @Transactional(rollbackFor = Exception.class)
	public Integer cancelOrder(PfOrderCancelIDTO idto) {
    	Long orderId = idto.getOrderId();
        Long storeId = idto.getStoreId();
        Order order = orderMapper.selectByPrimaryKey(orderId);
        
        
        QYAssert.isTrue(null != order , "订单不存在!");
        QYAssert.isTrue(order.getCreateId().equals(storeId), "操作失败，只能取消自己创建的订单！");
        QYAssert.isTrue(DateUtil.compareDate(new Date(), order.getOrderDurationTime()), "订单已过截单时间无法取消!");

        // 前置状态检查（参考：0:正常,1:删除, 2:取消）
        QYAssert.isTrue(order.getOrderStatus() == 0 , String.format("当前订单状态[%s]，不能取消", OrderStatusEnums.getName(order.getOrderStatus())));
        
        List<OrderItemEntry> commList = this.orderMapper.queryOrderItem4Copy(orderId);
        // 7.toB订单销量日统计
        List<PfCreOrderItemDTO> orderLists = new ArrayList<>();
        for(OrderItemEntry item : commList){
        	PfCreOrderItemDTO dto = new PfCreOrderItemDTO();
            dto.setCommodityId(item.getCommodityId());
            dto.setQuantity(item.getCommodityNum().negate());
            dto.setIsSpecialPrice(0);
            dto.setCommodityPrice(item.getCommodityPrice().negate());
            orderLists.add(dto);
        }
        pfCommoditySaleDayStatisticsService.saveSaleDayStatistics(order.getOrderTime(), orderLists);
        Map<String,Object> paramMap = new HashMap<String, Object>(2);
        paramMap.put("orderId", order.getId());
        paramMap.put("orderStatus", 2);
        paramMap.put("cancelReasonId", idto.getReasonOptionId());
        Integer rowNumber = orderMapper.updateOrderStatusByParameter(paramMap);
        QYAssert.isTrue(rowNumber > 0 , "订单取消失败!");
        // 取消子订单
        int subOrderRows = subOrderMapper.cancelSubOrder(order.getId());
        QYAssert.isTrue(subOrderRows > 0 , "子订单取消失败!");
        // 客户账户类型
        QueryPfUserAccountDTO queryPfUserAccountDTO = new QueryPfUserAccountDTO();
        queryPfUserAccountDTO.setStoreId(storeId);
        PfUserAccountDTO pfUserAccountDTO = pfStoreUserClient.queryUserAccountInfo(queryPfUserAccountDTO);
        QYAssert.notNull(pfUserAccountDTO, "客户账户不存在");
        if(XdaStoreTypeEnum.PRE_PAY.equals(pfUserAccountDTO.getStoreType())){
            saveOrderDeductions(order, storeId, idto.getUserId(), PfPayTypeEnum.PAY_BACK);
        }
        
        // 发送取消订单消息给统计查询
        log.info("start send kafka msg: orderId={} ", orderId);
        TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronization() {
        	@Override
        	public void afterCommit() {
        		orderAsyncKafkaService.sendKafkaCancelOrder(order);
        	}
        });
        
        // 记录订单日志
        orderHistoryService.insertOrderHistoryOnCancelOrder(order);
        return rowNumber;
	}

    /**
     * 订单预览
     * @param query
     * @return
     */
	public PfPreOrderODTO preOrderView(PfPreOrderIDTO query) {
		Long storeId = query.getStoreId();
        // 客户基本信息
		StoreDetailsODTO selectODTO = storeManageClient.findStoreDetailsById(storeId);
        QYAssert.notNull(selectODTO, "客户不存在");
        // 客户账户类型
        QueryPfUserAccountDTO queryPfUserAccountDTO = new QueryPfUserAccountDTO();
        queryPfUserAccountDTO.setStoreId(storeId);
        PfUserAccountDTO pfUserAccountDTO = pfStoreUserClient.queryUserAccountInfo(queryPfUserAccountDTO);
        QYAssert.notNull(pfUserAccountDTO, "客户账户不存在");
        Date orderTime = query.getOrderTime();
        
        BuildPfShoppingCart buildPfShoppingCart = new BuildPfShoppingCart(storeId, orderTime, pfShoppingCartMapper, orderMapper, orderListMapper, pfCommodityFrontClient, storeService, storeDurationMapper);
        ShoppingCartODTO shopCart = buildPfShoppingCart.shopCartList();
        
        if(!shopCart.getCanSettlement()){
            throw new BizLogicException(shopCart.getWarnMessage() , ApiErrorCodeEnum.PF_PRE_ORDER_WARN);
        }
       // 校验商品金额，品种数量以及商品数量
        if(!shopCart.getVarietySum().equals(query.getVarietySum()) || shopCart.getSummation().compareTo(query.getOrderAmount()) != 0){
            throw new BizLogicException(ApiErrorCodeEnum.PF_PRE_ORDER_WARN.getRemark() , ApiErrorCodeEnum.PF_PRE_ORDER_WARN);
        }

        PfPreOrderODTO preOrderDTO = new PfPreOrderODTO();
        preOrderDTO.covertStoreBaseInfo(selectODTO);
        preOrderDTO.setOrderTime(orderTime);
        preOrderDTO.setVarietySum(shopCart.getVarietySum());
        preOrderDTO.setTotalOriginPrice(shopCart.getSummation());
        preOrderDTO.setActuallyPaid(shopCart.getSummation());
        preOrderDTO.setStoreType(pfUserAccountDTO.getStoreType());
        preOrderDTO.setCurrentAmount(pfUserAccountDTO.getCurrentAmount());
        preOrderDTO.setFreightAmount(calculateFreightAmount(storeId,orderTime,shopCart));
        if(XdaStoreTypeEnum.PRE_PAY.equals(pfUserAccountDTO.getStoreType())){
            String minRechargeMoney = rechargeService.queryRechargeMoney(query.getAppCode());
            preOrderDTO.setMinRechargeMoney(minRechargeMoney);
        }
        return preOrderDTO;
	}

	/**
	 * 批发 查询订单列表
	 * @param param 查询参数
	 * @return 订单列表
	 */
	public PageInfo<PfOrder4AppODTO> queryOrderListByPage(PfQueryOrder4AppParam param) {
		param.setStoreId(param.getStoreId());
        PageInfo<PfOrder4AppODTO> page = PageHelper.startPage(param.getPageNo(), param.getPageSize())
        		.doSelectPageInfo(() -> {orderMapper.queryOrder4PfApp(param);});

        List<PfOrder4AppODTO> pfOrder4AppODTOS = page.getList();
        if(pfOrder4AppODTOS.isEmpty()){
            return page;
        }
        covertCommodityInfo(pfOrder4AppODTOS);
        page.setList(pfOrder4AppODTOS);
        return page;
	}
	
	private List<PfOrder4AppODTO> covertCommodityInfo(List<PfOrder4AppODTO> pfOrder4AppODTOS){
        Map<Long, PfOrder4AppODTO> orderMap = pfOrder4AppODTOS.stream().collect(Collectors.toMap(PfOrder4AppODTO::getOrderId, Function.identity()));

        List<PfOrderItem4AppODTO> odtos = orderListMapper.queryOrderItem4PfApp(new ArrayList<>(orderMap.keySet()) );

        Set<Long> commodityIds = odtos.stream().map(PfOrderItem4AppODTO :: getCommodityId).collect(Collectors.toSet());
        SelectPfCommodityInfoListIDTO infoListIDTO = new SelectPfCommodityInfoListIDTO();
        infoListIDTO.setCommodityIdList(new ArrayList<>(commodityIds));
        List<PfCommodityInfoODTO> pfCommodityInfoODTOList = pfCommodityTextClient.selectPfCommodityInfoList(infoListIDTO);
        QYAssert.notEmpty(pfCommodityInfoODTOList, "商品基本信息为空");
        Map<Long,PfCommodityInfoODTO> pfCommodityInfoODTOMap = pfCommodityInfoODTOList.stream().collect(Collectors.toMap(PfCommodityInfoODTO::getCommodityId, Function.identity()));
        /***
         * APP 我的订单列表实发金额显示问题：背景由于同一个订单多个仓库发货完成时间点不同，导致实发金额会变化 希望能整单完成拣货（出货）才能计算实发金额，才能生成送货单
         *
         * 查询订单拆单数量（不包括直送类型：t_sub_order表logistics_model=0，直送类型大仓不发送发货消息）及实际大仓发货数
         * 实际大仓发货数=根据t_sub_order表字段writeback_real_qty_flag该字段等于1 时从而知道子订单在大仓发货并发送发货消息：topic=PICK_UPDATE_SUB_ORDER_QUANTITY_TOPIC 来判断拆分的子单是否都发货
         */
        List<OrderRealDeliveryFinishEntry> deliveryFinishEntryList=subOrderMapper.findOrderRealDeliveryFinishList(new ArrayList<>(orderMap.keySet()));
        Map<Long,OrderRealDeliveryFinishEntry> deliveryFinishEntryMap = SpringUtil.isNotEmpty(deliveryFinishEntryList) ? deliveryFinishEntryList.stream().collect(
                Collectors.toMap(OrderRealDeliveryFinishEntry::getOrderId, Function.identity())) : new HashMap<>();
        odtos.forEach(commodity->{
        	PfOrder4AppODTO order = orderMap.get(commodity.getOrderId());
            OrderRealDeliveryFinishEntry deliveryFinishEntry=deliveryFinishEntryMap.get(commodity.getOrderId());
            /*** 订单拆单数量不等于实际发货数 实发金额赋值null*/
            if(deliveryFinishEntry!=null && deliveryFinishEntry.getSubNum().compareTo(deliveryFinishEntry.getRealFinishNum())!=0){
                order.setRealAmount(null);
            }
            if(order.getCommodities() == null){
                order.setCommodities(new ArrayList<>());
            }
            PfCommodityInfoODTO odto = pfCommodityInfoODTOMap.get(commodity.getCommodityId());
            if(odto == null) {return;}
            commodity.covert(odto);
            order.getCommodities().add(commodity);
        });
        return pfOrder4AppODTOS;
    }

	/**
	 * 订单详情
	 * @param orderCode
	 * @return
	 */
	public PfOrder4AppODTO queryOrderDetailByCode(String orderCode) {
		PfQueryOrder4AppParam param = new PfQueryOrder4AppParam();
        param.setOrderCode(orderCode);
        List<PfOrder4AppODTO> pfOrder4AppODTOS = orderMapper.queryOrder4PfApp(param);
        QYAssert.notEmpty(pfOrder4AppODTOS, "订单不存在");

        return covertCommodityInfo(pfOrder4AppODTOS).get(0);
	}

	/**
     * 订单复制
     * @param orderId 订单id
     * @return 是否成功
     */
    @Transactional(rollbackFor = Exception.class)
	public Integer copyOrderById4App(Long orderId) {
    	QYAssert.isTrue(null !=orderId , "orderId is not null !");
        PfTokenInfo tokenInfo = FastThreadLocalUtil.getPF();
        Long storeId =  tokenInfo.getStoreId();
        Long userId = tokenInfo.getUserId();
        String userName = tokenInfo.getUserName();
        List<OrderItemEntry> commList = this.orderMapper.queryOrderItem4Copy(orderId);
        QYAssert.isTrue(SpringUtil.isNotEmpty(commList), "该订单有误,无法复制!");
        pfShoppingCartService.clear(storeId, Collections.emptyList());

        Date now = new Date();
        List<PfShoppingCart> vos = new ArrayList<>();
        commList.forEach(item -> {
        	PfShoppingCart listVo = new PfShoppingCart();
            listVo.setStoreId(storeId);
            listVo.setCommodityId(item.getCommodityId());
            listVo.setQuantity(item.getCommodityNum());
            listVo.setCreateId(userId);
            listVo.setCreateName(userName);
            listVo.setCreateTime(now);
            listVo.setUpdateId(userId);
            listVo.setUpdateName(userName);
            listVo.setUpdateTime(now);
            vos.add(listVo);
        });
        return pfShoppingCartMapper.insertList(vos);
	}

    /**
     * 得到订单实发数量后，处理超发与短交
     * @param orderItemRealQuantityUpdatedMessage 子单实发数量回写消息
     */
    @Transactional
    public void handleOverIssueAndShotDelivery(SubOrderRealQtyWrittenBackMessage orderItemRealQuantityUpdatedMessage) {
        Long orderId = orderItemRealQuantityUpdatedMessage.getOrderId();
        log.info(String.format("开始处理订单[id=%s]超发与短交", orderId));
        Order order = orderMapper.selectByPrimaryKey(orderId);
        if (order == null) {
            log.error(String.format("处理批发订单超发与短交错误，未找到订单[id=%s]", orderId));
            return;
        }

        // 订单来源只允许批发APP或者PC端下单
        if (!(OrderTypeEnum.fromCode(order.getOrderType()) == OrderTypeEnum.PF_APP_ORDER
                || OrderTypeEnum.fromCode(order.getOrderType()) == OrderTypeEnum.PC_ORDER)) {
            log.info(String.format("订单[id=%s]不是批发订单，忽略超发与短交处理", orderId));
            return;
        }

        Long storeId = order.getStoreId();
        Store store = storeService.findStoreByStoreId(storeId);
        if (store == null) {
            log.error(String.format("处理批发订单[id=%s]超发与短交错误，未找到门店[storeId=%s]", orderId, storeId));
            return;
        }

        // 针对外部批发客户
        if (StoreTypeEnums.getById(store.getStoreTypeId()) != StoreTypeEnums.PFS
                || StoreTypeClassEnums.fromCode(store.getStoreType()) != StoreTypeClassEnums.EXTERNAL) {
            log.info(String.format("处理批发订单[id=%s]超发与短交，门店[storeId=%s]客户不是一个外部客户，忽略", orderId, storeId));
            return;
        }
        // 针对预付客户
        PrePayAccountVo acct = prePayAccountService.findByStoreId(storeId);
        if (acct == null) { // 找不到预付费账户，表示该客户非预付费
            log.info(String.format("处理批发订单[id=%s]超发与短交，门店[storeId=%s]客户未找到预付账户，忽略", orderId, storeId));
            return;
        }

        // 获取订单级别的锁
        String lockKey = LockConstants.generatePfOrderOverIssueAndShotDeliveryHandleLockKey(orderId);
        RLock lock = redissonClient.getLock(lockKey);
        try {
            // 这里使用了阻塞，而非tryLock，主要是因为没有考虑好如何在锁获取失败的情况下进行补偿
            // 在消息发送端，当确保相同order进同一个分区（相同订单串行处理），就可以保证这里不会有锁争用
            lock.lock();
            // 校验，订单是否已经处理过超发与短交
            boolean handled = checkIfHandledOverIssueAndShotDeliveryAlready(orderId);
            if (handled) {
                log.info(String.format("订单[id=%s]已执行过超发与短交处理计算，忽略", orderId));
                return;
            }

            if (!meetHandleOverIssueAndShotDeliveryCondition(orderItemRealQuantityUpdatedMessage, order)) { // 判断是否满足计算超发扣款条件
                log.info(String.format("处理批发订单[id=%s]超发与短交，当前订单不满足超发与短交处理条件（所有子单都回填了实发数量），忽略", orderId));
                return;
            }

            // 满足计算条件，开始计算
            // 计算是否超发或短交，以及相应的金额
            OverIssueAndShotDeliveryInfo info = buildOverIssueAndShotDeliveryInfo(orderItemRealQuantityUpdatedMessage, order);
            BigDecimal amount = info.getAmount();
            log.info(String.format("处理批发订单[id=%s]超发与短交，计算的金额=%s", orderId, amount));

            // 发生超发
            if (amount.compareTo(BigDecimal.ZERO) > 0) {
                log.info(String.format("处理批发订单[id=%s]超发，计算的金额=%s", orderId, amount));
                StoreDeductionBO deductionBO = StoreDeductionBO.builder()
                        .orderCode(order.getOrderCode())
                        .orderId(orderId)
                        .orderAmount(amount)
                        .storeId(storeId)
                        .tradeCode("PFC" + order.getOrderCode())
                        .tradeTime(new Date())
                        .orderTime(order.getOrderTime())
                        .billType(StoreBillTypeEnums.PF_OUTRUN_DEDUCTION.getCode())
                        .remark(info.getRemarks())
                        .userId(SystemUserConstant.SYSTEM_USER_ID)
                        .build();
                storeRechargeService.storeDeduction(deductionBO);
            }


            // 发生短交
            if (amount.compareTo(BigDecimal.ZERO) < 0) {
                log.info(String.format("处理批发订单[id=%s]短交，计算的金额=%s", orderId, amount));
                BigDecimal amountNegate = info.getAmount().negate();
                StoreRechargeBO rechargeBO = StoreRechargeBO.builder()
                        .orderCode(order.getOrderCode())
                        .tradeCode("DJ"+order.getOrderCode())
                        .money(amountNegate.doubleValue())
                        .storeId(storeId)
                        .tradeTime(new Date())
                        .receiptDate(order.getOrderTime())
                        .billType((StoreBillTypeEnums.PF_DISCREPANCY.getCode()))
                        .bankDate(order.getOrderTime())
                        .paymentMethod(PayMethodIdEnum.转账.getId())
                        .receiptType(ReceiptTypeIdEnum.货款.getCode())
                        .remark(info.getRemarks())
                        .build();
                storeRechargeService.storeRecharge(rechargeBO);
            }

            markOverIssueAndShotDeliveryHandled(order);
        } finally {
            if (lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
    }

    /**
     * 处理短交
     * @param info 短交信息
     */
    private void handleShotDelivery(OverIssueAndShotDeliveryInfo info) {
        BigDecimal amount = info.getAmount().negate();
        Order order = info.getOrder();
        Long storeId = order.getStoreId();

        // 执行扣款操作
        PrePaidAccountReqVo req = prePayAccountService.receive(storeId, amount, SystemUserConstant.SYSTEM_USER_ID, info.getRemarks(),
                PrePaidAccountReqRefBillTypeEnum.ORDER, order.getId(), order.getOrderTime());
        if (req.getStatus() == PrePaidAccountReqStatusEnum.RECORDED) { // 已入账
            // 记录order_bill
            OrderBill ob = new OrderBill();
            ob.setStoreId(storeId);
            ob.setOrderId(order.getId());
            ob.setOrderTime(order.getOrderTime());
            ob.setArAmount(BigDecimal.ZERO);
            ob.setPaAmount(req.getAmount());
            ob.setBillRemark(req.getRemarks());
            ob.setStoreBalance(req.getBalance());
            ob.setCreateTime(new Date());
            ob.setCrateId(req.getPromoterId());
            this.orderBillMapper.insertSelective(ob);
        }
    }

    /**
     * 处理超发
     * @param info 超发信息
     */
    private void handleOverIssue(OverIssueAndShotDeliveryInfo info) {
        BigDecimal amount = info.getAmount();
        Order order = info.getOrder();
        Long storeId = order.getStoreId();

        // 执行扣款操作
        PrePaidAccountReqVo req = prePayAccountService.pay(storeId, amount, SystemUserConstant.SYSTEM_USER_ID, info.getRemarks(),
                PrePaidAccountReqRefBillTypeEnum.ORDER, order.getId(), order.getOrderTime());
        if (req.getStatus() == PrePaidAccountReqStatusEnum.RECORDED) { // 已入账
            // 记录order_bill
            OrderBill ob = new OrderBill();
            ob.setStoreId(storeId);
            ob.setOrderId(order.getId());
            ob.setOrderTime(order.getOrderTime());
            ob.setArAmount(req.getAmount());
            ob.setPaAmount(BigDecimal.ZERO);
            ob.setBillRemark(req.getRemarks());
            ob.setStoreBalance(req.getBalance());
            ob.setCreateTime(new Date());
            ob.setCrateId(req.getPromoterId());
            this.orderBillMapper.insertSelective(ob);
        }
    }

    /**
	 * 超发/短交信息
	 * <AUTHOR>
	 *
	 */
	static class OverIssueAndShotDeliveryInfo {
		
		private Order order;

        public Order getOrder() {
            return order;
        }

        private List<OrderList> orderItems;

		public BigDecimal getAmount() {
			BigDecimal amount = BigDecimal.ZERO;
			
			for (OrderList item : orderItems) {
				BigDecimal price = item.getTotalPrice() != null ? item.getTotalPrice() : BigDecimal.ZERO;
				BigDecimal realPrice = item.getRealTotalPrice() != null ? item.getRealTotalPrice() : BigDecimal.ZERO;

                amount = amount.add(realPrice.subtract(price));
			}

			return amount;
		}
		
		public String getRemarks() {
            int compareResult = getAmount().compareTo(BigDecimal.ZERO);

            if (compareResult > 0) {
                return  String.format("<--批发超发：%s-->", order.getOrderCode());
            }

            if (compareResult < 0) {
                return  String.format("<--批发短交退款：%s-->", order.getOrderCode());
            }

			return "";
		}

		public void setOrder(Order order) {
			this.order = order;
		}

		public void setOrderItems(List<OrderList> items) {
			this.orderItems = items;
		}
		
	}
	
	/**
	 * 标记订单已做过超发扣款计算，防止重复扣款的出现
	 * @param order
	 */
	private void markOverIssueAndShotDeliveryHandled(Order order) {
		Long orderId = order.getId();
		log.info(String.format("标记订单[id=%s]已执行过超发/短交处理", orderId));
		PfOrder po = pfOrderMapper.selectByPrimaryKey(orderId);
		if (po != null) {
			po.setDeductOnOverissuanceCalConditional(true);
			int records = pfOrderMapper.updateByPrimaryKey(po);
			log.info(String.format("标记订单[id=%s]已执行过超发/短交处理(with update): orderId=%s", orderId, records>0));
			return;
		}
		po = new PfOrder();
		po.setId(orderId);
		po.setDeductOnOverissuanceCalConditional(true);
		pfOrderMapper.insert(po);
		log.info(String.format("标记订单已进行已执行过超发/短交处理(with insert): orderId=%s", orderId));
	}

	/**
	 * 判断是否满足计算超发扣款条件
	 * 
	 * 检查是否所有子单都已经回填了实发数量
	 * 
	 * @param orderItemRealQuantityUpdatedMessage
	 * @param order
	 * @return
	 */
	private boolean meetHandleOverIssueAndShotDeliveryCondition(
			SubOrderRealQtyWrittenBackMessage orderItemRealQuantityUpdatedMessage, Order order) {
		Long orderId = order.getId();
		Example exp = new Example(SubOrder.class);
		exp.createCriteria()
			.andEqualTo("orderId", orderId);
		// 遍历子单，检查回填标识
		List<SubOrder> subOrders = subOrderMapper.selectByExample(exp);
		for (SubOrder so : subOrders) {
			if (so.getWritebackRealQtyFlag() == null || !so.getWritebackRealQtyFlag()) { // 只要其中一个子单未回填，就标识当前订单不满足计算超发扣款条件
				return false;
			}
		}
		return true;
	}

	/**
	 * 构建超发/短交信息
	 * @param orderItemRealQuantityUpdatedMessage 订单实发消息
	 * @param order 订单
	 * @return 超发/短交信息
	 */
	private OverIssueAndShotDeliveryInfo buildOverIssueAndShotDeliveryInfo(
			SubOrderRealQtyWrittenBackMessage orderItemRealQuantityUpdatedMessage, Order order) {
		Long orderId = order.getId();
		Example exp = new Example(OrderList.class);
		exp.createCriteria()
				.andEqualTo("orderId", orderId);
		List<OrderList> items = orderListMapper.selectByExample(exp);
		OverIssueAndShotDeliveryInfo result = new OverIssueAndShotDeliveryInfo();
		result.setOrder(order);
		result.setOrderItems(items);
		return result;
	}

	/**
	 * 检查是否已经因超发进行过扣款
	 * @param orderId 订单id
	 * @return true 表示已扣款过
	 */
	private boolean checkIfHandledOverIssueAndShotDeliveryAlready(Long orderId) {
		PfOrder po = pfOrderMapper.selectByPrimaryKey(orderId);
		return po != null && po.getDeductOnOverissuanceCalConditional() != null && po.getDeductOnOverissuanceCalConditional();
	}

	public FileODTO queryOrderPdfFile(Long orderId) {
		QYAssert.isTrue(null != orderId, "orderId is not null !");
    	OrderFile orderFile = orderFileMapper.selectOne(new OrderFile(orderId));
    	if (null != orderFile) {
    		return new FileODTO(fileConfigProperties.getVisitUrl() + orderFile.getFileUrl(), orderFile.getFileSize());
    	}
    	
    	// 1、订单信息
    	Order order = orderMapper.selectByPrimaryKey(orderId);
    	QYAssert.isTrue(null != order, "该订单不存在!");
    	QYAssert.isTrue(OrderTypeEnum.PF_APP_ORDER.getCode().equals(order.getOrderType()), "该订单类型有误!");
    	QYAssert.isTrue(Integer.valueOf(15).equals(order.getProcessStatus()) || Integer.valueOf(19).equals(order.getProcessStatus()), "该订单尚未发货!");
        /***
         * APP 我的订单列表实发金额显示问题：背景由于同一个订单多个仓库发货完成时间点不同，导致实发金额会变化 希望能整单完成拣货（出货）才能计算实发金额，才能生成送货单
         *
         * 查询订单拆单数量（不包括直送类型：t_sub_order表logistics_model=0，直送类型大仓不发送发货消息）及实际大仓发货数
         * 实际大仓发货数=根据t_sub_order表字段writeback_real_qty_flag该字段等于1 时从而知道子订单在大仓发货并发送发货消息：topic=PICK_UPDATE_SUB_ORDER_QUANTITY_TOPIC 来判断拆分的子单是否都发货
         */
    	List<OrderRealDeliveryFinishEntry> deliveryFinishEntryList=subOrderMapper.findOrderRealDeliveryFinishList(Arrays.asList(orderId));
    	if(SpringUtil.isNotEmpty(deliveryFinishEntryList)) {
            OrderRealDeliveryFinishEntry entry = deliveryFinishEntryList.get(0);
            /*** 订单拆单数量不等于大仓实际发货数 不能生成送货单*/
            QYAssert.isTrue(entry.getSubNum().compareTo(entry.getRealFinishNum()) == 0, "大仓发货流程尚未完成，暂时无法生成送货单。如有疑问，请联系您的专属督导。");
        }
    	
    	PfOrderInfoODTO deliveryOrderInfo = new PfOrderInfoODTO(order);
    	Long companyId = order.getCompanyId();
    	if (null != companyId) {
    		Company company = companyMapper.selectByPrimaryKey(companyId);
    		if (null != company) {
    			deliveryOrderInfo.setCompanyName(company.getCompanyName());
    		}
    	}
    	
    	// 2、客户信息
    	Long storeId = order.getStoreId();
    	Store store = storeMapper.selectByPrimaryKey(storeId);
    	deliveryOrderInfo.setStoreInfo(store);
    	// 客户信息 - 线路组、操作员（对于鲜达来说肯定是客户）
    	deliveryOrderInfo.setStoreLineGroupName(storeMapper.selectStoreLineGroupName(storeId));
    	deliveryOrderInfo.setCreateName(store.getStoreName());
    	
    	// 3、订单镜像信息
    	OrderMirror orderMirror = new OrderMirror();
    	orderMirror.setOrderId(orderId);
    	orderMirror = orderMirrorMapper.selectOne(orderMirror);
    	deliveryOrderInfo.setOrderMirrorInfo(orderMirror);
    	// 订单镜像 - 设置职员信息
    	if (null != orderMirror) {
    		List<Long> employeeIdList = new ArrayList<>();
    		if (null != orderMirror.getDeliveryManId()) {
    			employeeIdList.add(orderMirror.getDeliveryManId());
    		}
    		if (null != orderMirror.getRegionalManagerId()) {
    			employeeIdList.add(orderMirror.getRegionalManagerId());
    		}
    		if (null != orderMirror.getSupervisionId()) {
    			employeeIdList.add(orderMirror.getSupervisionId());
    		}
    		if (SpringUtil.isNotEmpty(employeeIdList)) {
    			Example example = new Example(EmployeeUser.class);
    			example.createCriteria().andIn("employeeId", employeeIdList);
    	        example.selectProperties("employeeId", /*"employeeCode", "employeeName", */"employeePhone");
    			List<EmployeeUser> employeeUserList = employeeUserMapper.selectByExample(example);
    			if (SpringUtil.isNotEmpty(employeeUserList)) {
    				Map<Long, String> employeePhoneMap = employeeUserList.stream().filter(o -> {return !StringUtil.isNullOrEmpty(o.getEmployeePhone());}).collect(Collectors.toMap(EmployeeUser::getEmployeeId, EmployeeUser::getEmployeePhone));
    				deliveryOrderInfo.setRegionalManagerPhone(employeePhoneMap.get(deliveryOrderInfo.getRegionalManagerId()));
    				deliveryOrderInfo.setSupervisionPhone(employeePhoneMap.get(deliveryOrderInfo.getSupervisionId()));
    				deliveryOrderInfo.setDeliveryManPhone(employeePhoneMap.get(deliveryOrderInfo.getDeliveryManId()));
    			}
    		}
    	}
    	
    	// 4、订单项信息
    	deliveryOrderInfo.setOrderItemList(orderListGiftMapper.selectPfOrderItemList(orderId));
    	
    	// 5、生成文件 & 上传文件 & 删除本地文件
    	String fullFileName = pfOrderPdfA4Creator.createPdfFile(deliveryOrderInfo, OrderPdfA4Creator.DEFAULT_PAGE_SIZE);
    	
    	// TODO
//    	FileUploadResultVo fileUploadResult = null;
    	FileUploadRestSingleODTO fileUploadRestSingleODTO = null;
    	FileUploadResultODTO data = null;
    	try {
    		
    		File file = new File(fullFileName);
            InputStream inputStream = new FileInputStream(file);
            MultipartFile multipartFile = new MockMultipartFile("file", file.getName(), MediaType.MULTIPART_FORM_DATA_VALUE, inputStream);
            XsFileUploadClient cli = fileUploadClientFactory.getXsClient();
            fileUploadRestSingleODTO = cli.restUpload(multipartFile, "PF_DELIVERY_ORDER_FILE");
            log.info("xsFileUploadClient上传pdf-result ： {}", fileUploadRestSingleODTO);
            if (fileUploadRestSingleODTO == null) {
                QYAssert.isFalse("上传pdf异常!");
            }
            data = fileUploadRestSingleODTO.getData();
            if (data == null) {
                QYAssert.isFalse("上传pdf异常!");
            }
    		
//    		fileUploadResult = UploadFileUtil.postFile(fileConfigProperties.getServerUrl(), fullFileName);
		} catch (Exception e) {
			log.error("\n订单文件上传到资源库异常", e);
			QYAssert.isFalse("上传pdf异常!");
		} finally {
			UploadFileUtil.deleteLocalFile(fullFileName);
		}
//    	QYAssert.isTrue(null != fileUploadResult, "该订单文件上传到资源库失败!");
    	
    	
    	// 6、记录本地记录
    	orderFile = OrderFile.forInsert(orderId, data.getRelativeUrl(), data.getPicSpace());
		orderFileMapper.insert(orderFile);
		return new FileODTO(fileConfigProperties.getVisitUrl() + orderFile.getFileUrl(), orderFile.getFileSize());
	}

    @Autowired
    public void setStoreRechargeClient(StoreRechargeClient storeRechargeClient) {
        this.storeRechargeClient = storeRechargeClient;
    }
}
