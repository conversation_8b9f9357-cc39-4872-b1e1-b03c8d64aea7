package com.pinshang.qingyun.order.service.auto;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.pinshang.qingyun.base.api.TokenInfo;
import com.pinshang.qingyun.base.configure.expand.QYAssert;
import com.pinshang.qingyun.base.enums.IogisticsModelEnums;
import com.pinshang.qingyun.base.util.FastThreadLocalUtil;
import com.pinshang.qingyun.box.utils.StringUtil;
import com.pinshang.qingyun.order.dto.AutoCommodityIDTO;
import com.pinshang.qingyun.order.dto.AutoCommodityODTO;
import com.pinshang.qingyun.order.dto.XDCommodityODTO;
import com.pinshang.qingyun.order.enums.AutoCommodityOperationTypeEnums;
import com.pinshang.qingyun.order.mapper.auto.AutoCommodityMapper;
import com.pinshang.qingyun.order.mapper.CommodityMapper;
import com.pinshang.qingyun.order.model.auto.AutoCommodity;
import com.pinshang.qingyun.order.service.SendLogService;
import com.pinshang.qingyun.order.vo.auto.AutoCommodityLog;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;

import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Author: liuZhen
 * @DateTime: 2022/5/11 13:35
 */
@Service
@Slf4j
public class AutoCommodityOrderService {
    @Autowired
    private AutoCommodityMapper autoCommodityMapper;
    @Autowired
    private CommodityMapper commodityMapper;
    @Autowired
    private SendLogService sendLogService;
    @Autowired
    private AutoSettingService autoSettingService;

    //导入
    public List<String> commodityIdsExcelImport(Workbook wb) {
        QYAssert.notNull(wb, "模板不正确");
        Sheet sheet = wb.getSheetAt(0);
        QYAssert.notNull(sheet, "模板不正确");

        //错误提示信息
        List<String> warnMsg = new ArrayList<>();

        List<String> commodityCodes = validateImportExcel(warnMsg, sheet);

        //数量大于5000
        QYAssert.isTrue(commodityCodes.size() <= 5000, "导入商品数量大于5000!");
        QYAssert.isTrue(commodityCodes.size() > 0, "导入商品数量为0!");
        TokenInfo tokenInfo = FastThreadLocalUtil.getQY();
        List<String> ret = new ArrayList<>();

        //重复数据
        List<String> distinctCommodityCodes = commodityCodes.stream().distinct().collect(Collectors.toList());
        if (distinctCommodityCodes.size() < commodityCodes.size()) {
            //存在重复数据
            Map<String, Long> mapTemp = commodityCodes.stream().collect(Collectors.groupingBy(String::toString, Collectors.counting()));
            List<String> list1 = mapTemp.entrySet().stream().filter(m -> m.getValue() > 1).map(m -> m.getKey()).collect(Collectors.toList());
            list1.forEach(i -> {
                ret.add(i + "商品编码,excel中有重复数据");
            });

        }
        List<XDCommodityODTO> xdCommodityODTOS = commodityMapper.findCommodityInfoByCodes(distinctCommodityCodes);
        List<String> databaseCommodityCodes = xdCommodityODTOS.stream().map(commodityInfoEntry -> commodityInfoEntry.getCommodityCode()).collect(Collectors.toList());
        List<String> noCommodityInfoList = distinctCommodityCodes.stream().filter(item -> !databaseCommodityCodes.contains(item)).collect(Collectors.toList());
        if (!noCommodityInfoList.isEmpty()) {
            noCommodityInfoList.forEach(i -> {
                ret.add(i + "商品编码，没有匹配到商品");
            });
        }
        xdCommodityODTOS.forEach(i -> {
            if (i.getLogisticsModel()==null){
                ret.add(i.getCommodityCode()+"商品编码,未找到物流模式");
                i.setLogisticsModel(IogisticsModelEnums.DIRECT_SENDING.getCode());
            }else if (IogisticsModelEnums.DIRECT_SENDING.getCode()==i.getLogisticsModel()) {
                ret.add(i.getCommodityCode() + "商品编码，自动订货不支持直送商品");
            }
        });
        List<XDCommodityODTO> addCommodityList = xdCommodityODTOS.stream().filter(item -> IogisticsModelEnums.DIRECT_SENDING.getCode()!=item.getLogisticsModel()).collect(Collectors.toList());
        List<String> addCommodityIds = addCommodityList.stream().map(xdCommodityODTO -> xdCommodityODTO.getCommodityId()).collect(Collectors.toList());
        //已经在池子里的商品，跳过
        if (addCommodityIds.size() > 0) {
            List<String> existCommodityIds = autoCommodityMapper.queryCommodityIds(addCommodityIds);
            addCommodityIds.removeAll(existCommodityIds);
            addCommodityList =addCommodityList.stream().filter(i-> !existCommodityIds.contains(i.getCommodityId())).collect(Collectors.toList());
        }
        if (addCommodityIds.size() > 0) {
            autoCommodityMapper.saveList(addCommodityIds, tokenInfo.getUserId().toString());
            recordCommodityLog(addCommodityList, AutoCommodityOperationTypeEnums.ADD.getCode(),Boolean.FALSE);
        }
        if (ret.size() > 0) {
            ret.add(0, "正确数据已导入;如下数据有误,请检查:");
        } else {
            ret.add(0, "正确商品已导入");
        }
        updateSetting();
        return ret;
    }

    //查询
    public PageInfo<AutoCommodityODTO> queryAutoCommodity(AutoCommodityIDTO idto) {
        PageInfo<AutoCommodityODTO> pageInfo = PageHelper.startPage(idto.getPageNo(), idto.getPageSize()).doSelectPageInfo(
                () -> autoCommodityMapper.queryCommodityListByIdto(idto));
        return pageInfo;
    }

    public Boolean deleteByCommodityId(String commodityId) {
        Example example = new Example(AutoCommodity.class);
        example.createCriteria().andEqualTo("commodityId", commodityId);
        autoCommodityMapper.deleteByExample(example);
        XDCommodityODTO xdCommodityODTO = commodityMapper.getXDCommodityODTOById(Long.parseLong(commodityId));
        List<XDCommodityODTO> xdCommodityODTOS = new ArrayList<>();
        xdCommodityODTOS.add(xdCommodityODTO);
        recordCommodityLog(xdCommodityODTOS, AutoCommodityOperationTypeEnums.DEL.getCode(),Boolean.FALSE);
        updateSetting();
        return true;
    }


    private void updateSetting() {
        Example example = new Example(AutoCommodity.class);
        example.createCriteria().andIsNotNull("commodityId");
        Integer count = autoCommodityMapper.selectCountByExample(example);
        autoSettingService.updateHeadItems(count);
    }

    public void recordCommodityLog(List<XDCommodityODTO> list, Integer type,Boolean isSystem) {
        TokenInfo tokenInfo = FastThreadLocalUtil.getQY();
        if (isSystem){
            tokenInfo = new TokenInfo();
            tokenInfo.setRealName("系统");
            tokenInfo.setUserId(1L);
        }
        List<AutoCommodityLog> logs = new ArrayList<>();
        for (XDCommodityODTO addCommodity : list) {
            AutoCommodityLog autoCommodityLog = new AutoCommodityLog();
            autoCommodityLog.setCommodityId(Long.parseLong(addCommodity.getCommodityId()));
            autoCommodityLog.setCommodityCode(addCommodity.getCommodityCode());
            autoCommodityLog.setCommodityName(addCommodity.getCommodityName());
            autoCommodityLog.setCommoditySpec(addCommodity.getCommoditySpec());
            autoCommodityLog.setOperationType(type);
            autoCommodityLog.setCreateId(tokenInfo.getUserId());
            autoCommodityLog.setCreateName(tokenInfo.getRealName());
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            autoCommodityLog.setCreateTime(sdf.format(new Date()));
            logs.add(autoCommodityLog);
            if (logs.size()==50){
                sendLogService.sendLog(logs, "t_log_auto_commodity");
                logs.clear();
            }
        }
        if (logs.size()>0){
            sendLogService.sendLog(logs, "t_log_auto_commodity");
        }
    }

    private List<String> validateImportExcel(List<String> warnMsg, Sheet sheet) {
        List<String> res = new ArrayList<>();
        for (Row row : sheet) {
            int rowNum = row.getRowNum();
            if (rowNum == 0) {
                QYAssert.isTrue(checkRowNumZero(row, 0, "商品编码"), "模板不正确");
            }
            if (rowNum > 0) {
                Cell c0 = row.getCell(0);
                if (null == c0 ||
                        (c0 != null && (c0.getCellType().name().equals("STRING") || c0.getCellType().name().equals("BLANK")) && StringUtil.isBlank(c0.getStringCellValue().trim()))) {
                    warnMsg.add("第" + (rowNum + 1) + "行不能为空");
                    continue;
                }

                if (c0 != null && !c0.getCellType().name().equals("STRING")) {
                    warnMsg.add("第" + (rowNum + 1) + "行文本格式不对");
                    continue;
                }
                res.add(c0.getStringCellValue().trim());
            }
        }
        return res;
    }

    /**
     * 判断模板格式：根据Excel表头来判断
     *
     * @param row
     * @param index
     * @param cellName
     * @return
     */
    private boolean checkRowNumZero(Row row, int index, String cellName) {
        boolean result = true;
        if (row.getCell(index) == null || !cellName.equals(row.getCell(index).getStringCellValue())) {
            result = false;
        }
        return result;
    }

    private String isText(Cell c) {
        if (null == c) {
            return "";
        }
        String value = c.getStringCellValue().trim();
        return value == null ? "" : value;
    }

}
