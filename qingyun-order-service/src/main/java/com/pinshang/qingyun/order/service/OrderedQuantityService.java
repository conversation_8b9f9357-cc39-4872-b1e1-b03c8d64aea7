package com.pinshang.qingyun.order.service;

import com.pinshang.qingyun.base.enums.OperateTypeEnums;
import com.pinshang.qingyun.base.enums.order.OrderTypeEnum;
import com.pinshang.qingyun.box.utils.BeanCloneUtils;
import com.pinshang.qingyun.order.dto.ShopOrderedQuantityODTO;
import com.pinshang.qingyun.order.mapper.OrderMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @Author: sk
 * @Date: 2023/3/27
 */
@Slf4j
@Service
public class OrderedQuantityService {

    @Autowired
    private OrderMapper orderMapper;

    /**
     * 统计在途数量
     * @param orderTime
     * @param shopId
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public Boolean countShopOrderedQuantity(String orderTime, Long shopId) {

        // 先删除
        orderMapper.deleteShopOrderedQuantity(orderTime, shopId);

        List<Integer> filterOrderTypeList = Arrays.asList(OrderTypeEnum.GROUP_AUTO_ORDER.getCode(),
                OrderTypeEnum.DIRECT_REPLENISH_ORDER.getCode(),
                OrderTypeEnum.DIRECT_AUDIT_ORDER.getCode(),
                OrderTypeEnum.TAKE_AUTO_ORDER.getCode());
        // 新增
        orderMapper.insertShopOrderedQuantity(orderTime, shopId, filterOrderTypeList);

        return Boolean.TRUE;
    }

    /**
     * 创建取消订单维护t_md_shop_ordered_quantity，在途数量
     * 排除OrderType.GROUP_AUTO_ORDER(团购自动下单)、DIRECT_REPLENISH_ORDER(直送补单)、DIRECT_AUDIT_ORDER(直送审核通过)、TAKE_AUTO_ORDER(提货卡自动下单)
     * com.pinshang.qingyun.order.enums.OrderType
     * @return
     */
    @Transactional(rollbackFor = Exception.class,propagation = Propagation.REQUIRES_NEW)
    public Boolean insertOrUpdateOrderedQuantity(List<ShopOrderedQuantityODTO> orderList, Integer operateType){
        ShopOrderedQuantityODTO order = orderList.get(0);
        List<Long> commodityIdList = orderList.stream().map(item -> item.getCommodityId()).collect(Collectors.toList());

        // 查询已经保存的在途数量
        List<ShopOrderedQuantityODTO> shopOrderedQuantityList = orderMapper.queryShopOrderedQuantityList(order.getOrderTime(), order.getShopId(), commodityIdList);
        Map<String, List<ShopOrderedQuantityODTO>> orderedQuantityMap = shopOrderedQuantityList.stream().collect(
                Collectors.groupingBy(
                        item -> item.getOrderTime() + '-' + item.getShopId() + '-'+ item.getCommodityId()
                ));

        List<ShopOrderedQuantityODTO> insertList = new ArrayList<>();
        List<ShopOrderedQuantityODTO> updateList = new ArrayList<>();
        for(ShopOrderedQuantityODTO orderDto : orderList){
            String key = orderDto.getOrderTime() + '-' + orderDto.getShopId() + '-'+ orderDto.getCommodityId();
            if(orderedQuantityMap != null && orderedQuantityMap.get(key) != null){
                List<ShopOrderedQuantityODTO> orderedList = orderedQuantityMap.get(key);
                ShopOrderedQuantityODTO ordered = orderedList.get(0);
                if(OperateTypeEnums.新增.getCode().equals(operateType)){
                    ordered.setQuantity(ordered.getQuantity().add(orderDto.getQuantity()));
                }else {
                    ordered.setQuantity(ordered.getQuantity().subtract(orderDto.getQuantity()));
                }
                // 如果数量小于0，则设置0
                ordered.setQuantity(ordered.getQuantity().compareTo(BigDecimal.ZERO) < 0 ? BigDecimal.ZERO : ordered.getQuantity());
                updateList.add(ordered);
            }else {
                if(OperateTypeEnums.新增.getCode().equals(operateType)){
                    ShopOrderedQuantityODTO insert = BeanCloneUtils.copyTo(orderDto, ShopOrderedQuantityODTO.class);
                    insertList.add(insert);
                }
            }
        }

        if(CollectionUtils.isNotEmpty(insertList)){
            orderMapper.batchInsertShopOrderQuantity(insertList);
        }

        if(CollectionUtils.isNotEmpty(updateList)){
            orderMapper.batchUpdateShopOrderQuantity(updateList);
        }
        return Boolean.TRUE;
    }
}
