package com.pinshang.qingyun.order.service.xda.util;

import com.pinshang.qingyun.base.configure.expand.QYAssert;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 23/6/14/014 14:49
 */
public class ShoppingCartUtil {
    /**
     * 购物车可添加最大数量
     */
    public static BigDecimal ShoppingCart_Max_Value = BigDecimal.valueOf(9999.99);

    /**
     * 根据销售箱规成倍添加购物车商品
     * @param quantity
     * @param salesBoxCapacity
     * @return
     */
    public static BigDecimal checkSalesBoxCapacityUp(BigDecimal quantity, BigDecimal salesBoxCapacity){
        BigDecimal decimal = quantity.divide(salesBoxCapacity,2,BigDecimal.ROUND_UP).setScale(0, BigDecimal.ROUND_DOWN);
        return decimal.multiply(salesBoxCapacity);
    }
    public static BigDecimal checkSalesBoxCapacityDown(BigDecimal quantity,BigDecimal salesBoxCapacity){
        BigDecimal decimal = quantity.divide(salesBoxCapacity,2,BigDecimal.ROUND_UP).setScale(0, BigDecimal.ROUND_DOWN);
        return decimal.multiply(salesBoxCapacity);
    }

    /**
     * 检验购物车商品数量
     * @param quantity
     */
    public static void checkQuantity(BigDecimal quantity){
        if(quantity != null){
            QYAssert.isTrue(quantity.compareTo(BigDecimal.ZERO) > 0 && quantity.compareTo(ShoppingCart_Max_Value) <= 0
                    , "设置数量超量异常!");
        }
    }
}
