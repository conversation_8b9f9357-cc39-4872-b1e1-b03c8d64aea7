package com.pinshang.qingyun.order.config;

import com.pinshang.qingyun.base.api.QYApplicationContext;
import org.springframework.stereotype.Component;


@Component
public class LockConstants {
    public static String systemProfile = "xda:";
    /**
     * 下单锁 formatter
     */
    public static String OrderCommitLockFormatter = "XDA_ORDER_COMMIT:%s";
    /**
     * 添加购物车锁
     */
    public static String ShoppingCartAddLockFormatter = "XDA_SC_ADD:%s";

    /**
     * 订单状态操作锁
     */
    public static String OrderStatusOperateLockFormatter = "XDA_ORDER_STATUS_OPERATE_LOCK:%s";

    public static String OrderCallbackLockFormatter = ":ORDER_CALLBACK_LOCK:%s";

    public static String batchCreateOrderAdmin = "batch_create_order_admin";

    public static String createOrder = "CREATE_ORDER_USER_ID";

    public static String getCToBQRCode = "ORDER:getCToBQRCode";

    public static String cancelCupOrder = "CANCEL_ORDER_ID";
    /**
     * 批发订单，超发扣款计算
     */
    public static final String PF_ORDER_DEDUCTION_ON_OVERISSUANC = "PF_ORDER_DEDUCTION_ON_OVERISSUANC";

    /**
     * 批发订单，超发与短交处理
     */
    public static final String PF_ORDER_OVER_ISSUE_AND_SHOT_DELIVERY_HANDLE = "PF_ORDER_OVER_ISSUE_AND_SHOT_DELIVERY_HANDLE";

    /**
     * 预付费账户
     */
    public static final String PRE_PAID_ACCOUNT = "PRE_PAID_ACCOUNT";
    
    /**
     * 批发，商品库存锁定
     */
    public static final String PF_COMMODITY_INVENTORY = "PF_COMMODITY_INVENTORY";
    /**
     * 鲜食加盟：日结
     */
    public static final String XSJM_DAILY_SETTLE = "XSJM_DAILY_SETTLE";
    /**
     * 订单改价
     */
    public static final String UPDATE_ORDER_PRICE = "UPDATE_ORDER_PRICE";
    /**
     * 鲜达生成单号
     */
    public static final String XDA_PAY_BILL_GENERATOR = "XDA_PAY_BILL_GENERATOR";

    /**
     * 鲜达查询PDF文件
     */
    public static String XDA_ORDER_QUERY_PDF_LOCK = "XDA_ORDER_QUERY_PDF_LOCK:%s";
    /**
     * 订单复制锁
     */
    public static String xdaCopyOrderLockFormatter = "XDA_COPY_ORDER_LOCK:%s";

    /**
     * 查询订单支付状态
     */
    public static String XDA_QUERY_BILL_STATUS_STATUS = "XDA_QUERY_BILL_STATUS_STATUS_LOCK:%s";

    /**
     * 鲜达投诉少货、退款
     */
    public static final String XDA_COMPLAINT_LOCK = "XDA_COMPLAINT_LOCK";

    public static String generateOrderCommitLock (Long uid){
        return systemProfile + QYApplicationContext.redisKeyProfile + ":" + OrderCommitLockFormatter + uid.toString();
    }

    public static String generateShoppingCartAddLockKey(Long uid){
        return systemProfile + QYApplicationContext.redisKeyProfile + ":" + ShoppingCartAddLockFormatter + uid.toString();
    }
    public static String generateOrderStatusLockKey(Long orderId){
        return systemProfile + QYApplicationContext.redisKeyProfile + ":" + OrderStatusOperateLockFormatter + orderId.toString();
    }
    public static String generateCallbackLockKey(String orderCode){
        return systemProfile + QYApplicationContext.redisKeyProfile + ":" + OrderCallbackLockFormatter + orderCode;
    }

	public static String generatePfOrderDeductionOnOverissuanceLockKey(Long orderId) {
		return systemProfile + QYApplicationContext.redisKeyProfile + ":" + PF_ORDER_DEDUCTION_ON_OVERISSUANC + orderId;
	}

	public static String generatePrePaidAccountLock(Long storeId) {
		return systemProfile + QYApplicationContext.redisKeyProfile + ":" + PRE_PAID_ACCOUNT + storeId;
	}

	public static String generatePfCommodityInventory(Long commodityId) {
		return systemProfile + QYApplicationContext.redisKeyProfile + ":" + PF_COMMODITY_INVENTORY + commodityId;
	}


    public static String generatePfOrderOverIssueAndShotDeliveryHandleLockKey(Long orderId) {
        return systemProfile + QYApplicationContext.redisKeyProfile + ":" + PF_ORDER_OVER_ISSUE_AND_SHOT_DELIVERY_HANDLE + orderId;
    }

    public static String generateXsjmDailySettleLockKey(Long shopId) {
        return QYApplicationContext.redisKeyProfile + XSJM_DAILY_SETTLE + ":" + shopId;
    }

    public static String generateXdaComplaintLockKey(Long storeId) {
        return QYApplicationContext.redisKeyProfile + XDA_COMPLAINT_LOCK + ":" + storeId;
    }
    public static String generateUpdateOrderPriceLockKey(Long productPriceModelId) {
        return systemProfile + QYApplicationContext.redisKeyProfile + ":" + UPDATE_ORDER_PRICE + productPriceModelId;
    }

    public static String generateXdaPayBillLockKey() {
        return systemProfile + QYApplicationContext.redisKeyProfile + ":" + XDA_PAY_BILL_GENERATOR;
    }

    /**
     * 鲜达查询PDF文件
     * @param orderId
     * @return
     */
    public static String generateXdaQueryPdfLockKey(Long orderId) {
        return systemProfile + QYApplicationContext.redisKeyProfile + ":" + XDA_ORDER_QUERY_PDF_LOCK + orderId;
    }

    public static String generateXdaCopyOrderLockKey(Long orderId){
        return systemProfile + QYApplicationContext.redisKeyProfile + ":" + xdaCopyOrderLockFormatter + orderId.toString();
    }

    public static String generateXdaQueryBillStatusLockKey(String billCode){
        return systemProfile + QYApplicationContext.redisKeyProfile + ":" + XDA_QUERY_BILL_STATUS_STATUS + billCode;
    }

}
