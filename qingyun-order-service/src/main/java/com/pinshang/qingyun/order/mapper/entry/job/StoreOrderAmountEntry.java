package com.pinshang.qingyun.order.mapper.entry.job;

import com.pinshang.qingyun.renderer.annotation.FieldRender;
import com.pinshang.qingyun.renderer.constant.RenderFieldHelper;
import com.pinshang.qingyun.renderer.enums.FieldTypeEnum;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class StoreOrderAmountEntry {

    private Long storeId;

    @FieldRender(fieldType = FieldTypeEnum.STORE,fieldName = RenderFieldHelper.Store.storeName,keyName = "storeId")
    private String storeName;

    private BigDecimal orderAmount;

    private BigDecimal freightAmount;
}
