package com.pinshang.qingyun.order.service.pay.prepaidacct;

import java.util.List;

import com.pinshang.qingyun.order.vo.prepayacct.PrePaidAccountReqVo;

/**
 * 预付账户异步批量入账（成功/失败）后，回调
 * 注: 如果不做特殊处理，改回调会被以事务的形式与账户入账一起提交/回滚
 * <AUTHOR>
 *
 */
public interface PrePaidAccountBatchAsyncRecordListener {

	void onBatchRecordSuccess(Long storeId, List<PrePaidAccountReqVo> reqs);

	void onBatchRecordFailed(Long storeId, List<PrePaidAccountReqVo> reqs);

}
