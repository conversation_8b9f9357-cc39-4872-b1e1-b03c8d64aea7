package com.pinshang.qingyun.order.mapper.entry.order;

import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @Date 2024/2/3 12:10
 */
@Data
public class SubOrderItemCommodityPriceEntry {

    private Long id;

    private Long orderId;

    private Long commodityId;

    private BigDecimal price;

    private BigDecimal quantity;

    private BigDecimal totalPrice;

    private BigDecimal sumTotalPrice;

    private Long subOrderId;
}
