package com.pinshang.qingyun.order.service.xda.v4;

import com.alibaba.fastjson2.JSON;
import com.pinshang.qingyun.base.configure.expand.BizLogicException;
import com.pinshang.qingyun.base.configure.expand.QYAssert;
import com.pinshang.qingyun.base.constant.XdaAppVersionConstant;
import com.pinshang.qingyun.base.enums.OperateTypeEnums;
import com.pinshang.qingyun.base.enums.YesOrNoEnums;
import com.pinshang.qingyun.base.enums.xda.XdaOrderProcessStatusEunm;
import com.pinshang.qingyun.base.enums.xda.XdaStoreTypeEnum;
import com.pinshang.qingyun.box.utils.BeanCloneUtils;
import com.pinshang.qingyun.box.utils.SpringUtil;
import com.pinshang.qingyun.common.dto.DelayMsgIDTO;
import com.pinshang.qingyun.common.service.DelayMsgClient;
import com.pinshang.qingyun.kafka.enums.KafkaMessageOperationTypeEnum;
import com.pinshang.qingyun.order.dto.shopcart.v4.CommodityPromotionV4ODTO;
import com.pinshang.qingyun.order.dto.shopcart.v4.ShoppingCartGroupV4ODTO;
import com.pinshang.qingyun.order.dto.shopcart.v4.ShoppingCartV4ODTO;
import com.pinshang.qingyun.order.dto.xda.TdaOrderSyncTmsIDTO;
import com.pinshang.qingyun.order.dto.xda.v4.OrderCancelV4IDTO;
import com.pinshang.qingyun.order.dto.xda.v4.XdaCreatePrePayOrderV4IDTO;
import com.pinshang.qingyun.order.dto.xda.v4.XdaSaveOrderODTO;
import com.pinshang.qingyun.order.enums.XdaPayTypeEnum;
import com.pinshang.qingyun.order.enums.XdaPreOrderPayStatusEnums;
import com.pinshang.qingyun.order.mapper.*;
import com.pinshang.qingyun.order.mapper.entry.recharge.XdaPayBillEntry;
import com.pinshang.qingyun.order.model.order.*;
import com.pinshang.qingyun.order.service.*;
import com.pinshang.qingyun.order.service.order.AppOrderLogService;
import com.pinshang.qingyun.order.service.pay.callback.ThirdPartyPayCallbackEvent;
import com.pinshang.qingyun.order.service.recharge.RechargeService;
import com.pinshang.qingyun.order.service.xda.CouponDayStatisticsService;
import com.pinshang.qingyun.order.service.xda.OrderLimitQuantityService;
import com.pinshang.qingyun.order.service.xda.util.XdaShoppingCartToolUtil;
import com.pinshang.qingyun.order.vo.splitOrder.SplitOrderKafkaVo;
import com.pinshang.qingyun.store.dto.xda.QueryXdaUserAccountDTO;
import com.pinshang.qingyun.store.dto.xda.XdaUserAccountDTO;
import com.pinshang.qingyun.store.service.StoreCompanyClient;
import com.pinshang.qingyun.store.service.XdaStoreUserClient;
import com.pinshang.qinyun.cache.enums.RedisDelayQueueEnum;
import feign.codec.DecodeException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionSynchronization;
import org.springframework.transaction.support.TransactionSynchronizationAdapter;
import org.springframework.transaction.support.TransactionSynchronizationManager;
import tk.mybatis.mapper.entity.Example;

import java.math.BigDecimal;
import java.util.*;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * @Author: sk
 * @Date: 2024/5/8
 */
@Slf4j
@Service
public class XdaPreOrderService {

    @Autowired
    private StoreCompanyClient storeCompanyClient;
    @Autowired
    private XdaSpecialPriceLimitService xdaSpecialPriceLimitService;
    @Lazy
    @Autowired
    private XdaOrderV4Service  xdaOrderV4Service;
    @Autowired
    private XdaShoppingCartToolUtil xdaShoppingCartToolUtil;
    @Autowired
    private ToBService toBService;
    @Autowired
    private XdaShoppingCartV4Service xdaShoppingCartV4Service;
    @Autowired
    private OrderMapper orderMapper;
    @Autowired
    private StoreMapper storeMapper;
    @Autowired
    private XdaPreOrderMapper xdaPreOrderMapper;
    @Autowired
    private XdaPreOrderPromotionMapper xdaPreOrderPromotionMapper;
    @Autowired
    private XdaPreOrderItemMapper xdaPreOrderItemMapper;
    @Autowired
    private OrderListMapper orderListMapper;
    @Autowired
    private OrderListGiftMapper orderListGiftMapper;
    @Autowired
    private OrderSalesPromotionMapper orderSalesPromotionMapper;
    @Autowired
    private OrderService orderService;
    @Autowired
    private OrderHistoryService orderHistoryService;
    @Autowired
    private SplitOrderService splitOrderService;
    @Autowired
    private OrderAsyncKafkaService orderAsyncKafkaService;
    @Autowired
    private XfOrderMapper xfOrderMapper;
    @Autowired
    private XfOrderService xfOrderService;
    @Autowired
    private XdaStoreUserClient xdaStoreUserClient;
    @Autowired
    private XdaPayBillMapper xdaPayBillMapper;
    @Autowired
    private RechargeService rechargeService;
    @Autowired
    private WeChatSendMessageService weChatSendMessageService;
    @Autowired
    private DictionaryService dictionaryService;
    @Autowired
    private DelayMsgClient delayMsgClient;
    @Autowired
    private TdaOrderService tdaOrderService;
    @Autowired
    private OrderLimitQuantityService orderLimitQuantityService;
    @Autowired
    private OrderMtCouponService mtCouponService;
    @Autowired
    private CouponDayStatisticsService couponDayStatisticsService;
    @Autowired
    private SplitOrderSendKfkService splitOrderSendKfkService;
    @Autowired
    private XdaSaveOrderCheckService xdaSaveOrderCheckService;
    @Autowired
    private AppOrderLogService appOrderLogService;

    /**
     * 鲜达保存预订单
     * @param xdaCreatePrePayOrderV4IDTO
     */
    public XdaSaveOrderODTO saveXdaPreOrder(XdaCreatePrePayOrderV4IDTO xdaCreatePrePayOrderV4IDTO, String billCode, String appVersion) {

        XdaSaveOrderODTO xdaSaveOrderODTO = new XdaSaveOrderODTO();
        Long storeId = xdaCreatePrePayOrderV4IDTO.getStoreId();
        Long orderId = null;
        String orderCode = null;
        Long couponUserId = null;
        try {
            xdaCreatePrePayOrderV4IDTO.setIsPayPassword(false);
            xdaSaveOrderODTO.setIsSuccess(Boolean.TRUE);
            Date orderTime = xdaCreatePrePayOrderV4IDTO.getOrderTime();

            //1.获取购物车数据
            ShoppingCartV4ODTO shoppingCartV4ODTO = xdaOrderV4Service.getShoppingCart(storeId, orderTime, xdaCreatePrePayOrderV4IDTO.getDeliverytimerange(), xdaCreatePrePayOrderV4IDTO.getDeliverybatch(), xdaCreatePrePayOrderV4IDTO.getCouponUserId());

            // 2.保存订单统一校验,并保存订单
            Order order = xdaSaveOrderCheckService.xdaCreateOrderCheckAndSaveOrder(xdaCreatePrePayOrderV4IDTO, true, false, shoppingCartV4ODTO);
            orderId = order.getId();
            orderCode = order.getOrderCode();
            xdaSaveOrderODTO.setOrderId(order.getId());
            if(StringUtils.isNotBlank(order.getErrorMsg())) {
                // 调用解冻库存
                toBService.warehouseUnfreezeInventory(orderId, xdaCreatePrePayOrderV4IDTO.getStoreId());
                xdaSaveOrderODTO.setIsSuccess(Boolean.FALSE);
                xdaSaveOrderODTO.setErrorMsg(order.getErrorMsg());
                return xdaSaveOrderODTO;
            }

            // 3.保存预订单
            saveXdaPreOrder(order, billCode);

            // 4.保存预订单促销方案
            saveXdaPreOrderPromotion(shoppingCartV4ODTO,order.getId());

            // 5.优惠券核销
            couponUserId = shoppingCartV4ODTO.getCouponUserId();
            mtCouponService.useCoupon(couponUserId, order.getStoreId(), order.getOrderCode());

            // 6.清空购物车
            xdaShoppingCartV4Service.clearV4(storeId,null);
            log.info("购物车已清空 -- storeId {}", storeId);

            // 7 记录设备号
            appOrderLogService.saveAppOrderLog(orderId, order.getStoreId(), order.getOrderTime());

            Long finalOrderId = orderId;
            String finalOrderCode = orderCode;
            TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronizationAdapter() {
                @Override
                public void afterCommit() {

                    // 加入延迟队列，超过时间未支付自动取消
                    // 添加逻辑，老版本预订单3分钟未支付就自动取消。如果回调过来，则这时候订单已经取消了。需要手动给用户退款
                    // 回调方法 com.pinshang.qingyun.order.listener.PayKafkaListener.onPayChangeMessage
                    saveXdaPreOrderAddDelayMsg(appVersion, finalOrderCode);

                    // 维护B端特价限购记录表
                    orderLimitQuantityService.saveOrderLimitQuantity(finalOrderId, OperateTypeEnums.新增.getCode(), true);

                }
            });
        }catch (Exception exception){
            //冻结成功，如果出现异常，也能获取到orderId，用于解冻
            log.warn("订单创建异常调用解冻 orderId = {}", orderId);
            toBService.warehouseUnfreezeInventory(orderId, xdaCreatePrePayOrderV4IDTO.getStoreId());
            log.error("订单创建异常解冻成功 exception = {}",exception);
            try {
                //返还券
                mtCouponService.refundCoupon(orderCode, couponUserId, storeId);
            }catch (Exception e){
                log.warn("鲜达保存预订单失败,返还券失败 orderCode  {} couponUserId {}", orderCode, couponUserId, e);
            }

            log.error("预订单创建失败",exception);
            if(exception instanceof BizLogicException || exception instanceof DecodeException){
                throw exception;
            }else {
                throw new BizLogicException("订单创建失败");
            }

        }
        return xdaSaveOrderODTO;
    }

    /**
     *  加入延迟队列，超过时间未支付自动取消
     *  添加逻辑，老版本预订单3分钟未支付就自动取消。如果回调过来，则这时候订单已经取消了。需要手动给用户退款
     * @param appVersion
     * @param orderCode
     */
    private void saveXdaPreOrderAddDelayMsg(String appVersion, String orderCode) {
        Boolean oldVersion = StringUtils.isNotBlank(appVersion) && appVersion.compareToIgnoreCase(XdaAppVersionConstant.TDA_VERSION) < 0;
        Integer xdaPreOrderDelaySeconds = dictionaryService.queryXdaPreOrderDelaySeconds();
        DelayMsgIDTO delayMsgIDTO = new DelayMsgIDTO(
                RedisDelayQueueEnum.XDA_PRE_ORDER_DELAY.getCode(),
                orderCode,
                oldVersion ? 180 : (Long.valueOf(xdaPreOrderDelaySeconds) - 5 ),
                TimeUnit.SECONDS);
        delayMsgClient.addDelayQueue(delayMsgIDTO);
    }

    public void saveXdaPreOrder(Order order, String billCode){
        XdaPreOrder xdaPreOrder = BeanCloneUtils.copyTo(order, XdaPreOrder.class);
        xdaPreOrder.setPayStatus(XdaPreOrderPayStatusEnums.INIT.getCode());
        xdaPreOrder.setBillCode(billCode);
        xdaPreOrderMapper.insertSelective(xdaPreOrder);

        List<XdaPreOrderItem> xdaPreOrderItems = BeanCloneUtils.copyTo(order.getOrderList(), XdaPreOrderItem.class);
        xdaPreOrderItems.forEach(xdaPreOrderItem -> {
            xdaPreOrderItem.setId(null);
        });
        xdaPreOrderItemMapper.insertList(xdaPreOrderItems);


        orderMapper.deleteByPrimaryKey(order.getId());

    }


    public void saveXdaPreOrderPromotion(ShoppingCartV4ODTO shoppingCartV4ODTO,Long orderId){
        if(SpringUtil.isNotEmpty(shoppingCartV4ODTO.getPromotionGroup())){
            List<XdaPreOrderSalesPromotion> orderSalesPromotionList = new ArrayList<>();
            for (ShoppingCartGroupV4ODTO shoppingCartGroupV4ODTO : shoppingCartV4ODTO.getPromotionGroup()) {
                if(null==shoppingCartGroupV4ODTO.getFullStatus() || shoppingCartGroupV4ODTO.getFullStatus()== 0){
                    continue;
                }
                XdaPreOrderSalesPromotion orderSalesPromotion = new XdaPreOrderSalesPromotion();
                CommodityPromotionV4ODTO commodityPromotionV4ODTO = shoppingCartGroupV4ODTO.getCommodityPromotionV4ODTO();
                orderSalesPromotion.setOrderId(orderId);
                orderSalesPromotion.setPromotionId(Long.valueOf(shoppingCartGroupV4ODTO.getPromotionId()));
                orderSalesPromotion.setPromotionName(commodityPromotionV4ODTO.getPromotionName());
                orderSalesPromotion.setPromotionType(commodityPromotionV4ODTO.getPromotionType());
                String[] result = commodityPromotionV4ODTO.getNextTips().split(";");
                orderSalesPromotion.setTips(result[0]);
                orderSalesPromotion.setPromotionRules(commodityPromotionV4ODTO.getRuleList().stream().collect(Collectors.joining(",")));
                orderSalesPromotionList.add(orderSalesPromotion);
            }
            if(SpringUtil.isNotEmpty(orderSalesPromotionList)){
                xdaPreOrderPromotionMapper.insertList(orderSalesPromotionList);
            }
        }
    }

    /**
     * 查询鲜达预付单未支付的订单
     * @param storeId
     * @return
     */
    public XdaPreOrder queryNoPayXdaPreOrder(Long storeId){
        Example example = new Example(XdaPreOrder.class);
        example.createCriteria().andEqualTo("storeId", storeId)
                .andEqualTo("payStatus", YesOrNoEnums.NO.getCode());
        List<XdaPreOrder> preOrderList = xdaPreOrderMapper.selectByExample(example);

        if(CollectionUtils.isNotEmpty(preOrderList)) {
            preOrderList.sort(Comparator.comparing(XdaPreOrder::getCreateTime).reversed());
            return preOrderList.get(0);
        }else {
            return null;
        }
    }

    /**
     * 根据支付单号查询鲜达预订单
     * @param billCode
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public XdaPreOrder queryXdaPreOrderByBillCode(String billCode){
        Example example = new Example(XdaPreOrder.class);
        example.createCriteria().andEqualTo("billCode", billCode);
        return xdaPreOrderMapper.selectOneByExample(example);
    }

    /**
     * 鲜达预付单转成订单
     * @param xdaPreOrder
     */
    @Transactional(rollbackFor = Exception.class)
    public void xdaPreOrderToOrder(ThirdPartyPayCallbackEvent event, XdaPreOrder xdaPreOrder) {
        Long xfOrderId = null;
        try{
            // 更新预订单为支付完成
            xdaPreOrder.setPayStatus(XdaPreOrderPayStatusEnums.SUCCESS.getCode());
            xdaPreOrder.setUpdateTime(new Date());
            xdaPreOrderMapper.updateByPrimaryKeySelective(xdaPreOrder);

            // 保存order
            Order order = BeanCloneUtils.copyTo(xdaPreOrder, Order.class);
            order.setTotalAmount(order.getOrderAmount());
            order.setFinalAmount(order.getOrderAmount());
            order.setPrintNum(1);
            order.setOrderStatus(0);
            order.setSyncStatus(YesOrNoEnums.NO.getCode());
            order.setProcessStatus(XdaOrderProcessStatusEunm.WAITING_SHIP.getCode());

            // 是否通达客户
            Boolean isTdaStore = tdaOrderService.isTdaStore(order.getStoreId());
            if(isTdaStore){
                order.setProcessStatus(XdaOrderProcessStatusEunm.WAITING_PICK.getCode());
            }
            order.setPresaleStatus(YesOrNoEnums.NO.getCode());
            orderMapper.insertSelective(order);

            // 现付订单置为成功
            XfOrder xfOrder = new XfOrder();
            xfOrder.setBillCode(xdaPreOrder.getBillCode());
            XfOrder xf = xfOrderMapper.selectOne(xfOrder);
            xfOrderId = xf.getId();
            xfOrderService.updateStatus(xf.getId(),1,"创建成功", order.getId());

            Example example = new Example(XdaPreOrderItem.class);
            example.createCriteria().andEqualTo("orderId", xdaPreOrder.getId());
            List<XdaPreOrderItem> xdaPreOrderItemList = xdaPreOrderItemMapper.selectByExample(example);

            List<OrderList> orderList = BeanCloneUtils.copyTo(xdaPreOrderItemList, OrderList.class);
            log.info("xdaPreOrderItemList:{}  orderList:{}", JSON.toJSONString(xdaPreOrderItemList), JSON.toJSONString(orderList));

            // 保存order_list,order_gift_list
            List<OrderList> kfkOrderList = new ArrayList<>();
            orderList.forEach(item -> {
                item.setId(null);
                orderListMapper.insert(item);

                OrderListGift gift = BeanCloneUtils.copyTo(item, OrderListGift.class);
                //确保giftList插入时生成数据库自增的ID
                gift.setId(null);
                orderListGiftMapper.insert(gift);

                // 给统计查询发消息，id必须是order_gift_list.id
                item.setId(gift.getId());
                kfkOrderList.add(item);
            });
            order.setOrderList(kfkOrderList);

            // 保存订单营销表
            Example example2 = new Example(XdaPreOrderSalesPromotion.class);
            example2.createCriteria().andEqualTo("orderId", xdaPreOrder.getId());
            List<XdaPreOrderSalesPromotion> xdaPreOrderSalesPromotions = xdaPreOrderPromotionMapper.selectByExample(example2);
            List<OrderSalesPromotion> orderSalesPromotionList = BeanCloneUtils.copyTo(xdaPreOrderSalesPromotions, OrderSalesPromotion.class);
            if(CollectionUtils.isNotEmpty(orderSalesPromotionList)){
                orderSalesPromotionList.forEach(item -> {
                    item.setId(null);
                });
                orderSalesPromotionMapper.insertList(orderSalesPromotionList);
            }

            orderService.crateOrderMirror(order);

            // 记录订单日志
            orderHistoryService.insertOrderHistoryOnCreateOrder(order);

            // 客户账户类型
            QueryXdaUserAccountDTO queryXdaUserAccountDTO = new QueryXdaUserAccountDTO();
            queryXdaUserAccountDTO.setStoreId(order.getStoreId());
            XdaUserAccountDTO xdaUserAccountDTO = xdaStoreUserClient.queryUserAccountInfo(queryXdaUserAccountDTO);
            QYAssert.notNull(xdaUserAccountDTO, "客户账户不存在");

            // 扣款 预付款用户保存付款单
            if(XdaStoreTypeEnum.PRE_PAY.equals(xdaUserAccountDTO.getStoreType())){
                xdaOrderV4Service.saveOrderDeductions(order, order.getStoreId(), XdaPayTypeEnum.DEDUCTION, "",null);
            }

            // 8.事务提交后 发送kafka消息
            TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronization() {
                @Override
                public void afterCommit() {
                    // 统计查询发送消息
                    orderAsyncKafkaService.sendKafkaSaveOrderMessage(order);

                    // 给物流发送消息
                    if(isTdaStore){
                        TdaOrderSyncTmsIDTO tdaOrderSyncTmsIDTO = TdaOrderSyncTmsIDTO.forOrder(order.getId(), XdaOrderProcessStatusEunm.WAITING_PICK.getCode());
                        tdaOrderService.tdaOrderSyncToTms(tdaOrderSyncTmsIDTO);
                    }

                    // 发送拆单消息
                    sendSplitOrderMsg(order.getId(), order.getOrderTime(), order.getCreateId());

                    // 查询订单优惠券信息，实时维护优惠券统计表
                    couponDayStatisticsService.saveOrUpdateCouponDayStatistics(order.getId(), OperateTypeEnums.新增.getCode());
                }
            });
        }catch (Exception exception){
            log.error("-----鲜达预付单转成订单---新增订单报错",exception);
            String errorDetailMsg =  null == exception.getMessage() ? "鲜达预付单转成订单,创建订单报错" : exception.getMessage();
            xfOrderService.updateStatus(xfOrderId,2,errorDetailMsg, null);

            // 订单报错后扣除账户余额、支付金额原路返回、发送短信提醒
            try{
                List<String> billCodes = new ArrayList<>();
                billCodes.add(event.getOrderCode());
                List<XdaPayBillEntry> xdaPayBillByIds = xdaPayBillMapper.findXdaPayBillByIds(billCodes);
                if(SpringUtil.isNotEmpty(xdaPayBillByIds) &&  null != xdaPayBillByIds.get(0)){
                    XdaPayBillEntry xdaPay = xdaPayBillByIds.get(0);
                    rechargeService.xdaOrderCreateErrorDeal(xdaPay, event);
                }
            }catch (Exception e){
                log.error("-----鲜达预付单转成订单失败,金额原路返回失败.billCode: {}, 详细信息 {}", xdaPreOrder.getBillCode(), e);

                StringBuffer sb = new StringBuffer();
                sb.append("鲜达预付单转成订单失败,金额原路返回失败,billCode:" + xdaPreOrder.getBillCode());
                //发送微信模板信息
                weChatSendMessageService.sendWeChatMessage(sb.toString());
            }

            try {
                // 预订单转订单失败，退还券
                mtCouponService.refundCoupon(xdaPreOrder.getId(), xdaPreOrder.getStoreId());
            }catch (Exception e){
                log.warn("鲜达预订单转订单失败，退还券失败，订单号：{}", xdaPreOrder.getId(), e);
            }

            throw new RuntimeException("鲜达预付单转成订单失败");
        }
    }

    /**
     * 发送拆单消息
     * @param orderId
     * @param orderTime
     * @param createId
     */
    private void sendSplitOrderMsg(Long orderId, Date orderTime, Long createId) {
        SplitOrderKafkaVo splitOrderKafkaVo = new SplitOrderKafkaVo();
        splitOrderKafkaVo.setOrderId(orderId);
        splitOrderKafkaVo.setOrderTime(orderTime);
        splitOrderKafkaVo.setType(KafkaMessageOperationTypeEnum.INSERT);
        splitOrderKafkaVo.setCreateId(createId);
        splitOrderKafkaVo.setEnterpriseId(78L);
        //splitOrderService.execute(splitOrderKafkaVo, splitOrderService, weChatSendMessageService);
        splitOrderSendKfkService.sendSplitOrderKfkMsg(splitOrderKafkaVo);
    }

    /**
     *  鲜达预订单手动取消
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public Integer cancelXdaPreOrder(OrderCancelV4IDTO idto) {
        XdaPreOrder xdaPreOrder = xdaPreOrderMapper.selectByPrimaryKey(idto.getOrderId());
        QYAssert.isTrue(xdaPreOrder != null, "预订单不存在，请刷新重试");
        if(!XdaPreOrderPayStatusEnums.INIT.getCode().equals(xdaPreOrder.getPayStatus())){
            QYAssert.isFalse("预订单非待支付状态，不能取消");
        }
        xdaPreOrderCancel(xdaPreOrder, idto.getReasonOptionId(), "预订单手动取消", false);
        return 1;
    }

    /**
     * 鲜达预订单超时自动取消
     * @param orderCode
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public Boolean xdaPreOrderAutoCancel(String orderCode) {
        Example example = new Example(XdaPreOrder.class);
        example.createCriteria().andEqualTo("orderCode", orderCode);
        XdaPreOrder xdaPreOrder = xdaPreOrderMapper.selectOneByExample(example);
        if(xdaPreOrder != null && XdaPreOrderPayStatusEnums.INIT.getCode().equals(xdaPreOrder.getPayStatus())){
            xdaPreOrderCancel(xdaPreOrder, null , "预订单超时自动取消", true);
        }else {
            log.warn("鲜达预订单超时自动取消,状态不对。预订单号: {} status: {}", orderCode, xdaPreOrder.getPayStatus());
        }

        return Boolean.TRUE;
    }

    private void xdaPreOrderCancel(XdaPreOrder xdaPreOrder, Long reasonOptionId, String remark, Boolean isAutoCancel) {
        // 更新预订单为取消
        xdaPreOrder.setPayStatus(XdaPreOrderPayStatusEnums.CANCEL.getCode());
        xdaPreOrder.setUpdateTime(new Date());
        xdaPreOrderMapper.updateByPrimaryKeySelective(xdaPreOrder);

        String billCode = xdaPreOrder.getBillCode();
        XfOrder xfOrder = xfOrderMapper.selectOne(new XfOrder(billCode));
        if(xfOrder != null){
            xfOrderService.updateStatus(xfOrder.getId(),2, remark, null);
        }

        // 生成取消状态的订单
        Order order = BeanCloneUtils.copyTo(xdaPreOrder, Order.class);
        order.setTotalAmount(order.getOrderAmount());
        order.setFinalAmount(order.getOrderAmount());
        order.setPrintNum(1);
        order.setOrderStatus(2);
        order.setSyncStatus(YesOrNoEnums.NO.getCode());
        order.setProcessStatus(XdaOrderProcessStatusEunm.CANCEL.getCode());
        order.setPresaleStatus(YesOrNoEnums.NO.getCode());
        order.setCacelReasonId(reasonOptionId);
        orderMapper.insertSelective(order);

        Example example2 = new Example(XdaPreOrderItem.class);
        example2.createCriteria().andEqualTo("orderId", xdaPreOrder.getId());
        List<XdaPreOrderItem> xdaPreOrderItemList = xdaPreOrderItemMapper.selectByExample(example2);

        List<OrderList> orderList = BeanCloneUtils.copyTo(xdaPreOrderItemList, OrderList.class);

        List<OrderList> kfkOrderList = new ArrayList<>();
        orderList.forEach(item -> {
            item.setId(null);

            orderListMapper.insert(item);

            OrderListGift orderListGift = BeanCloneUtils.copyTo(item, OrderListGift.class);
            orderListGift.setId(null);
            orderListGiftMapper.insert(orderListGift);

            // 给统计查询发消息，id必须是order_gift_list.id
            item.setId(orderListGift.getId());
            kfkOrderList.add(item);
        });

        order.setOrderList(kfkOrderList);

        Example example3 = new Example(XdaPreOrderSalesPromotion.class);
        example3.createCriteria().andEqualTo("orderId", xdaPreOrder.getId());
        List<XdaPreOrderSalesPromotion> xdaPreOrderSalesPromotions = xdaPreOrderPromotionMapper.selectByExample(example3);
        List<OrderSalesPromotion> orderSalesPromotionList = BeanCloneUtils.copyTo(xdaPreOrderSalesPromotions, OrderSalesPromotion.class);
        if(CollectionUtils.isNotEmpty(orderSalesPromotionList)){
            orderSalesPromotionList.forEach(item -> {
                item.setId(null);
            });
            orderSalesPromotionMapper.insertList(orderSalesPromotionList);
        }

        orderService.crateOrderMirror(order);
        // 记录订单日志
        orderHistoryService.insertOrderHistoryOnCancelOrder(order);

        // 鲜达预订单超时自动取消是系统自动取消的,存在大仓释放冻结失败。自动取消失败了,存在问题。
        // 因此自动取消的需要进行异常处理，确保系统能够取消成功。人为的手动取消订单，则不进行异常处理。直接按照大仓抛错
        if(isAutoCancel) {
            try {
                toBService.warehouseUnfreezeInventory(order.getId(), xdaPreOrder.getStoreId());
            }catch (Exception e){
                log.warn("订单自动取消,库存未释放成功。订单号：{}", order.getOrderCode());
                weChatSendMessageService.sendWeChatMessage("订单自动取消,库存未释放成功。订单号：" + order.getOrderCode());
            }
        }else {
            // 解冻库存
            if(!toBService.warehouseUnfreezeInventory(order.getId(), xdaPreOrder.getStoreId())){
                QYAssert.isFalse("订单取消失败!");
            }
        }

        // 返还优惠券
        mtCouponService.refundCoupon(order.getId(), order.getStoreId());

        // 预订单没有支付成功，订单被自动取消。则删除下单设备记录
        if(isAutoCancel) {
            appOrderLogService.deleteAppOrderLogByOrderId(order.getId());
        }

        TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronizationAdapter() {
            @Override
            public void afterCommit() {
                orderAsyncKafkaService.sendKafkaCancelOrder(order);

                // 维护B端特价限购记录表
                orderLimitQuantityService.saveOrderLimitQuantity(xdaPreOrder.getId(), OperateTypeEnums.删除.getCode(), true);
            }
        });
    }
}
