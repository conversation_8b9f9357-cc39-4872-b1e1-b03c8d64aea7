package com.pinshang.qingyun.order.service.xda;

import com.pinshang.qingyun.base.configure.expand.QYAssert;
import com.pinshang.qingyun.base.enums.OperateTypeEnums;
import com.pinshang.qingyun.marketing.dto.mtCoupon.MtCouponDayStatisticsSaveIDTO;
import com.pinshang.qingyun.marketing.service.mtCoupon.MtCouponTobClient;
import com.pinshang.qingyun.order.dto.xda.MtCouponDayStatisticsODTO;
import com.pinshang.qingyun.order.mapper.OrderListMapper;
import com.pinshang.qingyun.order.mapper.OrderMapper;
import com.pinshang.qingyun.order.model.order.Order;
import com.pinshang.qingyun.order.model.order.OrderList;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.entity.Example;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Author: sk
 * @Date: 2024/8/20
 */
@Slf4j
@Service
public class CouponDayStatisticsService {

    @Autowired
    private MtCouponTobClient mtCouponTobClient;
    @Autowired
    private OrderMapper orderMapper;
    @Autowired
    private OrderListMapper orderListMapper;


    /**
     * 查询订单使用优惠券的信息，实时维护优惠券统计表
     * @param orderId
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public Boolean saveOrUpdateCouponDayStatistics(Long orderId, Integer operateType){
        Boolean isAdd = OperateTypeEnums.新增.getCode().equals(operateType);

        Order order = orderMapper.selectByPrimaryKey(orderId);
        QYAssert.isTrue(order != null, "订单不存在");

        Example orderListExample = new Example(OrderList.class);
        orderListExample.createCriteria().andEqualTo("orderId", orderId);
        List<OrderList> orderList = orderListMapper.selectByExample(orderListExample);

        List<OrderList> filterList = orderList.stream().filter(p -> p.getCouponId() != null).collect(Collectors.toList());
        if(CollectionUtils.isNotEmpty(filterList)){
            BigDecimal discountTotalAmount = filterList.stream().map(item -> item.getCouponDiscountAmount()).reduce(BigDecimal.ZERO, BigDecimal::add);

            MtCouponDayStatisticsSaveIDTO dayStatisticsSaveIdto = new MtCouponDayStatisticsSaveIDTO();
            dayStatisticsSaveIdto.setCouponId(filterList.get(0).getCouponId());
            dayStatisticsSaveIdto.setUserQuntity(isAdd ? 1 : -1);
            dayStatisticsSaveIdto.setUsedTotalOrderAmount(isAdd ? order.getOrderAmount() : order.getOrderAmount().negate());
            dayStatisticsSaveIdto.setDiscountTotalAmount(isAdd ? discountTotalAmount : discountTotalAmount.negate());
            dayStatisticsSaveIdto.setStatisticsDate(order.getCreateTime());
            mtCouponTobClient.saveOrUpdateCouponDayStatistics(Collections.singletonList(dayStatisticsSaveIdto));
        }

        return Boolean.TRUE;
    }

    /**
     * 优惠券日统计
     * 查询已核销数量、已核销订单总金额、折扣总金额
     * @param beginTime
     * @param endTime
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public List<MtCouponDayStatisticsODTO> queryOrderCouponList(String beginTime, String endTime){
        // 1个订单里面有且只有一张券
        List<MtCouponDayStatisticsODTO> orderCouponList = orderMapper.queryOrderCouponList(beginTime, endTime);
        if(CollectionUtils.isEmpty(orderCouponList)){
            return new ArrayList<>();
        }

        List<MtCouponDayStatisticsODTO> resultList = new ArrayList<>();
        Map<Long, List<MtCouponDayStatisticsODTO>> companyMap = orderCouponList.stream().collect(Collectors.groupingBy(MtCouponDayStatisticsODTO::getCouponId));
        companyMap.forEach((k, v) -> {

            // 1个订单核销数量为1，核销金额只算一次
            Map<Long, BigDecimal> orderMap = new HashMap<>();
            v.forEach(item -> {
                orderMap.put(item.getOrderId(), item.getUsedTotalOrderAmount());
            });

            MtCouponDayStatisticsODTO couponDayStatistics = new MtCouponDayStatisticsODTO();
            couponDayStatistics.setCouponId(k);
            couponDayStatistics.setUserQuntity(orderMap.size());
            couponDayStatistics.setUsedTotalOrderAmount(orderMap.values().stream().reduce(BigDecimal.ZERO, BigDecimal::add));
            couponDayStatistics.setDiscountTotalAmount(v.stream().collect(Collectors.reducing(BigDecimal.ZERO, MtCouponDayStatisticsODTO::getDiscountTotalAmount, BigDecimal::add)));
            resultList.add(couponDayStatistics);
        });

        return resultList;
    }
}
