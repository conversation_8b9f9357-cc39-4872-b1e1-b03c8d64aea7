package com.pinshang.qingyun.order.controller.xda.v3;

import com.pinshang.qingyun.base.api.ApiResponse;
import com.pinshang.qingyun.base.configure.expand.QYAssert;
import com.pinshang.qingyun.base.constant.QingyunConstant;
import com.pinshang.qingyun.order.config.LockConstants;
import com.pinshang.qingyun.order.dto.xda.v3.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;

/**
 * <AUTHOR>
 *
 */
@Deprecated
@RestController
@RequestMapping("/xda/orderV3")
@Api(value = "鲜达APP订单相关接口",tags ="XdaAppOrderV3Controller",description ="鲜达APP订单相关接口" )
public class XdaAppOrderV3Controller {


    @ApiOperation(value = "预览订单", notes = "预览订单")
    @ApiImplicitParam(name = "xdaPreOrderV3IDTO", value = "", required = true, paramType = "body", dataTypeClass = XdaPreOrderV3IDTO.class)
    @RequestMapping(value = "/preOrderV3",method = RequestMethod.POST)
    public XdaPreOrderV3ODTO preOrderView(@RequestBody XdaPreOrderV3IDTO xdaPreOrderV3IDTO){
        QYAssert.isFalse("请升级到最新版本");
        return null;
    }

    /**
     * 预览订单 详细信息
     */
    @ApiOperation(value = "预览订单 -商品详细信息", notes = "预览订单 -商品详细信息")
    @ApiImplicitParam(name = "xdaPreOrderV3IDTO", value = "", required = true, paramType = "body", dataTypeClass = XdaPreOrderV3IDTO.class)
    @RequestMapping(value = "/preOrderViewCommodityListV3",method = RequestMethod.POST)
    public XdaPreOrderItemV3ODTO preOrderViewCommodityListV3(@RequestBody XdaPreOrderV3IDTO xdaPreOrderV3IDTO){
        QYAssert.isFalse("请升级到最新版本");
        return null;
    }
    /**
     * 创建订单
     * @TODO 订单扣款 流程修改
     * @param xdaCreatePrePayOrderV3IDTO
     * @return
     */
    @ApiOperation(value = "创建订单", notes = "创建订单")
    @ApiImplicitParam(name = "xdaCreatePrePayOrderV3IDTO", value = "", required = true, paramType = "body", dataTypeClass = XdaCreatePrePayOrderV3IDTO.class)
    @RequestMapping(value = "/creatOrderV3",method = RequestMethod.POST)
    public boolean creatOrder(@RequestBody XdaCreatePrePayOrderV3IDTO xdaCreatePrePayOrderV3IDTO){
        QYAssert.isFalse("请升级到最新版本");
        return Boolean.FALSE;
    }
    @PostMapping("/cancelOrderV3")
    @ApiOperation(value = "订单取消", notes = "订单取消")
    public Integer cancelOrderV3(@RequestBody OrderCancelV3IDTO idto){
        QYAssert.isFalse("请升级到最新版本");
        return null;
    }
    /**
     * 订单复制
     * @param orderId
     * @return
     */
    @RequestMapping(value = "/copyOrderByIdXdaAppV3",method = RequestMethod.GET)
    @ApiOperation(value = "订单复制", notes = "订单复制")
    public Integer copyOrderByIdXdaAppV3(@RequestParam(value = "orderId",required = false) Long orderId,@RequestParam(value = "orderDate",required = false) String orderDate){
        QYAssert.isFalse("请升级到最新版本");
        return null;
    }

    @PostMapping("/listV3")
    @ApiOperation(value = "订单列表")
    public ApiResponse<XdaOrderAppV3ODTO> queryOrderListByPageV3(@RequestBody XdaQueryOrderAppParamV3 param, HttpServletResponse response){
        QYAssert.isFalse("请升级到最新版本");
        return null;
    }

    @GetMapping("/queryOrderDetailByCodeV3")
    @ApiOperation(value = "订单列表详情")
    public XdaOrderAppV3ODTO queryOrderDetailByCodeV3(@RequestParam(value = "orderCode",required = false) String orderCode){
        QYAssert.isFalse("请升级到最新版本");
        return null;
    }

    @ApiOperation(value = "订单列表详情-订单商品清单", notes = "订单列表详情-订单商品清单")
    @RequestMapping(value = "/queryXdaOrderDetailV3",method = RequestMethod.POST)
    public XdaPreOrderItemV3ODTO queryXdaOrderCommodityDetailV3(@RequestParam(value = "orderCode",required = false) String orderCode){
        QYAssert.isFalse("请升级到最新版本");
        return null;
    }
}
