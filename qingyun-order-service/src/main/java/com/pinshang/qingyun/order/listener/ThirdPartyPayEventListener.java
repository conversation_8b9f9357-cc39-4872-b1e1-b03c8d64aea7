package com.pinshang.qingyun.order.listener;


import com.pinshang.qingyun.base.enums.XSPayBillStatusEnums;
import com.pinshang.qingyun.box.utils.SpringUtil;
import com.pinshang.qingyun.order.config.LockConstants;
import com.pinshang.qingyun.order.mapper.XdaPayBillMapper;
import com.pinshang.qingyun.order.mapper.entry.recharge.XdaPayBillEntry;
import com.pinshang.qingyun.order.service.pay.callback.ThirdPartyPayCallbackEvent;
import com.pinshang.qingyun.order.service.recharge.RechargeService;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationListener;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2019/12/5 18:36
 */
@Component
@Slf4j
public class ThirdPartyPayEventListener implements ApplicationListener<ThirdPartyPayCallbackEvent> {

    @Autowired
    private RechargeService rechargeService;
    @Autowired
    private RedissonClient redissonClient;
    @Autowired
    private XdaPayBillMapper xdaPayBillMapper;

    @Override
    public void onApplicationEvent(@NotNull ThirdPartyPayCallbackEvent event) {
        EventProcessPool.getExecutor().execute(()->{
            if(event.isValidate()){
                //1、查询订单状态
                List<XdaPayBillEntry> payBillList = xdaPayBillMapper.findXdaPayBillByIds(Collections.singletonList(event.getOrderCode()));
                if(SpringUtil.isNotEmpty(payBillList)){
                    XdaPayBillEntry payBill = payBillList.get(0);
                    if(payBill.getBillStatus().equals(XSPayBillStatusEnums.WAITING_PAY.getCode())){
                        RLock lock = redissonClient.getLock(LockConstants.generateCallbackLockKey(event.getOrderCode()));
                        if(lock.tryLock()){
                            try {
                                rechargeService.paySuccessPostHandle(event,payBill);
//                                //发送充值消息
//                                Boolean flag = rechargeService.sendRechargeMsg(payBill.getPayAmount(),payBill.getStoreId(),payBill.getCreateId());
//                                if(!flag){
//                                    log.error("充值消息发送失败!orderCode:{}", payBill.getBillCode());
//                                }
                            } finally {
                                lock.unlock();
                            }
                        }else {
                            log.error("===========err==========>当前节点未能获取支付回调锁, event:{}, orderId:{}, order_status:{}"
                                    , event.toString(), payBill.getBillCode()+"", payBill.getBillStatus());
                        }
                    }
                }
            }else{
                log.error("支付宝回调,订单关闭或校验异常!event:{}", event);
            }
        });
    }
}
