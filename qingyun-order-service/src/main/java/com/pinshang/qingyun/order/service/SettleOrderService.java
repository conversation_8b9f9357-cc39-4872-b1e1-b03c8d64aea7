package com.pinshang.qingyun.order.service;

import com.pinshang.qingyun.order.mapper.SettleOrderMapper;
import com.pinshang.qingyun.order.mapper.entry.settlement.SettleOrderEntry;
import com.pinshang.qingyun.order.mapper.entry.settlement.SettleOrderItemEntry;
import com.pinshang.qingyun.order.vo.settle.OrderReqVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class SettleOrderService {

    @Autowired
    private SettleOrderMapper settleOrderMapper;

    public List<SettleOrderEntry> findOrderByParams(OrderReqVo orderReqVo){
      return  settleOrderMapper.findOrderByParams(orderReqVo);
    }

    public List<SettleOrderEntry> findXsZsOrderByParams(OrderReqVo orderReqVo){
      return  settleOrderMapper.findXsZsOrderByParams(orderReqVo);
    }


    public List<SettleOrderItemEntry> findXsOrderItemByOrderIds(List<Long> orderIds,int logisticsModel){
        return  settleOrderMapper.findXsOrderItemByOrderIds(orderIds,logisticsModel);
    }


    public List<SettleOrderEntry> findTOrderByParams(OrderReqVo orderReqVo){
        return  settleOrderMapper.findTOrderByParams(orderReqVo);
    }


    public List<SettleOrderItemEntry> findTOrderItemByOrderIds(List<Long> orderIds){
        return  settleOrderMapper.findTOrderItemByOrderIds(orderIds);
    }


}
