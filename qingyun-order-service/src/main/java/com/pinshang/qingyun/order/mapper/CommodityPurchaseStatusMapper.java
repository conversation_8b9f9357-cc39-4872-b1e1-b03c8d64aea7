package com.pinshang.qingyun.order.mapper;

import com.pinshang.qingyun.base.mybatis.MyMapper;
import com.pinshang.qingyun.order.mapper.entry.purchase.ShopCommodityPurchaseStatusEntry;
import com.pinshang.qingyun.order.mapper.entry.shop.*;
import com.pinshang.qingyun.order.model.commodity.CommodityPurchaseStatus;
import com.pinshang.qingyun.order.vo.commodity.CommodityPurchaseStatusVo;
import com.pinshang.qingyun.order.vo.purchase.ShopCommodityPurchaseStatusVo;
import com.pinshang.qingyun.order.vo.shop.PriceModelLogVo;
import com.pinshang.qingyun.order.vo.shop.ShopQtOrderProductVo;
import com.pinshang.qingyun.order.vo.shop.XsShopCommodityPurchaseStatusVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
@Mapper
public interface CommodityPurchaseStatusMapper extends MyMapper<CommodityPurchaseStatus> {

    int updateByShopCommodity(List<CommodityPurchaseStatusVo> vos);

    List<ShopQtOrderProductEntry> selectShopQtOrderProduct(ShopQtOrderProductVo vo);

    List<PriceModelLogEntry> selectProductPriceModel(PriceModelLogVo vo);
    List<CommodityPriceEntry> selectProductPriceModelListByPriceModeClode(@Param("priceModeClode")String priceModeClode);
    List<CommodityListEntry> selectCommodityByProductPriceModelId(@Param("productPriceModelId") Long productPriceModelId);

    List<XsShopCommodityPurchaseStatusEntry> selectXsShopCommodityPurchaseStatus(XsShopCommodityPurchaseStatusVo vo);

    List<ShopCommodityPurchaseStatusEntry> selectShopCommodityPurchaseStatusList(ShopCommodityPurchaseStatusVo vo);

    List<String> selectShopCommodityPurchaseStatusListByShopIdAndCommodityId(@Param("shopIdAndCommodityIdList") List<String> shopIdAndCommodityIdList);

    int updateCommodityPurchaseStatusByShopIdAndCommodityId(@Param("shopIdAndCommodityIdList") List<String> shopIdAndCommodityIdList);

    /**
     * 门店id查询商品可采状态id集合
     * @param shopId
     * @return
     */
    List<Long> findCommodityPurchaseStatusIdListByShopId(@Param("shopId") Long shopId);

    /**
     * 期初门店商品id字段
     * @param id
     */
    void initShopCommodityById(@Param("id") Long id);
}

