package com.pinshang.qingyun.order.controller.client;

import com.pinshang.qingyun.order.service.SubOrderService;
import com.pinshang.qingyun.order.vo.subOrder.StockOutJobSubOrderReqVo;
import com.pinshang.qingyun.order.vo.subOrder.StockOutJobSubOrderRespVo;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping("/stockOutJob/client")
public class StockOutSubOrderController {

    @Autowired
    private SubOrderService service;

    @ApiOperation(value = "获取实收数量列表")
    @PostMapping("/getStockOutSubOrderList")
    public List<StockOutJobSubOrderRespVo> getStockOutSubOrderList(@RequestBody StockOutJobSubOrderReqVo reqVo){
        return service.getStockOutSubOrderList(reqVo);
    }

}
