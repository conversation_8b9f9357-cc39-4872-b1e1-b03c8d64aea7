package com.pinshang.qingyun.order.controller;

import com.pinshang.qingyun.base.api.TokenInfo;
import com.pinshang.qingyun.base.configure.expand.QYAssert;
import com.pinshang.qingyun.base.util.FastThreadLocalUtil;
import com.pinshang.qingyun.order.dto.order.QuickGoodsIDTO;
import com.pinshang.qingyun.order.mapper.entry.order.QuickGoodsEntry;
import com.pinshang.qingyun.order.service.ConsignmentSupplierService;
import com.pinshang.qingyun.order.service.QuickGoodsService;
import com.pinshang.qingyun.order.util.StallUtils;
import com.pinshang.qingyun.order.vo.order.CreateOrderFilterVo;
import com.pinshang.qingyun.order.vo.order.QuickGoodsVo;
import com.pinshang.qingyun.renderer.annotation.MethodRender;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@Slf4j
@RequestMapping("/order")
public class QuickGoodsController {
	@Autowired
	private QuickGoodsService quickGoodsService;
	@Autowired
	private RedissonClient redissonClient;
	@Autowired
	private ConsignmentSupplierService consignmentSupplierService;

	@MethodRender
	@PostMapping(value = "/findQuickGoodsList")
	public List<QuickGoodsEntry> findQuickGoodsList(@RequestBody QuickGoodsVo vo){
		TokenInfo tokenInfo = FastThreadLocalUtil.getQY();
		if(tokenInfo != null){
			vo.setCreateId(tokenInfo.getUserId());
			vo.setConsignmentId(tokenInfo.getConsignmentId());
			vo.setIsInternal(tokenInfo.getIsInternal());
			if(StallUtils.isStallSubcontractor(tokenInfo.getManagementMode())){
				vo.setStallIdList(consignmentSupplierService.selectUserStallIdList(tokenInfo.getShopId()));
				vo.setBigShop(true);
			}
		}
		return quickGoodsService.findQuickGoodsList(vo);
	}
	
	@PostMapping(value = "/deleteQuickGoodsByStoreId")
	public Integer deleteQuickGoodsByStoreId(@RequestBody QuickGoodsVo vo){
		return quickGoodsService.deleteQuickGoodsByStoreId(vo);
	}

	/**
	 * 快速订货添加到购物车(门店快速订货)
	 * @param vo
	 * @return
	 */
	@PostMapping(value="/quickGoods/addShoppingCart")
	public Integer addShoppingCart(@RequestBody QuickGoodsVo vo) {
		TokenInfo ti = FastThreadLocalUtil.getQY();
		QuickGoodsIDTO idto =new QuickGoodsIDTO();
		idto.setEnterpriseId(ti.getEnterpriseId());
		if(ti.getStoreId()!= null){
			idto.setStoreId(ti.getStoreId());
		}
		idto.setCreateId(ti.getUserId());
		idto.setIsInternal(ti.getIsInternal());
		RLock lock = redissonClient.getLock("ORDER:addShoppingCart:" + vo.getCreateId());
		if(lock.tryLock()){
			try {
				return quickGoodsService.addShoppingCart(vo);
			}finally {
				lock.unlock();
			}
		}else {
			QYAssert.isFalse("系统繁忙,请勿频繁操作!");
		}
		return 0;
	}

	@PostMapping(value="/quickGoods/addShoppingCartAdmin")
	public Integer addShoppingCartAdmin(@RequestBody QuickGoodsVo vo) {
		TokenInfo tokenInfo = FastThreadLocalUtil.getQY();
		RLock lock = redissonClient.getLock("ORDER:AddShoppingCartAdmin:" + tokenInfo.getUserId());
		if(lock.tryLock()){
			try {
				vo.setCreateId(tokenInfo.getUserId());
				vo.setEnterpriseId(78L);
				return quickGoodsService.addShoppingCartAdmin(vo);
			}finally {
				lock.unlock();
			}
		}else {
			QYAssert.isFalse("系统繁忙,请勿频繁操作!");
		}
		return 0;
	}
	
	@RequestMapping(value = "/saveQuickGoods", method = RequestMethod.POST)
	public Boolean saveQuickGoods(@RequestBody QuickGoodsVo vo) {
		log.info("saveQuickGoods storeId: {}", vo.getStoreId());
		//try{
			return quickGoodsService.saveQuickGoods(vo);
		/*}catch(Exception ex){
			log.error(ex.getMessage());
			return false;
		}*/
	}

	@RequestMapping(value = "/saveQuickGoods4admin", method = RequestMethod.POST)
	public Boolean saveQuickGoods4admin(@RequestBody QuickGoodsVo vo)  {

		RLock lock = redissonClient.getLock("ORDER:saveQuickGoods4admin:" + vo.getCreateId());
		if(lock.tryLock()){
			try {
				quickGoodsService.saveQuickGoods4admin(vo);
			} finally {
				lock.unlock();
			}
		}else {
			QYAssert.isFalse("系统繁忙,请勿频繁操作!");
		}
		return Boolean.TRUE;
	}
}
