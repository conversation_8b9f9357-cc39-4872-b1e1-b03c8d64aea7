package com.pinshang.qingyun.order.mapper.entry.order;

import java.math.BigDecimal;

public class ProductPriceEntry {

	private String productId;
	private String productCode;
	private String productName;
	private BigDecimal price;
	private BigDecimal limitNum;//限量

	//是否可变价——特价不可变价，其余从 订货设置 取
	private Integer changePriceStatus;
	private BigDecimal originalPrice;

	public BigDecimal getOriginalPrice() {
		return originalPrice;
	}

	public void setOriginalPrice(BigDecimal originalPrice) {
		this.originalPrice = originalPrice;
	}

	public String getProductId() {
		return productId;
	}
	public void setProductId(String productId) {
		this.productId = productId;
	}
	public String getProductCode() {
		return productCode;
	}
	public void setProductCode(String productCode) {
		this.productCode = productCode;
	}
	public BigDecimal getPrice() {
		return price;
	}
	public void setPrice(BigDecimal price) {
		this.price = price;
	}
	public BigDecimal getLimitNum() {
		return limitNum;
	}
	public void setLimitNum(BigDecimal limitNum) {
		this.limitNum = limitNum;
	}
	public String getProductName() {
		return productName;
	}
	public void setProductName(String productName) {
		this.productName = productName;
	}

	public Integer getChangePriceStatus() {
		return changePriceStatus;
	}

	public void setChangePriceStatus(Integer changePriceStatus) {
		this.changePriceStatus = changePriceStatus;
	}
}
