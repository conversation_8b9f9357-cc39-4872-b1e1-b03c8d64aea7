package com.pinshang.qingyun.order.service;

import com.github.pagehelper.PageInfo;
import com.pinshang.qingyun.base.enums.IogisticsModelEnums;
import com.pinshang.qingyun.base.enums.YesOrNoEnums;
import com.pinshang.qingyun.base.spring.SpringBeanFinder;
import com.pinshang.qingyun.box.utils.DateUtil;
import com.pinshang.qingyun.cms.service.CommodityEverydayFreshClient;
import com.pinshang.qingyun.order.constant.ThreadPoolBeanConstants;
import com.pinshang.qingyun.order.dto.CommodityFreshOrderODTO;
import com.pinshang.qingyun.order.enums.DeliveryBatchTypeEnum;
import com.pinshang.qingyun.order.mapper.CommodityMapper;
import com.pinshang.qingyun.order.mapper.OrderMapper;
import com.pinshang.qingyun.order.mapper.entry.MdShopOrderSettingEntry;
import com.pinshang.qingyun.order.vo.order.OrderDto;
import com.pinshang.qingyun.price.dto.commodity.CommodityListRequestIDTO;
import com.pinshang.qingyun.price.dto.commodity.CommodityResultODTO;
import com.pinshang.qingyun.price.service.ProductPriceModelClient;
import com.pinshang.qingyun.shop.dto.ShopCommodityStockODTO;
import com.pinshang.qingyun.shop.service.ShopCommodityClient;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.jetbrains.annotations.NotNull;
import org.redisson.api.RAtomicLong;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 日日鲜商品订单
 * @Author: sk
 * @Date: 2023/3/3
 */
@Slf4j
@Service
public class CommodityFreshOrderService {

    @Autowired
    private OrderMapper orderMapper;
    @Autowired
    private WeChatSendMessageService weChatSendMessageService;
    @Autowired
    private ShopCommodityClient shopCommodityClient;
    @Autowired
    private CommodityMapper commodityMapper;
    @Autowired
    private ProductPriceModelClient productPriceModelClient;
    @Lazy
    @Autowired
    private MdShopOrderSettingService mdShopOrderSettingService;
    @Autowired
    private CloudOrderAllocationService cloudOrderAllocationService;
    @Autowired
    private CommodityEverydayFreshClient commodityEverydayFreshClient;
    @Autowired
    private CommodityFreshOrderSaveService commodityFreshOrderSaveService;
    @Autowired
    private RedissonClient redissonClient;
    @Autowired
    private OrderSplitService orderSplitService;

    private static String FRESH_ORDER = "日日鲜商品提交订单: ";

    /**
     * 汇总日日鲜商品昨日下单数量(job调用)
     * @param orderTime yyyy-MM-dd
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public Boolean commodityFreshOrderSummary(String orderTime) {
        // 查询日日鲜商品
        List<Long> commodityIdList = commodityEverydayFreshClient.getAllEverydayFresh();
        if(CollectionUtils.isEmpty(commodityIdList)){
            log.warn("日鲜商品为空!");
            return Boolean.FALSE;
        }

        //orderMapper.deleteCommodityFreshOrders(orderTime);

        orderMapper.batchInsertCommodityFreshOrders(orderTime, commodityIdList);
        return Boolean.TRUE;
    }

    /**
     * 创建日日鲜订单
     * @param hhMM HH:mm
     * @return
     */
    public Boolean createCommodityFreshOrder(String hhMM){
        // 校验重复任务
        String lockKey =  "fresh_commodity_create_order" + hhMM.replaceAll(":","");
        RAtomicLong lockKeyRa = redissonClient.getAtomicLong(lockKey);
        long lock = lockKeyRa.incrementAndGet();
        if (lock == 1) {
            lockKeyRa.expire(10, TimeUnit.MINUTES);
        }
        if (lock > 1) {
            return Boolean.FALSE;
        }


        String dayStr = DateUtil.getDateFormate(new Date(), "yyyy-MM-dd");
        // 查询未提交的门店商品并且总部可售的商品
        List<CommodityFreshOrderODTO> commodityFreshList = orderMapper.queryCommodityFreshList(dayStr);
        if(CollectionUtils.isEmpty(commodityFreshList)){
            log.warn(FRESH_ORDER + "今日商品已经全部下单了或总部不可售");
            return Boolean.FALSE;
        }

        // 根据门店分组
        Map<Long, List<CommodityFreshOrderODTO>> commodityFreshMap = commodityFreshList.stream().collect(Collectors.groupingBy(CommodityFreshOrderODTO::getShopId));
        // 查询今天下单的日日鲜商品数量
        List<Long> commodityIdList = commodityFreshList.stream().distinct().map(item -> item.getCommodityId()).collect(Collectors.toList());
        List<CommodityFreshOrderODTO> todayOrderedList = orderMapper.queryOrderedFreshList(dayStr, commodityIdList);

        //查询所有日日鲜自动订货供应商黑名单
        List<Long> filterSupplierList = mdShopOrderSettingService.queryAllOrderSupplierBlackList();

        // 获取线程池
        ThreadPoolTaskExecutor threadPool = (ThreadPoolTaskExecutor) SpringBeanFinder.getBean(ThreadPoolBeanConstants.ORDER_THREADPOOL);

        for(Map.Entry<Long, List<CommodityFreshOrderODTO>> entry : commodityFreshMap.entrySet()){
            // 创建任务并提交到线程池中
            threadPool.execute(new Runnable() {
                @Override
                public void run() {
                    try {
                        Long shopId = entry.getKey();
                        List<CommodityFreshOrderODTO> list = entry.getValue();
                        List<CommodityFreshOrderODTO> mdOrderedList = todayOrderedList.stream().filter(p -> p.getShopId().equals(shopId)).collect(Collectors.toList());
                        List<Long> orderedCommodityIdList = new ArrayList<>();
                        if(CollectionUtils.isNotEmpty(mdOrderedList)){
                            orderedCommodityIdList = mdOrderedList.stream().map(item -> item.getCommodityId()).collect(Collectors.toList());
                        }
                        createCommodityFreshOrder(hhMM, list, orderedCommodityIdList, filterSupplierList);
                    } catch (Exception e) {
                        log.error("日日鲜商品提交订单异常: ",e);

                        StringBuffer sb = new StringBuffer();
                        sb.append("日日鲜商品提交订单异常,门店id " + entry.getKey());
                        weChatSendMessageService.sendWeChatMessage(sb.toString());
                    }
                }
            });
        }

        return Boolean.TRUE;
    }

    /**
     * 创建日日鲜订单
     * @param hhMM HH:mm
     * @return
     */
    public Boolean createCommodityFreshOrder(String hhMM, List<CommodityFreshOrderODTO> list, List<Long> orderedCommodityIdList, List<Long> filterSupplierList){
        Long shopId = list.get(0).getShopId();
        Long storeId = list.get(0).getStoreId();
        List<Long> commodityIdList = list.stream().map(item -> item.getCommodityId()).collect(Collectors.toList());
        Map<Long, CommodityFreshOrderODTO> commodityFreshMap = list.stream().collect(Collectors.toMap(CommodityFreshOrderODTO::getCommodityId, Function.identity()));

        // 判断已上架状态
        List<Long> onLineCommodityIdList = new ArrayList<>();
        Map<Long, ShopCommodityStockODTO> shopCommodityMap = shopCommodityClient.queryShopCommodityValidStock(shopId, commodityIdList);
        if(shopCommodityMap == null || shopCommodityMap.size() == 0){
            log.warn(FRESH_ORDER + "t_xs_shop_commodity表不存在商品,门店id: {} 截单时间: {} 商品idList: {}", shopId, hhMM, commodityIdList);
            return Boolean.FALSE;
        }else {
            List<Long> notExistList = new ArrayList<>();
            commodityIdList.forEach(commodityId -> {
                if(shopCommodityMap.get(commodityId) != null
                        && YesOrNoEnums.NO.getCode().equals(shopCommodityMap.get(commodityId).getAppStatus())){
                    onLineCommodityIdList.add(commodityId);
                }else {
                    notExistList.add(commodityId);
                }
            });
            if(CollectionUtils.isNotEmpty(notExistList)){
                log.warn(FRESH_ORDER + "商品未上架,门店id: {} 截单时间: {} 商品idList: {}", shopId, hhMM, notExistList);
            }
            if(CollectionUtils.isEmpty(onLineCommodityIdList)){
                return Boolean.FALSE;
            }
        }

        commodityIdList = new ArrayList<>();
        commodityIdList.addAll(onLineCommodityIdList);

        // 调用client 去 qingyun-price 查询
        CommodityListRequestIDTO idto = new CommodityListRequestIDTO();
        idto.setStoreId(storeId + "");
        idto.setCommodityIdListAll(commodityIdList);
        idto.setPageNo(1);
        idto.setPageSize(Integer.MAX_VALUE);
        PageInfo<CommodityResultODTO> resultPageData = productPriceModelClient.findStoreCommodityListByPage(idto);
        List<CommodityResultODTO> pageList = resultPageData.getList();
        if(CollectionUtils.isEmpty(pageList)){
            log.warn(FRESH_ORDER + "客户价格方案列表为空,门店id: {} 截单时间: {} 商品idList: {}", shopId, hhMM, commodityIdList);
            return false;
        }

        // 过滤掉价格为null或者0的订单商品
        pageList = pageList.stream().filter(p -> p.getCommodityPrice() != null
                && p.getCommodityPrice().compareTo(BigDecimal.ZERO) > 0).collect(Collectors.toList());

        if(CollectionUtils.isEmpty(pageList)){
            log.warn(FRESH_ORDER + "所有商品价格为0，门店id: {} 截单时间: {}", shopId, hhMM);
            return false;
        }

        List<String> commodityIdStrList = pageList.stream().map(item -> item.getCommodityId()).collect(Collectors.toList());
        commodityIdList = pageList.stream().map(item -> Long.valueOf(item.getCommodityId())).collect(Collectors.toList());
        // 获取门店订货通用设置
        List<MdShopOrderSettingEntry> mdShopOrderSettingList = mdShopOrderSettingService.queryMdShopOrderSettingListByIds(storeId, commodityIdStrList);
        if(CollectionUtils.isEmpty(mdShopOrderSettingList)){
            log.warn(FRESH_ORDER + "门店订货通用设置为空,门店id: {} 截单时间: {} 商品idList: {}", shopId, hhMM, commodityIdStrList);
            return false;
        }else if(mdShopOrderSettingList.size() < commodityIdList.size()){
            List<Long> idList = mdShopOrderSettingList.stream().map(item -> item.getCommodityId()).collect(Collectors.toList());
            List<Long> notExistList = getNotExistIds(commodityIdList, idList);
            log.warn(FRESH_ORDER + "门店订货通用设置不存在,门店id: {} 截单时间: {} 商品idList: {}", shopId, hhMM, notExistList);
        }

        // 此次截单范围内的 filterSettingList
        List<CommodityFreshOrderODTO> filterList = new ArrayList<>(pageList.size());
        for(MdShopOrderSettingEntry settingEntry : mdShopOrderSettingList){
            // 日日鲜自动订货供应商黑名单,过滤
            if(CollectionUtils.isNotEmpty(filterSupplierList) && filterSupplierList.contains(Long.valueOf(settingEntry.getSupplierId()))){
                log.warn(FRESH_ORDER + "日日鲜自动订货供应商黑名单过滤,门店id: {} 截单时间: {} 商品id: {} 供应商id: {}", shopId, hhMM, settingEntry.getCommodityId(), settingEntry.getSupplierId());
                continue;
            }

            CommodityFreshOrderODTO freshOrderODTO = getCommodityFreshOrderODTO(shopId, storeId, commodityFreshMap, settingEntry);

            // 商品已经定过
            Boolean ifOrdered = CollectionUtils.isNotEmpty(orderedCommodityIdList) &&  orderedCommodityIdList.contains(settingEntry.getCommodityId());
            if(!ifOrdered){
                Integer logisticsModel = settingEntry.getLogisticsModel().intValue();
                // 配送比较仓库时间
                if( logisticsModel == IogisticsModelEnums.DISPATCHING.getCode()){
                    if(hhMM.equals(settingEntry.getDefaultWarehouseEndTime())){
                        filterList.add(freshOrderODTO);
                    }
                } else if(logisticsModel == IogisticsModelEnums.DIRECT_CONNECTION.getCode()){
                    //直通比较供应商时间
                    if(hhMM.equals(settingEntry.getDefaultSupplierEndTime())){
                        filterList.add(freshOrderODTO);
                    }
                }
            }
        }

        if(CollectionUtils.isEmpty(filterList)){
            List<Long> idList = mdShopOrderSettingList.stream().map(item -> item.getCommodityId()).collect(Collectors.toList());
            log.warn(FRESH_ORDER + "门店id: {} 截单时间: {} 商品idList: {} 没有可提交订单数据", shopId, hhMM, idList);
            return false;
        }

        // 转成OrderDto对象
        List<OrderDto> orderDtoList = orderSplitService.convertToOrderDto(filterList, "日日鲜自动下单",false);
        List<Long> updateCommodityIdList = filterList.stream().map(item -> item.getCommodityId()).collect(Collectors.toList());

        // 开启事务保存、更新日日鲜商品下单记录为已经下单
        commodityFreshOrderSaveService.saveCommodityFreshOrder(orderDtoList, shopId, updateCommodityIdList);
        return Boolean.TRUE;
    }

    @NotNull
    private CommodityFreshOrderODTO getCommodityFreshOrderODTO(Long shopId, Long storeId, Map<Long, CommodityFreshOrderODTO> commodityFreshMap, MdShopOrderSettingEntry settingEntry) {
        CommodityFreshOrderODTO freshOrderODTO = new CommodityFreshOrderODTO();
        freshOrderODTO.setShopId(shopId);
        freshOrderODTO.setStoreId(storeId);
        freshOrderODTO.setCommodityId(settingEntry.getCommodityId());
        freshOrderODTO.setQuantity(commodityFreshMap.get(settingEntry.getCommodityId()).getQuantity());
        freshOrderODTO.setOrderTime(getBeginDeliveryTimeRange(settingEntry.getDeleveryTimeRange()));
        freshOrderODTO.setLogisticsModel(settingEntry.getLogisticsModel().intValue());
        freshOrderODTO.setSupplierId(Long.valueOf(settingEntry.getSupplierId()));
        freshOrderODTO.setWarehouseId(Long.valueOf(settingEntry.getWarehouseId()));
        freshOrderODTO.setDeliveryBatch(DeliveryBatchTypeEnum.ONE_BATCH.getCode().toString());
        freshOrderODTO.setDeleveryTimeRange(settingEntry.getDeleveryTimeRange());
        freshOrderODTO.setEnterpriseId(78L);
        freshOrderODTO.setUserId(-1L);
        freshOrderODTO.setCreateName("系统");
        return freshOrderODTO;
    }

    public String getBeginDeliveryTimeRange(String deliveryTimeRange){
        String timeRange  = deliveryTimeRange.split("-")[0];
        Calendar calendar = Calendar.getInstance();
        if(timeRange.equals("0")){
            calendar.add(Calendar.DATE, 1);
        }else {
            calendar.add(Calendar.DATE,Integer.valueOf(timeRange));
        }
        return DateUtil.getDateFormate(calendar.getTime(),"yyyy-MM-dd");
    }

    @NotNull
    private List<Long> getNotExistIds(List<Long> commodityIdList, List<Long> idList) {
        List<Long> notExistList = new ArrayList<>();
        commodityIdList.forEach(commodityId -> {
            if (!idList.contains(commodityId)) {
                notExistList.add(commodityId);
            }
        });
        return notExistList;
    }
}
