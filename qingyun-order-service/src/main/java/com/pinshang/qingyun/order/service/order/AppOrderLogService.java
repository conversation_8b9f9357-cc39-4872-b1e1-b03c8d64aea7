package com.pinshang.qingyun.order.service.order;

import com.pinshang.qingyun.base.api.XdaTokenInfo;
import com.pinshang.qingyun.base.util.FastThreadLocalUtil;
import com.pinshang.qingyun.box.utils.DateUtil;
import com.pinshang.qingyun.common.dto.AppIdentificationInfoODTO;
import com.pinshang.qingyun.common.service.DeviceTokenClient;
import com.pinshang.qingyun.order.mapper.order.AppOrderLogMapper;
import com.pinshang.qingyun.order.model.order.AppOrderLog;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;

import java.util.Date;

/**
 * @Author: sk
 * @Date: 2025/3/4
 */
@Slf4j
@Service
public class AppOrderLogService {

    @Autowired
    private AppOrderLogMapper appOrderLogMapper;
    @Autowired
    private DeviceTokenClient deviceTokenClient;

    /**
     * 保存设备信息
     */
    public void saveAppOrderLog(Long orderId, Long storeId, Date orderTime){
        XdaTokenInfo xdaTokenInfo = FastThreadLocalUtil.getXDA();

        if(xdaTokenInfo != null && StringUtils.isNotBlank(xdaTokenInfo.getToken())) {
            AppIdentificationInfoODTO identification = deviceTokenClient.queryAppIdentificationByDeviceToken(xdaTokenInfo.getToken());
            if(identification != null) {
                AppOrderLog appOrderLog = AppOrderLog.builder()
                        .orderId(orderId)
                        .storeId(storeId)
                        .deviceId(identification.getDeviceId())
                        .systemType(identification.getSystemType())
                        .systemVersion(identification.getSystemVersion())
                        .appVersionName(xdaTokenInfo.getAppVersion())
                        .orderTime(orderTime)
                        .createTime(new Date())
                        .build();

                appOrderLogMapper.insert(appOrderLog);
            }else {
                log.info("查询设备信息失败 token {}", xdaTokenInfo.getToken());
            }
        }
    }

    /**
     * 预订单没有支付成功，订单被自动取消。则删除下单设备记录
     * @param orderId
     */
    @Async
    public void deleteAppOrderLogByOrderId(Long orderId){
        String nowDate = DateUtil.getDateFormate(new Date(), "yyyy-MM-dd");
        Example ex = new Example(AppOrderLog.class);
        ex.createCriteria().andGreaterThan("createTime", nowDate + " 00:00:00")
                .andLessThan("createTime", nowDate + " 23:59:59")
                .andEqualTo("orderId", orderId);
        appOrderLogMapper.deleteByExample(ex);
    }
}
