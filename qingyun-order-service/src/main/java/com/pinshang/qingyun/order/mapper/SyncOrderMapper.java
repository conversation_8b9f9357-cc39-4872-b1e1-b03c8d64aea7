package com.pinshang.qingyun.order.mapper;

import com.pinshang.qingyun.order.model.order.DeliveryTime;
import com.pinshang.qingyun.order.model.order.Order;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Update;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.util.List;

@Mapper
@Repository
public interface SyncOrderMapper {

	List<Order> listTomorrowOrderId(@Param("deliveryTime") DeliveryTime deliveryTime);

	Integer updateAmountPrice(@Param("orderId") Long orderId, @Param("finalAmount") BigDecimal finalAmount);

	@Update("update t_order set sync_status = 1 where id = #{orderId} and sync_status = 0")
	Integer modifySyncStatus(@Param("orderId") Long orderId);

	List<Long> listNotSyncOrderIds(@Param("deliveryTime") DeliveryTime deliveryTime);

	Integer updateNotSyncByOrderId(@Param("orderIds") List<Long> notSyncOrderIds);
}
