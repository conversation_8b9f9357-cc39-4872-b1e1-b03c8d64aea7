package com.pinshang.qingyun.order.service;

import com.pinshang.qingyun.order.enums.ProductTypeEnums;
import com.pinshang.qingyun.order.mapper.GiftModelMapper;
import com.pinshang.qingyun.order.mapper.OrderListMapper;
import com.pinshang.qingyun.order.mapper.cup.ConsumableLimitMapper;
import com.pinshang.qingyun.order.model.commodity.Commodity;
import com.pinshang.qingyun.order.model.cup.ConsumableLimit;
import com.pinshang.qingyun.order.model.gift.GiftModel;
import com.pinshang.qingyun.order.model.order.OrderList;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @Author: sk
 * @Date: 2024/7/18
 */
@Slf4j
@Service
public class GiftModelService {

    @Autowired
    GiftModelMapper giftModelMapper;

    public List<GiftModel> findGiftModelByStoreId(Long storeId,String orderTime){
        return giftModelMapper.findGiftModelByStoreId(storeId,orderTime);
    }


}
