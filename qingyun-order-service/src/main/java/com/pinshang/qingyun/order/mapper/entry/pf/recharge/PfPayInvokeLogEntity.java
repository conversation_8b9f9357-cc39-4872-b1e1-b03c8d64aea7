package com.pinshang.qingyun.order.mapper.entry.pf.recharge;

import java.util.Date;

import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;

import lombok.Data;

/**
 * <AUTHOR>
 * @Date 2019/11/26 13:58
 */
@Data
@Entity
@Table(name = "t_pf_pay_invoke_log")
public class PfPayInvokeLogEntity {
    @Id
    private Long id;
    /**
     * 订单或退货单 code
     */
    private String referCode;
    /**
     * 1=支付查询，2=退款查询，3=预支付，4=退款，5=支付
     */
    private Integer referType;
    /**
     * 1=请求，2=响应
     */
    private Integer isRequest;
    /**
     * json格式的支付请求或支付响应数据
     */
    private String data;
    private Date createTime;
}
