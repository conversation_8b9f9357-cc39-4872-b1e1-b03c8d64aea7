package com.pinshang.qingyun.order.mapper.entry.payType;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * Created by Ruansi on 2019/08/15.
 */
@Data
public class PayTypeAndTipEntry {

    private Long id;
    @ApiModelProperty("支付方式")
    private Long payType;
    @ApiModelProperty("支付类型编号")
    private String code;
    @ApiModelProperty("支付类型名称")
    private String name;
    @ApiModelProperty("启用状态，0=停用，1=启用")
    private Integer status;
    //所有的促销语
    List<PayTypeTipEntry> payTypeAndTips;
    List<String> tips;
}
