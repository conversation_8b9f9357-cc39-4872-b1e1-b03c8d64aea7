package com.pinshang.qingyun.order.service.recharge;

import com.pinshang.qingyun.base.api.ApiErrorCodeEnum;
import com.pinshang.qingyun.base.api.ApiResult;
import com.pinshang.qingyun.base.api.XdaTokenInfo;
import com.pinshang.qingyun.base.configure.expand.QYAssert;
import com.pinshang.qingyun.base.enums.*;
import com.pinshang.qingyun.base.enums.pay.AppCodeEnum;
import com.pinshang.qingyun.base.enums.pay.PayTypeIntEnum;
import com.pinshang.qingyun.base.enums.xd.XdPayTypeEnum;
import com.pinshang.qingyun.base.util.FastThreadLocalUtil;
import com.pinshang.qingyun.base.util.OrderCodeUtil;
import com.pinshang.qingyun.box.utils.*;
import com.pinshang.qingyun.common.dto.DictionaryODTO;
import com.pinshang.qingyun.common.dto.SmsMessageIDTO;
import com.pinshang.qingyun.common.service.DictionaryClient;
import com.pinshang.qingyun.common.service.SmsMessageClient;
import com.pinshang.qingyun.kafka.enums.KafkaMessageOperationTypeEnum;
import com.pinshang.qingyun.order.bo.StoreRechargeBO;
import com.pinshang.qingyun.order.config.LockConstants;
import com.pinshang.qingyun.order.constant.VersionConstant;
import com.pinshang.qingyun.order.dto.PrePayBillODTO;
import com.pinshang.qingyun.order.dto.PrePayOrderODTO;
import com.pinshang.qingyun.order.mapper.*;
import com.pinshang.qingyun.order.mapper.entry.payType.PayTypeAndTipEntry;
import com.pinshang.qingyun.order.mapper.entry.payType.PayTypeEntry;
import com.pinshang.qingyun.order.mapper.entry.recharge.XdaPayBillEntry;
import com.pinshang.qingyun.order.mapper.entry.recharge.XdaPayBillJobEntry;
import com.pinshang.qingyun.order.mapper.entry.recharge.XdaPayInvokeLogEntity;
import com.pinshang.qingyun.order.model.order.OrderBill;
import com.pinshang.qingyun.order.model.order.XdaPreOrder;
import com.pinshang.qingyun.order.model.order.XfOrder;
import com.pinshang.qingyun.order.model.recharge.XDAPayBill;
import com.pinshang.qingyun.order.model.shop.Shop;
import com.pinshang.qingyun.order.model.store.StorePlaceOrderLog;
import com.pinshang.qingyun.order.service.StoreRechargeService;
import com.pinshang.qingyun.order.service.WeChatSendMessageService;
import com.pinshang.qingyun.order.service.XfOrderService;
import com.pinshang.qingyun.order.service.pay.ThirdPartyPayService;
import com.pinshang.qingyun.order.service.pay.callback.ThirdPartyPayCallbackEvent;
import com.pinshang.qingyun.order.service.pay.model.PrePayReqParam;
import com.pinshang.qingyun.order.service.pay.model.PrePayResponse;
import com.pinshang.qingyun.order.service.pay.model.RefundReqParam;
import com.pinshang.qingyun.order.service.pay.model.TradeQueryParam;
import com.pinshang.qingyun.order.service.pay.model.log.ThirdPartyPayLogEvent;
import com.pinshang.qingyun.order.service.xda.v4.XdaOrderV4Service;
import com.pinshang.qingyun.order.service.xda.v4.XdaPreOrderService;
import com.pinshang.qingyun.order.util.PayTypeConvertUtil;
import com.pinshang.qingyun.order.util.RedissonLockExecutor;
import com.pinshang.qingyun.order.vo.recharge.RechargeVo;
import com.pinshang.qingyun.pay.dto.*;
import com.pinshang.qingyun.pay.service.AppPayClient;
import com.pinshang.qingyun.pay.service.PayLogClient;
import com.pinshang.qingyun.store.dto.storePlaceOrder.StoreIsRechargeODTO;
import com.pinshang.qingyun.store.service.StorePlaceOrderClient;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.aop.framework.AopContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.entity.Example;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2019/11/24 20:43
 */
@Component
@Slf4j
public class RechargeService {
    @Autowired
    private ThirdPartyPayService thirdPartyPayService;
    @Autowired
    private XdaPayBillMapper xdaPayBillMapper;
    @Autowired
    private XdaPayInvokeLogMapper xdaPayInvokeLogMapper;
    @Autowired
    private DictionaryClient dictionaryClient;
    @Lazy
    @Autowired
    private AppPayClient payClient;
    @Autowired
    private RedissonClient redissonClient;

    @Autowired
    private RechargeService rechargeService;
    @Autowired
    private XfOrderService xfOrderService;
    @Autowired
    private XfOrderMapper xfOrderMapper;

    @Autowired
    private XdaOrderV4Service xdaOrderV4Service;
    @Autowired
    private StorePlaceOrderClient storePlaceOrderClient;
    @Value("${white.list.payCheck.userIds:0}")
    private String userIds;
    @Autowired
    private StorePlaceOrderLogMapper storePlaceOrderLogMapper;
    @Autowired
    private SmsMessageClient smsMessageClient;
    @Autowired
    private WeChatSendMessageService weChatSendMessageService;
    @Lazy
    @Autowired
    private XdaPreOrderService xdaPreOrderService;
    @Autowired
    private StoreRechargeService storeRechargeService;
    @Lazy
    @Autowired
    private ShopMapper shopMapper;
    @Autowired
    private OrderBillMapper orderBillMapper;
    @Lazy
    @Autowired
    private PayLogClient payLogClient;
    @Autowired
    private RedissonLockExecutor redissonLockExecutor;

    /**
     * 鲜达充值
     * @param vo
     */
    @Transactional(rollbackFor = Exception.class)
    public PrePayBillODTO xdaAppRecharge(RechargeVo vo){
        QYAssert.isTrue(vo != null,"鲜达入参不能为空！");
        QYAssert.isTrue(vo.getPayType() != null,"鲜达支付方式不能为空！");
        QYAssert.isTrue(vo.getPayAmount() != null,"鲜达充值金额不能为空！");

        StoreIsRechargeODTO storeIsRechargeODTO = storePlaceOrderClient.checkXdaStoreIsRechargeByStoreId(vo.getStoreId());
        QYAssert.isTrue(null != storeIsRechargeODTO,"登录信息有误,请重新登录！");
        QYAssert.isTrue(storeIsRechargeODTO.getSuccess(),storeIsRechargeODTO.getMessage());
        String confPayAmount = this.queryRechargeMoney(vo.getAppCode());
        //默认true 需要校验充值金额 白名单用户不校验充值金额：false，
        Boolean flag = Boolean.TRUE;
        if (StringUtils.isNotBlank(userIds)) {
            String[] userIdList= userIds.split(",");
            for (String userId : userIdList) {
                if (userId.equals(vo.getUserId().toString())){
                    flag = Boolean.FALSE;
                    break;
                }
            }
        }
        if (flag){
            vo.checkPayAmount( confPayAmount );
        }
        String payBillCode = String.valueOf(OrderCodeUtil.genAppOrderCodeLong(OrderCodeEnums.XDA_PAY_BILL_PREFIX));
        if (StringUtils.isNotBlank(vo.getBillCode())){
            QYAssert.isTrue(vo.getBillCode().length()>2,"单号前缀错误");
            QYAssert.isTrue(vo.getBillCode().substring(0,2).equals(OrderCodeEnums.XDA_PAY_BILL_PREFIX.getCode().toString()),"单号前缀错误");
            payBillCode = vo.getBillCode();
        }

        //1、生成鲜达第三方交易支付流水表（t_xda_pay_bill）数据
        XDAPayBill xdaPayBill= XDAPayBill.builder()
                .storeId(vo.getStoreId())
                .billCode(payBillCode)
                .billStatus(XSPayBillStatusEnums.WAITING_PAY.getCode())
                .payAmount(vo.getPayAmount())
                .payType(vo.getPayType().getCode())
                .referType(XdaPayReferTypeEnums.ORDER_PAY.getCode()).build();
        xdaPayBill.getInstance(vo.getUserId(),KafkaMessageOperationTypeEnum.INSERT);
        int insertNum = xdaPayBillMapper.insertXdaPayBill(xdaPayBill);
        QYAssert.isTrue(insertNum > 0,"充值失败！");
        //2、生成鲜达第三方交易支付定时补偿表（t_xda_pay_bill_job）数据
        Long xdaPayBillId = xdaPayBill.getId();
        int insertJobNum = xdaPayBillMapper.insertXdaPayBillJob(xdaPayBillId);
        QYAssert.isTrue(insertJobNum > 0,"充值失败！");
        PrePayBillODTO prePayBillODTO = this.handleThirdPartyPrePay(vo,payBillCode,xdaPayBillId);
        prePayBillODTO.setBillCode(payBillCode);
        return prePayBillODTO;
    }

    /**
     * 鲜达job
     */
    public void xdaAppRechargeJob(){
        List<XdaPayBillJobEntry> jobList = xdaPayBillMapper.findXdaPayBillJob();
        if(SpringUtil.isNotEmpty(jobList)){
            for (XdaPayBillJobEntry entry : jobList) {
                if (entry.getBillStatus() == XSPayBillStatusEnums.WAITING_PAY.getCode()) {
                    //查询是否存在已支付的订单
                    Example ex = new Example(XDAPayBill.class);
                    ex.createCriteria().andEqualTo("billCode", entry.getBillCode()).andEqualTo("billStatus", XSPayBillStatusEnums.PAY_FINISHED.getCode());
                    List<XDAPayBill> entries = xdaPayBillMapper.selectByExample(ex);
                    if (SpringUtil.isEmpty(entries)) {
                        TradeQueryParam tradeQueryParam = new TradeQueryParam(entry);
                        QueryODTO queryODTO = thirdPartyPayService.tradeQuery(tradeQueryParam, AppCodeEnum.XDA, prepayCreationLogEvent -> rechargeService.handleLogEvent(prepayCreationLogEvent));
                        if (queryODTO.getException() == null && queryODTO.getBizOk()) {
                            ThirdPartyPayCallbackEvent payCallbackEvent = ThirdPartyPayCallbackEvent.build(queryODTO.getBillCode()
                                    , entry.getBillCode(), null, true, XdPayTypeEnum.getByCode(entry.getPayType()), queryODTO.getPayTime(), queryODTO.getAttach()).withoutLog();
                            rechargeService.callBackEvent(payCallbackEvent);
                            continue;
                        } else {
                            //  订单关闭，或者订单不存在处理
                            log.warn("xdaAppRechargeJob订单未支付 bill{}", JsonUtil.java2json(entry));
                        }
                    }
                }
                xdaPayBillMapper.deleteXdaPayBillJob(entry.getPayBillId());
            }
        }
    }

    /**
     * 手工退款
     */
    public ApiResult refundAllAmountTest(String billCode){
        //1、查询订单状态
        List<XdaPayBillEntry> payBillList = xdaPayBillMapper.findXdaPayBillByIds(Collections.singletonList(billCode));
        QYAssert.isTrue(SpringUtil.isNotEmpty(payBillList),"未查询到订单信息");
        //退款金额
        BigDecimal refundAmount = BigDecimal.ZERO;
        XdaPayBillEntry rehargePayBill = null;
        BigDecimal payBillAmount = BigDecimal.ZERO;
        for (XdaPayBillEntry payBill : payBillList) {
            if (payBill.getReferType().equals(XdaPayReferTypeEnums.ORDER_PAY.getCode())) {
                if (payBill.getBillStatus().equals(XSPayBillStatusEnums.PAY_FINISHED.getCode())) {
                    refundAmount = payBill.getPayAmount();
                    rehargePayBill = payBill;
                } else {
                    return ApiResult.fail("充值未完成，无法退款！");
                }
            } else {
                payBillAmount = payBillAmount.add(payBill.getPayAmount());
            }
        }
        refundAmount = refundAmount.subtract(payBillAmount);
        if(refundAmount.compareTo(BigDecimal.ZERO) > 0){
            RefundReqParam refundReqParam = new RefundReqParam(rehargePayBill);
            refundReqParam.setRefundAmount(refundAmount);
            RefundODTO refundODTO = thirdPartyPayService.refund(refundReqParam, prepayCreationLogEvent -> rechargeService.handleLogEvent(prepayCreationLogEvent));

            if(refundODTO.getException() == null && refundODTO.getBizOk()){
                //1、生成鲜达第三方交易支付流水表（t_xda_pay_bill）数据
                XDAPayBill xdaPayBill=    XDAPayBill.builder()
                        .storeId(rehargePayBill.getStoreId())
                        .billCode(rehargePayBill.getBillCode())
                        .billStatus(XSPayBillStatusEnums.PAY_FINISHED.getCode())
                        .payAmount(refundAmount)
                        .payType(XdPayTypeEnum.getByCode(rehargePayBill.getPayType()).getCode())
                        .referType(XdaPayReferTypeEnums.ARTIFICIAL_PAY.getCode())
                        .payTime(new Date()).build();
                xdaPayBill.getInstance(0L,KafkaMessageOperationTypeEnum.INSERT);
                xdaPayBillMapper.insertXdaPayBill(xdaPayBill);
            }else{
                return ApiResult.ok("退款失败，请稍后尝试！");
            }
        }else{
            return ApiResult.fail("当前订单无可退金额！");
        }
        return ApiResult.ok("鲜达手工退款成功！");
    }

    /**
     * 查询有效的支付方式及促销语
     * @return
     */
    public PayTypeEntry queryValidPayMethodAndTip(){
        List<PayTypeAndTipEntry> payTypeList = new ArrayList<>();
        List<PayTypeODTO>  payTypeODTOS= payClient.findAllByAppCode(AppCodeEnum.XDA.getCode());
        payTypeODTOS=  payTypeODTOS.stream().sorted(Comparator.comparing(PayTypeODTO::getSortNum)).collect(Collectors.toList());
        //是否添加了聚合微信小程序
        AtomicReference<Boolean> flag = new AtomicReference<>(false);
        payTypeODTOS.forEach(i -> {
                    PayTypeAndTipEntry payTypeAndTipEntry = new PayTypeAndTipEntry();
                    payTypeAndTipEntry.setPayType((long) PayTypeConvertUtil.payTypeTOXdaPayType(i.getPayType()).getCode());
                    payTypeAndTipEntry.setTips(i.getTips());
                    payTypeAndTipEntry.setName(i.getName());
                    payTypeAndTipEntry.setStatus(YesOrNoEnums.YES.getCode());
                    //聚合微信小程序支付需要判断版本
                    if (XdPayTypeEnum.UNION_MINI.getCode() == payTypeAndTipEntry.getPayType()) {
                        //判断版本
                        if (VersionUtils.compareVersions(VersionConstant.UNION_MINI_PAY_VERSION,FastThreadLocalUtil.getXDA().getAppVersion())) {
                            return;
                        }
                        //添加了聚合微信小程序
                        flag.set(true);
                    }
                    //TODO code
                    //payTypeAndTipEntry.setCode();
                    payTypeList.add(payTypeAndTipEntry);
                }
        );
        //如果添加了聚合微信小程序
        if (flag.get()){
            //移除微信支付
            payTypeList.removeIf(item -> XdPayTypeEnum.WECHAT.getCode()== item.getPayType());
        }
        List<DictionaryODTO> rechargeList = queryRechargeDetail();
        QYAssert.isTrue(SpringUtil.isNotEmpty(payTypeList) && SpringUtil.isNotEmpty(rechargeList),"获取充值方式失败！");
        return new PayTypeEntry(payTypeList,rechargeList);
    }

    /**
     * 获取最小充值金额
     * */
    public String queryRechargeMoney(String appCode) {
        // 鲜达：Android、iOS、小程序
        if ("50003".equals(appCode) || "60003".equals(appCode) || "60004".equals(appCode)) {
            // 鲜达充值，放开最低金额限制
            return "0";
        }

        List<DictionaryODTO> rechargeList = this.queryRechargeDetail();
        QYAssert.isTrue(SpringUtil.isNotEmpty(rechargeList),"获取充值限额信息失败！");
        String confPayAmount = null;
        for (DictionaryODTO item : rechargeList) {
            if("money".equals(item.getOptionCode())){
                confPayAmount = item.getOptionValue();
            }
        }
        return confPayAmount;
    }

    /**
     * 获取充值说明
     * */
    public List<DictionaryODTO> queryRechargeDetail() {
        List<DictionaryODTO> dictionaryList = dictionaryClient.querySubDictionaryByParentOptionCode("xda_recharge");
        if(dictionaryList != null  && dictionaryList.size() > 0){
            return dictionaryList;
        }
        return Collections.EMPTY_LIST;
    }


    @NotNull
    private PrePayBillODTO handleThirdPartyPrePay(RechargeVo vo,String payBillCode,Long xdaPayBillId){
        PrePayBillODTO bill = new PrePayBillODTO();
        PrePayReqParam prePayReqParam = new PrePayReqParam();
        prePayReqParam.setOrderCode(payBillCode);
        prePayReqParam.setPayAmount(vo.getPayAmount());
        prePayReqParam.setPayType(vo.getPayType());
        prePayReqParam.setUid(vo.getUserId());
        prePayReqParam.setJsCode(vo.getJsCode());
        prePayReqParam.setStoreId(vo.getStoreId());
        PrePayResponse prePayResponse = thirdPartyPayService.createPrePayBill(prePayReqParam,prepayCreationLogEvent -> rechargeService.handleLogEvent(prepayCreationLogEvent));
        if(prePayResponse.getException() != null){
            throw prePayResponse.getException();
        }
        //回填三方支待付单json
        try {
            this.backFillPrepayJson(prePayResponse.getPrePayJson(vo.getPayType())
                    , xdaPayBillId, vo.getPayType());
        } catch (Exception e) {
            log.warn("保存三方预支付单失败");
        }
        bill.setOriginalAppId(prePayResponse.getOriginalAppId());
        if(vo.getPayType().equals(XdPayTypeEnum.ALIPAY)){
            bill.setAlipayPrePayBill(prePayResponse.getUnionPrePayBill());
        }else if(vo.getPayType().equals(XdPayTypeEnum.WECHAT)){
            bill.setWechatPrePayBill(prePayResponse.getUnionPrePayBill());
        }else if(vo.getPayType().equals(XdPayTypeEnum.MINI)){
            bill.setWechatPrePayBill(prePayResponse.getUnionPrePayBill());
        }else if(vo.getPayType().equals(XdPayTypeEnum.UNION_PAY)){
            bill.setUnionPayPrePayBill(prePayResponse.getUnionPrePayBill());
        }else if (vo.getPayType().equals(XdPayTypeEnum.UNION_MINI)){
            bill.setWechatPrePayBill(prePayResponse.getMiniPrePayBill());
        }
        return bill;
    }

    /**
     * 回填三方预支付单json
     * @param json
     * @param payBillId
     * @param payType
     */
    public void backFillPrepayJson(String json, Long payBillId, XdPayTypeEnum payType){
        XDAPayBill xdaPayBill = new XDAPayBill();
        switch (payType){
            case ALIPAY:
                xdaPayBill.setPrepayJsonAlipay(json);
                break;
            case WECHAT:
                xdaPayBill.setPrepayJsonWechat(json);
                break;
            case MINI:
            case UNION_MINI:
                xdaPayBill.setPrepayJsonMini(json);
                break;
            case UNION_PAY:
                xdaPayBill.setPrepayJsonUnion(json);
                break;
            default:
                throw new RuntimeException("支付类型异常!");
        }
        xdaPayBill.setId(payBillId);
        xdaPayBillMapper.updateByPrimaryKeySelective(xdaPayBill);
    }


    /**
     * 支付成功后续处理
     */
    @Transactional(rollbackFor = Exception.class)
    public void paySuccessPostHandle(ThirdPartyPayCallbackEvent event, XdaPayBillEntry payBill) {
        this.addRecharge(payBill,event.getAttach());
        log.warn("修改鲜达第三方交易支付流水表 orderCode:{}",event.getOrderCode());
        XDAPayBill xdaPayBill =  XDAPayBill.builder()
                .storeId(payBill.getStoreId())
                .tradeBillCode(event.getTransactionId())
                .payTime(event.getPayTime())
                .billStatus(XSPayBillStatusEnums.PAY_FINISHED.getCode()).build();
        xdaPayBill.getInstance(1L,KafkaMessageOperationTypeEnum.UPDATE);
        xdaPayBill.setId(payBill.getId());
        xdaPayBillMapper.updateByPrimaryKeySelective(xdaPayBill);
        xdaPayBillMapper.deleteXdaPayBillJob(payBill.getId());
        //7、记录支付完成得日志
        this.handleLogEvent(new ThirdPartyPayLogEvent(event));
    }
    // 充值
    private void addRecharge(XdaPayBillEntry payBill, Attach attach) {
        // 参见 StoreRecharge
        long paymentMethod = 0L;
        if (payBill.getPayType().equals(XdPayTypeEnum.ALIPAY.getCode())){
            paymentMethod = 9131078242860601319L;
        } else if (payBill.getPayType().equals(XdPayTypeEnum.WECHAT.getCode())){
            paymentMethod = 9131078242860601318L;
        } else if (payBill.getPayType().equals(XdPayTypeEnum.UNION_MINI.getCode())){
            paymentMethod = 9131078242888821829L;
        } else if (payBill.getPayType().equals(XdPayTypeEnum.UNION_PAY.getCode())){
            paymentMethod = 9131078242860601330L;
        }
        Long receiptType = 9131078242860601331L;
        String remark;
        remark = "鲜达app充值:" + payBill.getBillCode() + ":" + new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date());
        if (attach != null && attach.getMerchantName() != null && attach.getMerchantName() != "null") {
            remark = remark.concat(":").concat(attach.getMerchantName());
        }
        StoreRechargeBO rechargeBO = StoreRechargeBO.builder()
                .tradeCode(payBill.getBillCode())
                .thirdPartyTradeCode(payBill.getBillCode())
                .money(payBill.getPayAmount().doubleValue())
                .paymentMethod(paymentMethod)
                .receiptType(receiptType)
                .storeId(payBill.getStoreId())
                .tradeTime(new Date())
                .receiptDate(new Date())
                .billType(StoreBillTypeEnums.XD_DEPOSIT.getCode())
                .remark(remark)
                .build();
        Boolean result = storeRechargeService.storeRecharge(rechargeBO);
        QYAssert.isTrue(result, "充值失败！");
    }

    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRES_NEW)
    public void handleLogEvent(ThirdPartyPayLogEvent logEvent){
        XdaPayInvokeLogEntity logEntity = new XdaPayInvokeLogEntity();
        logEntity.setCreateTime(new Date());
        logEntity.setIsRequest(logEvent.getLogType().getCode());
        logEntity.setReferType(logEvent.getOperateType().getCode());
        logEntity.setReferCode(logEvent.getOrderCode());
        if(StringUtils.isNotBlank(logEvent.getJson())){
            logEntity.setData(logEvent.getJson());
        }else {
            logEntity.setData("预支付订单无返回参数");
        }

        xdaPayInvokeLogMapper.insert(logEntity);
    }

    /**
     * 1.支付回调成功调用
     * 2.鲜达job补偿调用
     * 3.查询订单状态调用
     * @param event
     */
    public void callBackEvent(ThirdPartyPayCallbackEvent event) {
        RechargeService proxy = ((RechargeService) AopContext.currentProxy());
        RLock lock = redissonClient.getLock(LockConstants.generateCallbackLockKey(event.getOrderCode()));
        lock.lock(5L, TimeUnit.SECONDS);
        try {
            proxy.processCallBackEvent(event);
        } finally {
            if (lock.isLocked() && lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public void processCallBackEvent(ThirdPartyPayCallbackEvent event){
        List<XdaPayBillEntry> payBillList = xdaPayBillMapper.findXdaPayBillByIds(Collections.singletonList(event.getOrderCode()));
        if(SpringUtil.isNotEmpty(payBillList)){
            log.warn("callBackEvent orderCoder:{}",event.getOrderCode());
            XdaPayBillEntry payBill = payBillList.get(0);
            log.warn("订单状态BillStatus:{}",payBill.getBillStatus());
            if(payBill.getBillStatus().equals(XSPayBillStatusEnums.WAITING_PAY.getCode())){
                // 充值方法去掉try catch方法，由外层进行捕获发送通知消息
                paySuccessPostHandle(event, payBill);
            }else {
                log.error("订单状态异常orderCode:{}",event.getOrderCode());
            }
        }else {
            log.error("订单不存在orderCoder:{}",event.getOrderCode());
        }
    }

   /* @Transactional
    public void creatOrder(ThirdPartyPayCallbackEvent event){
        List<String> billCodes = new ArrayList<>();
        billCodes.add(event.getOrderCode());
        List<XdaPayBillEntry> xdaPayBillByIds = xdaPayBillMapper.findXdaPayBillByIds(billCodes);
        if(SpringUtil.isNotEmpty(xdaPayBillByIds) &&  null != xdaPayBillByIds.get(0)){
            XdaPayBillEntry xdaPay = xdaPayBillByIds.get(0);
            XfOrder xfOrder = new XfOrder();
            xfOrder.setBillCode(event.getOrderCode());
            XfOrder xf = xfOrderMapper.selectOne(xfOrder);
            if (null == xf) {
                return;
            }
            //支付成功
            if(xdaPay.getBillStatus() == XSPayBillStatusEnums.PAY_FINISHED.getCode()){
                CreateXfOrderIDTO createXfOrderIDTO = JSON.parseObject(xf.getReqJson(), CreateXfOrderIDTO.class);

                try {
                    log.error("-----开始创建订单-------------");
                    Long orderId = null;
                    if(createXfOrderIDTO.getAppVersion().compareToIgnoreCase("1.5.2") >= 0){
                        //创建订单
                        XdaCreatePrePayOrderV4IDTO xdaCreatePrePayOrderV4IDTO = BeanCloneUtils.copyTo(createXfOrderIDTO,XdaCreatePrePayOrderV4IDTO.class);
                        xdaCreatePrePayOrderV4IDTO.setIsPayPassword(Boolean.FALSE);
                        xdaCreatePrePayOrderV4IDTO.setStoreId(xf.getStoreId());
                        XdaSaveOrderODTO order4 = xdaOrderV4Service.creatOrder(xdaCreatePrePayOrderV4IDTO);
                        orderId = order4.getOrderId();
                        if(!order4.getIsSuccess() && StringUtils.isNotBlank(order4.getErrorMsg())){
                            QYAssert.isFalse(ApiErrorCodeEnum.XDA_PRE_ORDER_V4_WARN.getRemark());
                        }
                    }else {
                        //创建订单
                        XdaCreatePrePayOrderV3IDTO xdaCreatePrePayOrderV3IDTO = BeanCloneUtils.copyTo(createXfOrderIDTO,XdaCreatePrePayOrderV3IDTO.class);
                        xdaCreatePrePayOrderV3IDTO.setIsPayPassword(Boolean.FALSE);
                        xdaCreatePrePayOrderV3IDTO.setStoreId(xf.getStoreId());
                        Order order3 = xdaOrderV3Service.creatOrder(xdaCreatePrePayOrderV3IDTO);
                        orderId = order3.getId();
                    }

                    xfOrderService.updateStatus(xf.getId(),1,"创建成功", orderId);
                }catch (Exception exception){
                    log.error("-----订单创建失败---新增订单报错",exception);
                    String errorDetailMsg =  null == exception.getMessage()?"创建订单报错":exception.getMessage();
                    xfOrderService.updateStatus(xf.getId(),2,errorDetailMsg, null);

                    // 鲜达卡点支付，订单报错后扣除账户余额、支付金额原路返回、发送短信提醒
                    try{
                        xdaOrderCreateErrorDeal(xdaPay,event);
                    }catch (Exception e){
                        log.error("-----鲜达订单异常创建失败,billCode: {}, 详细信息", xdaPay.getBillCode(), e);

                        StringBuffer sb = new StringBuffer();
                        sb.append("鲜达订单异常创建失败,billCode:").append(xdaPay.getBillCode());
                        //发送微信模板信息
                        weChatSendMessageService.sendWeChatMessage(sb.toString());
                    }
                }
            }else if(xdaPay.getBillStatus() == XSPayBillStatusEnums.WAITING_PAY.getCode()){
                log.error("-----支付单状态异常-------------");
            }else {
                xfOrderService.updateStatus(xf.getId(),2,"未知原因", null);
            }
        }else {
            log.error("订单不存在orderCoder:{}",event.getOrderCode());
        }
    }*/

    /**
     * 鲜达卡点支付，订单报错后扣除账户余额、支付金额原路返回、发送短信提醒
     * 1.扣除账户余额
     * 2.支付金额原路返回
     * 3.发送短信
     * @param xdaPay
     */
    public void xdaOrderCreateErrorDeal(XdaPayBillEntry xdaPay,ThirdPartyPayCallbackEvent event) {
        // 1.扣除账户余额  需求10212 入账负
        String reverseTime = DateUtil.getDateFormate(new Date(), DateUtil.DEFAULT_DATE_FORMAT);
        String merchantName = "";
        if (Objects.nonNull(event) && Objects.nonNull(event.getAttach())) {
            Attach attach = event.getAttach();
            merchantName = attach.getMerchantName();
        }
        String remark = "鲜达订单异常/取消,扣除账户余额:" + xdaPay.getBillCode() + ":" + reverseTime + ":" + merchantName;
        StoreRechargeBO rechargeBO = StoreRechargeBO.builder()
                .tradeCode(xdaPay.getBillCode() + "999")
                .storeId(xdaPay.getStoreId())
                .money(-xdaPay.getPayAmount().doubleValue())
                .remark(remark)
                .receiptDate(xdaPay.getPayTime())
                .tradeTime(new Date())
                .thirdPartyTradeCode(xdaPay.getBillCode() + "999")
                .billType((StoreBillTypeEnums.XD_ORDER_ERROR_CANCEL_DEDUCTION.getCode()))
                .build();
        storeRechargeService.storeRecharge(rechargeBO);

        //2.支付金额原路返回
        reversePay(xdaPay);

        // 3.发送短信
        StorePlaceOrderLog storePlaceOrderLog = storePlaceOrderLogMapper.selectStorePlaceOrderLogByStoreId(xdaPay.getStoreId());
        if(storePlaceOrderLog != null && StringUtils.isNotBlank(storePlaceOrderLog.getPhoneNumber())){
            SmsMessageIDTO smsMessageIDTO = new SmsMessageIDTO();
            smsMessageIDTO.setTypeCode("XDA_APP_CREATE_ORDER_ERROR_CODE");
            smsMessageIDTO.setPhone(storePlaceOrderLog.getPhoneNumber());
            smsMessageIDTO.setContent("您的订单提交失败，支付金额将原路返回");
            smsMessageClient.sendOneMessage(smsMessageIDTO);
        }
    }



    /**
     * 支付金额原路反还
     * @param xdaPay
     */
    public void reversePay(XdaPayBillEntry xdaPay) {
        List<XdaPayBillEntry> bills = xdaPayBillMapper.findXdaPayBillByIds(Collections.singletonList(xdaPay.getBillCode()));
        xdaPay.setPayType(bills.get(0).getPayType());

        XdPayTypeEnum xdPayTypeEnum = XdPayTypeEnum.getByCode(xdaPay.getPayType());
        PayTypeIntEnum payTypeIntEnum = PayTypeConvertUtil.xdPayTypeEnumToPayTypeIntEnum(xdPayTypeEnum);
        RefundIDTO idto = new RefundIDTO();
        idto.setBillCode(xdaPay.getBillCode());
        idto.setRefundOrderCode(xdaPay.getBillCode() + "999");
        idto.setRefundAmount(xdaPay.getPayAmount());
        idto.setRefundReason("鲜达订单,原路退款");
        idto.setShopId(thirdPartyPayService.getCompanyIdByStoreId(xdaPay.getStoreId()));
        idto.setPayAmount(xdaPay.getPayAmount());
        idto.setPayType(payTypeIntEnum.getCode());
        idto.setAppCode(AppCodeEnum.XDA.getCode());
        idto.setUserId(xdaPay.getCreateId());
        RefundODTO refundODTO = payClient.refund(idto);
    }

    public Integer queryBillStatus(String billCode){
        //1、查询订单状态
        List<XdaPayBillEntry> payBillList = xdaPayBillMapper.findXdaPayBillByIds(Collections.singletonList(billCode));
        if (CollectionUtils.isEmpty(payBillList)){
            return 0;
        }
        XdaPayBillEntry xdaPayBillEntry=payBillList.get(0);
        if (xdaPayBillEntry.getBillStatus().equals(XSPayBillStatusEnums.PAY_FINISHED.getCode())){
            return 1;
        }else {
            TradeQueryParam tradeQueryParam = new TradeQueryParam();
            tradeQueryParam.setBillCode(xdaPayBillEntry.getBillCode());
            tradeQueryParam.setTradeBillCode(xdaPayBillEntry.getTradeBillCode());
            tradeQueryParam.setPayType( XdPayTypeEnum.getByCode(xdaPayBillEntry.getPayType()));
            tradeQueryParam.setStoreId(xdaPayBillEntry.getStoreId());

            QueryODTO queryODTO = thirdPartyPayService.tradeQuery(tradeQueryParam, AppCodeEnum.XDA, prepayCreationLogEvent -> rechargeService.handleLogEvent(prepayCreationLogEvent));
            if(queryODTO.getException() == null && queryODTO.getBizOk()){
                ThirdPartyPayCallbackEvent payCallbackEvent = ThirdPartyPayCallbackEvent.build(queryODTO.getBillCode()
                        , xdaPayBillEntry.getBillCode(), null, true, XdPayTypeEnum.getByCode(xdaPayBillEntry.getPayType()), queryODTO.getPayTime(),queryODTO.getAttach()).withoutLog();
                rechargeService.callBackEvent(payCallbackEvent);
                return 1;
            }
        }
        return 2;
    }

    public String getNewBillCode() {
        String billCode = redissonLockExecutor.exec(
                LockConstants.XDA_PAY_BILL_GENERATOR,
                300,
                500,
                TimeUnit.MILLISECONDS,
                () -> OrderCodeUtil.genAppOrderCodeLong(OrderCodeEnums.XDA_PAY_BILL_PREFIX).toString()
        );
        QYAssert.isTrue(StringUtils.isNotBlank(billCode), "单号生成失败,请重试");
        return billCode;
    }

    public PrePayOrderODTO getPrePay(){
        PrePayIDTO prePayIDTO = new PrePayIDTO();
        XdaTokenInfo tokenInfo = FastThreadLocalUtil.getXDA();
        prePayIDTO.setAppCode(AppCodeEnum.XDA.getCode());
        prePayIDTO.setPayType(PayTypeIntEnum.UNIONPAY_MINI.getCode());
        prePayIDTO.setShopId(thirdPartyPayService.getCompanyIdByStoreId(tokenInfo.getStoreId()));
        String  originalAppId = payClient.getOriginalAppId(prePayIDTO);
        PrePayOrderODTO prePayOrderODTO = new PrePayOrderODTO();
        prePayOrderODTO.setOrderId(getNewBillCode());
        prePayOrderODTO.setOriginalAppId(originalAppId);
        return prePayOrderODTO;
    }


    /**
     * 获取预订单billCode
     * @param storeId
     * @return
     */
    public String getPreOrderBillCode(Long storeId){
        XdaPreOrder xdaPreOrder = xdaPreOrderService.queryNoPayXdaPreOrder(storeId);
        if(xdaPreOrder != null){
            return xdaPreOrder.getBillCode();
        }
        return null;
    }

    /**
     * 银联C扫B充值
     * @param shopId
     * @param orderCode
     * @param amount
     */
    public void cToBAddRecharge(Long shopId, String orderCode, BigDecimal amount, Date payTime) {
        log.warn("银联C扫B 充值参数， billCode {} shopId {}  amount {} ", orderCode, shopId, amount);
        try{
            if(shopId == null  || amount == null){
                log.warn("银联C扫B 回调异常，shopId or amount is null， billCode {}", orderCode);
                return;
            }

            Shop shop = shopMapper.selectByPrimaryKey(shopId);
            Example ex = new Example(OrderBill.class);
            ex.createCriteria().andEqualTo("storeId", shop.getStoreId())
                    .andEqualTo("orderId", Long.valueOf(orderCode));
            List<OrderBill> orderBillList = orderBillMapper.selectByExample(ex);

            if(CollectionUtils.isEmpty(orderBillList)){
                //充值
                StoreRechargeBO rechargeBO = StoreRechargeBO.builder()
                        .tradeCode(orderCode)
                        .thirdPartyTradeCode(orderCode)
                        .receiptDate(payTime)
                        .money(amount.doubleValue())
                        .storeId(shop.getStoreId())
                        .tradeTime(new Date())
                        .receiptDate(new Date())
                        .billType(StoreBillTypeEnums.SHOP_WEB_DEPOSIT.getCode())
                        .remark("<--门店web 充值：" + orderCode + " -->")
                        .userId(-1L)
                        .build();
                storeRechargeService.storeRecharge(rechargeBO);
            }else {
                log.warn("银联C扫B 回调异常，订单已存在，billCode {}", orderCode);
            }

        }catch (Exception e){
            log.error("银联C扫B 回调异常" , e);
            String param = "billCode: " + orderCode + "shopId: " + shopId + "amount" + amount;
            weChatSendMessageService.sendWeChatMessage("银联C扫B 回调异常，" + param);
        }
    }

    public Boolean savePayLog(SavePayLogIDTO payLog) {
        return payLogClient.savePayLog(payLog);
    }
}
