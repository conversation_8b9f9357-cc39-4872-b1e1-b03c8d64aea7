package com.pinshang.qingyun.order.controller;

import com.pinshang.qingyun.base.util.FastThreadLocalUtil;
import com.pinshang.qingyun.order.service.MockOrderService;
import com.pinshang.qingyun.order.vo.mock.MockOrderVo;
import io.swagger.annotations.ApiOperation;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;


@RestController
@RequestMapping("/mockOrder")
public class MockOrderController {
    @Autowired
    private MockOrderService mockOrderService;
    @Autowired
    private RedissonClient redissonClient;

    @ApiOperation("快速下单进大仓，不考虑客户产品价格方案")
    @PostMapping("/mockCreateOrder")
    public Integer mockCreateOrder(@RequestBody MockOrderVo vo) {
        RLock lock = redissonClient.getLock("MOCK_CREATE_ORDER_"+ vo.getOrderTime());
        if (lock.tryLock()) {
            try {
                Long userId = FastThreadLocalUtil.getQY().getUserId();
                vo.setUserId(userId);
                mockOrderService.mockCreateOrder(vo);
            }finally {
                lock.unlock();
            }
        }
        return 1;
    }

}
