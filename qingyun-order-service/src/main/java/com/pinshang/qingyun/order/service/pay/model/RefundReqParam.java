package com.pinshang.qingyun.order.service.pay.model;

import com.pinshang.qingyun.base.enums.xd.XdPayTypeEnum;
import com.pinshang.qingyun.order.mapper.entry.recharge.XdaPayBillEntry;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * 第三方退款请求参数
 * <AUTHOR>
 * @Date 2019/11/26 21:35
 */
@Data
@NoArgsConstructor
public class RefundReqParam {
    private String orderCode;
    /**
     * 补差时没有退货单填orderCode
     */
    private String refundOrderCode;
    /**
     * 退款金额,0.01
     */
    private BigDecimal refundAmount;

    private String refundReason;
    /**
     * 支付宝退款选填
     */
    private Long userId;
    /**
     * 支付宝退款选填
     */
    private Long shopId;
    /**
     * 订单的->实付金额,微信支付必填,0.01
     */
    private BigDecimal payAmount;

    private XdPayTypeEnum payType;
    private Long storeId;

    public RefundReqParam(XdaPayBillEntry payBill){
        this.orderCode = payBill.getBillCode();
        this.refundOrderCode = payBill.getBillCode();
        this.refundReason = "手工退款";
        this.payType = XdPayTypeEnum.getByCode(payBill.getPayType());
        this.payAmount = payBill.getPayAmount();
        this.storeId = payBill.getStoreId();
    }
}

