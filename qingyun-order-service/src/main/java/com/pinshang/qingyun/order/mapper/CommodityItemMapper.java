package com.pinshang.qingyun.order.mapper;

import com.pinshang.qingyun.base.mybatis.MyMapper;
import com.pinshang.qingyun.order.mapper.entry.commodity.CommodityItemEntry;
import com.pinshang.qingyun.order.model.commodity.CommodityItem;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * Created by hhf on 2019/9/3.
 */
@Mapper
@Repository
public interface CommodityItemMapper extends MyMapper<CommodityItem> {

    /**
     * 根据商品id 查询子商品集合
     * @param commodityId
     * @return
     */
    List<CommodityItemEntry> findCommodityItemListByCommodityId(@Param("commodityId")Long commodityId);

    /**
     * 根据商品id批量查询子商品集合
     */
    List<CommodityItemEntry> findCommodityItemListByCommodityIdList(@Param("commodityIdList")List<Long> commodityIdList);

    /**
     * 根据子商品id 查询主商品集合
     * @param commodityItemId
     * @return
     */
    List<CommodityItemEntry> findCommodityMasterListByCommodityItemId(@Param("commodityItemId")Long commodityItemId);

}
