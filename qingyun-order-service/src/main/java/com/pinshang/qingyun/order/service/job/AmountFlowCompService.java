package com.pinshang.qingyun.order.service.job;

import com.pinshang.qingyun.base.enums.OrderStatusEnums;
import com.pinshang.qingyun.base.spring.SpringBeanFinder;
import com.pinshang.qingyun.box.utils.DateUtil;
import com.pinshang.qingyun.box.utils.SpringUtil;
import com.pinshang.qingyun.order.constant.ThreadPoolBeanConstants;
import com.pinshang.qingyun.order.dto.job.OrderBillStoreBalanceDTO;
import com.pinshang.qingyun.order.dto.job.OrderBillStoreChargingDTO;
import com.pinshang.qingyun.order.mapper.entry.job.StoreOrderAmountEntry;
import com.pinshang.qingyun.order.service.OrderBillService;
import com.pinshang.qingyun.order.service.OrderService;
import com.pinshang.qingyun.order.service.WeChatSendMessageService;
import com.pinshang.qingyun.renderer.service.IRenderService;
import com.pinshang.qingyun.shop.service.StoreClient;
import com.pinshang.qingyun.store.dto.storeNew.SelectStoreBasicInfoListIDTO;
import com.pinshang.qingyun.store.dto.storeNew.StoreBasicInfoODTO;
import com.pinshang.qingyun.store.dto.storeSettlement.StoreSettlementCollectODTO;
import com.pinshang.qingyun.store.service.StoreNewClient;
import com.pinshang.qingyun.store.service.StoreSettlementNewClient;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@Slf4j
@RequiredArgsConstructor
public class AmountFlowCompService {

    private final OrderBillService orderBillService;
    private final StoreSettlementNewClient storeSettlementNewClient;
    private final OrderService orderService;
    private final Integer PRE_PAY_STORE_ACCOUNT = 1;
    private final WeChatSendMessageService weChatSendMessageService;
    private final StoreNewClient storeNewClient;

    /**
     * 金额对比有问题，方法被放弃
     * @return
     */
    @Deprecated
    public Boolean doAmountFlowComp() {

        String yesterdayStr = DateUtil.get4yMd(DateUtil.addDay(-1));

        List<StoreOrderAmountEntry> yesterdayPrePayStoreOrders = queryYesterdayStoreOrders(yesterdayStr, OrderStatusEnums.NORMAL.getCode(), PRE_PAY_STORE_ACCOUNT);
        if (SpringUtil.isNotEmpty(yesterdayPrePayStoreOrders)) {

            List<Long> storeIds = yesterdayPrePayStoreOrders.stream().map(StoreOrderAmountEntry::getStoreId).collect(Collectors.toList());

            Map<String,String> storeCodeMap = getStoreCodeMap(storeIds);
            Map<Long, BigDecimal> storeChargingMap = getStoreChargingMap(yesterdayStr, storeIds);
            Map<Long, BigDecimal> storeOrderAmountMap = yesterdayPrePayStoreOrders.stream().collect(Collectors.toMap(StoreOrderAmountEntry::getStoreId, storeOrderAmountEntry -> storeOrderAmountEntry.getOrderAmount().add(storeOrderAmountEntry.getFreightAmount())));
            Map<Long, StoreSettlementCollectODTO> storeBalanceMap = getStoreBalanceMap(storeIds);

            long currentTimeMillis = System.currentTimeMillis();

            // 获取线程池
            ThreadPoolTaskExecutor threadPoolExecutor = (ThreadPoolTaskExecutor) SpringBeanFinder.getBean(ThreadPoolBeanConstants.ORDER_THREADPOOL);

            for (Long storeId : storeIds) {
                threadPoolExecutor.execute(
                        () -> {
                            BigDecimal yesterdayBeforeLatestStoreBalance = orderBillService.queryOrderBillStoreBalance(yesterdayStr, storeId);
                            BigDecimal yesterdayStoreChargingMoney = Optional.ofNullable(storeChargingMap.get(storeId)).orElse(BigDecimal.ZERO);
                            BigDecimal yesterdayStoreOrderAmount =  storeOrderAmountMap.get(storeId);
                            BigDecimal storeSettlementStoreBalance = Optional.ofNullable(storeBalanceMap.get(storeId)).map(StoreSettlementCollectODTO::getCollectPrice).map(BigDecimal::valueOf).orElse(BigDecimal.ZERO);

                            if (yesterdayBeforeLatestStoreBalance
                                    .add(yesterdayStoreChargingMoney)
                                    .subtract(yesterdayStoreOrderAmount)
                                    .compareTo(storeSettlementStoreBalance) != 0) {
                                String storeCode = storeCodeMap.get(String.valueOf(storeId));
                                log.warn("金额不一致，客户ID[{}],客户CODE[{}]", storeId,storeCode);
                                weChatSendMessageService.sendWeChatMessage("预付客户，充值消费余额比金额不一致,客户ID[" + storeId + "],客户CODE[" + storeCode + "]");
                            }
                        });

            }

        }
        return Boolean.TRUE;
    }

    private Map<Long, BigDecimal> getStoreChargingMap(String yesterdayStr, List<Long> storeIds) {

        List<OrderBillStoreChargingDTO> orderBillStoreChargingDTOS = orderBillService.queryChargingOrderBill(yesterdayStr, storeIds);
        Map<Long, BigDecimal> yesterdayStoreChargingMap = orderBillStoreChargingDTOS.stream()
                .collect(Collectors.groupingBy(
                        OrderBillStoreChargingDTO::getStoreId,
                        Collectors.mapping(
                                OrderBillStoreChargingDTO::getChargingMoney,
                                Collectors.reducing(BigDecimal.ZERO, BigDecimal::add)
                        )
                ));
        return yesterdayStoreChargingMap;
    }

    private Map<Long, StoreSettlementCollectODTO> getStoreBalanceMap(List<Long> storeIds) {

        List<StoreSettlementCollectODTO> storeSettlementCollectODTOS = storeSettlementNewClient.listStoreSettlementByStoreIds(storeIds);
        Map<Long, StoreSettlementCollectODTO> storeSettlementStoreBalanceMap = storeSettlementCollectODTOS.stream().collect(Collectors.toMap(StoreSettlementCollectODTO::getStoreId, Function.identity()));
        return storeSettlementStoreBalanceMap;

    }

    private Map<String, String> getStoreCodeMap(List<Long> storeIds) {

        SelectStoreBasicInfoListIDTO idto = new SelectStoreBasicInfoListIDTO();
        idto.setStoreIdList(storeIds);
        List<StoreBasicInfoODTO> storeBasicInfoODTOS = storeNewClient.selectStoreBasicInfoList(idto);
        Map<String,String> storeCodeMap = storeBasicInfoODTOS.stream().collect(Collectors.toMap(StoreBasicInfoODTO::getStoreId,StoreBasicInfoODTO::getStoreCode));
        return storeCodeMap;

    }

    private List<StoreOrderAmountEntry> queryYesterdayStoreOrders(String yesterdayStr, Integer orderStatus, Integer storeAccountType) {
        return orderService.queryOrder(yesterdayStr, orderStatus, storeAccountType);
    }

    /**
     * 线程执行时间
     *
     * @param threadPool
     * @param currentTimeMillis
     */
    public void threadOver(ThreadPoolExecutor threadPool, long currentTimeMillis, String remark) {
        try {
            boolean loop = true;
            do {
                //等待所有线程执行完毕当前任务结束
                loop = !threadPool.awaitTermination(2, TimeUnit.SECONDS);//等待2秒
            } while (loop);

            if (loop != true) {
                log.info(remark + " 所有线程执行完毕");
            }

        } catch (InterruptedException e) {
            log.error(remark + " 线程执行时间异常", e);
        } finally {
            log.info(remark + " 耗时：" + (System.currentTimeMillis() - currentTimeMillis) + " 毫秒");
        }
    }


}


