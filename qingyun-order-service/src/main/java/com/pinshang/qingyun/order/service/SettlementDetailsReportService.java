package com.pinshang.qingyun.order.service;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.pinshang.qingyun.base.configure.expand.QYAssert;
import com.pinshang.qingyun.base.enums.LogisticsModelEnums;
import com.pinshang.qingyun.box.utils.SpringUtil;
import com.pinshang.qingyun.box.utils.StringUtil;
import com.pinshang.qingyun.common.dto.SystemPropODTO;
import com.pinshang.qingyun.common.service.SystemPropClient;
import com.pinshang.qingyun.order.mapper.OrderMapper;
import com.pinshang.qingyun.order.mapper.ShopMapper;
import com.pinshang.qingyun.order.mapper.StoreSettlementMapper;
import com.pinshang.qingyun.order.mapper.entry.shop.SettlementDetailsReportEntry;
import com.pinshang.qingyun.order.mapper.entry.store.StoreSettlementEntry;
import com.pinshang.qingyun.order.model.shop.Shop;
import com.pinshang.qingyun.order.vo.shop.SettlementDetailsReportVo;
import com.pinshang.qingyun.storage.dto.StockOutOrderODTO;
import com.pinshang.qingyun.storage.dto.stock.StockOutOrderTimeSearchIDTO;
import com.pinshang.qingyun.storage.service.StockOutOrderClient;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Created by hhf on 2019/1/16.
 * 结算明细报表呢
 */
@Service
public class SettlementDetailsReportService {

    @Autowired
    private OrderMapper orderMapper;
    @Autowired
    private StoreSettlementMapper storeSettlementMapper;
    @Autowired
    private StockOutOrderClient stockOutOrderClient;
    @Autowired
    private ShopMapper shopMapper;
    @Autowired
    private SystemPropClient systemPropClient;

    /**
     * 结算明细报表
     * @param vo
     * @return
     */
    public PageInfo<SettlementDetailsReportEntry> settlementDetailsReport(SettlementDetailsReportVo vo){
        QYAssert.isTrue(null != vo,"参数异常");
        QYAssert.isTrue(null != vo.getShopId()," 门店id不能为空");
        QYAssert.isTrue( SpringUtil.isNotEmpty(vo.getShopIdList())," 当前登录用户没有门店权限");
        QYAssert.isTrue( vo.getShopIdList().contains(vo.getShopId())," 当前登录用户没有该门店的权限");

        if(StringUtils.isEmpty(vo.getDeliveryTimeEnd()) && StringUtils.isEmpty(vo.getSettleTimeEnd())){
            QYAssert.isTrue(true,"请选择送货日期或结算日期");
        }
        //送货日期
        if(!StringUtil.isBlank(vo.getDeliveryTimeStart()) && !StringUtil.isBlank(vo.getDeliveryTimeEnd())){
            vo.setDeliveryTimeStart(vo.getDeliveryTimeStart()+ " 00:00:00");
            vo.setDeliveryTimeEnd(vo.getDeliveryTimeEnd()+ " 23:59:59");
        }
        Map<Long, Date> subOrderIdAndTimeMap = new HashMap<>();
        //结算日期
        if(!StringUtil.isBlank(vo.getSettleTimeStart()) && !StringUtil.isBlank(vo.getSettleTimeEnd())){
            vo.setSettleTimeStart(vo.getSettleTimeStart()+ " 00:00:00");
            vo.setSettleTimeEnd(vo.getSettleTimeEnd()+ " 23:59:59");
            List<Long> storeIdList = new ArrayList<>();
            //根据门店id 查询 客户id
            Shop shop = shopMapper.selectByPrimaryKey(vo.getShopId());
            storeIdList.add(shop.getStoreId());

            //配送直通订单的结算日期范围内的订单集合
            StockOutOrderTimeSearchIDTO stockOutOrderTime = new StockOutOrderTimeSearchIDTO();
            stockOutOrderTime.setSettleTimeEnd(vo.getSettleTimeEnd());
            stockOutOrderTime.setSettleTimeStart(vo.getSettleTimeStart());
            stockOutOrderTime.setStoreIdList(storeIdList);

            List<StockOutOrderODTO> stockOutOrderTimeList = stockOutOrderClient.findStockOutOrderTimeBySubOrderCodeList(stockOutOrderTime);
            if(SpringUtil.isNotEmpty(stockOutOrderTimeList)){
                List<Long> subOrderIdList = stockOutOrderTimeList.stream().map(e->e.getSubOrderId()).collect(Collectors.toList());
                if(null != subOrderIdList){
                    vo.setSubOrderIdList(subOrderIdList);
                }
                //订单编码-出库时间
                subOrderIdAndTimeMap = stockOutOrderTimeList.stream().collect(Collectors.toMap(StockOutOrderODTO::getSubOrderId, StockOutOrderODTO::getOutTime));
            }else{
                List<Long> subOrderIdList = new ArrayList<>();
                subOrderIdList.add(0L);
                vo.setSubOrderIdList(subOrderIdList);
            }
        }
        //客户类型集合 注释原因: http://192.168.0.213/zentao/story-view-7357.html
        /*List<String> storeTypeIdList = this.getStoreTypeIdList();
        if(SpringUtil.isNotEmpty(storeTypeIdList)){
            List<Long> ids = storeTypeIdList.stream().map(Long ::valueOf).collect(Collectors.toList());
            vo.setStoreTypeIdList(ids);
        }*/

        //分页信息
        PageInfo<SettlementDetailsReportEntry> pageDate = PageHelper.startPage(vo.getPageNo(), vo.getPageSize()).doSelectPageInfo(() -> {
            orderMapper.settlementDetailsReport(vo);
        });
        List<SettlementDetailsReportEntry> list = pageDate.getList();
        if(SpringUtil.isNotEmpty(list)){
            if(StringUtils.isEmpty(vo.getSettleTimeStart()) || StringUtils.isEmpty(vo.getSettleTimeEnd()) ){
                //配送直通订单的结算日期需要修改为取大仓发货的发货时间
                List<Integer> ids = new ArrayList<>();
                ids.add(LogisticsModelEnums.直通.getCode());
                ids.add(LogisticsModelEnums.配送.getCode());

                // 使用lambda表达式过滤出结果并放到result列表里
                List<SettlementDetailsReportEntry> result = list.stream().filter((SettlementDetailsReportEntry entry) -> ids.contains(entry.getLogisticsModel())).collect(Collectors.toList());
                if(SpringUtil.isNotEmpty(result)){
                    //查询大仓发货的发货时间
                    List<Long> subOrderIdList = result.stream().map(e->e.getSubOrderId()).collect(Collectors.toList());
                    if(SpringUtil.isNotEmpty(subOrderIdList)){
                        StockOutOrderTimeSearchIDTO stockOutOrderTimeSearchIDTO = new StockOutOrderTimeSearchIDTO();
                        stockOutOrderTimeSearchIDTO.setSubOrderIdList(subOrderIdList);
                        List<StockOutOrderODTO> stockOutOrderList = stockOutOrderClient.findStockOutOrderTimeBySubOrderCodeList(stockOutOrderTimeSearchIDTO);
                        if(SpringUtil.isNotEmpty(stockOutOrderList)){
                            subOrderIdAndTimeMap = stockOutOrderList.stream().collect(Collectors.toMap(StockOutOrderODTO::getSubOrderId, StockOutOrderODTO::getOutTime));
                        }
                    }
                }
            }

            //赋值
            if(SpringUtil.isNotEmpty(subOrderIdAndTimeMap)){
                for(SettlementDetailsReportEntry entry :list){
                    if(null != entry && null != entry.getSubOrderId()){
                        Date outDate = subOrderIdAndTimeMap.get(entry.getSubOrderId());
                        if(null != outDate){
                            entry.setSettleTime(outDate);
                        }
                    }
                }
            }
            //多条码
            for(SettlementDetailsReportEntry entry :list){
                String barCodes = entry.getBarCodes();
                if(StringUtils.isNotEmpty(barCodes)){
                    String[] barCode = barCodes.trim().split(",");
                    List barCodeList = java.util.Arrays.asList(barCode);
                    entry.setBarCodeList(barCodeList);
                }
                entry.setSettlePrice(null != entry.getSettlePrice() ? entry.getSettlePrice().setScale(2,BigDecimal.ROUND_HALF_UP) : BigDecimal.ZERO);
            }
        }
        return pageDate ;
    }

    /**
     * 查询结算总金额
     * @param vo
     * @return
     */
    public BigDecimal findTotalSettlePrice(SettlementDetailsReportVo vo){
        QYAssert.isTrue(null != vo,"参数异常");
        QYAssert.isTrue(null != vo.getShopId()," 门店id不能为空");
        QYAssert.isTrue( SpringUtil.isNotEmpty(vo.getShopIdList())," 当前登录用户没有门店权限");
        QYAssert.isTrue( vo.getShopIdList().contains(vo.getShopId())," 当前登录用户没有该门店的权限");
        if(StringUtils.isEmpty(vo.getDeliveryTimeEnd()) && StringUtils.isEmpty(vo.getSettleTimeEnd())){
            QYAssert.isTrue(true,"请选择送货日期或结算日期");
        }
        //送货日期
        if(!StringUtil.isBlank(vo.getDeliveryTimeStart()) && !StringUtil.isBlank(vo.getDeliveryTimeEnd())){
            vo.setDeliveryTimeStart(vo.getDeliveryTimeStart()+ " 00:00:00");
            vo.setDeliveryTimeEnd(vo.getDeliveryTimeEnd()+ " 23:59:59");
        }
        //结算日期
        if(!StringUtil.isBlank(vo.getSettleTimeStart()) && !StringUtil.isBlank(vo.getSettleTimeEnd())){
            vo.setSettleTimeStart(vo.getSettleTimeStart()+ " 00:00:00");
            vo.setSettleTimeEnd(vo.getSettleTimeEnd()+ " 23:59:59");

            List<Long> storeIdList = new ArrayList<>();
            //根据门店id 查询 客户id
            Long shopId = vo.getShopId();
            Shop shop = shopMapper.selectByPrimaryKey(shopId);
            storeIdList.add(shop.getStoreId());


            //配送直通订单的结算日期范围内的订单集合
            StockOutOrderTimeSearchIDTO stockOutOrderTime = new StockOutOrderTimeSearchIDTO();
            stockOutOrderTime.setSettleTimeEnd(vo.getSettleTimeEnd());
            stockOutOrderTime.setSettleTimeStart(vo.getSettleTimeStart());
            stockOutOrderTime.setStoreIdList(storeIdList);

            List<StockOutOrderODTO> stockOutOrderTimeList = stockOutOrderClient.findStockOutOrderTimeBySubOrderCodeList(stockOutOrderTime);
            if(SpringUtil.isNotEmpty(stockOutOrderTimeList)){
                List<Long> subOrderIdList = stockOutOrderTimeList.stream().map(e->e.getSubOrderId()).collect(Collectors.toList());
                if(SpringUtil.isNotEmpty(subOrderIdList)){
                    vo.setSubOrderIdList(subOrderIdList);
                }
            }else{
                List<Long> subOrderIdList = new ArrayList<>();
                subOrderIdList.add(0L);
                vo.setSubOrderIdList(subOrderIdList);
            }
        }

        BigDecimal totalSettlePrice = orderMapper.findTotalSettlePrice(vo);
        if(null == totalSettlePrice){
            totalSettlePrice = BigDecimal.ZERO;
        }else{
            totalSettlePrice = totalSettlePrice.setScale(2,BigDecimal.ROUND_HALF_UP);
        }
        return totalSettlePrice;
    }

    public StoreSettlementEntry queryBalance(String storeCode){
        return storeSettlementMapper.queryBalance(storeCode);
    }

    public List<StoreSettlementEntry> findStoreSettleByStoreIds(List<Long> storeIds){
        return  storeSettlementMapper.findStoreSettleByStoreIds(storeIds);
    }

    //查询符合条件的客户类型
    public List<String> getStoreTypeIdList(){
        List<String> storeTypeIdList = new ArrayList<>();
        SystemPropODTO systemProp = systemPropClient.findSystemPropByCode("store_type_id_list");
        if(null != systemProp && StringUtils.isNotEmpty(systemProp.getValue())){
            String[] storeTypeIds = systemProp.getValue().split(",");
            storeTypeIdList = Arrays.asList(storeTypeIds);
        }
        return storeTypeIdList;
    }
}
