package com.pinshang.qingyun.order.mapper;

import com.pinshang.qingyun.order.mapper.entry.order.OrderMonitorEntry;
import com.pinshang.qingyun.order.mapper.entry.order.SubOrderMonitorEntry;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2018/10/12 11:04
 */
@Repository
public interface OrderMonitorMapper {
    OrderMonitorEntry queryOrderInfo(@Param("orderCode") String orderCode);

    List<SubOrderMonitorEntry> querySubOrderInfo(@Param("orderCode") String orderCode);
}
