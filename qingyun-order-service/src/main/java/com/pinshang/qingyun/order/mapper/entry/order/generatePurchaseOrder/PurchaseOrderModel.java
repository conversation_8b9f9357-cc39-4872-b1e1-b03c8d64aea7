//package com.pinshang.qingyun.order.mapper.entry.order.generatePurchaseOrder;
//
//import lombok.Data;
//
//import javax.persistence.Entity;
//import javax.persistence.Id;
//import javax.persistence.Table;
//import java.math.BigDecimal;
//import java.time.LocalDate;
//import java.time.LocalDateTime;
//
//@Data
//@Table(name = "t_purchase_order")
//@Entity
//public class PurchaseOrderModel {
//    /**
//     * 主键id
//     */
//    @Id
//    private Long id;
//
//    /**
//     * 采购单编号
//     */
//    private String purchaseCode;
//
//    /**
//     * 供应商id
//     */
//    private String supplierId;
//
//    /**
//     * 收货仓库id
//     */
//    private String warehouseId;
//
//    /**
//     * 订单日期
//     */
//    private LocalDateTime orderTime;
//
//    /**
//     * 最晚收货日期
//     */
//    private LocalDate latestReceiveTime;
//
//    /**
//     * 订单金额
//     */
//    private BigDecimal totalAmount;
//
//    /**
//     * 订单状态,0-收货中,1-已关闭,2-已取消
//     */
//    private Integer status;
//
//    /**
//     * 企业id
//     */
//    private String enterpriseId;
//
//    /**
//     * 备注
//     */
//    private String remark;
//
//    /**
//     * 创建日期
//     */
//    private LocalDateTime createTime;
//
//    /**
//     * 更新日期
//     */
//    private LocalDateTime updateTime;
//
//    /**
//     * 采购人(因为页面上,采购人可以不是当前创建人)
//     */
//    private String purchaserId;
//
//    /**
//     * 创建人
//     */
//    private String createId;
//
//    /**
//     * 更新人
//     */
//    private String updateId;
//
//    //物流模式
//    private Integer logisticsModel;
//
//
//    private String storeId;
//
//
//}