package com.pinshang.qingyun.order.mapper;

import com.pinshang.qingyun.base.mybatis.MyMapper;
import com.pinshang.qingyun.order.dto.job.OrderBillStoreBalanceDTO;
import com.pinshang.qingyun.order.dto.job.OrderBillStoreChargingDTO;
import com.pinshang.qingyun.order.model.order.OrderBill;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;

@Repository
public interface OrderBillMapper extends MyMapper<OrderBill> {
    Integer recordTheTransaction(OrderBill orderBill);

    List<OrderBillStoreBalanceDTO> queryOrderBillList(@Param("dateStr") String dateStr,
                                                                    @Param("storeId")Long storeId);

    List<OrderBillStoreChargingDTO> queryChargingOrderBill(@Param("dateStr") String dateStr,
                                                           @Param("billRemarkLikeStr")String billRemarkLikeStr,
                                                           @Param("storeIdList") List<Long> storeIdList);
}
