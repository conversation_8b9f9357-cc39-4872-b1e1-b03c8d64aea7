package com.pinshang.qingyun.order.mapper;


import com.pinshang.qingyun.base.mybatis.MyMapper;
import com.pinshang.qingyun.order.dto.XDCommodityODTO;
import com.pinshang.qingyun.order.dto.cup.ProductPriceDto;
import com.pinshang.qingyun.order.mapper.entry.commodity.CommodityBasicEntry;
import com.pinshang.qingyun.order.mapper.entry.commodity.CommodityResultEntry;
import com.pinshang.qingyun.order.mapper.entry.order.CommodityInfoEntry;
import com.pinshang.qingyun.order.mapper.entry.order.OrderItemEntry;
import com.pinshang.qingyun.order.mapper.entry.order.ProductStatusEntry;
import com.pinshang.qingyun.order.model.commodity.Commodity;
import com.pinshang.qingyun.order.vo.commodity.CommodityListRequestVO;
import com.pinshang.qingyun.order.vo.commodity.CommodityVO;
import com.pinshang.qingyun.order.vo.cup.CommodityListQueryDTO;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.util.List;

@Repository
public interface CommodityMapper extends MyMapper<Commodity> {

	String findStoreIdBySubOrderCode(@Param("subOrderCode")String subOrderCode);

	List<ProductStatusEntry> queryCommodityStatus(@Param("storeId")Long storeId, @Param("commodityIdList")List<Long> commodityIdList);

	List<Long> findShopCommodityPurchaseList(CommodityListRequestVO vo);

	List<CommodityBasicEntry> findCommodityBasicListByParam(CommodityVO vo);

	List<CommodityBasicEntry> findCommodityBarCodeByIds(@Param("commodityIdList") List<Long> commodityIdList);

	List<CommodityResultEntry> findCartNumByCommodityId(@Param("storeId")Long storeId, @Param("commodityId")Long commodityId);

	List<CommodityInfoEntry> findCommodityInfo(@Param("commodityIdList") List<String> commodityIdList);

	List<CommodityInfoEntry> findCommodityInfoIds(@Param("commodityIdList") List<Long> commodityIdList);

	XDCommodityODTO getXDCommodityODTOById(@Param("commodityId")Long commodityId);

	List<XDCommodityODTO> getXDCommodityODTOByIdList(@Param("list")List<Long> list);

	List<XDCommodityODTO> findCommodityInfoByCodes(@Param("commodityCodes") List<String> commodityCodes);

	List<Commodity> findCommodityBarCodeByParam(@Param("commodityIdList") List<Long> commodityIdList,@Param("barCode") String barCode);

	BigDecimal getOrderedQuantity(@Param("storeId") String storeId, @Param("commodityId") Long commodityId, @Param("barCode") String barCode);

	Long findCommodityByCode(@Param("barCode") String barCode);
	Long findCommodityByBarCode(@Param("barCode") String barCode);

	List<Long> getCommodityFreezeGroup();

	List<Commodity> findStoreCommodityByParams(CommodityListQueryDTO queryDTO);

    List<ProductPriceDto> findStoreCommodityByStoreId(@Param("storeId") String storeId,@Param("productIds") List<Long> productIds);

	List<Commodity> findStoreCommodityByStoreIdAndCommodityCode(@Param("storeId")String storeId, @Param("commodityId")List<String> commodityCodeList, @Param("addCommodityType") int addCommodityType);

	List<Long> queryNewCommodityIdList(@Param("nowDate") String nowDate, @Param("commodityIdList") List<Long> commodityIdList);
}