package com.pinshang.qingyun.order.mapper;

import com.pinshang.qingyun.base.mybatis.MyMapper;
import com.pinshang.qingyun.order.dto.OrderInfoODTO;
import com.pinshang.qingyun.order.mapper.entry.order.*;
import com.pinshang.qingyun.order.mapper.entry.shop.SubOrderInfoEntry;
import com.pinshang.qingyun.order.mapper.entry.shop.SubOrderItemInfoEntry;
import com.pinshang.qingyun.order.mapper.entry.splitOrder.SubOrder2DeliveryOrderItemEntry;
import com.pinshang.qingyun.order.mapper.entry.splitOrder.SubOrder2DeliveryOrderListEntry;
import com.pinshang.qingyun.order.model.order.SubOrder;
import com.pinshang.qingyun.order.model.order.SubOrderItem;
import com.pinshang.qingyun.order.model.shop.Shop;
import com.pinshang.qingyun.order.vo.order.OrderVo;
import com.pinshang.qingyun.order.vo.order.PickSubOrderItemRespVo;
import com.pinshang.qingyun.order.vo.order.SubOrderSearchVo;
import com.pinshang.qingyun.order.vo.order.SubOrderVo;
import com.pinshang.qingyun.order.vo.subOrder.StockOutJobSubOrderReqVo;
import com.pinshang.qingyun.order.vo.subOrder.StockOutJobSubOrderRespVo;
import com.pinshang.qingyun.order.vo.subOrder.UnDeliverySubOrderJobReqVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Set;

/**
 * Created by zhaoranguang on 2017/6/22.
 */
@Mapper
@Repository
public interface SubOrderMapper extends MyMapper<SubOrder>{

   @Update("update t_md_preorder set purchase_order_id = #{purchaseOrderId},purchase_code = #{purchaseCode}, order_status =2 where id = #{subOrderId}")
   Integer updatePurchaseOrderId(@Param("subOrderId") String subOrderId,@Param("purchaseOrderId") Long purchaseOrderId,@Param("purchaseCode") String purchaseCode);
   
   @Select(" select  o.`id` from `t_sub_order` o where o.`order_id` =#{orderId}  ")
   List<Long> findSubOrderIdbyOrderId(@Param("orderId")Long orderId);
   
   @Update("update `t_sub_order` set `status` = 2 where id =#{subOrderId} ")
   Integer updateSubOrderStatus(@Param("subOrderId")Long subOrderId);
   
   @Update("UPDATE t_sub_order SET order_time = #{date} WHERE order_id = (SELECT id FROM t_order WHERE order_code = #{orderCode})")
   Integer modifyDeliverDate(@Param("orderCode") String orderCode, @Param("date") String date);

   @Update("UPDATE t_sub_order set total_price = #{totalPrice} where id = #{subOrderId} ")
   Integer updateTotalPriceById(@Param("totalPrice") BigDecimal totalPrice, @Param("subOrderId")String subOrderId);

   BigDecimal getTotalPriceBySubOrderId(@Param("logisticsModel")Integer logisticsModel,@Param("subOrderId") String subOrderId);

   List<SubOrderItemInfoEntry> getSubOrderItemInfoById(@Param("subOrderId") String subOrderId);

   SubOrderInfoEntry getSubOrderInfoById(@Param("subOrderId") String subOrderId);

   //=============================  订单拆单相关   ============================================//
   List<SubOrder2DeliveryOrderListEntry> listSubOrder2DeliveryOrder(@Param("orderIdList") List<Long> orderIdList,
                                                                    @Param("warehouseId") Long warehouseId,
                                                                    @Param("businessType") Integer businessType);
   List<SubOrder2DeliveryOrderListEntry> listSubOrder2DeliveryOrderConfirm(@Param("ids") List<Long> subOrderIds,@Param("businessType")Integer businessType);

   List<SubOrder2DeliveryOrderItemEntry> listSubOrder2DeliveryOrderItem(@Param("subOrderIdList") List<Long> subOrderIdList,
                                                                        @Param("subStatus") Integer subStatus,
                                                                        @Param("businessType") Integer businessType);

   //============================   手动生成发货单相关   ============================================//
   List<SubOrderListEntry> queryUnDeliverySubOrderList(SubOrderVo vo);

   List<SubOrderItemListEntry> querySubOrderItemList(SubOrderSearchVo vo);

   List<PickSubOrderItemRespVo> findItemsBySubOrderId(@Param("subOrderId")Long subOrderId);

   int batchUpdateDeliveryQuantity(List<PickSubOrderItemRespVo> deliveryItemList);

   int batchUpdateXDDeliveryQuantity(List<PickSubOrderItemRespVo> deliveryItemList);

   int batchUpdateCombDeliveryQuantity(List<SubOrderItem> deliveryItemList);
   int batchUpdateCombDeliveryQuantityNOReceive(List<SubOrderItem> deliveryItemList);

    List<PickSubOrderItemRespVo> findItemsBySubOrderIds(@Param("ids") List<Long> subOrderIds);

   List<OrderVo> listOrderDetail(@Param("subOrderId")Long subOrderId, @Param("excludeOrderTypeList") List<Long> excludeOrderTypeList);

   List<OrderVo> listOrderDetailByOrderId(@Param("orderId") Long orderId, @Param("excludeOrderTypeList") List<Long> excludeOrderTypeList);

   List<OrderVo> queryOrderListDetailByOrderId(@Param("orderId") Long orderId);

   List<Shop> findShopListByStoreIds(@Param("storeIds") List<Long> storeIds);

   /**
    * 根据订单id批量取消子订单
    * @param orderId
    * @return
    */
   int cancelSubOrder(@Param("orderId") Long orderId);

    List<OrderInfoODTO> getOrderInfoList(@Param("subOrderIdList") List<Long> subOrderIdList);

    Integer updateOrderProcessStatus(@Param("subOrderIds") List<Long> subOrderIds,@Param("processStatus") Integer processStatus);

   /**
    *t_store的business_type不为传入的businessType才进行更新t_sub_order的已回填实发数量标识 2024-05-18
    * @param subOrderIds
    * @param writebackRealQtyFlag
    * @param businessType
    * @return
    */
    Integer updateSubOrderWritebackRealQtyFlagIfNecessary(@Param("subOrderIds") List<Long> subOrderIds,
                                                          @Param("writebackRealQtyFlag") Boolean writebackRealQtyFlag,
                                                          @Param("businessType") Integer businessType);

   /**
    *t_store的business_type不为传入的businessType才进行更新t_sub_order的已回填实发数量标识 2024-05-18
    * @param subOrderIds
    * @param writebackRealQtyFlag
    * @param businessType
    * @return
    */
   Integer updateSubOrderWritebackRealQtyFlagViaOrderIdAndSubOrderItemCommodityList(@Param("orderId") Long orderId,
                                                         @Param("writebackRealQtyFlag") Integer writebackRealQtyFlag,
                                                         @Param("subOrderItemCommodityList") List<Long> subOrderItemCommodityList);

   List<StockOutJobSubOrderRespVo> getStockOutSubOrderList(StockOutJobSubOrderReqVo reqVo);

    /***
     * APP 我的订单列表实发金额显示问题：背景由于同一个订单多个仓库发货完成时间点不同，导致实发金额会变化 希望能整单完成拣货（出货）才能计算实发金额，才能生成送货单
     *
     * 查询订单拆单数量（不包括直送类型：t_sub_order表logistics_model=0，直送类型大仓不发送发货消息）及实际大仓发货数
     * 实际大仓发货数=根据t_sub_order表字段writeback_real_qty_flag该字段等于1 时从而知道子订单在大仓发货并发送发货消息：topic=PICK_UPDATE_SUB_ORDER_QUANTITY_TOPIC 来判断拆分的子单是否都发货
     */
   List<OrderRealDeliveryFinishEntry> findOrderRealDeliveryFinishList(@Param("orderIds") List<Long> orderIds);

    /***
     * 对应需求http://192.168.0.213/zentao/story-view-8527.html
     * 获取 子单是否都发货完成, 子单发货完成业务需填充对应订单的（子单发货完成标识t_order表real_sub_order_done_status字段：值等于1-所有子单发货完 不包括直送类型）
     * 查询订单拆单数量（不包括直送类型：t_sub_order表logistics_model=0，直送类型大仓不发送发货消息）及实际大仓发货数
     * 实际大仓发货数=根据t_sub_order表字段writeback_real_qty_flag该字段等于1 时从而知道子订单在大仓发货并发送发货消息：topic=PICK_UPDATE_SUB_ORDER_QUANTITY_TOPIC 来判断拆分的子单是否都发货
     */
    List<Long> findOrderIdListBySubOrderIds(@Param("subOrderIds") List<Long> subOrderIds);

   List<SubOrder2DeliveryOrderListEntry> queryUnDeliverySubOrderJobList(UnDeliverySubOrderJobReqVo param);

   List<SubOrder2DeliveryOrderListEntry> queryUnDeliverySubOrderXDAJobList(UnDeliverySubOrderJobReqVo param);

   Integer batchUpdateShiftStoreType(@Param("shiftStoreTypeIds") List<Long> shiftStoreTypeIds,@Param("currentTime") String currentTime);

   List<OrderTODeliveryOrderEntry> findNoDeliveryByOrderIds(@Param("orderIds") List<Long> orderIds,
                                                            @Param("warehouseId") Long warehouseId,
                                                            @Param("businessType")Integer businessType);

   List<OrderTODeliveryOrderEntry> queryUnDeliverySubOrderIdsJob(UnDeliverySubOrderJobReqVo param);

   List<OrderTODeliveryOrderEntry> queryUnDeliverySubOrderIdsXDAJob(UnDeliverySubOrderJobReqVo param);

   List<SubOrderItemCommodityPriceEntry> selectSubOrderItemCommodityPrice(@Param("orderTime")String orderTime);

   /**
    *
    * @param orderIds
    * @param handleOverFlag 为0时无需处理多发扣款
    * @return
    */
   List<OrderRealTotalPriceListEntry> findOrderRealTotalPriceListByOrderIds(@Param("orderIds") Set<Long> orderIds,@Param("handleOverFlag")Integer handleOverFlag);

   List<SubOrderInfoListEntry> selectSubOrderInfoListByOrderIds(@Param("orderIdList") List<Long> orderIdList,@Param("logisticsModel") Integer logisticsModel);
}
