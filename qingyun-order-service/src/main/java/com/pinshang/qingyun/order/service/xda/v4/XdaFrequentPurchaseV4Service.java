package com.pinshang.qingyun.order.service.xda.v4;

import com.pinshang.qingyun.box.utils.SpringUtil;
import com.pinshang.qingyun.order.dto.xda.v4.CreOrderItemV4DTO;
import com.pinshang.qingyun.order.mapper.XdaFrequentPurchaseMapper;
import com.pinshang.qingyun.order.model.order.XdaFrequentPurchase;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2023/12/26 16:29
 */
@Service
@Slf4j
public class XdaFrequentPurchaseV4Service {

    @Autowired
    private XdaFrequentPurchaseMapper xdaFrequentPurchaseMapper;


    private List<CreOrderItemV4DTO> groupingByList(List<CreOrderItemV4DTO> orderCommodityList){
        List<CreOrderItemV4DTO> resultList = new ArrayList<>();

        orderCommodityList.stream()
                .collect(Collectors.groupingBy(item -> item.getCommodityId()))//分组
                .forEach((k,v)->{
                    Optional<CreOrderItemV4DTO> item =  v.stream().reduce((v1, v2)->{//合并
                        v1.setQuantity(v1.getQuantity().add(v2.getQuantity()));
                        return v1;
                    });
                    resultList.add(item.orElse(new CreOrderItemV4DTO()));
                });
        return resultList;
    }

    /**
     * 保存线上订单销量日统计
     * @param storeId
     * @param orderCommodityList
     */
    public void saveSaleDayStatistics(Long storeId, List<CreOrderItemV4DTO> orderCommodityList){
        if(CollectionUtils.isEmpty(orderCommodityList)){
            return;
        }
        orderCommodityList = groupingByList(orderCommodityList);
        // 查询客户常购清单
        Set<Long> commodityIds = orderCommodityList.stream().map(CreOrderItemV4DTO :: getCommodityId).collect(Collectors.toSet());
        List<XdaFrequentPurchase> frequentPurchaseIdList = xdaFrequentPurchaseMapper.queryFrequentPurchase(storeId, commodityIds);
        Map<Long, XdaFrequentPurchase> frequentPurchaseMap = frequentPurchaseIdList.stream().collect(Collectors.toMap(XdaFrequentPurchase::getCommodityId, Function.identity()));

        List<XdaFrequentPurchase> addList = new ArrayList<>(frequentPurchaseIdList.size());
        List<XdaFrequentPurchase> updateList = new ArrayList<>(frequentPurchaseIdList.size());

        orderCommodityList.forEach(p->{
            XdaFrequentPurchase frequentPurchase = new XdaFrequentPurchase();
            frequentPurchase.setStoreId(storeId);
            frequentPurchase.setCommodityId(p.getCommodityId());
            XdaFrequentPurchase fp =  frequentPurchaseMap.get(p.getCommodityId());
            if(fp != null){
                frequentPurchase.setQuantity(fp.getQuantity().add(p.saleQuantity()));
                frequentPurchase.setUpdateTime(new Date());
                updateList.add(frequentPurchase);
            }else {
                frequentPurchase.setUpdateTime(new Date());
                frequentPurchase.setQuantity(p.saleQuantity());
                addList.add(frequentPurchase);
            }
        });

        if(SpringUtil.isNotEmpty(addList)){
            xdaFrequentPurchaseMapper.insertList(addList);
        }
        if(SpringUtil.isNotEmpty(updateList)){
            xdaFrequentPurchaseMapper.batchUpdate(updateList);
        }

    }
}
