package com.pinshang.qingyun.order.mapper.entry.order;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.pinshang.qingyun.base.enums.order.OrderTypeEnum;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2018/10/12 10:00
 */
@Data
public class OrderMonitorEntry {
    /**
     * 主键id
     */
    private Long orderId;
    /**
     * 订单编码
     */
    private String orderCode;
    /**
     * 客户编码
     */
    private String storeCode;
    /**
     * 客户名称
     */
    private String storeName;
    /**
     * 订单时间
     */
    private Date orderTime;
    /**
     * 订单金额
     */
    private BigDecimal orderAmount;
    /**
     * 最终金额
     */
    private BigDecimal finalAmount;
    /**
     * 创建时间
     */
    private Date createTime;
    /**
     * 订单状态:0=正常，1=删除
     */
    private Integer orderStatus;
    /**
     * 同步状态：0=未同步，1=已同步
     */
    private Integer syncStatus;

    /**
     * 订单类型(1=PC下单,2=APP下单, 8=鲜达APP 10-门店订货下单 21-团购订单 22-门店订货PC版 23-门店订货PDA版 24-门店订货代理补货 25-大仓配货自动下单 26-门店自动订货 27-代理订货 28-直送补单 29-直送审核通过)
     * */
    private Integer orderType;

    /**
     * 订单类型 描述
     * */
    private String orderTypeDesc;

    /**
     * 流程状态(鲜达新加)  7=待发货    11=出库中　15=配送中　　19=配送完成
     * */
    private Integer processStatus;
    /**
     * 订单截止时间(鲜达新加),根据当前客户截止时间与订单中的最晚商品截止时间得来;订单取消与是否进大仓时使用
     * */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date orderDurationTime;

    /**
     * 0-无需批次配送, 1-1配，2-2配，3-3配，9-临时批次
     * */
    private Integer deliveryBatch;

    /**
     * 是否可变价 :1是 0否
     * */
    private Integer changePriceStatus;

    /**
     * 子订单是否出库完标识：只有1代表所有子单(不包括直送)已出库
     * */
    private Integer realSubOrderDoneStatus;

    private Integer presaleStatus;

    /**
     * 订单商品列表
     */
    private List<OrderItemMonitorEntry> orderItem;

    public String getOrderTypeDesc() {
        if(this.getOrderType() != null){
            OrderTypeEnum enums =  OrderTypeEnum.fromCode(this.getOrderType());
            if(enums != null){
                return enums.getDesc();
            }
        }
        return orderTypeDesc;
    }
}
