package com.pinshang.qingyun.order.service;

import com.pinshang.qingyun.base.configure.expand.QYAssert;
import com.pinshang.qingyun.base.enums.IogisticsModelEnums;
import com.pinshang.qingyun.base.enums.PurchaseOrderEnums;
import com.pinshang.qingyun.box.utils.DateUtil;
import com.pinshang.qingyun.box.utils.SpringUtil;
import com.pinshang.qingyun.box.utils.StringUtil;
import com.pinshang.qingyun.common.service.CodeClient;
import com.pinshang.qingyun.order.enums.OrderModeType;
import com.pinshang.qingyun.order.mapper.PurchaseOrderItemMapper;
import com.pinshang.qingyun.order.mapper.PurchaseOrderMapper;
import com.pinshang.qingyun.order.mapper.SubOrderMapper;
import com.pinshang.qingyun.order.mapper.entry.order.generatePurchaseOrder.PurchaseOrder;
import com.pinshang.qingyun.order.mapper.entry.order.generatePurchaseOrder.SubOrderEntry;
import com.pinshang.qingyun.order.mapper.entry.order.generatePurchaseOrder.SubOrderItemModel;
import com.pinshang.qingyun.order.model.purchase.PurchaseOrderItem;
import com.pinshang.qingyun.product.dto.CommodityCategoryIDTO;
import com.pinshang.qingyun.product.dto.CommodityCategoryODTO;
import com.pinshang.qingyun.product.service.CategoryClient;
import com.pinshang.qingyun.purchase.dto.PurchaseOrderGenerateDto;
import com.pinshang.qingyun.purchase.dto.PurchaseOrderItemIDto;
import com.pinshang.qingyun.purchase.dto.SupplierPriceIDTO;
import com.pinshang.qingyun.purchase.dto.SupplierPriceODTO;
import com.pinshang.qingyun.purchase.service.PurchaseOrderClient;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * Created by zhaoranguang on 2017/6/20.
 */
@Service(value = "purchaseService")
@Slf4j
public class PurchaseService {

    @Autowired
    PurchaseOrderMapper purchaseOrderMapper;

    @Autowired
    PurchaseOrderItemMapper purchaseOrderItemMapper;

    @Autowired
    SubOrderMapper subOrderMapper;

    @Autowired
    CodeClient codeClient;

    @Autowired
    CategoryClient categoryClient;

    @Autowired
    PurchaseOrderClient purchaseOrderClient;


    @Transactional(rollbackFor = Exception.class)
    public void generatePurchaseOrder(SubOrderEntry subOrderEntry){
        String enterpriseId = subOrderEntry.getEnterpriseId();
        String supplierId = subOrderEntry.getSupplierId();
        String subOrderId  = subOrderEntry.getId();
        List <SubOrderItemModel> subOrderItems = groupingByList(subOrderEntry.getSubOrderItems());
        List <String> commodityIds = subOrderItems.stream().map(SubOrderItemModel::getCommodityId).collect(Collectors.toList());
        List<CommodityCategoryODTO> commodityCategoryODTOS = getCategoryId(commodityIds);
        List <SupplierPriceODTO> supplierPriceODTOS = getSupplierPriceODTO(enterpriseId, supplierId, commodityIds);
        BigDecimal totalAmount = sum(subOrderItems, supplierPriceODTOS, subOrderEntry.getModeType());
        PurchaseOrder purchaseOrder = generatePrimaryOrder(subOrderEntry, totalAmount);
        List<PurchaseOrderItem> purchaseOrderItems = generateItemOrder(purchaseOrder.getId(), subOrderItems,supplierPriceODTOS,commodityCategoryODTOS, subOrderEntry);
        //update subOrderModel purchase order id

        Long purchaseId = doSavePurchase(purchaseOrder, purchaseOrderItems);
        updatePurchaseOrderId(subOrderId,purchaseId, purchaseOrder.getPurchaseCode());
    }

    @Transactional(rollbackFor = Exception.class)
    public Long doSavePurchase(PurchaseOrder purchaseOrder, List<PurchaseOrderItem> purchaseOrderItems) {
        PurchaseOrderGenerateDto purchaseOrderDto = new PurchaseOrderGenerateDto();
        BeanUtils.copyProperties(purchaseOrder, purchaseOrderDto);
        List<PurchaseOrderItemIDto> commodityList = new ArrayList<>();
        for (PurchaseOrderItem item : purchaseOrderItems) {
            PurchaseOrderItemIDto idto = new PurchaseOrderItemIDto();
            BeanUtils.copyProperties(item, idto);
            commodityList.add(idto);
        }
        purchaseOrderDto.setCommodityList(commodityList);
        purchaseOrderDto = purchaseOrderClient.generatePurchaseOrder(purchaseOrderDto);

        return purchaseOrderDto.getPurchaserId();
    }

    //合并相同的商品
    private List <SubOrderItemModel> groupingByList(List<SubOrderItemModel> subOrderItems){
        if(SpringUtil.isEmpty(subOrderItems)){
            return null;
        }
        List <SubOrderItemModel> resultList = new ArrayList<SubOrderItemModel>();
        subOrderItems.stream()
                .collect(Collectors.groupingBy(SubOrderItemModel::getCommodityId))//根据商品id分组
                .forEach((k,v)->{
                    Optional<SubOrderItemModel>  item =  v.stream().reduce((v1,v2)->{//合并
                        v1.setQuantity(v1.getQuantity().add(v2.getQuantity()));//数量
                        v1.setTotalPrice(v1.getTotalPrice().add(v2.getTotalPrice()));//金额
                        return v1;
                    });
                    resultList.add(item.orElse(new SubOrderItemModel()));
                });
        return resultList;
    }


    private void updatePurchaseOrderId(String subOrderId, Long purchaseOrderId, String purchaseCode) {
        subOrderMapper.updatePurchaseOrderId(subOrderId,purchaseOrderId, purchaseCode);
    }


    private List<CommodityCategoryODTO> getCategoryId(List <String> commodityIds) {
        CommodityCategoryIDTO commodityCategoryIDTO = new CommodityCategoryIDTO();
        commodityCategoryIDTO.setCommodityIds(commodityIds);
        commodityCategoryIDTO.setLevel(1);
        return  categoryClient.getCategoryIds(commodityCategoryIDTO);
    }

    private BigDecimal sum(List <SubOrderItemModel> subOrderItems, List <SupplierPriceODTO> supplierPriceODTOS, Integer modeType) {
        BigDecimal sum = BigDecimal.ZERO;
        for (SubOrderItemModel subOrderItemModel : subOrderItems) {
            BigDecimal price = BigDecimal.ZERO;
            if (OrderModeType.DRIECT_SENDING_BACKORDER.getCode().equals(modeType)) {
                price = subOrderItemModel.getPrice();
            } else {
                price = getPriceByCommodityId(supplierPriceODTOS,subOrderItemModel.getCommodityId());
            }
            sum = sum.add(price.multiply(subOrderItemModel.getQuantity()));
        }
        return sum;

    }

    private List <SupplierPriceODTO> getSupplierPriceODTO(String enterpriseId, String supplierId, List <String> commodityIds) {
        SupplierPriceIDTO supplierPriceIDTO = new SupplierPriceIDTO();
        supplierPriceIDTO.setSupplierId(supplierId);
        supplierPriceIDTO.setEnterpriseId(enterpriseId);
        supplierPriceIDTO.setCommodityIds(commodityIds);
        return purchaseOrderClient.queryPrice(supplierPriceIDTO);
    }


    public List <SubOrderEntry> getSubOrderEntries(String endTime) {
        endTime += ":00";
        return purchaseOrderMapper.getSubOrderBeforeDeadline(endTime);

    }


    private PurchaseOrder generatePrimaryOrder(SubOrderEntry subOrderEntry, BigDecimal totalAmount){
        PurchaseOrder purchaseOrderModel = new PurchaseOrder();
        purchaseOrderModel.setLogisticsModel(IogisticsModelEnums.DIRECT_SENDING.getCode());
        copyProperties(subOrderEntry, purchaseOrderModel, totalAmount);
//        purchaseOrderMapper.insert(purchaseOrderModel);
//        log.info("生成预订单采购单ID："+purchaseOrderModel.getId()+",预订单iD:"+subOrderEntry.getId());
        log.info("生成预订单采购单ID： 预订单iD:"+subOrderEntry.getId());
        return purchaseOrderModel;
    }

    private void copyProperties(SubOrderEntry subOrderEntry, PurchaseOrder purchaseOrderModel, BigDecimal totalAmount){
        String enterpriseId = subOrderEntry.getEnterpriseId();
        Date date = new Date();
        purchaseOrderModel.setOrderTime(date);
        purchaseOrderModel.setSupplierId(Long.valueOf(subOrderEntry.getSupplierId()));
        purchaseOrderModel.setEnterpriseId(Long.valueOf(enterpriseId));
        purchaseOrderModel.setWarehouseId(9999L);//直送仓库
        purchaseOrderModel.setLatestReceiveTime(DateUtil.addDay(date,2));
        purchaseOrderModel.setPurchaserId(-1L);
        purchaseOrderModel.setStatus(PurchaseOrderEnums.CLOSED.getCode());
        String purchaseCode = generatePurchaseCode(enterpriseId);
        QYAssert.isTrue(null!= purchaseCode , "PurchaseCode 获取失败 !");
        purchaseOrderModel.setPurchaseCode(purchaseCode);
        purchaseOrderModel.setTotalAmount(totalAmount);
        purchaseOrderModel.setCreateId(-1L);
        purchaseOrderModel.setUpdateId(-1L);
        purchaseOrderModel.setStoreId(Long.parseLong(subOrderEntry.getStoreId()));
        purchaseOrderModel.setRemark("系统由preOrder自动生成");
    }


    private List<PurchaseOrderItem> generateItemOrder(Long id, List <SubOrderItemModel> subOrderItems, List <SupplierPriceODTO> supplierPriceODTOS,
                                   List<CommodityCategoryODTO> commodityCategoryODTOS, SubOrderEntry subOrderEntry) {
        List<PurchaseOrderItem> purchaseOrderItemModels = new ArrayList <>();
        for (SubOrderItemModel item : subOrderItems) {
            PurchaseOrderItem purchaseOrderItemModel =  new PurchaseOrderItem();
            copyPropertiesToItem(id,purchaseOrderItemModel,item,supplierPriceODTOS,commodityCategoryODTOS, subOrderEntry);
            purchaseOrderItemModels.add(purchaseOrderItemModel);
        }
//        if(!SpringUtil.isEmpty(purchaseOrderItemModels)){
//            purchaseOrderItemMapper.insertList(purchaseOrderItemModels);
//        }
        return purchaseOrderItemModels;
    }

    private void copyPropertiesToItem(Long id,PurchaseOrderItem purchaseOrderItemModel, SubOrderItemModel item,
                                      List <SupplierPriceODTO> supplierPriceODTOS,
                                      List<CommodityCategoryODTO> commodityCategoryODTOS,
                                      SubOrderEntry subOrderEntry) {
        String commodityId = item.getCommodityId();
        purchaseOrderItemModel.setCommodityId(Long.parseLong(item.getCommodityId()));
        purchaseOrderItemModel.setQuantity(item.getQuantity());
        purchaseOrderItemModel.setPurchaseOrderId(id);
        if (OrderModeType.DRIECT_SENDING_BACKORDER.getCode().equals(subOrderEntry.getModeType())) {
            purchaseOrderItemModel.setPrice(item.getPrice());
        } else {
            purchaseOrderItemModel.setPrice(getPriceByCommodityId(supplierPriceODTOS,commodityId));
        }

        String categoryIdStr = getCategoryIdByCommodityId(commodityCategoryODTOS, commodityId);
        purchaseOrderItemModel.setCategoryId((StringUtil.isNullOrEmpty(categoryIdStr) ? null : Long.parseLong(categoryIdStr)));
        purchaseOrderItemModel.setReceivedQuantity(BigDecimal.ZERO);
    }


    private String generatePurchaseCode(String enterpriseId){
        String purchaseCode = "";
        try {
            purchaseCode = codeClient.createCode("PURCHASE_CODE");
        } catch (Throwable e) {
            purchaseCode =null;
        }
        return purchaseCode;
    }


    private BigDecimal getPriceByCommodityId(List <SupplierPriceODTO> supplierPriceODTOS,String commodityId) {
        return  supplierPriceODTOS.stream().filter(e -> e.getCommodityId().equals(commodityId)).findFirst().map(SupplierPriceODTO::getPrice).orElse(BigDecimal.ZERO);
    }

    private String getCategoryIdByCommodityId(List<CommodityCategoryODTO> commodityCategoryODTOS,String commodityId){
        if(commodityCategoryODTOS !=null && !commodityCategoryODTOS.isEmpty()){
            for (CommodityCategoryODTO commodityCategoryODTO : commodityCategoryODTOS) {
                if(commodityCategoryODTO.getCommodityId().equals(commodityId)){
                    return commodityCategoryODTO.getCategoryId();
                }
            }
        }
        return null;
    }
}

