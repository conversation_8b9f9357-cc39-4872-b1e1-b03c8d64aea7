package com.pinshang.qingyun.order.constant;

import com.pinshang.qingyun.base.api.QYApplicationContext;
import com.pinshang.qingyun.base.constant.QingyunConstant;
import com.pinshang.qingyun.base.enums.DeliveryOrderTypeEnums;


public class LockKeyConstant {

    /**
     * SO生成DO单锁
     * @param warehouseId
     * @param orderTime
     * @return
     */
    public static String createDeliveryOrderLock (Long warehouseId, String orderTime){
        return String.format("%s:%s:%s:%s:%s",QYApplicationContext.redisKeyProfile, QingyunConstant.CREATE_DELIVERY_ORDER_LOCK, DeliveryOrderTypeEnums.SALE.getName(),warehouseId, orderTime);
    }
}
