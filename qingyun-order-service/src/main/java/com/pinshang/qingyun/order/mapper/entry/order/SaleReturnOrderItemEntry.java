package com.pinshang.qingyun.order.mapper.entry.order;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class SaleReturnOrderItemEntry {

    @ApiModelProperty("商品ID")
    private Long commodityId;
    @ApiModelProperty("商品名称")
    private String commodityName;
    @ApiModelProperty("商品编码")
    private String commodityCode;
    @ApiModelProperty("规格")
    private String commoditySpec;
    @ApiModelProperty("单位")
    private String unitName;
    @ApiModelProperty("退货数量")
    private BigDecimal returnQuantity;
    @ApiModelProperty("退货原因")
    private Integer returnReason;
    @ApiModelProperty("备注")
    private String remark;
    @ApiModelProperty("批次管理")
    private Integer batchStatus;
    @ApiModelProperty("锁库状态0为未锁 1位锁库")
    private Integer lockStatus;
    @ApiModelProperty("拣货位id")
    private Long pickShelfId;
    @ApiModelProperty("拣货位号")
    private String pickShelfNo;
    @ApiModelProperty("条形码")
    private String barCode;
}
