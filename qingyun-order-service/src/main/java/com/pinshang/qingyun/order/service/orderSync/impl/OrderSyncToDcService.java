package com.pinshang.qingyun.order.service.orderSync.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.pinshang.qingyun.box.utils.BeanCloneUtils;
import com.pinshang.qingyun.order.mapper.OrderMapper;
import com.pinshang.qingyun.order.mapper.SubOrderItemMapper;
import com.pinshang.qingyun.order.mapper.SubOrderMapper;
import com.pinshang.qingyun.order.model.order.Order;
import com.pinshang.qingyun.order.model.order.SubOrder;
import com.pinshang.qingyun.order.model.order.SubOrderItem;
import com.pinshang.qingyun.order.service.orderSync.IOrderSyncToDcService;
import com.pinshang.qingyun.order.vo.order.OrderClientVO;
import com.pinshang.qingyun.order.vo.order.OrderSyncToDcReqVo;
import com.pinshang.qingyun.order.vo.order.OrderSyncToDcRespVo;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static java.util.stream.Collectors.groupingBy;
import static java.util.stream.Collectors.toList;

@Service
public class OrderSyncToDcService implements IOrderSyncToDcService {
    @Autowired
    private OrderMapper orderMapper;
    @Autowired
    private SubOrderMapper subOrderMapper;
    @Autowired
    private SubOrderItemMapper subOrderItemMapper;

    @Override
    public List<OrderSyncToDcRespVo> queryOrderSyncToDcList(OrderSyncToDcReqVo vo){
//        return orderMapper.queryOrderSyncToDcList(vo);
        return orderMapper.queryOrderSyncToDcListV2(vo);
    }

    @Override
    public List<OrderClientVO> getOrderListByIds(List<Long> orderIds){
        Example orderExample = new Example(Order.class);
        orderExample.createCriteria().andIn("id",orderIds);
        List<Order> orderList = orderMapper.selectByExample(orderExample);
        return processOrderList(orderList,null);
    }

    @Override
    public  List<OrderClientVO> queryOrderListBySubOrderIds(List<Long> subOrderIds){
        Example subExample = new Example(SubOrder.class);
        subExample.createCriteria().andIn("id", subOrderIds);
        List<SubOrder> subOrders = subOrderMapper.selectByExample(subExample);
        if(CollectionUtils.isEmpty(subOrders)){
            return null;
        }
        Map<Long,List<SubOrder>> subOrderMap = subOrders.stream().collect(groupingBy(SubOrder::getOrderId));
        Example orderExample = new Example(Order.class);
        orderExample.createCriteria().andIn("id",subOrderMap.keySet());
        List<Order> orderList = orderMapper.selectByExample(orderExample);
        return processOrderList(orderList,subOrderMap);
    }

    private List<OrderClientVO> processOrderList(List<Order> orderList,Map<Long,List<SubOrder>> subOrderMap){
        return orderList.stream().map(order -> {
            OrderClientVO clientVO = BeanCloneUtils.copyTo(order, OrderClientVO.class);
            if (order.getPrintType() != null) {
                clientVO.setPrintType(order.getPrintType().getCode());
            }
            List<SubOrder> subOrders;
            if(subOrderMap == null || subOrderMap.isEmpty()){
                Example subExample = new Example(SubOrder.class);
                subExample.createCriteria().andEqualTo("orderId", order.getId());
                subOrders = subOrderMapper.selectByExample(subExample);
            }else{
                subOrders = subOrderMap.get(order.getId());
            }
            clientVO.setSubOrders(subOrders);
            if(CollectionUtils.isNotEmpty(subOrders)){
                List<Long> subOrderIds = subOrders.stream().map(SubOrder::getId).collect(toList());
                Example itemExample = new Example(SubOrderItem.class);
                itemExample.createCriteria().andIn("subOrderId", subOrderIds);
                List<SubOrderItem> subOrderItems = subOrderItemMapper.selectByExample(itemExample);
                clientVO.setSubOrderItems(subOrderItems);
            }
            return clientVO;
        }).collect(Collectors.toList());
    }


    @Override
    public PageInfo<OrderSyncToDcRespVo> queryOrderPage(OrderSyncToDcReqVo vo){
        return PageHelper.startPage(vo.getPageNo(), vo.getPageSize()).doSelectPageInfo(() -> {
            orderMapper.queryOrderSyncToDcList(vo);
        });
    }
}
