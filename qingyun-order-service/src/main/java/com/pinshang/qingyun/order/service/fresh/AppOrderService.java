package com.pinshang.qingyun.order.service.fresh;

import com.pinshang.qingyun.box.utils.SpringUtil;
import com.pinshang.qingyun.order.mapper.OrderListGiftMapper;
import com.pinshang.qingyun.order.mapper.OrderMapper;
import com.pinshang.qingyun.order.mapper.fresh.AppOrderMsgRetryMapper;
import com.pinshang.qingyun.order.model.fresh.AppOrderMsgRetry;
import com.pinshang.qingyun.order.model.order.Order;
import com.pinshang.qingyun.order.model.order.OrderList;
import com.pinshang.qingyun.order.model.order.OrderListGift;
import com.pinshang.qingyun.order.service.OrderAsyncKafkaService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionSynchronizationAdapter;
import org.springframework.transaction.support.TransactionSynchronizationManager;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

import static java.util.stream.Collectors.toList;

/**
 * 清美生鲜订单
 */
@Slf4j
@Service
public class AppOrderService {

	@Autowired
	private OrderMapper orderMapper;

	@Autowired
	private OrderListGiftMapper orderListGiftMapper;
	
	@Autowired
	private AppOrderMsgRetryMapper appOrderMsgRetryMapper;

	@Autowired
	private OrderAsyncKafkaService orderAsyncKafkaService;

	/**
	 * 发送生鲜订单创建消息
	 * 
	 * @param orderId
	 */
	@Transactional
	public void sendAppOrderSaveMsg(Long orderId) {
		Order order = this.getOrderMsg(orderId);
		if (null == order) {
			log.warn("\n发送生鲜订单创建消息.订单信息为-! orderId={}", orderId);
			return;
		}

		TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronizationAdapter() {
			@Override
			public void afterCommit() {
				orderAsyncKafkaService.sendKafkaSaveOrderMessage(order);
			}
		});
	}

	/**
	 * 发送生鲜订单变更消息
	 * 
	 * @param orderId
	 */
	@Transactional
	public void sendAppOrderUpdateMsg(Long orderId) {
		Order order = this.getOrderMsg(orderId);
		if (null == order) {
			log.warn("\n发送生鲜订单变更消息.订单信息为-! orderId={}", orderId);
			return;
		}

		orderAsyncKafkaService.sendKafkaUpdateOrderMessage(order);
	}

	private Order getOrderMsg(Long orderId) {
		if (null == orderId) {
			return null;
		}

		Order order = orderMapper.selectByPrimaryKey(orderId);
		if (null == order) {
			return null;
		}

		OrderListGift orderListGift = new OrderListGift();
		orderListGift.setOrderId(orderId);
		List<OrderListGift> orderListGiftList = orderListGiftMapper
				.select(orderListGift);
		if (SpringUtil.isNotEmpty(orderListGiftList)) {
			order.setOrderList(orderListGiftList.stream()
					.map(OrderListGift::covert).collect(toList()));
		}

		return order;
	}

	/**
	 * 补偿生鲜订单消息Job
	 */
	public int compensateAppOrderMsgJob() {
		List<AppOrderMsgRetry> appOrderMsgRetryList = appOrderMsgRetryMapper.selectAll();
		if (SpringUtil.isEmpty(appOrderMsgRetryList)) {
			return 0;
		}

		appOrderMsgRetryList.stream().sorted(Comparator.comparing(AppOrderMsgRetry::getId)).collect(Collectors.toList());
		for (AppOrderMsgRetry appOrderMsgRetry: appOrderMsgRetryList) {
			Order order = this.getOrderMsg(appOrderMsgRetry.getOrderId());
			if (null == order) {
				log.warn("\n 补偿生鲜订单消息Job.未查询到订单信息：appOrderMsgRetry={}", appOrderMsgRetry);
				continue;
			}
			
			try {
				switch (appOrderMsgRetry.getOperateType()) {
				case "INSERT":
					orderAsyncKafkaService.sendKafkaUpdateOrderMessage(order);
					appOrderMsgRetryMapper.deleteByPrimaryKey(appOrderMsgRetry.getId());
					break;
				case "UPDATE":
					orderAsyncKafkaService.sendKafkaUpdateOrderMessage(order);
					appOrderMsgRetryMapper.deleteByPrimaryKey(appOrderMsgRetry.getId());
					break;
				case "CANCEL":
					orderAsyncKafkaService.sendKafkaCancelOrder(order);
					appOrderMsgRetryMapper.deleteByPrimaryKey(appOrderMsgRetry.getId());
					break;
				default:
					log.warn("\n 补偿生鲜订单消息Job.不支持的消息操作类型：appOrderMsgRetry={}", appOrderMsgRetry);
					break;
				}
			} catch (Exception e) {
				log.error("\n 补偿生鲜订单消息Job.执行异常：appOrderMsgRetry={}", appOrderMsgRetry, e);
			}
		}
		return appOrderMsgRetryList.size();
	}

	public static void main(String[] args) {
		AppOrderMsgRetry msg = null;
		List<AppOrderMsgRetry> list = new ArrayList<>();

		msg = new AppOrderMsgRetry();
		msg.setId(1L);
		list.add(msg);

		msg = new AppOrderMsgRetry();
		msg.setId(2L);
		list.add(msg);

		System.out.println(list);
	}

}
