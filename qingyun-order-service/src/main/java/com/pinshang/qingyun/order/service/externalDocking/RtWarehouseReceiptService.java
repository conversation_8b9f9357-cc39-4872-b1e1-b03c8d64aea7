package com.pinshang.qingyun.order.service.externalDocking;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.pinshang.qingyun.base.configure.expand.QYAssert;
import com.pinshang.qingyun.box.utils.ConcurrentDateUtil;
import com.pinshang.qingyun.box.utils.SpringUtil;
import com.pinshang.qingyun.order.mapper.RtWarehouseReceiptMapper;
import com.pinshang.qingyun.order.mapper.entry.externalDocking.RtWarehouseReceiptEntry;
import com.pinshang.qingyun.order.vo.externalDocking.RtWarehouseReceiptVo;
import org.springframework.stereotype.Service;

import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.List;


/**
 * 大润发出库单
 * <AUTHOR>
 * @date 2023年2月15日10:00:09
 */
@Service
public class RtWarehouseReceiptService {

    private final RtWarehouseReceiptMapper rtWarehouseReceiptMapper;

    public RtWarehouseReceiptService(RtWarehouseReceiptMapper rtWarehouseReceiptMapper) {
        this.rtWarehouseReceiptMapper = rtWarehouseReceiptMapper;
    }

    /**
     * 大润发入库单列表分页查询
     * @param vo
     * @return
     */
    public PageInfo<RtWarehouseReceiptEntry> selectRtWarehouseReceiptPageInfo(RtWarehouseReceiptVo vo){
        QYAssert.isTrue(null != vo, "参数异常");
        QYAssert.isTrue(null != vo.getDeliveryTime(), "送货日期不能为空");
        QYAssert.isTrue(null != vo.getStoreShortName(), "客户简称不能为空");
        PageInfo<RtWarehouseReceiptEntry> pageDate = PageHelper.startPage(vo.getPageNo(), vo.getPageSize()).doSelectPageInfo(() -> {
            rtWarehouseReceiptMapper.selectRtWarehouseReceiptList(vo);
        });
        if(SpringUtil.isNotEmpty(pageDate.getList())){
            List<RtWarehouseReceiptEntry> list = pageDate.getList();
            list.forEach(p->{
                p.setSupplier("上海清美绿色食品（集团）有限公司");//供应商
                DateFormat df1 = SDF_FULL_DATE_TIME.get();
                p.setWarehousingTime(df1.format(new Date()));//送货日期
                Calendar c = Calendar.getInstance();
                try {
                    DateFormat dateFormat = ConcurrentDateUtil.SDF_FULL_DATE.get();
                    Date date = dateFormat.parse(p.getManufactureTime());
                    c.setTime(date);//传入指定时间
                    int day=c.get(Calendar.DATE);
                    c.set( Calendar.DATE,day-1);//得到送货日期的前一天
                    DateFormat df2 = SDF_FULL_DATE.get();
                    p.setManufactureTime(df2.format(c.getTime()));//生产日期 manufactureTime
                } catch (ParseException e) {
                    e.printStackTrace();
                }
                p.setOperator("诸杰");
            });
        }
        return pageDate;

    }
    public static ThreadLocal<DateFormat> SDF_FULL_DATE_TIME = new ThreadLocal<DateFormat>() {
        @Override
        protected DateFormat initialValue() {
            return new SimpleDateFormat("yyyy/MM/dd HH:mm:ss");
        }
    };
    public static ThreadLocal<DateFormat> SDF_FULL_DATE = new ThreadLocal<DateFormat>() {
        @Override
        protected DateFormat initialValue() {
            return new SimpleDateFormat("yyyy-MM-dd");
        }
    };
}
