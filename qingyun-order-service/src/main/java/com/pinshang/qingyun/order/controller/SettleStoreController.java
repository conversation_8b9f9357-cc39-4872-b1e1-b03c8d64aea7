package com.pinshang.qingyun.order.controller;

import com.github.pagehelper.PageInfo;
import com.pinshang.qingyun.order.mapper.entry.SettlementStoreEntry;
import com.pinshang.qingyun.order.mapper.entry.StoreEntry;
import com.pinshang.qingyun.order.service.SettleStoreService;
import com.pinshang.qingyun.order.vo.SettlementStoreVo;
import com.pinshang.qingyun.order.vo.StoreVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/settleStore")
public class SettleStoreController {

    @Autowired
    private SettleStoreService settleStoreService;


    /**
     * 条件查询客户
     */
    @RequestMapping(value = "/selectStore")
    public PageInfo<StoreEntry> selectStore(@RequestBody StoreVo vo){
        return settleStoreService.selectStore(vo);
    }

    /**
     * 条件查询结账客户
     * @param vo
     * @return
     */
    @RequestMapping(value = "/selectSettleStore")
    public PageInfo<SettlementStoreEntry> selectSettleStore(@RequestBody SettlementStoreVo vo){
        return settleStoreService.selectSettleStore(vo);
    }

    /**
     * 批量添加客户
     * @param storeCodes
     * @return
     */
    @GetMapping("/selectStoreByStores")
    public List<StoreEntry> selectStoreByStores(@RequestParam(value = "storeCodes",required = false) String storeCodes){
        return settleStoreService.selectStoreByStores(storeCodes);
    }

}
