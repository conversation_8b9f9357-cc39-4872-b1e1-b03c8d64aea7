package com.pinshang.qingyun.order.listener;

import com.alibaba.fastjson.JSON;
import com.pinshang.qingyun.box.utils.SpringUtil;
import com.pinshang.qingyun.kafka.base.KafkaMessageWrapper;
import com.pinshang.qingyun.kafka.base.KafkaTopicConstant;
import com.pinshang.qingyun.order.service.BStockShortService;
import com.pinshang.qingyun.order.service.WeChatSendMessageService;
import com.pinshang.qingyun.order.vo.BStockLackVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2024/03/12
 * @Version 1.0
 */
@Component
@Slf4j
public class BStockListener {
    @Autowired
    private WeChatSendMessageService weChatSendMessageService;

    @Autowired
    private BStockShortService bStockShortService;


    /**
     * 记录B端库存依据——商品库存不足的时间点
     */
    @KafkaListener(id="${application.name.switch}" + KafkaTopicConstant.B_STOCK_LACK_TOPIC,
            topics = {"${application.name.switch}" + KafkaTopicConstant.B_STOCK_LACK_TOPIC })
    public void stockShortListener(String message){
        log.info("====================记录B端库存依据——商品库存不足的时间点message==" + message);

        this.shortMessage(message);
    }

    @KafkaListener(id= KafkaTopicConstant.B_STOCK_LACK_TOPIC,
            topics = KafkaTopicConstant.B_STOCK_LACK_TOPIC )
    public void stockShortCompatibleListener(String message){
        log.info("====================记录B端库存依据——商品库存不足的时间点(兼容旧topic)message==" + message);

        this.shortMessage(message);
    }

    public void shortMessage(String message){
        try {
            KafkaMessageWrapper messageWrapper = JSON.parseObject(message, KafkaMessageWrapper.class);
            if (messageWrapper != null && messageWrapper.getData() != null) {
                log.info("data:{}", messageWrapper.getData().toString());
                List<BStockLackVO> voList = JSON.parseArray(messageWrapper.getData().toString(), BStockLackVO.class);
                if(SpringUtil.isNotEmpty(voList)){
                    bStockShortService.stockShort(voList);
                }
            }
        }catch (Exception e) {
            log.error("记录B端库存依据消费消息异常,异常:{}", e);
            //发送微信模板信息
            weChatSendMessageService.sendWeChatMessage("记录B端库存依据消费消息异常");
        }
    }
}
