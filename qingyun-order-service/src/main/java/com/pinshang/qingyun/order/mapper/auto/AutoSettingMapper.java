package com.pinshang.qingyun.order.mapper.auto;


import com.pinshang.qingyun.base.mybatis.MyMapper;
import com.pinshang.qingyun.order.model.auto.AutoSetting;
import com.pinshang.qingyun.order.model.auto.AutoShopCommodity;
import com.pinshang.qingyun.order.vo.auto.AutoSettingPageVO;
import com.pinshang.qingyun.order.vo.auto.AutoSettingRequestVO;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 */
@Repository
public interface AutoSettingMapper extends MyMapper<AutoSetting> {

    /**
     * 更新所有总部设置规划品项数量
     * @param headItems
     * @return
     */
    Integer updateHeadItems(@Param("headItems") Integer headItems);

    /**
     * 门店设置列表
     * @param vo
     * @return
     */
    List<AutoSettingPageVO> autoSettingPage(@Param("vo") AutoSettingRequestVO vo);

}