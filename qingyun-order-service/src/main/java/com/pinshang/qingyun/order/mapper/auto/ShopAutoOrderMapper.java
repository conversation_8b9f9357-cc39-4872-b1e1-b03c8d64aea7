package com.pinshang.qingyun.order.mapper.auto;


import com.pinshang.qingyun.order.dto.AutoShopCommodityODTO;
import com.pinshang.qingyun.order.model.auto.AutoOrderLog;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface ShopAutoOrderMapper{

    List<AutoShopCommodityODTO> queryAutoShopList();

    List<AutoShopCommodityODTO> queryAutoShopCommodityList(@Param("shopId") Long shopId);

    List<Long> queryAutoOrderLogCommodityIds(@Param("beginDate") String beginDate, @Param("endDate") String endDate, @Param("storeId") Long storeId);

    int batchInsertAutoOrderLog(@Param("autoOrderLogList") List<AutoOrderLog> autoOrderLogList);
}
