package com.pinshang.qingyun.order.controller;

import com.pinshang.qingyun.base.api.ApiResult;
import com.pinshang.qingyun.base.api.XdaTokenInfo;
import com.pinshang.qingyun.base.configure.expand.QYAssert;
import com.pinshang.qingyun.base.enums.xd.XdPayTypeEnum;
import com.pinshang.qingyun.base.util.FastThreadLocalUtil;
import com.pinshang.qingyun.order.config.LockConstants;
import com.pinshang.qingyun.order.dto.PrePayBillODTO;
import com.pinshang.qingyun.order.dto.PrePayOrderODTO;
import com.pinshang.qingyun.order.mapper.entry.payType.PayTypeEntry;
import com.pinshang.qingyun.order.service.recharge.RechargeService;
import com.pinshang.qingyun.order.util.PayTypeConvertUtil;
import com.pinshang.qingyun.order.vo.recharge.RechargeVo;
import com.pinshang.qingyun.pay.dto.SavePayLogIDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 * @Date 2020/12/23
 */
@RestController
@RequestMapping("/recharge")
@Api(value = "鲜达充值", tags = "xdaPayType", description = "鲜达充值" )
@Slf4j
public class RechargeController {
    @Autowired
    private RechargeService rechargeService;
    @Autowired
    private RedissonClient redissonClient;

    @ApiOperation(value = "鲜达充值", notes = "鲜达充值")
    @RequestMapping(value = "/xdaAppRecharge", method = RequestMethod.POST)
    public PrePayBillODTO xdaAppRecharge(@RequestBody RechargeVo vo) {
        XdaTokenInfo tokenInfo = FastThreadLocalUtil.getXDA();
        Long storeId = tokenInfo.getStoreId();
        QYAssert.isTrue(storeId != null, "未能获取到用户信息,请重新登录!");

        String lockKey = LockConstants.generateOrderCommitLock(storeId);
        RLock lock = redissonClient.getLock(lockKey);
        if(lock.tryLock()){
            try {
                vo.setStoreId(storeId);
                vo.setUserId(tokenInfo.getUserId());
                vo.setAppCode(tokenInfo.getAppCode());
                return rechargeService.xdaAppRecharge(vo);
            } finally {
                lock.unlock();
            }
        }else {
            QYAssert.isFalse("系统繁忙,请勿频繁操作!");
        }
        return null;
    }

    @ApiOperation(value = "鲜达补偿job", notes = "鲜达补偿job")
    @RequestMapping(value = "/xdaAppRechargeJob", method = RequestMethod.GET)
    public void xdaAppRechargeJob() {
        rechargeService.xdaAppRechargeJob();
    }

    /**
     * 查询有效的支付方式及促销语
     * @return
     */
    @ApiOperation(value = "查询有效的支付方式及促销语", notes = "查询有效的支付方式及促销语")
    @GetMapping("/queryValidPayMethodAndTip")
    public PayTypeEntry queryValidPayMethodAndTip(){
        return rechargeService.queryValidPayMethodAndTip();
    }

    /**
     * 人工退款
     * @return
     */
    @ApiOperation(value = "人工退款", notes = "人工退款")
    @ApiImplicitParam(name = "billCode", value = "订单id", required = true, paramType = "query", dataType = "Long")
    @RequestMapping(value = "/refundAllAmountTest", method = RequestMethod.POST)
    public ApiResult refundAllAmountTest(@RequestParam(value = "billCode",required = false) String billCode){
        QYAssert.isTrue(StringUtils.isNotBlank(billCode), "付款流水编码(充值单号)不能为null");
        return rechargeService.refundAllAmountTest(billCode);
    }

    @ApiOperation(value = "查询订单是否支付成功", notes = "查询订单是否支付成功")
    @GetMapping("/queryBillStatus/{billCode}")
    public Integer queryBillStatus(@PathVariable("billCode") String billCode){
        QYAssert.isTrue(StringUtils.isNotBlank(billCode), "付款流水编码(充值单号)不能为null");
        //return rechargeService.queryBillStatus(billCode);
        String lockKey = LockConstants.generateXdaQueryBillStatusLockKey(billCode);
        RLock lock = redissonClient.getLock(lockKey);
        if(lock.tryLock()){
            try {
                return rechargeService.queryBillStatus(billCode);
            } finally {
                lock.unlock();
            }
        }
        return 0;
    }
    @ApiOperation(value = "获取一个新的订单号", notes = "获取一个新的订单号")
    @GetMapping("/getNewBillCode")
    public String  getNewBillCode(){
        return rechargeService.getNewBillCode();
    }
    @ApiOperation(value = "获取一个新的订单号", notes = "获取一个新的订单号")
    @GetMapping("/getPrePay")
    public PrePayOrderODTO getPrePay(){
        return rechargeService.getPrePay();
    }



    @ApiOperation(value = "获取预订单billCode", notes = "获取预订单billCode")
    @GetMapping("/getPreOrderBillCode")
    public String  getPreOrderBillCode(){
        XdaTokenInfo xdaTokenInfo = FastThreadLocalUtil.getXDA();
        Long storeId = xdaTokenInfo.getStoreId();
        QYAssert.isTrue(!xdaTokenInfo.getIsTouristStore(), "游客无法下单。");
        QYAssert.isTrue(storeId != null, "未能获取到用户信息,请重新登录!");
        return rechargeService.getPreOrderBillCode(storeId);
    }

    @PostMapping("/savePayLog")
    public Boolean savePayLog(@RequestBody SavePayLogIDTO payLog) {
        XdPayTypeEnum xdPayTypeEnum = XdPayTypeEnum.getByCode(payLog.getPayType());
        payLog.setPayType(PayTypeConvertUtil.xdPayTypeEnumToPayTypeIntEnum(xdPayTypeEnum).getCode());
        return rechargeService.savePayLog(payLog);
    }
}
