package com.pinshang.qingyun.order.service.cup;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.pinshang.qingyun.base.configure.expand.QYAssert;
import com.pinshang.qingyun.box.utils.BeanCloneUtils;
import com.pinshang.qingyun.box.utils.DateUtil;
import com.pinshang.qingyun.order.dto.cup.*;
import com.pinshang.qingyun.order.mapper.cup.OrderCupReportMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.joda.time.DateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @Author: sk
 * @Date: 2024/7/16
 */
@Service
@Slf4j
public class OrderCupReportService {

    @Autowired
    private OrderCupReportMapper orderCupReportMapper;


    /**
     * 未订货查询
     * @param idto
     * @return
     */
    public NoOrderStoreResultODTO findNoOrderStorePage(NoOrderSearchPageIDTO idto) {
        QYAssert.isTrue(StringUtils.isNotBlank(idto.getStartDate()), "送货日期不能为空");
        QYAssert.isTrue(StringUtils.isNotBlank(idto.getEndDate()), "送货日期不能为空");

        Date endDate = DateUtil.parseDate(idto.getEndDate(), "yyyy-MM-dd");
        Date startDate = DateUtil.parseDate(idto.getStartDate(), "yyyy-MM-dd");
        int diff = DateUtil.getDayDif(endDate, startDate);
        QYAssert.isTrue(diff <= 30, "送货日期的跨度不能超过31天");

        PageInfo<NoOrderStoreODTO> pageDate = null ;
        pageDate = PageHelper.startPage(idto.getPageNo(), idto.getPageSize()).doSelectPageInfo(() -> {
            orderCupReportMapper.noOrderPageSearch(idto);
        });

        List<String> orderDataList = processOrderDataList(startDate, endDate);
        List<NoOrderStoreODTO> list = pageDate.getList();
        if(CollectionUtils.isNotEmpty(list)){
            list.forEach(store -> {
                List<String> cloneList = new ArrayList<>(orderDataList);
                store.setOrderList(cloneList);
                proceedStoreOrders(store, startDate, endDate);
            });
        }

        List<String> resultDateList = new ArrayList<>();
        orderDataList.forEach(item -> {
            resultDateList.add(item.split("#")[0]);
        });
        return new NoOrderStoreResultODTO(pageDate, resultDateList);
    }


    /**
     * 默认所有的用户所有日期都是未订的,
     * 查数据库,更新正确.
     *
     * @param store 客户
     */
    private void proceedStoreOrders(NoOrderStoreODTO store, Date startDate, Date endDate) {

        String storeId = store.getStoreId();
        List<String> orderList = store.getOrderList();

        List<OrderTimeCountODTO> orderTimeCountODTOS = orderCupReportMapper.findStoreOrder(Long.valueOf(storeId), startDate, endDate);
        //在这段时间内,有下单. 则要修改默认的(默认是全部没有下单)
        if (CollectionUtils.isNotEmpty(orderTimeCountODTOS)) {
            Map<String, Integer> orderTimeCountMap = orderTimeCountODTOS.stream().collect(Collectors.toMap(OrderTimeCountODTO::getOrderTimeForamt,OrderTimeCountODTO::getOrderTimeCount,(key1 , key2)-> key2));
            for (int i = 0; i < orderList.size(); i++) {
                String item = orderList.get(i);
                if (orderTimeCountMap.containsKey(item.split("#")[0])) {
                    String replace = item.replace("未订", "已订");
                    orderList.set(i, replace);
                }
            }

            //重排序,日期从小到大.
            orderList.sort((o1, o2) -> {
                String s = o1.split("#")[0];
                String s2 = o2.split("#")[0];
                DateTime dateTime = new DateTime(s.replace("/", "-"));
                DateTime dateTime2 = new DateTime(s2.replace("/", "-"));
                if (dateTime2.isBefore(dateTime)) {
                    return 1;
                } else {
                    return -1;
                }
            });
        }
    }

    public List<String> processOrderDataList(Date startDate, Date endDate) {
        List<String> result = new ArrayList<>();
        DateTime startTime = new DateTime(startDate.getTime());
        DateTime endTime = new DateTime(endDate.getTime());
        int count = 0;
        if (endTime.isBefore(startTime)) {
            endTime = startTime;
            startTime = endTime.minusDays(7);
        }

        while (!startTime.toString("yyyy/MM/dd").equals(endTime.toString("yyyy/MM/dd"))) {
            if (count > 30) {
                break;
            }
            result.add(startTime.toString("yyyy/MM/dd") + "#未订");
            count++;
            startTime = startTime.plusDays(1);
        }
        result.add(endTime.toString("yyyy/MM/dd") + "#未订");
        return result;
    }

    /**
     *获取发货时间
     * @return
     */
    public List<String> getDeliveryTime() {
        List<String> endTimes = new ArrayList<>();
        List<DeliveryTimeODTO> deliveryTimeODTOS = orderCupReportMapper.getDeliveryTime();
        for (DeliveryTimeODTO dt : deliveryTimeODTOS) {
            if(!endTimes.contains(dt.getEndTime())){
                endTimes.add(dt.getEndTime());
            }
        }
        return endTimes;
    }
}
