package com.pinshang.qingyun.order.controller.web;

import com.alibaba.excel.EasyExcel;
import com.pinshang.qingyun.base.util.ExcelUtil;
import com.pinshang.qingyun.order.bo.ExportLastMonthLineGroupBO;
import com.pinshang.qingyun.order.mapper.entry.orderStatistics.OrderCompanyAndStoreTypeEntry;
import com.pinshang.qingyun.order.service.CompanyProductOrderReportService;
import com.pinshang.qingyun.order.vo.order.ExportLastMonthLineGroupReqVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/05/18 10:25
 */
@RestController
@RequestMapping("/companyProductOrder")
@Slf4j
public class CompanyProductOrderReportController {

    @Autowired
    private CompanyProductOrderReportService companyProductOrderReportService;

    @GetMapping("/exportLastMonthLineGroupReport")
    public void exportLastMonthLineGroupReport(ExportLastMonthLineGroupReqVO vo,HttpServletResponse httpServletResponse){
        log.info("==============>>>>>>>>>>>>>>导出上月线路组汇总报表开始");
        try {
            List<ExportLastMonthLineGroupBO> list = companyProductOrderReportService.getList(vo);
            String fileName = "上月线路组汇总报表";
            ExcelUtil.setFileNameAndHead(httpServletResponse, fileName);
            EasyExcel.write(httpServletResponse.getOutputStream(), ExportLastMonthLineGroupBO.class).autoCloseStream(Boolean.FALSE).sheet("上月线路组汇总").doWrite(list);
        } catch (Exception e) {
            log.error("上月线路组汇总报表导出异常",e);
        }
        log.info("==============>>>>>>>>>>>>>>导出上月线路组汇总报表结束");
    }

}
