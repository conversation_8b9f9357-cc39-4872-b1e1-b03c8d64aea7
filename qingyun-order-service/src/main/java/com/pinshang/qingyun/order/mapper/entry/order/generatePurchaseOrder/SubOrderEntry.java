package com.pinshang.qingyun.order.mapper.entry.order.generatePurchaseOrder;

import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2017/6/20.
 */
@Data
public class SubOrderEntry {

    private String id;
    //订单id
    private String orderId;
    //品种合计
    private Integer varietyTotal;

    /*物流模式 0-直送, 1-配送, 2-直通*/
    private Integer logisticsModel;
    //供应商id
    private String supplierId;
    //0:未生成do单, 1:已生成do单
    private Integer status;
    //仓库id
    private String warehouseId;

    private String createId;

    private LocalDateTime createTime;

    private String enterpriseId;

    private String subOrderCode;

    private BigDecimal totalPrice;

    private LocalDateTime orderTime;

    private String purchaseOrderId;

    private String storeId;
    
    private Integer orderStatus;

    /** 0:普通订单 1：补货订单 2: 直送补货 **/
    private Integer modeType;
    
    private List<SubOrderItemModel> subOrderItems;

}
