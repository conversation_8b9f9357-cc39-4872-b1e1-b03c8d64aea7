package com.pinshang.qingyun.order.mapper.entry.order;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.pinshang.qingyun.order.vo.order.SaleReturnOrderPicVO;
import com.pinshang.qingyun.renderer.annotation.FieldRender;
import com.pinshang.qingyun.renderer.constant.RenderFieldHelper;
import com.pinshang.qingyun.renderer.enums.FieldTypeEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * Created by crell on 2017/11/6.
 */
@Data
public class SaleReturnItemEntry {
    private Long id;

    private String commodityId;

    private String commodityCode;

    private String commodityName;

    private String commoditySpec;

    private String barCode;

    @ApiModelProperty("单位")
    @FieldRender(fieldType = FieldTypeEnum.DICTIONARY,fieldName = RenderFieldHelper.Dictionary.optionName,keyName = "commodityUnitId")
    private String unit;

    @ApiModelProperty("退货价格")
    private BigDecimal price;

    @ApiModelProperty("退货数量")
    private BigDecimal returnQuantity;

    @ApiModelProperty("退货金额")
    private BigDecimal totalPrice;

    @ApiModelProperty("退货原因：t_dictionary表")
    private Integer returnReason;

    @ApiModelProperty("实退数量（合格数量+报损数量）")
    private BigDecimal realReturnQuantity;

    @ApiModelProperty("门店备注")
    private String remark;

    //商品所属物流模式、供应商、仓库
    private Integer logisticsModel;

    @ApiModelProperty("供应商id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long supplierId;

    @ApiModelProperty("仓库id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long warehouseId;

    private BigDecimal commodityPackageSpec;

    // 份数
    private Long shares;

    @ApiModelProperty("责任方类型(Responsible party) 1=配送，2=大仓，3=门店  ")
    private Integer rpType;

    private Integer ifWeight;

    private List<SaleReturnOrderPicVO> picList;
    private String videoUrl;
    private String visitVideoUrl;

    private Long commodityUnitId; // 单位id

    /** 实发数量 */
    private BigDecimal deliveryQuantity;

    /** 档口id */
    private String stallId;
}
