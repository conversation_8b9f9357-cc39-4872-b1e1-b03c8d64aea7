package com.pinshang.qingyun.order.service;

import com.pinshang.qingyun.base.configure.expand.QYAssert;
import com.pinshang.qingyun.base.enums.IogisticsModelEnums;
import com.pinshang.qingyun.base.enums.YesOrNoEnums;
import com.pinshang.qingyun.base.enums.storage.StockTypeEnum;
import com.pinshang.qingyun.box.utils.SpringUtil;
import com.pinshang.qingyun.order.dto.GrouponCommodtyOrderDTO;
import com.pinshang.qingyun.order.dto.MsShopOrderSettingBaseDTO;
import com.pinshang.qingyun.order.enums.DeliveryBatchTypeEnum;
import com.pinshang.qingyun.order.enums.ProductTypeEnums;
import com.pinshang.qingyun.order.mapper.ShopMapper;
import com.pinshang.qingyun.order.mapper.entry.MdShopOrderSettingEntry;
import com.pinshang.qingyun.order.mapper.groupon.GrouponOrderLogMapper;
import com.pinshang.qingyun.order.model.groupon.GrouponOrderLogModel;
import com.pinshang.qingyun.order.model.shop.Shop;
import com.pinshang.qingyun.order.vo.order.OrderDto;
import com.pinshang.qingyun.order.vo.order.OrderItemDto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @Author: sk
 * @Date: 2023/4/13
 */
@Slf4j
@Service
public class OrderSplitService {
    @Autowired
    private ConsigneeCommonService consigneeCommonService;
    @Lazy
    @Autowired
    private MdShopOrderSettingService mdShopOrderSettingService;
    @Autowired
    private ShopMapper shopMapper;
    @Autowired
    private GrouponOrderLogMapper grouponOrderLogMapper;

    /**
     * 1.根据订单时间、门店id和门店订货通用设置进行拆单
     * 2.转成OrderDto对象
     */
    public <T extends MsShopOrderSettingBaseDTO> List<OrderDto> convertToOrderDto(List<T> list, String orderRemark, Boolean ifConsigneeSplit) {
        /*if(ifConsigneeSplit){
            Map<Long,Long> consigneeCommMap = consigneeCommonService.getConsigneeCommMap(null, list.get(0).getStoreId());
            list.forEach( i ->{
                i.setConsignmentId(consigneeCommMap.get(i.getCommodityId()));
            });
        }*/
        // 1.根据订单时间、门店id和门店订货通用设置进行拆单
        String splitCode = "_";
        Map<String, List<T>> groupMap = list.stream().collect(Collectors.groupingBy(item -> {
            String groupByField = "";
            Integer presaleStatus = item.getPresaleStatus() != null ? item.getPresaleStatus() : YesOrNoEnums.NO.getCode();
            Integer stockType = item.getStockType() != null ? item.getStockType() : StockTypeEnum.UN_LIMIT.getCode();
            if(ifConsigneeSplit && item.getConsignmentId() != null && item.getConsignmentId() > 0){
                groupByField = item.getOrderTime() + splitCode + item.getStoreId() + splitCode +
                        item.getLogisticsModel() + splitCode + item.getWarehouseId() + splitCode + item.getDeleveryTimeRange() + item.getConsignmentId()
                        + splitCode + stockType + splitCode + presaleStatus;
            }else {
                groupByField = item.getOrderTime() + splitCode + item.getStoreId() + splitCode +
                        item.getLogisticsModel() + splitCode + item.getWarehouseId() + splitCode + item.getDeleveryTimeRange()
                        + splitCode + stockType + splitCode + presaleStatus;
            }

            if (item.getLogisticsModel() == IogisticsModelEnums.DISPATCHING.getCode()) {
                return groupByField;
            } else if (item.getLogisticsModel() == IogisticsModelEnums.DIRECT_CONNECTION.getCode()) {
                return groupByField + splitCode + item.getSupplierId();
            }else {
                return groupByField;
            }
        }));

        // 2.转成OrderDto对象
        List<OrderDto> orderDtoList = new ArrayList<>();
        groupMap.forEach((key, settingList) -> {
            OrderDto orderDto = new OrderDto();
            T t = settingList.get(0);
            BeanUtils.copyProperties(t,orderDto);
            orderDto.setOrderRemark(orderRemark);
            Set<Long> originOrderIdList = new HashSet<>();
            Map<Long, List<Long>> originOrderMap = new HashMap<>();
            settingList.forEach(i -> {
                OrderItemDto orderItemDto = new OrderItemDto(i.getCommodityId() + "", i.getCommodityCode(), i.getCommodityName(), i.getPrice(), i.getQuantity(), "订单商品", ProductTypeEnums.PRODUCT.getCode());
                orderItemDto.setOriginalPrice(orderItemDto.getPrice());
                orderItemDto.setPresaleStatus(i.getPresaleStatus() != null ? i.getPresaleStatus() : YesOrNoEnums.NO.getCode());
                orderItemDto.setStockType(i.getStockType() != null ? i.getStockType() : StockTypeEnum.UN_LIMIT.getCode());
                orderItemDto.setSourceRatio(i.getSourceRatio());
                orderItemDto.setTargetRatio(i.getTargetRatio());
                orderItemDto.setConvertStatus(i.getConvertStatus());
                orderItemDto.setTargetQuantity(i.getTargetQuantity());
                orderItemDto.setTargetCommodityId(i.getTargetCommodityId());
                orderDto.addItem(orderItemDto);

                if(i.getOriginOrderId() != null){
                    originOrderIdList.add(i.getOriginOrderId());

                    List<Long> commodityIdList;
                    if(originOrderMap.containsKey(i.getOriginOrderId())){
                        commodityIdList = originOrderMap.get(i.getOriginOrderId());
                        if(!commodityIdList.contains(i.getCommodityId())){
                            commodityIdList.add(i.getCommodityId());
                        }
                    }else {
                        commodityIdList = new ArrayList<>();
                        commodityIdList.add(i.getCommodityId());
                    }
                    originOrderMap.put(i.getOriginOrderId(), commodityIdList);
                }
            });
            orderDto.setOriginOrderMap(originOrderMap);
            orderDto.setOriginOrderIdList(new ArrayList<>(originOrderIdList));

            // 商品有可能重复，进行合并
            List<OrderItemDto> resultList = orderDto.getItems().stream()
                    .collect(Collectors.toMap(OrderItemDto::getProductId, a -> a, (o1, o2)-> {
                        o1.setProductNum(o1.getProductNum().add(o2.getProductNum()));
                        if (Objects.nonNull(o1.getTargetQuantity()) && Objects.nonNull(o2.getTargetQuantity())) {
                            o1.setTargetQuantity(o1.getTargetQuantity().add(o2.getTargetQuantity()));
                        }
                        return o1;
                    })).values().stream().collect(Collectors.toList());

            orderDto.setItems(resultList);
            orderDtoList.add(orderDto);
        });
        return orderDtoList;
    }



    /**
     * 设置storeId、物流模式、供应商、仓库
     * @param grouponCommodityList
     */
    public void setGroupOrderSetting(List<GrouponCommodtyOrderDTO> grouponCommodityList){
        List<Long> shopIdList = grouponCommodityList.stream().map(item -> item.getShopId()).collect(Collectors.toList());

        Example shopEx = new Example(Shop.class);
        shopEx.createCriteria().andIn("id", shopIdList);
        List<Shop> shopList = shopMapper.selectByExample(shopEx);
        Map<Long, Shop> shopMap = shopList.stream().collect(Collectors.toMap(Shop::getId, Function.identity()));

        for(GrouponCommodtyOrderDTO odto : grouponCommodityList){
            Shop shop = shopMap.get(odto.getShopId());
            odto.setStoreId(shop.getStoreId());

            List<String> commodityIdList = new ArrayList<>();
            commodityIdList.add(odto.getCommodityId() + "");
            List<MdShopOrderSettingEntry> settingList = mdShopOrderSettingService.queryMdShopOrderSettingListByIds(shop.getStoreId(), commodityIdList);
            QYAssert.isTrue(SpringUtil.isNotEmpty(settingList), "门店订货配置信息有误");
            MdShopOrderSettingEntry settingEntry = settingList.get(0);

            odto.setLogisticsModel(settingEntry.getLogisticsModel().intValue());
            odto.setSupplierId(Long.valueOf(settingEntry.getSupplierId()));
            odto.setWarehouseId(Long.valueOf(settingEntry.getWarehouseId()));
            odto.setDeleveryTimeRange(settingEntry.getDeleveryTimeRange());


            odto.setEnterpriseId(78L);
            odto.setUserId(-1L);
            odto.setCreateName("系统");
            odto.setDeliveryBatch(DeliveryBatchTypeEnum.ONE_BATCH.getCode().toString());
        }
    }


    /**
     * 记录清美团购、云超团购id 日志
     * @param groupIdList
     */
    public void insertGrouponOrderLog(List<Long> groupIdList, Integer groupType){
        Set set = new HashSet();
        set.addAll(groupIdList);
        List<Long> newGroupIdList = new ArrayList();
        newGroupIdList.addAll(set);

        List<GrouponOrderLogModel> grouponOrderLogModelList = new ArrayList<>();
        for(Long groupId:newGroupIdList){
            GrouponOrderLogModel grouponOrderLogModel = new GrouponOrderLogModel();
            grouponOrderLogModel.setGrouponType(groupType);
            grouponOrderLogModel.setGrouponId(groupId);
            grouponOrderLogModel.setCreateId(-1L);
            grouponOrderLogModel.setCreateTime(new Date());
            grouponOrderLogModelList.add(grouponOrderLogModel);
        }
        grouponOrderLogMapper.insertList(grouponOrderLogModelList);
    }
}
