package com.pinshang.qingyun.order.manage.delivery.service;

import com.pinshang.qingyun.base.configure.expand.QYAssert;
import com.pinshang.qingyun.order.manage.delivery.dto.DeliveryOrderDTO;
import com.pinshang.qingyun.order.manage.delivery.dto.StoreInfo;
import com.pinshang.qingyun.order.manage.delivery.setting.StoreDeliverySetting;
import com.pinshang.qingyun.order.mapper.StoreSettlementMapper;
import com.pinshang.qingyun.order.model.order.StoreSettlement;
import com.pinshang.qingyun.order.model.store.Store;
import com.pinshang.qingyun.order.service.ShopService;
import com.pinshang.qingyun.order.service.StoreService;
import com.pinshang.qingyun.store.dto.storeType.StoreTypeDescIDTO;
import com.pinshang.qingyun.store.dto.storeType.StoreTypeDescODTO;
import com.pinshang.qingyun.store.service.StoreTypeClient;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
public class StoreDeliverySettingService {

    private final ShopService shopService;

    private final StoreSettlementMapper storeSettlementMapper;

    private final StoreTypeClient storeTypeClient;

    private final StoreService storeService;

    public StoreDeliverySettingService(ShopService shopService, StoreSettlementMapper storeSettlementMapper, StoreTypeClient storeTypeClient, StoreService storeService) {
        this.shopService = shopService;
        this.storeSettlementMapper = storeSettlementMapper;
        this.storeTypeClient = storeTypeClient;
        this.storeService = storeService;
    }

    public Map<Long,StoreDeliverySetting> getStoreDeliverySetting(List<DeliveryOrderDTO> deliveryOrderDTOS) {

        QYAssert.isTrue(deliveryOrderDTOS
                        .stream()
                    .allMatch(deliveryOrderDTO -> deliveryOrderDTO.getStoreInfo() != null && deliveryOrderDTO.getStoreInfo().getStoreId() !=null),"客户id全不能为空");
        QYAssert.isTrue(deliveryOrderDTOS
                .stream()
                .allMatch(deliveryOrderDTO -> deliveryOrderDTO.getStoreTypeId() != null),"客户类型id全不能为空");

        Map<Long,StoreDeliverySetting> ret = new HashMap<>();
        List<Long> storeIds = deliveryOrderDTOS.stream().map(DeliveryOrderDTO::getStoreInfo).map(StoreInfo::getStoreId).collect(Collectors.toList());
        Map<Long, Boolean> jmShopMap = shopService.listJmShop(storeIds);

        List<Store> storeListByStoreIdList = storeService.findStoreListByStoreIdList(storeIds);
        Map<Long, Store> storeMap = storeListByStoreIdList.stream().collect(Collectors.toMap(Store::getId, Function.identity()));

        Map<Long, DeliveryOrderDTO> deliveryOrderMap = deliveryOrderDTOS.stream().collect(Collectors.toMap(DeliveryOrderDTO::getOrderId, Function.identity()));
        List<DeliveryOrderDTO> deliveryFinishEntryList = new ArrayList<>(deliveryOrderMap.values());
        List<StoreSettlement> storeSettlements = storeSettlementMapper.listStoreSettlementByStoreIds(storeIds);
        Map<Long, StoreSettlement> storeSettlementMap = storeSettlements.stream().collect(Collectors.toMap(StoreSettlement::getStoreId, Function.identity()));

        List<Long> storeTypeIds = deliveryFinishEntryList.stream().map(DeliveryOrderDTO::getStoreTypeId).collect(Collectors.toList());
        StoreTypeDescIDTO storeTypeDescIDTO = new StoreTypeDescIDTO();
        storeTypeDescIDTO.setIdList(storeTypeIds);

        List<StoreTypeDescODTO> storeTypeDescODTOS = storeTypeClient.selectStoreTypeData(storeTypeDescIDTO);
        Map<Long, StoreTypeDescODTO> storeTypeDescIdMap = storeTypeDescODTOS.stream().collect(Collectors.toMap(StoreTypeDescODTO::getId, Function.identity()));


        deliveryOrderDTOS.forEach(
                deliveryOrderDTO -> {
                    StoreDeliverySetting storeDeliverySetting = new StoreDeliverySetting();
                    Boolean collectStatus = storeSettlementMap.get(deliveryOrderDTO.getStoreInfo().getStoreId()) == null?false:storeSettlementMap.get(deliveryOrderDTO.getStoreInfo().getStoreId()).getCollectStatus();
                    Integer settleBillReturnAmountState = storeTypeDescIdMap.get(deliveryOrderDTO.getStoreTypeId()) == null ? null : storeTypeDescIdMap.get(deliveryOrderDTO.getStoreTypeId()).getSettleBillReturnAmountState();
                    Integer settleBillCalcType = storeTypeDescIdMap.get(deliveryOrderDTO.getStoreTypeId()) == null ? null : storeTypeDescIdMap.get(deliveryOrderDTO.getStoreTypeId()).getSettleBillCalcType();
                    Boolean jmFlag = jmShopMap.get(deliveryOrderDTO.getStoreInfo().getStoreId()) == null ? false : jmShopMap.get(deliveryOrderDTO.getStoreInfo().getStoreId());
                    Integer storeType = storeMap.get(deliveryOrderDTO.getStoreInfo().getStoreId())==null? null:storeMap.get(deliveryOrderDTO.getStoreInfo().getStoreId()).getStoreType();
                    String storeTypeCode = storeTypeDescIdMap.get(deliveryOrderDTO.getStoreTypeId()) == null ? null : storeTypeDescIdMap.get(deliveryOrderDTO.getStoreTypeId()).getStoreTypeCode();

                    storeDeliverySetting.setIsPrePayStoreAccount(collectStatus);
                    storeDeliverySetting.setSettleBillCalcType(settleBillCalcType);
                    storeDeliverySetting.setSettleBillReturnAmountState(settleBillReturnAmountState);
                    storeDeliverySetting.setIsXsjm(jmFlag);
                    storeDeliverySetting.setStoreType(storeType);
                    storeDeliverySetting.setStoreTypeId(deliveryOrderDTO.getStoreTypeId());
                    storeDeliverySetting.setStoreTypeCode(storeTypeCode);
                    ret.put(deliveryOrderDTO.getOrderId(),storeDeliverySetting);
                }
        );

        return ret;
    }
}
