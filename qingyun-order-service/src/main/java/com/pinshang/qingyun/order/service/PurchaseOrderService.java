package com.pinshang.qingyun.order.service;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.pinshang.qingyun.base.configure.expand.QYAssert;
import com.pinshang.qingyun.base.util.FastThreadLocalUtil;
import com.pinshang.qingyun.box.utils.BeanCloneUtils;
import com.pinshang.qingyun.box.utils.DateUtil;
import com.pinshang.qingyun.order.dto.consignment.ConsignmentOrderPageODTO;
import com.pinshang.qingyun.order.dto.consignment.ConsignmentOrderQueryIDTO;
import com.pinshang.qingyun.order.mapper.CommodityMapper;
import com.pinshang.qingyun.order.mapper.PreOrderMapper;
import com.pinshang.qingyun.order.mapper.ShopMapper;
import com.pinshang.qingyun.order.mapper.entry.commodity.CommodityBasicEntry;
import com.pinshang.qingyun.order.mapper.entry.purchase.DirectSendingPurchaseItemEntry;
import com.pinshang.qingyun.order.model.shop.Shop;
import com.pinshang.qingyun.order.vo.commodity.CommodityVO;
import com.pinshang.qingyun.purchase.dto.DirectSendingPurchaseItemODto;
import com.pinshang.qingyun.purchase.dto.ShopPurchaseSearchIDto;
import com.pinshang.qingyun.purchase.service.PurchaseListClient;
import com.pinshang.qingyun.shop.admin.dto.ConsignmentSupplierInfoODTO;
import com.pinshang.qingyun.shop.dto.ShopDto;
import com.pinshang.qingyun.shop.service.ShopClient;
import com.pinshang.qingyun.smm.dto.user.SelectUserShopIdListIDTO;
import com.pinshang.qingyun.smm.service.SMMUserClient;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.joda.time.DateTime;
import org.joda.time.format.DateTimeFormat;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * Created by honway on 2017/4/7 11:14.
 * 采购单服务
 */
@Service
@Slf4j
public class PurchaseOrderService {

    @Autowired
    private PurchaseListClient purchaseListClient;

    @Autowired
    private PreOrderMapper preOrderMapper;
    @Autowired
    private ConsignmentSupplierService consignmentSupplierService;
    @Autowired
    private ShopMapper shopMapper;
    @Autowired
    private SMMUserClient sMMUserClient;
    @Autowired
    private ShopClient shopClient;
    @Autowired
    private CommodityMapper commodityMapper;

    public List<DirectSendingPurchaseItemODto> findDirectSendingPurchaseItems(ShopPurchaseSearchIDto shopPurchaseSearchIDto){
        List<DirectSendingPurchaseItemODto> result = new ArrayList<>();

        List<DirectSendingPurchaseItemEntry> list = preOrderMapper.getMdPreOrderList(shopPurchaseSearchIDto.getSupplierId(), shopPurchaseSearchIDto.getOrderTime());
        if (!CollectionUtils.isEmpty(list)) {
            Map<Long, DirectSendingPurchaseItemEntry> preOrderMap = list.stream().collect(Collectors.toMap(DirectSendingPurchaseItemEntry::getPurchaseOrderId, Function.identity()));

            List<Long> purchaseOrderIdList = list.stream().map(item -> item.getPurchaseOrderId()).collect(Collectors.toList());
            shopPurchaseSearchIDto.setPurchaseOrderIdList(purchaseOrderIdList);

            result = purchaseListClient.findDirectSendingPurchaseItems(shopPurchaseSearchIDto);

            if (!CollectionUtils.isEmpty(result)) {
                result.forEach(item -> {
                    DirectSendingPurchaseItemEntry entry = preOrderMap.get(item.getPurchaseOrderId());
                    if (entry != null) {
                      item.setOrderTime(entry.getOrderTime());
                      item.setReceiveStatus(entry.getReceiveStatus());
                      item.setSubOrderCode(entry.getSubOrderCode());
                      item.setShopName(entry.getShopName());
                    }
                });
            }
        }

        return result;
    }

    /**
     * 查询最近半年未收货的代销供应商订单(直送订单, pos调用)
     * @return
     */
    public Boolean existNotReceiveConsignmentOrder(Long shopId){
        Shop shop = shopMapper.selectByPrimaryKey(shopId);
        Long storeId = shop.getStoreId();
        List<ConsignmentSupplierInfoODTO> consignmentSupplierList = consignmentSupplierService.queryConsignmentSupplierList(Collections.singletonList(storeId), null);
        if(org.apache.commons.collections.CollectionUtils.isNotEmpty(consignmentSupplierList)){
            List<Long> commodityIdList = consignmentSupplierList.stream().map(ConsignmentSupplierInfoODTO::getCommodityId).collect(Collectors.toList());
            List<Long> supplierIdList = consignmentSupplierList.stream().map(ConsignmentSupplierInfoODTO::getSupplierId).collect(Collectors.toList());

            String  beginDate = DateUtil.getDateFormate(DateUtil.addDay(new Date(), -180), "yyyy-MM-dd")+" 00:00:00";
            String  endDate = DateUtil.getDateFormate(DateUtil.addDay(new Date(), -1), "yyyy-MM-dd")+" 23:59:59";
            Integer count = preOrderMapper.countNotReceiveConsignmentOrder(storeId, beginDate, endDate, commodityIdList, supplierIdList);
            return count > 0;
        }else {
            return false;
        }
    }


    /**
     * 代销订货明细
     * @param vo
     * @return
     */
    @Deprecated
    public PageInfo<ConsignmentOrderPageODTO> consignmentOrderReport(ConsignmentOrderQueryIDTO vo) {
        PageInfo<ConsignmentOrderPageODTO> pageInfo = new PageInfo();

        QYAssert.isTrue(StringUtils.isNotBlank(vo.getBeginDate()), "请选择送货日期");
        QYAssert.isTrue(StringUtils.isNotBlank(vo.getEndDate()), "请选择送货日期");

        DateTime beginDate = DateTime.parse(vo.getBeginDate(), DateTimeFormat.forPattern("yyyy-MM-dd"));
        DateTime endDate = DateTime.parse(vo.getEndDate(), DateTimeFormat.forPattern("yyyy-MM-dd"));
        QYAssert.isTrue(!beginDate.plusDays(30).isBefore(endDate), "送货日期不能大于31天");

        if(vo.getCommodityId() != null || StringUtils.isNotBlank(vo.getBarCode())
                    || vo.getCateId1() != null || vo.getCateId2() != null || vo.getCateId3() != null){
            CommodityVO commodityVO = BeanCloneUtils.copyTo(vo, CommodityVO.class);
            List<CommodityBasicEntry> basicEntryList = commodityMapper.findCommodityBasicListByParam(commodityVO);
            if(org.apache.commons.collections.CollectionUtils.isNotEmpty(basicEntryList)){
                List<Long> commodityIdList = basicEntryList.stream().map(item -> Long.valueOf(item.getCommodityId())).collect(Collectors.toList());
                vo.setCommodityIdList(commodityIdList);
            }else {
                return pageInfo;
            }
        }


        List<Long> shopIdList = sMMUserClient.selectUserShopIdList( SelectUserShopIdListIDTO.onlyDb(FastThreadLocalUtil.getQY().getUserId()));
        if (org.apache.commons.collections.CollectionUtils.isEmpty(shopIdList)){
            return pageInfo;
        }

        if (StringUtils.isNotBlank(vo.getOrgCode())) {
            List<ShopDto> shopDtos = shopClient.selectShopListByParentOrgCode(vo.getOrgCode());
            if (org.apache.commons.collections.CollectionUtils.isNotEmpty(shopDtos)) {
                List<Long> orgShopIdList = shopDtos.stream().map(ShopDto::getId).collect(Collectors.toList());
                shopIdList.retainAll(orgShopIdList);

                if (org.apache.commons.collections.CollectionUtils.isEmpty(shopIdList)) {
                    return pageInfo;
                }
            }else {
                return pageInfo;
            }
        }
        vo.setShopIdList(shopIdList);

        pageInfo= PageHelper.startPage(vo.getPageNo(), vo.getPageSize()).doSelectPageInfo(() -> {
            preOrderMapper.consignmentOrderReport(vo);
        });

        List<ConsignmentOrderPageODTO> list = pageInfo.getList();
        for(ConsignmentOrderPageODTO odto : list){
            //odto.setAuditQuantity(odto.getRealReceiveQuantity());
            if(odto.getAuditQuantity() != null){
                odto.setDifferQuantity(odto.getRequireQuantity().subtract(odto.getAuditQuantity()));
            }
        }
        return pageInfo;
    }
}
