package com.pinshang.qingyun.order.manage.delivery.strategy.impl;

import com.pinshang.qingyun.base.constant.SystemUserConstant;
import com.pinshang.qingyun.base.enums.StoreBillTypeEnums;
import com.pinshang.qingyun.order.bo.StoreDeductionBO;
import com.pinshang.qingyun.order.manage.delivery.dto.DeliveryOrderDTO;
import com.pinshang.qingyun.order.manage.delivery.setting.StoreDeliverySetting;
import com.pinshang.qingyun.order.manage.delivery.strategy.DeliveryStrategy;
import com.pinshang.qingyun.order.manage.delivery.utils.PfDeliverySettingUtils;
import com.pinshang.qingyun.order.service.StoreRechargeService;
import com.pinshang.qingyun.settlementTb.service.StoreRechargeClient;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.transaction.support.TransactionSynchronizationAdapter;
import org.springframework.transaction.support.TransactionSynchronizationManager;

import java.math.BigDecimal;
import java.util.Date;

import static com.pinshang.qingyun.order.manage.delivery.constant.DeliveryConstants.*;

@Component
@Slf4j
public class RealTimeOverShipmentStrategy implements DeliveryStrategy {

    private final StoreRechargeClient storeRechargeClient;
    private final StoreRechargeService storeRechargeService;


    public RealTimeOverShipmentStrategy(StoreRechargeClient storeRechargeClient, StoreRechargeService storeRechargeService) {
        this.storeRechargeClient = storeRechargeClient;
        this.storeRechargeService = storeRechargeService;
    }

    @Override
    public void handle(DeliveryOrderDTO deliveryOrderDTO, StoreDeliverySetting storeDeliverySetting) {
        if (isOverDeliveryChargeApplicable(deliveryOrderDTO,storeDeliverySetting)) {
            TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronizationAdapter() {
                @Override
                public void afterCommit() {
                    triggerCharge(deliveryOrderDTO,storeDeliverySetting);
                }
            });
        }

    }

    //超发处理
    private void triggerCharge(DeliveryOrderDTO deliveryOrderDTO,StoreDeliverySetting storeDeliverySetting) {
        BigDecimal money = deliveryOrderDTO.getOverDeliveryMoney();
        if (money != null && money.compareTo(BigDecimal.ZERO) > 0) {
            log.warn("监听出库消息 订单：{} 子单出库完后 存在多发【money={}】 进行扣款 多发扣款执行开始...", deliveryOrderDTO.getOrderCode(), money);
            StoreDeductionBO deductionBO = StoreDeductionBO.builder()
                    .orderCode(deliveryOrderDTO.getOrderCode())
                    .orderId(deliveryOrderDTO.getOrderId())
                    .orderAmount(money)
                    .storeId(deliveryOrderDTO.getStoreInfo().getStoreId())
                    .tradeTime(new Date())
                    .orderTime(deliveryOrderDTO.getOrderTime())
                    .userId(SystemUserConstant.SYSTEM_USER_ID)
                    .build();
            boolean pfsStoreTypeFlag = PFS_STORETYPE_CODE.equals(storeDeliverySetting.getStoreTypeCode());
            if(pfsStoreTypeFlag){
                deductionBO.setRemark("<--批发超发：" + deliveryOrderDTO.getOrderCode() + "-->");
                deductionBO.setTradeCode(PFS_OVER_DELIVERY_FLAG + deliveryOrderDTO.getOrderCode());
                deductionBO.setBillType(StoreBillTypeEnums.PF_OUTRUN_DEDUCTION.getCode());
            }else{
                deductionBO.setRemark("<--多发扣款：" + deliveryOrderDTO.getOrderCode() + "-->");
                deductionBO.setTradeCode(OVER_DELIVERY_FLAG + deliveryOrderDTO.getOrderCode());
                deductionBO.setBillType(StoreBillTypeEnums.SHOP_OUTRUN_DEDUCTION.getCode());
            }

            try {
                storeRechargeService.storeDeduction(deductionBO);
            } catch (Exception e) {
                log.warn("is method triggerCharge storeRechargeClient.deductionAccount(dto) fegin远程调用失败 error:{}", e);
            }
            log.warn("监听出库消息 订单：{} 子单出库完后 存在多发【money={}】 进行扣款 多发扣款执行结束...", deliveryOrderDTO.getOrderCode(), money);
        }
    }

    //超发条件  ----  预付 & 【【客户类型： 鲜食加盟】 || 【批发】】
    private boolean isOverDeliveryChargeApplicable(DeliveryOrderDTO deliveryOrderDTO,StoreDeliverySetting storeDeliverySetting) {
        boolean isPrePayAccountFlag = storeDeliverySetting.getIsPrePayStoreAccount() == null ? false : storeDeliverySetting.getIsPrePayStoreAccount().booleanValue();

        return isPrePayAccountFlag && (overDeliveryFlag(storeDeliverySetting) || PfDeliverySettingUtils.ifPfUnderDeliveryConfition(deliveryOrderDTO,storeDeliverySetting));

    }

    private boolean overDeliveryFlag(StoreDeliverySetting storeDeliverySetting){
        boolean isXsjm = storeDeliverySetting.getIsXsjm()==null?false: storeDeliverySetting.getIsXsjm().booleanValue();
        return isXsjm;
    }


}
