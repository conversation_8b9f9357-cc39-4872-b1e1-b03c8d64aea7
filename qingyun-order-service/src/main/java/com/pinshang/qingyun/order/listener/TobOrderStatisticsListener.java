package com.pinshang.qingyun.order.listener;

import com.pinshang.qingyun.box.utils.DateUtil;
import com.pinshang.qingyun.box.utils.JsonUtil;
import com.pinshang.qingyun.kafka.base.KafkaMessageWrapper;
import com.pinshang.qingyun.kafka.base.KafkaTopicConstant;
import com.pinshang.qingyun.order.mapper.ProcessOrderCommodityStatisticsMapper;
import com.pinshang.qingyun.order.model.tob.ToBProcessOrderCommodityStatistics;
import com.pinshang.qingyun.order.service.tob.ITobCommodityStatisticsService;
import com.pinshang.qingyun.order.vo.tob.ToBOrderStatisticsReqVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;

import java.math.BigDecimal;
import java.util.Date;

@Slf4j
@Service
public class TobOrderStatisticsListener {

    @Autowired
    private ProcessOrderCommodityStatisticsMapper orderStatisticsMapper;
    @Autowired
    private ITobCommodityStatisticsService iTobCommodityStatisticsService;


    @KafkaListener(
            topics = {"${application.name.switch}" + KafkaTopicConstant.TOB_ORDER_COMMODITY_STATISTICS,
                    "${spring.application.name}-Retry-${application.name.switch}" + KafkaTopicConstant.TOB_ORDER_COMMODITY_STATISTICS},
            containerFactory = "kafkaListenerContainerFactory", errorHandler = "kafkaConsumerErrorHandler"
            , concurrency = "1"
    )
    public void cloudOrderListener(String message){
        log.info("云超订单listener:{}", message);
        KafkaMessageWrapper messageWrapper = JsonUtil.json2java(message, KafkaMessageWrapper.class);
        ToBOrderStatisticsReqVo reqVo = JsonUtil.json2java(JsonUtil.java2json(messageWrapper.getData()), ToBOrderStatisticsReqVo.class);
        if (reqVo == null){
            log.info("dc监听云超订单数据异常message:{}",message);
        }else {
            iTobCommodityStatisticsService.updateTobOrderStatistics(reqVo);
        }
    }

    private void updateTobOrderStatistics(ToBOrderStatisticsReqVo reqVo) {
        // 入参：送货时间；商品ID；下单数量；下单份数,类型：1：新增订单；2：取消订单；
        if (reqVo.getType() == 1) {
            try {
                ToBProcessOrderCommodityStatistics statistics = new ToBProcessOrderCommodityStatistics(reqVo);
                orderStatisticsMapper.insert(statistics);
            } catch (Exception e) {
                Example example = new Example(ToBProcessOrderCommodityStatistics.class);
                example.createCriteria().andEqualTo("orderTime", DateUtil.get4yMd(reqVo.getOrderTime())).andEqualTo("commodityId", reqVo.getCommodityId());
                ToBProcessOrderCommodityStatistics newStatistics = orderStatisticsMapper.selectOneByExample(example);
                if (newStatistics == null) {
                    ToBProcessOrderCommodityStatistics statistics = new ToBProcessOrderCommodityStatistics(reqVo);
                    orderStatisticsMapper.insert(statistics);
                } else {
                    example = new Example(ToBProcessOrderCommodityStatistics.class);
                    example.createCriteria().andEqualTo("id", newStatistics.getId());
                    ToBProcessOrderCommodityStatistics update = new ToBProcessOrderCommodityStatistics();
                    update.setOrderQuantity(newStatistics.getOrderQuantity().add(reqVo.getQuantity()));
                    update.setOrderNumber(newStatistics.getOrderNumber() + reqVo.getNumber());
                    update.setUpdateTime(new Date());
                    update.setUpdateId(-1L);
                    orderStatisticsMapper.updateByExampleSelective(update, example);
                }
            }
        }else if (reqVo.getType() == 2){
            Example example = new Example(ToBProcessOrderCommodityStatistics.class);
            example.createCriteria().andEqualTo("orderTime", reqVo.getOrderTime()).andEqualTo("commodityId", reqVo.getCommodityId());
            ToBProcessOrderCommodityStatistics oldStatistics = orderStatisticsMapper.selectOneByExample(example);
            BigDecimal oldTotalQuantity = oldStatistics.getOrderQuantity() == null ? BigDecimal.ZERO : oldStatistics.getOrderQuantity();
            int oldTotalNumber = oldStatistics.getOrderNumber() == null ? 0 : oldStatistics.getOrderNumber();
            example = new Example(ToBProcessOrderCommodityStatistics.class);
            example.createCriteria().andEqualTo("id", oldStatistics.getId());
            ToBProcessOrderCommodityStatistics update = new ToBProcessOrderCommodityStatistics();
            update.setOrderQuantity(oldTotalQuantity.subtract(reqVo.getQuantity()));
            update.setOrderNumber(oldTotalNumber - reqVo.getNumber());
            update.setUpdateTime(new Date());
            update.setUpdateId(-1L);
            orderStatisticsMapper.updateByExampleSelective(update, example);
        }
    }
}
