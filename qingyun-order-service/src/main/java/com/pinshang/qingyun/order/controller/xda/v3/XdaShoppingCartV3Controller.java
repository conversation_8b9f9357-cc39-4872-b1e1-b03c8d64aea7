package com.pinshang.qingyun.order.controller.xda.v3;

import com.pinshang.qingyun.base.configure.expand.QYAssert;
import com.pinshang.qingyun.order.dto.shopcart.v3.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;

@Deprecated
@Slf4j
@RestController
@RequestMapping("/xda/shoppingCartV3")
@Api(value = "鲜达APP购物车相关接口",tags ="XdaShoppingCartV3Controller",description ="鲜达APP购物车相关接口" )
public class XdaShoppingCartV3Controller {



    @PostMapping("/addV3")
    @ApiOperation(value = "购物车--加")
    public ShoppingCartV3ODTO addShopCart(@RequestBody ShoppingCartAddV3IDTO shoppingCartAddV3IDTO) {
        QYAssert.isFalse("请升级到最新版本");
        return null;
    }
    @PostMapping("/minusV3")
    @ApiOperation(value = "购物车--减")
    public ShoppingCartV3ODTO minusShopCart(@RequestBody ShoppingCartMinusV3IDTO shoppingCartAddV3IDTO){
        QYAssert.isFalse("请升级到最新版本");
        return null;
    }

    @PostMapping("/setNumV3")
    @ApiOperation(value = "购物车--设置数量")
    public ShoppingCartV3ODTO setNumShopCart(@RequestBody ShoppingCartSetNumV3IDTO shoppingCartSetNumV3IDTO){
        QYAssert.isFalse("请升级到最新版本");
        return null;
    }
    @GetMapping("/refreshV3")
    @ApiOperation(value = "购物车--刷新")
    public ShoppingCartV3ODTO refresh(@RequestParam(value = "orderDate",required = false) String orderDate){
        QYAssert.isFalse("请升级到最新版本");
        return null;
    }

    @PostMapping("/clearV3")
    @ApiOperation(value = "购物车--清空")
    public ShoppingCartV3ODTO clear(@RequestBody ShoppingCartClearV3IDTO shoppingCartClearV3IDTO){
        QYAssert.isFalse("请升级到最新版本");
        return null;
    }
    @GetMapping("/getCategoryV3")
    @ApiOperation(value = "购物车--品类数量")
    public Integer getCategoryNum(@RequestParam(value = "orderDate",required = false) String orderDate, @RequestParam(value = "storeId",required = false) Long storeId){
        QYAssert.isFalse("请升级到最新版本");
        return null;
    }

    @GetMapping("/getNormalGroupAmountV3")
    @ApiOperation(value = "购物车--普通商品组优惠后的价格")
    public BigDecimal getNormalGroupAmountV3(@RequestParam(value = "orderDate",required = false) String orderDate, @RequestParam(value = "storeId",required = false) Long storeId){
        QYAssert.isFalse("请升级到最新版本");
        return null;
    }

    @GetMapping("/clear/invalidV3")
    @ApiOperation(value = "购物车--清空失效商品")
    public ShoppingCartV3ODTO clearInvalidCommodity(@RequestParam(value = "orderDate",required = false) String orderDate){
        QYAssert.isFalse("请升级到最新版本");
        return null;
    }

}
