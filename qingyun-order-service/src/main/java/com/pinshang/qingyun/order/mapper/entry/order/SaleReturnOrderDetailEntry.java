package com.pinshang.qingyun.order.mapper.entry.order;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
public class SaleReturnOrderDetailEntry {

    @ApiModelProperty("客户名称")
    private String storeName;
    @ApiModelProperty("退货单号")
    private String returnOrderCode;
    @ApiModelProperty("退货单日期")
    private Date returnOrderDate;
    @ApiModelProperty("仓库id")
    private Long warehouseId;
    @ApiModelProperty("仓库")
    private String warehouseName;
    @ApiModelProperty("备注")
    private String remark;
    @ApiModelProperty("收货人Id")
    private Long updateId;
    @ApiModelProperty("收货人名称")
    private String updateName;
    @ApiModelProperty("状态")
    private Integer status;
    @ApiModelProperty("明细信息")
    private List<SaleReturnOrderItemEntry> itemList;
}
