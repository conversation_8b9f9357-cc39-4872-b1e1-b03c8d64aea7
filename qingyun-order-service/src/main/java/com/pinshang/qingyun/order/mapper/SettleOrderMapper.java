package com.pinshang.qingyun.order.mapper;

import com.pinshang.qingyun.order.mapper.entry.settlement.SettleOrderEntry;
import com.pinshang.qingyun.order.mapper.entry.settlement.SettleOrderItemEntry;
import com.pinshang.qingyun.order.vo.settle.OrderReqVo;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface SettleOrderMapper{


    List<SettleOrderEntry> findOrderByParams(OrderReqVo orderReqVo);

    List<SettleOrderEntry> findXsZsOrderByParams(OrderReqVo orderReqVo);

    List<SettleOrderItemEntry> findXsOrderItemByOrderIds(@Param("orderIds") List<Long> orderIds,@Param("logisticsModel")int logisticsModel);



    /**
     * 源订单表数据
     * @param orderReqVo
     * @return
     */
    List<SettleOrderEntry> findTOrderByParams(OrderReqVo orderReqVo);
    /**
     * 源订单明细表数据
     * @param orderIds
     * @return
     */
    List<SettleOrderItemEntry> findTOrderItemByOrderIds(@Param("orderIds") List<Long> orderIds);

}
