package com.pinshang.qingyun.order.listener;

import com.pinshang.qingyun.box.utils.SpringUtil;
import com.pinshang.qingyun.order.model.order.Order;
import com.pinshang.qingyun.order.model.order.OrderHistory;
import com.pinshang.qingyun.order.model.order.OrderList;
import com.pinshang.qingyun.order.model.order.User;
import com.pinshang.qingyun.order.vo.order.OrderHistoryAddTypeEnums;
import org.apache.commons.compress.utils.Lists;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.math.BigDecimal;
import java.util.*;


/**
 * OrderHistoryHelper.java
 *
 * <AUTHOR>
 * @date 2017年4月6日
 *
 * <AUTHOR> 优化
 * @date 2024-12-06
 */
public class OrderHistoryHelper {
	
	private static Logger logger = LoggerFactory.getLogger(OrderHistoryHelper.class);
	private static final BigDecimal DEFAULT_NUM = new BigDecimal(0);



	// copy 一期代码，这里只有变价操作，没有其他操作
	// 商品-添加：新有旧无
	// 商品-修改：新有旧有
	// 商品-删除：新无旧有
	public static List<OrderHistory> orderHistoryList(List<OrderList> oldOrderListList,
													   List<OrderList> newOrderListList,
													   OrderHistoryAddTypeEnums orderHistoryAddTypeEnum,
													   User user) {

		loggerDebug(oldOrderListList,newOrderListList,orderHistoryAddTypeEnum,user);

		List<OrderHistory> orderHistoryList = new ArrayList<OrderHistory>();

		//按照老的订单构建老订单Map映射
		Map<String, OrderList> oldOrderListMap = buildOldOrderListMap(oldOrderListList);
		//新的订单商品id集合，可能会有重复，比如限量限购的会生成两条商品记录
		List<String> newCommodityIdTypeList = new ArrayList<>();
		Map<String, OrderList> newOrderListMap = buildNewOrderListMap(newOrderListList,newCommodityIdTypeList);

		//开始遍历处理老的商品与修改后的订单中的商品对比
		orderHistoryList.addAll(processOldOrderCommodityList(oldOrderListMap,newCommodityIdTypeList,newOrderListMap,orderHistoryAddTypeEnum,user));
		//开始遍历处理老的商品与修改后的订单中的商品对比,此时newCommodityIdList中存在的一定是需要新增的商品，因为存在的商品已经在上一步被移除了
		orderHistoryList.addAll(processNewOrderCommodityList(newCommodityIdTypeList,newOrderListMap,orderHistoryAddTypeEnum,user));

		logger.debug("\n orderHistoryList=" + orderHistoryList);

		return orderHistoryList;
	}

	private static void loggerDebug(List<OrderList> oldOrderListList, List<OrderList> newOrderListList, OrderHistoryAddTypeEnums orderHistoryAddTypeEnum, User user) {
		StringBuilder stringBuilder = new StringBuilder();
		stringBuilder.append("\n oldOrderListList=")
				.append(oldOrderListList)
				.append("\n newOrderListList=")
				.append(newOrderListList)
				.append("\n addType=")
				.append(orderHistoryAddTypeEnum)
				.append("\n user=")
				.append(user);
		logger.debug(stringBuilder.toString());
	}

	private static List<OrderHistory> processNewOrderCommodityList(List<String> newCommodityIdTypeList,
																   Map<String, OrderList> newOrderListMap,
																   OrderHistoryAddTypeEnums orderHistoryAddTypeEnum,
																   User user) {
		List<OrderHistory> orderHistoryList = Lists.newArrayList();
		newCommodityIdTypeList.forEach(newCommodityIdType-> {
			 // 新增
			OrderList newOrderList = newOrderListMap.get(newCommodityIdType);
			BigDecimal newCommodityNum = null == newOrderList.getCommodityNum()? DEFAULT_NUM: newOrderList.getCommodityNum();
			OrderHistory orderHistory = OrderHistory.commodity_add(newOrderList.getOrderId(), newOrderList.getCommodityId(), newOrderList.getCommodityCode(), newOrderList.getCommodityName(), newCommodityNum.doubleValue() + "", orderHistoryAddTypeEnum.getCode(), user.getUserId(), user.getRealName(), new Date());
			orderHistoryList.add(orderHistory);
		});
		return orderHistoryList;
	}

	private static List<OrderHistory> processOldOrderCommodityList(Map<String, OrderList> oldOrderListMap,
													 				List<String> newCommodityIdTypeList,
													 				Map<String, OrderList> newOrderListMap,
																   OrderHistoryAddTypeEnums orderHistoryAddTypeEnum,
																   User user) {
		List<OrderHistory> orderHistoryList = Lists.newArrayList();
		//取出老订单id的集合，用于后续遍历
		List<String> oldCommodityIdTypeList = new ArrayList<>(oldOrderListMap.keySet());
		Integer addType = orderHistoryAddTypeEnum.getCode();
		oldCommodityIdTypeList.forEach(oldCommodityIdType->{
			if (newCommodityIdTypeList.contains(oldCommodityIdType)) { // 修改
				OrderList oldOrderList = oldOrderListMap.get(oldCommodityIdType);
				OrderList newOrderList = newOrderListMap.get(oldCommodityIdType);
				BigDecimal oldCommodityNum = null == oldOrderList.getCommodityNum()? DEFAULT_NUM: oldOrderList.getCommodityNum();
				BigDecimal newCommodityNum = null == newOrderList.getCommodityNum()? DEFAULT_NUM: newOrderList.getCommodityNum();

				if (0 != oldCommodityNum.compareTo(newCommodityNum)) { // 数量相同则不记录
					OrderHistory orderHistory = OrderHistory.commodity_modify(newOrderList.getOrderId(), newOrderList.getCommodityId(), newOrderList.getCommodityCode(), newOrderList.getCommodityName(), newCommodityNum.doubleValue() + "", oldCommodityNum.doubleValue() + "", addType, user.getUserId(), user.getRealName(), new Date());
					orderHistoryList.add(orderHistory);
				}
				BigDecimal oldCommodityPrice = null == oldOrderList.getCommodityPrice()? DEFAULT_NUM: oldOrderList.getCommodityPrice();
				BigDecimal newCommodityPrice = null == newOrderList.getCommodityPrice()? DEFAULT_NUM: newOrderList.getCommodityPrice();
				if(0 != oldCommodityPrice.compareTo(newCommodityPrice)){
					OrderHistory orderHistory = OrderHistory.commodity_modify(newOrderList.getOrderId(), newOrderList.getCommodityId(), newOrderList.getCommodityCode(), newOrderList.getCommodityName(), newCommodityPrice.doubleValue() + "", oldCommodityPrice.doubleValue() + "", addType, user.getUserId(), user.getRealName(), new Date());
					orderHistoryList.add(orderHistory);
				}

				newCommodityIdTypeList.remove(oldCommodityIdType); // 去除A
			} else { // 删除
				OrderList oldOrderList = oldOrderListMap.get(oldCommodityIdType);
				OrderHistory orderHistory = OrderHistory.commodity_delete(oldOrderList.getOrderId(), oldOrderList.getCommodityId(), oldOrderList.getCommodityCode(), oldOrderList.getCommodityName(), addType, user.getUserId(), user.getEmployeeName(), new Date());
				orderHistoryList.add(orderHistory);
			}
		});
		return orderHistoryList;
	}

	private static Map<String, OrderList> buildNewOrderListMap(List<OrderList> newOrderListList, List<String> newCommodityIdTypeList) {
		Map<String, OrderList> newOrderListMap = new HashMap<>();
		if (SpringUtil.isNotEmpty(newOrderListList)) {
			newOrderListList.forEach(newOrder ->{
				String key = generateKey(newOrder);
				newCommodityIdTypeList.add(key);
				newOrderListMap.put(key, newOrder);

			});
		}
		return newOrderListMap;
	}

	private static Map<String, OrderList> buildOldOrderListMap(List<OrderList> oldOrderListList){
		Map<String, OrderList> oldOrderListMap = new HashMap<>();
		if (SpringUtil.isNotEmpty(oldOrderListList)) {
			oldOrderListList.forEach(old->{
				String key = generateKey(old);
				oldOrderListMap.put(key, old);
			});
		}
		return oldOrderListMap;
	}
	
	private static String generateKey(OrderList orderList){
		return orderList.getCommodityId() + "-" + orderList.getType() + "-" + (orderList.getPromotionId() != null ? orderList.getPromotionId() : "");
	}
}
