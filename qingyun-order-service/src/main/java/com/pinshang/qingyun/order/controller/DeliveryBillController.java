package com.pinshang.qingyun.order.controller;

import com.github.pagehelper.PageInfo;
import com.pinshang.qingyun.order.config.QingyunProperties;
import com.pinshang.qingyun.order.mapper.entry.deliveryBill.DeliveryBillEntry;
import com.pinshang.qingyun.order.service.DeliveryBillService;
import com.pinshang.qingyun.order.vo.deliveryBill.DeliveryBillSearchVo;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * @description: 送货单
 * @author: hhf
 * @time: 2021/8/4 14:25
 */
@RestController
@RequestMapping("/deliveryBill")
@Slf4j
public class DeliveryBillController {

    @Autowired
    private DeliveryBillService deliveryBillService;

    @Autowired
    private QingyunProperties qingyunProperties;

    /**
     * 送货单列表查询
     * @param vo
     * @return
     */
    @PostMapping("/findDeliveryBillPageInfoByParams")
    public PageInfo<DeliveryBillEntry> findDeliveryBillPageInfoByParams(@RequestBody DeliveryBillSearchVo vo){
        return deliveryBillService.findDeliveryBillPageInfoByParams(vo);
    }


    @ApiOperation(value = "导出", notes = "导出")
    @RequestMapping(value = "/exportBill", method = RequestMethod.POST)
    public String exportBill(@RequestParam(value = "id",required = false) Long id){
        try {
            String s = deliveryBillService.exportData(id);
            return qingyunProperties.getPdfUrlPrefix()+s;
        }catch (Exception e){
            log.error("exportBill e",e);
        }
        return null;
    }



    @ApiOperation(value = "打印", notes = "打印")
    @RequestMapping(value = "/printerBill", method = RequestMethod.POST)
    public String printerBill(@RequestParam(value = "id",required = false) Long id,@RequestParam(value = "userId",required = false) Long userId){
        return deliveryBillService.printer(id,userId);
    }




}
