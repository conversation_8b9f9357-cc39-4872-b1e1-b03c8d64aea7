package com.pinshang.qingyun.order.service.xda.util;

import com.pinshang.qingyun.base.configure.expand.QYAssert;
import com.pinshang.qingyun.box.utils.DateUtil;
import com.pinshang.qingyun.order.dto.shopcart.v3.ShoppingCartCommodityV3ODTO;
import com.pinshang.qingyun.order.dto.shopcart.v3.ShoppingCartGroupV3ODTO;
import com.pinshang.qingyun.order.dto.shopcart.v3.ShoppingCartV3ODTO;
import com.pinshang.qingyun.order.dto.shopcart.v4.ShoppingCartCommodityV4ODTO;
import com.pinshang.qingyun.order.dto.shopcart.v4.ShoppingCartGroupV4ODTO;
import com.pinshang.qingyun.order.dto.shopcart.v4.ShoppingCartV4ODTO;
import com.pinshang.qingyun.order.mapper.OrderMapper;
import com.pinshang.qingyun.order.mapper.XdaShoppingCartMapper;
import com.pinshang.qingyun.order.mapper.entry.store.StoreDurationEntry;
import com.pinshang.qingyun.order.model.order.XdaShoppingCart;
import com.pinshang.qingyun.order.util.OrderTimeUtil;
import com.pinshang.qingyun.xda.product.dto.shoppingCart.XdaShoppingCartV3IDTO;
import com.pinshang.qingyun.xda.product.dto.shoppingCart.XdaShoppingCartV3ODTO;
import com.pinshang.qingyun.xda.product.service.XdaShoppingCartController;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RLock;
import org.redisson.api.RMap;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import tk.mybatis.mapper.entity.Example;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.Date;
import java.util.List;
import java.util.concurrent.TimeUnit;

@Component
@Slf4j
public class XdaShoppingCartToolUtil {

    @Autowired
    private XdaShoppingCartMapper xdaShoppingCartMapper;

    @Autowired
    private RedissonClient redissonClient;

    @Autowired
    private XdaShoppingCartController xdaShoppingCartController;

    @Autowired
    private OrderMapper orderMapper;
    /**
     * 获取销售箱规 加购物车的倍数
     * @param commodityId
     * @return
     */
    public BigDecimal getQuantity(Long commodityId, Long storeId){
        /*RMap<Long, BigDecimal> map = redissonClient.getMap("commodity:salesBoxCapacity");
        if(map.containsKey(commodityId)){
            return map.get(commodityId);
        }
        RLock lock = redissonClient.getLock("commoditySalesBoxCapacity"+commodityId);
        if(lock.tryLock()){
            try {
                if(map.containsKey(commodityId)){
                    return map.get(commodityId);
                }
                BigDecimal salesBoxCapacity = getSalesBoxCapacity(commodityId, storeId);
                map.put(commodityId,salesBoxCapacity);
                map.expire(5L, TimeUnit.MINUTES);
                return salesBoxCapacity;
            }finally {
                lock.unlock();
            }
        }*/
        return getSalesBoxCapacity(commodityId, storeId);
    }
    public BigDecimal getSalesBoxCapacity(Long commodityId, Long storeId){
        XdaShoppingCartV3IDTO xdaShoppingCartV3IDTO =XdaShoppingCartV3IDTO.builder().commodityId(commodityId).build();
        xdaShoppingCartV3IDTO.setStoreId(storeId);
        XdaShoppingCartV3ODTO xdaCommoditySalesBoxCapacity = xdaShoppingCartController.getXdaCommoditySalesBoxCapacity(xdaShoppingCartV3IDTO);
        return xdaCommoditySalesBoxCapacity.getSalesBoxCapacity();
    }
    /**
     * 根据条件查询购物车
     * @param storeId
     * @param commodityId
     * @return
     */
    public List<XdaShoppingCart> queryShoppingCart(Long storeId, Long commodityId, Integer commodityType){
        Example ex = new Example(XdaShoppingCart.class);
        Example.Criteria criteria = ex.createCriteria();
        criteria.andEqualTo("commodityType",commodityType);
        criteria.andEqualTo("storeId", storeId);
        if(null != commodityId){
            criteria.andEqualTo("commodityId", commodityId);
        }
        return xdaShoppingCartMapper.selectByExample(ex);
    }

    /**
     * 设置错误提示
     * @param commodityType
     * @param commodityId
     * @param stockWarningTips
     * @param shoppingCartV3ODTO
     */
    public void setStockWarningTips(Integer commodityType, Long commodityId, String stockWarningTips, ShoppingCartV3ODTO shoppingCartV3ODTO){
        if(commodityType.equals(1)){
            List<ShoppingCartCommodityV3ODTO> commodities = shoppingCartV3ODTO.getNormalGroup().getCommodities();
            commodities.forEach(item->{
                if(item.getCommodityId().equals(commodityId) ){
                    item.setStockWarningTips(stockWarningTips);
                }
            });
            List<ShoppingCartGroupV3ODTO> promotionGroup = shoppingCartV3ODTO.getPromotionGroup();
            promotionGroup.forEach(item->{
                item.getCommodities().forEach(commodityItem->{
                    if(commodityItem.getCommodityId().equals(commodityId) ){
                        commodityItem.setStockWarningTips(stockWarningTips);
                    }
                });
            });
        }else if(commodityType.equals(2)){
            List<ShoppingCartCommodityV3ODTO> commodities = shoppingCartV3ODTO.getThGroups().getCommodities();
            commodities.forEach(item->{
                if(item.getCommodityId().equals(commodityId) ){
                    item.setStockWarningTips(stockWarningTips);
                }
            });
        }
    }
    public void setStockWarningTipsV4(Integer commodityType, Long commodityId, String stockWarningTips, ShoppingCartV4ODTO shoppingCartV4ODTO){
        if(commodityType.equals(1)){
            List<ShoppingCartCommodityV4ODTO> commodities = shoppingCartV4ODTO.getNormalGroup().getCommodities();
            commodities.forEach(item->{
                if(item.getCommodityId().equals(commodityId) ){
                    //整车有提示，优先取整车的，没有才设置单个加车的提示
                    if (StringUtils.isBlank(item.getStockWarningTips())) {
                        item.setStockWarningTips(stockWarningTips);
                    }else {
                        //整车有提示
                        shoppingCartV4ODTO.setStockWarningTips(item.getStockWarningTips());
                    }
                }
            });
            List<ShoppingCartGroupV4ODTO> promotionGroup = shoppingCartV4ODTO.getPromotionGroup();
            promotionGroup.forEach(item->{
                item.getCommodities().forEach(commodityItem->{
                    if(commodityItem.getCommodityId().equals(commodityId) ){
                        if (StringUtils.isBlank(commodityItem.getStockWarningTips())) {
                            commodityItem.setStockWarningTips(stockWarningTips);
                        }else {
                            //整车有提示
                            shoppingCartV4ODTO.setStockWarningTips(commodityItem.getStockWarningTips());
                        }
                    }
                });
            });
        }else if(commodityType.equals(2)){
            List<ShoppingCartCommodityV4ODTO> commodities = shoppingCartV4ODTO.getThGroups().getCommodities();
            commodities.forEach(item->{
                if(item.getCommodityId().equals(commodityId) ){
                    if (StringUtils.isBlank(item.getStockWarningTips())) {
                        item.setStockWarningTips(stockWarningTips);
                    }else {
                        //整车有提示
                        shoppingCartV4ODTO.setStockWarningTips(item.getStockWarningTips());
                    }
                }
            });
        }
    }



    /**
     * 获取客户下单截止时间
     * @param storeId
     * @return
     */
    public String getStoreDurationEndTime(Long storeId){
        StoreDurationEntry sd = orderMapper.findStoreDurationByStoreId(storeId.toString());
        if(null != sd){
            if (StringUtils.isNotBlank(sd.getBeginTime()) && StringUtils.isNotBlank(sd.getEndTime())){
                return sd.getEndTime();
            }
        }
        return null;
    }
    /**
     * 检查客户是否在订货时间内
     * 订单下单截止时间和订单截止时间(表里现存字段)进行判断，
     * 下单时间必须小于等于订单截止时间
     * @param orderDurationTime
     * @return
     */
//    public String checkStoreDuration(Date orderDurationTime){
//        StoreDurationEntry sd = orderMapper.findStoreDurationByStoreId(storeId.toString());
//        if(null != sd){
//            int difDay = DateUtil.getDayDif(orderTime, DateUtil.getNowDate());
//            if (StringUtils.isNotBlank(sd.getBeginTime()) && StringUtils.isNotBlank(sd.getEndTime())){
//                if(difDay >= 1){
//                    QYAssert.isTrue(DateTimeUtil.compareNewDate(sd.getBeginTime(), "23:59"), "超出订货时间,无法操作!");
//                }else {
//                    QYAssert.isTrue(DateTimeUtil.compareNewDate(sd.getBeginTime(), sd.getEndTime()), "超出订货时间,无法操作!");
//                }
//                return sd.getEndTime();
//            }
//        }
//        return null;
//    }
    public Boolean checkStoreDuration(Date orderDurationTime) {
        Date now = new Date();
        int val = orderDurationTime.compareTo(now);
        if(val > 0)
            return Boolean.TRUE;
        else if(val < 0)
            return Boolean.FALSE;
        else
            return Boolean.TRUE;
    }

    /**
     * 计算订单最晚可取消时间
     * 当前日期 + 订货日期与商品最大最早起送日期之间的天数 + 客户截单时间
     * 订单截止时间逻辑修改
     * http://192.168.0.213/zentao/story-view-8197.html
     * T + n - T+(n+4)
     * 送货时间 - n = 订单截止时间
     * @param orderTime
     * @param maxBeginDeliveryTime
     * @param storeEndTime
     * @return
     */
    public Date calculateOrderDeadline(Date orderTime, Date maxBeginDeliveryTime, String storeEndTime){
        Date orderDurationTime = null;

        if (OrderTimeUtil.isTheSameDay(orderTime, new Date())) {
            // 是当天单，保存到订单中的截单时间重新赋值为当前时间
            orderDurationTime = DateUtil.parseDate(DateUtil.get4yMdHms(new Date()), "yyyy-MM-dd HH:mm");
        }else {
            // 计算T+n
            int days = DateUtil.getDayDif(maxBeginDeliveryTime,Date.from(LocalDate.now().atStartOfDay(ZoneId.systemDefault()).toInstant()) );
            QYAssert.isTrue(days >= 0, "商品最晚订货时间发生改变，请刷新购物车确认");
            //计算订单截止时间(送货日期 - n)
            Date durationTime = DateUtil.addDay(orderTime, 0 - (days == 0 ? 1 : days));
            String time = DateUtil.getDateFormate(durationTime, "yyyy-MM-dd") + " " + storeEndTime;
            orderDurationTime =  DateUtil.parseDate(time, "yyyy-MM-dd HH:mm");

            //经产品确认客户订货时间段和订单截止时间判断,判断是否超时
            QYAssert.isTrue(checkStoreDuration(orderDurationTime), "超出订货时间,无法操作!");

        }
        return orderDurationTime;
    }

}
