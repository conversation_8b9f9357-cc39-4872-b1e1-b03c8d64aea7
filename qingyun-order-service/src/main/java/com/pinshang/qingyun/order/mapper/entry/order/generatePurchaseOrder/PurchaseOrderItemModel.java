//package com.pinshang.qingyun.order.mapper.entry.order.generatePurchaseOrder;
//
//import lombok.Data;
//
//import javax.persistence.Entity;
//import javax.persistence.Id;
//import javax.persistence.Table;
//import java.math.BigDecimal;
//
//@Data
//@Table(name = "t_purchase_order_item")
//@Entity
//public class PurchaseOrderItemModel {
//    /** 主键id */
//    @Id
//    private Long id;
//
//    /** 采购订单id */
//    private Long purchaseOrderId;
//
//    /** 商品id */
//    private String commodityId;
//
//
//    /** 采购单价 */
//    private BigDecimal price;
//
//    /** 采购数量 */
//    private BigDecimal quantity;
//
//
//    private BigDecimal receivedQuantity;
//
//    private String categoryId;
//
//}