package com.pinshang.qingyun.order.mapper;

import java.math.BigDecimal;
import java.util.List;

import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import com.pinshang.qingyun.base.mybatis.MyMapper;
import com.pinshang.qingyun.order.model.order.ProductPriceModelList;
import org.springframework.web.bind.annotation.RequestParam;

@Repository
public interface ProductPriceModelListMapper extends MyMapper<ProductPriceModelList>{
	BigDecimal getModelPrice(@Param("storeId")Long storeId,@Param("commodityId")Long commodityId);

    List<ProductPriceModelList> findCommodityPriceByStoreIdAndCommodityId(@Param("storeId") Long storeId, @Param("commodityIdList") List<Long> commodityIdList);
}
