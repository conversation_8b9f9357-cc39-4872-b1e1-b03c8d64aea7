package com.pinshang.qingyun.order.controller;

import com.alibaba.excel.EasyExcel;
import com.github.pagehelper.PageInfo;
import com.pinshang.qingyun.base.api.TokenInfo;
import com.pinshang.qingyun.base.configure.expand.QYAssert;
import com.pinshang.qingyun.base.enums.order.OrderTypeEnum;
import com.pinshang.qingyun.base.service.DdTokenShopIdService;
import com.pinshang.qingyun.base.util.ExcelUtil;
import com.pinshang.qingyun.base.util.FastThreadLocalUtil;
import com.pinshang.qingyun.box.utils.DateUtil;
import com.pinshang.qingyun.box.utils.SpringUtil;
import com.pinshang.qingyun.infrastructure.exportfile.cache.starter.FileCacheQuery;
import com.pinshang.qingyun.order.dto.SettlementDetailsReportODTO;
import com.pinshang.qingyun.order.mapper.entry.shop.OrderTypeEntry;
import com.pinshang.qingyun.order.mapper.entry.shop.SettlementDetailsReportEntry;
import com.pinshang.qingyun.order.service.SettlementDetailsReportService;
import com.pinshang.qingyun.order.vo.shop.SettlementDetailsReportVo;
import com.pinshang.qingyun.renderer.annotation.MethodRender;
import com.pinshang.qingyun.renderer.service.IRenderService;
import com.pinshang.qingyun.smm.dto.user.SelectUserShopIdListIDTO;
import com.pinshang.qingyun.smm.service.SMMUserClient;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.math.BigDecimal;
import java.net.URLDecoder;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * Created by hhf on 2019/1/16.
 * 结算明细报表
 */
@RestController
@RequestMapping("/settlementDetailsReport")
public class SettlementDetailsReportController {
    @Autowired
    private SettlementDetailsReportService settlementDetailsReport;

    @Autowired
    private SMMUserClient smmUserClient;

    @Autowired
    private DdTokenShopIdService ddTokenShopIdService;

    @Autowired
    private IRenderService renderService;

    /**
     * 结算明细报表
     * @param vo
     * @return
     */
    @MethodRender
    @Deprecated // 已弃用 迁移至 qingyun-order-manage-service
    @PostMapping("/settlementDetailsReport")
    public PageInfo<SettlementDetailsReportEntry> settlementDetailsReport(@RequestBody SettlementDetailsReportVo vo){
        setShopIdList(vo);
        //校验档口权限
        ddTokenShopIdService.processReadDdTokenShopId(vo.getShopId(), vo.getStallId());
        return settlementDetailsReport.settlementDetailsReport(vo);
    }

    /**
     * 查询结算总金额
     * @param vo
     * @return
     */
    @PostMapping("/findTotalSettlePrice")
    @Deprecated // 已弃用 迁移至 qingyun-order-manage-service
    public BigDecimal findTotalSettlePrice(@RequestBody SettlementDetailsReportVo vo){
        setShopIdList(vo);
        return settlementDetailsReport.findTotalSettlePrice(vo);
    }

    /**
     * 查询符合条件的客户类型
     * @return
     */
    @GetMapping("/getStoreTypeIdList")
    public List<String> getStoreTypeIdList(){
        return settlementDetailsReport.getStoreTypeIdList();
    }

    /**
     * 查询所有的订单来源+ 订单来源为空
     * @return
     */
    @ApiOperation(value = "查询所有的订单来源+ 订单来源为空", notes = "查询所有的订单来源+ 订单来源为空", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @GetMapping("/getAllOrderType")
    public List<OrderTypeEntry> getAllOrderType(){
        EnumSet<OrderTypeEnum> orderTypes = OrderTypeEnum.allList();
        if(SpringUtil.isNotEmpty(orderTypes)){
            List<OrderTypeEntry> list = new ArrayList<>();
            list.add(0,new OrderTypeEntry(0, "空"));
            for (OrderTypeEnum orderType : orderTypes) {
                if(!orderType.equals(OrderTypeEnum.MINI_GROUPON_ORDER) ){
                    OrderTypeEntry orderTypeEntry = new OrderTypeEntry(orderType.getCode(), orderType.getDesc());
                    list.add(orderTypeEntry);
                }
            }
            return list;
        }
        return Collections.EMPTY_LIST;
    }


    /**
     * 结算明细报表-导出
     * dto
     * @return
     */
    @FileCacheQuery(bizCode = "SETTLEMENT_DETAIL_REPORT")
    @Deprecated // 已弃用 迁移至 qingyun-order-manage-service
    @ApiOperation(value = "结算明细报表-导出", notes = "结算明细报表-导出")
    @RequestMapping(value = "/exportInfo/exportSettlementDetailReport", method = RequestMethod.GET)
    public void exportSettlementDetailReport(SettlementDetailsReportVo vo, HttpServletResponse httpServletResponse) throws IOException {
        //解析 商品编码
        if(StringUtils.isNotEmpty(vo.getCommodityCode())){
            try {
                String commodityCode = vo.getCommodityCode();
                vo.setCommodityCode(URLDecoder.decode(commodityCode, "UTF-8"));
            } catch (UnsupportedEncodingException e) {
                e.printStackTrace();
            }
        }
        setShopIdList(vo);
        vo.setPageNo(1);
        vo.setPageSize(Integer.MAX_VALUE);
        PageInfo<SettlementDetailsReportEntry> pageInfo = settlementDetailsReport.settlementDetailsReport(vo);

        List<SettlementDetailsReportEntry> list = pageInfo.getList();
        renderService.render(list,"/settlementDetailsReport/exportInfo/exportSettlementDetailReport");

        if(SpringUtil.isNotEmpty(list)){
            QYAssert.isTrue(list.size()<65001,"限制:最多只能导出65000条数据");
        }
        List<SettlementDetailsReportODTO> odtoList;
        if(SpringUtil.isNotEmpty(list)) {
            //遍历数据
            SettlementDetailsReportODTO odto;
            odtoList = new ArrayList<>(list.size());
            //查询总和
            BigDecimal totalSettlePrice = settlementDetailsReport.findTotalSettlePrice(vo);
            odto = new SettlementDetailsReportODTO();
            odto.setCommoditySpec("合计");
            if (null != totalSettlePrice) {
                odto.setSettlePrice(totalSettlePrice);
            } else {
                odto.setSettlePrice(BigDecimal.ZERO);
            }
            for (SettlementDetailsReportEntry item : list) {
                odto = new SettlementDetailsReportODTO();
                odto.setShopName(item.getShopName());
                odto.setOrderTime(item.getOrderTime());
                odto.setSettleTime(item.getSettleTime());
                odto.setOrderTimeStr(DateUtil.get4yMd(item.getOrderTime()));
                odto.setSettleTimeStr(DateUtil.get4yMd(item.getSettleTime()));
                odto.setOrderTypeName(item.getOrderTypeName());
                odto.setSubOrderCode(item.getSubOrderCode());
                odto.setPreOrderCode(item.getPreOrderCode());
                odto.setCategoryName(item.getCategoryName());
                odto.setBarCode(item.getBarCode());
                odto.setCommodityCode(item.getCommodityCode());
                odto.setCommodityName(item.getCommodityName());
                odto.setCommoditySpec(item.getCommoditySpec());
                odto.setCommodityUnitName(item.getCommodityUnitName());
                odto.setPrice(item.getPrice());
                odto.setQuantity(item.getQuantity() + "");
                odto.setRealDeliveryQuantity(item.getRealDeliveryQuantity() + "" );
                odto.setSettlePrice(item.getSettlePrice());
                odto.setStallName(item.getStallName());
                odtoList.add(odto);
            }
        }else{
            odtoList = new ArrayList<>();
        }
        // 文件名
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmss");
        String fileName = "结算明细表" + "_" + sdf.format(new Date());
        try {
            com.pinshang.qingyun.base.util.ExcelUtil.setFileNameAndHead(httpServletResponse, fileName);
            EasyExcel.write(httpServletResponse.getOutputStream(), SettlementDetailsReportODTO.class).autoCloseStream(Boolean.FALSE).sheet("结算明细表")
                    .doWrite(odtoList);

        }catch (Exception e){
            ExcelUtil.setExceptionResponse( httpServletResponse );
        }
    }

    private void setShopIdList(SettlementDetailsReportVo vo) {
        TokenInfo tokenInfo = FastThreadLocalUtil.getQY();
        List<Long> shopIdList = smmUserClient.selectUserShopIdList(SelectUserShopIdListIDTO.firstCacheThenDb(tokenInfo.getUserId()));
        vo.setShopIdList(shopIdList);
    }

}
