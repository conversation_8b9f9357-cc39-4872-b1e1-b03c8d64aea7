package com.pinshang.qingyun.order.service;

import com.pinshang.qingyun.base.api.TokenInfo;
import com.pinshang.qingyun.base.configure.expand.QYAssert;
import com.pinshang.qingyun.base.enums.YesOrNoEnums;
import com.pinshang.qingyun.base.service.DdTokenShopIdService;
import com.pinshang.qingyun.base.util.FastThreadLocalUtil;
import com.pinshang.qingyun.order.mapper.ShopMapper;
import com.pinshang.qingyun.order.mapper.entry.stall.StallEntry;
import com.pinshang.qingyun.order.model.commodity.Commodity;
import com.pinshang.qingyun.order.model.shop.Shop;
import com.pinshang.qingyun.order.util.StallUtils;
import com.pinshang.qingyun.shop.admin.dto.ConsignmentSupplierInfoIDTO;
import com.pinshang.qingyun.shop.admin.dto.ConsignmentSupplierInfoODTO;
import com.pinshang.qingyun.shop.admin.service.ConsignmentSupplierClient;
import com.pinshang.qingyun.shop.dto.bigShop.StallCommodityODTO;
import com.pinshang.qingyun.shop.dto.bigShop.StallCommodityQueryIDTO;
import com.pinshang.qingyun.shop.dto.bigShop.StallODTO;
import com.pinshang.qingyun.shop.dto.shopCommodity.ShopCommodityInfoODTO;
import com.pinshang.qingyun.shop.service.bigShop.StallClient;
import com.pinshang.qingyun.smm.dto.userstall.SelectUserStallIdListIDTO;
import com.pinshang.qingyun.smm.service.UserStallClient;
import com.pinshang.qingyun.xd.wms.dto.ShopCommodityStockDTO;
import com.pinshang.qingyun.xd.wms.dto.StockQueryIDTO;
import com.pinshang.qingyun.xd.wms.dto.bigShop.GoodsAllocationODTO;
import com.pinshang.qingyun.xd.wms.service.XdStockClient;
import com.pinshang.qingyun.xd.wms.service.bigShop.GoodsAllocationClient;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.redisson.api.RBucket;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @Author: sk
 * @Date: 2024/6/4
 */
@Slf4j
@Service
public class ConsignmentSupplierService {

    @Autowired
    private ConsignmentSupplierClient consignmentSupplierClient;
    @Autowired
    private StallClient stallClient;
    @Autowired
    private UserStallClient userStallClient;
    @Autowired
    private DdTokenShopIdService ddTokenShopIdService;
    @Autowired
    private GoodsAllocationClient goodsAllocationClient;
    @Autowired
    private ShopMapper shopMapper;
    @Autowired
    private CommodityService commodityService;
    @Autowired
    private XdStockClient xdStockClient;
    @Autowired
    private ShopService shopService;

    private  static final String SHOP_REDIS_KEY = "shop:redis:";

    /**
     * 查询代销供应商信息(香烟)
     * @param storeIdList
     * @param commodityIdList
     * @return
     */
    public List<ConsignmentSupplierInfoODTO> queryConsignmentSupplierList(List<Long> storeIdList, List<Long> commodityIdList) {
        ConsignmentSupplierInfoIDTO consignmentSupplierIDTO = new ConsignmentSupplierInfoIDTO();
        consignmentSupplierIDTO.setStoreIdList(storeIdList);
        consignmentSupplierIDTO.setCommodityIdList(commodityIdList);
        consignmentSupplierIDTO.setStatus(YesOrNoEnums.YES.getCode());
        return consignmentSupplierClient.batchQueryByStoreIds(consignmentSupplierIDTO);
    }

    /**
     * 根据档口ids获取档口名称 map
     * @param stallIdList
     * @return
     */
    public Map<Long, String> queryStallMapByIds(List<Long> stallIdList) {
        if(CollectionUtils.isEmpty(stallIdList)){
            return new HashMap<>();
        }
        Map<Long, String> stallMap = new HashMap<>(stallIdList.size());
        List<StallODTO> stallODTOList = stallClient.queryStallByIds(stallIdList);
        if(CollectionUtils.isNotEmpty(stallODTOList)){
            stallODTOList.forEach(stallODTO -> stallMap.put(stallODTO.getId(), stallODTO.getStallName()));
        }
        return stallMap;
    }

    /**
     * 根据档口ids获取档口 map
     * @param stallIdList
     * @return
     */
    public Map<Long, StallODTO> queryStallWholeMapByIds(List<Long> stallIdList) {
        if(CollectionUtils.isEmpty(stallIdList)){
            return new HashMap<>();
        }
        Map<Long, StallODTO> stallMap = new HashMap<>(stallIdList.size());
        List<StallODTO> stallODTOList = stallClient.queryStallByIds(stallIdList);
        if(CollectionUtils.isNotEmpty(stallODTOList)){
            stallMap = stallODTOList.stream().collect(Collectors.toMap(StallODTO::getId, Function.identity()));
        }
        return stallMap;
    }

    /**
     * 根据档口编码获取档口 map
     * @param stallCodeList
     * @return
     */
    public Map<String, StallODTO> queryStallMapByCodes(List<String> stallCodeList) {
        if(CollectionUtils.isEmpty(stallCodeList)){
            return new HashMap<>();
        }
        Map<String, StallODTO> stallMap = new HashMap<>(stallCodeList.size());
        List<StallODTO> stallODTOList = stallClient.queryStallByStallCodes(stallCodeList);
        if(CollectionUtils.isNotEmpty(stallODTOList)){
            stallODTOList.forEach(stallODTO -> stallMap.put(stallODTO.getStallCode(), stallODTO));
        }
        return stallMap;
    }

    /**
     * 批量获取档口商品（用于大店代理导入）
     * @param idto
     * @return
     */
    public Map<String, String> queryStallCommodityMap(StallCommodityQueryIDTO idto) {
        Map<String, String> stallCommodityMap = new HashMap<>(idto.getCommodityCodeList().size());
        List<StallCommodityODTO> stallCommodityList = stallClient.queryStallCommodityList(idto);
        if(CollectionUtils.isNotEmpty(stallCommodityList)){
            stallCommodityList.forEach(stallCommodity->{
                String key  = stallCommodity.getShopId() + "" + stallCommodity.getStallCode() + "" + stallCommodity.getCommodityCode();
                stallCommodityMap.put(key, key);
            });
        }
        return stallCommodityMap;
    }

    /**
     * 根据shopId、stallId、commodityIdList获取档口商品信息(成本价、上下架、是否可售)
     * @return
     */
    public List<StallCommodityODTO> queryStallCommodityInfo(Long shopId, Long stallId, List<Long> commodityIdList){
        QYAssert.notNull(shopId, "shopId不能为空");
        QYAssert.notNull(stallId, "stallId不能为空");

        StallCommodityQueryIDTO idto = new StallCommodityQueryIDTO();
        idto.setShopId(shopId);
        idto.setStallId(stallId);
        idto.setCommodityIdList(commodityIdList);
        return stallClient.queryStallCommodityInfo(idto);
    }

    /**
     * 根据货位ids获取货位 map
     * @param goodsAllocationIdList
     * @return
     */
    public Map<Long, GoodsAllocationODTO> queryGoodsAllocationMapByIds(List<Long> goodsAllocationIdList) {
        if(CollectionUtils.isEmpty(goodsAllocationIdList)){
            return new HashMap<>();
        }
        Map<Long, GoodsAllocationODTO> goodsAllocationMap = new HashMap<>(goodsAllocationIdList.size());
        List<GoodsAllocationODTO> stallODTOList = goodsAllocationClient.queryGoodsAllocationByIdList(goodsAllocationIdList);
        if(CollectionUtils.isNotEmpty(stallODTOList)){
            goodsAllocationMap = stallODTOList.stream().collect(Collectors.toMap(GoodsAllocationODTO::getId, Function.identity()));
        }
        return goodsAllocationMap;
    }

    /**
     * 根据档口id获取其下的商品信息
     * @return
     */
    public List<Long> getCommodityIdListByStallIds(Long shopId, Long stallId){
        return stallClient.queryCommodityIdsByShopAndStall(shopId, stallId);
    }


    /**
     * 根据shopId获取用户权限下面的档口list
     * @return
     */
    public List<Long> selectUserStallIdList(Long shopId){
        SelectUserStallIdListIDTO idto = SelectUserStallIdListIDTO.init(FastThreadLocalUtil.getQY().getUserId(), shopId);
        return userStallClient.selectUserStallIdList(idto);
    }


    /**
     * 统一判断档口
     * @param t
     * @param <T>
     */
    public <T extends StallEntry> void checkStallId(T t) {
        TokenInfo ti = FastThreadLocalUtil.getQY();
        if(ti != null){
            // pda登录进来managementMode 是空的，需要重新查询
            Integer managementMode = ti.getManagementMode();
            if(managementMode == null && ti.getShopId() != null) {
                Shop shop = shopService.getShopFromCacheThenDb(ti.getStoreId());
                managementMode = shop.getManagementMode();
            }

            if(StallUtils.isStallSubcontractor(managementMode)){
                QYAssert.isTrue(t.getStallId() != null, "请选择档口");

                // 判断档口不能为空、设置档口、校验权限
                ddTokenShopIdService.processDdTokenShopId(ti.getShopId(), t.getStallId());
            }
        }
    }

    /**
     * 校验商品是否档口商品
     * @param shopId
     * @param stallId
     * @param commodityIdList
     */
    public void checkStallCommodity(Long shopId, Long stallId, List<Long> commodityIdList){
        TokenInfo token = FastThreadLocalUtil.getQY();
        Shop shop = shopService.getShopFromCacheThenDb(token.getStoreId());

        Boolean isBigShop = StallUtils.isStallSubcontractor(shop.getManagementMode());
        if(isBigShop) {
            List<Long> stallCommodityIdList = getCommodityIdListByStallIds(shopId, stallId);
            QYAssert.isTrue(CollectionUtils.isNotEmpty(stallCommodityIdList), "该档口没有商品");

            Map<Long, Long> stallCommodityIdMap = stallCommodityIdList.stream().collect(Collectors.toMap(Function.identity(), Function.identity()));

            Map<Long, Commodity> commMap = commodityService.findCommodityInfoByIdMap(commodityIdList);
            for(Long commodityId : commodityIdList) {
                if(!stallCommodityIdMap.containsKey(commodityId)) {
                    Commodity commodity = commMap.get(commodityId);
                    QYAssert.isFalse(commodity.getCommodityName() + " 非档口商品");
                }
            }
        }
    }

    /**
     * 批量查询大店商品库存
     * 购物车、快速订货列表。存在多个档口
     * key: stallId + commodityId
     */
    public Map<String, ShopCommodityInfoODTO> queryBigShopCommodityStock(Long shopId, List<Long> stallIdList, List<Long> commodityIdList){
        Set<Long> stallIdSet = new HashSet<>(stallIdList);
        Map<String, ShopCommodityInfoODTO> bigShopStockMap = new HashMap<>(commodityIdList.size());

        // 查询大店库存
        StockQueryIDTO queryDTO = new StockQueryIDTO();
        queryDTO.setWarehouseId(shopId);
        queryDTO.setStallIdList(new ArrayList<>(stallIdSet));
        queryDTO.setCommodityList(commodityIdList);
        List<ShopCommodityStockDTO> stockList = xdStockClient.queryBigShopCommodityStock(queryDTO);
        if(!org.springframework.util.CollectionUtils.isEmpty(stockList)) {
            stockList.forEach(item -> {
                ShopCommodityInfoODTO itemDTO = new ShopCommodityInfoODTO();
                itemDTO.setStockQuantity(item.getStockQuantity().add(item.getPickingAreaStock()).add(item.getWarehouseAreaStock()));
                itemDTO.setStockNumber(itemDTO.getStockQuantity().divide(item.getCommodityPackageSpec(), 0, BigDecimal.ROUND_DOWN).intValue());
                String key = item.getStallId() + "_" + item.getCommodityId();
                bigShopStockMap.put(key, itemDTO);
            });
        }
        return bigShopStockMap;
    }
}
