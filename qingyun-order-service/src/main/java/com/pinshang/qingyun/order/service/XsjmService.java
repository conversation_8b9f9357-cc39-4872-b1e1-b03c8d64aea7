package com.pinshang.qingyun.order.service;

import com.alibaba.fastjson.JSON;
import com.pinshang.qingyun.base.api.TokenInfo;
import com.pinshang.qingyun.base.configure.expand.QYAssert;
import com.pinshang.qingyun.base.enums.OrderCodeEnums;
import com.pinshang.qingyun.base.enums.StoreBillTypeEnums;
import com.pinshang.qingyun.base.enums.pay.AppCodeEnum;
import com.pinshang.qingyun.base.enums.pay.PayTypeIntEnum;
import com.pinshang.qingyun.base.enums.shop.ShopTypeEnums;
import com.pinshang.qingyun.base.enums.xd.StockInOutTypeEnums;
import com.pinshang.qingyun.base.util.OrderCodeUtil;
import com.pinshang.qingyun.box.utils.DateUtil;
import com.pinshang.qingyun.box.utils.IDGenerator;
import com.pinshang.qingyun.box.utils.SpringUtil;
import com.pinshang.qingyun.common.dto.DictionaryODTO;
import com.pinshang.qingyun.common.service.DictionaryClient;
import com.pinshang.qingyun.order.bo.StoreRechargeBO;
import com.pinshang.qingyun.order.dto.AnalysisBySynthesisListIDTO;
import com.pinshang.qingyun.order.dto.CompensateSettleDailyIDTO;
import com.pinshang.qingyun.order.dto.SettleDailyODTO;
import com.pinshang.qingyun.order.mapper.FranchiseSalesAnalysisMapper;
import com.pinshang.qingyun.order.mapper.ShopMapper;
import com.pinshang.qingyun.order.mapper.StoreSettlementMapper;
import com.pinshang.qingyun.order.model.order.StoreSettlement;
import com.pinshang.qingyun.order.model.shop.Shop;
import com.pinshang.qingyun.order.model.xsjm.FranchiseSalesAnalysis;
import com.pinshang.qingyun.order.service.xsjm.KafkaMessageService;
import com.pinshang.qingyun.order.vo.kafka.XjShopCommodityStockKafkaVo;
import com.pinshang.qingyun.pay.dto.cscanb.CToBGetQRCodeReq;
import com.pinshang.qingyun.pay.dto.cscanb.UnionCToBGetQRCodeResp;
import com.pinshang.qingyun.pay.service.ScanPayClient;
import com.pinshang.qingyun.report.dto.pos.AnalysisBySynthesisDTO;
import com.pinshang.qingyun.report.dto.pos.AnalysisBySynthesisIDTO;
import com.pinshang.qingyun.report.dto.pos.JoinShopSummaryAmountDTO;
import com.pinshang.qingyun.report.dto.pos.JoinShopSummaryAmountIDTO;
import com.pinshang.qingyun.report.service.pos.JoinShopClient;
import com.pinshang.qingyun.shop.dto.SelectShopDropdownInfoListIDTO;
import com.pinshang.qingyun.shop.dto.ShopDropdownInfoODTO;
import com.pinshang.qingyun.shop.dto.ShopDto;
import com.pinshang.qingyun.shop.dto.ShopODTO;
import com.pinshang.qingyun.shop.service.ShopClient;
import com.pinshang.qingyun.store.dto.storeSettlement.StoreSettlementCollectODTO;
import com.pinshang.qingyun.store.service.StoreSettlementNewClient;
import com.pinshang.qingyun.xd.wms.dto.ShopStockDTO;
import com.pinshang.qingyun.xd.wms.dto.StockQueryIDTO;
import com.pinshang.qingyun.xd.wms.service.XdStockClient;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.entity.Example;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;


/**
 * 鲜食加盟 Service
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class XsjmService {

    @Autowired
    private XdStockClient xdStockClient;

    @Autowired
    private JoinShopClient joinShopClient;

    @Autowired
    private KafkaMessageService kafkaMessageService;

    @Autowired
    private StoreSettlementNewClient storeSettlementNewClient;

    @Autowired
    private ScanPayClient scanPayClient;

    @Autowired
    private DictionaryClient dictionaryClient;

    @Autowired
    private FranchiseSalesAnalysisMapper franchiseSalesAnalysisMapper;

    @Autowired
    private ShopClient shopClient;

    @Autowired
    private StoreRechargeService storeRechargeService;

    @Autowired
    private StoreSettlementMapper storeSettlementMapper;

    @Autowired
    private ShopMapper shopMapper;

    /**
     * 预日结
     *
     * @param tokenInfo 门店信息
     */
    public SettleDailyODTO preSettleDaily(TokenInfo tokenInfo) {
        //查询客户余额
        Long storeId = tokenInfo.getStoreId();
        List<StoreSettlementCollectODTO> odtos = storeSettlementNewClient.listStoreSettlementByStoreIds(Collections.singletonList(storeId));
        QYAssert.isTrue(CollectionUtils.isNotEmpty(odtos) && Objects.equals(odtos.get(0).getCollectStatus(), Boolean.TRUE), "对应客户类型必须是预付客户!");
        StoreSettlementCollectODTO storeSettlement = odtos.get(0);
        Double collectPrice = storeSettlement.getCollectPrice();
        // 查询门店的可提现余额
        List<JoinShopSummaryAmountDTO> dtoList = this.getWithdrawableBalance(Collections.singletonList(tokenInfo.getShopId()), DateUtil.getDateFormate(new Date(), "yyyy-MM-dd"));
        JoinShopSummaryAmountDTO summaryAmount = dtoList.stream().findFirst().orElse(null);
        BigDecimal amount = Objects.nonNull(summaryAmount) ? summaryAmount.getAmount() : BigDecimal.ZERO;
        BigDecimal balance = BigDecimal.valueOf(collectPrice).add(amount).setScale(2, RoundingMode.HALF_UP);
        SettleDailyODTO odto = new SettleDailyODTO();
        if (balance.compareTo(BigDecimal.ZERO) >= 0) {
            odto.setFlag(0);
        } else {
            odto.setFlag(1);
            odto.setTips("应付货款" + balance.abs().stripTrailingZeros() + "元，请充值。否则会影响明天订货！");
        }
        return odto;
    }

    /**
     * 手动日结
     *
     * @param shopId 门店id
     * @param userId 用户id
     */
    @Transactional(rollbackFor = Exception.class)
    public void confirmSettleDaily(Long shopId, Long userId) {
        // 查询客户id
        ShopDto shop = shopClient.findShopById(shopId);
        String dailyDate = DateUtil.getDateFormate(new Date(), "yyyy-MM-dd");
        // 查询门店的可提现余额
        List<JoinShopSummaryAmountDTO> dtoList = this.getWithdrawableBalance(Collections.singletonList(shopId), dailyDate);
        JoinShopSummaryAmountDTO summaryAmount = dtoList.stream().findFirst().orElse(null);
        //日结金额
        BigDecimal dailyAmount = Objects.nonNull(summaryAmount) ? summaryAmount.getAmount() : BigDecimal.ZERO;
        //日结过的门店
        Example example = new Example(FranchiseSalesAnalysis.class);
        example.createCriteria().andEqualTo("dailyDate", dailyDate).andEqualTo("shopId", shopId);
        List<FranchiseSalesAnalysis> exsistList = franchiseSalesAnalysisMapper.selectByExample(example);
        QYAssert.isTrue(CollectionUtils.isEmpty(exsistList), "今日已日结");
        //查询综合数据
        List<AnalysisBySynthesisDTO> analysisList = this.analysisBySynthesis(Collections.singletonList(shopId), dailyDate);
        AnalysisBySynthesisDTO analysis = analysisList.stream().findFirst().orElse(null);
        //结算单个门店
        settleSingleShop(shopId, Long.valueOf(shop.getStoreId()), userId, dailyAmount, analysis, 1, dailyDate);
    }

    /**
     * 自动日结（job时间到了，自动执行，操作未 日结过的门店，创建人是系统）
     * 1.调用进销存接口，库存清零，同时生成盘盈、盘亏数据
     * 2.日结充值
     */
    @Transactional(rollbackFor = Exception.class)
    public void autoSettleDaily() {
        //查询所有鲜食加盟的门店
        SelectShopDropdownInfoListIDTO idto = new SelectShopDropdownInfoListIDTO();
        idto.setShopType(ShopTypeEnums.XSJM.getCode());
        idto.setSceneType(22);
        List<ShopDropdownInfoODTO> odtoList = shopClient.selectShopDropdownInfoList(idto);
        if (CollectionUtils.isEmpty(odtoList)) {
            return;
        }
        //鲜食加盟门店id集合
        List<Long> shopIds = odtoList.stream().map(ShopDropdownInfoODTO::getShopId).distinct().collect(Collectors.toList());
        String dailyDate = DateUtil.getDateFormate(new Date(), "yyyy-MM-dd");

        // 批量查询门店的可提现余额
        Map<Long, BigDecimal> shopAmountMap = new HashMap<>();
        List<JoinShopSummaryAmountDTO> dtoList = this.getWithdrawableBalance(shopIds, dailyDate);
        if (CollectionUtils.isNotEmpty(dtoList)) {
            dtoList.forEach(x -> shopAmountMap.put(x.getShopId(), x.getAmount()));
        }

        //查询综合数据
        Map<Long, AnalysisBySynthesisDTO> analysisMap = new HashMap<>();
        List<AnalysisBySynthesisDTO> analysisList = this.analysisBySynthesis(shopIds, dailyDate);
        if (CollectionUtils.isNotEmpty(analysisList)) {
            analysisList.forEach(x -> analysisMap.put(x.getShopId(), x));
        }
        //根据门店查询客户id
        List<ShopODTO> shopListById = shopClient.getShopListById(shopIds);
        Map<Long, Long> shopMap = shopListById.stream().collect(Collectors.toMap(ShopODTO::getId, ShopODTO::getStoreId));

        //日结过的门店
        Example example = new Example(FranchiseSalesAnalysis.class);
        example.createCriteria().andEqualTo("dailyDate", dailyDate).andIn("shopId", shopIds);
        List<FranchiseSalesAnalysis> exsistList = franchiseSalesAnalysisMapper.selectByExample(example);
        Map<String, FranchiseSalesAnalysis> map = exsistList.stream().collect(Collectors.toMap(x -> x.getShopId() + "_" + DateUtil.getDateFormate(x.getDailyDate(), "yyyy-MM-dd"), Function.identity()));
        //批量自动日结
        for (Long shopId : shopIds) {
            if (!map.containsKey(shopId + "_" + dailyDate)) {
                //自动日结，只操作未 日结过的
                Integer settleType = 2;
                settleSingleShop(shopId, shopMap.get(shopId), -1L, shopAmountMap.getOrDefault(shopId, BigDecimal.ZERO), analysisMap.get(shopId), settleType, dailyDate);
            }
        }
    }

    /**
     * 日结补偿
     * 创建人是门店自己，修改人是系统（更新营业额）
     *
     * @param dto 日结补偿入参
     */
    @Transactional(rollbackFor = Exception.class)
    public void compensateSettleDaily(CompensateSettleDailyIDTO dto) {
        //校验门店类型
        QYAssert.isTrue(Objects.nonNull(dto) && Objects.nonNull(dto.getShopId()), "补偿门店不能为空");
        ShopDto shop = shopClient.findShopById(dto.getShopId());
        QYAssert.isTrue(Objects.nonNull(shop) && Objects.equals(shop.getShopType(), ShopTypeEnums.XSJM.getCode()), "门店类型错误");
        String dailyDate = Objects.isNull(dto.getDailyDate()) ? DateUtil.getDateFormate(new Date(), "yyyy-MM-dd") : dto.getDailyDate();
        Example example = new Example(FranchiseSalesAnalysis.class);
        example.createCriteria().andEqualTo("dailyDate", dailyDate).andEqualTo("shopId", dto.getShopId());
        List<FranchiseSalesAnalysis> exsistList = franchiseSalesAnalysisMapper.selectByExample(example);
        QYAssert.isTrue(CollectionUtils.isNotEmpty(exsistList), "未查询到该门店当日的日结记录");
        FranchiseSalesAnalysis franchiseSalesAnalysis = exsistList.get(0);
        //查询综合数据
        List<AnalysisBySynthesisDTO> analysisList = this.analysisBySynthesis(Collections.singletonList(dto.getShopId()), dailyDate);
        AnalysisBySynthesisDTO analysis = analysisList.stream().findFirst().orElse(null);
        if (Objects.nonNull(analysis)) {
            //更新综合数据
            franchiseSalesAnalysis.setTurnover(analysis.getTurnover());
            franchiseSalesAnalysis.setTotalCustomers(Math.toIntExact(analysis.getTotalCustomers()));
            franchiseSalesAnalysis.setCustomersBefore19(Math.toIntExact(analysis.getCustomersBefore19()));
            franchiseSalesAnalysis.setSalesBefore19(analysis.getSalesBefore19());
            franchiseSalesAnalysis.setGoodsPayment(analysis.getGoodsPayment());
            franchiseSalesAnalysis.setDiscountRate(analysis.getDiscountRate());
            franchiseSalesAnalysis.setLossRate(analysis.getLossRate());
            franchiseSalesAnalysis.setFranchiseeNetProfit(analysis.getFranchiseeNetProfit());
        }
        franchiseSalesAnalysis.setUpdateId(-1L);
        //更新充值余额
        List<JoinShopSummaryAmountDTO> summaryAmountDTO = this.getWithdrawableBalance(Collections.singletonList(dto.getShopId()), dailyDate);
        //待补偿充值金额
        BigDecimal rechargeAmount = BigDecimal.ZERO;
        if (CollectionUtils.isNotEmpty(summaryAmountDTO)) {
            //可提现余额
            BigDecimal totalAmount = summaryAmountDTO.get(0).getAmount();
            if (totalAmount.compareTo(franchiseSalesAnalysis.getDailyAmount()) > 0) {
                rechargeAmount = totalAmount.subtract(franchiseSalesAnalysis.getDailyAmount());
                //更新日结报表的日结金额
                franchiseSalesAnalysis.setDailyAmount(totalAmount);
            }
        }
        //更新日结报表
        franchiseSalesAnalysisMapper.updateByPrimaryKeySelective(franchiseSalesAnalysis);
        //补偿 充值到账户
        this.recharge(Long.valueOf(shop.getStoreId()), rechargeAmount, franchiseSalesAnalysis.getDailySettleCode());
        log.info("门店{}已补偿{}的余额{}元", dto.getShopId(), dailyDate, rechargeAmount);
    }

    /**
     * 日结单个门店
     * 1.调用进销存接口，库存清零，同时生成盘盈、盘亏数据
     * 2.日结充值
     *
     * @param shopId      门店id
     * @param createId    操作人id
     * @param settleType  1-手动日结 2-自动日结
     * @param storeId     客户id
     * @param dailyAmount 日结金额
     * @param analysis    综合分析数据
     * @param dailyDate   日结日期
     */
    public void settleSingleShop(Long shopId, Long storeId, Long createId, BigDecimal dailyAmount, AnalysisBySynthesisDTO analysis, Integer settleType, String dailyDate) {
        //调用进销存接口，库存清零，同时生成盘盈、盘亏数据
        StockQueryIDTO idto = new StockQueryIDTO();
        idto.setWarehouseId(shopId);
        List<ShopStockDTO> commodityStockList = xdStockClient.queryShopStock(shopId);
        //盘盈商品列表
        List<XjShopCommodityStockKafkaVo.XjShopCommodityStockItemVo> inventorySurplusCommodityList = new ArrayList<>();
        //盘亏商品列表
        List<XjShopCommodityStockKafkaVo.XjShopCommodityStockItemVo> inventoryDeficitCommodityList = new ArrayList<>();
        for (ShopStockDTO shopCommodityStockDTO : commodityStockList) {
            XjShopCommodityStockKafkaVo.XjShopCommodityStockItemVo itemVo = new XjShopCommodityStockKafkaVo.XjShopCommodityStockItemVo();
            itemVo.setCommodityId(shopCommodityStockDTO.getCommodityId());
            itemVo.setQuantity(shopCommodityStockDTO.getStockQuantity().negate());
            itemVo.setStockNumber(-shopCommodityStockDTO.getStockNumber());
            itemVo.setCurrentQuantity(BigDecimal.ZERO);
            itemVo.setCurrentStockNumber(0);
            if (shopCommodityStockDTO.getStockQuantity().compareTo(BigDecimal.ZERO) > 0) {
                // 待盘亏的商品
                inventoryDeficitCommodityList.add(itemVo);
            } else if (shopCommodityStockDTO.getStockQuantity().compareTo(BigDecimal.ZERO) < 0) {
                // 待盘盈的商品
                inventorySurplusCommodityList.add(itemVo);
            }
        }
        // 记录日结报表
        FranchiseSalesAnalysis franchiseSalesAnalysis = this.buildFranchiseSales(shopId, createId, dailyAmount, analysis, settleType, dailyDate);
        franchiseSalesAnalysisMapper.insertSelective(franchiseSalesAnalysis);

        //充值到账户
        this.recharge(storeId, dailyAmount, franchiseSalesAnalysis.getDailySettleCode());

        //盘整
        if (CollectionUtils.isNotEmpty(inventorySurplusCommodityList)) {
            XjShopCommodityStockKafkaVo kafkaVo = new XjShopCommodityStockKafkaVo();
            kafkaVo.setShopId(shopId);
            kafkaVo.setUserId(createId);
            kafkaVo.setCommodityList(inventorySurplusCommodityList);
            kafkaVo.setReferId(franchiseSalesAnalysis.getId());
            kafkaVo.setReferCode(franchiseSalesAnalysis.getDailySettleCode());
            //盘赢
            kafkaVo.setTypeCode(StockInOutTypeEnums.IN_INVENTORY_PLUS.getCode());
            log.info("鲜食加盟门店库存盘赢日志:{}", JSON.toJSONString(kafkaVo));
            kafkaMessageService.sendUpdateXjStock(kafkaVo);
        }
        if (CollectionUtils.isNotEmpty(inventoryDeficitCommodityList)) {
            XjShopCommodityStockKafkaVo kafkaVo = new XjShopCommodityStockKafkaVo();
            kafkaVo.setShopId(shopId);
            kafkaVo.setUserId(createId);
            kafkaVo.setCommodityList(inventoryDeficitCommodityList);
            kafkaVo.setReferId(franchiseSalesAnalysis.getId());
            kafkaVo.setReferCode(franchiseSalesAnalysis.getDailySettleCode());
            //盘亏
            kafkaVo.setTypeCode(StockInOutTypeEnums.OUT_INVENTORY_LOSS.getCode());
            log.info("鲜食加盟门店库存盘亏日志:{}", JSON.toJSONString(kafkaVo));
            kafkaMessageService.sendUpdateXjStock(kafkaVo);
        }
    }

    /**
     * 鲜食加盟获取获取二维码
     */
    public UnionCToBGetQRCodeResp getCToBCode(CToBGetQRCodeReq req) {
        QYAssert.isTrue(req.getPayAmount() != null, "充值金额不能为空!");

        DictionaryODTO dictionaryODTO = dictionaryClient.getDictionaryByCode("xsjmMoreThanAmount");
        BigDecimal xsjmMoreThanAmount = new BigDecimal(dictionaryODTO.getOptionValue());
        QYAssert.isTrue(req.getPayAmount().compareTo(xsjmMoreThanAmount) >= 0, "充值金额不能小于" + xsjmMoreThanAmount + "元!");

        req.setBillCode(String.valueOf(OrderCodeUtil.genAppOrderCodeLong(OrderCodeEnums.XSJM_ADD_CHARGE_BILL_PREFIX)));

        req.setPayAmount(req.getPayAmount());
        req.setPayType(PayTypeIntEnum.UNION_C_TO_B.getCode());
        req.setAppCode(AppCodeEnum.UNION_C_TO_B.getCode());
        return scanPayClient.getCToBQRCode(req);
    }

    /**
     * 鲜食加盟获取客户余额
     *
     * @param shopId
     * @return
     */
    public BigDecimal getStoreCollectPrice(Long shopId) {
        QYAssert.isTrue(shopId != null, "门店id不能为空");
        Shop shop = shopMapper.selectByPrimaryKey(shopId);

        Example ex = new Example(StoreSettlement.class);
        ex.createCriteria().andEqualTo("storeId", shop.getStoreId());
        List<StoreSettlement> ssList = this.storeSettlementMapper.selectByExample(ex);
        StoreSettlement ss = null;
        if (SpringUtil.isNotEmpty(ssList)) {
            ss = ssList.get(0);
        }
        if (null != ss && ss.getCollectStatus()) {
            if (ss.getCollectPrice() == null) {
                ss.setCollectPrice(BigDecimal.ZERO.doubleValue());
            }
            return new BigDecimal(ss.getCollectPrice());
        }
        return BigDecimal.ZERO;
    }

    /**
     * 组装日结信息
     *
     * @param shopId    门店id
     * @param createId  用户id
     * @param dailyDate 日结日期
     */
    private FranchiseSalesAnalysis buildFranchiseSales(Long shopId, Long createId, BigDecimal dailyAmount, AnalysisBySynthesisDTO analysis, Integer settleType, String dailyDate) {
        FranchiseSalesAnalysis franchiseSalesAnalysis = new FranchiseSalesAnalysis();
        franchiseSalesAnalysis.setDailySettleCode(IDGenerator.newOrderCode());
        franchiseSalesAnalysis.setShopId(shopId);
        franchiseSalesAnalysis.setDailyDate(DateUtil.parseDate(dailyDate, "yyyy-MM-dd"));
        franchiseSalesAnalysis.setSettleType(settleType);
        franchiseSalesAnalysis.setDailyAmount(dailyAmount);
        if (Objects.nonNull(analysis)) {
            franchiseSalesAnalysis.setTurnover(analysis.getTurnover());
            franchiseSalesAnalysis.setTotalCustomers(Math.toIntExact(analysis.getTotalCustomers()));
            franchiseSalesAnalysis.setCustomersBefore19(Math.toIntExact(analysis.getCustomersBefore19()));
            franchiseSalesAnalysis.setSalesBefore19(analysis.getSalesBefore19());
            franchiseSalesAnalysis.setGoodsPayment(analysis.getGoodsPayment());
            franchiseSalesAnalysis.setDiscountRate(analysis.getDiscountRate());
            franchiseSalesAnalysis.setLossRate(analysis.getLossRate());
            franchiseSalesAnalysis.setFranchiseeNetProfit(analysis.getFranchiseeNetProfit());
        }
        franchiseSalesAnalysis.setCreateId(createId);
        return franchiseSalesAnalysis;
    }

    /**
     * 调用pos查询批量查询门店可提现余额
     *
     * @param shopIds   门店
     * @param dailyDate 日结日期
     * @return 余额
     */
    public List<JoinShopSummaryAmountDTO> getWithdrawableBalance(List<Long> shopIds, String dailyDate) {
        JoinShopSummaryAmountIDTO req = new JoinShopSummaryAmountIDTO();
        req.setShopIds(shopIds);
        req.setDate(dailyDate);
        return joinShopClient.getSummaryAmount(req);
    }

    /**
     * 调用pos查询批量查询门店综合数据
     *
     * @param shopIds   门店
     * @param dailyDate 日结日期
     * @return 综合数据
     */
    public List<AnalysisBySynthesisDTO> analysisBySynthesis(List<Long> shopIds, String dailyDate) {
        AnalysisBySynthesisIDTO req = new AnalysisBySynthesisIDTO();
        req.setShopIds(shopIds);
        req.setDate(dailyDate);
        return joinShopClient.analysisBySynthesis(req);
    }

    /**
     * 门店日结充值
     */
    public void recharge(Long storeId, BigDecimal rechargeAmount, String dailySettleCode) {
        if (rechargeAmount.compareTo(BigDecimal.ZERO) <= 0) {
            return;
        }
        //充值
        StoreRechargeBO rechargeBO = StoreRechargeBO.builder()
                .orderCode(dailySettleCode)
                .tradeCode(dailySettleCode)
                .money(rechargeAmount.doubleValue())
                .storeId(storeId)
                .tradeTime(new Date())
                .receiptDate(new Date())
                .billType(StoreBillTypeEnums.SHOP_DAY_SETTLE.getCode())
                .remark("<--门店日结 -->")
                .userId(-1L)
                .build();
        try {
            storeRechargeService.storeRecharge(rechargeBO);
        } catch (Exception e) {
            log.warn("门店日结充值失败", e);
        }
    }


    /**
     * 综合分析报表
     *
     * @param dto
     * @return
     */
    public FranchiseSalesAnalysis analysisBySynthesisList(AnalysisBySynthesisListIDTO dto) {
        QYAssert.isTrue(dto.getShopId() != null, "门店id不能为空");
        QYAssert.isTrue(dto.getDate() != null, "日期不能为空");

        Example example = new Example(FranchiseSalesAnalysis.class);
        example.createCriteria().andEqualTo("shopId", dto.getShopId())
                .andEqualTo("dailyDate", dto.getDate());
        List<FranchiseSalesAnalysis> list = franchiseSalesAnalysisMapper.selectByExample(example);
        if (SpringUtil.isNotEmpty(list)) {
            return list.get(0);
        } else {
            return null;
        }
    }

}
