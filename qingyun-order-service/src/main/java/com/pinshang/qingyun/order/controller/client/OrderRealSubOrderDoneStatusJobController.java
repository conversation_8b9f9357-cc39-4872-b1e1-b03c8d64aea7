package com.pinshang.qingyun.order.controller.client;

import com.pinshang.qingyun.base.configure.expand.QYAssert;
import com.pinshang.qingyun.common.dto.DictionaryODTO;
import com.pinshang.qingyun.common.service.DictionaryClient;
import com.pinshang.qingyun.order.service.OrderRealSubOrderDoneStatusService;
import com.pinshang.qingyun.order.vo.OrderRealSubOrderDoneStatusVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Calendar;

/**
 * <AUTHOR>
 * @date 2024/7/2 11:25
 * <p>
 * 回填订单 real_sub_order_done_status 子订单是否出库完标识：只有1代表所有子单(不包括直送)已出库
 * 直接调用 dc 接口获取出库完成的订单 做标志
 */
@RestController
@RequestMapping("/realSubOrderDoneStatus")
@Slf4j
public class OrderRealSubOrderDoneStatusJobController {

    @Autowired
    private OrderRealSubOrderDoneStatusService orderRealSubOrderDoneStatusService;

    @Autowired
    private DictionaryClient dictionaryClient;


    @PostMapping(value = "/exec/job")
    public void processOrderRealSubOrderDoneStatus(@RequestBody OrderRealSubOrderDoneStatusVO vo) {

        if(StringUtils.isBlank(vo.getOrderIds())) {
            if (vo.getStartTime() == null && vo.getEndTime() != null) {
                QYAssert.isFalse("要执行任务的时间区间(开始)不能为空！");
            } else if (vo.getStartTime() != null && vo.getEndTime() == null) {
                QYAssert.isFalse("要执行任务的时间区间(结束)不能为空！");
            } else if (vo.getStartTime() == null && vo.getEndTime() == null) {
                DictionaryODTO dictionaryODTO = dictionaryClient.getDictionaryByCode("realSubOrderDoneTime");
                int minute = 120;
                if (dictionaryODTO != null && dictionaryODTO.getOptionState() != null && dictionaryODTO.getOptionState().intValue() == 1
                        && StringUtils.isNotBlank(dictionaryODTO.getOptionValue())) {
                    minute = Integer.parseInt(dictionaryODTO.getOptionValue());
                }
                Calendar calendar = Calendar.getInstance();
                vo.setEndTime(calendar.getTime());
                calendar.add(Calendar.MINUTE,-(minute));
                calendar.set(Calendar.SECOND, 0);
                calendar.set(Calendar.MILLISECOND, 0);
                vo.setStartTime(calendar.getTime());
            }
        }
        orderRealSubOrderDoneStatusService.processOrderRealSubOrderDoneStatus(vo);
    }
}
