package com.pinshang.qingyun.order.controller.pf;

import java.text.ParseException;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.pinshang.qingyun.base.api.PfTokenInfo;
import com.pinshang.qingyun.base.configure.expand.QYAssert;
import com.pinshang.qingyun.base.util.FastThreadLocalUtil;
import com.pinshang.qingyun.order.dto.xda.QueryXdaComplaintDTO;
import com.pinshang.qingyun.order.dto.xda.XdaComplaintCommodityDTO;
import com.pinshang.qingyun.order.dto.xda.XdaComplaintOrderCancelDTO;
import com.pinshang.qingyun.order.dto.xda.XdaComplaintOrderODTO;
import com.pinshang.qingyun.order.enums.ComplaintTypeEnum;
import com.pinshang.qingyun.order.service.xda.XdaComplaintOrderService;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * 投诉，服用鲜达
 * <AUTHOR>
 *
 */
@RestController
@RequestMapping("/pf/complaint")
@Api(value = "批发投诉单相关api", tags = "xda-complaint")
public class PfComplaintOrderController {

	@Autowired
    private XdaComplaintOrderService xdaComplaintOrderService;


    /***
     * 客户投诉单列表（复用鲜达）
     * @return
     */
    @ApiOperation(value = "客户投诉单列表", notes = "客户投诉单列表")
    @PostMapping("/queryComplaintOrderList")
    public List<XdaComplaintOrderODTO> queryComplaintOrderList(@RequestBody QueryXdaComplaintDTO dto){
        PfTokenInfo xdaTokenInfo = FastThreadLocalUtil.getPF();
        QYAssert.isTrue(!(xdaTokenInfo == null || xdaTokenInfo.getStoreId() == null), "客户未登录！");
        dto.setStoreId(xdaTokenInfo.getStoreId());
        return xdaComplaintOrderService.queryComplaintOrderList(dto);
    }
    
    /***
     * 客户当天可投诉的商品列表
     * @return
     */
    @ApiOperation(value = "客户可投诉的商品列表, 投诉类型 0-差异投诉，1-退货投诉", notes = "可投诉的商品列表")
    @GetMapping("/queryComplaintCommodityList/{complaintType}")
    public XdaComplaintCommodityDTO queryComplaintCommodityList(@PathVariable("complaintType") Integer complaintType) {
    	PfTokenInfo pfTokenInfo = FastThreadLocalUtil.getPF();
        QYAssert.isTrue(!(pfTokenInfo == null || pfTokenInfo.getStoreId() == null), "客户未登录！");
        QYAssert.notNull(complaintType, "投诉类型不能为空！");
        QYAssert.isTrue(complaintType == 0 || complaintType == 1, "不支持此种投诉类型！");
        XdaComplaintCommodityDTO complaintCommodityDTO = new XdaComplaintCommodityDTO();
        complaintCommodityDTO.setStoreId(pfTokenInfo.getStoreId());
        complaintCommodityDTO.setComplaintType(ComplaintTypeEnum.getByCode(complaintType));
        return xdaComplaintOrderService.queryComplaintCommodityList(complaintCommodityDTO);
    }
    
    /***
     * 客户投诉单提交
     * @return
     */
    @ApiOperation(value = "提交投诉单", notes = "投诉单提交")
    @PostMapping("/saveComplaintCommodity")
    public Boolean saveComplaintCommodity(@RequestBody XdaComplaintCommodityDTO complaintCommodityDTO) {
    	PfTokenInfo pfTokenInfo = FastThreadLocalUtil.getPF();
        QYAssert.isTrue(!(pfTokenInfo == null || pfTokenInfo.getStoreId() == null), "客户未登录！");
        complaintCommodityDTO.setStoreId(pfTokenInfo.getStoreId());
        complaintCommodityDTO.setStoreCode(pfTokenInfo.getStoreCode());
        return xdaComplaintOrderService.saveComplaintCommodity(complaintCommodityDTO);
    }
    
    /***
     * 客户投诉单取消
     * @return
     */
    @ApiOperation(value = "取消投诉单", notes = "取消修改投诉单-操作类型，true-取消投诉单下的所有投诉商品，false-取消指定的投诉商品")
    @PostMapping("/cancelComplaintOrder")
    public Boolean cancelComplaintOrder(@RequestBody XdaComplaintOrderCancelDTO dto){
    	PfTokenInfo pfTokenInfo = FastThreadLocalUtil.getPF();
        QYAssert.isTrue(!(pfTokenInfo == null || pfTokenInfo.getStoreId() == null), "客户未登录！");
        dto.setStoreId(pfTokenInfo.getStoreId());
        return xdaComplaintOrderService.cancelComplaintOrder(dto);
    }
    
}
