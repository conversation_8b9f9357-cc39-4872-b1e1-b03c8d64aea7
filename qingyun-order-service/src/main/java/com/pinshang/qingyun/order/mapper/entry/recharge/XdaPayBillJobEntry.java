package com.pinshang.qingyun.order.mapper.entry.recharge;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Data
public class XdaPayBillJobEntry {

    /** 客户ID */
    private Long payBillId;

    /** 付款流水编码(充值单号） */
    private String billCode;

    /** 支付方式：1＝支付宝，2＝微信，5=云闪付支付 **/
    private Integer payType;

    /** 交易流水状态，1＝待支付，2＝支付完成，3＝支付取消（目前定时业务会在查询支付平台”未支付“时，改为”支付取消“，退款也有支付取消），4＝支付失败（待定，实践中于考虑）,5=支付关闭(定时补偿时当支付平台返回交易关闭时改为此状态 ) 6=支付平台支付出错(支付平台内部系统处理出错,服务不可用等,是支付平台针对此交易他们的系统发生了未知的问题)   9=系统交易不存在(有支付流水，没有订单或退货单) **/
    private Integer billStatus;

    /** 外部的交易流水号（支付宝、微信或云闪付返回的支付单号） **/
    private String tradeBillCode;

    private Long storeId;
}