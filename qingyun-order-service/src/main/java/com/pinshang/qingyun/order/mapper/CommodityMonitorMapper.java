package com.pinshang.qingyun.order.mapper;

import com.pinshang.qingyun.order.mapper.entry.commodity.monitor.CommodityDeliveryOrderMonitorEntry;
import com.pinshang.qingyun.order.mapper.entry.commodity.monitor.CommodityOrderMonitorEntry;
import com.pinshang.qingyun.order.mapper.entry.commodity.monitor.CommodityPickOrderMonitorEntry;
import com.pinshang.qingyun.order.mapper.entry.commodity.monitor.CommoditySubOrderMonitorEntry;
import com.pinshang.qingyun.order.vo.commodity.CommodityMonitorRequestVo;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2018/10/11 11:02
 */
@Repository
public interface CommodityMonitorMapper {

    CommodityOrderMonitorEntry queryCommodityOrderQuantity(CommodityMonitorRequestVo vo);

    BigDecimal queryCommodityCancelOrderQuantity(CommodityMonitorRequestVo vo);

    List<CommoditySubOrderMonitorEntry> queryCommoditySubOrderQuantity(CommodityMonitorRequestVo vo);

}
