package com.pinshang.qingyun.order.service;

import com.pinshang.qingyun.base.configure.expand.QYAssert;
import com.pinshang.qingyun.base.enums.IogisticsModelEnums;
import com.pinshang.qingyun.base.enums.YesOrNoEnums;
import com.pinshang.qingyun.base.enums.order.OrderTypeEnum;
import com.pinshang.qingyun.base.enums.storage.StockTypeEnum;
import com.pinshang.qingyun.base.enums.storage.ToBTypeEnums;
import com.pinshang.qingyun.box.utils.DateTimeUtil;
import com.pinshang.qingyun.box.utils.DateUtil;
import com.pinshang.qingyun.box.utils.JsonUtil;
import com.pinshang.qingyun.box.utils.SpringUtil;
import com.pinshang.qingyun.order.dto.TakeCardOrderODTO;
import com.pinshang.qingyun.order.enums.DeliveryBatchTypeEnum;
import com.pinshang.qingyun.order.mapper.ShopMapper;
import com.pinshang.qingyun.order.mapper.entry.MdShopOrderSettingEntry;
import com.pinshang.qingyun.order.model.order.Order;
import com.pinshang.qingyun.order.model.shop.Shop;
import com.pinshang.qingyun.order.util.StallUtils;
import com.pinshang.qingyun.order.util.ThreadLocalUtils;
import com.pinshang.qingyun.order.vo.order.OrderDto;
import com.pinshang.qingyun.shop.admin.dto.take.SelectTakeAppointment4OrderInfoListIDTO;
import com.pinshang.qingyun.shop.admin.dto.take.TakeAppointment4OrderInfoODTO;
import com.pinshang.qingyun.shop.admin.service.TakeAppointmentClient;
import com.pinshang.qingyun.storage.dto.tob.COrderToBOrderDealInventoryIDTO;
import com.pinshang.qingyun.storage.dto.tob.CommodityInventoryODTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RAtomicLong;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * @Author: sk
 * @Date: 2020/11/23
 */
@Slf4j
@Service(value = "takeCardCreateOrderService")
public class TakeCardCreateOrderService {


    @Autowired
    private TakeAppointmentClient takeAppointmentClient;

    @Autowired
    private RedissonClient redissonClient;

    @Autowired
    private ShopMapper shopMapper;

    @Lazy
    @Autowired
    private MdShopOrderSettingService mdShopOrderSettingService;
    @Lazy
    @Autowired
    private OrderService orderService;
    @Autowired
    private GrouponOrderSaveService grouponOrderSaveService;
    @Autowired
    private WeChatSendMessageService weChatSendMessageService;
    @Autowired
    private OrderSplitService orderSplitService;
    @Autowired
    private BStockService bStockService;
    @Autowired
    private ShopService shopService;

    /**
     * 获取送货日期范围中的起始日期 +1
     * @param deliveryTimeRange
     * @return
     */
    public String getBeginDeliveryTimeRange(String deliveryTimeRange){
        String timeRange  = deliveryTimeRange.split("-")[0];
        Calendar calendar = Calendar.getInstance();
        //calendar.add(Calendar.DATE,Integer.valueOf(timeRange) + 1);
        // 送货日期范围起始日期为0，默认加一天
        if(timeRange.equals("0")){
            calendar.add(Calendar.DATE,Integer.valueOf(timeRange) + 1);
        }else {
            calendar.add(Calendar.DATE,Integer.valueOf(timeRange));
        }
        return DateUtil.getDateFormate(calendar.getTime(),"yyyy-MM-dd");
    }

    /**
     * 获取所有匹配的预约单的
     * @param takeAppointmentList
     * @param orderTime
     * @return
     */
    public List<TakeCardOrderODTO> getMatchAppointment(List<TakeAppointment4OrderInfoODTO> takeAppointmentList,String orderTime){
        List<TakeCardOrderODTO> takeCardOrderList = new ArrayList<>();
        for(TakeAppointment4OrderInfoODTO infoODTO : takeAppointmentList){
            String appointDate = DateUtil.getDateFormate(infoODTO.getAppointDate(),"yyyy-MM-dd");
            Shop shop = shopMapper.selectByPrimaryKey(infoODTO.getShopId());

            TakeCardOrderODTO takeCardOrderODTO = new TakeCardOrderODTO();
            takeCardOrderODTO.setTakeAppointmentId(infoODTO.getTakeAppointmentId());
            takeCardOrderODTO.setShopId(infoODTO.getShopId());
            takeCardOrderODTO.setStoreId(shop.getStoreId());
            takeCardOrderODTO.setCommodityId(infoODTO.getRealCommodityId());
            takeCardOrderODTO.setQuantity(new BigDecimal(infoODTO.getQuantity()));

            List<String> commodityIdList = new ArrayList<>();
            commodityIdList.add(infoODTO.getRealCommodityId() + "");
            List<MdShopOrderSettingEntry> settingList = mdShopOrderSettingService.queryMdShopOrderSettingListByIds(shop.getStoreId(), commodityIdList);

            QYAssert.isTrue(SpringUtil.isNotEmpty(settingList), "门店订货配置信息有误");
            MdShopOrderSettingEntry settingEntry = settingList.get(0);
            QYAssert.isTrue(StringUtils.isNotBlank(settingEntry.getDefaultWarehouseEndTime()) && StringUtils.isNotBlank(settingEntry.getDefaultSupplierEndTime()) , "门店订货配置信息有误,供应商或者仓库时间为空");

            takeCardOrderODTO.setOrderTime(appointDate);
            takeCardOrderODTO.setLogisticsModel(settingEntry.getLogisticsModel().intValue());
            takeCardOrderODTO.setSupplierId(Long.valueOf(settingEntry.getSupplierId()));
            takeCardOrderODTO.setWarehouseId(Long.valueOf(settingEntry.getWarehouseId()));
            takeCardOrderODTO.setDeliveryBatch(DeliveryBatchTypeEnum.ONE_BATCH.getCode().toString());
            takeCardOrderODTO.setDeleveryTimeRange(settingEntry.getDeleveryTimeRange());
            takeCardOrderODTO.setEnterpriseId(78L);
            takeCardOrderODTO.setUserId(-1L);
            takeCardOrderODTO.setCreateName("系统");

            Integer logisticsModel = settingEntry.getLogisticsModel().intValue();
            // 配送范围
            String beginDeliveryTimeRange = getBeginDeliveryTimeRange(settingEntry.getDeleveryTimeRange());

            // 预约时间为 ≤T+2（通用订货设置中起始日期）
            if(DateUtil.compareDate(DateUtil.parseDate(appointDate,"yyyy-MM-dd"),DateUtil.parseDate(beginDeliveryTimeRange,"yyyy-MM-dd"))){
                // 配送比较仓库时间
                if( logisticsModel == IogisticsModelEnums.DISPATCHING.getCode()){
                    if(DateTimeUtil.compareTime(settingEntry.getDefaultWarehouseEndTime(),orderTime)){
                        takeCardOrderList.add(takeCardOrderODTO);
                    }

                } else if(logisticsModel == IogisticsModelEnums.DIRECT_CONNECTION.getCode()
                        || logisticsModel == IogisticsModelEnums.DIRECT_SENDING.getCode()){
                    //直通、直送比较供应商时间
                    if(DateTimeUtil.compareTime(settingEntry.getDefaultSupplierEndTime(),orderTime)){
                        takeCardOrderList.add(takeCardOrderODTO);
                    }
                }
            }
        }
        return takeCardOrderList;
    }
    /**
     *
     * @return
     */
    @Async
    public Object createTakeCardOrder(String orderTime) throws Throwable{

        // 校验重复任务
        String lockKey =  "take_card_create_order" + orderTime.replaceAll(":","");
        RAtomicLong lockKeyRa = redissonClient.getAtomicLong(lockKey);
        long lock = lockKeyRa.incrementAndGet();
        if (lock == 1) {
            lockKeyRa.expire(60, TimeUnit.SECONDS);
        }
        if (lock > 1) {
            return 0;
        }

        // 预约时间>=今天的 并且<= 8天后的 所有未生成订单的预约单
        Calendar calendar = Calendar.getInstance();
        String today = DateUtil.getDateFormate(calendar.getTime(),"yyyy-MM-dd");
        calendar.add(Calendar.DATE,8);
        String after8Day = DateUtil.getDateFormate(calendar.getTime(),"yyyy-MM-dd");

        SelectTakeAppointment4OrderInfoListIDTO takeAppointment4OrderInfoIDTO = new SelectTakeAppointment4OrderInfoListIDTO();
        takeAppointment4OrderInfoIDTO.setBeginAppointDate(today);
        takeAppointment4OrderInfoIDTO.setEndAppointDate(after8Day);
        List<TakeAppointment4OrderInfoODTO> takeAppointmentList = takeAppointmentClient.selectTakeAppointment4OrderInfoList(takeAppointment4OrderInfoIDTO);
        if(CollectionUtils.isEmpty(takeAppointmentList)){
            return 0;
        }

        // 提货卡过滤大店
        Set<Long> shopIdList = takeAppointmentList.stream().map(item -> item.getShopId()).collect(Collectors.toSet());
        List<Shop> shopList = shopService.getShopByIdList(new ArrayList<>(shopIdList));
        Map<Long, Long> bigShopIdMap = new HashMap<>(shopList.size());
        shopList.forEach(item -> {
            if(StallUtils.isStallSubcontractor(item.getManagementMode())) {
                bigShopIdMap.put(item.getId(), item.getId());
            }
        });

        takeAppointmentList = takeAppointmentList.stream().filter(p -> !bigShopIdMap.containsKey(p.getShopId())).collect(Collectors.toList());
        if(CollectionUtils.isEmpty(takeAppointmentList)){
            return 0;
        }

        // 获取所有匹配的预约单的
        List<TakeCardOrderODTO> takeCardOrderList = getMatchAppointment(takeAppointmentList,orderTime);
        if(CollectionUtils.isEmpty(takeCardOrderList)){
            return 0;
        }

        // 通过的预约单id
        List<Long> appointmentIdList = takeCardOrderList.stream().map(item -> item.getTakeAppointmentId()).collect(Collectors.toList());

        // B端库存依据,拆单使用
        Map<Long, BigDecimal> orderQuantityGiftMap = new HashMap<>();
        takeCardOrderList.forEach(dto ->{
            orderQuantityGiftMap.put(dto.getCommodityId(), dto.getQuantity());
        });
        Map<Long, CommodityInventoryODTO> getbStockMap = bStockService.getbStockMap(DateUtil.parseDate(DateTimeUtil.defaultDeliveryDate(), "yyyy-MM-dd"), orderQuantityGiftMap);
        takeCardOrderList.forEach(dto ->{
            dto.setPresaleStatus(YesOrNoEnums.NO.getCode());
            CommodityInventoryODTO commodityInventoryODTO = getbStockMap.get(dto.getCommodityId());
            dto.setStockType(commodityInventoryODTO != null ? commodityInventoryODTO.getStockType() : StockTypeEnum.UN_LIMIT.getCode());
            if (Objects.nonNull(commodityInventoryODTO)) {
                Integer sourceRatio = commodityInventoryODTO.getSourceRatio();
                dto.setSourceRatio(sourceRatio);
                Integer targetRatio = commodityInventoryODTO.getTargetRatio();
                dto.setTargetRatio(targetRatio);
                Long targetCommodityId = commodityInventoryODTO.getTargetCommodityId();
                dto.setTargetCommodityId(targetCommodityId);
                Integer convertStatus = commodityInventoryODTO.getCanConvert() ? 1 : 0;
                dto.setConvertStatus(convertStatus);
                BigDecimal quantity = dto.getQuantity();
                // 组合商品目标数量 : 向上取整 ( quantity / source_ratio * target_ratio)
                if (commodityInventoryODTO.getCanConvert()) {
                    BigDecimal targetQuantity = quantity.divide(new BigDecimal(sourceRatio), 0, RoundingMode.UP).multiply(new BigDecimal(targetRatio));
                    dto.setTargetQuantity(targetQuantity);
                }
            }
            dto.setOriginOrderId(dto.getTakeAppointmentId());
            dto.setOriginCommodityId(dto.getCommodityId());
        });

        List<OrderDto> orderDtoList = orderSplitService.convertToOrderDto(takeCardOrderList, "提货卡" , false);
        // 确认下单
        takeAppointmentClient.confirmOrder(appointmentIdList);

        ThreadLocalUtils.setOrderType(OrderTypeEnum.TAKE_AUTO_ORDER.getCode());
        // 循环创建订单
        for(OrderDto orderDto : orderDtoList){
            // 更新一下最新的配置: 商品价格、商品限量、商品是否可变价、商品名称
            orderService.buildProductPrice(orderDto);
            // 保存提货卡订单
            try{
                Order order = grouponOrderSaveService.saveGroupOrder(orderDto, null);
                //c端库存冻结转b端冻结
                COrderToBOrderDealInventoryIDTO cOrderToBOrder = COrderToBOrderDealInventoryIDTO.builder()
                        .oriderIdMap(orderDto.getOriginOrderMap())
                        .orderType(ToBTypeEnums.TAKE_CARD.getCode())
                        .orderId(order.getId())
                        .orderCode(order.getOrderCode())
                        .orderTime(order.getOrderTime())
                        .type(ToBTypeEnums.SALE.getCode())
                        .sourceType(OrderTypeEnum.TAKE_AUTO_ORDER.getCode())
                        .deliveryBatch(order.getDeliveryBatch())
                        .storeId(order.getStoreId())
                        .storeCode(null)
                        .storeName(null)
                        .build();
                bStockService.cOrderToBOrderDealInventory(cOrderToBOrder);
            }catch (Throwable e){
                log.error("自动提交提货卡异常:" + JsonUtil.java2json(orderDto) + e.getMessage());
                weChatSendMessageService.sendWeChatMessage("自动提交提货卡异常");
            }
        }
        ThreadLocalUtils.remove();
        return 1;
    }



}
