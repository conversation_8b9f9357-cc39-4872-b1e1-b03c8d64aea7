package com.pinshang.qingyun.order.mapper;

import com.pinshang.qingyun.base.mybatis.MyMapper;
import com.pinshang.qingyun.order.dto.order.SaleReturnDetailPageIDTO;
import com.pinshang.qingyun.order.dto.order.SaleReturnDetailPageODTO;
import com.pinshang.qingyun.order.mapper.entry.order.SaleReturnItemEntry;
import com.pinshang.qingyun.order.mapper.entry.order.SaleReturnItemForAuditEntry;
import com.pinshang.qingyun.order.model.order.SaleReturnItem;
import com.pinshang.qingyun.order.vo.order.SaleReturnItemAddVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Mapper
@Repository
public interface SaleReturnItemMapper extends MyMapper<SaleReturnItem>{

    List<SaleReturnItemEntry> getSaleReturnItemById(@Param("saleReturnOrderId") String saleReturnOrderId);

    int confirmItems(List<SaleReturnItemAddVo> list);

    List<SaleReturnItemEntry> copySaleReturnItemById(@Param("saleReturnOrderId") String saleReturnOrderId);

    List<SaleReturnItemForAuditEntry> getSaleReturnItemForAuditById(@Param("saleReturnOrderId") String saleReturnOrderId);

    List<SaleReturnDetailPageODTO> saleReturnDetailPage(SaleReturnDetailPageIDTO idto);

    int cancelBySaleReturnId(@Param("saleReturnCode") String saleReturnCode, @Param("remark") String remark);
}
