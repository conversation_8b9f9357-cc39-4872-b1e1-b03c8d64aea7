package com.pinshang.qingyun.order.service;

import com.pinshang.qingyun.base.enums.StoreBillTypeEnums;
import com.pinshang.qingyun.base.enums.YesOrNoEnums;
import com.pinshang.qingyun.box.utils.BeanCloneUtils;
import com.pinshang.qingyun.box.utils.TimeUtil;
import com.pinshang.qingyun.order.bo.StoreDeductionBO;
import com.pinshang.qingyun.order.bo.StoreRechargeBO;
import com.pinshang.qingyun.order.enums.ProductTypeEnums;
import com.pinshang.qingyun.order.mapper.CommodityMapper;
import com.pinshang.qingyun.order.model.commodity.Commodity;
import com.pinshang.qingyun.order.model.order.*;
import com.pinshang.qingyun.price.dto.commodity.CommodityListRequestIDTO;
import com.pinshang.qingyun.price.dto.commodity.CommodityResultODTO;
import com.pinshang.qingyun.price.service.ProductPriceModelClient;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 订单改价Service
 *
 * <AUTHOR>
 * @since 2018年4月26日
 */
@Slf4j
@Service
public class OrderChangePriceService {
    @Autowired
    private OrderService orderService;
    @Autowired
    private CommodityMapper commodityMapper;
    @Autowired
    private SubOrderService subOrderService;
    @Autowired
    private OrderListService orderListService;
    @Autowired
    private OrderHistoryService orderHistoryService;
    @Autowired
    private OrderListGiftService orderListGiftService;
    @Autowired
    private StoreRechargeService storeRechargeService;
    @Autowired
    private StoreSettlementService storeSettlementService;
    @Autowired
    private OrderChangePriceService orderChangePriceService;
    @Autowired
    private ProductPriceModelClient productPriceModelClient;

    /**
     * 根据产品价格方案和用户ID，更新相关订单价格
     *
     * @param productPriceModelId 产品价格方案ID
     * @param userId              用户ID
     * @return 更新后的订单编号列表
     */
    public List<String> updateOrderPrices(Long productPriceModelId, Long userId) {
        log.info("开始更新订单价格，产品价格方案ID: {}, 用户ID: {}", productPriceModelId, userId);

        // 用于发送结算kafka消息
        List<String> updatedOrderCodes = new ArrayList<>();

        // 根据产品价格方案ID 获取所有关联门店
        List<StoreSettlement> storeSettlements = storeSettlementService.getStoreSettlementsByProductPriceModelId(productPriceModelId);
        if (CollectionUtils.isEmpty(storeSettlements)) {
            return updatedOrderCodes;
        }

        // 缓存商品价格，优化重复查询
        Map<Long, BigDecimal> priceCache = new HashMap<>();

        // 遍历所有关联门店，处理订单
        storeSettlements.stream()
                // 只处理可变价的订单列表
                .flatMap(settlement -> orderService.getOrdersByStoreIdAndCpsEq1(settlement.getStoreId()).stream())
                .forEach(order -> {
                    try {
                        // 处理订单，更新订单及其子单的价格
                        orderChangePriceService.processOrder(order, userId, priceCache, updatedOrderCodes);
                    } catch (Exception e) {
                        log.warn("订单改价出错，产品价格方案ID: {}, 订单ID: {}, 异常:", productPriceModelId, order.getId(), e);
                    }
                });

        log.info("订单价格更新完成，更新的订单数量: {}", updatedOrderCodes.size());
        return updatedOrderCodes;
    }

    /**
     * 处理单个订单，更新订单及其子单的价格
     *
     * @param order             订单信息
     * @param userId            用户ID
     * @param priceCache        商品价格缓存
     * @param updatedOrderCodes 更新的订单编号列表
     */
    @Transactional(rollbackFor = Exception.class)
    public void processOrder(Order order, Long userId, Map<Long, BigDecimal> priceCache, List<String> updatedOrderCodes) {
        log.info("订单改价,处理订单ID: {}, 编号: {}", order.getId(), order.getOrderCode());

        List<OrderList> newOrderLists = new ArrayList<>();
        List<OrderList> oldOrderLists = new ArrayList<>();

        // 查询订单子单（t_sub_order）
        List<SubOrder> subOrders = subOrderService.getSubOrdersByOrderId(order.getId());
        BigDecimal finalAmount = BigDecimal.ZERO;
        if (CollectionUtils.isNotEmpty(subOrders)) {
            finalAmount = subOrders.stream()
                    // 处理单个子单
                    .map(subOrder -> processSubOrder(order, subOrder, priceCache, newOrderLists, oldOrderLists))
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
        }

        // 取t_order_list上 的商品的totalPrice 合计金额（t_order_list）
        //BigDecimal orderAmount = orderService.getOrderAmountByOrderId(order.getId());
        Map<String, BigDecimal> amountMap = orderService.getOrderAmountByOrderId(order.getId());
        BigDecimal orderAmount = amountMap.get("orderAmount");
        BigDecimal realTotalAmount = amountMap.get("realTotalAmount");

        Date orderUpdateTime = new Date();

        // 更新订单金额（t_order）
        orderService.updateOrderTotalPrice(order, finalAmount, orderAmount, orderUpdateTime, realTotalAmount);

        // 处理回款和扣款逻辑。
        processRechargeAndDeduction(order, orderAmount, orderUpdateTime);

        // 记录订单历史
        orderHistoryService.logOrderHistory(oldOrderLists, newOrderLists, userId);

        // 结算kafka消息内容
        updatedOrderCodes.add(order.getOrderCode());

        log.info("订单改价,订单处理完成，订单ID: {}, 更新后的金额: {}", order.getId(), finalAmount);
    }

    /**
     * 处理单个子单，更新其明细价格并计算总价
     *
     * @param subOrder   子单信息
     * @param priceCache 商品价格缓存
     * @return 子单总价
     */
    private BigDecimal processSubOrder(Order order, SubOrder subOrder, Map<Long, BigDecimal> priceCache, List<OrderList> newOrderLists, List<OrderList> oldOrderLists) {
        log.info("订单改价,处理子单，子单ID: {}", subOrder.getId());
        Set<Long> doneCommodityIds = new HashSet<>();
        List<SubOrderItem> subOrderItems = subOrderService.getSubOrderItemsBySubOrderId(subOrder.getId());
        BigDecimal subOrderTotalPrice = subOrderItems.stream()
                .map(item -> processSubOrderItem(order, item, priceCache, newOrderLists, oldOrderLists,doneCommodityIds))
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        // 更新子单总价（t_sub_order）
        subOrderService.updateSubOrderTotalPrice(subOrder, subOrderTotalPrice);
        return subOrderTotalPrice;
    }

    /**
     * 处理单个子单明细，更新价格并计算总价
     *
     * @param subOrderItem     子单明细
     * @param priceCache       商品价格缓存
     * @param doneCommodityIds 已处理的商品ID（用于日志）
     * @return 更新后的明细总价
     */
    private BigDecimal processSubOrderItem(Order order, SubOrderItem subOrderItem, Map<Long, BigDecimal> priceCache,
                                           List<OrderList> newOrderLists, List<OrderList> oldOrderLists, Set<Long> doneCommodityIds) {
        // 查询最新商品价格
        BigDecimal newPrice = getPriceForItem(order.getStoreId(), priceCache, subOrderItem);

        // 每个明细行的商品总价
        BigDecimal subOrderItemTotalPrice = subOrderItem.getTotalPrice();
        if (BigDecimal.ZERO.compareTo(subOrderItem.getPrice()) == 0) {
            return subOrderItemTotalPrice;
        }
        if (newPrice != null && subOrderItem.getQuantity() != null) {
            log.info("更新子单明细价格，明细ID: {}, 新价格: {}", subOrderItem.getId(), newPrice);
            Long commodityId = subOrderItem.getCommodityId();

            // 新的子单商品总价
            subOrderItemTotalPrice = subOrderItem.getQuantity().multiply(newPrice);
            if (subOrderItemTotalPrice.compareTo(subOrderItem.getTotalPrice()) == 0) {
                return subOrderItemTotalPrice;
            }

            subOrderItem.setTotalPrice(subOrderItemTotalPrice);
            subOrderItem.setPrice(newPrice);

            // 更新子单明细（t_sub_order_item）
            subOrderService.updateItemByPrimaryKeySelective(subOrderItem);

            // 更新订单明细（t_order_list_gift）
            updateOrderListGift(order.getId(), commodityId, newPrice);

            // 更新订单明细（t_order_list）
            updateOrderList(order.getId(), commodityId, newPrice, newOrderLists, oldOrderLists, doneCommodityIds);

        }

        return subOrderItemTotalPrice;
    }

    /**
     * 获取商品的最新价格
     *
     * @param priceCache   商品价格缓存
     * @param subOrderItem 子单明细
     * @return 商品最新价格
     */
    private BigDecimal getPriceForItem(Long storeId, Map<Long, BigDecimal> priceCache, SubOrderItem subOrderItem) {
        if (!YesOrNoEnums.YES.getCode().equals(subOrderItem.getChangePriceStatus())) {
            return null;
        }

        return priceCache.computeIfAbsent(subOrderItem.getCommodityId(), commodityId -> {
            log.info("订单改价,查询商品价格，商品ID: {}, 客户ID: {}", commodityId, storeId);

            CommodityListRequestIDTO request = new CommodityListRequestIDTO();
            request.setStoreId(String.valueOf(storeId));
            request.setCommodityIdListAll(Collections.singletonList(commodityId));

            List<CommodityResultODTO> result = productPriceModelClient.findStoreCommodityList(request);
            return CollectionUtils.isNotEmpty(result) ? result.get(0).getCommodityPrice() : null;
        });
    }

    /**
     * 更新订单明细（t_order_list）
     *
     * @param orderId          订单ID
     * @param commodityId      商品ID
     * @param newPrice         新价格
     * @param newOrderLists    新订单列表（用于日志）
     * @param oldOrderLists    旧订单列表（用于日志）
     * @param doneCommodityIds 已处理的商品ID（用于日志）
     */
    private void updateOrderList(Long orderId, Long commodityId, BigDecimal newPrice,
                                 List<OrderList> newOrderLists, List<OrderList> oldOrderLists, Set<Long> doneCommodityIds) {


        // 获取订单明细列表
        List<OrderList> orderLists = orderListService.getOrderListByCommodityIdAndOrderId(orderId, commodityId);
        if (CollectionUtils.isEmpty(orderLists)) {
            return;
        }

        // 获取商品信息
        Commodity commodity = commodityMapper.selectByPrimaryKey(commodityId);

        // 处理每个订单明细
        List<OrderList> updateList = orderLists.stream()
                .filter(item -> !ProductTypeEnums.GIFT.getCode().equals(item.getType()))
                .map(orderList -> {
                    OrderList oldOrderList = BeanCloneUtils.copyTo(orderList, OrderList.class);
                    if (Objects.nonNull(commodity)) {
                        oldOrderList.setCommodityCode(commodity.getCommodityCode());
                        oldOrderList.setCommodityName(commodity.getCommodityName());
                    }
                    // 保存旧数据用于日志
                    if (!doneCommodityIds.contains(commodityId)) {
                        oldOrderLists.add(oldOrderList);
                    }

                    // 创建新的订单明细 用于日志和更新
                    OrderList newOrderList = createUpdatedOrderList(orderList, commodity, newPrice);

                    // 保存新数据用于日志
                    if (!doneCommodityIds.contains(commodityId)) {
                        newOrderLists.add(newOrderList);
                    }

                    return newOrderList;
                })
                .collect(Collectors.toList());

        // 批量更新 t_order_list
        if (CollectionUtils.isNotEmpty(updateList)) {
            orderListService.updateBatchOrderList(updateList);
            doneCommodityIds.add(commodityId);
        }
    }

    /**
     * 更新订单明细（t_order_list_gift）
     *
     * @param orderId     订单ID
     * @param commodityId 商品ID
     * @param newPrice    新价格
     */
    private void updateOrderListGift(Long orderId, Long commodityId, BigDecimal newPrice) {
        // 获取订单赠品明细列表
        List<OrderListGift> orderListGifts = orderListGiftService.getOrderListGiftByCommodityIdAndOrderId(orderId, commodityId);
        if (CollectionUtils.isEmpty(orderListGifts)) {
            return;
        }

        // 过滤并处理需要更新的记录
        List<OrderListGift> updateList = orderListGifts.stream()
                .filter(item -> !ProductTypeEnums.GIFT.getCode().equals(item.getType()))
                .map(orderListGift -> {
                    OrderListGift newGift = new OrderListGift();
                    newGift.setId(orderListGift.getId());
                    newGift.setCommodityPrice(newPrice);

                    // 计算总价和实发总价
                    BigDecimal commodityNum = orderListGift.getCommodityNum();
                    if (commodityNum != null && newPrice != null) {
                        BigDecimal totalPrice = newPrice.multiply(commodityNum);
                        newGift.setTotalPrice(totalPrice);

                        // 计算实发总价
                        if (orderListGift.getRealQuantity() != null) {
                            BigDecimal realTotalPrice = totalPrice.divide(commodityNum, 6, RoundingMode.HALF_UP)
                                    .multiply(orderListGift.getRealQuantity())
                                    .setScale(2, RoundingMode.HALF_UP);
                            newGift.setRealTotalPrice(realTotalPrice);
                        }
                    }
                    return newGift;
                })
                .collect(Collectors.toList());

        // 批量更新 t_order_list_gift
        if (CollectionUtils.isNotEmpty(updateList)) {
            orderListGiftService.updateBatchOrderListGift(updateList);
        }
    }

    /**
     * 处理回款和扣款逻辑。
     *
     * @param order           订单对象。
     * @param orderUpdateTime 订单更新时间。
     */
    private void processRechargeAndDeduction(Order order, BigDecimal orderAmount, Date orderUpdateTime) {
        if (order.getOrderAmount().compareTo(orderAmount) == 0) {
            // 订单金额未发生变化，无需处理回款和扣款。
            return;
        }

        boolean isPreStore = storeRechargeService.isPreStore(order.getStoreId());
        if (!isPreStore) {
            // 非预付费客户，无需处理回款和扣款。
            return;
        }

        try {
            // 回款
            storeRechargeService.storeRecharge(buildRechargeBO(order, orderUpdateTime));

            // 扣款
            storeRechargeService.storeDeduction(buildDeductionBO(order, orderAmount, orderUpdateTime));
        } catch (Exception e) {
            log.error("订单改价,处理回款扣款时发生异常，订单ID: {}", order.getId(), e);
        }
    }

    /**
     * 构建回款对象。
     *
     * @param order           订单对象。
     * @param orderUpdateTime 订单更新时间。
     * @return 回款对象。
     */
    private StoreRechargeBO buildRechargeBO(Order order, Date orderUpdateTime) {
        return StoreRechargeBO.builder()
                .orderCode(order.getOrderCode())
                .tradeCode(order.getOrderCode() + "_" + TimeUtil.toString(orderUpdateTime, TimeUtil.DATE_FORMAT_LONG))
                .money(order.getOrderAmount().doubleValue())
                .storeId(order.getStoreId())
                .tradeTime(new Date())
                .receiptDate(new Date())
                .billType(StoreBillTypeEnums.SHOP_ORDER_CHANGE_PRICE_REFUNDMENT.getCode())
                .remark("<--门店订货变价退款:" + order.getOrderCode() + " -->")
                .userId(-1L)
                .build();
    }

    /**
     * 构建扣款对象。
     *
     * @param order       订单对象。
     * @param orderAmount 最新明细行上的订单金额合计。
     * @return 扣款对象。
     */
    private StoreDeductionBO buildDeductionBO(Order order, BigDecimal orderAmount, Date orderUpdateTime) {
        return StoreDeductionBO.builder()
                .orderCode(order.getOrderCode())
                .orderId(order.getId())
                .orderAmount(orderAmount)
                .storeId(order.getStoreId())
                .tradeCode(order.getOrderCode() + "_" + TimeUtil.toString(orderUpdateTime, TimeUtil.DATE_FORMAT_LONG))
                .tradeTime(new Date())
                .orderTime(order.getOrderTime())
                .billType(StoreBillTypeEnums.SHOP_ORDER_CHANGE_PRICE_DEDUCTION.getCode())
                .remark("<--门店订货变价扣款:" + order.getOrderCode() + " -->")
                .userId(-1L)
                .build();
    }

    /**
     * 创建更新后的订单明细 t_order_list
     */
    private OrderList createUpdatedOrderList(OrderList orderList, Commodity commodity, BigDecimal newPrice) {
        OrderList newOrderList = BeanCloneUtils.copyTo(orderList, OrderList.class);
        newOrderList.setId(orderList.getId());
        newOrderList.setCommodityPrice(newPrice);
        if (Objects.nonNull(commodity)) {
            newOrderList.setCommodityCode(commodity.getCommodityCode());
            newOrderList.setCommodityName(commodity.getCommodityName());
        }

        // 计算总价和实发总价
        BigDecimal commodityNum = orderList.getCommodityNum();
        if (Objects.nonNull(commodityNum) && newPrice != null) {
            BigDecimal totalPrice = commodityNum.multiply(newPrice);
            newOrderList.setTotalPrice(totalPrice);

            // 计算实发总价
            if (orderList.getRealQuantity() != null) {
                BigDecimal realTotalPrice = totalPrice.divide(commodityNum, 6, RoundingMode.HALF_UP)
                        .multiply(orderList.getRealQuantity())
                        .setScale(2, RoundingMode.HALF_UP);
                newOrderList.setRealTotalPrice(realTotalPrice);
            }
        }

        return newOrderList;
    }

}
