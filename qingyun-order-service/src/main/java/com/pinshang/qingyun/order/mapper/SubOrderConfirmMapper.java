package com.pinshang.qingyun.order.mapper;

import com.pinshang.qingyun.base.mybatis.MyMapper;
import com.pinshang.qingyun.order.mapper.entry.order.OrderTODeliveryOrderEntry;
import com.pinshang.qingyun.order.model.order.SubOrderConfirm;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;


@Mapper
@Repository
public interface SubOrderConfirmMapper extends MyMapper<SubOrderConfirm> {

    List<OrderTODeliveryOrderEntry> queryUnConfirmSubOrderIdList();

    Integer batchDelete(@Param("subOrderIds") List<Long> subOrderIds);

}
