package com.pinshang.qingyun.order.controller.client;

import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.pinshang.qingyun.order.dto.tms.RefOrderForWaybillODTO;
import com.pinshang.qingyun.order.dto.tms.SelectRefOrderForWaybillIDTO;
import com.pinshang.qingyun.order.dto.tms.WarehouseConfirmInfoODTO;
import com.pinshang.qingyun.order.service.tms.OrderTmsService;

/**
 * 【订单系统 <-> 物流系统】
 **/
@RestController
@RequestMapping("/ordertms/client")
public class OrderTmsClientController {
	
    @Autowired
    private OrderTmsService orderTmsService;

    @ApiOperation(value = "查询【源单-用于运单】")
    @PostMapping(value = "/selectRefOrder")
	@ApiImplicitParam(name = "idto", value = "请求IDTO", required = true, paramType = "body", dataTypeClass = SelectRefOrderForWaybillIDTO.class)
    public RefOrderForWaybillODTO selectRefOrder(@RequestBody SelectRefOrderForWaybillIDTO idto) {
    	return orderTmsService.selectRefOrder(idto);
    }
    
    @ApiOperation(value = "查询【大仓确认信息】列表")
    @PostMapping(value = "/selectWarehouseConfirmInfoList")
    @ApiImplicitParam(name="returnOrderId", value="退货单ID", required = true, paramType = "query", dataTypeClass = Long.class)
    public List<WarehouseConfirmInfoODTO> selectWarehouseConfirmInfoList(@RequestParam(value = "returnOrderId", required = false)Long returnOrderId) {
    	return orderTmsService.selectWarehouseConfirmInfoList(returnOrderId);
    }

}
