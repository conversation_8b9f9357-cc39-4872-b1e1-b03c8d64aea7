package com.pinshang.qingyun.order.service;

import com.github.pagehelper.PageInfo;
import com.pinshang.qingyun.base.api.ApiErrorCodeEnum;
import com.pinshang.qingyun.base.api.TokenInfo;
import com.pinshang.qingyun.base.configure.expand.BizLogicException;
import com.pinshang.qingyun.base.configure.expand.QYAssert;
import com.pinshang.qingyun.base.enums.IogisticsModelEnums;
import com.pinshang.qingyun.base.enums.OrderSourceTypeEnum;
import com.pinshang.qingyun.base.enums.YesOrNoEnums;
import com.pinshang.qingyun.base.enums.shop.ShopTypeEnums;
import com.pinshang.qingyun.base.enums.storage.StockTypeEnum;
import com.pinshang.qingyun.base.util.FastThreadLocalUtil;
import com.pinshang.qingyun.bigdata.dto.order.XsjmOrderReferenceODTO;
import com.pinshang.qingyun.bigdata.dto.order.XsjmOrderReferenceQueryIDTO;
import com.pinshang.qingyun.bigdata.service.OrderReferenceClient;
import com.pinshang.qingyun.box.utils.*;
import com.pinshang.qingyun.cms.service.CommodityEverydayFreshClient;
import com.pinshang.qingyun.marketing.dto.XSPricePromotionCacheGetIDTO;
import com.pinshang.qingyun.marketing.dto.XSPricePromotionCacheODTO;
import com.pinshang.qingyun.marketing.service.MtPricePromotionCacheClient;
import com.pinshang.qingyun.order.client.report.service.QingyunReportClient;
import com.pinshang.qingyun.order.dto.OrderReferenceXsjmDetailODTO;
import com.pinshang.qingyun.order.dto.ShopOrderedQuantityODTO;
import com.pinshang.qingyun.order.dto.StoreOftenBuyProductDTO;
import com.pinshang.qingyun.order.dto.XDCommodityODTO;
import com.pinshang.qingyun.order.enums.MustSellCommodityTypeEnum;
import com.pinshang.qingyun.order.enums.OrderPropertyEnum;
import com.pinshang.qingyun.order.enums.OrderedStatusEnum;
import com.pinshang.qingyun.order.enums.ShopGradeEnum;
import com.pinshang.qingyun.order.mapper.CommodityMapper;
import com.pinshang.qingyun.order.mapper.MdShopOrderSettingMapper;
import com.pinshang.qingyun.order.mapper.OrderMapper;
import com.pinshang.qingyun.order.mapper.PreOrderMapper;
import com.pinshang.qingyun.order.mapper.entry.MdShopOrderSettingEntry;
import com.pinshang.qingyun.order.mapper.entry.SupplyBaseEntry;
import com.pinshang.qingyun.order.mapper.entry.commodity.*;
import com.pinshang.qingyun.order.model.order.PreOrder;
import com.pinshang.qingyun.order.model.promotion.Promotion;
import com.pinshang.qingyun.order.model.promotion.PromotionProduct;
import com.pinshang.qingyun.order.model.shop.Shop;
import com.pinshang.qingyun.order.service.auto.AutoShopCommodityService;
import com.pinshang.qingyun.order.service.order.OrderReferenceCheckService;
import com.pinshang.qingyun.order.util.BeanUtil;
import com.pinshang.qingyun.order.util.OrderTimeUtil;
import com.pinshang.qingyun.order.util.OrderUtil;
import com.pinshang.qingyun.order.util.StallUtils;
import com.pinshang.qingyun.order.vo.commodity.CommodityListRequestVO;
import com.pinshang.qingyun.order.vo.commodity.CommodityVO;
import com.pinshang.qingyun.order.vo.commodity.PreCommodityRequestVo;
import com.pinshang.qingyun.price.dto.commodity.CommodityListRequestIDTO;
import com.pinshang.qingyun.price.dto.commodity.CommodityResultODTO;
import com.pinshang.qingyun.price.service.ProductPriceModelClient;
import com.pinshang.qingyun.renderer.client.dto.DictionaryODTO;
import com.pinshang.qingyun.shop.admin.dto.ConsignmentSupplierInfoODTO;
import com.pinshang.qingyun.shop.admin.dto.MustSellCommodityODTO;
import com.pinshang.qingyun.shop.admin.service.MdShopMustSellCommodityClient;
import com.pinshang.qingyun.shop.dto.shopCommodity.*;
import com.pinshang.qingyun.shop.service.ShopCommodityClient;
import com.pinshang.qingyun.shop.service.ShopCommodityUpDownManagerClient;
import com.pinshang.qingyun.shop.service.consignment.ConsignmentClient;
import com.pinshang.qingyun.shop.service.mdCheck.MdCheckReportClient;
import com.pinshang.qingyun.storage.dto.tob.CommodityInventoryODTO;
import com.pinshang.qingyun.storage.service.CommoditySupplierClient;
import com.pinshang.qingyun.storage.service.CommodityWarehouseClient;
import com.pinshang.qingyun.xd.wms.dto.ShopCommodityStockDTO;
import com.pinshang.qingyun.xd.wms.dto.StockQueryIDTO;
import com.pinshang.qingyun.xd.wms.service.XdStockClient;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import tk.mybatis.mapper.entity.Example;

import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@Slf4j
public class ProductService {
	
	@Autowired
	private CommodityMapper commodityMapper;
	
	@Autowired
	private ShoppingCartService shoppingCartService;
	
	@Autowired
	private ShopService shopService;

	@Autowired
	private PreOrderMapper preOrderMapper;

	@Autowired
	private CommoditySupplierClient commoditySupplierClient;

	@Autowired
	private ShopCommodityClient shopCommodityClient;

	@Lazy
	@Autowired
	private MdShopOrderSettingService mdShopOrderSettingService;

	@Autowired
	private QingyunReportClient qingyunReportClient;

	@Autowired
	private XdStockClient xdStockClient;

	@Autowired
	private CommodityWarehouseClient commodityWarehouseClient;

	@Autowired
	private MdShopOrderSettingMapper mdShopOrderSettingMapper;

	@Autowired
	private CommonService commonService;

	@Autowired
	private MtPricePromotionCacheClient mtPricePromotionCacheClient;
	@Autowired
	private CommodityEverydayFreshClient commodityEverydayFreshClient;
	@Autowired
	private MdCheckReportClient mdCheckReportClient;
	@Autowired
	private ConsignmentClient consignmentClient;
	@Autowired
	private ProductPriceModelClient productPriceModelClient;
	@Autowired
	private AutoShopCommodityService autoShopCommodityService;
	@Autowired
	private OrderMapper orderMapper;
	@Autowired
	private ShopCommodityUpDownManagerClient shopCommodityUpDownManagerClient;
	@Autowired
	private MdShopMustSellCommodityClient mdShopMustSellCommodityClient;
	@Autowired
	private BStockService bStockService;
	@Autowired
	private ConsignmentSupplierService consignmentSupplierService;
	@Autowired
	private OrderReferenceClient orderReferenceClient;
	@Autowired
	private OrderReferenceService orderReferenceService;
	@Autowired
	private OrderReferenceCheckService orderReferenceCheckService;

	/**
	 * 获取toB的特价
	 * @param storeId
	 * @return
	 */
	public Map<String, CommodityResultEntry> getToBPromotionPrice(String storeId){
		CommodityListRequestVO vo=new CommodityListRequestVO();
		vo.setOrderTime(DateTimeUtil.defaultDeliveryDate());
		vo.setStoreId(storeId);
		List<CommodityResultEntry> promotionCommodityCodeList = commonService.findPromotionCommodityCodeList(vo);
		Map<String, CommodityResultEntry> commodityCodeAndPromotionPriceMap = promotionCommodityCodeList.stream()
				.collect(Collectors.toMap(CommodityResultEntry::getCommodityCode, Function.identity()));
	    return commodityCodeAndPromotionPriceMap;
	}

	//获取日期星期
	public  String dateToWeek(String datetime) {
		SimpleDateFormat f = new SimpleDateFormat("yyyy-MM-dd");
		String[] weekDays = { "周日", "周一", "周二", "周三", "周四", "周五", "周六" };
		Calendar cal = Calendar.getInstance(); // 获得一个日历
		Date datet = null;
		try {
			datet = f.parse(datetime);
			cal.setTime(datet);
		} catch (ParseException e) {
			e.printStackTrace();
		}
		int w = cal.get(Calendar.DAY_OF_WEEK) - 1; // 指示一个星期中的某天。
		if (w < 0) {
            w = 0;
        }
		return weekDays[w];
	}

	//解析条码
	private void checkBarcode(CommodityListRequestVO vo) {
		String barCode=vo.getBarCode();
		if(StringUtils.isNotBlank(barCode)){
			// 是否为称重码(称重码为2打头 18位)
			boolean isWeightCode = barCode.length() == 18 && barCode.startsWith("2");
			if(isWeightCode){
				// 获取条形码
				barCode = barCode.substring(1, 7);
				vo.setBarCode(barCode);
			}
		}
	}
	//获取特价(to B的特价)
	private void setPromotionPrice(CommodityListRequestVO vo, CommodityHandleEntry resultEntry) {
		//获取某门店的所有特价商品信息列表
		Map<String, CommodityResultEntry> map=getToBPromotionPrice(vo.getStoreId());
		CommodityResultEntry commodityResultEntry = map.get(resultEntry.getCommodityCode());
		if (commodityResultEntry != null) {
			resultEntry.setCommodityPrice(commodityResultEntry.getCommodityPrice());
			resultEntry.setPromotionProduct(true);
		}
	}
	public  int differentDaysByMillisecond(Date date1, Date date2){
		int days = (int) ((date2.getTime() - date1.getTime()) / (1000*3600*24));
		return days;
	}
	/**设置月均销量
	 * 月均销量的计算分2种情况：
	 	如果从第一次收货到昨天的日期差为N天 >= 30天，则计算公式为：30天的总销量 / 30 ;
	 	如果从第一次收货到昨天的日期差为N天 < 30天，则计算公式为：N天的总销量 / N
	 */
	public void setAvgMonthSalesQuantity(Long storeId,CommodityHandleEntry resultEntry){
		if(StringUtils.isNotBlank(resultEntry.getInStorageDate())){
			Calendar cal   =   Calendar.getInstance();
			cal.add(Calendar.DATE,   -1);
			String yesterday=DateUtil.getDateFormate(cal.getTime(),"yyyy-MM-dd");
			int days=differentDaysByMillisecond(DateUtil.parseDate(resultEntry.getInStorageDate(),"yyyy-MM-dd"),DateUtil.parseDate(yesterday,"yyyy-MM-dd"));
			if(days>=30){
				days=30;
			}
			if(days<=0){
				return;
			}
			//根据days的时间天数，计算开始时间统计总销量
			Calendar cal1   =   Calendar.getInstance();
			cal1.add(Calendar.DATE,   -days);
			String inBegin=DateUtil.getDateFormate(cal1.getTime(),"yyyy-MM-dd");
			//调用report去查询
			List<CommodityOrderSalesEntry> list=qingyunReportClient.findMonthSalesQuanty(Long.valueOf(resultEntry.getCommodityId()),storeId,inBegin,yesterday,1);
			if(!CollectionUtils.isEmpty(list)  &&  null!=list.get(0) && null !=list.get(0).getSalesQuanty() ){
				resultEntry.setAvgMonthSalesQuantity(list.get(0).getSalesQuanty().divide(new BigDecimal(days),0,BigDecimal.ROUND_HALF_UP));
			}else{
				resultEntry.setAvgMonthSalesQuantity(BigDecimal.ZERO);
			}
		}
	}
	//计算一周前之内的进销数量
	public void setWeekSalesQuantity(Long storeId,CommodityHandleEntry resultEntry){
		Calendar start   =   Calendar.getInstance();
		start.add(Calendar.DATE,   -7);
		Calendar end   =   Calendar.getInstance();
		end.add(Calendar.DATE,   -1);

		String yesterday=DateUtil.getDateFormate(end.getTime(),"yyyy-MM-dd");
		String yesterBegin=DateUtil.getDateFormate(start.getTime(),"yyyy-MM-dd");
		////调用report去查询
		List<CommodityOrderSalesEntry> list=qingyunReportClient.findMonthSalesQuanty(Long.valueOf(resultEntry.getCommodityId()),storeId,yesterBegin,yesterday,0);
		List<CommodityOrderSalesEntry> weekList=new ArrayList<CommodityOrderSalesEntry>();
		Map<String,CommodityOrderSalesEntry> map=new HashMap<>();
		if(!CollectionUtils.isEmpty(list)){
			for(CommodityOrderSalesEntry entry:list){
				map.put(entry.getSaleDate(),entry);
			}
		}
		while (start.getTime().before(end.getTime()) || start.getTime().equals(end.getTime())) {
			String dateStr=DateUtil.getDateFormate(start.getTime(),"yyyy-MM-dd");
			if(map.get(dateStr)!=null){
				CommodityOrderSalesEntry ce=map.get(dateStr);
				ce.setSaleDate(dateStr.substring(dateStr.length()-2,dateStr.length()));
				ce.setWeek(dateToWeek(dateStr));
				weekList.add(ce);
			}else{
				CommodityOrderSalesEntry ent=new CommodityOrderSalesEntry();
				ent.setSaleDate(dateStr.substring(dateStr.length()-2,dateStr.length()));
				ent.setWeek(dateToWeek(dateStr));
				weekList.add(ent);
			}
			start.add(Calendar.DATE,   1);
		}
		resultEntry.setOrderSaleList(weekList);
	}
	//设置是否可以加入购物车
	public void addToShoppingCart(CommodityHandleEntry resultEntry){
		String supplyStartTime=resultEntry.getSupplyStartTime();
		String supplyEndTime=resultEntry.getSupplyEndTime();
		if(StringUtils.isBlank(supplyStartTime) || StringUtils.isBlank(supplyEndTime)){
			QYAssert.isFalse("订货设置异常，请联系管理员!");
		}
		Date hourbegin = DateUtil.parseDate(supplyStartTime, "HH:mm");
		Date hourend = DateUtil.parseDate(supplyEndTime, "HH:mm");
		Date newDate=DateUtil.parseDate(DateUtil.getDateFormate(new Date(),"HH:mm"),"HH:mm");
	    if(newDate.getTime() >= hourbegin.getTime() && newDate.getTime() <= hourend.getTime()){
			resultEntry.setAddToShoppingCart(true);
		}
	}
	//获取商品特价（to C 的特价）
	public  BigDecimal getPromotionPrice(Long shopId,Long commodityId,BigDecimal retailPrice){
		// 获取商品特价
		XSPricePromotionCacheGetIDTO pricePromotionIdto = new XSPricePromotionCacheGetIDTO();
		pricePromotionIdto.setChannelType(OrderSourceTypeEnum.POS.getCode());
		pricePromotionIdto.setShopId(shopId);
		List<XSPricePromotionCacheGetIDTO.XSPricePromotionCommodityIDTO> pricePromotionCommodityVos= new ArrayList<>();
		XSPricePromotionCacheGetIDTO.XSPricePromotionCommodityIDTO xsPricePromotionCommodityVo = new XSPricePromotionCacheGetIDTO.XSPricePromotionCommodityIDTO();
		xsPricePromotionCommodityVo.setCommodityId(commodityId);
		xsPricePromotionCommodityVo.setCommodityPrice(retailPrice);
		xsPricePromotionCommodityVo.setQuantity(BigDecimal.ONE);
		pricePromotionCommodityVos.add(xsPricePromotionCommodityVo);
		pricePromotionIdto.setCommodityList(pricePromotionCommodityVos);
		BigDecimal promotionPrice = null;
		try{
			Map<Long, XSPricePromotionCacheODTO> specialOfferMap = mtPricePromotionCacheClient.getMap(pricePromotionIdto);
			XSPricePromotionCacheODTO xsPricePromotionCacheODTO = specialOfferMap.get(commodityId);
			if(xsPricePromotionCacheODTO != null){
				promotionPrice = xsPricePromotionCacheODTO.getPrice();
			}
		}catch (Exception e){
			log.error("get promotion price error, exception is:", e);
		}
		return promotionPrice;
	}
	/**
	 * 扫码订货》商品条码,商品ID查询(手持)
	 * @param vo
	 * @return
	 */
	public CommodityHandleEntry findCommodityByBarcodeOrId(CommodityListRequestVO vo){
		QYAssert.notNull(vo.getStoreId(),"客户ID不能为空");
		CommodityHandleEntry resultEntry=new CommodityHandleEntry();
		//解析条码
		checkBarcode(vo);

		Shop shop = shopService.getShopByStoreId(Long.valueOf(vo.getStoreId()));

		// 查询门店下面可采购的商品
		vo.setShopId(shop.getId());
		List<Long> commodityPurchaseList = commodityMapper.findShopCommodityPurchaseList(vo);
		if(!CollectionUtils.isEmpty(commodityPurchaseList)){
			vo.setCommodityPurchaseIdList(commodityPurchaseList);
		}else {
			throw new BizLogicException(ApiErrorCodeEnum.PRICE_MODEL_NO_COMMODITY.getCode() + "商品不可售或不可采", ApiErrorCodeEnum.PRICE_MODEL_NO_COMMODITY);
		}

		// 门店PC订货直接过滤掉 代销的直送商品
		List<ConsignmentSupplierInfoODTO> consignmentSupplierList = consignmentSupplierService.queryConsignmentSupplierList(Collections.singletonList(Long.valueOf(vo.getStoreId())), commodityPurchaseList);
		if(org.apache.commons.collections.CollectionUtils.isNotEmpty(consignmentSupplierList)){
			throw new BizLogicException(ApiErrorCodeEnum.PRICE_MODEL_NO_COMMODITY.getCode() + "不能订购代销商品", ApiErrorCodeEnum.PRICE_MODEL_NO_COMMODITY);
		}

		// 订货列表，判断是否是档口模式。档口模式下，只显示档口商品
		TokenInfo ti = FastThreadLocalUtil.getQY();
		Boolean isBigShop = StallUtils.isStallSubcontractor(shop.getManagementMode());
		if(isBigShop){
			List<Long> stallCommodityIdList = consignmentSupplierService.getCommodityIdListByStallIds(ti.getShopId(), vo.getStallId());
			if(CollectionUtils.isEmpty(stallCommodityIdList)){
				throw new BizLogicException(ApiErrorCodeEnum.PRICE_MODEL_NO_COMMODITY.getCode() + "该档口下，不允许订此商品", ApiErrorCodeEnum.PRICE_MODEL_NO_COMMODITY);
			}else {
				if(!stallCommodityIdList.contains(commodityPurchaseList.get(0))){
					throw new BizLogicException(ApiErrorCodeEnum.PRICE_MODEL_NO_COMMODITY.getCode() + "该档口下，不允许订此商品", ApiErrorCodeEnum.PRICE_MODEL_NO_COMMODITY);
				}
			}
		}
		/*// 代销商判断
		TokenInfo tokenInfo = FastThreadLocalUtil.getQY();
		// 非代理进来，拆分数据权限
		if(!tokenInfo.getIsInternal() && tokenInfo.getConsignmentId() != null && tokenInfo.getConsignmentId() > 0){
			List<Long> consignmentCommIds = consignmentClient.getCommodityIdListByShopIdAndConsignment(tokenInfo.getShopId(), tokenInfo.getConsignmentId());
			if(CollectionUtils.isEmpty(consignmentCommIds)){
				throw new BizLogicException(ApiErrorCodeEnum.PRICE_MODEL_NO_COMMODITY.getCode() + "代销商下无商品", ApiErrorCodeEnum.PRICE_MODEL_NO_COMMODITY);
			}else {
				if(!consignmentCommIds.contains(commodityPurchaseList.get(0))){
					throw new BizLogicException(ApiErrorCodeEnum.PRICE_MODEL_NO_COMMODITY.getCode() + "代销商没有该商品", ApiErrorCodeEnum.PRICE_MODEL_NO_COMMODITY);
				}
			}
		}*/

		// 调用client 去 qingyun-price 查询
		CommodityListRequestIDTO idto = new CommodityListRequestIDTO();
		BeanUtils.copyProperties(vo,idto);
		PageInfo<CommodityResultODTO> resultPageData = productPriceModelClient.findStoreCommodityListByPage(idto);

		PageInfo<CommodityResultEntry> pageData = BeanUtil.copyProperties(resultPageData,CommodityResultEntry.class);
		this.findCommodityListForShoppingCartByPage(pageData.getList(),shop.getStoreId(),shop.getId(),shop.getShopType(),shop.getManagementMode(),vo.getStallId());

		if(SpringUtil.isEmpty(pageData.getList())){
			throw new BizLogicException(ApiErrorCodeEnum.PRICE_MODEL_NO_COMMODITY.getCode() + "价格方案中没有该商品", ApiErrorCodeEnum.PRICE_MODEL_NO_COMMODITY);
		}

//		this.processCommodityLogisticsModel(vo.getStoreId(),pageData);

		// 供应商时间，如果商品是配送模式，供应商时间取仓库时间
		setSupplyTime(Long.valueOf(vo.getStoreId()), pageData.getList());

		CommodityResultEntry result=pageData.getList().get(0);
		BeanUtils.copyProperties(result,resultEntry);

		// 设置是否可以加入购物车
		addToShoppingCart(resultEntry);

		// 设置商品的特价(总部给门店to B的特价)
		this.setPromotionPrice(vo, resultEntry);

		//获取门店给C端客户的特价
		BigDecimal promotionPrice = getPromotionPrice(result.getShopId(),Long.valueOf(resultEntry.getCommodityId()),resultEntry.getRetailPrice());
		if(null !=promotionPrice){
			resultEntry.setPromotionPrice(promotionPrice);
		}
		//理论毛利率：有特价的取特价，没有特价的取零售价进行计算
		BigDecimal price=resultEntry.getPromotionPrice()!=null?resultEntry.getPromotionPrice():resultEntry.getRetailPrice();
		if(null !=price){
			resultEntry.setGrossProfitRate(price.subtract(resultEntry.getCommodityPrice()).divide(price,4,BigDecimal.ROUND_HALF_UP).multiply(new BigDecimal(100)).setScale(2)+"%");
		}
		//计算月均销量
		setAvgMonthSalesQuantity(Long.valueOf(vo.getStoreId()),resultEntry);
		//计算前7日进销量
		setWeekSalesQuantity(Long.valueOf(vo.getStoreId()),resultEntry);
		//计算标签
		if(null !=resultEntry.getNewProductFlag() && "01".equals(resultEntry.getNewProductFlag())){
			resultEntry.setNewProduct(true);
		}
		//计算购物车此商品数量
		List<CommodityResultEntry> cartList=commodityMapper.findCartNumByCommodityId(Long.valueOf(vo.getStoreId()),Long.valueOf(resultEntry.getCommodityId()));
		if(!CollectionUtils.isEmpty(cartList)){
			resultEntry.setCartNum(cartList.get(0).getCartNum());
		}
		// 获取已订未收 订单数量
		BigDecimal orderedQuantity = commodityMapper.getOrderedQuantity(vo.getStoreId(),vo.getCommodityId(),vo.getBarCode());
		resultEntry.setOrderedQuantity(orderedQuantity == null ? BigDecimal.ZERO : orderedQuantity);

		// 获取自动订货商品
		List<Long> autoCOmmodityIdList = autoShopCommodityService.getAutoCommodityList(Long.valueOf(vo.getStoreId()));
		resultEntry.setAutoCommodity(!CollectionUtils.isEmpty(autoCOmmodityIdList) && autoCOmmodityIdList.contains(Long.valueOf(resultEntry.getCommodityId())));

		// 查询B端库存依据
		Map<Long, BigDecimal> orderQuantityMap = new HashMap<>();
		orderQuantityMap.put(Long.valueOf(resultEntry.getCommodityId()), BigDecimal.ZERO);
		Map<Long, CommodityInventoryODTO> toBStockMap = bStockService.getbStockMap(DateUtil.parseDate(DateTimeUtil.defaultDeliveryDate(), "yyyy-MM-dd"), orderQuantityMap);

		// 判断商品可订货状态
		Boolean betweenNow = DateTimeUtil.compareNewDate(resultEntry.getSupplyStartTime(), resultEntry.getSupplyEndTime());
		resultEntry.setOrderedStatus(!betweenNow ? OrderedStatusEnum.OVER_TIME.getCode() : OrderedStatusEnum.ORDERED.getCode());
		CommodityInventoryODTO commodityInventoryODTO = toBStockMap.get(Long.valueOf(resultEntry.getCommodityId()));
		Boolean isDirect = Integer.valueOf(IogisticsModelEnums.DIRECT_SENDING.getCode()).equals(result.getLogisticsModel());
		if(commodityInventoryODTO != null){
			if(StockTypeEnum.WAREHOUSE.getCode().equals(commodityInventoryODTO.getStockType())
					|| StockTypeEnum.LIMIT.getCode().equals(commodityInventoryODTO.getStockType())){
				if(!commodityInventoryODTO.getHaveInventory() && !isDirect){ // 无库存
					resultEntry.setOrderedStatus(OrderedStatusEnum.SOLD_OUT.getCode());
				}
			}
			if(betweenNow && (commodityInventoryODTO.getHaveInventory() || isDirect)){
				resultEntry.setOrderedStatus(OrderedStatusEnum.ORDERED.getCode());
			}
		}
		return resultEntry;
	}

	public List<CommodityResultEntry> findCommodityListForShoppingCartByPage(List<CommodityResultEntry> resultList, Long storeId, Long shopId, Integer shopType,
																			 Integer managementMode, Long stallId) {
		if (SpringUtil.isEmpty(resultList)) {
			return resultList;
		}
		List<Long> commodityList = resultList.stream()
				.map(item -> Long.parseLong(item.getCommodityId()))
				.collect(Collectors.toList());

		CommodityVO commodityVO = new CommodityVO();
		commodityVO.setCommodityIdList(commodityList);
		List<CommodityBasicEntry> commList = commodityMapper.findCommodityBasicListByParam(commodityVO);
		Map<String, CommodityBasicEntry> commMap = commList.stream().collect(Collectors.toMap(CommodityBasicEntry::getCommodityId, Function.identity()));

		Set<Long> dictionaryIdSet = new HashSet<>();
		if (SpringUtil.isNotEmpty(commList)) {
			commList.forEach(item -> {
				dictionaryIdSet.add(item.getCommodityCycleId());
				dictionaryIdSet.add(item.getCommodityUnitId());
			});
		}
		Map<String, DictionaryODTO> dictionaryMap = commonService.listDictionarysByIdSet(dictionaryIdSet);

		ShopCommodityInfoIDTO idto = new ShopCommodityInfoIDTO();
		idto.setStoreId(storeId);
		idto.setCommodityIdList(commodityList);
		// 获取xs店 上下架
		Map<Long, ShopCommodityInfoODTO> idAndObjMap = shopCommodityClient.getShopCommodityList(idto);

		// 获取鲜到库存
		Map<Long, ShopCommodityInfoODTO> xdStockMap = new HashMap<>();
		if(StallUtils.isStallSubcontractor(managementMode)){
			xdStockMap = getBigShopCommodityStock(shopId, stallId, commodityList,false);
		}else {
			xdStockMap = getXdShopCommodityStock(shopId,commodityList);
		}


		for(CommodityResultEntry item : resultList){
			CommodityBasicEntry resultEntry = commMap.get(item.getCommodityId());
			if(null != resultEntry){
				BeanUtils.copyProperties(resultEntry,item);
			}

			if(ShopTypeEnums.XD.getCode().equals(shopType) || StallUtils.isStallSubcontractor(managementMode)){
				item.setSalesBoxCapacity(item.getXdSalesBoxCapacity());
			}

			item.setNewProductFlag(null != dictionaryMap.get(item.getCommodityCycleId() + "") ? dictionaryMap.get(item.getCommodityCycleId() + "").getOptionCode() : "");
			item.setCommodityUnit(null != dictionaryMap.get(item.getCommodityUnitId() + "" ) ? dictionaryMap.get(item.getCommodityUnitId() + "").getOptionName() : "");

			item.setShopId(shopId);
			Long commodityId = Long.parseLong(item.getCommodityId());
			ShopCommodityInfoODTO shopCommodity = idAndObjMap.get(commodityId);
			if (shopCommodity != null) {
				item.setRetailPrice(shopCommodity.getCommodityPrice());
				BigDecimal onlineQuantity = shopCommodity.getOnlineQuantity() != null ? shopCommodity.getOnlineQuantity() : BigDecimal.ZERO;
				BigDecimal orderedQuantity = shopCommodity.getOrderedQuantity() != null ? shopCommodity.getOrderedQuantity() : BigDecimal.ZERO;
				item.setOnlineQuantity(onlineQuantity.add(orderedQuantity));
				item.setInStorageDate(shopCommodity.getInStorageDate());
				item.setAppStatus(shopCommodity.getAppStatus());
			}

			ShopCommodityInfoODTO xdShopCommodity = xdStockMap.get(commodityId);
			if (xdShopCommodity != null) {
				item.setStockQuantity(xdShopCommodity.getStockQuantity());
				item.setStockNumber(xdShopCommodity.getStockNumber());
			}
		}

		return resultList;
	}

	/**
	 * 获取大店排面区库存
	 * @param shopId
	 * @param stallId
	 * @param commodityList
	 * @return
	 */
	public Map<Long, ShopCommodityInfoODTO> getBigShopCommodityStock(Long shopId, Long stallId, List<Long> commodityList, Boolean isReturnOrder){
		Map<Long, ShopCommodityInfoODTO> xdStockMap = new HashMap<>();
		StockQueryIDTO queryDTO = new StockQueryIDTO();
		queryDTO.setWarehouseId(shopId);
		queryDTO.setStallId(stallId);
		queryDTO.setCommodityList(commodityList);
		List<ShopCommodityStockDTO> stockList = xdStockClient.queryBigShopCommodityStock(queryDTO);
		if(CollectionUtils.isEmpty(stockList)){
			return new HashMap<>(1024);
		}
		for(ShopCommodityStockDTO stock : stockList){
			ShopCommodityInfoODTO infoODTO = new ShopCommodityInfoODTO();
			infoODTO.setCommodityId(stock.getCommodityId());
			if(isReturnOrder){
				infoODTO.setStockQuantity(stock.getStockQuantity());
			}else {
				infoODTO.setStockQuantity(stock.getStockQuantity().add(stock.getPickingAreaStock()).add(stock.getWarehouseAreaStock()));
			}

			xdStockMap.put(stock.getCommodityId(),infoODTO);
		}
		return xdStockMap;
	}


	/**
	 * 获取鲜到库存、份数
	 * @param shopId
	 * @param commodityList
	 * @return
	 */
	public Map<Long, ShopCommodityInfoODTO> getXdShopCommodityStock(Long shopId,List<Long> commodityList){
		Map<Long, ShopCommodityInfoODTO> xdStockMap = new HashMap<>();
		StockQueryIDTO queryDTO = new StockQueryIDTO();
		queryDTO.setWarehouseId(shopId);
		queryDTO.setCommodityList(commodityList);
		List<ShopCommodityStockDTO> stockList = xdStockClient.queryShopCommodityStock2(queryDTO);
		if(CollectionUtils.isEmpty(stockList)){
			return new HashMap<>(1024);
		}
		for(ShopCommodityStockDTO stock : stockList){
			ShopCommodityInfoODTO infoODTO = new ShopCommodityInfoODTO();
			infoODTO.setCommodityId(stock.getCommodityId());
			infoODTO.setStockQuantity(stock.getStockQuantity());
			infoODTO.setStockNumber(stock.getStockNumber());

			xdStockMap.put(stock.getCommodityId(),infoODTO);
		}
		return xdStockMap;
	}

	/**
	 * 获取鲜到库存、份数(库存负数的改成0)
	 * 这里库存加上在途数量
	 * @param shopId
	 * @param commodityList
	 * @return
	 */
	public Map<Long, ShopCommodityInfoODTO> getXdShopCommodityStock2(Long shopId, List<Long> commodityList){
		// 查询门店商品在途数量
		String orderTime = DateUtil.getDateFormate(new Date(),"yyyy-MM-dd");
		List<ShopOrderedQuantityODTO> orderedQuantityList = orderMapper.selectShopOrderQuantity(shopId,commodityList,orderTime);
		Map<Long, ShopOrderedQuantityODTO> orderedQuantityMap = orderedQuantityList.stream().collect(Collectors.toMap(ShopOrderedQuantityODTO::getCommodityId, Function.identity()));


		Map<Long, ShopCommodityInfoODTO> xdStockMap = getXdShopCommodityStock(shopId, commodityList);
		for (Map.Entry<Long, ShopCommodityInfoODTO> entry : xdStockMap.entrySet()) {
			ShopCommodityInfoODTO shopCommodityInfoODTO = entry.getValue();
			if(shopCommodityInfoODTO.getStockQuantity() == null
					|| shopCommodityInfoODTO.getStockQuantity().compareTo(BigDecimal.ZERO) < 0){
				shopCommodityInfoODTO.setStockQuantity(BigDecimal.ZERO);
			}
			// 库存加上在途数量
			ShopOrderedQuantityODTO shopOrderedQuantity = orderedQuantityMap.get(shopCommodityInfoODTO.getCommodityId());
			if(shopOrderedQuantity != null){
				shopCommodityInfoODTO.setStockQuantity(shopCommodityInfoODTO.getStockQuantity().add(shopOrderedQuantity.getQuantity()));
			}
		}
		return xdStockMap;
	}

	/**
	 *无码订货(手持)
	 * @param vo
	 * @return
	 */
	public PageInfo<CommodityResultEntry> findCommodityHandListPage(CommodityListRequestVO vo){
		QYAssert.isTrue(StringUtils.isNotBlank(vo.getStoreId()), "该用户未关联门店及客户 ");

		// 如果查询条件中的供应商id不为空，则先获取供应商下的所有商品id,然后将所有商品id作为in查询条件，将supplierId相等条件转换为商品列表in条件
		if (vo.getSupplerId() != null && vo.getSupplerId().intValue() > 0) {
			// 查询供应商下的所有商品(注意：是所有)
			List<Long> commodityIdList = commoditySupplierClient.queryCommodityIdsByDefaultSupplier(vo.getSupplerId());
			if(SpringUtil.isNotEmpty(commodityIdList)){
				vo.setCommodityIdList(commodityIdList);
			}else{
				PageInfo<CommodityResultEntry> p=new PageInfo<CommodityResultEntry>();
				p.setList(new ArrayList<>());
				return p;
			}

		}
		vo.setOrderTime(DateTimeUtil.defaultDeliveryDate());
		Map<String, PromotionProduct> priceMap = commonService.findPromotionPrice(vo.getStoreId(),DateTimeUtil.defaultDeliveryDate());
		//List<CommodityResultEntry> promotionCommodityCodeList = commonService.findPromotionCommodityCodeList(vo);
		if (vo.isPromotionProduct()){
			if(priceMap.size() > 0){
				List<String> commodityCodeList = priceMap.keySet().stream().collect(Collectors.toList());;
				vo.setCommodityCodeList(commodityCodeList);
			}else{
				PageInfo<CommodityResultEntry> p=new PageInfo<CommodityResultEntry>();
				p.setList(new ArrayList<>());
				return p;
			}
		}

		// 查询门店下面可采购的商品
		Shop shop = shopService.getShopByStoreId(Long.valueOf(vo.getStoreId()));
		vo.setShopId(shop.getId());
		List<Long> commodityPurchaseList = commodityMapper.findShopCommodityPurchaseList(vo);
		if(!CollectionUtils.isEmpty(commodityPurchaseList)){
			vo.setCommodityPurchaseIdList(commodityPurchaseList);
		}else {
			PageInfo<CommodityResultEntry> p=new PageInfo<CommodityResultEntry>();
			p.setList(new ArrayList<>());
			return p;
		}

		// 订货列表，判断是否是档口模式。档口模式下，只显示档口商品
		TokenInfo ti = FastThreadLocalUtil.getQY();
		Boolean isBigShop = StallUtils.isStallSubcontractor(shop.getManagementMode());
		if(isBigShop){
			List<Long> stallCommodityIdList = consignmentSupplierService.getCommodityIdListByStallIds(ti.getShopId(), vo.getStallId());
			if(CollectionUtils.isEmpty(stallCommodityIdList)){
				return null;
			}else {
				vo.setCommodityIdListAll(stallCommodityIdList); // in
			}
		}
		/*// 代销商判断
		TokenInfo tokenInfo = FastThreadLocalUtil.getQY();
		// 非代理进来，拆分数据权限
		if(!tokenInfo.getIsInternal() ){
			if(tokenInfo.getConsignmentId() != null && tokenInfo.getConsignmentId() > 0){
				List<Long> consignmentCommIds = consignmentClient.getCommodityIdListByShopIdAndConsignment(tokenInfo.getShopId(), tokenInfo.getConsignmentId());
				if(CollectionUtils.isEmpty(consignmentCommIds)){
					PageInfo<CommodityResultEntry> p=new PageInfo<CommodityResultEntry>();
					p.setList(new ArrayList<>());
					return p;
				}else {
					vo.setCommodityIdListAll(consignmentCommIds); // in
				}
			}else {
				List<ConsignmentCommodityODTO> consignmentCommodityList = consignmentClient.selectConsignmentCommodityByShopId(tokenInfo.getShopId());
				if(!CollectionUtils.isEmpty(consignmentCommodityList)){
					List<Long> consignmentCommodityIds = consignmentCommodityList.stream().map(item -> item.getCommodityId()).collect(Collectors.toList());
					vo.setCommodityIdList2(consignmentCommodityIds); // not in
				}
			}
		}*/


		// 门店PC订货直接过滤掉 代销的直送商品
		List<ConsignmentSupplierInfoODTO> consignmentSupplierList = consignmentSupplierService.queryConsignmentSupplierList(Collections.singletonList(Long.valueOf(vo.getStoreId())), null);
		if(org.apache.commons.collections.CollectionUtils.isNotEmpty(consignmentSupplierList)){
			Set<Long> idSetList = consignmentSupplierList.stream().map(ConsignmentSupplierInfoODTO::getCommodityId).collect(Collectors.toSet());
			vo.setCommodityIdList2(new ArrayList<>(idSetList)); // not in
		}

		// 调用client 去 qingyun-price 查询
		CommodityListRequestIDTO idto = new CommodityListRequestIDTO();
		BeanUtils.copyProperties(vo,idto);
		PageInfo<CommodityResultODTO> resultPageData = productPriceModelClient.findStoreCommodityListByPage(idto);

		PageInfo<CommodityResultEntry> pageData = BeanUtil.copyProperties(resultPageData,CommodityResultEntry.class);
		this.findCommodityListForShoppingCartByPage(pageData.getList(),shop.getStoreId(),shop.getId(),shop.getShopType(),shop.getManagementMode(),vo.getStallId());
		// 供应商时间，如果商品是配送模式，供应商时间取仓库时间
		setSupplyTime(Long.valueOf(vo.getStoreId()), pageData.getList());

		if(!CollectionUtils.isEmpty(pageData.getList())){
			// 获取自动订货商品
			List<Long> autoCommodityIdList = autoShopCommodityService.getAutoCommodityList(Long.valueOf(vo.getStoreId()));

			List<Long> commodityList = pageData.getList().stream().map(item -> Long.parseLong(item.getCommodityId())).collect(Collectors.toList());
			// 查询B端库存依据
			Map<Long, BigDecimal> orderQuantityMap = new HashMap<>();
			commodityList.forEach(entry->{
				orderQuantityMap.put(entry, BigDecimal.ZERO);
			});
			Map<Long, CommodityInventoryODTO> toBStockMap = bStockService.getbStockMap(DateUtil.parseDate(DateTimeUtil.defaultDeliveryDate(), "yyyy-MM-dd"), orderQuantityMap);

			for(CommodityResultEntry resultEntry: pageData.getList()){
				resultEntry.setAutoCommodity(!CollectionUtils.isEmpty(autoCommodityIdList) && autoCommodityIdList.contains(Long.valueOf(resultEntry.getCommodityId())));

				Boolean isDirect = Integer.valueOf(IogisticsModelEnums.DIRECT_SENDING.getCode()).equals(resultEntry.getLogisticsModel());
				// 判断商品可订货状态
				Boolean betweenNow = DateTimeUtil.compareNewDate(resultEntry.getSupplyStartTime(), resultEntry.getSupplyEndTime());
				resultEntry.setOrderedStatus(!betweenNow ? OrderedStatusEnum.OVER_TIME.getCode() : OrderedStatusEnum.ORDERED.getCode());
				CommodityInventoryODTO commodityInventoryODTO = toBStockMap.get(Long.valueOf(resultEntry.getCommodityId()));
				if(commodityInventoryODTO != null){
					if(StockTypeEnum.WAREHOUSE.getCode().equals(commodityInventoryODTO.getStockType())
							|| StockTypeEnum.LIMIT.getCode().equals(commodityInventoryODTO.getStockType())){
						if(!commodityInventoryODTO.getHaveInventory() && !isDirect){ // 无库存
							resultEntry.setOrderedStatus(OrderedStatusEnum.SOLD_OUT.getCode());
						}
					}
					if(betweenNow && (commodityInventoryODTO.getHaveInventory() || isDirect)){
						resultEntry.setOrderedStatus(OrderedStatusEnum.ORDERED.getCode());
					}
				}
			}
			setCommodityPromotionPrice(pageData,priceMap);
		}
		return pageData;
	}

	/**
	 * 订货列表
	 * @param vo
	 * @return
	 */
	public PageInfo<CommodityResultEntry> findCommodityListByPage(CommodityListRequestVO vo){
		QYAssert.isTrue(StringUtils.isNotBlank(vo.getStoreId()) && vo.getShopId() != null, "该用户未关联门店及客户 ");

		// 查询当前门店商品加购物车数量
		Map<String, BigDecimal> nums =shoppingCartService.getShoppingCartItemNums(Long.valueOf(vo.getStoreId()), vo.getStallId());

		// 获取某门店的所有特价商品信息列表
		vo.setOrderTime(DateTimeUtil.defaultDeliveryDate());
		Map<String, PromotionProduct> priceMap = commonService.findPromotionPrice(vo.getStoreId(),DateTimeUtil.defaultDeliveryDate());
		if (vo.isPromotionProduct()) {
			if(priceMap.size() > 0){
				List<String> commodityCodeList = priceMap.keySet().stream().collect(Collectors.toList());
				// 此commodityCodeList 放入 查询门店下面可采购的商品 commodityMapper.findShopCommodityPurchaseList(vo)
				vo.setCommodityCodeList(commodityCodeList);
			}else {
				return null;
			}
		}

		List<Long> commodityIdListAll = new ArrayList<>();
		List<Long> notCommodityIdListAll = new ArrayList<>();

		// 查询门店下面可采购的商品
		Shop shop = shopService.getShopByStoreId(Long.valueOf(vo.getStoreId()));
		vo.setShopId(shop.getId());
		List<Long> commodityPurchaseList = commodityMapper.findShopCommodityPurchaseList(vo);
		if(!CollectionUtils.isEmpty(commodityPurchaseList)){
			commodityIdListAll.addAll(commodityPurchaseList);
		}else {
			return null;
		}

		// 订货列表，判断是否是档口模式。档口模式下，只显示档口商品
		TokenInfo ti = FastThreadLocalUtil.getQY();
		if(StallUtils.isStallSubcontractor(ti.getManagementMode())){
			List<Long> stallCommodityIdList = consignmentSupplierService.getCommodityIdListByStallIds(ti.getShopId(), vo.getStallId());
			if(CollectionUtils.isEmpty(stallCommodityIdList)){
				return null;
			}else {
				commodityIdListAll.retainAll(stallCommodityIdList);
			}
		}

		List<Long> autoCommodityIdList = autoShopCommodityService.getAutoCommodityList(Long.valueOf(vo.getStoreId()));
		// 如果是自动订货
		if(vo.getAutoCommodity() != null){
			if(vo.getAutoCommodity()){
				if(CollectionUtils.isEmpty(autoCommodityIdList)){
					return null;
				}else {
					commodityIdListAll.retainAll(autoCommodityIdList);
				}
			}else {
				if(!CollectionUtils.isEmpty(autoCommodityIdList)){
					notCommodityIdListAll.addAll(autoCommodityIdList);
				}
			}
		}

		// 仅日日鲜商品
		if(vo.isFreshCommodity()){
			List<Long> freshCommodityIdList = commodityEverydayFreshClient.getAllEverydayFresh();
			if(CollectionUtils.isEmpty(freshCommodityIdList)){
				return null;
			}else {
				commodityIdListAll.retainAll(freshCommodityIdList);
			}
		}

		//是否常购商品
		if(vo.isOftenBuyProduct()){
			String[] last31Days = OrderTimeUtil.last31DaysRange();
			List<StoreOftenBuyProductDTO> storeOftenBuyProductDTOList = orderMapper.queryStoreOftenBuyProductDTO(Long.valueOf(vo.getStoreId()),last31Days[0],last31Days[1]);
			if(CollectionUtils.isEmpty(storeOftenBuyProductDTOList)){
				return null;
			}else {
				commodityIdListAll.retainAll(storeOftenBuyProductDTOList.stream().map(StoreOftenBuyProductDTO::getCommodityId).collect(Collectors.toList()));
			}
		}

		// 商品上下架
		if(vo.getAppStatus() != null){
			XsShopCommodityAppStatusIDTO xsShopCommodityAppStatusIDTO = new XsShopCommodityAppStatusIDTO();
			xsShopCommodityAppStatusIDTO.setShopId(vo.getShopId());
			xsShopCommodityAppStatusIDTO.setAppStatus(vo.getAppStatus());
			List<XsShopCommodityAppStatusODTO> appStatusList = shopCommodityUpDownManagerClient.selectXsShopCommodityAppStatus(xsShopCommodityAppStatusIDTO);
		    if(CollectionUtils.isEmpty(appStatusList)){
				return null;
			}else {
				List<Long> appStatusCommodityIdList = appStatusList.stream().map(item -> item.getCommodityId()).collect(Collectors.toList());
				commodityIdListAll.retainAll(appStatusCommodityIdList);
			}
		}

		// 如果查询条件中的供应商id不为空，则先获取供应商下的所有商品id,然后将所有商品id作为in查询条件，将supplierId相等条件转换为商品列表in条件
        List<Long> commodityIdList = null;
		if (vo.getSupplerId() != null && vo.getSupplerId().intValue() > 0) {
			// 查询供应商下的所有商品(注意：是所有)
			commodityIdList = commoditySupplierClient.queryCommodityIdsByDefaultSupplier(vo.getSupplerId());
			if(!CollectionUtils.isEmpty(commodityIdList)){
				commodityIdListAll.retainAll(commodityIdList);
			}else {
				return null;
			}
		}


		//如果查询条件中的仓库id不为空，则先获取仓库下的所有商品id,然后将所有商品id作为in查询条件，将warehouseid相等条件转换为商品列表in条件
		if (vo.getWarehouseId() != null && vo.getWarehouseId().intValue() > 0) {
			// 查询仓库下的所有商品(注意：是所有)
			List<Long> commodityIdList2 = commodityWarehouseClient.queryCommodityIdsByDefaultWarehouseid(vo.getWarehouseId());
			if(!CollectionUtils.isEmpty(commodityIdList2)){
				commodityIdListAll.retainAll(commodityIdList2);
			}else {
				return null;
			}
		}

		// 如果物流模式不为空
		if (vo.getLogisticsModel() != null) {
			Integer shopType = shopService.getShopType(Long.valueOf(vo.getStoreId()));
			List<Long> commodityIdList3 = mdShopOrderSettingMapper.queryCommodityIdsByStoreLogisticsModel(shopType,vo.getLogisticsModel());
			if(!CollectionUtils.isEmpty(commodityIdList3)){
				commodityIdListAll.retainAll(commodityIdList3);
			}else {
				return null;
			}
		}

		// 如果是新品
		if(YesOrNoEnums.YES.getCode().equals(vo.getNewProduct())) {
			// 查询新品List
			List<Long> newCommodityIdList = commodityMapper.queryNewCommodityIdList(DateUtil.get4yMd(new Date()), null);
			if(!CollectionUtils.isEmpty(newCommodityIdList)){
				commodityIdListAll.retainAll(newCommodityIdList);
			}else {
				return null;
			}
		}

		// 如果选中仅显示数量为0
		if(vo.isShowZero()){
			List<Long> commodityIdZeroList = new ArrayList<>();
			for(String key : nums.keySet()){
				BigDecimal value = nums.get(key);
				if(value.compareTo(BigDecimal.ZERO) > 0){
					commodityIdZeroList.add(Long.valueOf(key));
				}
			}
			notCommodityIdListAll.addAll(commodityIdZeroList);
		}

		// 门店PC订货直接过滤掉 代销的直送商品
		List<ConsignmentSupplierInfoODTO> consignmentSupplierList = consignmentSupplierService.queryConsignmentSupplierList(Collections.singletonList(Long.valueOf(vo.getStoreId())), null);
		if(org.apache.commons.collections.CollectionUtils.isNotEmpty(consignmentSupplierList)){
			Set<Long> idSetList = consignmentSupplierList.stream().map(ConsignmentSupplierInfoODTO::getCommodityId).collect(Collectors.toSet());
			notCommodityIdListAll.addAll(new ArrayList<>(idSetList));
		}

		vo.setCommodityIdList2(notCommodityIdListAll); // commodityIdList notIn

		if(CollectionUtils.isEmpty(commodityIdListAll)){
			return null;
		}else {
			vo.setCommodityIdListAll(commodityIdListAll); // commodityIdList in
		}

        if (vo.getSupplerId() != null && vo.getSupplerId().intValue() > 0 && vo.isPromotionProduct()) {
            if (SpringUtil.isEmpty(commodityIdList) && priceMap.size() == 0) {
                return null;
            }
        }

        if(vo.getCheckGroupId() != null){
        	List<Long> checkGroupCommodityIdList = mdCheckReportClient.getMdCheckCommodityByParam(Long.valueOf(vo.getStoreId()),vo.getCheckGroupId());
        	vo.setCheckGroupCommodityIdList(checkGroupCommodityIdList);
		}

		PageInfo<CommodityResultEntry> pageData;
		// 调用client 去 qingyun-price 查询
		CommodityListRequestIDTO idto = new CommodityListRequestIDTO();
		BeanUtils.copyProperties(vo,idto);

		// 根据shopId查询门店必售商品
		List<MustSellCommodityODTO> sellCommodityList = mdShopMustSellCommodityClient.getCommodityByShopId(vo.getShopId());
		if(!CollectionUtils.isEmpty(sellCommodityList)){
			idto.setPageNo(1);
			idto.setPageSize(Integer.MAX_VALUE);
			PageInfo<CommodityResultODTO> resultPageData = productPriceModelClient.findStoreCommodityListByPage(idto);
			if(CollectionUtils.isEmpty(resultPageData.getList())){
				return null;
			}
			List<CommodityResultEntry> resultEntries = BeanCloneUtils.copyTo(resultPageData.getList(), CommodityResultEntry.class);
			setOrderPropertyType(sellCommodityList, resultEntries);

			// 排序
			resultEntries = resultEntries.stream().sorted(Comparator.comparing(CommodityResultEntry::getOrderPropertyType).thenComparing(CommodityResultEntry::getCommodityIdLong)).collect(Collectors.toList());
			pageData = ListToPageInfoUtil.convert(resultEntries, vo.getPageSize(), vo.getPageNo());
		}else {
			PageInfo<CommodityResultODTO> resultPageData = productPriceModelClient.findStoreCommodityListByPage(idto);
			pageData = BeanUtil.copyProperties(resultPageData,CommodityResultEntry.class);
		}

		// 获取库存、上下架信息
        this.findCommodityListForShoppingCartByPage(pageData.getList(),shop.getStoreId(),shop.getId(),shop.getShopType(),shop.getManagementMode(), vo.getStallId());

		// 设置商品的特价
		this.setCommodityPromotionPrice(pageData,  priceMap);
		this.processCommodityLimit(vo.getStoreId(), pageData);
//		this.processCommodityLogisticsModel(vo.getStoreId(),pageData);

		// 渲染近15天日均销量、当日销售数量、近15天毛利率
		orderReferenceService.renderShopOrderingMetrics(vo.getShopId(), pageData);
		// 配送取仓库时间
		this.setSupplyTime(Long.valueOf(vo.getStoreId()), pageData.getList());

		List<CommodityResultEntry> list = pageData.getList();
		if(null !=list && !list.isEmpty()){
			List<Long> commodityIdListResult = list.stream().map(item -> Long.valueOf(item.getCommodityId())).collect(Collectors.toList());

			// 查询门店商品在途数量
			String orderTime = DateUtil.getDateFormate(new Date(),"yyyy-MM-dd");
			List<ShopOrderedQuantityODTO> orderedQuantityList = orderMapper.selectShopOrderQuantity(shop.getId(),commodityIdListResult,orderTime);
			Map<Long, ShopOrderedQuantityODTO> orderedQuantityMap = orderedQuantityList.stream().collect(Collectors.toMap(ShopOrderedQuantityODTO::getCommodityId, Function.identity()));

			List<Long> idList = list.stream().map(item -> Long.valueOf(item.getCommodityId())).collect(Collectors.toList());
			Map<Long,String> barCodeMap = commonService.getCommodityBarCodeMap(idList,null);

			// 查询B端库存依据
			Map<Long, BigDecimal> orderQuantityMap = new HashMap<>();
			commodityIdListResult.forEach(entry->{
				orderQuantityMap.put(entry, BigDecimal.ZERO);
			});
			Map<Long, CommodityInventoryODTO> toBStockMap = bStockService.getbStockMap(DateUtil.parseDate(DateTimeUtil.defaultDeliveryDate(), "yyyy-MM-dd"), orderQuantityMap);

			// 查询新品
			List<Long> newCommodityIdList = commodityMapper.queryNewCommodityIdList(DateUtil.get4yMd(new Date()), commodityIdListResult);
			Map<Long, Long> newCommodityMap;
			if(!CollectionUtils.isEmpty(newCommodityIdList)) {
				newCommodityMap = newCommodityIdList.stream().collect(Collectors.toMap(item->item, item->item));
			} else {
                newCommodityMap = new HashMap<>();
            }

            list.forEach(entry->{
				if(nums.containsKey(entry.getCommodityId())){
					entry.setCommodityNumber(nums.get(entry.getCommodityId()));
					entry.setCommodityShares(entry.getCommodityNumber().divide(entry.getCommodityPackageSpec(),0,BigDecimal.ROUND_UP));
				}
				String barCodes = barCodeMap.get(Long.valueOf(entry.getCommodityId()));
				entry.setBarCodeList(barCodes);
				entry.setBarCode(barCodes != null ? barCodes.split(",")[0]:"");

				entry.setAutoCommodity(!CollectionUtils.isEmpty(autoCommodityIdList) && autoCommodityIdList.contains(Long.valueOf(entry.getCommodityId())));
				ShopOrderedQuantityODTO orderedQuantityODTO = orderedQuantityMap.get(Long.valueOf(entry.getCommodityId()));
				if(orderedQuantityODTO != null){
					entry.setOrderedQuantity(orderedQuantityODTO.getQuantity());
				}

				// 判断商品可订货状态
				Boolean betweenNow = DateTimeUtil.compareNewDate(entry.getSupplyStartTime(), entry.getSupplyEndTime());
				entry.setOrderedStatus(!betweenNow ? OrderedStatusEnum.OVER_TIME.getCode() : OrderedStatusEnum.ORDERED.getCode());
				CommodityInventoryODTO commodityInventoryODTO = toBStockMap.get(Long.valueOf(entry.getCommodityId()));
				if(commodityInventoryODTO != null){
					if(StockTypeEnum.WAREHOUSE.getCode().equals(commodityInventoryODTO.getStockType())
						|| StockTypeEnum.LIMIT.getCode().equals(commodityInventoryODTO.getStockType())){
						if(!commodityInventoryODTO.getHaveInventory()){ // 无库存
							entry.setOrderedStatus(OrderedStatusEnum.SOLD_OUT.getCode());
						}
					}
					if (betweenNow && Objects.nonNull(commodityInventoryODTO.getHaveInventory()) && commodityInventoryODTO.getHaveInventory()) {
						entry.setOrderedStatus(OrderedStatusEnum.ORDERED.getCode());
					}
				}

				// 设置是否新品
				entry.setNewProduct(newCommodityMap.containsKey(Long.valueOf(entry.getCommodityId())));
			});
		}
		return pageData ;
	}


	private void setOrderPropertyType(List<MustSellCommodityODTO> sellCommodityList, List<CommodityResultEntry> resultEntries) {
		Integer shopSize = sellCommodityList.get(0).getGrade(); //门店货架大小 1大店  2 中店  3 小店
		Map<Long, MustSellCommodityODTO> sellCommodityMap = sellCommodityList.stream().collect(Collectors.toMap(MustSellCommodityODTO::getCommodityId, Function.identity()));
		for(CommodityResultEntry entry : resultEntries){
			MustSellCommodityODTO mustSellCommodityODTO = sellCommodityMap.get(Long.valueOf(entry.getCommodityId()));
			if(mustSellCommodityODTO != null){
				// 必售标志 sellType 1 大店必售 2 大中店必售  3 全部必售
				if(shopSize == ShopGradeEnum.BIG.getCode()){
					if(mustSellCommodityODTO.getType()== MustSellCommodityTypeEnum.BIGSELL.getCode()
							|| mustSellCommodityODTO.getType() == MustSellCommodityTypeEnum.MIDSELL.getCode()
							|| mustSellCommodityODTO.getType() == MustSellCommodityTypeEnum.ALL.getCode()){
						entry.setOrderPropertyType(OrderPropertyEnum.SELL_MUST.getCode());
					}
				}else if(shopSize == ShopGradeEnum.MIDDLE.getCode()){
					if(mustSellCommodityODTO.getType() == MustSellCommodityTypeEnum.MIDSELL.getCode()
							|| mustSellCommodityODTO.getType() == MustSellCommodityTypeEnum.ALL.getCode()){
						entry.setOrderPropertyType(OrderPropertyEnum.SELL_MUST.getCode());
					}
					if(mustSellCommodityODTO.getType() == MustSellCommodityTypeEnum.BIGSELL.getCode()){
						entry.setOrderPropertyType(OrderPropertyEnum.SELL_FIRST.getCode());
					}
				}else {
					if(mustSellCommodityODTO.getType() == MustSellCommodityTypeEnum.ALL.getCode()){
						entry.setOrderPropertyType(OrderPropertyEnum.SELL_MUST.getCode());
					}
					if(mustSellCommodityODTO.getType() == MustSellCommodityTypeEnum.BIGSELL.getCode()
							|| mustSellCommodityODTO.getType() == MustSellCommodityTypeEnum.MIDSELL.getCode()){
						entry.setOrderPropertyType(OrderPropertyEnum.SELL_FIRST.getCode());
					}
				}
			}else {
				entry.setOrderPropertyType(OrderPropertyEnum.SELL_SECOND.getCode());
			}
		}
	}

	/**
	 * 直通直送取默认供应商时间(t_dc_commodity_default_supplier)
	 * 配送取仓库时间(t_dc_commodity_default_warehouse)
	 * @param list
	 * @param storeId
	 */
	public <T extends SupplyBaseEntry> void setSupplyTime(Long storeId, List<T> list) {
		if (SpringUtil.isEmpty(list)) {
			return;
		}

		List<String> commodityIds = list.stream().map(item -> item.getProductId()).collect(Collectors.toList());
		Map<Long, MdShopOrderSettingEntry> commodityIdAndSettingMap = mdShopOrderSettingService.getCommodityIdAndSettingMap(storeId, commodityIds);

		list.forEach(item -> {
			MdShopOrderSettingEntry entry = commodityIdAndSettingMap.get(Long.valueOf(item.getProductId()));
			if (entry != null) {
				item.setSupplierId(Long.valueOf(entry.getSupplierId()));
				item.setLogisticsModel(entry.getLogisticsModel().intValue());
				if (entry.getLogisticsModel().intValue() == IogisticsModelEnums.DISPATCHING.getCode()) {
					item.setSupplyStartTime(entry.getDefaultWarehouseBeginTime());
					item.setSupplyEndTime(entry.getDefaultWarehouseEndTime());
				} else {
					item.setSupplyStartTime(entry.getDefaultSupplierBeginTime());
					item.setSupplyEndTime(entry.getDefaultSupplierEndTime());
				}
			}
		});
	}


	private void setCommodityPromotionPrice(PageInfo<CommodityResultEntry> pageData, Map<String, PromotionProduct> priceMap) {
        if (priceMap.size() == 0) {
            return;
        }

        pageData.getList().forEach(item-> {
            String commodityCode = item.getCommodityCode();
			PromotionProduct promotionProduct = priceMap.get(commodityCode);
            if (promotionProduct != null) {
				item.setPromotionProduct(true);
                item.setCommodityPrice(new BigDecimal(promotionProduct.getPrice()));
				item.setPromotionProduct(true);
				item.setStartTime(promotionProduct.getStartTime());
				item.setEndTime(promotionProduct.getEndTime());
            }
        });
    }


	public String findStoreIdBySubOrderCode(String subOrderCode){
		return commodityMapper.findStoreIdBySubOrderCode(subOrderCode);
	}

	
	private void processCommodityLimit(String storeId,PageInfo<CommodityResultEntry> pageDate){
		if(StringUtils.isNotBlank(storeId) && SpringUtil.isNotEmpty(pageDate.getList())){
			List<ProductLimitEntry> limitProductList = this.findLimitProductByStoreId(storeId);
			if(SpringUtil.isNotEmpty(limitProductList)){
				limitProductList.forEach(l->{
					pageDate.getList().forEach(c->{
						if(l.getCommodityId().equals(c.getCommodityId())){
							if(null == c.getCommodityNumberLimit() || c.getCommodityNumberLimit().compareTo(BigDecimal.ZERO) < 0 ){
								c.setCommodityNumberLimit(l.getLimitNumber());
							}
						}
					});
				});
			}
		}
	}
	
	
    private List<ProductLimitEntry> findLimitProductByStoreId(String storeId){
    	
    	List<ProductLimitEntry> resultDto = new ArrayList<ProductLimitEntry>();
    	
    	if(StringUtils.isBlank(storeId)){
    		return null;
    	}
		
		String productPriceModelId = commonService.findProductPriceModelIdByStoreId(storeId);
				
		List<ProductLimitEntry> customerLimitProductList = commonService.findCustomerLimitByStoreId(storeId);
		
		List<ProductLimitEntry> priceModelLimitProductList = new ArrayList<ProductLimitEntry>();
		
		if(null != productPriceModelId){
			priceModelLimitProductList = commonService.findProductPriceModelLimitByPriceModelId(productPriceModelId);
		}
		
		if(SpringUtil.isNotEmpty(priceModelLimitProductList)){
			priceModelLimitProductList.forEach(p->{
				ProductLimitEntry dto = new ProductLimitEntry();
				dto.setCommodityId(p.getCommodityId());
				dto.setCommodityName(p.getCommodityName());
				dto.setLimitNumber(p.getLimitNumber());
				resultDto.add(dto);
			});
		}
		
		if(SpringUtil.isNotEmpty(customerLimitProductList)){
			if(SpringUtil.isEmpty(resultDto)){
				customerLimitProductList.forEach(c->{
					ProductLimitEntry dto = new ProductLimitEntry();
					dto.setCommodityId(c.getCommodityId());
					dto.setCommodityName(c.getCommodityName());
					dto.setLimitNumber(c.getLimitNumber());
					resultDto.add(dto);
				});
			}else{
				List<ProductLimitEntry> existProductList = customerLimitProductList.stream().filter(c->{
					return resultDto.stream().anyMatch(i->{
						return i.getCommodityId().equals(c.getCommodityId());
					});
				}).collect(Collectors.toList());
				
				if(SpringUtil.isNotEmpty(existProductList)){
					existProductList.forEach(p->{
						resultDto.forEach(i->{
							if(i.getCommodityId().equals(p.getCommodityId())){
								i.setLimitNumber(p.getLimitNumber());
							}
						});
					});
				}
				
				List<ProductLimitEntry> noExistProductList = customerLimitProductList.stream().filter(c->{
					return !resultDto.stream().anyMatch(i->{
						return i.getCommodityId().equals(c.getCommodityId());
					});
				}).collect(Collectors.toList());
				
				if(SpringUtil.isNotEmpty(noExistProductList)){
					noExistProductList.forEach(p->{
						ProductLimitEntry dto = new ProductLimitEntry();
						dto.setCommodityId(p.getCommodityId());
						dto.setCommodityName(p.getCommodityName());
						dto.setLimitNumber(p.getLimitNumber());
						resultDto.add(dto);
					});
				}
			}
		}
    	return resultDto;
    }
    
    public List<PreCommodityEntry> findPreCommodityByParam(PreCommodityRequestVo vo){
		List<PreCommodityEntry> list = new ArrayList<>();
		//预订单审核传preOrderCode,获取该预订单的门店的storeId
		if(StringUtils.isNotBlank(vo.getPreOrderCode())){
			Example example = new Example(PreOrder.class);
			example.createCriteria().andEqualTo("orderCode", vo.getPreOrderCode());
			List<PreOrder> preOrderList = preOrderMapper.selectByExample(example);

			QYAssert.isTrue(SpringUtil.isNotEmpty(preOrderList), "该订单异常");
			PreOrder preOrder = preOrderList.get(0);
			vo.setStoreId(preOrder.getStoreId());
			vo.setSupplierId(preOrder.getSupplierId());
		}

		/*// 如果查询条件中的供应商id不为空，则先获取供应商下的所有商品id,然后将所有商品id作为in查询条件，将供应商id条件转换为商品列表条件
		if (vo.getSupplierId() != null && vo.getSupplierId().intValue() > 0) {
			List<Long> commodityIdList = commoditySupplierClient.queryCommodityIdsByDefaultSupplier(vo.getSupplierId());
			vo.setCommodityIdList(commodityIdList);
		}*/
		CommodityListRequestIDTO idto = new CommodityListRequestIDTO();
		idto.setStoreId(vo.getStoreId() + "");

		if(StringUtils.isNotBlank(vo.getCommodityKey())){
			CommodityVO commodityVO = new CommodityVO();
			commodityVO.setCommodityKey(vo.getCommodityKey());
			List<CommodityBasicEntry> commList = commodityMapper.findCommodityBasicListByParam(commodityVO);
			if(!CollectionUtils.isEmpty(commList)){
				List<Long> commodityIdList = commList.stream().map(item -> Long.valueOf(item.getCommodityId())).collect(Collectors.toList());
				idto.setCommodityIdListAll(commodityIdList);
			}else {
				return new ArrayList<>();
			}
		}

		Integer shopType = shopService.getShopType(vo.getStoreId());
		List<Long> directCommodityList = mdShopOrderSettingMapper.findDirectCommodityList(shopType, vo.getSupplierId());
		if(!CollectionUtils.isEmpty(directCommodityList)){
			idto.setCommodityPurchaseIdList(directCommodityList);
		}else {
			return new ArrayList<>();
		}
		List<CommodityResultODTO> commodityResultList = productPriceModelClient.findStoreCommodityList(idto);
		if(SpringUtil.isNotEmpty(commodityResultList)){
			for(CommodityResultODTO commodityResult : commodityResultList){
				PreCommodityEntry preCommodityEntry = new PreCommodityEntry();
				BeanUtils.copyProperties(commodityResult,preCommodityEntry);
				preCommodityEntry.setReferencePrice(commodityResult.getCommodityPrice());
				list.add(preCommodityEntry);
			}

			// 收货审核/新增商品(一次只返回100条)
			if(list.size() > 100){
				list = list.subList(0,90);
			}
			this.processPreCommodityPromotionPrice(vo.getStoreId().toString(), list, vo.getOrderTime());
			list.forEach(i->{
				i.setReferencePrice( i.getReferencePrice().compareTo(BigDecimal.ZERO) >0 ? i.getReferencePrice() : null);
			});
		}
		//List<PreCommodityEntry> list = this.commodityMapper.findPreCommodityByParam(vo);
    	return list;
    }
    
    
	private void processPreCommodityPromotionPrice(String storeId, List<PreCommodityEntry> list ,String orderTime){
		if(SpringUtil.isNotEmpty(list) && StringUtils.isNotBlank(storeId) && StringUtils.isNotBlank(orderTime)){
    		List<Promotion> promotionList = commonService.findCommodityPromotionByStoreId(storeId,orderTime);
    		if(SpringUtil.isNotEmpty(promotionList)){
    			promotionList.forEach(p->{ //多特价
        			List<PromotionProduct> promotionProductList = commonService.findPromotionProductByPromotionId(p.getId().toString());
        			if(SpringUtil.isNotEmpty(promotionProductList)){
        				promotionProductList.forEach(pp->{
        					list.forEach(c->{
        						if(c.getCommodityCode().equals(pp.getProductCode())){
        							c.setCommodityPrice(BigDecimal.valueOf(pp.getPrice()));
        						}
        					});
        				});
        			}
    			});
    		}
		}
	}

	/**
	 *pos折扣码查询使用
	 * @param barCode
	 * @param storeId
	 * @return
	 */
    public CouponCodeQueryODTO couponQuery(String barCode, Long storeId) {
//		CouponCodeQueryEntry result = commodityMapper.couponQuery(barCode, storeId);

		CouponCodeQueryODTO odto = shopCommodityClient.couponQuery(barCode, storeId);
		if (odto != null) {
			// 获取进货价
			CommodityListRequestIDTO idto = new CommodityListRequestIDTO();
			idto.setStoreId(storeId + "");
			idto.setBarCode(barCode);
			List<CommodityResultODTO> commodityResultList = productPriceModelClient.findStoreCommodityList(idto);
			if(!CollectionUtils.isEmpty(commodityResultList)){
				odto.setCommodityPrice(commodityResultList.get(0).getCommodityPrice());
			}
			/*CouponCodeQueryODTO priceODTO = commodityMapper.getCommodityPrice(barCode, storeId);
			if(null != priceODTO){
				odto.setCommodityPrice(priceODTO.getCommodityPrice());
			}*/
		}

		return odto;
    }


	/**
	 * 鲜食加盟订货参考
	 * @param storeId
	 * @param shopId
	 * @param commodityId
	 * @return
	 */
	public OrderReferenceXsjmDetailODTO xsjmOrderReference(Long storeId, Long shopId, Long commodityId) {
		Boolean isXsJmShop = shopService.isJmShop(storeId);
		if(!isXsJmShop) {
			return null;
		}

		// 查询商品信息
		XDCommodityODTO commodityODTO = commodityMapper.getXDCommodityODTOById(commodityId);
		QYAssert.isTrue(commodityODTO != null, "商品不存在");

		OrderReferenceXsjmDetailODTO referenceODTO = BeanCloneUtils.copyTo(commodityODTO, OrderReferenceXsjmDetailODTO.class);

		// 收货价：送货日期等于当前日期的订单且已收货的订单的收货价（正常品，不算赠品）（一个商品存在多个价格时，任意取一个）
		//如果商品没存在收货，则显示为空
		BigDecimal receivePrice = orderMapper.queryOrderPrice(storeId, commodityId, DateUtil.get4yMd(new Date()));
		referenceODTO.setReceivePrice(receivePrice);

		XsjmOrderReferenceQueryIDTO idto = new XsjmOrderReferenceQueryIDTO();
		idto.setShopId(shopId);
		idto.setCommodityId(commodityId);
		List<XsjmOrderReferenceODTO> referenceODTOList = new ArrayList<>();
		try {
			// 1分钟内报错2次，30分钟内不再调用
			if(orderReferenceCheckService.enableOrderReference()) {
				referenceODTOList = orderReferenceClient.orderReference(idto);
			}
		}catch (Exception e){
			log.warn("日日鲜订货参考，调用大数据异常", e);
			return null;
		}

		if(!CollectionUtils.isEmpty(referenceODTOList)) {
			List<OrderReferenceXsjmDetailODTO.ReferenceDetailODTO> referenceDetailList = BeanCloneUtils.copyTo(referenceODTOList, OrderReferenceXsjmDetailODTO.ReferenceDetailODTO.class);
			referenceODTO.setReferenceDetailList(referenceDetailList);

			//  行列转换
			List list1 = OrderUtil.convert(OrderReferenceXsjmDetailODTO.ReferenceDetailODTO.class,referenceDetailList);
			referenceODTO.setList1(list1);

		}

		return  referenceODTO;
	}

	/**
	 * 获取门店库存
	 */
	public Map<Long, ShopCommodityInfoODTO> getShopStock(Shop shop, Long stallId, List<Long> commodityIds) {
		if (StallUtils.isStallSubcontractor(shop.getManagementMode())) {
			return getBigShopCommodityStock(shop.getId(), stallId, commodityIds, false);
		}
		return getXdShopCommodityStock(shop.getId(), commodityIds);
	}
}
