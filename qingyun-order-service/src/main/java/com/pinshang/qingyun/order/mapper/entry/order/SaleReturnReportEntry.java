package com.pinshang.qingyun.order.mapper.entry.order;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Data
public class SaleReturnReportEntry {
    private Long id; //退货明细表自增ID
    private Long enterpriseId;
    private String storeCode;
    private String storeName;
    private String orderCode;
    private Date createTime;
    private Date updateTime;
    private Long commodityId;
    private String commodityCode;
    private String barCode;
    private String commodityName;
    private String commoditySpec;
    private BigDecimal returnQuantity;
    private BigDecimal realReturnQuantity;
    private BigDecimal price;
    private BigDecimal totalPrice;
    private String barCodes;	// 子码列表
    private String shopName;
    private String returnReasonName;
    private Integer rpType;

    private Integer returnReason;

    public String getReturnReasonName() {
        if(null != returnReason){
            if(returnReason.equals(1)){
                return  "临保";
            }else if(returnReason.equals(2)){
                return "过保";
            }else if(returnReason.equals(3)){
                return "破损";
            }else if(returnReason.equals(4)){
                return "丢失";
            }else if(returnReason.equals(5)){
                return "损坏";
            }else {
                return returnReasonName;
            }
        }
        return "";
    }
}
