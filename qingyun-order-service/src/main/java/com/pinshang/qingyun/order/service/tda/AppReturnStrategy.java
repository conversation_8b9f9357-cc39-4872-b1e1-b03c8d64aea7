/*
 * tramy.cn Inc.
 * Copyright (c) 2021-2024 All Rights Reserved.
 */
package com.pinshang.qingyun.order.service.tda;

import com.pinshang.qingyun.base.configure.expand.QYAssert;
import com.pinshang.qingyun.base.enums.tms.TmsBusinessTypeEnums;
import com.pinshang.qingyun.box.utils.DateUtil;
import com.pinshang.qingyun.order.dto.xda.tda.SaveReturnOrderItemDTO;
import com.pinshang.qingyun.order.dto.xda.tda.SaveReturnOrderODTO;
import com.pinshang.qingyun.order.enums.ComplaintTypeEnum;
import com.pinshang.qingyun.order.enums.tda.TDaReturnOrderTypeEnum;
import com.pinshang.qingyun.order.enums.tda.TDaReturnSourceEnum;
import com.pinshang.qingyun.order.enums.tda.TDaReturnTypeEnum;
import com.pinshang.qingyun.order.mapper.XdaReturnOrderItemMapper;
import com.pinshang.qingyun.order.mapper.XdaReturnOrderItemPicMapper;
import com.pinshang.qingyun.order.mapper.XdaReturnOrderMapper;
import com.pinshang.qingyun.order.model.commodity.Commodity;
import com.pinshang.qingyun.order.model.store.Store;
import com.pinshang.qingyun.order.model.xda.XdaReturnOrder;
import com.pinshang.qingyun.order.model.xda.XdaReturnOrderItem;
import com.pinshang.qingyun.order.model.xda.XdaReturnOrderItemPic;
import com.pinshang.qingyun.order.service.CommodityService;
import com.pinshang.qingyun.order.service.StoreService;
import com.pinshang.qingyun.order.service.tda.factory.ReturnOrderTemplate;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.entity.Example;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

import static com.pinshang.qingyun.order.service.xda.XdaComplaintOrderService.getUUID;

/**
 * <p>
 * app客户申请
 * </p>
 *
 * <AUTHOR> shenyang
 * @version : 1.0.0
 * @history : modify history
 * <author>              <time>              <version>              <desc>
 * @since : 2024/05/17 23:09
 */
@Component
public class AppReturnStrategy extends ReturnOrderTemplate implements ReturnOrderStrategy {
    @Autowired
    private StoreService storeService;
    @Autowired
    private CommodityService commodityService;
    @Autowired
    private ApplicationContext applicationContext;
    @Autowired
    private XdaReturnOrderMapper xdaReturnOrderMapper;
    @Autowired
    private XdaReturnOrderItemMapper xdaReturnOrderItemMapper;
    @Autowired
    private XdaReturnOrderItemPicMapper xdaReturnOrderItemPicMapper;

    @Override
    public Boolean execute(SaveReturnOrderODTO saveReturnOrderDTO) {
        AppReturnStrategy contextBean = applicationContext.getBean(AppReturnStrategy.class);
        super.buildIsWeight(saveReturnOrderDTO);
        return contextBean.saveReturnOrder(saveReturnOrderDTO);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean saveReturnOrder(SaveReturnOrderODTO saveReturnOrderDTO) {
        AtomicReference<Boolean> result = new AtomicReference<>(false);
        Long storeId = saveReturnOrderDTO.getStoreId();
        Store store = storeService.findStoreByStoreId(storeId);
        QYAssert.isTrue(!(storeId == null || store == null), "客户未登录！");

        if (!Objects.equals(store.getBusinessType(), TmsBusinessTypeEnums.TD_SALE.getCode())) {
            //不是通达销售并且是app发起的，返回null，走原投诉逻辑
            return null;
        }

        for (SaveReturnOrderItemDTO itemDTO : saveReturnOrderDTO.getComplaintItemList()) {
            if (Objects.equals(itemDTO.getIsWeight(), 1)) {
                if (Objects.equals(itemDTO.getComplaintType(), 2)) {
                    //称重品退货数量必须等于实发数量  -1，1
                    QYAssert.isTrue(itemDTO.getRealReturnQuantity().abs().compareTo(itemDTO.getRealDeliveryQuantity()) == 0, "称重品退货数量必须等于实发数量！");
                } else {
                    //称重商品差异投诉 实收+实发=0，-1+1=0
                    QYAssert.isTrue(BigDecimal.ZERO.compareTo(itemDTO.getRealReturnQuantity().add(itemDTO.getRealDeliveryQuantity())) == 0, "称重商品差异投诉实收数量必须为0！");
                }
            }
        }

        Integer returnOrderType = (ComplaintTypeEnum.COMMON.getCode() == saveReturnOrderDTO.getComplaintType()) ?
                TDaReturnOrderTypeEnum.SHORTAGE.getCategoryCode() :
                TDaReturnOrderTypeEnum.RETURN.getCategoryCode();

        Example example = new Example(XdaReturnOrder.class);
        example.createCriteria()
                .andEqualTo("returnSource", 1)
                .andNotEqualTo("status", 5)
                .andEqualTo("deliveryTime", DateUtil.parseDate(saveReturnOrderDTO.getDeliveryDate(), "yyyy-MM-dd"))
                .andEqualTo("storeId", storeId);
        List<XdaReturnOrder> xdaReturnOrders = xdaReturnOrderMapper.selectByExample(example);
        //校验是否存在退货单
        if (CollectionUtils.isNotEmpty(xdaReturnOrders)) {
            //如果是走的更新插入退货明细，则不用走创建退货单逻辑
            boolean foundOrderToUpdate;
            //客户发起的是退货单
            if (Objects.equals(returnOrderType, TDaReturnOrderTypeEnum.RETURN.getCode())) {
                //退货单：申请送货日期当天，存在待取货、待大仓确认、已完成，不允许再发起退货
                xdaReturnOrders
                        .stream()
                        .filter(x -> Objects.equals(x.getReturnOrderType(), TDaReturnOrderTypeEnum.RETURN.getCategoryCode())
                                && (x.getStatus() == 2 || x.getStatus() == 4 || x.getStatus() == 6))
                        .findAny()
                        .ifPresent(x -> QYAssert.isTrue(false, "已有" + TDaReturnOrderTypeEnum.getByCode(x.getReturnOrderType()).getCategoryDesc() + "完成的投诉，今日不可再投诉！"));

                //退货单：申请送货日期当天，存在待审核，更新该售后单中的退货的商品信息
                foundOrderToUpdate = xdaReturnOrders
                        .stream()
                        .filter(x -> Objects.equals(x.getReturnOrderType(), TDaReturnOrderTypeEnum.RETURN.getCategoryCode())
                                && (x.getStatus() == 1))
                        .findAny()
                        .map(x ->
                        {
                            //更新 退货信息，有投诉过的商品需报错
                            super.checkConflictCommodity(saveReturnOrderDTO, x);
                            // 没有投诉过的累计投诉金额，插入明细
                            XdaReturnOrder updateOrder = new XdaReturnOrder();
                            updateOrder.setId(x.getId());
                            updateOrder.setTotalApplyMoney(saveReturnOrderDTO.getComplaintTotalMoney().add(x.getTotalApplyMoney()));
                            updateOrder.setPickUpTimeRange(saveReturnOrderDTO.getPickUpTimeRange());
                            xdaReturnOrderMapper.updateByPrimaryKeySelective(updateOrder);
                            saveXdaReturnOrderItem(saveReturnOrderDTO, x.getId());
                            result.set(true);
                            return true;
                        }).orElse(false);
            } else {
                //少货/多货单：申请送货日期当天，存在已完成，不允许再发起少货
                xdaReturnOrders
                        .stream()
                        .filter(x -> Objects.equals(x.getReturnOrderType(), TDaReturnOrderTypeEnum.SHORTAGE.getCategoryCode())
                                && (x.getStatus() == 6))
                        .findAny()
                        .ifPresent(x -> QYAssert.isTrue(false, "已有" + TDaReturnOrderTypeEnum.getByCode(x.getReturnOrderType()).getCategoryDesc() + "完成的投诉，今日不可再投诉！"));

                //少货/多货单：申请送货日期当天，存在待审核，更新该售后单中的退货的商品信息
                foundOrderToUpdate = xdaReturnOrders
                        .stream()
                        .filter(x -> Objects.equals(x.getReturnOrderType(), TDaReturnOrderTypeEnum.SHORTAGE.getCategoryCode())
                                && (x.getStatus() == 1))
                        .findAny()
                        .map(x ->
                        {
                            // 更新 退货信息，有投诉过的商品需报错
                            super.checkConflictCommodity(saveReturnOrderDTO, x);
                            // 没有投诉过的累计投诉金额，插入明细
                            XdaReturnOrder updateOrder = new XdaReturnOrder();
                            updateOrder.setId(x.getId());
                            updateOrder.setTotalApplyMoney(saveReturnOrderDTO.getComplaintTotalMoney().add(x.getTotalApplyMoney()));
                            updateOrder.setPickUpTimeRange(saveReturnOrderDTO.getPickUpTimeRange());
                            xdaReturnOrderMapper.updateByPrimaryKeySelective(updateOrder);
                            saveXdaReturnOrderItem(saveReturnOrderDTO, x.getId());
                            result.set(true);
                            return true;
                        }).orElse(false);
            }
            if (foundOrderToUpdate) {
                return result.get();
            }
        }

        //创建退货单
        XdaReturnOrder xdaReturnOrder = new XdaReturnOrder();
        xdaReturnOrder.setStoreId(saveReturnOrderDTO.getStoreId());
        xdaReturnOrder.setReturnOrderType(returnOrderType);
        xdaReturnOrder.setReturnOrderCode(getUUID());
        xdaReturnOrder.setReturnOrderSeq(String.valueOf(new Date().getTime()));

        //根据批次和取货时间反查物流中心id
        xdaReturnOrder.setBusinessType(store.getBusinessType());
        xdaReturnOrder.setReturnType(TDaReturnTypeEnum.STORE_RETURN.getCode());
        xdaReturnOrder.setReturnSource(TDaReturnSourceEnum.APP_RETURN.getCode());
        xdaReturnOrder.setDriverId(saveReturnOrderDTO.getDriverId());
        xdaReturnOrder.setDriverName(saveReturnOrderDTO.getDriverName());
        xdaReturnOrder.setSourceOrderId(saveReturnOrderDTO.getOrderId());
        xdaReturnOrder.setSourceOrderCode(saveReturnOrderDTO.getOrderCode());
        xdaReturnOrder.setLogisticsCenterId(saveReturnOrderDTO.getLogisticsCenterId());
        xdaReturnOrder.setDeliveryBatch(saveReturnOrderDTO.getDeliveryBatch());
        xdaReturnOrder.setWaybillCode(saveReturnOrderDTO.getWaybillCode());
        xdaReturnOrder.setPickUpTimeRange(saveReturnOrderDTO.getPickUpTimeRange());
        xdaReturnOrder.setDeliveryTime(DateUtil.parseDate(saveReturnOrderDTO.getDeliveryDate(), "yyyy-MM-dd"));
        xdaReturnOrder.setPhone(store.getLinkmanMobile());
        xdaReturnOrder.setDeliveryAddress(store.getDeliveryAddress());
        xdaReturnOrder.setTotalApplyMoney(saveReturnOrderDTO.getComplaintTotalMoney());
        xdaReturnOrder.setBusinessType(TmsBusinessTypeEnums.TD_SALE.getCode());
        xdaReturnOrder.setRemark(saveReturnOrderDTO.getComplaintReason());
        //插入退货单
        int flag = xdaReturnOrderMapper.insertSelective(xdaReturnOrder);
        //插入退货明细
        saveXdaReturnOrderItem(saveReturnOrderDTO, xdaReturnOrder.getId());
        return flag > 0;

    }

    //插入退货明细
    private void saveXdaReturnOrderItem(SaveReturnOrderODTO saveReturnOrderDTO, Long id) {
        //创建退货明细
        List<Long> commodityIdList = saveReturnOrderDTO.getComplaintItemList().stream().map(SaveReturnOrderItemDTO::getCommodityId).distinct().collect(Collectors.toList());

        //查询商品信息（包装规格）
        Map<Long, Commodity> commodityInfoByIdMap = commodityService.findCommodityInfoByIdMap(commodityIdList);

        List<XdaReturnOrderItem> xdaReturnOrderItems = saveReturnOrderDTO.getComplaintItemList()
                .stream()
                .filter(x -> Objects.nonNull(commodityInfoByIdMap.get(x.getCommodityId())))
                .map(dto -> {
                    Commodity commodity = commodityInfoByIdMap.get(dto.getCommodityId());
                    XdaReturnOrderItem xdaReturnOrderItem = new XdaReturnOrderItem();
                    xdaReturnOrderItem.setReturnOrderId(id);
                    xdaReturnOrderItem.setCommodityId(dto.getCommodityId());
                    xdaReturnOrderItem.setCommodityPrice(dto.getCommodityPrice());
                    xdaReturnOrderItem.setComplaintContent(saveReturnOrderDTO.getComplaintReason());
                    BigDecimal applyQuantity = dto.getRealReturnQuantity().abs();
                    //计算申请详情
                    super.calculateApplyDetails(applyQuantity, dto, commodity, xdaReturnOrderItem);
                    xdaReturnOrderItem.setComplaintPicList(dto.getComplaintPicList());
                    xdaReturnOrderItem.setReturnReasonType(dto.getQuestionType());
                    xdaReturnOrderItem.setCommodityOrderQuantity(dto.getRealDeliveryQuantity());
                    if (dto.getComplaintType() == 1) {
                        xdaReturnOrderItem.setReturnOrderType(TDaReturnOrderTypeEnum.SHORTAGE.getCode());
                    } else if (dto.getComplaintType() == 2) {
                        xdaReturnOrderItem.setReturnOrderType(TDaReturnOrderTypeEnum.RETURN.getCode());
                    } else if (dto.getComplaintType() == 3) {
                        xdaReturnOrderItem.setReturnOrderType(TDaReturnOrderTypeEnum.OVERAGE.getCode());
                    }
                    return xdaReturnOrderItem;
                }).collect(Collectors.toList());

        if (CollectionUtils.isNotEmpty(xdaReturnOrderItems)) {
            // 插入 退货明细
            xdaReturnOrderItemMapper.batchInsertSelective(xdaReturnOrderItems);

            //创建退货图片明细
            List<XdaReturnOrderItemPic> picList = new ArrayList<>();
            for (XdaReturnOrderItem xdaReturnOrderItem : xdaReturnOrderItems) {
                List<String> complaintPicList = xdaReturnOrderItem.getComplaintPicList();
                if (TDaReturnSourceEnum.APP_RETURN.getCode() == saveReturnOrderDTO.getReturnSource()
                        && TDaReturnOrderTypeEnum.RETURN.getCode() == xdaReturnOrderItem.getReturnOrderType()) {
                    QYAssert.isTrue(!(complaintPicList == null || complaintPicList.isEmpty()), "退货商品至少上传一张图片");
                    QYAssert.isTrue(complaintPicList.size() < 4, "退货商品最多上传三张图片");
                }
                if (CollectionUtils.isNotEmpty(complaintPicList)) {
                    for (String pic : complaintPicList) {
                        XdaReturnOrderItemPic xdaReturnOrderItemPic = new XdaReturnOrderItemPic();
                        xdaReturnOrderItemPic.setReturnOrderItemId(xdaReturnOrderItem.getId());
                        xdaReturnOrderItemPic.setImgPicUrl(pic);
                        picList.add(xdaReturnOrderItemPic);
                    }
                }
            }

            // 插入 退货图片明细
            if (CollectionUtils.isNotEmpty(picList)) {
                xdaReturnOrderItemPicMapper.batchInsertSelective(picList);
            }
        }
    }

    @Override
    public Integer getReturnSourceType() {
        return TDaReturnSourceEnum.APP_RETURN.getCode();
    }
}