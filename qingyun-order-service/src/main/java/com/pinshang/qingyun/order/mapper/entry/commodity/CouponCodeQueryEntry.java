package com.pinshang.qingyun.order.mapper.entry.commodity;

import lombok.Data;

import java.math.BigDecimal;

@Data
public class CouponCodeQueryEntry {

    /**
     * 条形码
     */
    private  String barcode;
    private String commodityId;
    private String commodityCode;

    /**
     * 商品名称
     */
    private String commodityName;

    private String commoditySpec;//规格

    private String commodityUnit;//单位

    private BigDecimal retailPrice;//门店零售价

    private BigDecimal promotionPrice;//特价

    private BigDecimal commodityPrice;//进货价

    private  Integer isWeight;//是否称重0-不称量,1-称重

    private Integer posDiscount;//允许POS手动打折:1-是,0-否

    private BigDecimal yy;//折扣在yy折以下将低于进货价
}
