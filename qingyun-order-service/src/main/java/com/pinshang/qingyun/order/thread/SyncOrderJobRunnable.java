//package com.pinshang.qingyun.order.thread;
//
//import com.pinshang.qingyun.box.utils.SpringUtil;
//import com.pinshang.qingyun.order.model.order.DeliveryTime;
//import com.pinshang.qingyun.order.service.syncOrderJob.SyncOrderJobService;
//import lombok.extern.slf4j.Slf4j;
//import org.redisson.api.RLock;
//import org.redisson.api.RedissonClient;
//
//import java.util.List;
//
//@Slf4j
//public class SyncOrderJobRunnable implements Runnable{
//	private final static String SYNC_ORDER_JOB_COVER_REDIS_PREFIX = "sync_order_job_cover_redis_prefix";
//	private String coverTime;
//	private SyncOrderJobService syncOrderJobService;
//	private RedissonClient redissonClient;
//
//	public SyncOrderJobRunnable(String coverTime,SyncOrderJobService syncOrderJobService,RedissonClient redissonClient){
//		this.coverTime = coverTime;
//		this.syncOrderJobService =syncOrderJobService;
//		this.redissonClient = redissonClient;
//	}
//
//	public String getCoverTime() {
//		return coverTime;
//	}
//
//	@Override
//	public void run() {
//		//log.info("\n====coverTime=="+this.getCoverTime());
//		 try {
//			 List<DeliveryTime> deliveryTimes =syncOrderJobService.getDeliveryTimeList(this.getCoverTime());
//			 if(SpringUtil.isEmpty(deliveryTimes)){
//				 return ;
//			 }
//			 deliveryTimes.forEach((deliveryTime) -> {
//				 //String lineGroupId = deliveryTime.getLineGroupId();
//				 //syncOrderJobService.syncOrderAndSO2DO(lineGroupId);
//				 RLock lock = redissonClient.getLock(SYNC_ORDER_JOB_COVER_REDIS_PREFIX+ deliveryTime.getLineGroupId());
//				 if (!lock.isLocked()) {
//					 lock.lock();
//					 try {
//						 syncOrderJobService.syncOrderAndSO2DO(deliveryTime);
//					 }finally {
//						 lock.unlock();
//					 }
//				 }
//			 });
//		 } catch (Throwable e) {
//			log.error("订单覆盖异常",e);
//		}
//	}
//
//
//}
