package com.pinshang.qingyun.order.service;

import com.pinshang.qingyun.base.api.QYApplicationContext;
import com.pinshang.qingyun.base.enums.YesOrNoEnums;
import com.pinshang.qingyun.box.utils.BeanCloneUtils;
import com.pinshang.qingyun.order.aspect.OrderBillLog;
import com.pinshang.qingyun.order.bo.StoreDeductionBO;
import com.pinshang.qingyun.order.bo.StoreRechargeBO;
import com.pinshang.qingyun.order.bo.StoreRechargeRubricationBO;
import com.pinshang.qingyun.order.enums.BillEnum;
import com.pinshang.qingyun.order.mapper.StoreSettlementMapper;
import com.pinshang.qingyun.order.model.order.StoreSettlement;
import com.pinshang.qingyun.order.model.shop.Shop;
import com.pinshang.qingyun.order.util.RedissonLockExecutor;
import com.pinshang.qingyun.settlementTb.dto.StoreRechargeDTO;
import com.pinshang.qingyun.settlementTb.dto.StoreRechargeOrderIDTO;
import com.pinshang.qingyun.settlementTb.dto.StoreRechargeRubricationIDTO;
import com.pinshang.qingyun.settlementTb.service.StoreRechargeClient;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;

import java.util.Collections;
import java.util.List;
import java.util.Objects;


/**
 * <p>
 * 充值退款 service
 * </p>
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class StoreRechargeService {

    @Autowired
    private StoreRechargeClient storeRechargeClient;

    @Autowired
    private RedissonLockExecutor redissonLockExecutor;

    @Autowired
    private StoreSettlementMapper storeSettlementMapper;
    @Autowired
    private ShopService shopService;
    /**
     * 是否是预付费客户
     */
    public Boolean isPreStore(Long storeId) {
        if (Objects.isNull(storeId)) {
            return false;
        }
        Example ex = new Example(StoreSettlement.class);
        ex.createCriteria().andEqualTo("storeId", storeId);
        List<StoreSettlement> ssList = this.storeSettlementMapper.selectByExample(ex);
        return CollectionUtils.isNotEmpty(ssList) && ssList.get(0).getCollectStatus();
    }

    /**
     * 红冲
     */
    @OrderBillLog(BillEnum.RED_INK_DESC)
    public void redInkAdjustment(StoreRechargeRubricationBO bo) {
        StoreRechargeRubricationIDTO dto = BeanCloneUtils.copyTo(bo, StoreRechargeRubricationIDTO.class);
        String lockKey = QYApplicationContext.redisKeyProfile + BillEnum.RED_INK.getCode() + ":" + bo.getStoreId();
        redissonLockExecutor.exec(lockKey, 5L, () -> storeRechargeClient.rubricationRecharge(dto));
    }

    /**
     * 给客户回款（充值）
     */
    @OrderBillLog(BillEnum.RECHARGE_DESC)
    public Boolean storeRecharge(StoreRechargeBO bo) {
        StoreRechargeDTO dto = BeanCloneUtils.copyTo(bo, StoreRechargeDTO.class);
        String lockKey = QYApplicationContext.redisKeyProfile + BillEnum.RECHARGE.getCode() + ":" + bo.getStoreId();
        return redissonLockExecutor.exec(lockKey, 5L, () -> storeRechargeClient.addRecharge(dto));
    }

    /**
     * 给客户扣款
     */
    @OrderBillLog(BillEnum.DEDUCTION_DESC)
    public void storeDeduction(StoreDeductionBO bo) {
        StoreRechargeOrderIDTO dto = BeanCloneUtils.copyTo(bo, StoreRechargeOrderIDTO.class);
        // 扣款时候加校验，余额不足，结算报错
        dto.setValidateAccountAmount(YesOrNoEnums.YES.getCode());

        // 如果客户在门店表，则不判断余额
        if(bo.getStoreId() != null) {
            List<Shop> shopList =  shopService.getShopByStoreIdList(Collections.singletonList(bo.getStoreId()));
            if(CollectionUtils.isNotEmpty(shopList)) {
                dto.setValidateAccountAmount(null);
            }
        }
        String lockKey = QYApplicationContext.redisKeyProfile + BillEnum.DEDUCTION.getCode() + ":" + bo.getStoreId();
        redissonLockExecutor.exec(lockKey, 5L, () -> storeRechargeClient.deductionAccount(dto));
    }
}
