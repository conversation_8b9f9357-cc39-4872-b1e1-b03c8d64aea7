package com.pinshang.qingyun.order.service.externalDocking;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.pinshang.qingyun.base.configure.expand.QYAssert;
import com.pinshang.qingyun.order.mapper.TescoMapper;
import com.pinshang.qingyun.order.mapper.entry.externalDocking.TescoEntry;
import com.pinshang.qingyun.order.vo.externalDocking.EmailVo;
import com.pinshang.qingyun.order.vo.externalDocking.TescoVo;
import com.sun.mail.util.PropUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.FileSystemResource;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.mail.javamail.JavaMailSenderImpl;
import org.springframework.mail.javamail.MimeMessageHelper;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.mail.MessagingException;
import javax.mail.internet.MimeMessage;
import java.io.File;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

/**
 * 乐购台账
 * <AUTHOR>
 * @date 2023年2月15日10:00:09
 */
@Service
public class TescoService {
    static {
        System.setProperty("mail.mime.splitlongparameters", "false");
    }

    @Autowired
    private TescoMapper tescoMapper;

    @Autowired
    private JavaMailSenderImpl javaMailSender;

    /**
     * 乐购台账列表分页查询
     * @param vo
     * @return
     */
    public PageInfo<TescoEntry> findTescoPageInfo(TescoVo vo){
        QYAssert.isTrue(null != vo, "参数异常");
        PageInfo<TescoEntry> pageDate = PageHelper.startPage(vo.getPageNo(), vo.getPageSize()).doSelectPageInfo(() -> {
            tescoMapper.findTescoList(vo);
        });
        return pageDate;
    }

    public HashMap<String, List<TescoEntry>> cvtTescoEntry(TescoVo vo) {
        vo.initExportPage();
        HashMap<String, List<TescoEntry>> tescoMaps = new HashMap<>();
        PageInfo<TescoEntry> pageDate = PageHelper.startPage(vo.getPageNo(), vo.getPageSize()).doSelectPageInfo(() -> {
            tescoMapper.findTescoList(vo);
        });
        List<TescoEntry> pageDateList = pageDate.getList();
        pageDateList.forEach(item->{
            String key = item.getRtShopCode()+"#"+item.getRtShopName()+"#"+item.getEmail();
            if (tescoMaps.containsKey(key)) {
                tescoMaps.get(key).add(item);
            } else {
                List<TescoEntry> tescoRsps = new ArrayList() {{
                    add(item);
                }};
                tescoMaps.put(key, tescoRsps);
            }
        });
        return tescoMaps;
    }
    /**
     * 发送邮件
     * @param emailVo
     * @throws MessagingException
     */
    public void sendEmail(EmailVo emailVo) throws MessagingException {
        MimeMessage mimeMessage = javaMailSender.createMimeMessage();
        MimeMessageHelper mimeMessageHelper = new MimeMessageHelper(mimeMessage,true);
        mimeMessageHelper.setSubject(emailVo.getSubject());
        mimeMessageHelper.setText(emailVo.getContent());
        mimeMessageHelper.setTo(emailVo.getTo());
        if(emailVo.getCc()!=null){
            mimeMessageHelper.setCc(emailVo.getCc());
        }
        mimeMessageHelper.setFrom("<EMAIL>");
        if(emailVo.getFile()!=null){
            FileSystemResource file = new FileSystemResource(emailVo.getFile());
            mimeMessageHelper.addAttachment(emailVo.getFileName(),file);
        }
        javaMailSender.send(mimeMessage);
    }
}
