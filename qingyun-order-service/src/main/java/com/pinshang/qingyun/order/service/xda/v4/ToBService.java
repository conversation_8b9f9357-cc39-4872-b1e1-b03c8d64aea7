package com.pinshang.qingyun.order.service.xda.v4;

import com.alibaba.fastjson.JSON;
import com.pinshang.qingyun.base.api.QYApplicationContext;
import com.pinshang.qingyun.base.enums.CloudDeliveryBatchTypeEnum;
import com.pinshang.qingyun.base.enums.DeliveryOrderTypeEnums;
import com.pinshang.qingyun.base.enums.order.OrderTypeEnum;
import com.pinshang.qingyun.base.enums.order.ProductTypeEnum;
import com.pinshang.qingyun.base.enums.storage.ToBTypeEnums;
import com.pinshang.qingyun.box.utils.SpringUtil;
import com.pinshang.qingyun.marketing.dto.mtCoupon.MtOrderODTO;
import com.pinshang.qingyun.order.dto.XDCommodityODTO;
import com.pinshang.qingyun.order.dto.pf.PfCreOrderItemDTO;
import com.pinshang.qingyun.order.dto.shopcart.ShoppingCartCommodityODTO;
import com.pinshang.qingyun.order.dto.shopcart.ShoppingCartODTO;
import com.pinshang.qingyun.order.dto.shopcart.v4.ShoppingCartCommodityV4ODTO;
import com.pinshang.qingyun.order.dto.shopcart.v4.ShoppingCartGroupV4ODTO;
import com.pinshang.qingyun.order.dto.shopcart.v4.ShoppingCartV4ODTO;
import com.pinshang.qingyun.order.dto.xda.v4.CreOrderItemV4DTO;
import com.pinshang.qingyun.order.dto.xda.v4.TdaDeliveryTimeRangeODTO;
import com.pinshang.qingyun.order.enums.CombTypeEnum;
import com.pinshang.qingyun.order.enums.ProductTypeEnums;
import com.pinshang.qingyun.order.enums.StockLevelEnums;
import com.pinshang.qingyun.order.enums.StockType;
import com.pinshang.qingyun.order.mapper.CommodityItemMapper;
import com.pinshang.qingyun.order.mapper.CommodityMapper;
import com.pinshang.qingyun.order.mapper.StoreMapper;
import com.pinshang.qingyun.order.mapper.entry.commodity.CommodityItemEntry;
import com.pinshang.qingyun.order.mapper.entry.commodity.CommodityItemEntryList;
import com.pinshang.qingyun.order.model.order.Order;
import com.pinshang.qingyun.order.model.store.Store;
import com.pinshang.qingyun.order.util.ReflectionUtils;
import com.pinshang.qingyun.product.dto.commodity.CommodityDetailODTO;
import com.pinshang.qingyun.product.dto.commodity.CommodityItemODTO;
import com.pinshang.qingyun.product.service.CommodityClient;
import com.pinshang.qingyun.storage.dto.tob.*;
import com.pinshang.qingyun.storage.service.tob.ToBClient;
import com.pinshang.qingyun.xda.product.service.XdaShoppingCartController;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RBucket;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2024/3/6 14:35
 */
@Slf4j
@Service
public class ToBService {

    @Autowired
    private ToBClient toBClient;

    @Autowired
    private StoreMapper storeMapper;

    @Autowired
    private XdaShoppingCartController xdaShoppingCartController;

    @Autowired
    private CommodityClient commodityClient;

    @Autowired
    private CommodityMapper commodityMapper;

    @Autowired
    private CommodityItemMapper commodityItemMapper;
    @Autowired
    private TdaOrderService tdaOrderService;

    @Autowired
    private RedissonClient redissonClient;

    @Autowired
    private OrderMtCouponService mtCouponService;

    public Map<Long, CommodityInventoryODTO> queryCommodityInventoryOne(Date orderTime,Long  commodityId,BigDecimal quantity,Integer commodityType,
                                                                     Long storeId, String deliveryTimeRange, Integer deliveryBatch) {
        Map<Long, BigDecimal> orderQuantityMap = new HashMap<>();
        orderQuantityMap.put(commodityId,quantity);
        List<CommodityInventoryDetailIDTO> orderCommodityList = new ArrayList<>();
        for (Map.Entry<Long, BigDecimal> entry : orderQuantityMap.entrySet()) {
            CommodityInventoryDetailIDTO idto = new CommodityInventoryDetailIDTO();
            idto.setCommodityId(entry.getKey());
            idto.setQuantity(entry.getValue());
            idto.setLevel(ProductTypeEnum.NORMAL.getCode());
            if (Objects.equals(commodityType,2)){
                idto.setLevel(ProductTypeEnum.TH.getCode());
            }
            orderCommodityList.add(idto);
        }
        List<CommodityInventoryODTO> commodityInventoryODTOS = this.queryCommodityInventory(orderTime, orderCommodityList,storeId,deliveryTimeRange,deliveryBatch);
        return commodityInventoryODTOS.stream().collect(Collectors.toMap(CommodityInventoryODTO::getCommodityId, e -> e));
    }
    public List<CommodityInventoryODTO> queryCommodityInventory(Date orderTime, List<CommodityInventoryDetailIDTO> orderCommodityList,
                                                                Long storeId, String deliveryTimeRange, Integer deliveryBatch){
        // 通达销售设置物流中心id和type
        TdaDeliveryTimeRangeODTO deliveryTimeRangeODTO = tdaOrderService.checkDeliveryTimeRange(storeId, deliveryTimeRange, deliveryBatch, false);

        return doQueryCommodityInventory(orderTime,orderCommodityList,storeId,null,deliveryTimeRangeODTO);

    }

    public List<CommodityInventoryODTO> queryCupCommodityInventory(Date orderTime, List<CommodityInventoryDetailIDTO> orderCommodityList,
                                                                Long storeId,Long orderId, String deliveryTimeRange, Integer deliveryBatch){
        // 通达销售设置物流中心id和type
        TdaDeliveryTimeRangeODTO deliveryTimeRangeODTO = tdaOrderService.checkCupDeliveryTimeRange(storeId, deliveryTimeRange, deliveryBatch);

        return doQueryCommodityInventory(orderTime,orderCommodityList,storeId,orderId,deliveryTimeRangeODTO);

    }

    private List<CommodityInventoryODTO> doQueryCommodityInventory(Date orderTime, List<CommodityInventoryDetailIDTO> orderCommodityList,
                                                                Long storeId,Long orderId,TdaDeliveryTimeRangeODTO deliveryTimeRangeODTO){
        if(SpringUtil.isEmpty(orderCommodityList)){
            return Collections.emptyList();
        }

        CommodityInventoryIDTO commodityInventoryIDTO = new CommodityInventoryIDTO();
        commodityInventoryIDTO.setOrderTime(orderTime);
        commodityInventoryIDTO.setOrderCommodityList(orderCommodityList);
        commodityInventoryIDTO.setOrderId(orderId);
        if(deliveryTimeRangeODTO != null){
            //commodityInventoryIDTO.setLogisticsCenterId(deliveryTimeRangeODTO.getLogisticsCenterId());
            commodityInventoryIDTO.setType(DeliveryOrderTypeEnums.TD_SALE.getCode());
        }
        List<CommodityInventoryODTO> commodityInventoryODTOS = toBClient.queryCommodityWithBomInventory(commodityInventoryIDTO);
        log.info("查询B端库存,入参:{},返回:{}", JSON.toJSONString(commodityInventoryIDTO),JSON.toJSONString(commodityInventoryODTOS));
        if(SpringUtil.isEmpty(commodityInventoryODTOS)){
            return Collections.emptyList();
        }
        return commodityInventoryODTOS;
    }


    public List<CommodityInventoryDetailIDTO> getPfInventoryOrderCommodityList(ShoppingCartODTO shoppingCartODTO) {
        List<CommodityInventoryDetailIDTO> orderCommodityList = new ArrayList<>();
        for (ShoppingCartCommodityODTO commodity : shoppingCartODTO.getNormalGroup().getCommodities()) {
            orderCommodityList.add(CommodityInventoryDetailIDTO.builder().commodityId(commodity.getCommodityId())
                    .quantity(commodity.getQuantity()).level(ProductTypeEnum.NORMAL.getCode()).build());
        }
        for (ShoppingCartCommodityODTO commodity : shoppingCartODTO.getFreezeGroups().getCommodities()) {
            orderCommodityList.add(CommodityInventoryDetailIDTO.builder().commodityId(commodity.getCommodityId())
                    .quantity(commodity.getQuantity()).level(ProductTypeEnum.NORMAL.getCode()).build());
        }
        for (ShoppingCartCommodityODTO commodity : shoppingCartODTO.getGiftGroup().getCommodities()) {
            orderCommodityList.add(CommodityInventoryDetailIDTO.builder().commodityId(commodity.getCommodityId())
                    .quantity(commodity.getQuantity()).level(ProductTypeEnum.GIFT.getCode()).build());
        }
        return mergeOrderItemsByLevel(orderCommodityList);
    }
    /**
     * 将购物车的商品组装成多箱规商品信息
     */
    public List<CommodityInventoryDetailIDTO> getInventoryOrderCommodityList(ShoppingCartV4ODTO shoppingCartODTO) {
        List<CommodityInventoryDetailIDTO> orderCommodityList = new ArrayList<>();
        List<Long> commodityIdList = new ArrayList<>();
        for (ShoppingCartCommodityV4ODTO commodity : shoppingCartODTO.getNormalGroup().getCommodities()) {
            if (Objects.equals(commodity.getProductType(), 2)) {
                commodityIdList.add(commodity.getCommodityId());
            }
        }
        for (ShoppingCartCommodityV4ODTO commodity : shoppingCartODTO.getThGroups().getCommodities()) {
            if (Objects.equals(commodity.getProductType(), 2)) {
                commodityIdList.add(commodity.getCommodityId());
            }
        }
        for (ShoppingCartGroupV4ODTO shoppingCartGroupV4ODTO : shoppingCartODTO.getPromotionGroup()) {
            for (ShoppingCartCommodityV4ODTO commodity : shoppingCartGroupV4ODTO.getCommodities()) {
                if (Objects.equals(commodity.getProductType(), 2)) {
                    commodityIdList.add(commodity.getCommodityId());
                }
            }
        }
        //批量查询组合商品的子商品列表
        List<CommodityItemEntryList> commodityItemList = getCommodityItemListByCommodityIdList(commodityIdList);
        Map<Long, List<CommodityItemEntry>> map = commodityItemList.stream().collect(Collectors.toMap(CommodityItemEntryList::getCommodityId, CommodityItemEntryList::getCommodityItemEntry));

        // 特价商品
        List<ShoppingCartCommodityV4ODTO> sepcialCommodityGroup = shoppingCartODTO.getNormalGroup().getCommodities().stream().filter(x -> Objects.equals(x.getIsSpecialPrice(), 1)).collect(Collectors.toList());
        for (ShoppingCartCommodityV4ODTO commodity : sepcialCommodityGroup) {
            if (Objects.equals(commodity.getProductType(), 2)) {
                List<CommodityItemEntry> commodityItemODTOList = map.get(commodity.getCommodityId());
                if (CollectionUtils.isNotEmpty(commodityItemODTOList)) {
                    for (CommodityItemEntry commodityItemODTO : commodityItemODTOList) {
                        CommodityInventoryDetailIDTO idto = new CommodityInventoryDetailIDTO();
                        idto.setCommodityId(Long.valueOf(commodityItemODTO.getCommodityItemId()));
                        idto.setQuantity(commodity.getQuantity().multiply(commodityItemODTO.getCommodityNum()));
                        idto.setLevel(StockLevelEnums.SPECIAL_COMB.getCode());
                        orderCommodityList.add(idto);
                    }
                }
            }else {
                CommodityInventoryDetailIDTO idto = new CommodityInventoryDetailIDTO();
                idto.setCommodityId(commodity.getCommodityId());
                idto.setQuantity(commodity.getQuantity());
                idto.setLevel(StockLevelEnums.SPECIAL.getCode());
                orderCommodityList.add(idto);
            }

        }

        // 普通商品
        List<ShoppingCartCommodityV4ODTO> normalCommodityGroup = shoppingCartODTO.getNormalGroup().getCommodities().stream().filter(x -> Objects.equals(x.getIsSpecialPrice(), 0)).collect(Collectors.toList());
        for (ShoppingCartCommodityV4ODTO commodity : normalCommodityGroup) {
            if (Objects.equals(commodity.getProductType(), 2)) {
                List<CommodityItemEntry> commodityItemODTOList = map.get(commodity.getCommodityId());
                if (CollectionUtils.isNotEmpty(commodityItemODTOList)) {
                    for (CommodityItemEntry commodityItemODTO : commodityItemODTOList) {
                        CommodityInventoryDetailIDTO idto = new CommodityInventoryDetailIDTO();
                        idto.setCommodityId(Long.valueOf(commodityItemODTO.getCommodityItemId()));
                        idto.setQuantity(commodity.getQuantity().multiply(commodityItemODTO.getCommodityNum()));
                        idto.setLevel(StockLevelEnums.NORMAL_COMB.getCode());
                        orderCommodityList.add(idto);
                    }
                }
            } else {
                CommodityInventoryDetailIDTO idto = new CommodityInventoryDetailIDTO();
                idto.setCommodityId(commodity.getCommodityId());
                idto.setQuantity(commodity.getQuantity());
                idto.setLevel(StockLevelEnums.NORMAL.getCode());
                orderCommodityList.add(idto);
            }

        }
        List<ShoppingCartCommodityV4ODTO> promotionGroupCommodityList = shoppingCartODTO.getPromotionGroup().stream().flatMap(item -> item.getCommodities().stream()).collect(Collectors.toList());
        // 赠品
        for (ShoppingCartCommodityV4ODTO commodity : promotionGroupCommodityList) {
            Boolean isGift = commodity.getIsGift();
            CommodityInventoryDetailIDTO idto = new CommodityInventoryDetailIDTO();
            idto.setCommodityId(commodity.getCommodityId());
            idto.setQuantity(commodity.getQuantity());
            //是否是赠品
            if (isGift) {
                idto.setLevel(StockLevelEnums.GIFT.getCode());
                orderCommodityList.add(idto);
            } else {
                //不是赠品活动，但是是组合品
                if (Objects.equals(commodity.getProductType(), 2)) {
                    List<CommodityItemEntry> commodityItemODTOList = map.get(commodity.getCommodityId());
                    if (CollectionUtils.isNotEmpty(commodityItemODTOList)) {
                        for (CommodityItemEntry commodityItemODTO : commodityItemODTOList) {
                            CommodityInventoryDetailIDTO detailIDTO = new CommodityInventoryDetailIDTO();
                            detailIDTO.setCommodityId(Long.valueOf(commodityItemODTO.getCommodityItemId()));
                            detailIDTO.setQuantity(commodity.getQuantity().multiply(commodityItemODTO.getCommodityNum()));
                            detailIDTO.setLevel(StockLevelEnums.NORMAL_COMB.getCode());
                            orderCommodityList.add(detailIDTO);
                        }
                    }
                } else {
                    idto.setLevel(StockLevelEnums.NORMAL.getCode());
                    orderCommodityList.add(idto);
                }
            }
        }
        // 特惠品
        for (ShoppingCartCommodityV4ODTO commodity : shoppingCartODTO.getThGroups().getCommodities()) {
            if (Objects.equals(commodity.getProductType(), 2)) {
                List<CommodityItemEntry> commodityItemODTOList = map.get(commodity.getCommodityId());
                if (CollectionUtils.isNotEmpty(commodityItemODTOList)) {
                    for (CommodityItemEntry commodityItemODTO : commodityItemODTOList) {
                        CommodityInventoryDetailIDTO idto = new CommodityInventoryDetailIDTO();
                        idto.setCommodityId(Long.valueOf(commodityItemODTO.getCommodityItemId()));
                        idto.setQuantity(commodity.getQuantity().multiply(commodityItemODTO.getCommodityNum()));
                        idto.setLevel(StockLevelEnums.TH_COMB.getCode());
                        orderCommodityList.add(idto);
                    }
                }
            }else {
                CommodityInventoryDetailIDTO idto = new CommodityInventoryDetailIDTO();
                idto.setCommodityId(commodity.getCommodityId());
                idto.setQuantity(commodity.getQuantity());
                idto.setLevel(StockLevelEnums.TH.getCode());
                orderCommodityList.add(idto);
            }
        }
        return mergeOrderItemsByLevel(orderCommodityList);
    }

    /**
     * 根据商品等级合并订单数量
     */
    public List<CommodityInventoryDetailIDTO> mergeOrderItemsByLevel(List<CommodityInventoryDetailIDTO> orderCommodityList) {
        if (CollectionUtils.isEmpty(orderCommodityList)) {
            return Collections.emptyList();
        }
        Map<String, BigDecimal> productMap = orderCommodityList.stream()
                .collect(Collectors.groupingBy(
                        idto -> idto.getCommodityId() + "_" + idto.getLevel(),
                        Collectors.reducing(BigDecimal.ZERO, CommodityInventoryDetailIDTO::getQuantity, BigDecimal::add)
                ));
        List<CommodityInventoryDetailIDTO> mergedList = new ArrayList<>();
        // 构建聚合后的商品列表
        productMap.forEach((key, value) -> {
            String[] parts = key.split("_");
            CommodityInventoryDetailIDTO idto = new CommodityInventoryDetailIDTO();
            idto.setCommodityId(Long.parseLong(parts[0]));
            idto.setLevel(Integer.parseInt(parts[1]));
            idto.setQuantity(value);
            mergedList.add(idto);
        });
        return mergedList;
    }

    /*public Map<Long, CommodityInventoryODTO> queryCommodityInventory(Date orderTime, Map<Long, BigDecimal> orderQuantityMap,Long orderId){
        CommodityInventoryIDTO commodityInventoryIDTO = new CommodityInventoryIDTO();
        commodityInventoryIDTO.setOrderTime(orderTime);
        commodityInventoryIDTO.setOrderQuantityMap(orderQuantityMap);
        commodityInventoryIDTO.setOrderId(orderId);
        List<CommodityInventoryODTO> commodityInventoryODTOS = toBClient.queryCommodityWithBomInventory(commodityInventoryIDTO);
        if(SpringUtil.isEmpty(commodityInventoryODTOS)){
            return new HashMap<>();
        }
        return commodityInventoryODTOS.stream().collect(Collectors.toMap(CommodityInventoryODTO::getCommodityId, e -> e));
    }*/
    /**
     * 新增订单库存冻结
     * @param order
     * @param creOrderItemDTOList
     * @return
     */
    public Map<Long,CommodityInventoryODTO> savePfOrderWarehouseFreezeInventory(Order order,List<PfCreOrderItemDTO> creOrderItemDTOList,
                                                                              Map<Long,Integer> commodityStockTypeMap){
        WarehouseFreezeInventoryIDTO warehouseFreezeInventoryIDTO = new WarehouseFreezeInventoryIDTO();
        warehouseFreezeInventoryIDTO.setOrderId(order.getId());
        warehouseFreezeInventoryIDTO.setOrderCode(order.getOrderCode());
        warehouseFreezeInventoryIDTO.setOrderTime(order.getOrderTime());
        warehouseFreezeInventoryIDTO.setType(ToBTypeEnums.SALE.getCode());
        warehouseFreezeInventoryIDTO.setSourceType(OrderTypeEnum.PF_APP_ORDER.getCode());
        warehouseFreezeInventoryIDTO.setDeliveryBatch(CloudDeliveryBatchTypeEnum.ZERO_BATCH.getCode());
        warehouseFreezeInventoryIDTO.setStoreId(order.getStoreId());
        warehouseFreezeInventoryIDTO.setNeedError(0);
        Store store = storeMapper.selectByPrimaryKey(order.getStoreId());
        if(null != store){
            warehouseFreezeInventoryIDTO.setStoreCode(store.getStoreCode());
            warehouseFreezeInventoryIDTO.setStoreName(store.getStoreName());
        }
        List<ToBOrderDetailIDTO> toBOrderDetailIDTOS = new ArrayList<>();
        Map<Long, Map<ProductTypeEnums, List<PfCreOrderItemDTO>>> orderListMap = creOrderItemDTOList.stream().collect(Collectors.groupingBy(PfCreOrderItemDTO::getCommodityId, Collectors.groupingBy(PfCreOrderItemDTO::getType)));
        Map<Long, BigDecimal> commodityPackageSpecMap = xdaShoppingCartController.selectCommodityPackageSpecByCommodityIdList(orderListMap.keySet().stream().collect(Collectors.toList()));
        orderListMap.forEach((key,value)->{
            BigDecimal noQuantity = value.values().stream().flatMap(Collection::stream).filter(item -> !item.getType().equals(ProductTypeEnums.GIFT)).map(PfCreOrderItemDTO::getQuantity).reduce(BigDecimal.ZERO, BigDecimal::add);
            BigDecimal giftQuantity = value.values().stream().flatMap(Collection::stream).filter(item -> item.getType().equals(ProductTypeEnums.GIFT)).map(PfCreOrderItemDTO::getQuantity).reduce(BigDecimal.ZERO, BigDecimal::add);
            BigDecimal add = noQuantity.add(giftQuantity);
            ToBOrderDetailIDTO toBOrderDetailIDTO = new ToBOrderDetailIDTO();
            toBOrderDetailIDTO.setCommodityId(key);
            toBOrderDetailIDTO.setStockType(commodityStockTypeMap.get(key));
            BigDecimal remainder = add.divide(commodityPackageSpecMap.get(key),0,BigDecimal.ROUND_UP);
            toBOrderDetailIDTO.setOrderNumber(remainder.intValue());
            toBOrderDetailIDTO.setOrderQuantity(add);
            toBOrderDetailIDTO.setGuaranteedQuantity(noQuantity);
            toBOrderDetailIDTOS.add(toBOrderDetailIDTO);
        });
        List<ToBOrderDetailIDTO> collect = toBOrderDetailIDTOS.stream().filter(item -> item.getOrderQuantity().compareTo(BigDecimal.ZERO) > 0).collect(Collectors.toList());
        warehouseFreezeInventoryIDTO.setOrderCommodityDetailList(collect);
        List<CommodityInventoryODTO> commodityInventoryODTOS = null;
        try {
            commodityInventoryODTOS = toBClient.warehouseFreezeInventory(warehouseFreezeInventoryIDTO);
        }catch (Exception exception){
            log.error("新增订单调用冻结接口 异常={}",exception);
            log.error("新增订单调用冻结接口 请求参数={}",warehouseFreezeInventoryIDTO);
            log.error("新增订单调用冻结接口 返回参数={}",commodityInventoryODTOS);
        }
        if(SpringUtil.isEmpty(commodityInventoryODTOS)){
            return new HashMap<>();
        }
        return commodityInventoryODTOS.stream().collect(Collectors.toMap(CommodityInventoryODTO::getCommodityId,e->e));
    }
    /**
     * 新增订单库存冻结
     * @param order
     * @param creOrderItemDTOList
     * @return
     */
    public void saveXdaOrderWarehouseFreezeInventory(Order order,List<CreOrderItemV4DTO> creOrderItemDTOList,
                                                     Map<Long,Integer> commodityStockTypeMap,
                                                     TdaDeliveryTimeRangeODTO tdaDeliveryTimeRangeODTO){
        WarehouseFreezeInventoryIDTO warehouseFreezeInventoryIDTO = new WarehouseFreezeInventoryIDTO();
        warehouseFreezeInventoryIDTO.setNeedError(0);
        warehouseFreezeInventoryIDTO.setOrderId(order.getId());
        warehouseFreezeInventoryIDTO.setOrderCode(order.getOrderCode());
        warehouseFreezeInventoryIDTO.setOrderTime(order.getOrderTime());
        warehouseFreezeInventoryIDTO.setType(ToBTypeEnums.SALE.getCode());
        warehouseFreezeInventoryIDTO.setSourceType(OrderTypeEnum.XDA_APP_ORDER.getCode());
        warehouseFreezeInventoryIDTO.setDeliveryBatch(order.getDeliveryBatch());
        warehouseFreezeInventoryIDTO.setStoreId(order.getStoreId());
        warehouseFreezeInventoryIDTO.setNeedError(0);
        Store store = storeMapper.selectByPrimaryKey(order.getStoreId());
        if(null != store){
            warehouseFreezeInventoryIDTO.setStoreCode(store.getStoreCode());
            warehouseFreezeInventoryIDTO.setStoreName(store.getStoreName());
        }
        List<ToBOrderDetailIDTO> toBOrderDetailIDTOS = new ArrayList<>();
        //冻结库存时过滤掉组合商品，冻结子商品
        List<CreOrderItemV4DTO> creOrderItemList = creOrderItemDTOList.stream().filter(x -> !Objects.equals(x.getCombType(), CombTypeEnum.COMB.getCode())).collect(Collectors.toList());
        Map<Long, Map<ProductTypeEnums, List<CreOrderItemV4DTO>>> orderListMap = creOrderItemList.stream().collect(Collectors.groupingBy(CreOrderItemV4DTO::getCommodityId, Collectors.groupingBy(CreOrderItemV4DTO::getType)));
        Map<Long, BigDecimal> commodityPackageSpecMap = xdaShoppingCartController.selectCommodityPackageSpecByCommodityIdList(orderListMap.keySet().stream().collect(Collectors.toList()));
        orderListMap.forEach((key,value)->{
            BigDecimal noQuantity = value.values().stream().flatMap(Collection::stream).filter(item -> !item.getType().equals(ProductTypeEnums.GIFT)).map(CreOrderItemV4DTO::getQuantity).reduce(BigDecimal.ZERO, BigDecimal::add);
            BigDecimal giftQuantity = value.values().stream().flatMap(Collection::stream).filter(item -> item.getType().equals(ProductTypeEnums.GIFT)).map(CreOrderItemV4DTO::getQuantity).reduce(BigDecimal.ZERO, BigDecimal::add);
            BigDecimal add = noQuantity.add(giftQuantity);
            ToBOrderDetailIDTO toBOrderDetailIDTO = new ToBOrderDetailIDTO();
            toBOrderDetailIDTO.setCommodityId(key);
            toBOrderDetailIDTO.setStockType(commodityStockTypeMap.get(key));
            BigDecimal remainder = add.divide(commodityPackageSpecMap.get(key),0,BigDecimal.ROUND_UP);
            toBOrderDetailIDTO.setOrderNumber(remainder.intValue());
            toBOrderDetailIDTO.setOrderQuantity(add);
            toBOrderDetailIDTO.setGuaranteedQuantity(noQuantity);
            toBOrderDetailIDTOS.add(toBOrderDetailIDTO);
        });
        List<ToBOrderDetailIDTO> collect = toBOrderDetailIDTOS.stream().filter(item -> item.getOrderQuantity().compareTo(BigDecimal.ZERO) > 0).collect(Collectors.toList());
        warehouseFreezeInventoryIDTO.setOrderCommodityDetailList(collect);
        if(tdaDeliveryTimeRangeODTO != null){
            //warehouseFreezeInventoryIDTO.setLogisticsCenterId(tdaDeliveryTimeRangeODTO.getLogisticsCenterId());
            warehouseFreezeInventoryIDTO.setType(DeliveryOrderTypeEnums.TD_SALE.getCode());
        }else {
            warehouseFreezeInventoryIDTO.setType(DeliveryOrderTypeEnums.SALE.getCode());
        }

        // 调用大仓冻结库存
        toBClient.warehouseFreezeInventory(warehouseFreezeInventoryIDTO);
    }


    /**
     * 根据订单ID 解冻商品库存
     * @param orderId
     * @return
     */
    public Boolean warehouseUnfreezeInventory(Long orderId, Long storeId){
        if(orderId == null){
            return Boolean.FALSE;
        }

        log.warn("-----------调用解冻 orderId = {}", orderId);
        //try {
            Boolean isTdaStore = tdaOrderService.isTdaStore(storeId);
            WarehouseUnfreezeInventoryIDTO reqVo = new WarehouseUnfreezeInventoryIDTO();
            reqVo.setOrderId(orderId);
            if(isTdaStore){
                reqVo.setType(DeliveryOrderTypeEnums.TD_SALE.getCode());
            }else {
                reqVo.setType(DeliveryOrderTypeEnums.SALE.getCode());
            }
            return toBClient.warehouseUnfreezeInventory(reqVo);
       // }catch (Exception ex){
       //     log.warn("调用解冻异常 orderId{}", orderId);
        //    return Boolean.FALSE;
        //}
    }


    /**
     * 计算组合商品最小库存
     * 比如添加购物车时 3C=3（1A+2B），此时A、B的库存分别为 2、7时，
     * 则C的库存min（2/1=2，7/2=3.5）=2，则提示：库存不足，余量为2
     *
     * @param commodityItemList         子商品列表
     * @param commodityInventoryODTOMap 子商品库存
     * @return 组合商品最小库存
     */
    public BigDecimal calculateMinStock(List<CommodityItemODTO> commodityItemList, Map<Long, CommodityInventoryODTO> commodityInventoryODTOMap) {
        BigDecimal minStock = BigDecimal.valueOf(Long.MAX_VALUE);
        for (CommodityItemODTO commodityItem : commodityItemList) {
            String commodityItemId = commodityItem.getCommodityItemId();
            CommodityInventoryODTO commodityInventory = commodityInventoryODTOMap.get(Long.valueOf(commodityItemId));
            if (Objects.nonNull(commodityInventory)){
                BigDecimal inventoryNum = commodityInventory.getInventoryQuantity();
                BigDecimal stockDivided = inventoryNum.divide(commodityItem.getCommodityNum(), 0, RoundingMode.DOWN);
                minStock = minStock.min(stockDivided);
            }
        }
        return minStock;
    }

    /**
     * 查询组合商品的子商品
     * @param commodityId 组合商品id
     * @return 子商品列表
     */
    public List<CommodityItemODTO> getCommodityItemList(Long commodityId) {
        XDCommodityODTO xdCommodityODTO = commodityMapper.getXDCommodityODTOById(commodityId);
        if (Objects.nonNull(xdCommodityODTO) && Objects.equals(xdCommodityODTO.getProductType(), 2)) {
            CommodityDetailODTO commodityDetail = commodityClient.findCommodityDetailById(commodityId);
            return Objects.nonNull(commodityDetail) ? commodityDetail.getCommodityItemList() : Collections.emptyList();
        }
        return Collections.emptyList();
    }

    /**
     * 判断子商品列表里是否有依据大仓或者限量的
     */
    public boolean checkStockFlag(List<CommodityItemODTO> commodityItemList, Map<Long, CommodityInventoryODTO> commodityInventoryODTOMap) {
        boolean stockFlag = false;
        for (CommodityItemODTO commodityItemODTO : commodityItemList) {
            String commodityItemId = commodityItemODTO.getCommodityItemId();
            CommodityInventoryODTO commodityInventoryODTO = commodityInventoryODTOMap.get(Long.valueOf(commodityItemId));
            if (!stockFlag && !commodityInventoryODTO.getStockType().equals(StockType.UNLIMITED.getCode())) {
                stockFlag = true;
            }
        }
        return stockFlag;
    }

    /**
     * 根据组合商品id 批量查询子商品列表
     */
    public List<CommodityItemEntryList> getCommodityItemListByCommodityIdList(List<Long> commodityIdList) {
        if (CollectionUtils.isEmpty(commodityIdList)) {
            return Collections.emptyList();
        }
        List<CommodityItemEntry> commodityItemList = commodityItemMapper.findCommodityItemListByCommodityIdList(commodityIdList);
        if (CollectionUtils.isEmpty(commodityItemList)) {
            return Collections.emptyList();
        }
        return commodityItemList
                .stream()
                .collect(Collectors.groupingBy(CommodityItemEntry::getCommodityId))
                .entrySet()
                .stream()
                .map(entry -> {
                    CommodityItemEntryList commodityItemEntryList = new CommodityItemEntryList();
                    commodityItemEntryList.setCommodityId(entry.getKey());
                    commodityItemEntryList.setCommodityItemEntry(entry.getValue());
                    return commodityItemEntryList;
                }).collect(Collectors.toList());
    }

    /**
     * 结算类型：
     * 0：不结算，
     * 1：鲜达模式=取最小（数量：实发小取实发，下单小去下单，日期：实发晚取实发日期，送货日期晚取送货日期），
     * 2：批发模式，
     * 3：门店结算（取实发），
     * 4：取下单
     * @param id 客户类型Id
     * @return
     */
    public Integer findSettleBillCalcTypeById(Long id){
        return storeMapper.findSettleBillCalcTypeById(id);
    }


    /**
     * 处理特价限购提示
     */
    public void handleSpecialLimitWarningTips(Object object, Long storeId, Long commodityId, BigDecimal orderQuantity, BigDecimal availableTotalLimit, BigDecimal availableStoreLimit, Integer specialLimit) {
        String specialLimitWarning = StringUtils.EMPTY;
        //达到限量数未达限购数
        if (orderQuantity.compareTo(availableTotalLimit) > 0 && orderQuantity.compareTo(availableStoreLimit) <= 0) {
            specialLimitWarning = "特价商品限量供应，超出部分将按原价计算";
        }
        //未达限量数达到限购数
        if (orderQuantity.compareTo(availableTotalLimit) <= 0 && orderQuantity.compareTo(availableStoreLimit) > 0) {
            specialLimitWarning = "特价商品每天限" + specialLimit + "份，超出部分将按原价计算";
        }
        //同时达到限量数和限购数
        if (orderQuantity.compareTo(availableTotalLimit) > 0 && orderQuantity.compareTo(availableStoreLimit) > 0) {
            specialLimitWarning = "特价商品每天限" + specialLimit + "份，超出部分将按原价计算";
        }
        if (StringUtils.isNotEmpty(specialLimitWarning)){
            //已超限
            ReflectionUtils.setIsExceedLimit(object, Boolean.TRUE);
        }

        //设置app超限提示
        String key = "SPECIAL_PRICE_LIMIT_WARNING:%s:%s";
        RBucket<Integer> bucket = redissonClient.getBucket(QYApplicationContext.redisKeyProfile + String.format(key, storeId, commodityId));
        //warningFlag：null-空（过期或者首次判断），1-超限，2-不超限
        Integer warningFlag = bucket.get();
        if (Objects.isNull(warningFlag)) {
            if (StringUtils.isNotEmpty(specialLimitWarning)) {
                //空-》超限，提示
                warningFlag = 1;
                //设置超限提示
                ReflectionUtils.setSpecialLimitWarningTips(object, specialLimitWarning);
            } else {
                //空-》不超限，不提示
                warningFlag = 2;
            }
        } else {
            if (Objects.equals(warningFlag, 1) && StringUtils.isNotEmpty(specialLimitWarning)) {
                //超限-》超限，不提示
                warningFlag = 1;
            } else if (Objects.equals(warningFlag, 1) && StringUtils.isEmpty(specialLimitWarning)) {
                //超限-》不超限，不提示
                warningFlag = 2;
            } else if (Objects.equals(warningFlag, 2) && StringUtils.isNotEmpty(specialLimitWarning)) {
                //不超限-》超限，提示
                warningFlag = 1;
                //设置超限提示
                ReflectionUtils.setSpecialLimitWarningTips(object, specialLimitWarning);
            } else if (Objects.equals(warningFlag, 2) && StringUtils.isEmpty(specialLimitWarning)) {
                //不超限-》不超限，不提示
                warningFlag = 2;
            }
        }
        bucket.set(warningFlag, 7, TimeUnit.DAYS);
    }

    /**
     * 计算优惠券优惠
     */
    public MtOrderODTO processCoupon(Long storeId, Long couponUserId, ShoppingCartV4ODTO shoppingCartODTO) {
        return mtCouponService.processCoupon(storeId, couponUserId, shoppingCartODTO);
    }
}
