package com.pinshang.qingyun.order.service;

import com.pinshang.qingyun.order.mapper.OrderMirrorMapper;
import com.pinshang.qingyun.order.mapper.StoreAreaMapper;
import com.pinshang.qingyun.order.model.order.OrderMirror;
import com.pinshang.qingyun.order.model.store.StoreArea;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;

/**
 * @Author: sk
 * @Date: 2024/7/18
 */
@Slf4j
@Service
public class OrderMirrorService {

    @Autowired
    private OrderMirrorMapper orderMirrorMapper;

    public OrderMirror findOrderMirrorByOrderId(Long orderId) {
        Example example = new Example(OrderMirror.class);
        example.createCriteria().andEqualTo("orderId",orderId);
        return orderMirrorMapper.selectOneByExample(example);
    }



}
