package com.pinshang.qingyun.order.service.xda;

import com.pinshang.qingyun.base.configure.expand.QYAssert;
import com.pinshang.qingyun.base.enums.BusinessTypeEnums;
import com.pinshang.qingyun.box.utils.BeanCloneUtils;
import com.pinshang.qingyun.box.utils.DateUtil;
import com.pinshang.qingyun.box.utils.SpringUtil;
import com.pinshang.qingyun.box.utils.StringUtil;
import com.pinshang.qingyun.common.dto.DictionaryODTO;
import com.pinshang.qingyun.common.service.DictionaryClient;
import com.pinshang.qingyun.order.dto.xda.*;
import com.pinshang.qingyun.order.dto.xda.tda.SaveReturnOrderODTO;
import com.pinshang.qingyun.order.dto.xda.v4.TdaDeliveryTimeRangeODTO;
import com.pinshang.qingyun.order.enums.ComplaintTypeEnum;
import com.pinshang.qingyun.order.enums.XdaComplainItemTypeEnums;
import com.pinshang.qingyun.order.enums.tda.TDaReturnSourceEnum;
import com.pinshang.qingyun.order.mapper.xda.XdaComplaintCommodityItemPicMapper;
import com.pinshang.qingyun.order.mapper.xda.XdaComplaintDeliveryManODTO;
import com.pinshang.qingyun.order.mapper.xda.XdaComplaintOrderItemMapper;
import com.pinshang.qingyun.order.mapper.xda.XdaComplaintOrderMapper;
import com.pinshang.qingyun.order.model.xda.XdaComplaint;
import com.pinshang.qingyun.order.model.xda.XdaComplaintCommodityItem;
import com.pinshang.qingyun.order.model.xda.XdaComplaintCommodityItemPic;
import com.pinshang.qingyun.order.service.StoreService;
import com.pinshang.qingyun.order.service.tda.XdaReturnOrderService;
import com.pinshang.qingyun.order.service.xda.v4.TdaOrderService;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.InputStream;
import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

/**
 * <AUTHOR>
 * @Version 0.1.0
 * @Date 2020/12/30 14:24
 * @Copyright © 2019-2020 qm
 * @Description -- 类说明
 * <p>
 * 主表：投诉和退货
 * 投诉：
 * 问题类型：多货-9131078242860604176，少货-9131078242860601561
 * 投诉类型：多货，少货
 * 退货：
 * 问题类型：质量问题，口感问题，品相问题，日期问题
 * 投诉类型：退货
 **/
@Service
public class XdaComplaintOrderService {
    @Value("${pinshang.img-xd-server-url}")
    private String imgServerUrl;

    @Autowired
    private XdaComplaintOrderMapper xdaComplaintOrderMapper;

    @Autowired
    private XdaComplaintOrderItemMapper xdaComplaintOrderItemMapper;


    @Autowired
    private XdaComplaintCommodityItemPicMapper xdaCommodityItemPicMapper;

    @Autowired
    private XdaReturnOrderService xdaReturnOrderService;

    @Autowired
    private StoreService storeService;

    @Autowired
    private DictionaryClient dictionaryClient;

    @Autowired
    private TdaOrderService tdaOrderService;

    private static SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");

    /**
     * 客户投诉单列表
     *
     * @param dto
     * @return
     */
    public List<XdaComplaintOrderODTO> queryComplaintOrderList(QueryXdaComplaintDTO dto) {

        //根据店铺类型查询退款货单
        List<XdaComplaintOrderODTO> xdaComplaintOrderList =
                storeService.isTdaStore(dto.getStoreId())
                ?xdaReturnOrderService.queryComplaintOrderList(dto)
                        :xdaComplaintOrderMapper.queryComplaintOrderList(dto);
        //更新商品图片url、按商品 ID 和价格排序
        xdaComplaintOrderList.forEach(this::processComplaintOrder);

        return xdaComplaintOrderList;
    }

    private void processComplaintOrder(XdaComplaintOrderODTO xdaComplaintOrderODTO) {

        List<XdaComplaintCommodityItemDTO> itemList = xdaComplaintOrderODTO.getComplaintCommodityList();

        if (CollectionUtils.isNotEmpty(itemList)) {
            // 更新商品图片 URL
            itemList.stream()
                    .filter(item -> !StringUtil.isNullOrEmpty(item.getCommodityPicUrl())
                            && !item.getCommodityPicUrl().contains("http"))
                    .forEach(commodity -> commodity.setCommodityPicUrl(imgServerUrl + commodity.getCommodityPicUrl()));

            // 按商品 ID 和价格排序
            itemList.sort(Comparator
                    .comparingLong(XdaComplaintCommodityItemDTO::getCommodityId)
                    .thenComparing(Comparator.comparing(XdaComplaintCommodityItemDTO::getCommodityPrice).reversed()));
        }
    }

    /**
     * 投诉单取消
     *
     * @param dto
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public Boolean cancelComplaintOrder(XdaComplaintOrderCancelDTO dto) {
        Boolean tdaStore = storeService.isTdaStore(dto.getStoreId());
        if (Objects.equals(tdaStore, Boolean.TRUE)) {
            //通达客户走通达取消逻辑
            xdaReturnOrderService.cancelReturnOrder(dto);
            return Boolean.TRUE;
        }
        QYAssert.notNull(dto.getComplainId(), "取消的投诉单不存在！");
        XdaComplaint complaintDB = xdaComplaintOrderMapper.selectByPrimaryKey(dto.getComplainId());
        QYAssert.notNull(complaintDB, "投诉单不存在！");
        QYAssert.isTrue(dto.getStoreId().equals(complaintDB.getStoreId()), "只能管理当前客户下的投诉单！");
        QYAssert.isTrue(complaintDB.getComplaintHandleStatus() == 1, "投诉状态已变更，不可取消！");
        XdaComplaint xdaComplaint = new XdaComplaint();
        xdaComplaint.setUpdateTime(new Date());
        if (dto.getOperationType()) {
            cancelAllComplainOrder(complaintDB, xdaComplaint);
        } else {
            List<XdaComplaintCommodityItem> xdaComplaintCommodityItemList = xdaComplaintOrderItemMapper.selectXdaComplaintOrderItem(dto.getComplainId(), Boolean.TRUE, null);
            QYAssert.isTrue(!xdaComplaintCommodityItemList.isEmpty(), "此投诉单已没有可取消的商品！");
//            List<XdaComplaintCommodityItem> xdaComplaintCommodityItem = xdaComplaintOrderItemMapper.selectXdaComplaintOrderItem(dto.getComplainId(), Boolean.TRUE, Arrays.asList(dto.getCommodityId()));
            Map<String, XdaComplaintCommodityItem> xdaComplaintCommodityItemMap = xdaComplaintCommodityItemList.stream().collect(Collectors.toMap(x -> x.getCommodityId() + "_" + BigDecimal.valueOf(x.getCommodityPrice()).stripTrailingZeros(), q -> q));
            if (xdaComplaintCommodityItemList.size() == 1) {
                cancelAllComplainOrder(complaintDB, xdaComplaint);
            } else {
                // 重复取消的问题（一次只能取消一个）
                QYAssert.isTrue(xdaComplaintCommodityItemMap.containsKey(dto.getCommodityId()+"_"+dto.getCommodityPrice().stripTrailingZeros()), "此商品已被取消！");

                // 改造原因，数据库原来存的的是+-返回数量，后面改绝对值了，所以需要通过类型来判断正负，原来的的多个取消现在只能单个取消
                XdaComplaintCommodityItem oldItem = xdaComplaintCommodityItemMap.get(dto.getCommodityId()+"_"+dto.getCommodityPrice().stripTrailingZeros());
//                投诉类型:1少货（负值）;2退货（负值）;3多货（正值）
                BigDecimal realReturnQuantity = BigDecimal.ZERO;
                if (oldItem.getComplaintType() == XdaComplainItemTypeEnums.LESS.getCode() || oldItem.getComplaintType() == XdaComplainItemTypeEnums.RETURN.getCode()) {
                    realReturnQuantity = BigDecimal.valueOf(oldItem.getComplaintNumber()).abs();
                } else {
                    realReturnQuantity = BigDecimal.valueOf(oldItem.getComplaintNumber()).negate();
                }
                xdaComplaint.setId(complaintDB.getId());
                BigDecimal cancelTotalMoney = realReturnQuantity.multiply(dto.getCommodityPrice());
//                Double complaintTotalMoney = (BigDecimal.valueOf(complaintDB.getComplaintTotalMoney()).subtract(cancelTotalMoney)).doubleValue();
                xdaComplaint.setComplaintTotalMoney(cancelTotalMoney.doubleValue());
                xdaComplaintOrderMapper.updateXdaComplaintOrder(xdaComplaint);
                xdaComplaintOrderItemMapper.delXdaComplaintOrderItem(complaintDB.getId(), dto.getCommodityId(),dto.getCommodityPrice());
            }
        }
        return Boolean.TRUE;
    }

    /**
     * 投诉单取消修改
     *
     * @param dto
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public Boolean operateComplaintOrder(XdaComplaintOrderODTO dto) {
        checkOperateComplaintParams(dto);
        XdaComplaint xdaComplaint = new XdaComplaint();
        xdaComplaint.setUpdateTime(new Date());
        XdaComplaint complaintDB = xdaComplaintOrderMapper.selectByPrimaryKey(dto.getComplainId());
        QYAssert.notNull(complaintDB, "投诉单不存在！");
        QYAssert.isTrue(!(complaintDB.getComplaintHandleStatus() == 2L || complaintDB.getComplaintHandleStatus() == 3L), "投诉单已审核通过或已取消，不可更改！");
        switch (dto.getOperationType()) {
            case 0:
                cancelAllComplainOrder(complaintDB, xdaComplaint);
                break;
            case 1:
                cancelPartComplainOrder(dto, complaintDB, xdaComplaint);
                break;
            case 2:
                updateComplainOrder(dto, complaintDB, xdaComplaint);
                break;
            default:
                break;
        }
        return Boolean.TRUE;
    }

    /**
     * 取消全部投诉单
     *
     * @param complaintDB
     * @param xdaComplaint
     */
    @Transactional(rollbackFor = Exception.class)
    public void cancelAllComplainOrder(XdaComplaint complaintDB, XdaComplaint xdaComplaint) {
        xdaComplaint.setComplaintHandleStatus(3L);
        xdaComplaint.setId(complaintDB.getId());
        xdaComplaintOrderMapper.updateByPrimaryKeySelective(xdaComplaint);
        xdaComplaintOrderItemMapper.updateXdaComplaintOrderItem(complaintDB.getId(), Boolean.FALSE, null);
    }

    /**
     * 取消部分投诉单
     *
     * @param complaintDB
     * @param xdaComplaint
     */
    @Transactional(rollbackFor = Exception.class)
    public void cancelPartComplainOrder(XdaComplaintOrderODTO dto, XdaComplaint complaintDB, XdaComplaint xdaComplaint) {
        List<XdaComplaintCommodityItem> xdaComplaintCommodityItemList = xdaComplaintOrderItemMapper.selectXdaComplaintOrderItem(dto.getComplainId(), Boolean.TRUE, null);
        List<XdaComplaintCommodityItemDTO> complaintCommodityList = dto.getComplaintCommodityList();
        if (xdaComplaintCommodityItemList.size() == complaintCommodityList.size()) {
            cancelAllComplainOrder(complaintDB, xdaComplaint);
        } else if (xdaComplaintCommodityItemList.size() > complaintCommodityList.size()) {
            List<Long> commodityIdList = complaintCommodityList.stream().map(XdaComplaintCommodityItemDTO::getCommodityId).collect(Collectors.toList());
            xdaComplaintOrderItemMapper.updateXdaComplaintOrderItem(complaintDB.getId(), Boolean.FALSE, commodityIdList);
            xdaComplaint.setId(complaintDB.getId());
            Double complaintTotalMoney = (BigDecimal.valueOf(complaintDB.getComplaintTotalMoney()).subtract(dto.getComplaintTotalMoney())).doubleValue();
            xdaComplaint.setComplaintTotalMoney(complaintTotalMoney);
            xdaComplaintOrderMapper.updateByPrimaryKeySelective(xdaComplaint);
        } else {
            QYAssert.notNull(null, "有不存在的投诉商品！");
        }
    }


    /**
     * 修改投诉单
     *
     * @param complaintDB
     * @param xdaComplaint
     */
    @Transactional(rollbackFor = Exception.class)
    public void updateComplainOrder(XdaComplaintOrderODTO dto, XdaComplaint complaintDB, XdaComplaint xdaComplaint) {
        List<Long> commodityIdList = dto.getComplaintCommodityList().stream().map(XdaComplaintCommodityItemDTO::getCommodityId).collect(Collectors.toList());

        List<XdaComplaintCommodityItem> xdaComplaintCommodityItemList = xdaComplaintOrderItemMapper.selectXdaComplaintOrderItem(complaintDB.getId(), Boolean.TRUE, commodityIdList);

        Map<Long, List<XdaComplaintCommodityItem>> xdaComplaintCommodityOldMap = xdaComplaintCommodityItemList.parallelStream().collect(Collectors.groupingBy(XdaComplaintCommodityItem::getCommodityId));
        BigDecimal newComplaintTotalMoney = dto.getComplaintTotalMoney();
        dto.setComplaintTotalMoney(BigDecimal.ZERO);
        dto.getComplaintCommodityList().forEach(complaintCommodity -> {
            if (xdaComplaintCommodityOldMap.containsKey(complaintCommodity.getCommodityId())) {
                XdaComplaintCommodityItem xdaComplaintCommodityItemOld = xdaComplaintCommodityOldMap.get(complaintCommodity.getCommodityId()).get(0);
                Double complaintNumberOld = xdaComplaintCommodityItemOld.getComplaintNumber();
                complaintNumberOld = xdaComplaintCommodityItemOld.getComplaintType() == 3 ? -1 * complaintNumberOld : complaintNumberOld;

                BigDecimal complaintNumberOld1 = BigDecimal.valueOf(complaintNumberOld);
                QYAssert.isTrue(complaintNumberOld1.compareTo(complaintCommodity.getRealReturnQuantity()) != 0, "商品" + complaintCommodity.getCommodityName() + "修改数量与原数量相同，不予修改！");
                dto.setComplaintTotalMoney(dto.getComplaintTotalMoney().add(complaintNumberOld1.multiply(complaintCommodity.getCommodityPrice())));

            } else {
                QYAssert.notNull(null, complaintCommodity.getCommodityId() + "此投诉商品不存在！");
            }
        });

        BigDecimal complaintTotalMoneyDB = BigDecimal.valueOf(complaintDB.getComplaintTotalMoney());
        BigDecimal oldComplaintTotalMoney = dto.getComplaintTotalMoney();
        xdaComplaint.setId(complaintDB.getId());
        xdaComplaint.setComplaintTotalMoney(complaintTotalMoneyDB.add(newComplaintTotalMoney.subtract(oldComplaintTotalMoney)).doubleValue());
        xdaComplaintOrderMapper.updateByPrimaryKeySelective(xdaComplaint);
        xdaComplaintOrderItemMapper.updateXdaComplaintOrderItems(dto.getComplaintCommodityList());
    }

    /**
     * 客户可投诉的商品列表
     *
     * @param xdaComplaintCommodityDTO
     * @return
     */
    public XdaComplaintCommodityDTO queryComplaintCommodityList(XdaComplaintCommodityDTO xdaComplaintCommodityDTO) {
        Long storeId = xdaComplaintCommodityDTO.getStoreId();
        boolean isTdaStore = storeService.isTdaStore(storeId);
        if (isTdaStore) {
            xdaComplaintCommodityDTO.setBusinessType(10);
        }
        if(StringUtils.isEmpty(xdaComplaintCommodityDTO.getDeliveryDate())){
            xdaComplaintCommodityDTO.setDeliveryDate(DateUtil.getDateFormate(new Date(),"yyyy-MM-dd"));
        }

        //获取客户所有订单中可以投诉的商品明细，通达客户取已经配送完成的订单，非通达客户可以投诉配送中的订单
        List<XdaComplaintCommodityItemDTO> complaintCommodityItemList = xdaComplaintOrderMapper.queryComplaintCommodity(xdaComplaintCommodityDTO);
        if (complaintCommodityItemList.isEmpty()) {
            xdaComplaintCommodityDTO.setComplaintItemList(Collections.EMPTY_LIST);
            xdaComplaintCommodityDTO.setComplaintCompleteItemList(Collections.EMPTY_LIST);
            return xdaComplaintCommodityDTO;
        }

        //当天已投诉的商品列表
        List<XdaComplaintCommodityItemDTO> complaintCommodityCompleteItemList;
        //当天未投诉的商品列表
        List<XdaComplaintCommodityItemDTO> commodityItemList = processComplaintCommodityItemList(complaintCommodityItemList, isTdaStore, xdaComplaintCommodityDTO);
        if (isTdaStore) {
            complaintCommodityCompleteItemList = xdaReturnOrderService.queryReturnOrderByDeliveryDateAndStoreId(xdaComplaintCommodityDTO.getStoreId(), xdaComplaintCommodityDTO.getDeliveryDate(), null);
            if (CollectionUtils.isNotEmpty(complaintCommodityCompleteItemList)) {
                tdaMatchAndUpdateComplaintItems(commodityItemList, complaintCommodityCompleteItemList, xdaComplaintCommodityDTO);
            }
        } else {
            complaintCommodityCompleteItemList = xdaComplaintOrderMapper.queryComplaintCompleteCommodity(xdaComplaintCommodityDTO.getStoreId(), xdaComplaintCommodityDTO.getDeliveryDate(), xdaComplaintCommodityDTO.getComplaintType().getCode(), null);
            xdaMatchAndUpdateComplaintItems(commodityItemList, complaintCommodityCompleteItemList, xdaComplaintCommodityDTO);
        }

        // 设置排序
        sortCommodityItems(commodityItemList);
        sortCommodityItems(complaintCommodityCompleteItemList);
        // 设置结果
        xdaComplaintCommodityDTO.setComplaintItemList(commodityItemList);
        xdaComplaintCommodityDTO.setComplaintCompleteItemList(complaintCommodityCompleteItemList);

        return xdaComplaintCommodityDTO;
    }

    private void sortCommodityItems(List<XdaComplaintCommodityItemDTO> commodityItemList) {
        commodityItemList.sort(Comparator.comparingLong(XdaComplaintCommodityItemDTO::getCommodityId)
                .thenComparing(Comparator.comparing(XdaComplaintCommodityItemDTO::getCommodityPrice).reversed()));
    }

    private void tdaMatchAndUpdateComplaintItems(List<XdaComplaintCommodityItemDTO> commodityItemList, List<XdaComplaintCommodityItemDTO> complaintCommodityCompleteItemList, XdaComplaintCommodityDTO xdaComplaintCommodityDTO) {

        // 根据ComplaintTypeEnum.COMMON.getCode() 筛选已发起的非称重品列表
        List<XdaComplaintCommodityItemDTO> notWeightList = complaintCommodityCompleteItemList.stream()
                .filter(item -> Objects.equals(item.getIsWeight(), 0)
                        && (ComplaintTypeEnum.COMMON.getCode() == xdaComplaintCommodityDTO.getComplaintType().getCode()
                        ? (item.getComplaintType() == 1 || item.getComplaintType() == 3)
                        : item.getComplaintType() == 2))
                .collect(Collectors.toList());

        // 称重品的已发起的列表
        List<XdaComplaintCommodityItemDTO> isWeightList = complaintCommodityCompleteItemList.stream()
                .filter(item -> Objects.equals(item.getIsWeight(), 1))
                .collect(Collectors.toList());

        complaintCommodityCompleteItemList.clear();


        // 非称重品与已发起列表对比并更新
        for (XdaComplaintCommodityItemDTO notWeightCommodity : notWeightList) {
            Iterator<XdaComplaintCommodityItemDTO> iterator = commodityItemList.iterator();
            while (iterator.hasNext()) {
                XdaComplaintCommodityItemDTO itemDTO = iterator.next();
                if (isMatchingNotWeightItem(itemDTO, notWeightCommodity)) {
                    complaintCommodityCompleteItemList.add(itemDTO);
                    iterator.remove();
                    break;
                }
            }
        }

        // 称重品与已发起列表对比并更新
        for (XdaComplaintCommodityItemDTO isWeightCommodity : isWeightList) {
            Iterator<XdaComplaintCommodityItemDTO> iterator = commodityItemList.iterator();
            while (iterator.hasNext()) {
                XdaComplaintCommodityItemDTO itemDTO = iterator.next();
                if (isMatchingWeightItem(itemDTO, isWeightCommodity)) {
                    complaintCommodityCompleteItemList.add(itemDTO);
                    iterator.remove();
                    break;
                }
            }
        }

    }

    private void xdaMatchAndUpdateComplaintItems(List<XdaComplaintCommodityItemDTO> commodityItemList, List<XdaComplaintCommodityItemDTO> complaintCommodityCompleteItemList, XdaComplaintCommodityDTO xdaComplaintCommodityDTO) {

        if(SpringUtil.isNotEmpty(complaintCommodityCompleteItemList)){
            List<XdaComplaintCommodityItemDTO> complaintCommodityCompleteItemListCopy = BeanCloneUtils.copyTo(complaintCommodityCompleteItemList, XdaComplaintCommodityItemDTO.class);
            complaintCommodityCompleteItemList.clear();

            for (XdaComplaintCommodityItemDTO xdaComplaintCommodityItemDTO : complaintCommodityCompleteItemListCopy) {
                Iterator<XdaComplaintCommodityItemDTO> iterator = commodityItemList.iterator();
                while (iterator.hasNext()) {
                    XdaComplaintCommodityItemDTO itemDTO = iterator.next();
                    if (isMatchingNotWeightItem(itemDTO, xdaComplaintCommodityItemDTO)) {
                        complaintCommodityCompleteItemList.add(itemDTO);
                        iterator.remove();
                        break;
                    }
                }
            }
        }



    }

    private List<XdaComplaintCommodityItemDTO> processComplaintCommodityItemList(List<XdaComplaintCommodityItemDTO> complaintCommodityItemList, boolean isTdaStore, XdaComplaintCommodityDTO xdaComplaintCommodityDTO) {

        List<XdaComplaintCommodityItemDTO> commodityItemList = new ArrayList<>();

        if (isTdaStore) {
            // 非称重品处理
            Map<String, List<XdaComplaintCommodityItemDTO>> nonWeightCommodityMap = complaintCommodityItemList.stream()
                    .filter(item -> Objects.equals(item.getIsWeight(), 0))
                    .collect(Collectors.groupingBy(item -> item.getCommodityId() + "_" + item.getCommodityPrice().stripTrailingZeros()));

            // 对每个组按价格降序排序
//            nonWeightCommodityMap.forEach((key, list) -> list.sort(Comparator.comparing(XdaComplaintCommodityItemDTO::getCommodityPrice).reversed()));

            nonWeightCommodityMap.forEach((key, value) -> {
                if (!value.isEmpty()) {
                    XdaComplaintCommodityItemDTO item = value.get(0);
                    BigDecimal realDeliveryQuantity = value.stream()
                            .map(com -> com.getRealDeliveryQuantity() != null ? com.getRealDeliveryQuantity() : BigDecimal.ZERO)
                            .reduce(BigDecimal.ZERO, BigDecimal::add);

                    item.setRealDeliveryQuantity(realDeliveryQuantity);
                    item.setRealDeliveryQuantityStr(realDeliveryQuantity + item.getCommodityUnit());

                    updateCommodityPicUrl(item);
                    commodityItemList.add(item);
                }
            });

            // 称重品处理
            commodityItemList.addAll(processWeightCommodityItemList(complaintCommodityItemList));

        } else {
            // 非通达销售，原逻辑
            Map<String, List<XdaComplaintCommodityItemDTO>> complaintCommodityMap = complaintCommodityItemList.stream()
                    .collect(Collectors.groupingBy(item -> item.getCommodityId() + "_" + item.getCommodityPrice().stripTrailingZeros()));

            complaintCommodityMap.forEach((key, value) -> {
                XdaComplaintCommodityItemDTO item = value.get(0);
                BigDecimal realDeliveryQuantity = value.stream()
                        .map(com -> com.getRealDeliveryQuantity() != null ? com.getRealDeliveryQuantity() : BigDecimal.ZERO)
                        .reduce(BigDecimal.ZERO, BigDecimal::add);

                item.setRealDeliveryQuantity(realDeliveryQuantity);
                item.setRealDeliveryQuantityStr(realDeliveryQuantity + item.getCommodityUnit());
                updateCommodityPicUrl(item);

                commodityItemList.add(item);
            });
        }
        return commodityItemList;
    }

    private Collection<? extends XdaComplaintCommodityItemDTO> processWeightCommodityItemList(List<XdaComplaintCommodityItemDTO> complaintCommodityItemList) {
        return complaintCommodityItemList.stream()
                .filter(item -> Objects.equals(item.getIsWeight(), 1))
                .sorted(Comparator.comparingLong(XdaComplaintCommodityItemDTO::getCommodityId)
                        .thenComparing(Comparator.comparing(XdaComplaintCommodityItemDTO::getCommodityPrice).reversed()))
                .peek(item -> {
                    item.setRealDeliveryQuantityStr(item.getRealDeliveryQuantity() + item.getCommodityUnit());
                    updateCommodityPicUrl(item);
                })
                .collect(Collectors.toList());
    }

    private void updateCommodityPicUrl(XdaComplaintCommodityItemDTO item) {
        if (!StringUtil.isNullOrEmpty(item.getCommodityPicUrl()) && !item.getCommodityPicUrl().contains("http")) {
            item.setCommodityPicUrl(imgServerUrl + item.getCommodityPicUrl());
        }
    }

    /**
     * 提交投诉单
     *
     * @param xdaComplaintCommodityDTO
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public Boolean saveComplaintCommodity(XdaComplaintCommodityDTO xdaComplaintCommodityDTO)  {
        Long storeId = xdaComplaintCommodityDTO.getStoreId();
        boolean isTdaStore = storeService.isTdaStore(storeId);
        if (isTdaStore) {
            xdaComplaintCommodityDTO.setBusinessType(BusinessTypeEnums.TD_SALE.getCode());
        }
        checkComplaintParams(xdaComplaintCommodityDTO);
        //处理通达销售客户 退货少货
        SaveReturnOrderODTO dto = BeanCloneUtils.copyTo(xdaComplaintCommodityDTO, SaveReturnOrderODTO.class);
        dto.setReturnSource(TDaReturnSourceEnum.APP_RETURN.getCode());
        dto.setComplaintType(xdaComplaintCommodityDTO.getComplaintType().getCode());
        Boolean tDaResult = xdaReturnOrderService.generateReturnOrder(dto);
        if (Objects.nonNull(tDaResult)){
            //不为null，说明客户是通达销售，不用走原投诉单提交流程
            return tDaResult;
        }
        //客户是非通达销售，走原投诉单提交流程
        XdaComplaint xdaComplaint = xdaComplaintOrderMapper.queryComplaint(xdaComplaintCommodityDTO.getStoreId(), xdaComplaintCommodityDTO.getDeliveryDate(),xdaComplaintCommodityDTO.getComplaintType().getCode());
        XdaComplaint c = new XdaComplaint();
        Date d = new Date();
        String xdaComplaintId;
        int flag = -1;
        if (xdaComplaint == null) {
            xdaComplaintId = getUUID();
            //查询送货员(根据客户id和送货时间查询订单镜像表中的送货员id)
            Long deliveryManId = getComplaintDelivery(xdaComplaintCommodityDTO);
            //set字段, 插入投诉
            c = new XdaComplaint(xdaComplaintCommodityDTO, xdaComplaintId, deliveryManId, d);
            QYAssert.isTrue(c.getDeliveryDate() != null, "送货日期不能为空！");
            flag = xdaComplaintOrderMapper.insert(c);

        } else {
            QYAssert.isTrue(xdaComplaint.getComplaintHandleStatus() != 2, "已有" + xdaComplaintCommodityDTO.getComplaintType().getName() + "完成的投诉，今日不可再投诉！");
            xdaComplaintId = xdaComplaint.getId();
            c.setId(xdaComplaintId);
            c.setComplaintTotalMoney(xdaComplaintCommodityDTO.getComplaintTotalMoney().doubleValue());
            if (!StringUtil.isNullOrEmpty(xdaComplaintCommodityDTO.getComplaintReason())) {
                c.setComplaintRemark(xdaComplaintCommodityDTO.getComplaintReason());
            }
            flag = xdaComplaintOrderMapper.updateXdaComplaintOrder(c);
        }
        saveComplaintOrderItem(xdaComplaintCommodityDTO, xdaComplaintId, d);
        return flag > 0;
    }

    /**
     * get保存投诉送货员
     * @param xdaComplaintCommodityDTO
     * @return
     */
    private Long getComplaintDelivery(XdaComplaintCommodityDTO xdaComplaintCommodityDTO) {
        XdaComplaintDeliveryManODTO orderDeliveryMan = xdaComplaintOrderMapper.findOrderDeliveryMan(xdaComplaintCommodityDTO.getStoreId(), xdaComplaintCommodityDTO.getDeliveryDate());
        if(null != orderDeliveryMan){
           return orderDeliveryMan.getDeliveryManId();
        }else{
            //查询当前客户关联的送货员
            XdaComplaintDeliveryManODTO storeDeliveryMan = xdaComplaintOrderMapper.findStoreDeliveryMan(xdaComplaintCommodityDTO.getStoreId());
            if(null != storeDeliveryMan){
                return storeDeliveryMan.getDeliveryManId();
            }
        }
        return null;
    }

    /**
     * 保存投诉单明细
     *
     * @param xdaComplaintCommodityDTO
     * @param xdaComplaintId
     * @param d
     */
    @Transactional(rollbackFor = Exception.class)
    public void saveComplaintOrderItem(XdaComplaintCommodityDTO xdaComplaintCommodityDTO, String xdaComplaintId, Date d) {
        //生成明细
        List<XdaComplaintCommodityItemDTO> items = xdaComplaintCommodityDTO.getComplaintItemList();
//        List<XdaComplaintCommodityItem> xdaComplaintCommodityItemList = xdaComplaintOrderItemMapper.selectXdaComplaintOrderItem(xdaComplaintId, Boolean.TRUE, items.stream().map(XdaComplaintCommodityItemDTO::getCommodityId).collect(Collectors.toList()));
//        QYAssert.isTrue(xdaComplaintCommodityItemList.isEmpty(), "有重复提交的商品");
        List<XdaComplaintCommodityItem> xdaComplaintCommodityItems = new ArrayList<>(items.size());
        List<XdaComplaintCommodityItemPic> xdaComplaintCommodityItemPicItems = new ArrayList<>(items.size() * 3);
        for (XdaComplaintCommodityItemDTO it : items) {
            String itemId = getUUID();
            XdaComplaintCommodityItem item = new XdaComplaintCommodityItem(it, itemId, xdaComplaintId, d);
            item.setComplaintContent(xdaComplaintCommodityDTO.getComplaintReason());
            xdaComplaintCommodityItems.add(item);
            xdaComplaintOrderItemMapper.insert(item);
            // 投诉图片，退货时至少一张，最多3张
            if (ComplaintTypeEnum.RETURN == xdaComplaintCommodityDTO.getComplaintType()) {
                List<String> complaintPicList = it.getComplaintPicList();
                QYAssert.isTrue(!(complaintPicList == null || complaintPicList.isEmpty()), "退货商品至少上传一张图片");
                QYAssert.isTrue(complaintPicList.size() < 4, "退货商品最多上传三张图片");
                complaintPicList.forEach(picUrl -> {
                    XdaComplaintCommodityItemPic xdaComplaintCommodityItemPic = new XdaComplaintCommodityItemPic(getUUID(), itemId, picUrl);
                    xdaComplaintCommodityItemPicItems.add(xdaComplaintCommodityItemPic);
                    xdaCommodityItemPicMapper.insert(xdaComplaintCommodityItemPic);
                });
            }
        }
        QYAssert.isTrue(!xdaComplaintCommodityItems.isEmpty(), "未查询到对应的投诉商品！");
        // uid作为主键名不支持批量插入
//        xdaComplaintOrderItemMapper.insertList(xdaComplaintCommodityItems);
//        xdaCommodityItemPicMapper.insertList(xdaComplaintCommodityItemPicItems);
    }

    public static String getUUID() {
        UUID uuid = UUID.randomUUID();
        String str = uuid.toString();
        String uuidStr = "00000000" + str.replace("-", "");
        return uuidStr;
    }

    /**
     * 提交投诉单参数检验
     *
     * @param xdaComplaintCommodityDTO
     */
    private void checkComplaintParams(XdaComplaintCommodityDTO xdaComplaintCommodityDTO) {
        ComplaintTypeEnum complaintType = xdaComplaintCommodityDTO.getComplaintType();
        QYAssert.notNull(complaintType, "投诉类型不能为空！");
        QYAssert.notNull(xdaComplaintCommodityDTO.getStoreId(), "投诉人不能为空！");
        QYAssert.notNull(xdaComplaintCommodityDTO.getDeliveryDate(), "订单日期不能为空！");
        List<XdaComplaintCommodityItemDTO> complaintItemList = xdaComplaintCommodityDTO.getComplaintItemList();
        QYAssert.notNull(complaintItemList, "投诉商品不能为空！");
        QYAssert.isTrue(!complaintItemList.isEmpty(), "投诉商品列表不能为空！");
        boolean isTdaStore = Objects.equals(BusinessTypeEnums.TD_SALE.getCode(), xdaComplaintCommodityDTO.getBusinessType());
        List<Long> commodityIdList = complaintItemList.stream().map(XdaComplaintCommodityItemDTO::getCommodityId).collect(Collectors.toList());
        List<XdaComplaintCommodityQuantityDTO> quantityItemList;
        //退货需要考虑差异投诉的数量
        //差异投诉需要考虑退货的数量
        ComplaintTypeEnum convertComplaintType = ComplaintTypeEnum.RETURN == complaintType ? ComplaintTypeEnum.COMMON : ComplaintTypeEnum.RETURN;
        if(!isTdaStore){
            //非通达的进行送货日期是否已经发生过投诉单的校验，通达的在后续流程中校验
            checkIfExistsComplaintCompleteCommodity(xdaComplaintCommodityDTO.getStoreId(), xdaComplaintCommodityDTO.getDeliveryDate(),complaintType.getCode(), complaintItemList);
            quantityItemList = xdaComplaintOrderMapper.queryComplaintCompleteCommodityQuantity(xdaComplaintCommodityDTO.getStoreId(), xdaComplaintCommodityDTO.getDeliveryDate(), convertComplaintType.getCode(), commodityIdList);
        }else{
            //查询通达非称重品 已发起数量
            //此处不查通达称重品是因为，通达称重品发起退货或者投诉后，商品id+价格+实发数量三元组一样得就不允许在发生退货或者投诉了
            quantityItemList = xdaReturnOrderService.queryCompleteCommodityQuantity(xdaComplaintCommodityDTO.getStoreId(), xdaComplaintCommodityDTO.getDeliveryDate(), convertComplaintType.getCode(), commodityIdList);
        }

        Map<String, XdaComplaintCommodityQuantityDTO> commodityQuantityMap = quantityItemList.isEmpty() ? Collections.EMPTY_MAP : quantityItemList.stream().collect(Collectors.toMap(x->x.getCommodityId()+"_"+x.getCommodityPrice().stripTrailingZeros(), q -> q));
        xdaComplaintCommodityDTO.setComplaintTotalMoney(BigDecimal.ZERO);

        //构造投诉商品总金额
        complaintItemList.forEach(com -> {
            buildXdaComplaintCommodityItemDTO(commodityQuantityMap, com, xdaComplaintCommodityDTO.getComplaintType(), Boolean.FALSE);
            xdaComplaintCommodityDTO.setComplaintTotalMoney(xdaComplaintCommodityDTO.getComplaintTotalMoney().add(com.getRealReturnQuantity().multiply(com.getCommodityPrice())));

        });
    }

    private void checkIfExistsComplaintCompleteCommodity(Long storeId, String deliveryDate, int complaintTypeCode,List<XdaComplaintCommodityItemDTO> complaintItemList) {
        List<Long> commodityIdList = complaintItemList.stream().map(XdaComplaintCommodityItemDTO::getCommodityId).collect(Collectors.toList());

        List<XdaComplaintCommodityItemDTO> itemList = xdaComplaintOrderMapper.queryComplaintCompleteCommodity(storeId, deliveryDate,complaintTypeCode , commodityIdList);
        if (!itemList.isEmpty()) {
            Set<String> complaintItemsSet = complaintItemList.stream()
                    .map(item -> item.getCommodityId() + "_" + item.getCommodityPrice().stripTrailingZeros())
                    .collect(Collectors.toSet());

            for (XdaComplaintCommodityItemDTO itemDTO : itemList) {
                String itemKey = itemDTO.getCommodityId() + "_" + itemDTO.getCommodityPrice().stripTrailingZeros();
                if (complaintItemsSet.contains(itemKey)) {
                    QYAssert.notNull(null, "商品" + itemDTO.getCommodityName() + "选择的送货日期这天已被投诉，不可再投诉!");
                }
            }
        }
    }

    /**
     * 投诉单取消修改参数检验
     *
     * @param dto
     */
    private void checkOperateComplaintParams(XdaComplaintOrderODTO dto) {
        QYAssert.notNull(dto.getOrderComplaintType(), "订单投诉类型不能为空！");
        if (dto.getOperationType() == 0) {

        } else if (dto.getOperationType() == 1 || dto.getOperationType() == 2) {
            QYAssert.notNull(dto.getComplaintCommodityList(), "投诉商品不能为空！");
            QYAssert.isTrue(!dto.getComplaintCommodityList().isEmpty(), "投诉商品不能为空！");
            dto.setComplaintTotalMoney(BigDecimal.ZERO);

            dto.getComplaintCommodityList().forEach(itemDto -> {
                Boolean cancel = dto.getOperationType() == 1 ? Boolean.TRUE : Boolean.FALSE;
                buildXdaComplaintCommodityItemDTO(Collections.EMPTY_MAP, itemDto, dto.getOrderComplaintType(), cancel);
                dto.setComplaintTotalMoney(dto.getComplaintTotalMoney().add(itemDto.getRealReturnQuantity().multiply(itemDto.getCommodityPrice())));
            });

        } else {
            QYAssert.notNull(null, "不支持此种操作类型！");
        }
    }

    private void buildXdaComplaintCommodityItemDTO(Map<String, XdaComplaintCommodityQuantityDTO> commodityQuantityMap, XdaComplaintCommodityItemDTO com, ComplaintTypeEnum orderComplaintType, Boolean cancel) {

        //校验投诉商品价格和退货数量
        validateComplaintCommodity(com,orderComplaintType);

        //初始化变量
        BigDecimal realReturnQuantity = com.getRealReturnQuantity();
        BigDecimal realDeliveryQuantity = com.getRealDeliveryQuantity().abs();

        // 投诉类型:1少货（负值）;2退货（负值）;3多货（正值）;4服务;5质量
        Integer complaintType;
        if (cancel) {
            //处理取消逻辑，取消需要设置realReturnQuantity，少货、退货负值需要转为正值，多货正值需要转为负值
            complaintType = handleCancelComplaintType(com);
        } else {
            //处理常规投诉逻辑
            complaintType = handleRegularComplaintType(commodityQuantityMap, com, orderComplaintType, realDeliveryQuantity);
        }
        com.setComplaintType(complaintType);
        com.setComplaintMoney(com.getRealReturnQuantity().abs().multiply(com.getCommodityPrice()));

    }

    private Integer handleRegularComplaintType(Map<String, XdaComplaintCommodityQuantityDTO> commodityQuantityMap, XdaComplaintCommodityItemDTO com, ComplaintTypeEnum orderComplaintType, BigDecimal realDeliveryQuantity) {
        if (ComplaintTypeEnum.COMMON == orderComplaintType) {
            //处理差异投诉
            return handleDifferenceComplaintType(commodityQuantityMap, com, realDeliveryQuantity);
        } else {
            //处理退货投诉
            return handleReturnComplaintType(commodityQuantityMap, com, realDeliveryQuantity);
        }
    }

    /**
     * 处理退货投诉
     * @param commodityQuantityMap
     * @param com
     * @param realDeliveryQuantity
     * @return
     */
    private Integer handleReturnComplaintType(Map<String, XdaComplaintCommodityQuantityDTO> commodityQuantityMap, XdaComplaintCommodityItemDTO com, BigDecimal realDeliveryQuantity) {
        QYAssert.notNull(com.getQuestionType(), "投诉问题原因类型不能为空！");
        Integer complaintType = XdaComplainItemTypeEnums.RETURN.getCode();

        String commodityKey = com.getCommodityId() + "_" + com.getCommodityPrice().stripTrailingZeros();
        //先前已投诉的多货（+）少货数（-），如果此时发起退货，需要将实发数加上客户已发起的投诉中的多货（+）少货（-）的数量
        boolean hasPreviousDifferenceComplaint = commodityQuantityMap.containsKey(commodityKey);
        if (hasPreviousDifferenceComplaint) {
            BigDecimal previousDifferenceComplaintQuantity = commodityQuantityMap.get(commodityKey).getComplaintType() == XdaComplainItemTypeEnums.LESS.getCode()
                    ? commodityQuantityMap.get(commodityKey).getRealReturnQuantity().abs().negate()
                    : commodityQuantityMap.get(commodityKey).getRealReturnQuantity().abs();
            realDeliveryQuantity = realDeliveryQuantity.add(previousDifferenceComplaintQuantity);
        }

        QYAssert.isTrue(com.getRealReturnQuantity().compareTo(realDeliveryQuantity) < 1, "商品" + com.getCommodityName() + "超可退数" + com.getRealReturnQuantity().subtract(realDeliveryQuantity));
        //退货数一定是负数
        com.setRealReturnQuantity(com.getRealReturnQuantity().negate());
        return complaintType;
    }

    /**
     * 处理差异投诉
     * @param commodityQuantityMap
     * @param com
     * @param realDeliveryQuantity
     * @return
     */
    private Integer handleDifferenceComplaintType(Map<String, XdaComplaintCommodityQuantityDTO> commodityQuantityMap, XdaComplaintCommodityItemDTO com, BigDecimal realDeliveryQuantity) {
        String commodityKey = com.getCommodityId() + "_" + com.getCommodityPrice().stripTrailingZeros();
        //处理差异投诉需要找到之前是否存在退货
        boolean hasPreviousReturn = commodityQuantityMap.containsKey(commodityKey);
        BigDecimal previousReturnQuantity = hasPreviousReturn ? commodityQuantityMap.get(commodityKey).getRealReturnQuantity().abs() : BigDecimal.ZERO;

        //差异投诉实收数量
        BigDecimal realIncomeQuantity = com.getRealReturnQuantity();
        if (hasPreviousReturn) {
            QYAssert.isTrue(realIncomeQuantity.abs().compareTo(previousReturnQuantity) > -1, "商品" + com.getCommodityName() + "实收小于已退货数" + previousReturnQuantity + "，不能投诉！");
        }

        //客户多收少收数量（+多收-少收）
        BigDecimal realReturnQuantity = realIncomeQuantity.subtract(realDeliveryQuantity);
        Integer complaintType = (BigDecimal.ZERO.compareTo(realReturnQuantity) == 1) ? XdaComplainItemTypeEnums.LESS.getCode() : XdaComplainItemTypeEnums.MORE.getCode();
        com.setQuestionType(complaintType == XdaComplainItemTypeEnums.LESS.getCode() ? 9131078242860601561L : 9131078242860604176L);

        QYAssert.isTrue(BigDecimal.ZERO.compareTo(realReturnQuantity) != 0, "商品" + com.getCommodityName() + "实收与实发一致，不能投诉！");
        //少货是-，多货是+
        com.setRealReturnQuantity(realReturnQuantity);
        return complaintType;
    }

    private Integer handleCancelComplaintType(XdaComplaintCommodityItemDTO com) {
        Integer complaintType = com.getComplaintType();
        QYAssert.notNull(complaintType, "商品投诉类型不能为空！");

        BigDecimal realReturnQuantity = com.getRealReturnQuantity();
        if (complaintType == XdaComplainItemTypeEnums.LESS.getCode() || complaintType == XdaComplainItemTypeEnums.RETURN.getCode()) {
            realReturnQuantity = realReturnQuantity.abs();
        } else if (complaintType == XdaComplainItemTypeEnums.MORE.getCode()) {
            realReturnQuantity = realReturnQuantity.negate();
        } else {
            QYAssert.isTrue(Boolean.FALSE, "不支持商品" + com.getCommodityName() + "的取消类型" + complaintType);
        }
        com.setRealReturnQuantity(realReturnQuantity);
        return complaintType;
    }

    private void validateComplaintCommodity(XdaComplaintCommodityItemDTO com,ComplaintTypeEnum orderComplaintType) {
        QYAssert.notNull(com.getCommodityPrice(), "投诉商品价格不能为空！");
        BigDecimal realReturnQuantity = com.getRealReturnQuantity();
        QYAssert.notNull(realReturnQuantity, "投诉商品数量不能为空！");
        if (ComplaintTypeEnum.RETURN == orderComplaintType) {
            QYAssert.isTrue(BigDecimal.ZERO.compareTo(realReturnQuantity) != 0, "商品退货数量不能为0！");
        }
        BigDecimal realDeliveryQuantity = com.getRealDeliveryQuantity();
        QYAssert.notNull(realDeliveryQuantity, "商品实发数量不能为空！");
    }


    /**
     * 可以申请送货日期的天数 数据字典配置
     */
    public List<String> selectComplaintDeliveryDate() {
        //查询字典配置的鲜达可以申请送货日期的天数
        DictionaryODTO dictionary = dictionaryClient.getDictionaryByCode("XdaApplyDate");
        int optionValue = Integer.parseInt(dictionary.getOptionValue());

        LocalDate today = LocalDate.now();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");

        //使用IntStream生成日期流
        return IntStream.range(0,optionValue)
                .mapToObj(i -> today.minusDays(i).format(formatter))
                .collect(Collectors.toList());
    }

    public List<TdaDeliveryTimeRangeODTO> selectComplaintDeliveryDateRange(Long storeId) {
        List<TdaDeliveryTimeRangeODTO> deliveryTimeRanges = tdaOrderService.queryDeliveryTimeRangeListByStoreId(storeId);
        long id = 1;
        for (TdaDeliveryTimeRangeODTO timeRange : deliveryTimeRanges) {
            timeRange.setId(id++);
            timeRange.setDisabled(0);
        }
        return deliveryTimeRanges;
    }

    private boolean isMatchingWeightItem(XdaComplaintCommodityItemDTO item, XdaComplaintCommodityItemDTO dto) {
        String itemKey = generateWeightItemKey(item.getCommodityId(), item.getRealDeliveryQuantity(), item.getCommodityPrice());
        String dtoKey = generateWeightItemKey(dto.getCommodityId(), dto.getRealDeliveryQuantity(), dto.getCommodityPrice());
        return Objects.equals(itemKey, dtoKey);
    }

    private String generateWeightItemKey(Object commodityId, BigDecimal quantity, BigDecimal price) {
        return commodityId + "_" + quantity.stripTrailingZeros() + "_" + price.stripTrailingZeros();
    }

    private boolean isMatchingNotWeightItem(XdaComplaintCommodityItemDTO item, XdaComplaintCommodityItemDTO dto) {
        String itemKey = generateNotWeightItemKey(item.getCommodityId(), item.getCommodityPrice());
        String dtoKey = generateNotWeightItemKey(dto.getCommodityId(), dto.getCommodityPrice());
        return Objects.equals(itemKey, dtoKey);
    }

    private String generateNotWeightItemKey(Object commodityId, BigDecimal price) {
        return commodityId + "_" + price.stripTrailingZeros();
    }

}
