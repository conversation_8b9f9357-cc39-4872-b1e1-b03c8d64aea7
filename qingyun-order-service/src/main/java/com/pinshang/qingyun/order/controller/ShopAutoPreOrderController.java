package com.pinshang.qingyun.order.controller;

import com.github.pagehelper.PageInfo;
import com.pinshang.qingyun.order.dto.*;
import com.pinshang.qingyun.order.service.auto.AutoPreOrderService;
import com.pinshang.qingyun.order.vo.auto.AutoShopCommodityListVO;
import com.pinshang.qingyun.order.vo.auto.AutoShopCommodityRequestVO;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;

/**
 * @Author: liuZhen
 * @DateTime: 2022/5/13 15:50
 */
@RequestMapping("shopAutoPreOrder")
@RestController
public class ShopAutoPreOrderController {
    @Autowired
    private AutoPreOrderService autoPreOrderService;

    /**
     * @param idto
     * @return
     */
    @Deprecated
    @PostMapping("list")
    public PageInfo<AutoPreOrderODTO> List(@RequestBody AutoPreOrderIDTO idto) {
        return autoPreOrderService.list(idto);
    }

    /**
     * 导出门店加货申请单
     *
     * @param vo
     * @return
     */
    @Deprecated
    @ApiOperation(value = "导出门店加货申请单")
    @PostMapping("/exportList")
    public List<AutoPreOrderODTO> exportList(@RequestBody AutoPreOrderIDTO vo) {
        vo.initExportPage();
        PageInfo<AutoPreOrderODTO> pageDate = autoPreOrderService.list(vo);
        if (pageDate.getSize() > 0) {
            return pageDate.getList();
        }
        return new ArrayList<>();
    }

    //详情
    @ApiOperation(value = "详情")
    @GetMapping("detailByPreOrderId/{preOrderId}")
    public AutoPreOrderDetailDTO detailByPreOrderId(@PathVariable("preOrderId") Long preOderId) {
        return autoPreOrderService.detailByPreOrderId(preOderId);
    }

    //通过
    @ApiOperation(value = "审核")
    @PostMapping("auditPassOrFail")
    public List<String> auditPassOrFail(@RequestBody AutoPreOrderAuditIDTO autoPreOrderAuditIDTO) {
        return autoPreOrderService.auditPassOrFail(autoPreOrderAuditIDTO);
    }

    @ApiOperation(value = "取消审核")
    @GetMapping("cancel/{preOrderId}")
    public Boolean cancel(@PathVariable("preOrderId") Long preOrderId) {
        return autoPreOrderService.cancel(preOrderId);
    }

}
