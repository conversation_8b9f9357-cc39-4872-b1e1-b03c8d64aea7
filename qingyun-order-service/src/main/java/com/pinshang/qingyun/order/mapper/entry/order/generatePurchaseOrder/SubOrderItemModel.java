package com.pinshang.qingyun.order.mapper.entry.order.generatePurchaseOrder;

import com.pinshang.qingyun.base.po.BaseSimplePO;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Date;

/*
 * 子订单明细表
 */
@Data
public class SubOrderItemModel {

	private String itemId;
	//子订单id
	private String subOrderId;
	//商品id
	private String commodityId;
	//数量
	private BigDecimal quantity;
	//单价
	private BigDecimal price;

	private BigDecimal totalPrice;

	private String createId;

	private LocalDateTime createTime;

	private BigDecimal realReceiveQuantity;

	private Integer status;

	private BigDecimal realDeliveryQuantity;

	private String rejectReason;

	/** 0:普通订单 1：补货订单 2: 直送补货 **/
	private Integer modeType;

}
