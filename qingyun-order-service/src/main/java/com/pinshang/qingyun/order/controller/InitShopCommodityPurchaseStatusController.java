package com.pinshang.qingyun.order.controller;

import com.pinshang.qingyun.order.service.InitShopCommodityPurchaseStatusService;
import io.swagger.annotations.Api;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * @description:
 * @author: hhf
 * @time: 2022/10/11/011 10:41
 */
@RestController
@RequestMapping("/initShopCommodityPurchaseStatus")
@Api(value = "期初门店商品可采状态相关字段", tags = "InitShopCommodityPurchaseStatusController")
public class InitShopCommodityPurchaseStatusController {

    @Autowired
    private InitShopCommodityPurchaseStatusService initShopCommodityPurchaseStatusService;

    /**
     * 期初字段shop_commodity
     */
    @GetMapping(value = "/initShopCommodityPurchaseStatusShopIdAndCommodityId")
    public void initShopCommodityPurchaseStatusShopIdAndCommodityId(){
        initShopCommodityPurchaseStatusService.initShopCommodityPurchaseStatusShopIdAndCommodityId();
    }
}
