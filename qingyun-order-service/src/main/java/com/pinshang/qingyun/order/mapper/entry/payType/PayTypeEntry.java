package com.pinshang.qingyun.order.mapper.entry.payType;

import com.pinshang.qingyun.box.utils.SpringUtil;
import com.pinshang.qingyun.common.dto.DictionaryODTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * Created by Ruansi on 2019/08/15.
 */
@Data
@NoArgsConstructor
public class PayTypeEntry {
    @ApiModelProperty("充值方式")
    private List<PayTypeAndTipEntry> payTypeList;
    @ApiModelProperty("充值金额")
    private BigDecimal money;
    @ApiModelProperty("充值说明：（explain_title）")
    private String explainTitle;
    @ApiModelProperty("详细说明")
    private List<String> explainDetail;

    public PayTypeEntry(List<PayTypeAndTipEntry> payTypeList, List<DictionaryODTO> list){
        this.payTypeList = payTypeList;
        List<String> explainDetailList = new ArrayList<>();
        list.forEach(item ->{
            if("money".equals(item.getOptionCode())){
                this.money = new BigDecimal(item.getOptionValue());
            }else if("explain_title".equals(item.getOptionCode())){
                this.explainTitle = item.getOptionValue();
            }else{
                explainDetailList.add(item.getOptionValue());
            }
        });
        if(SpringUtil.isNotEmpty(explainDetailList)){
            this.explainDetail = explainDetailList;
        }
    }
}
