package com.pinshang.qingyun.order.service;

import com.pinshang.qingyun.box.utils.BeanCloneUtils;
import com.pinshang.qingyun.order.dto.OrderBillLogSaveIDTO;
import com.pinshang.qingyun.order.mapper.OrderBillLogMapper;
import com.pinshang.qingyun.order.model.OrderBillLog;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;

/**
 * <p>
 * 订单账单业务日志  服务service
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-02
 */
@Slf4j
@Service
public class OrderBillLogService {

    @Resource
    private OrderBillLogMapper orderBillLogMapper;

    /**
     * 保存 订单账单业务日志
     */
    @Async(value = "orderBillLogThreadPoolExecutor")
    @Transactional(rollbackFor = Exception.class)
    public void saveLogAsync(OrderBillLogSaveIDTO req) {
        OrderBillLog orderBillLog = BeanCloneUtils.copyTo(req, OrderBillLog.class);
        orderBillLogMapper.insert(orderBillLog);
    }

}
