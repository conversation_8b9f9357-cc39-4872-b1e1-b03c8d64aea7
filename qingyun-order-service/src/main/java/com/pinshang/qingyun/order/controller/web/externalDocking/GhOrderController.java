package com.pinshang.qingyun.order.controller.web.externalDocking;

import com.alibaba.excel.EasyExcel;
import com.github.pagehelper.PageInfo;
import com.pinshang.qingyun.base.util.ExcelUtil;
import com.pinshang.qingyun.box.utils.ConcurrentDateUtil;
import com.pinshang.qingyun.box.utils.SpringUtil;
import com.pinshang.qingyun.order.mapper.entry.externalDocking.GhOrderEntry;
import com.pinshang.qingyun.order.service.externalDocking.GhOrderService;
import com.pinshang.qingyun.order.vo.externalDocking.GhOrderVo;
import com.pinshang.qingyun.order.vo.externalDocking.RtOutboundOrderVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.text.DateFormat;
import java.text.ParseException;
import java.util.Date;

/**
 * 高校订单
 * <AUTHOR>
 * @date 2023年2月15日10:00:09
 */
@Slf4j
@RestController
@RequestMapping("/ghOrderList/web")
@Api(value = "高校订单相关接口", tags = "ghOrder", description = "高校订单相关接口")
public class GhOrderController {


    private final GhOrderService ghOrderService;


    public GhOrderController(GhOrderService ghOrderService) {
        this.ghOrderService = ghOrderService;
    }

    @ApiOperation(value = "高校订单列表分页查询", notes = "高校商品列表分页查询")
    @ApiImplicitParam(name = "ghOrderVo", value = "", required = true, paramType = "body", dataTypeClass = GhOrderVo.class)
    @RequestMapping(value = "/findGhOrderPageInfo",method = RequestMethod.POST)
    public PageInfo<GhOrderEntry> findGhOrderPageInfo(@RequestBody GhOrderVo ghOrderVo){
        return ghOrderService.findGhOrderPageInfo(ghOrderVo);
    }

    @ApiOperation(value = "导出列表", notes = "导出列表")
    @RequestMapping(value = "/export", method = RequestMethod.GET)
    public void exportExcel(GhOrderVo reqVO, HttpServletResponse response) throws IOException {
        reqVO.initExportPage();
        PageInfo<GhOrderEntry> pageInfo = ghOrderService.findGhOrderPageInfo(reqVO);
        if(SpringUtil.isNotEmpty(pageInfo.getList())){
            DateFormat dateFormat = ConcurrentDateUtil.SDF_FULL_DATE_WITHOUT_UNDERLINE.get();
            DateFormat sdfFullDate = ConcurrentDateUtil.SDF_FULL_DATE.get();
            pageInfo.getList().forEach(item->{
                try {
                    if(null != item.getOrderTime()){
                        item.setOrderTime(dateFormat.format(sdfFullDate.parse(item.getOrderTime())));
                    }
                    if(null != item.getCreateTime()){
                        item.setCreateTime(dateFormat.format(sdfFullDate.parse(item.getCreateTime())));
                    }
                    if(null != item.getManufactureDate()){
                        item.setManufactureDate(dateFormat.format(sdfFullDate.parse(item.getManufactureDate())));
                    }
                } catch (ParseException e) {
                    throw new RuntimeException(e);
                }
            });
        }
        try {
            ExcelUtil.setFileNameAndHead(response, "高校订单列表" + System.currentTimeMillis());
            EasyExcel.write(response.getOutputStream(), GhOrderEntry.class).autoCloseStream(Boolean.FALSE).sheet("高校订单列表")
                    .doWrite(pageInfo.getList());
        }catch (Exception e){
            log.error("高校订单列表导出错误", e);
            ExcelUtil.setExceptionResponse( response );
        }
    }
}
