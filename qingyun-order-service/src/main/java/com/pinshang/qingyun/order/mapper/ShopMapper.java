package com.pinshang.qingyun.order.mapper;

import com.pinshang.qingyun.base.mybatis.MyMapper;
import com.pinshang.qingyun.order.mapper.entry.StoreEntry;
import com.pinshang.qingyun.order.model.shop.Shop;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface ShopMapper extends MyMapper<Shop>{

    List<StoreEntry> selectStoreList(@Param("storeIdList") List<Long> storeIdList);

    /**
     * 查询所有门店id集合
     * @return
     */
    List<Long> findAllShopIdList();

    Integer getShopType(@Param("storeId") Long storeId);

    List<StoreEntry> listShopType(@Param("storeIds") List<Long> storeIds);

}