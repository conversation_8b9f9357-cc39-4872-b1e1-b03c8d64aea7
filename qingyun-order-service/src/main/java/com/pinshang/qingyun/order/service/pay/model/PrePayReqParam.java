package com.pinshang.qingyun.order.service.pay.model;

import com.pinshang.qingyun.base.enums.xd.XdPayTypeEnum;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 预支付请求类
 * 对接的三方支付很少没做泛化处理
 * <AUTHOR>
 * @Date 2019/11/24 16:08
 */
@Data
public class PrePayReqParam {
    private Long uid;
    private String orderCode;
    /**
     * 订单的实付金额,0.01
     */
    private BigDecimal payAmount;
    /**
     * 支付宝需要的额外参数,必填
     */
    private String payTimeout;

    /**
     * 微信需要的额外参数,必填
     */
    private String clientIp;
    /**
     * 微信小程序支付参数,必填
     */
    private String openId;
    /**
     * 如果没有openId,传jsCode;
     */
    private String jsCode;

    private XdPayTypeEnum payType;
    /**
     * 订单过期时间
     */
    private Date bizTime;

    private Long storeId;
}
