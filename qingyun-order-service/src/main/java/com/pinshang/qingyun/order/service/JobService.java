package com.pinshang.qingyun.order.service;

import com.pinshang.qingyun.box.utils.DateUtil;
import com.pinshang.qingyun.box.utils.SpringUtil;
import com.pinshang.qingyun.common.dto.XxlJobInfoODTO;
import com.pinshang.qingyun.common.dto.XxlJobQueryVoIDTO;
import com.pinshang.qingyun.common.dto.XxlJobVoIDTO;
import com.pinshang.qingyun.common.service.XxlJobClient;
import com.pinshang.qingyun.order.dto.ShopAutoOrderJobIDTO;
import com.pinshang.qingyun.order.mapper.MdShopOrderSettingMapper;
import com.pinshang.qingyun.order.mapper.OrderListMapper;
import com.pinshang.qingyun.order.mapper.SubOrderItemMapper;
import com.pinshang.qingyun.order.mapper.SubOrderMapper;
import com.pinshang.qingyun.order.mapper.entry.order.SubOrderItemCommodityPriceEntry;
import com.pinshang.qingyun.order.mapper.entry.order.generatePurchaseOrder.SubOrderEntry;
import com.pinshang.qingyun.order.model.order.OrderList;
import com.pinshang.qingyun.order.model.order.SubOrder;
import com.pinshang.qingyun.order.model.order.SubOrderItem;
import com.pinshang.qingyun.order.service.auto.ShopAutoOrderService;
import com.pinshang.qingyun.order.util.OrderTimeUtil;
import com.pinshang.qingyun.storage.service.CommoditySupplierClient;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Author: sk
 * @Date: 2021/6/10
 */
@Slf4j
@Service
public class JobService {

    @Autowired
    private MdShopOrderSettingMapper mdShopOrderSettingMapper;

    @Autowired
    private XxlJobClient xxlJobClient;
    @Lazy
    @Autowired
    private TakeCardCreateOrderService takeCardCreateOrderService;

    @Autowired
    private PurchaseService purchaseService;

    @Autowired
    private CommoditySupplierClient commoditySupplierClient;
    @Lazy
    @Autowired
    private ShopAutoOrderService shopAutoOrderService;
    @Autowired
    private CommodityFreshOrderService commodityFreshOrderService;

    @Autowired
    private SubOrderMapper subOrderMapper;

    @Autowired
    private OrderListMapper orderListMapper;

    @Autowired
    private SubOrderItemMapper subOrderItemMapper;
    /**
     * 创建提货卡 job
     * @return
     */
    public Boolean createTakeCardOrderJob() {

        String nowHour = DateUtil.getDateFormate(new Date(),"HH:mm");
        log.info("提货卡预约单提交订单开始种植任务--------------------------------------------------------------" + new Date());
        List<String> orderTimeList = mdShopOrderSettingMapper.getEndTimeList(nowHour);
        if(CollectionUtils.isEmpty(orderTimeList)){
            return Boolean.TRUE;
        }
        XxlJobVoIDTO xxlJobVoIDTO = getXxlJobVoIDTO(orderTimeList);
        xxlJobClient.replaceJob(xxlJobVoIDTO);

        XxlJobQueryVoIDTO vo = new XxlJobQueryVoIDTO();
        vo.setExecutorHandler("executeTakeCardOrderJobHandler");
        List<XxlJobInfoODTO> jobList = xxlJobClient.queryJobList(vo);
        if(CollectionUtils.isNotEmpty(jobList)){
            for(XxlJobInfoODTO info : jobList){
                log.info("提货卡 jobGroup：" + info.getJobGroup() + " 描述：" + info.getJobDesc() + " jobHandler："+ info.getExecutorHandler() + " cron：" + info.getJobCron());
            }
        }
        return Boolean.TRUE;
    }

    public void addTakeCardJob(List<String> orderTimeList){
        XxlJobVoIDTO xxlJobVoIDTO = getXxlJobVoIDTO(orderTimeList);
        xxlJobClient.setAddJob(xxlJobVoIDTO);
    }

    @NotNull
    private XxlJobVoIDTO getXxlJobVoIDTO(List<String> orderTimeList) {
        XxlJobVoIDTO xxlJobVoIDTO = new XxlJobVoIDTO();
        xxlJobVoIDTO.setJobHandler("executeTakeCardOrderJobHandler");
        xxlJobVoIDTO.setJobDesc("提货卡创建订单");
        xxlJobVoIDTO.setAuthor("苏坤");
        xxlJobVoIDTO.setRunTimes(orderTimeList);
        return xxlJobVoIDTO;
    }

    /**
     * 提货卡job执行
     * @param orderTime
     * @return
     * @throws Throwable
     */
    public Boolean executeTakeCardOrderJob(String orderTime) throws Throwable {
        takeCardCreateOrderService.createTakeCardOrder(orderTime);
        log.info("提货卡订单执行提交时间：" + orderTime);

        // 日日鲜功能停用
        // 执行日日鲜订单创建
        //commodityFreshOrderService.createCommodityFreshOrder(orderTime);
        return Boolean.TRUE;
    }



    /**
     * 创建生成采购单job
     * @return
     */
    public Boolean createPurchaseOrderJob() {
        log.info("创建生成采购单种植任务--------------------------------------------------------------" + new Date());
        List<String> supplierEndTimeList = commoditySupplierClient.getSupplierEndTime();
        if(CollectionUtils.isEmpty(supplierEndTimeList)){
            return Boolean.TRUE;
        }
        List<String> endTimeList = new ArrayList<>();
        supplierEndTimeList.forEach(endTime->{
            if(StringUtils.isNotBlank(endTime) && !endTimeList.contains(endTime)){
                endTimeList.add(endTime);
            }
        });

        XxlJobVoIDTO xxlJobVoIDTO = new XxlJobVoIDTO();
        xxlJobVoIDTO.setJobHandler("executePurchaseOrderJobHandler");
        xxlJobVoIDTO.setJobDesc("生成采购单");
        xxlJobVoIDTO.setAuthor("苏坤");
        xxlJobVoIDTO.setRunTimes(endTimeList);
        xxlJobClient.replaceJob(xxlJobVoIDTO);

        XxlJobQueryVoIDTO vo = new XxlJobQueryVoIDTO();
        vo.setExecutorHandler("executePurchaseOrderJobHandler");
        List<XxlJobInfoODTO> jobList = xxlJobClient.queryJobList(vo);
        if(CollectionUtils.isNotEmpty(jobList)){
            for(XxlJobInfoODTO info : jobList){
                log.info("生成采购单jobGroup：" + info.getJobGroup() + " 描述：" + info.getJobDesc() + " jobHandler："+ info.getExecutorHandler() + " cron：" + info.getJobCron());
            }
        }
        return Boolean.TRUE;
    }

    /**
     * 执行生成采购单job
     * @param orderTime
     * @return
     * @throws Throwable
     */
    public Boolean executePurchaseOrderJob(String orderTime) {
        List <SubOrderEntry> subOrderEntries = purchaseService.getSubOrderEntries(orderTime);
        log.info("生成直送采购单,供应商结束时间endTime: " +orderTime +", 需要生成的采购单数：" + (subOrderEntries == null ? 0 : subOrderEntries.size()));

        subOrderEntries.stream().forEach(entry -> purchaseService.generatePurchaseOrder(entry));
        return Boolean.TRUE;
    }



    /**
     * 门店自动订货job创建
     * @return
     */
    public Boolean createShopAutoOrderJob() {

        String nowHour = DateUtil.getDateFormate(new Date(),"HH:mm");
        log.info("创建门店自动订货job 开始种植任务--------------------------------------------------------------" + new Date());
        List<String> orderTimeList = mdShopOrderSettingMapper.getEndTimeList(nowHour);
        if(CollectionUtils.isEmpty(orderTimeList)){
            return Boolean.TRUE;
        }

        // 种植job时间提前30分钟
        List<String> minusTimeList = new ArrayList<>();
        for(String time : orderTimeList){
            minusTimeList.add(OrderTimeUtil.addMinute(time,-30));
        }

        XxlJobVoIDTO xxlJobVoIDTO = new XxlJobVoIDTO();
        xxlJobVoIDTO.setJobHandler("executeShopAutoOrderJobHandler");
        xxlJobVoIDTO.setJobDesc("创建门店自动订货job");
        xxlJobVoIDTO.setAuthor("苏坤");
        xxlJobVoIDTO.setRunTimes(minusTimeList);
        xxlJobClient.replaceJob(xxlJobVoIDTO);

        XxlJobQueryVoIDTO vo = new XxlJobQueryVoIDTO();
        vo.setExecutorHandler("executeShopAutoOrderJobHandler");
        List<XxlJobInfoODTO> jobList = xxlJobClient.queryJobList(vo);
        if(CollectionUtils.isNotEmpty(jobList)){
            for(XxlJobInfoODTO info : jobList){
                log.info("创建门店自动订货job jobGroup：" + info.getJobGroup() + " 描述：" + info.getJobDesc() + " jobHandler："+ info.getExecutorHandler() + " cron：" + info.getJobCron());
            }
        }
        return Boolean.TRUE;
    }

    /**
     * 门店自动订货job执行
     * @param orderTime
     * @return
     * @throws Throwable
     */
    public Boolean executeShopAutoOrderJob(ShopAutoOrderJobIDTO idto){
        log.info("门店自动订货job执行提交时间：" + idto.getOrderTime());
        return shopAutoOrderService.createShopAutoOrder(idto);
    }

    public void subOrderItemCommodityPriceJob(String date){
        if(SpringUtil.hasTextAndNotNull(date)){
            List<SubOrderItemCommodityPriceEntry> subOrderItemCommodityPriceEntries = subOrderMapper.selectSubOrderItemCommodityPrice(date);
            if(SpringUtil.isEmpty(subOrderItemCommodityPriceEntries)){
                return;
            }
            List<Long> orderIdList = subOrderItemCommodityPriceEntries.stream().map(SubOrderItemCommodityPriceEntry::getOrderId).distinct().collect(Collectors.toList());

            List<OrderList> orderListList = orderListMapper.selectOrderListByOrderIdList(orderIdList);
            Map<String, SubOrderItemCommodityPriceEntry> subOrderMap = subOrderItemCommodityPriceEntries.stream()
                    .collect(Collectors.toMap(item -> item.getOrderId()+ "_" + item.getCommodityId() + "_"  + item.getQuantity().stripTrailingZeros().toPlainString(), item -> item, (item1, item2) -> item1));
            Map<Long, List<OrderList>> orderListGroup = orderListList.stream().collect(Collectors.groupingBy(OrderList::getOrderId));
            Map<Long, BigDecimal> subOrderTotalPrice = new HashMap<>();;
            orderListGroup.forEach((item, orderList) -> {
                orderListList.forEach(orderItem->{
                    SubOrderItem subOrderItem = new SubOrderItem();
                    String key = orderItem.getOrderId()+ "_" + orderItem.getCommodityId() + "_" + orderItem.getCommodityNum().stripTrailingZeros().toPlainString();
                    /**
                     * 判断商品数量和商品订单id
                     * 当赠品和订单数量一致时 排除取赠品金额 去更新子表订单金额
                     * 判断是否参加促销  促销商品才会显示有误
                     * 判断是否为赠品  赠品金额为零
                     */
                    if(subOrderMap.containsKey(key)
                            && null != orderItem.getGiftModelId()
                            && orderItem.getCommodityPrice().compareTo(BigDecimal.ZERO)!=0){
                        SubOrderItemCommodityPriceEntry subOrderItemCommodityPriceEntry = subOrderMap.get(key);
                        Long id = subOrderItemCommodityPriceEntry.getId();
                        BigDecimal commodityPrice = orderItem.getCommodityPrice();
                        BigDecimal commodityNum = orderItem.getCommodityNum();
                        Long subOrderId = subOrderItemCommodityPriceEntry.getSubOrderId();
                        BigDecimal sumTotalPrice = subOrderItemCommodityPriceEntry.getSumTotalPrice();
                        subOrderItem.setId(id);
                        subOrderItem.setPrice(commodityPrice);
//                        BigDecimal totalPrice = subOrderItem.getPrice().multiply(commodityNum).setScale(2, BigDecimal.ROUND_HALF_UP);
                        subOrderItem.setTotalPrice(orderItem.getTotalPrice());
                        subOrderItem.setUpdateTime(new Date());

                        //当前subOrder需要减去的总金额
                        BigDecimal subtract = orderItem.getTotalPrice().subtract(subOrderItemCommodityPriceEntry.getTotalPrice());
                        if(subOrderTotalPrice.containsKey(subOrderId)){
                            BigDecimal add = subOrderTotalPrice.get(subOrderId).add(subtract);
                            subOrderTotalPrice.put(subOrderId,add);
                        }else {
                            BigDecimal subtractSumTotalPrice = sumTotalPrice.add(subtract);
                            subOrderTotalPrice.put(subOrderId,subtractSumTotalPrice);
                        }
                        subOrderItemMapper.updateByPrimaryKeySelective(subOrderItem);
                    }
                });
            });
            subOrderTotalPrice.forEach((key,value)->{
                SubOrder subOrder = new SubOrder();
                subOrder.setId(key);
                subOrder.setTotalPrice(value);
                subOrder.setUpdateTime(new Date());
                subOrderMapper.updateByPrimaryKeySelective(subOrder);
            });
        }
    }
}
