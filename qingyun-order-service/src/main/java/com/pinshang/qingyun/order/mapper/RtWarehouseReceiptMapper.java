package com.pinshang.qingyun.order.mapper;

import com.pinshang.qingyun.order.mapper.entry.externalDocking.RtWarehouseReceiptEntry;
import com.pinshang.qingyun.order.vo.externalDocking.RtWarehouseReceiptVo;
import org.apache.ibatis.annotations.Mapper;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 大润发出库单
 * <AUTHOR>
 * @date 2023年2月15日10:00:09
 */
@Mapper
@Repository
public interface RtWarehouseReceiptMapper {

    /**
     * 大润发入库单列表
     * @param vo
     * @return
     */
    List<RtWarehouseReceiptEntry> selectRtWarehouseReceiptList(RtWarehouseReceiptVo vo);
}
