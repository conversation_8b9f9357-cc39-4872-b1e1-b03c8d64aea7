package com.pinshang.qingyun.order.mapper;

import com.pinshang.qingyun.base.mybatis.MyMapper;
import com.pinshang.qingyun.order.model.user.AppUser;
import org.apache.ibatis.annotations.Param;

/**
 * <AUTHOR>
 * @Date 2018/10/10 10:23
 */
public interface AppUserMapper extends MyMapper<AppUser> {
    /**
     * 开户
     * @param user
     * @return
     */
    int openAccount(AppUser user);

    /**
     * 根据名字或主键更新用户:密码/状态/modify
     * @param user
     * @return
     */
    int editByName(AppUser user);

    /**
     * 根据storeCode查询
     * @param storeCode
     * @return
     */
    AppUser findByStoreCode(@Param("storeCode") String storeCode);
}
