package com.pinshang.qingyun.order.mapper;

import com.pinshang.qingyun.base.mybatis.MyMapper;
import com.pinshang.qingyun.order.mapper.entry.orderStatistics.OrderCompanyCommodityStatisticsEntry;
import com.pinshang.qingyun.order.model.statistics.OrderCompanyCommodityStatistics;
import com.pinshang.qingyun.order.vo.orderStatistics.OrderCompanyCommodityStatisticsVo;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Date;

@Repository
public interface OrderCompanyCommodityStatisticsMapper extends MyMapper<OrderCompanyCommodityStatistics>{
	List<OrderCompanyCommodityStatisticsEntry> findListByCondition(OrderCompanyCommodityStatisticsVo vo);

    List<OrderCompanyCommodityStatistics> queryOrderCommodityStatisticsByOrderTime(@Param("orderTime") Date orderTime);
}

