/*
 * tramy.cn Inc.
 * Copyright (c) 2021-2024 All Rights Reserved.
 */
package com.pinshang.qingyun.order.service.tda;

import com.pinshang.qingyun.order.dto.xda.tda.SaveReturnOrderODTO;

/**
 * <p>
 * 退货单策略类
 * </p>
 *
 * <AUTHOR> shenyang
 * @version : 1.0.0
 * @history : modify history
 * <author>              <time>              <version>              <desc>
 * @since : 2024/05/17 23:00
 */
public interface ReturnOrderStrategy {
    Boolean execute(SaveReturnOrderODTO saveReturnOrderDTO);

    Integer getReturnSourceType();
}