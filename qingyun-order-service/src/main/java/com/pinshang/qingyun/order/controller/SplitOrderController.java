package com.pinshang.qingyun.order.controller;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.pinshang.qingyun.base.constant.QingyunConstant;
import com.pinshang.qingyun.order.service.SplitOrderService;
import com.pinshang.qingyun.order.vo.orderMonitor.NonSplitCommodityDetail;
import com.pinshang.qingyun.order.vo.orderMonitor.NonSplitOrderQueryVo;
import com.pinshang.qingyun.order.vo.orderMonitor.NonSplitOrderVo;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import com.pinshang.qingyun.base.configure.expand.QYAssert;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * 大仓订单监控
 * 
 * <AUTHOR>
 */
@RestController
@RequestMapping("/splitOrder")
public class SplitOrderController {

    @Autowired
    private SplitOrderService splitOrderService;
    @Resource
    private RedissonClient redissonClient;

    /**
     * 未拆单查询列表
     * @param nonSplitOrderQueryVo
     * @return
     */
    @RequestMapping(value="/manualSplitOrderByPage",method = RequestMethod.POST)
    public PageInfo<NonSplitOrderVo> manualSplitOrderByPage(@RequestBody NonSplitOrderQueryVo nonSplitOrderQueryVo){
        QYAssert.isTrue(nonSplitOrderQueryVo.getOrderTime()!=null,"送货日期不能为空");
        return  PageHelper.startPage(nonSplitOrderQueryVo.getPageNo(), nonSplitOrderQueryVo.getPageSize()).doSelectPageInfo(() -> {
            splitOrderService.findNonSplitOrderList(nonSplitOrderQueryVo);
        });
    }

    /**
     * 未拆单详情
     * @param orderId
     * @return
     */
    @RequestMapping(value = "/findByOrderId/{orderId}", method = RequestMethod.GET)
    public List<NonSplitCommodityDetail> findNonSplitByOrderId(@PathVariable("orderId") Long orderId){
        return splitOrderService.findNonSplitByOrderId(orderId);
    }

    /**
     * 执行拆单
     * @param nonSplitOrderQueryVo
     */
    @RequestMapping(value="/manualSplitOrder",method = RequestMethod.POST)
    public void manualSplitOrder(@RequestBody  NonSplitOrderQueryVo nonSplitOrderQueryVo){
        RLock lock = redissonClient.getLock(QingyunConstant.MANUAL_SPLIT_ORDER);
        if (!lock.isLocked()) {
            lock.lock();
            try {
                QYAssert.isTrue(nonSplitOrderQueryVo.getOrderTime() != null, "送货日期不能为空");
                splitOrderService.manualSplitOrder(nonSplitOrderQueryVo);
            } finally {
                lock.unlock();
            }
        }
    }

    /**
     * 定时任务执行未拆单或者少拆单的单子
     * 拆单补偿只针对客服下单、生鲜下单、鲜达APP下单
     * @param createTime yyyy-MM-dd
     * @return
     */
    @RequestMapping(value="/splitOrderByJob",method = RequestMethod.GET)
    public Boolean splitOrderByJob(@RequestParam(value = "createTime", required = false) String createTime){
        return splitOrderService.splitOrderByJob(createTime);
    }

    /**
     * 定时备份、清理SplitOrder表3天前的数据
     * @return
     */
//    @GetMapping("/removeSplitOrderInfo")
//    public Integer removeSplitOrderInfo() {
//        return splitOrderService.removeSplitOrderInfo();
//    }
}
