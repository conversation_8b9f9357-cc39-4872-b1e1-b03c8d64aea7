package com.pinshang.qingyun.order.service;


import com.pinshang.qingyun.base.configure.expand.QYAssert;
import com.pinshang.qingyun.box.utils.*;
import com.pinshang.qingyun.order.mapper.*;
import com.pinshang.qingyun.order.model.order.*;
import com.pinshang.qingyun.order.vo.mock.MockOrderItemVo;
import com.pinshang.qingyun.order.vo.mock.MockOrderVo;
import com.pinshang.qingyun.order.vo.order.CreateOrderVo;
import com.pinshang.qingyun.order.vo.order.OrderItemRequestVo;
import com.pinshang.qingyun.order.vo.order.OrderRequestVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import java.math.BigDecimal;
import java.util.*;

@Slf4j
@Service
public class MockOrderService {
    @Autowired
    private OrderMapper orderMapper;
    @Autowired
    private OrderListMapper orderListMapper;
    @Autowired
    private OrderListGiftMapper orderListGiftMapper;
    @Autowired
    private OrderService orderService;

    /**
     * 模拟下订单，不考虑产品价格方案等因素
     * @param vo
     */
    @Transactional(rollbackFor = Exception.class)
    public void mockCreateOrder(MockOrderVo vo){
        QYAssert.isTrue(vo!=null && CollectionUtils.isNotEmpty(vo.getItemList()),"下单参数不能为空");
        List<OrderList> orderLists = new ArrayList<>();
        List<OrderListGift> orderListGifts = new ArrayList<>();

        BigDecimal totalPrice = BigDecimal.ZERO;
        for(MockOrderItemVo item : vo.getItemList()){
            BigDecimal price = BigDecimal.valueOf(RandomUtil.generateDouble(10,99));
            item.setPrice(price);
            totalPrice = totalPrice.add(price.multiply(item.getQuantity()));
            OrderList orderList = MockOrderItemVo.convertOrderList(item);
            orderLists.add(orderList);
            OrderListGift orderListGift = new OrderListGift();
            SpringUtil.copyProperties(orderList,orderListGift);
            orderListGifts.add(orderListGift);
        }
        for(Long storeId : vo.getStoreIdList()){
//            Order order = MockOrderVo.mockInit(vo,storeId,totalPrice);
//            orderMapper.insertSelective(order);
//            Long orderId = order.getId();
//            for(int i=0;i<orderLists.size();i++){
//                orderLists.get(i).setOrderId(orderId);
//                orderListGifts.get(i).setOrderId(orderId);
//            }
//            orderListMapper.insertList(orderLists);
//            orderListGiftMapper.insertList(orderListGifts);

            CreateOrderVo vo1 = new CreateOrderVo();
            vo1.setStoreId(999872035591509085L);
            vo1.setEnterpriseId(78L);
            vo1.setCreateId(1101L);
            vo1.setCreateName("测试");
            vo1.setInternal(false);
            vo1.setOrderTime("2024-01-03");
            vo1.setDeliveryBatch("1");
            List<CreateOrderVo.CreateOrderItemIDTO> itemList1 = new ArrayList<>();
            List<Long> commodityIds1 = Arrays.asList(
                    116176507808327216L, //标品
                    413791180690901056L,345139349439155712L // 称重包装品
            );
            commodityIds1.forEach(cId->{
                CreateOrderVo.CreateOrderItemIDTO itemVo = new CreateOrderVo.CreateOrderItemIDTO();
                itemVo.setCommodityId(cId);
                itemVo.setQuantity(BigDecimal.valueOf(3));
                itemList1.add(itemVo);
            });
            vo1.setItems(itemList1);

            OrderRequestVo orderRequestVo = new OrderRequestVo();
            orderRequestVo.setStoreId("999872035591509085");
            orderRequestVo.setOrderTime("2024-01-03");
            orderRequestVo.setEnterpriseId(78L);
            orderRequestVo.setUserId(1101L);
            orderRequestVo.setCreateName("测试");
            orderRequestVo.setLogisticsModel(2);
            orderRequestVo.setSupplierId(161L);
            orderRequestVo.setWarehouseId(10000L);
            orderRequestVo.setDeliveryBatch("1");
            orderRequestVo.setDeleveryTimeRange("0-1");
            orderRequestVo.setConsignmentId(-1L);

            List<OrderItemRequestVo> itemsList = new ArrayList<OrderItemRequestVo>();
            commodityIds1.forEach(i -> {
                OrderItemRequestVo item = new OrderItemRequestVo();
                item.setProductId(i.toString());
                item.setProductNum(BigDecimal.valueOf((int)(1+Math.random()*10)));
                itemsList.add(item);
            });
            orderRequestVo.setItemsList(itemsList);
            orderService.createOrder(orderRequestVo, vo1);
            System.out.println("=========="+(1+1));
        }
    }


}
