package com.pinshang.qingyun.order.controller.web.externalDocking;

import com.github.pagehelper.PageInfo;
import com.pinshang.qingyun.order.mapper.entry.externalDocking.RtWarehouseReceiptEntry;
import com.pinshang.qingyun.order.service.externalDocking.RtWarehouseReceiptService;
import com.pinshang.qingyun.order.util.RtFileUtil;
import com.pinshang.qingyun.order.vo.externalDocking.RtWarehouseReceiptVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.CellType;
import org.apache.poi.xssf.usermodel.*;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;

/**
 * 大润发入库单
 * <AUTHOR>
 * @date 2023年2月15日10:00:09
 */
@RestController
@RequestMapping("/rtWarehouseReceipt")
@Api(value = "大大润发入库单相关接口", tags = "rtWarehouseReceipt", description = "大润发入库单相关接口")
public class RtWarehouseReceiptController {

    private final RtWarehouseReceiptService rtWarehouseReceiptService;

    public RtWarehouseReceiptController(RtWarehouseReceiptService rtWarehouseReceiptService){
        this.rtWarehouseReceiptService = rtWarehouseReceiptService;
    }

    @ApiOperation(value = "大润发入库单列表分页查询", notes = "大润发入库单列表分页查询")
    @ApiImplicitParam(name = "vo", value = "", required = true, paramType = "body", dataTypeClass = RtWarehouseReceiptVo.class)
    @RequestMapping(value = "/selectRtWarehouseReceiptPageInfo",method = RequestMethod.POST)
    public PageInfo<RtWarehouseReceiptEntry> selectRtWarehouseReceiptPageInfo(@RequestBody RtWarehouseReceiptVo vo){
        return rtWarehouseReceiptService.selectRtWarehouseReceiptPageInfo(vo);
    }
    @ApiOperation(value = "导出列表", notes = "导出列表")
    @RequestMapping(value = "/export", method = RequestMethod.GET)
    public void exportExcel(RtWarehouseReceiptVo reqVO, HttpServletResponse response) throws IOException {
        File newFile = RtFileUtil.createNewFile(1);

        // 新文件写入数据，并下载*****************************************************
        InputStream is;
        XSSFWorkbook workbook = null;
        XSSFSheet sheetCommodity = null;
        try {
            // 将excel文件转为输入流
            is = new FileInputStream(newFile);
            // 创建个workbook，
            workbook = new XSSFWorkbook(is);
            // 获取第一个sheet
            sheetCommodity = workbook.getSheetAt(0);
        } catch (Exception e1) {
            e1.printStackTrace();
        }
        FileOutputStream fos = new FileOutputStream(newFile);
        // 写数据
        try {
            if (null != sheetCommodity) {
                XSSFRow row = sheetCommodity.getRow(1);
                if (row == null) {
                    row = sheetCommodity.createRow(1);
                }
                XSSFCell cell = row.getCell(0);
                if (cell == null) {
                    cell = row.createCell(0);
                }
                reqVO.initExportPage();
                PageInfo<RtWarehouseReceiptEntry> list = rtWarehouseReceiptService.selectRtWarehouseReceiptPageInfo(reqVO);
                List<RtWarehouseReceiptEntry> content = list.getList();

                String[][] values = new String[content.size()][];
                for (int i = 0; i < content.size(); i++) {
                    values[i] = new String[10];
                    // 将对象内容转换成string
                    RtWarehouseReceiptEntry entry = content.get(i);
                    values[i][0] = entry.getSupplier();
                    values[i][1] = entry.getProductSKU();
                    values[i][2] = entry.getProductName();
                    values[i][3] = entry.getOperator();
                    values[i][4] = entry.getWarehousingTime();
                    values[i][5] = entry.getManufactureTime();
                    values[i][6] = entry.getNumber();
                    values[i][7] = entry.getUnitPrice();
                    values[i][8] = entry.getCertificate();
                    values[i][9] = entry.getVoucher();
                }
                //插入数据
                for(int i=0;i<values.length;i++){
                    row = sheetCommodity.createRow(i + 2);
                    for(int j=0;j<values[i].length;j++){
                        cell = row.createCell(j);

                        if(StringUtils.isNotEmpty(values[i][j])){
                            cell.setCellValue(values[i][j]);
                            if(j==4){
                                XSSFCellStyle style7 = workbook.createCellStyle();
                                style7.setDataFormat(workbook.createDataFormat().getFormat( "yyyy/MM/dd hh:mm:ss" ));
                                SimpleDateFormat sdf = new SimpleDateFormat("yyyy/MM/dd hh:mm:ss");
                                cell.setCellValue(sdf.parse(values[i][j]));
                                cell.setCellStyle(style7);
                            }
                            if(j==5){
                                XSSFCellStyle style5 = workbook.createCellStyle();
                                style5.setDataFormat(workbook.createDataFormat().getFormat( "yyyy/MM/dd" ));

                                Date date = new SimpleDateFormat("yyyy-MM-dd").parse(values[i][j]);
                                cell.setCellValue(new SimpleDateFormat("yyyy/MM/dd").format(date));
                                cell.setCellStyle(style5);
                            }
                            if(j==6||j==7){
                                cell.setCellValue(Double.parseDouble(values[i][j]));
                                cell.setCellType(CellType.NUMERIC);
                            }

                        }
                    }
                }
            }

            workbook.write(fos);
            fos.flush();
            fos.close();
            // 下载
            RtFileUtil.exportExcel(1,response, newFile, reqVO.getStoreShortName());
        } catch (Exception e) {
            e.printStackTrace();
        }
        // 删除创建的新文件
        RtFileUtil.deleteFile(newFile);
    }
}
