package com.pinshang.qingyun.order.service.bigShop;/**
 * @Author: sk
 * @Date: 2025/8/4
 */

import com.pinshang.qingyun.base.enums.YesOrNoEnums;
import com.pinshang.qingyun.base.enums.shop.ShopTypeEnums;
import com.pinshang.qingyun.base.spring.SpringBeanFinder;
import com.pinshang.qingyun.box.utils.DateUtil;
import com.pinshang.qingyun.order.constant.RedissKeyConstant;
import com.pinshang.qingyun.order.constant.ThreadPoolBeanConstants;
import com.pinshang.qingyun.order.enums.ShopReceiveTypeEnums;
import com.pinshang.qingyun.order.mapper.bigShop.DdReceiveDocMapper;
import com.pinshang.qingyun.order.model.bigShop.DdReceiveDoc;
import com.pinshang.qingyun.order.service.ShopReceiveSettingService;
import com.pinshang.qingyun.order.service.ShopService;
import com.pinshang.qingyun.order.vo.shop.Quantity;
import com.pinshang.qingyun.order.vo.shop.ReceiveOrderVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.Nullable;
import org.redisson.api.RAtomicLong;
import org.redisson.api.RBucket;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date 2025年08月04日 下午2:14
 */
@Slf4j
@Service
public class BigShopAutoReceiveService {

    @Autowired
    private RedissonClient redissonClient;
    @Autowired
    private ShopReceiveSettingService shopReceiveSettingService;
    @Autowired
    private ShopService shopService;
    @Autowired
    private DdReceiveDocMapper ddReceiveDocMapper;
    @Autowired
    private BigShopAutoReceiveDetailService bigShopAutoReceiveDetailService;

    /**
     * 大店自动收货，根据大收货单开线程池。一次只收一个大收货单下面的订单
     * @return
     */
    public Boolean autoReceiveBigShopOrder(String orderTime){
        // 校验重复任务
        String lockKey =  "order:automaticReceiptBigShop";
        RAtomicLong lockKeyRa = redissonClient.getAtomicLong(lockKey);
        long automaticLock = lockKeyRa.incrementAndGet();
        if (automaticLock == 1) {
            lockKeyRa.expire(1, TimeUnit.MINUTES);
        }
        if (automaticLock > 1) {
            log.warn("大店自动收货任务正在执行中");
            return false;
        }

        // 获取开启自动收货的大店门店
        List<Long> shopIdList = getDdAutoShopIdList();
        if (shopIdList == null) return false;

        // 获取大店收货单
        List<DdReceiveDoc> ddReceiveDocList = getDdReceiveDocs(orderTime, shopIdList);
        if (ddReceiveDocList == null) return false;

        // 获取线程池
        ThreadPoolTaskExecutor threadPool = (ThreadPoolTaskExecutor) SpringBeanFinder.getBean(ThreadPoolBeanConstants.ORDER_THREADPOOL);

        for(DdReceiveDoc ddReceiveDoc : ddReceiveDocList) {
            // 创建任务并提交到线程池中
            threadPool.execute(new Runnable() {
                @Override
                public void run() {
                    // 和手动收货共用一个锁
                    // com.pinshang.qingyun.order.controller.bigShop.BigShopReceiveController.addBigShopReceive
                    RLock lock = redissonClient.getLock("order:addBigShopReceive:" + ddReceiveDoc.getId());
                    if (lock.tryLock()) {
                        try {
                            bigShopAutoReceiveDetailService.bigShopAutoReceiveDetail(ddReceiveDoc);
                        }catch (Exception e){
                            log.error("大店自动收货任务执行异常, docId {}  docCode {}", ddReceiveDoc.getId(), ddReceiveDoc.getDocCode(), e);
                        } finally {
                            lock.unlock();
                        }
                    }else  {
                        log.warn("大店手动收货任务正在执行中, docId {} ", ddReceiveDoc.getId());
                    }
                }
            });
        }

        return true;
    }

    /**
     * 获取大店收货单
     * @param orderTime
     * @param shopIdList
     * @return
     */
    public @Nullable List<DdReceiveDoc> getDdReceiveDocs(String orderTime, List<Long> shopIdList) {
        // 默认收货 T 和 T-1
        List<String> orderTimeList = new ArrayList<>();
        if(StringUtils.isNotBlank(orderTime)) {
            orderTimeList.add(orderTime);
        }else {
            orderTimeList.add(DateUtil.getDateFormate(DateUtil.addDay(-1), "yyyy-MM-dd"));
            orderTimeList.add(DateUtil.getDateFormate(new Date(), "yyyy-MM-dd"));
        }

        Example ddReceiveDocEx = new Example(DdReceiveDoc.class);
        ddReceiveDocEx.createCriteria().andIn("orderTime", orderTimeList)
                .andIn("shopId", shopIdList)
                .andEqualTo("docStatus", YesOrNoEnums.NO.getCode());
        List<DdReceiveDoc> ddReceiveDocList = ddReceiveDocMapper.selectByExample(ddReceiveDocEx);
        if(CollectionUtils.isEmpty(ddReceiveDocList)) {
            log.warn("没有需要收货的大店收货单");
            return null;
        }
        return ddReceiveDocList;
    }

    /**
     * 获取开启自动收货的大店门店
     * @return
     */
    public @Nullable List<Long> getDdAutoShopIdList() {
        // 查询开启自动收货的门店
        List<Long> shopIdList = shopReceiveSettingService.getShopIdList(ShopReceiveTypeEnums.AUTOMATIC.getCode());
        if(CollectionUtils.isEmpty(shopIdList)){
            log.warn("没有开启自动收货的门店");
            return null;
        }


        // 大店类型的门店
        List<Long> bigShopIdList = shopService.getShopIdListByShopType(ShopTypeEnums.GF.getCode());
        if(CollectionUtils.isEmpty(bigShopIdList)){
            log.warn("没有大店类型的门店");
            return null;
        }


        // 获取开启自动订货的大店
        shopIdList.retainAll(bigShopIdList);
        if(CollectionUtils.isEmpty(shopIdList)){
            log.warn("没有开启自动收货的大店");
            return null;
        }
        return shopIdList;
    }
}
