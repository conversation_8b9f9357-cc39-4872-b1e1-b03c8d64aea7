package com.pinshang.qingyun.order.service;

import com.pinshang.qingyun.box.utils.SpringUtil;
import com.pinshang.qingyun.order.mapper.OrderBillMapper;
import com.pinshang.qingyun.order.mapper.StoreSettlementMapper;
import com.pinshang.qingyun.order.model.order.Order;
import com.pinshang.qingyun.order.model.order.OrderBill;
import com.pinshang.qingyun.order.model.order.StoreSettlement;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.entity.Example;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * @Author: sk
 * @Date: 2024/7/29
 */
@Slf4j
@Service
public class OrderPaymentService {
    @Autowired
    private StoreSettlementMapper storeSettlementMapper;
    @Autowired
    private OrderBillMapper orderBillMapper;

    /**
     * 更新预付款余额\订单扣款记录对账明细
     * @param storeId
     * @param orderAmount
     * @param order
     * @param isXsJmShop
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public Boolean addPaymentForOrder(Long storeId, Order order, Boolean isXsJmShop) {
        Example ex = new Example(StoreSettlement.class);
        ex.createCriteria().andEqualTo("storeId", storeId);

        List<StoreSettlement> ssList = this.storeSettlementMapper.selectByExample(ex);
        StoreSettlement ss = null;
        if (SpringUtil.isNotEmpty(ssList)) {
            ss = ssList.get(0);
        }
        int rowNum = 0;
        if (ss != null) {
            if (ss.getCollectStatus()) {
                if (ss.getCollectPrice() == null) {
                    ss.setCollectPrice(BigDecimal.ZERO.doubleValue());
                }

                ss.setCollectPrice((BigDecimal.valueOf(ss.getCollectPrice()).subtract(order.getOrderAmount())).doubleValue());
                rowNum = storeSettlementMapper.updateByPrimaryKey(ss);

                // 记录记录
                OrderBill ob = new OrderBill();
                ob.setStoreId(order.getStoreId());
                ob.setOrderId(order.getId());
                ob.setArAmount(order.getOrderAmount());
                ob.setPaAmount(BigDecimal.ZERO);
                ob.setOrderTime(order.getOrderTime());
                ob.setStoreBalance(BigDecimal.valueOf(ss.getCollectPrice()));
                ob.setBillRemark("<--门店扣款:" + order.getOrderCode() + " -->");
                if(isXsJmShop){
                    ob.setBillRemark("<--门店订货扣款：" + order.getOrderCode() + " -->");
                }
                ob.setCreateTime(new Date());
                ob.setCrateId(storeId);
                this.orderBillMapper.insert(ob);

            }
        }

        return Boolean.TRUE;
    }
}
