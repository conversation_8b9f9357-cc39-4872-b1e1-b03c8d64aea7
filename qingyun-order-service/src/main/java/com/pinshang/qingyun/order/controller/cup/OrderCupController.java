package com.pinshang.qingyun.order.controller.cup;

import com.github.pagehelper.PageInfo;
import com.pinshang.qingyun.base.api.TokenInfo;
import com.pinshang.qingyun.base.configure.expand.QYAssert;
import com.pinshang.qingyun.base.util.FastThreadLocalUtil;
import com.pinshang.qingyun.box.utils.SpringUtil;
import com.pinshang.qingyun.kafka.enums.KafkaMessageOperationTypeEnum;
import com.pinshang.qingyun.order.config.LockConstants;
import com.pinshang.qingyun.order.dto.cup.*;
import com.pinshang.qingyun.order.enums.OrderModeType;
import com.pinshang.qingyun.order.mapper.entry.order.OrderLogListEntry;
import com.pinshang.qingyun.order.model.commodity.Commodity;
import com.pinshang.qingyun.order.model.order.Order;
import com.pinshang.qingyun.order.service.*;
import com.pinshang.qingyun.order.service.cup.OrderCupImportService;
import com.pinshang.qingyun.order.service.cup.OrderCupService;
import com.pinshang.qingyun.order.service.xda.v4.TdaOrderService;
import com.pinshang.qingyun.order.vo.cup.CommodityListQueryReqVO;
import com.pinshang.qingyun.order.vo.cup.StoreOrderCount;
import com.pinshang.qingyun.order.vo.splitOrder.SplitOrderKafkaVo;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.ss.usermodel.WorkbookFactory;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.InputStream;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/orderQilian")
@Slf4j
public class OrderCupController {

    @Autowired
    OrderService orderService;

    @Autowired
    CommodityService commodityService;

    @Autowired
    OrderCupService orderCupService;

    @Autowired
    RedissonClient redissonClient;

    @Autowired
    TdaOrderService tdaOrderService;

    @Autowired
    SplitOrderService splitOrderService;

    @Autowired
    WeChatSendMessageService weChatSendMessageService;

    @Autowired
    OrderCupImportService orderCupImportService;
    @Autowired
    private OrderHistoryService orderHistoryService;
    @Autowired
    private SplitOrderSendKfkService splitOrderSendKfkService;

    /**
     * 客户特定日期已下单数量
     *
     * @throws Exception
     */
    @ApiOperation(value = "客户特定日期已下单数量")
    @RequestMapping(value = "/countOrderNumber", method = RequestMethod.POST)
    public StoreOrderCount countOrderNumber(@RequestParam(value = "storeId",required = false) Long storeId, @RequestParam(value = "orderTime",required = false) String orderTime) {
        return orderService.countOrderNumberByStoreId(storeId, orderTime);
    }


    /**
     * 查看客户的商品列表
     *
     * @param reqVO
     * @return
     */
    @ApiOperation(value = "查看客户的商品列表")
    @RequestMapping(value = "/commoditylist", method = RequestMethod.POST)
    public PageInfo<Commodity> commoditylist(@RequestBody CommodityListQueryReqVO reqVO) {
        QYAssert.isTrue(reqVO.getStoreId() != null, "客户id不能为空");
        PageInfo<Commodity> result = commodityService.commodityList(reqVO);
        return result;
    }

    /**
     * 保存订单
     *
     * @param orderRequestDto
     * @return
     */
    @ApiOperation(value = "保存订单")
    @PostMapping("/save")
    public OrderDto save(@RequestBody OrderRequestDto orderRequestDto) {

        TokenInfo token = FastThreadLocalUtil.getQY();
        QYAssert.isTrue(token != null, "请登陆后再试...");
        orderRequestDto.setEnterpriseId(token.getEnterpriseId());
        orderRequestDto.setUserId(token.getUserId());
        orderRequestDto.setUserName(token.getRealName());
        OrderDto orderDto = orderCupService.createCupOrder(orderRequestDto);


        SplitOrderKafkaVo splitOrderKafkaVo = new SplitOrderKafkaVo();
        splitOrderKafkaVo.setOrderId(orderDto.getId());
        splitOrderKafkaVo.setType(KafkaMessageOperationTypeEnum.INSERT);
        splitOrderKafkaVo.setCreateId(orderDto.getUserId());
        splitOrderKafkaVo.setEnterpriseId(orderDto.getEnterpriseId());
        //splitOrderService.execute(splitOrderKafkaVo, splitOrderService, weChatSendMessageService);
        splitOrderSendKfkService.sendSplitOrderKfkMsg(splitOrderKafkaVo);
        return orderDto;
    }


    @ApiOperation(value = "订单取消", notes = "订单取消")
    @PostMapping("/cancel")
    public Boolean cancelOrder(@RequestParam("orderId") Long orderId) {
        TokenInfo tokenInfo = FastThreadLocalUtil.getQY();
        QYAssert.isTrue(tokenInfo != null, "未登录，无法取消");
        String lockKey = LockConstants.cancelCupOrder + orderId;
        RLock lock = redissonClient.getLock(lockKey);
        if (lock.tryLock()) {
            try {
                return orderCupService.cancelOrder(orderId, tokenInfo);
            } finally {
                lock.unlock();
            }
        } else {
            QYAssert.isFalse("系统繁忙,请勿频繁操作!");
        }
        return Boolean.TRUE;
    }


    @ApiOperation(value = "取消、修改订单校验", notes = "取消、修改订单校验")
    @PostMapping("/checkEditOrder")
    public JsonMsgBean checkEditOrder(@RequestParam(value = "orderId") Long orderId,
                                      @RequestParam(value = "editType", defaultValue = "unKnown") String editType) {
        return orderCupService.checkEditOrder(orderId, editType, false);
    }

    @ApiOperation(value = "复制订单校验", notes = "复制订单校验")
    @PostMapping("/checkCopyOrder")
    public JsonMsgBean checkCopyOrder(@RequestParam(value = "orderId") Long orderId) {
        return orderCupService.checkCopyOrder(orderId);
    }



    @ApiOperation(value = "订单列表", notes = "订单列表")
    @PostMapping("/findOrderListByPage")
    public PageInfo<OrderCupPageResultODTO> findOrderListByPage(@RequestBody OrderCupPageQueryIDTO vo) {
        return orderCupService.findOrderListByPage(vo);
    }

    /**
     * 修改订单
     *
     * @param orderRequestDto
     * @return
     */
    @ApiOperation(value = "修改订单")
    @PostMapping("/updateOrder")
    public OrderDto updateOrder(@RequestBody OrderRequestDto orderRequestDto) {
        TokenInfo token = FastThreadLocalUtil.getQY();
        QYAssert.isTrue(token != null, "请登陆后再试...");
        orderRequestDto.setEnterpriseId(token.getEnterpriseId());
        orderRequestDto.setUserId(token.getUserId());
        orderRequestDto.setUserName(token.getRealName());
        orderRequestDto.setEmployeeNumber(token.getEmployeeNumber());
        OrderDto orderDto = orderCupService.updateOrder(orderRequestDto);

        //拆单
        SplitOrderKafkaVo splitOrderKafkaVo = new SplitOrderKafkaVo();
        splitOrderKafkaVo.setOrderId(orderRequestDto.getOrderId());
        splitOrderKafkaVo.setType(KafkaMessageOperationTypeEnum.UPDATE);
        splitOrderKafkaVo.setCreateId(orderDto.getUserId());
        splitOrderKafkaVo.setEnterpriseId(orderDto.getEnterpriseId());
        //splitOrderService.execute(splitOrderKafkaVo, splitOrderService, weChatSendMessageService);
        splitOrderSendKfkService.sendSplitOrderKfkMsg(splitOrderKafkaVo);
        return orderDto;
    }


    /**
     * 修改订单
     *
     * @param orderRequestDto
     * @return
     */
    @ApiOperation(value = "复制订单")
    @PostMapping("/saveCopyOrder")
    public OrderDto saveCopyOrder(@RequestBody OrderRequestDto orderRequestDto) {

        TokenInfo token = FastThreadLocalUtil.getQY();
        QYAssert.isTrue(token != null, "请登陆后再试...");
        orderRequestDto.setEnterpriseId(token.getEnterpriseId());
        orderRequestDto.setUserId(token.getUserId());
        orderRequestDto.setUserName(token.getRealName());
        OrderDto orderDto = orderCupService.createCupOrder(orderRequestDto);

        //拆单
        SplitOrderKafkaVo splitOrderKafkaVo = new SplitOrderKafkaVo();
        splitOrderKafkaVo.setOrderId(orderDto.getId());
        splitOrderKafkaVo.setType(KafkaMessageOperationTypeEnum.INSERT);
        splitOrderKafkaVo.setCreateId(orderDto.getUserId());
        splitOrderKafkaVo.setEnterpriseId(orderDto.getEnterpriseId());
        //splitOrderService.execute(splitOrderKafkaVo, splitOrderService, weChatSendMessageService);
        splitOrderSendKfkService.sendSplitOrderKfkMsg(splitOrderKafkaVo);
        return orderDto;
    }


    /**
     * 创建补货单
     *
     * @param orderRequestDto
     * @return
     */
    @ApiOperation(value = "补货")
    @PostMapping("/saveManual")
    public OrderDto saveManual(@RequestBody OrderRequestDto orderRequestDto) {

        TokenInfo token = FastThreadLocalUtil.getQY();
        QYAssert.isTrue(token != null, "请登陆后再试...");
        QYAssert.isTrue(!tdaOrderService.isTdaStore(orderRequestDto.getStoreId()),
                "通达销售的客户不允许使用补货，请使用订单登记");
        List<Order> orderList = orderCupService.findReplenishmentOrderListByParams(orderRequestDto.getStoreId(), OrderModeType.REPLENISHMENT.getCode(), orderRequestDto.getOrderTime(), 0);

        QYAssert.isTrue(orderList.isEmpty(), "已存在补货单！");
        orderRequestDto.setEnterpriseId(token.getEnterpriseId());
        orderRequestDto.setUserId(token.getUserId());
        orderRequestDto.setUserName(token.getRealName());
        orderRequestDto.setModeType(OrderModeType.REPLENISHMENT.getCode());
        OrderDto orderDto = orderCupService.saveManualOrder(orderRequestDto);

        //拆单
        SplitOrderKafkaVo splitOrderKafkaVo = new SplitOrderKafkaVo();
        splitOrderKafkaVo.setOrderId(orderRequestDto.getOrderId());
        splitOrderKafkaVo.setType(KafkaMessageOperationTypeEnum.REPLENISHMENT);
        splitOrderKafkaVo.setCreateId(orderDto.getUserId());
        splitOrderKafkaVo.setEnterpriseId(orderDto.getEnterpriseId());
        //splitOrderService.execute(splitOrderKafkaVo, splitOrderService, weChatSendMessageService);
        splitOrderSendKfkService.sendSplitOrderKfkMsg(splitOrderKafkaVo);

        return orderDto;
    }


    @ApiOperation(value = "打印订单", notes = "打印订单")
    @PostMapping("/printer")
    public Boolean printerOrder(@RequestBody List<Long> orderIdList) {
        TokenInfo token = FastThreadLocalUtil.getQY();
        return orderCupService.printerOrder(orderIdList, token.getUserId());
    }


    @ApiOperation(value = "订单批量打印", notes = "订单批量打印")
    @PostMapping("/subPrinter")
    public Integer subPrinter(@RequestBody OrderBatchPrintRequestIDTO printRequestIDTO) {
        //TokenInfo token = FastThreadLocalUtil.getQY();
        RLock lock = redissonClient.getLock("order:subPrint:" + printRequestIDTO.getOrderTime());
        if (lock.tryLock()) {
            try {
                return orderCupService.subPrinter(printRequestIDTO);
            } finally {
                lock.unlock();
            }
        } else {
            QYAssert.isFalse("系统繁忙,请勿频繁操作!");
        }
        return 0;
    }

    /**
     * 订单登记按条码导入
     *
     * @return
     */
    @ApiOperation(value = "订单登记按条码导入")
    @PostMapping("/importByCommodityBarcodeExcel")
    public JsonMsgBean importByCommodityBarcodeExcel(@RequestParam String storeId, String orderTime, @RequestParam("file") MultipartFile file) throws Exception {
        TokenInfo token = FastThreadLocalUtil.getQY();
        QYAssert.isTrue(token != null, "请登陆后再试...");

        InputStream in = file.getInputStream();
        Workbook workBook = WorkbookFactory.create(in);

        return orderCupImportService.importOrderCommodity(storeId, orderTime, workBook, 1);
    }

    /**
     * 订单登记按商品编码导入
     *
     * @return
     */
    @ApiOperation(value = "订单登记按商品编码导入")
    @PostMapping("/importByCommoditycodeExcel")
    public JsonMsgBean importByCommoditycodeExcel(@RequestParam String storeId, String orderTime, @RequestParam("file") MultipartFile file) throws Exception {
        TokenInfo token = FastThreadLocalUtil.getQY();
        QYAssert.isTrue(token != null, "请登陆后再试...");

        InputStream in = file.getInputStream();
        Workbook workBook = WorkbookFactory.create(in);
        return orderCupImportService.importOrderCommodity(storeId, orderTime, workBook, 2);
    }

    @PostMapping("/detail/{orderId}")
    public Map<String, Object> detail(@PathVariable("orderId") Long orderId) {
        return orderCupService.orderDetail(orderId);
    }

    @PostMapping("/edit/{orderId}")
    public EditOrderDto edit(@PathVariable("orderId") Long orderId, @RequestParam(value = "editType", defaultValue = "unknown") String editType) {
        return orderCupService.editOrder(orderId, editType);
    }


    /**
     * 订单登记--显示客户下单明细
     *
     * @param storeId
     * @param orderTime
     * @param orderStatus
     * @return
     */
    @GetMapping("/loadStoreOrderList")
    public List<Order> loadStoreOrderList(@RequestParam Long storeId, @RequestParam String orderTime, @RequestParam Integer orderStatus) {

        return orderCupService.searchOrderListByStoreId(storeId, orderTime, orderStatus);
    }


    @ApiOperation(value = "订单日志", notes = "订单日志")
    @RequestMapping(value = "/orderLog", method = RequestMethod.POST)
    public PageInfo<OrderLogListEntry> orderLogList(@RequestBody OrderLogPageIDTO idto) {
        return orderHistoryService.orderLogList(idto);
    }
}
