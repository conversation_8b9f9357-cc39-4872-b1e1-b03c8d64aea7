package com.pinshang.qingyun.order.controller;

import com.pinshang.qingyun.order.service.syncOrderJob.SyncOrderJobService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import java.util.Date;

@RestController
@RequestMapping("/order")
@Slf4j
public class SyncOrderJobController {
	@Autowired
	private SyncOrderJobService syncOrderJobService;

	/**
	 * 订单覆盖
	 *
	 * @param coverTime 覆盖时间，可选参数。
	 */
	@PostMapping("/syncOrderAndSO2DO")
	public void syncOrderAndSO2DO(@RequestParam(value = "coverTime",required = false) String coverTime){
		long start = System.currentTimeMillis();
		log.warn("开始调用执行订单覆盖: {}", new Date());
		syncOrderJobService.execute(coverTime);
		long end = System.currentTimeMillis();
		log.warn("结束调用执行订单覆盖: {}  耗时：{}", new Date(),(end - start));
	}
	
}
