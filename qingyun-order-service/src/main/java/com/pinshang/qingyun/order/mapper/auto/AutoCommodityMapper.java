package com.pinshang.qingyun.order.mapper.auto;

import com.pinshang.qingyun.base.mybatis.MyMapper;
import com.pinshang.qingyun.order.dto.AutoCommodityIDTO;
import com.pinshang.qingyun.order.dto.AutoCommodityODTO;
import com.pinshang.qingyun.order.dto.XDCommodityODTO;
import com.pinshang.qingyun.order.model.auto.AutoCommodity;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * @Author: liuZhen
 * @DateTime: 2022/5/11 13:37
 */
@Repository
public interface AutoCommodityMapper extends MyMapper<AutoCommodity> {

    List<AutoCommodityODTO> queryCommodityListByIdto(AutoCommodityIDTO idto);

    List<String> queryCommodityIds(@Param("commodityIds") List<String> commodityIds);

    Integer saveList(@Param("commodityIds") List<String> commodityIds, @Param("createId") String createId);
    List<XDCommodityODTO> queryDirectSendingCommodity(@Param("logisticsModel") Integer logisticsModel);

    List<String> queryAutoCommodityCodes();
}
