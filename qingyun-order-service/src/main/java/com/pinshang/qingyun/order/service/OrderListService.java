package com.pinshang.qingyun.order.service;

import com.pinshang.qingyun.base.configure.expand.QYAssert;
import com.pinshang.qingyun.box.utils.SpringUtil;
import com.pinshang.qingyun.order.dto.xda.v3.XdaOrderItemAppV3ODTO;
import com.pinshang.qingyun.order.enums.ProductTypeEnums;
import com.pinshang.qingyun.order.manage.delivery.dto.DeliveryOrderDTO;
import com.pinshang.qingyun.order.manage.delivery.dto.OrderDeliveryFinishDTO;
import com.pinshang.qingyun.order.manage.delivery.dto.StoreInfo;
import com.pinshang.qingyun.order.mapper.OrderListMapper;
import com.pinshang.qingyun.order.mapper.OrderMapper;
import com.pinshang.qingyun.order.model.order.Order;
import com.pinshang.qingyun.order.model.order.OrderList;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.entity.Example;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

/**
 * @Author: sk
 * @Date: 2024/7/18
 */
@Slf4j
@Service
public class OrderListService {

    @Autowired
    private OrderListMapper orderListMapper;
    @Autowired
    private OrderMapper orderMapper ;
    public List<OrderList> findByOrderIdAndProductType(Long orderId,Integer productType) {

        return  orderListMapper.findByOrderIdAndProductType(orderId,productType);
    }

    public List<OrderList> findByOrderId(Long orderId) {

        return  orderListMapper.findByOrderIdAndProductType(orderId,null);

    }

    public List<DeliveryOrderDTO> calculateDeliveryOrderMoney(Map<Long, OrderDeliveryFinishDTO> candidateOrderDeliveryFinishMap){

        //按照订单id查询出所有的订单明细
        List<XdaOrderItemAppV3ODTO> orderLists = this.getOrderList(new ArrayList<>(candidateOrderDeliveryFinishMap.keySet()));

        //将实发数量和订单数量相同的订单明细排除，只要数量不同的进行处理
        List<XdaOrderItemAppV3ODTO> filterOrderLists  = filterSameQuantityDeliveryOrderList(orderLists);

        //将订单明细按照订单id进行分组
        Map<Long, List<XdaOrderItemAppV3ODTO>> orderListMap = filterOrderLists.stream()
                .collect(Collectors.groupingBy(XdaOrderItemAppV3ODTO::getOrderId));
        if(SpringUtil.isEmpty(orderListMap)){
            return null;
        }

        //生成最终的中间类DeliveryOrderDTO，包含每笔订单的多发金额和少发金额
        Map<Long, DeliveryOrderDTO> transformMap = transformOrderListMap(orderListMap,candidateOrderDeliveryFinishMap);

        return new ArrayList<>(transformMap.values());
    }

    public List<XdaOrderItemAppV3ODTO> getOrderList(List<Long> orderIds){
        QYAssert.isTrue(SpringUtil.isNotEmpty(orderIds),"订单id为空");
        return orderListMapper.queryOrderCommodityDetailV3ByIds(orderIds);
    }

    private Map<Long, DeliveryOrderDTO> transformOrderListMap(Map<Long, List<XdaOrderItemAppV3ODTO>> orderListMap,Map<Long, OrderDeliveryFinishDTO> candidateOrderDeliveryFinishMap) {
        Map<Long, DeliveryOrderDTO> ret = new HashMap<>();
        orderListMap.forEach(
                (key,value)->{
                    ret.put(key,formatDeliveryOrderMoneydPrice(value,candidateOrderDeliveryFinishMap.get(key)));
                }
        );
        return ret;
    }

    private List<XdaOrderItemAppV3ODTO> filterSameQuantityDeliveryOrderList(List<XdaOrderItemAppV3ODTO> orderLists) {
        return orderLists.stream().filter(xdaOrderItemAppV3ODTO ->
                        Optional.ofNullable(xdaOrderItemAppV3ODTO.getRealDeliveryQuantity()).orElse(BigDecimal.ZERO)
                                .compareTo(Optional.ofNullable(xdaOrderItemAppV3ODTO.getCommodityNum()).orElse(BigDecimal.ZERO)) !=0)
                .collect(Collectors.toList());
    }


    private DeliveryOrderDTO formatDeliveryOrderMoneydPrice(List<XdaOrderItemAppV3ODTO> value,OrderDeliveryFinishDTO orderDeliveryFinishDTO) {

        AtomicReference<BigDecimal> overDeliveryMoney = new AtomicReference<>(BigDecimal.ZERO);
        AtomicReference<BigDecimal> underDeliveryMoney = new AtomicReference<>(BigDecimal.ZERO);
        value.forEach(
                orderList->{
                    BigDecimal diff = orderList.getTotalPrice()
                            .subtract(orderList.getRealPrice());
                    if(diff.compareTo(BigDecimal.ZERO) > 0){
                        underDeliveryMoney.set(underDeliveryMoney.get().add(diff));
                    }else{
                        overDeliveryMoney.set(overDeliveryMoney.get().add(diff.negate()));
                    }
                }
        );
        XdaOrderItemAppV3ODTO xdaOrderItemAppV3ODTO = value.get(0);
        DeliveryOrderDTO deliveryOrderDTO = new DeliveryOrderDTO();
        deliveryOrderDTO.setOrderId(xdaOrderItemAppV3ODTO.getOrderId());
        deliveryOrderDTO.setOrderCode(orderDeliveryFinishDTO.getOrderCode());
        deliveryOrderDTO.setOverDeliveryMoney(overDeliveryMoney.get());
        deliveryOrderDTO.setUnderDeliveryMoney(underDeliveryMoney.get());
        deliveryOrderDTO.setOrderType(orderDeliveryFinishDTO.getOrderType());
        StoreInfo storeInfo = new StoreInfo();
        storeInfo.setStoreId(orderDeliveryFinishDTO.getStoreId());
        deliveryOrderDTO.setStoreInfo(storeInfo);
        deliveryOrderDTO.setStoreTypeId(orderDeliveryFinishDTO.getStoreTypeId());

        return deliveryOrderDTO;
    }

    public List<OrderList> getOrderListsByOrderId(Long orderId) {
        Example example = new Example(OrderList.class);
        example.createCriteria().andEqualTo("orderId", orderId);
        return orderListMapper.selectByExample(example);
    }

    public List<OrderList> getOrderListsProductByOrderId(Long orderId) {
        Example example = new Example(OrderList.class);
        example.createCriteria().andEqualTo("orderId", orderId)
                .andEqualTo("type", ProductTypeEnums.PRODUCT.getCode());
        return orderListMapper.selectByExample(example);
    }

    public void batchInsert(List<OrderList> orderList) {
        if (CollectionUtils.isEmpty(orderList)) {
            return;
        }
        orderListMapper.insertList(orderList);
    }

    public List<OrderList> getOrderListByCommodityIdAndOrderId(Long orderId, Long commodityId) {
        Example ex = new Example(OrderList.class);
        ex.createCriteria()
                .andEqualTo("commodityId", commodityId)
                .andEqualTo("orderId", orderId);
        return orderListMapper.selectByExample(ex);
    }

    @Transactional(rollbackFor = Exception.class)
    public void updateBatchOrderList(List<OrderList> updateList) {
        orderListMapper.updateBatchOrderList(updateList);
    }

}
