package com.pinshang.qingyun.order.mapper;


import com.pinshang.qingyun.base.mybatis.MyMapper;
import com.pinshang.qingyun.order.dto.xda.tda.AuditReturnOrdeItemIDTO;
import com.pinshang.qingyun.order.dto.xda.tda.ConfirmReturnOrdeItemIDTO;
import com.pinshang.qingyun.order.dto.xda.tda.ReturnOrderItemODTO;
import com.pinshang.qingyun.order.model.xda.XdaReturnOrderItem;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface XdaReturnOrderItemMapper extends MyMapper<XdaReturnOrderItem> {

    void batchInsertSelective(@Param("list") List<XdaReturnOrderItem> list);

    List<ReturnOrderItemODTO> queryByReturnOrderId(@Param("returnOrderId") Long returnOrderId);

    int batchUpdateByAudit(@Param("list") List<AuditReturnOrdeItemIDTO> list);

    List<ReturnOrderItemODTO> queryWarehousePendingItemList(@Param("returnOrderId") Long returnOrderId);

    int batchUpdateByConfirm(@Param("list") List<ConfirmReturnOrdeItemIDTO> list);
}

