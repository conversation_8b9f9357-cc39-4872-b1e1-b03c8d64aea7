package com.pinshang.qingyun.order.mapper;

import com.pinshang.qingyun.base.mybatis.MyMapper;
import com.pinshang.qingyun.order.dto.ShelvesShoppingCartODTO;
import com.pinshang.qingyun.order.mapper.entry.order.*;
import com.pinshang.qingyun.order.model.order.ShoppingCart;
import com.pinshang.qingyun.order.vo.order.ShoppingCartAdminPageVo;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

@Repository
public interface ShoppingCartMapper extends MyMapper<ShoppingCart>{
	/*查询商品信息拆单信息*/
	SplitOrderListEntry queryCommoditySplitInfo(@Param("storeId")Long storeId, @Param("commodityId") Long commodityId, @Param("enterpriseId") Long enterpriseId,@Param("ifAdmin") Boolean ifAdmin);

	//原本的shoppingCartDetail查 0
	List<ShoppingCartEntry> shoppingCartDetail(@Param("storeId") Long storeId,@Param("shoppingCartId") Long shoppingCartId,
											   @Param("isXd") Boolean isXd,@Param("isInternal") Boolean isInternal,
											   @Param("userId") Long userId);

	List<ShoppingCartEntry> shoppingCartDetailConsignment(@Param("storeId") Long storeId,@Param("shoppingCartId") Long shoppingCartId,
											   @Param("isXd") Boolean isXd,@Param("isInternal") Boolean isInternal,
											   @Param("userId") Long userId,@Param("consignmentId") Long consignmentId,
												@Param("stallIdList") List<Long> stallIdList);

	//管理员版本 shoppingCartDetail 查 1
	List<ShoppingCartEntry> shoppingCartDetailAdmin(@Param("storeId") Long storeId,@Param("shoppingCartId") Long shoppingCartId,
													@Param("isXd") Boolean isXd,@Param("isInternal") Boolean isInternal,
													@Param("userId") Long userId,@Param("bigShop") Boolean bigShop);

	List<ShoppingCartAdminEntry> shoppingCartAdminByStoreIdList(@Param("storeIdList") List<Long> storeIdList, @Param("userId") Long userId, @Param("bigShop") Boolean bigShop);

	// 查询所有购物车
	List<ShoppingCartEntry> queryAllShoppingCart();

	List<ShoppingCartEntry> xdShoppingCartDetail();


	/*查找客户价格方案*/
	List<ProductPriceEntry> findStoreCommodityPriceByStoreIdAndCommodityIds(Map<String,Object> paramMap);
	
	/*查找商品特价*/
	List<ProductPriceEntry> findProductCommodityPriceByStoreIdAndOrderTime(Map<String,Object> paramMap);
	
	//根据商品id list及企业id,查询拆单需要的信息
	List<SplitOrderListEntry> getSplitOrderInfo(@Param("commodityIds") List<Long> commodityIds, @Param("enterpriseId") Long enterpriseId);
	
	Integer getDeliveryBatchDay();
	
	List<DeliveryBatchEntry> findDeliveryBatchByCode(@Param("optionCode") String optionCode);

	BigDecimal getAdviseCommodityNum(@Param("storeId") Long storeId, @Param("commodityId") Long commodityId);

    void deleteXDShoppingCartItem(@Param("shoppingCartIdList") List<Long> shoppingCartIdList);

	void deleteXdShoppingCart(@Param("shoppingCartIdList") List<Long> shoppingCartIdList);

	List<Long> selectShpppingCartIdList();

    List<ShoppingCart> getDistinctShoppingCartAdmin(@Param("cartVo") ShoppingCartAdminPageVo shoppingCartAdminPageVo);

    Long countCartAdmin(@Param("userId") Long userId, @Param("bigShop") Boolean bigShop);

    List<ShelvesShoppingCartODTO> findShoppingCartCreateId(@Param("storeId") Long storeId);

	List<ShoppingCartEntry> queryAutoShoppingCart();

}