package com.pinshang.qingyun.order.controller.pf;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;

import javax.servlet.http.HttpServletResponse;

import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.pinshang.qingyun.base.api.ApiResponse;
import com.pinshang.qingyun.base.api.PfTokenInfo;
import com.pinshang.qingyun.base.configure.expand.QYAssert;
import com.pinshang.qingyun.base.constant.QingyunConstant;
import com.pinshang.qingyun.base.enums.TerminalSourceTypeEnum;
import com.pinshang.qingyun.base.util.FastThreadLocalUtil;
import com.pinshang.qingyun.order.config.LockConstants;
import com.pinshang.qingyun.order.dto.file.FileODTO;
import com.pinshang.qingyun.order.dto.pf.PfCreatePrePayOrderIDTO;
import com.pinshang.qingyun.order.dto.pf.PfOrder4AppODTO;
import com.pinshang.qingyun.order.dto.pf.PfOrderCancelIDTO;
import com.pinshang.qingyun.order.dto.pf.PfPreOrderIDTO;
import com.pinshang.qingyun.order.dto.pf.PfPreOrderODTO;
import com.pinshang.qingyun.order.dto.pf.PfQueryOrder4AppParam;
import com.pinshang.qingyun.order.dto.shopcart.ShoppingCartODTO;
import com.pinshang.qingyun.order.mapper.OrderListMapper;
import com.pinshang.qingyun.order.mapper.OrderMapper;
import com.pinshang.qingyun.order.mapper.PfShoppingCartMapper;
import com.pinshang.qingyun.order.mapper.StoreDurationMapper;
import com.pinshang.qingyun.order.mapper.entry.order.OrderItemEntry;
import com.pinshang.qingyun.order.service.StoreService;
import com.pinshang.qingyun.order.service.pf.BuildPfShoppingCart;
import com.pinshang.qingyun.order.service.pf.PfOrderService;
import com.pinshang.qingyun.pf.product.service.PfCommodityFrontClient;

import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;

/**
 * 批发订单
 *
 */
@RestController
@RequestMapping("/pf/order")
@Slf4j
public class PfAppOrderController {

	@Autowired
    private RedissonClient redissonClient;
	
	@Autowired
	private PfOrderService pfOrderService;
	
	
    
	@Autowired
    private OrderMapper orderMapper;
	
	@Autowired
	private PfShoppingCartMapper pfShoppingCartMapper;
	
	@Autowired
    private OrderListMapper orderListMapper;
	
	@Autowired
    private PfCommodityFrontClient pfCommodityFrontClient;
	
	@Autowired
    private StoreService storeService;
	
	@Autowired
    private StoreDurationMapper storeDurationMapper;
	
	@GetMapping("/detail")
    @ApiOperation(value = "订单明细")
    public PfOrder4AppODTO queryOrderDetailByCode(@RequestParam(value = "orderCode",required = false) String orderCode){
        return pfOrderService.queryOrderDetailByCode(orderCode);
    }
	
	@GetMapping("/copy")
    @ApiOperation(value = "订单复制")
    public Integer copyOrderById4App(@RequestParam(value = "orderId",required = false) Long orderId){
        return pfOrderService.copyOrderById4App(orderId);
    }
	
	@SuppressWarnings("unchecked")
	@PostMapping("/list")
    @ApiOperation(value = "订单列表")
    public ApiResponse<PfOrder4AppODTO> queryOrderListByPage(@RequestBody PfQueryOrder4AppParam param, HttpServletResponse response){
        response.addHeader(QingyunConstant.WRAP_RESPONSE_FLAG,QingyunConstant.WRAP_RESPONSE_FLAG_VALUE);
        PfTokenInfo pfTokenInfo = FastThreadLocalUtil.getPF();
        Long storeId =  pfTokenInfo.getStoreId();
        QYAssert.isTrue(storeId != null, "未能获取到用户信息,请重新登录!");
        param.setStoreId(storeId);
        return ApiResponse.convert(pfOrderService.queryOrderListByPage(param));
    }
	
	@PostMapping("/preOrder")
    @ApiOperation(value = "预览订单")
    public PfPreOrderODTO preOrderView(@RequestBody PfPreOrderIDTO query){
		PfTokenInfo pfTokenInfo = FastThreadLocalUtil.getPF();
        Long storeId = pfTokenInfo.getStoreId();
        QYAssert.isTrue(!pfTokenInfo.getIsTouristStore(), "游客无法下单。");
        QYAssert.isTrue(storeId != null, "未能获取到用户信息,请重新登录!");
        query.setStoreId(storeId);
        query.setAppCode(pfTokenInfo.getAppCode());
        return pfOrderService.preOrderView(query);
    }
	
	@PostMapping("/createOrder")
	@ApiOperation(value = "创建订单")
	public boolean createOrder(@RequestBody PfCreatePrePayOrderIDTO idto) {
		PfTokenInfo pfTokenInfo = FastThreadLocalUtil.getPF();
		QYAssert.isFalse("请升级到最新版本!");
		QYAssert.isTrue(!pfTokenInfo.getIsTouristStore(), "游客无法下单。");
		Long storeId = pfTokenInfo.getStoreId();
		QYAssert.isTrue(storeId != null, "未能获取到用户信息,请重新登录!");
		
		String appCode = pfTokenInfo.getAppCode();
        Long userId = pfTokenInfo.getUserId();
        TerminalSourceTypeEnum typeEnum = pfTokenInfo.getTerminalSourceType();
        idto.convert(storeId, appCode, userId, typeEnum);
        
		Date orderTime = idto.getOrderTime();
        // 购物车信息
	    BuildPfShoppingCart buildPfShoppingCart = new BuildPfShoppingCart(storeId, orderTime, pfShoppingCartMapper, orderMapper, orderListMapper, pfCommodityFrontClient, storeService, storeDurationMapper);
	    ShoppingCartODTO shopCart = buildPfShoppingCart.shopCartList();
	    List<Long> commodityIds = shopCart.getAllCommodityIds();
	    // 构建商品库存锁（多个）
	    List<RLock> commodityInventoryLocks = getCommodityInventoryLocks(commodityIds);
	    
        String storeLockKey = LockConstants.generateOrderCommitLock(storeId);
        RLock storeLock = redissonClient.getLock(storeLockKey);
        try {
        	// 这里用了两次锁，一次是客户锁，一次是他所下单的商品库存锁（保证扣减库存时的原子性）
        	if(storeLock.tryLock() && tryLockInventory(commodityInventoryLocks)){
        		return pfOrderService.creatOrder(idto);
        	} else {
        		QYAssert.isFalse("系统繁忙,请稍后再试!");
        	}
        } finally {
        	try {
	        	if (storeLock.isHeldByCurrentThread()) {
					storeLock.unlock();
				} 
        	} catch (Exception e) {
				log.error(String.format("释放客户锁[%s]异常", storeId), e);
			}
        	unlockInventory(commodityInventoryLocks);
        }
        
        return false;
	}
	
	private void unlockInventory(List<RLock> commodityInventoryLocks) {
		for (RLock lck : commodityInventoryLocks) {
			try {
				if (lck.isHeldByCurrentThread()) {
					lck.unlock();
				}
			} catch (Exception e) {
				log.error(String.format("释放商品库存锁[%s]异常", lck.getName()), e);
			}
		}
	}

	private boolean tryLockInventory(List<RLock> commodityInventoryLocks) {
		boolean flag = true;
		for (RLock lock : commodityInventoryLocks) {
			flag = flag && lock.tryLock();
		}
		return flag;
	}

	private List<RLock> getCommodityInventoryLocks(List<Long> commodityIds) {
		Collections.sort(commodityIds);
		List<RLock> result = new ArrayList<>();
		for (Long cid : commodityIds) {
			String lockKey = LockConstants.generatePfCommodityInventory(cid);
	        RLock lock = redissonClient.getLock(lockKey);
	        result.add(lock);
		}
		return result;
	}

	@PostMapping("/cancel")
    @ApiOperation(value = "订单取消")
	public Integer cancelOrder(@RequestBody PfOrderCancelIDTO idto) {
		PfTokenInfo pfTokenInfo = FastThreadLocalUtil.getPF();
		Long storeId =  pfTokenInfo.getStoreId();
		Long userId = pfTokenInfo.getUserId();
        QYAssert.isTrue(storeId != null, "未能获取到用户信息,请重新登录!");
        QYAssert.isTrue(userId != null, "未能获取到用户信息,请重新登录!");
        QYAssert.isTrue(null != idto.getOrderId() , "订单id不存在!");
        QYAssert.isTrue(null != idto.getReasonOptionId() , "取消原因必选!");
        idto.setStoreId(storeId);
        idto.setUserId(userId);
        
        List<OrderItemEntry> commList = this.orderMapper.queryOrderItem4Copy(idto.getOrderId());
        List<Long> commodityIds = new ArrayList<>();
        for (OrderItemEntry com : commList) {
        	commodityIds.add(com.getCommodityId());
        }
        // 构建商品库存锁（多个）
        List<RLock> commodityInventoryLocks = getCommodityInventoryLocks(commodityIds);
        
        String storeLockKey = LockConstants.generateOrderCommitLock(storeId);
        RLock storeLock = redissonClient.getLock(storeLockKey);
        
        try {
        	// 这里用了两次锁，一次是客户锁，一次是他所下单的商品库存锁（保证扣减库存时的原子性）
        	if (storeLock.tryLock() && tryLockInventory(commodityInventoryLocks)) {
        		return pfOrderService.cancelOrder(idto);
        	} else {
        		QYAssert.isFalse("系统繁忙,请稍后再试!");
        	}
        } finally {
        	try {
	        	if (storeLock.isHeldByCurrentThread()) {
					storeLock.unlock();
				} 
        	} catch (Exception e) {
				log.error(String.format("释放客户锁[%s]异常", storeId), e);
			}
        	unlockInventory(commodityInventoryLocks);
        }
        
        return null;
	}
	
	@ApiOperation(value = "查询  订单PDF文件")
    @GetMapping("/queryOrderPdfFile")
    public FileODTO queryOrderPdfFile(@RequestParam(value = "orderId",required = false) Long orderId){
		return pfOrderService.queryOrderPdfFile(orderId);
    }
}
