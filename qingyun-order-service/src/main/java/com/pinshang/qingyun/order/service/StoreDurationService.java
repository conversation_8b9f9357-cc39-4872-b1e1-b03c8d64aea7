package com.pinshang.qingyun.order.service;

import com.pinshang.qingyun.order.mapper.StoreDurationMapper;
import com.pinshang.qingyun.order.mapper.cup.ConsumableLimitMapper;
import com.pinshang.qingyun.order.model.commodity.Commodity;
import com.pinshang.qingyun.order.model.cup.ConsumableLimit;
import com.pinshang.qingyun.order.model.store.StoreDuration;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @Author: sk
 * @Date: 2024/7/18
 */
@Slf4j
@Service
public class StoreDurationService {

    @Autowired
    private StoreDurationMapper storeDurationMapper;


    public StoreDuration findStoreDurationByStoreId(Long storeId){

        Example example = new Example(StoreDuration.class);
        example.createCriteria().andEqualTo("storeId", storeId);
        List<StoreDuration> storeDurationList = storeDurationMapper.selectByExample(example);
        if(CollectionUtils.isEmpty(storeDurationList)){
            return null;
        }else{
            return storeDurationList.get(0);
        }

    }
}
