package com.pinshang.qingyun.order.mapper.entry.government;

import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2021/2/4 14:38
 */
@Data
public class GovernmentOrderTraceBackRespEntry {

    /*** 产品编码 */
    @ApiModelProperty(value = "产品编码")
    private String commodityCode;
    /*** 产品名称 */
    @ApiModelProperty(value = "产品名称")
    private String commodityName;

    /*** 生产日期 */
    @ApiModelProperty(value = "生产日期")
    private String manufactureDate;

    /*** 生产批次 */
    @ApiModelProperty(value = "生产批次")
    private String manufactureBatch;

    /*** 检验合格证号 */
    @ApiModelProperty(value = "检验合格证号")
    private String inspectionCertificate;

     /*** 销货数量 */
     @ApiModelProperty(value = "销货数量")
    private String commodityCount;

     /*** 数量单位 */
     @ApiModelProperty(value = "数量单位")
    private String commodityUnit;

     /*** 销货日期 */
     @ApiModelProperty(value = "销货日期")
    private String deliveryDate;

     /*** 购货者名称 */
     @ApiModelProperty(value = "购货者名称")
    private String outerStoreName;

     /*** 购货者地址 */
     @ApiModelProperty(value = "购货者地址")
    private String deliveryAddress;

     /*** 购货者联系方式 */
     @ApiModelProperty(value = "购货者联系方式")
    private String linkmanTel;

     /*** 订单号 */
     @ApiModelProperty(value = "订单号")
    private String orderCode;

     /*** SKU */
     @ApiModelProperty(value = "SKU")
    private String sku;
     /*** 配送中心 */
     @ApiModelProperty(value = "配送中心")
    private String distributionCentre;

    /*** 客户id */
    @ApiModelProperty(value = "客户id",hidden = true)
    private Long storeId;
    @ApiModelProperty(hidden = true)
    private String storeCode;
}
