package com.pinshang.qingyun.order.controller.xsjm;

import com.pinshang.qingyun.base.api.TokenInfo;
import com.pinshang.qingyun.base.configure.expand.QYAssert;
import com.pinshang.qingyun.base.util.FastThreadLocalUtil;
import com.pinshang.qingyun.order.config.LockConstants;
import com.pinshang.qingyun.order.dto.AnalysisBySynthesisListIDTO;
import com.pinshang.qingyun.order.dto.CompensateSettleDailyIDTO;
import com.pinshang.qingyun.order.dto.SettleDailyODTO;
import com.pinshang.qingyun.order.model.xsjm.FranchiseSalesAnalysis;
import com.pinshang.qingyun.order.service.XsjmService;
import com.pinshang.qingyun.order.util.RedissonLockExecutor;
import com.pinshang.qingyun.pay.dto.cscanb.CToBGetQRCodeReq;
import com.pinshang.qingyun.pay.dto.cscanb.UnionCToBGetQRCodeResp;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;

/**
 * 鲜食加盟 控制器
 *
 * <AUTHOR>
 * @since 2024年6月28日16:37:31
 */
@RestController
@RequestMapping("/xsjm")
@Api(value = "鲜食加盟相关接口", tags = "鲜食加盟相关接口")
public class XsjmController {

    @Autowired
    private XsjmService xsjmService;
    @Autowired
    private RedissonClient redissonClient;
    @Autowired
    private RedissonLockExecutor redissonLockExecutor;

    /**
     * 预日结
     */
    @ApiOperation(value = "预日结", notes = "预日结")
    @PostMapping("/preSettleDaily")
    public SettleDailyODTO settleDaily() {
        TokenInfo tokenInfo = FastThreadLocalUtil.getQY();
        QYAssert.isTrue(!(tokenInfo == null || tokenInfo.getUserId() == null || tokenInfo.getShopId() == null), "请先登录再操作！");
        return xsjmService.preSettleDaily(tokenInfo);
    }

    /**
     * 确认日结
     */
    @ApiOperation(value = "确认日结", notes = "手动日结")
    @PostMapping("/confirmSettleDaily")
    public void confirmSettleDaily() {
        TokenInfo tokenInfo = FastThreadLocalUtil.getQY();
        QYAssert.isTrue(!(tokenInfo == null || tokenInfo.getUserId() == null || tokenInfo.getShopId() == null), "请先登录再操作！");
        String lockKey = LockConstants.generateXsjmDailySettleLockKey(tokenInfo.getShopId());
        redissonLockExecutor.exec(lockKey, () -> xsjmService.confirmSettleDaily(tokenInfo.getShopId(), tokenInfo.getUserId()));
    }


    /**
     * 自动日结
     */
    @ApiOperation(value = "自动日结", notes = "自动日结")
    @PostMapping("/autoSettleDaily")
    public void autoSettleDaily() {
        xsjmService.autoSettleDaily();
    }


    /**
     * 日结补偿
     */
    @ApiOperation(value = "日结补偿", notes = "日结补偿")
    @PostMapping("/compensateSettleDaily")
    public void compensateSettleDaily(@RequestBody CompensateSettleDailyIDTO dto) {
        xsjmService.compensateSettleDaily(dto);
    }


    @ApiOperation(value = "鲜食加盟获取获取二维码")
    @PostMapping("/getCToBQRCode")
    public UnionCToBGetQRCodeResp getCToBQRCode(@RequestBody CToBGetQRCodeReq req) {
        TokenInfo tokenInfo = FastThreadLocalUtil.getQY();
        RLock lock = redissonClient.getLock(LockConstants.getCToBQRCode + tokenInfo.getShopId());
        if (lock.tryLock()) {
            try {
                req.setShopId(tokenInfo.getShopId());
                return xsjmService.getCToBCode(req);
            } finally {
                lock.unlock();
            }
        } else {
            QYAssert.isFalse("系统繁忙,请勿频繁操作!");
        }
        return null;
    }

    @ApiOperation(value = "鲜食加盟获取客户余额")
    @GetMapping("/getStoreCollectPrice")
    public BigDecimal getStoreCollectPrice() {
       TokenInfo tokenInfo = FastThreadLocalUtil.getQY();
       return xsjmService.getStoreCollectPrice(tokenInfo.getShopId());
    }

    @PostMapping("/analysisBySynthesisList")
    @ApiOperation("综合分析报表")
    public FranchiseSalesAnalysis analysisBySynthesisList(@RequestBody AnalysisBySynthesisListIDTO dto) {
        return xsjmService.analysisBySynthesisList(dto);
    }
}
