package com.pinshang.qingyun.order.service;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.pinshang.qingyun.box.utils.SpringUtil;
import com.pinshang.qingyun.box.utils.StringUtil;
import com.pinshang.qingyun.common.dto.DictionaryTreeODTO;
import com.pinshang.qingyun.common.service.DictionaryClient;
import com.pinshang.qingyun.order.mapper.CommodityMapper;
import com.pinshang.qingyun.order.mapper.PurchaseListMapper;
import com.pinshang.qingyun.order.mapper.entry.commodity.CommodityBasicEntry;
import com.pinshang.qingyun.order.mapper.entry.purchase.ConnectionPurchaseDetailEntry;
import com.pinshang.qingyun.order.mapper.entry.purchase.ConnectionPurchaseEntry;
import com.pinshang.qingyun.order.mapper.entry.purchase.SendPurchaseDetailEntry;
import com.pinshang.qingyun.order.vo.commodity.CommodityVO;
import com.pinshang.qingyun.order.vo.purchase.PurchaseListVo;
import com.pinshang.qingyun.product.dto.category.CategoryInfoODTO;
import com.pinshang.qingyun.product.service.CategoryClient;
import com.pinshang.qingyun.storage.dto.CommoditySupplierODto;
import com.pinshang.qingyun.storage.service.CommoditySupplierClient;
import com.pinshang.qingyun.supplier.dto.FullSupplierODTO;
import com.pinshang.qingyun.supplier.dto.QuerySupplierByIdsIDTO;
import com.pinshang.qingyun.supplier.service.SupplierClient;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
public class PurchaseListService {
	
	@Autowired
	private PurchaseListMapper purchaseListMapper;

	@Autowired
	private SupplierClient supplierClient;

	@Autowired
	private CommoditySupplierClient commoditySupplierClient;
	@Autowired
	private CommodityMapper commodityMapper;
	@Autowired
	private DictionaryClient dictionaryClient;
	@Autowired
	private CategoryClient categoryClient;

	/**
	 * 获取直通采购列表明细
	 * @param vo
	 * @return
	 */
	public PageInfo<ConnectionPurchaseDetailEntry> findConnectionPurchaseDetail(PurchaseListVo vo) {
		if(!StringUtil.isBlank(vo.getCreateOrderBeginDate()) && !StringUtil.isBlank(vo.getCreateOrderEndDate())){
			vo.setCreateOrderBeginDate(vo.getCreateOrderBeginDate()+ " 00:00:00");
			vo.setCreateOrderEndDate(vo.getCreateOrderEndDate()+ " 23:59:59");
		}

		Long supplierId = vo.getSupplierId();
		if (supplierId != null && supplierId > 0) {
			List<Long> commodityIdList = commoditySupplierClient.queryCommodityIdsByDefaultSupplier(supplierId);
			if(CollectionUtils.isNotEmpty(commodityIdList)){
				vo.setCommodityIdList(commodityIdList);
			}else {
				return new PageInfo<>();
			}
		}

		if(StringUtils.isNotBlank(vo.getCommodityKey())
		        || vo.getCateId1() != null
				|| vo.getCateId2() != null
				|| vo.getCateId3() != null){
			CommodityVO commodityVO = new CommodityVO();
			BeanUtils.copyProperties(vo,commodityVO);
			List<CommodityBasicEntry> commList = commodityMapper.findCommodityBasicListByParam(commodityVO);
			if(CollectionUtils.isNotEmpty(commList)){
				List<Long> commodityIdList = commList.stream().map(item -> Long.valueOf(item.getCommodityId())).collect(Collectors.toList());
			    vo.setCommodityIdList2(commodityIdList);
			}else {
				return new PageInfo<>();
			}
		}
		PageInfo<ConnectionPurchaseDetailEntry> pageInfo= PageHelper.startPage(vo.getPageNo(),vo.getPageSize()).doSelectPageInfo(()-> {
			purchaseListMapper.findConnectionPurchaseDetailNew(vo);
		});
		List<ConnectionPurchaseDetailEntry> list = pageInfo.getList();

		// 设置供应商名称
		if (SpringUtil.isNotEmpty(list)) {
			List<Long> commodityIdList = list.stream().map(item -> item.getCommodityId()).collect(Collectors.toList());
			Map<Long, CommoditySupplierODto> commodityIdAndSupplierODTOMap = commoditySupplierClient.queryCommodityDefaultSupplier(commodityIdList);

			CommodityVO commodityVO = new CommodityVO();
			commodityVO.setCommodityIdList(commodityIdList);
			List<CommodityBasicEntry> commList = commodityMapper.findCommodityBasicListByParam(commodityVO);
			Map<String, CommodityBasicEntry> commMap = commList.stream().collect(Collectors.toMap(CommodityBasicEntry::getCommodityId, Function.identity()));

			list.forEach(item -> {
				Long commodityId = item.getCommodityId();
				CommodityBasicEntry commodityResultEntry = commMap.get(commodityId + "");
				if(commodityResultEntry != null){
					BeanUtils.copyProperties(commodityResultEntry,item);
				}

				CommoditySupplierODto supplierODto = commodityIdAndSupplierODTOMap.get(commodityId);
				if (supplierODto != null) {
					item.setSupplierName(supplierODto.getSupplierName());
				}
				if(null !=item.getSalesBoxCapacity()){
					item.setBoxCapacity(item.getSalesBoxCapacity());
					item.setBoxNums(item.getCommodityNum().divide(item.getSalesBoxCapacity(),2, BigDecimal.ROUND_HALF_UP));
				}
			});
		}

		return new PageInfo<>(list);
	}



	public PageInfo<ConnectionPurchaseEntry> findSendPurchaseByPage(PurchaseListVo vo) {
		PageHelper.startPage(vo.getPageNo(), vo.getPageSize());
		List<ConnectionPurchaseEntry> list = purchaseListMapper.findSendPurchaseByPage(vo);

		// 设置名称、开始和结束时间
		if (SpringUtil.isNotEmpty(list)) {
			List<Long> supplierIdList = list.stream().map(item -> item.getSupplierId()).collect(Collectors.toList());
			QuerySupplierByIdsIDTO idsIDTO = new QuerySupplierByIdsIDTO();
			idsIDTO.setSupplierIds(supplierIdList);
			Map<Long, FullSupplierODTO> supplierIdAndODTOMap = supplierClient.querySupplierByIds(idsIDTO);
			list.forEach(item -> {
				FullSupplierODTO supplierODTO = supplierIdAndODTOMap.get(item.getSupplierId());
				if (supplierODTO != null) {
					item.setSupplierName(supplierODTO.getSupplierName());
					item.setSupplyStartTime(supplierODTO.getSupplyStartTime());
					item.setSupplyEndTime(supplierODTO.getSupplyEndTime());
				}

			});
		}

		return new PageInfo<>(list);
	}

	public List<SendPurchaseDetailEntry> findSendPurchaseDetail(PurchaseListVo vo) {
		return purchaseListMapper.findSendPurchaseDetail(vo);
	}
}
