package com.pinshang.qingyun.order.controller.settlement;

import com.pinshang.qingyun.order.service.ConsumerOrderSaveService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 * C端生成B端订单
 */
@RestController
@RequestMapping("/settlementConsumerOrder")
public class SettlementConsumerOrderController {

    @Autowired
    private ConsumerOrderSaveService consumerOrderSaveService;

    @PostMapping("/orderSave")
    public Long orderSave(@RequestParam("str") String str){
        return consumerOrderSaveService.saveOrder(str);
    }



}
