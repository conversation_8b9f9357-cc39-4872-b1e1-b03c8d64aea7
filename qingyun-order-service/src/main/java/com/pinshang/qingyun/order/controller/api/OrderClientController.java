package com.pinshang.qingyun.order.controller.api;

import com.pinshang.qingyun.order.dto.xda.v4.TdaDeliveryTimeRangeODTO;
import com.pinshang.qingyun.order.service.OrderService;
import com.pinshang.qingyun.order.service.xda.v4.TdaOrderService;
import com.pinshang.qingyun.order.vo.order.OrderClientVO;
import com.pinshang.qingyun.order.vo.order.OrderReportJobVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;


/**
 * @auther dy
 * @date 2023/12/19  14:15
 **/
@RestController
@RequestMapping("/order/api")
public class OrderClientController {

    @Autowired
    private OrderService orderService;

    @Autowired
    private TdaOrderService tdaOrderService;

    @PostMapping("/getOrderById")
    public OrderClientVO getOrderById(@RequestParam("orderId") Long orderId) {
        return orderService.getOrderById(orderId);
    }

    @RequestMapping(value = "/selectOrderListGiftJob", method = RequestMethod.POST)
    public Long selectOrderListGiftJob(@RequestBody OrderReportJobVO vo) {
        return orderService.selectOrderListGiftJob(vo);
    }

    @RequestMapping(value = "/selectTdaKfDeliveryTimeRangeList", method = RequestMethod.POST)
    public List<TdaDeliveryTimeRangeODTO> selectTdaKfDeliveryTimeRangeList(@RequestParam("orderTime") String orderTime,
                                                                           @RequestParam("storeId") Long storeId) {
        return tdaOrderService.selectTdaKfDeliveryTimeRangeList(orderTime,storeId);
    }

}
