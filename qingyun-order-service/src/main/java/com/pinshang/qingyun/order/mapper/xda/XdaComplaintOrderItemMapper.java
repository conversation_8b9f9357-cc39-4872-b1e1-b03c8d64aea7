package com.pinshang.qingyun.order.mapper.xda;

import com.pinshang.qingyun.base.mybatis.MyMapper;
import com.pinshang.qingyun.order.dto.xda.XdaComplaintCommodityItemDTO;
import com.pinshang.qingyun.order.model.xda.XdaComplaintCommodityItem;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @Version 0.1.0
 * @Date 2021/1/4 11:01
 * @Copyright © 2019-2020 qm
 * @Description -- 类说明
 **/
@Repository
public interface XdaComplaintOrderItemMapper extends MyMapper<XdaComplaintCommodityItem> {

    int updateXdaComplaintOrderItem(@Param("complaintId") String complaintId, @Param("enabled") Boolean enabled, @Param("commodityIdList") List<Long> commodityIdList);

    int delXdaComplaintOrderItem(@Param("complaintId") String complaintId, @Param("commodityId") Long commodityId, @Param("commodityPrice") BigDecimal commodityPrice);

    void updateXdaComplaintOrderItems(List<XdaComplaintCommodityItemDTO> updateList);

    List<XdaComplaintCommodityItem> selectXdaComplaintOrderItem(@Param("complaintId") String complaintId, @Param("enabled") Boolean enabled, @Param("commodityIdList") List<Long> commodityIdList);


}
