package com.pinshang.qingyun.order.service.auto;

import com.github.pagehelper.PageInfo;
import com.pinshang.qingyun.base.configure.expand.QYAssert;
import com.pinshang.qingyun.base.enums.IogisticsModelEnums;
import com.pinshang.qingyun.base.enums.YesOrNoEnums;
import com.pinshang.qingyun.base.enums.order.OrderTypeEnum;
import com.pinshang.qingyun.base.enums.storage.StockTypeEnum;
import com.pinshang.qingyun.base.spring.SpringBeanFinder;
import com.pinshang.qingyun.box.utils.BeanCloneUtils;
import com.pinshang.qingyun.box.utils.DateTimeUtil;
import com.pinshang.qingyun.box.utils.DateUtil;
import com.pinshang.qingyun.box.utils.SpringUtil;
import com.pinshang.qingyun.common.dto.DictionaryODTO;
import com.pinshang.qingyun.common.service.DictionaryClient;
import com.pinshang.qingyun.order.constant.ThreadPoolBeanConstants;
import com.pinshang.qingyun.order.dto.*;
import com.pinshang.qingyun.order.enums.DeliveryBatchTypeEnum;
import com.pinshang.qingyun.order.enums.OrderLaunchTypeEnum;
import com.pinshang.qingyun.order.enums.ProductTypeEnums;
import com.pinshang.qingyun.order.mapper.*;
import com.pinshang.qingyun.order.mapper.auto.AutoPreOrderItemMapper;
import com.pinshang.qingyun.order.mapper.auto.ShopAutoOrderMapper;
import com.pinshang.qingyun.order.mapper.entry.MdShopOrderSettingEntry;
import com.pinshang.qingyun.order.mapper.entry.commodity.CommodityBasicEntry;
import com.pinshang.qingyun.order.mapper.entry.commodity.ProductLimitEntry;
import com.pinshang.qingyun.order.mapper.entry.store.StoreDurationEntry;
import com.pinshang.qingyun.order.model.auto.AutoOrderLog;
import com.pinshang.qingyun.order.model.commodity.Commodity;
import com.pinshang.qingyun.order.model.order.ShoppingCartItem;
import com.pinshang.qingyun.order.model.promotion.Promotion;
import com.pinshang.qingyun.order.model.promotion.PromotionProduct;
import com.pinshang.qingyun.order.model.shop.Shop;
import com.pinshang.qingyun.order.model.store.Store;
import com.pinshang.qingyun.order.service.*;
import com.pinshang.qingyun.order.util.OrderTimeUtil;
import com.pinshang.qingyun.order.util.ThreadLocalUtils;
import com.pinshang.qingyun.order.vo.commodity.CommodityListRequestVO;
import com.pinshang.qingyun.order.vo.commodity.CommodityVO;
import com.pinshang.qingyun.order.vo.order.*;
import com.pinshang.qingyun.price.dto.commodity.CommodityListRequestIDTO;
import com.pinshang.qingyun.price.dto.commodity.CommodityResultODTO;
import com.pinshang.qingyun.price.service.ProductPriceModelClient;
import com.pinshang.qingyun.report.dto.ShopAutoCommodityTaxDTO;
import com.pinshang.qingyun.report.service.MdCommodityTaxClient;
import com.pinshang.qingyun.shop.dto.shopCommodity.ShopCommodityInfoODTO;
import com.pinshang.qingyun.storage.dto.tob.CommodityInventoryODTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.redisson.api.RAtomicLong;
import org.redisson.api.RBucket;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.entity.Example;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @Author: sk
 * @Date: 2022/5/11
 */

@Slf4j
@Service
public class ShopAutoOrderService {

    @Autowired
    private RedissonClient redissonClient;
    @Autowired
    private ShopAutoOrderMapper shopAutoOrderMapper;
    @Autowired
    private CommodityMapper commodityMapper;
    @Autowired
    private ProductPriceModelClient productPriceModelClient;
    @Autowired
    @Lazy
    private MdShopOrderSettingService mdShopOrderSettingService;
    @Autowired
    private ProductService productService;
    @Autowired
    private MdCommodityTaxClient mdCommodityTaxClient;
    @Lazy
    @Autowired
    private OrderService orderService;
    @Autowired
    private CommonService commonService;
    @Autowired
    private StoreMapper storeMapper;
    @Autowired
    private DictionaryClient dictionaryClient;
    @Autowired
    private GrouponOrderSaveService grouponOrderSaveService;
    @Autowired
    private AutoPreOrderItemMapper autoPreOrderItemMapper;
    @Autowired
    private ShoppingCartItemMapper shoppingCartItemMapper;
    @Autowired
    private ShoppingCartMapper shoppingCartMapper;
    @Autowired
    private WeChatSendMessageService weChatSendMessageService;
    @Autowired
    private CommodityService commodityService;
    @Autowired
    private OrderSplitService orderSplitService;
    @Autowired
    private OrderMapper orderMapper;
    @Lazy
    @Autowired
    private BStockService bStockService;
    private static String SHOP_AUTO_ORDER = "门店自动订货提交订单: ";
    @Autowired
    private ShopMapper shopMapper;
    private static AtomicInteger count = new AtomicInteger(0);

    /**
     * 自动订货失败之后，异步重新跑一次
     * @param autoStr = shopId + "," + storeId + "," + orderTime
     */
    @Async
    public void againAutoOrderAsync(String autoStr) {

        String [] str = autoStr.split(",");
        Long shopId = Long.valueOf(str[0]);
        Long storeId = Long.valueOf(str[1]);
        String orderTime = str[2];

        try{
            // 自动订货报错才会进来，不会有太多的错误
            // 线程并发过来,进行随机sleep.最多sleep 150秒，count会重新置为0

            Thread.sleep((count.getAndIncrement() + 1)* 3000L);
            if(StringUtils.isNotBlank(autoStr)){
                // 获取冻品凑整商品组
                List<Long> freezeGroupList = commodityService.getCommodityFreezeGroup();

                String Key =  "shop_auto_async" + shopId + orderTime.replaceAll(":","");
                RBucket<String> bucket = redissonClient.getBucket(Key);
                String auto = bucket.get();
                if(StringUtils.isEmpty(auto)){
                    bucket.set(autoStr,30, TimeUnit.MINUTES);
                    createShopAutoOrder(shopId, storeId, orderTime, freezeGroupList);
                }else {
                    log.warn("门店自动订货异步执行失败2 {}", autoStr);
                }
            }
        }catch (Exception e){
            log.error("门店自动订货异步执行失败 {}", autoStr, e);
            StringBuffer sb = new StringBuffer();
            sb.append("门店自动订货提交订单异常,门店id: " + shopId);
            sb.append(" 截单时间: " + orderTime);
            weChatSendMessageService.sendWeChatMessage(sb.toString());
        }finally {
            if(count.get() >= 50){
                count.set(0);
            }
        }
    }

    /**
     * 迁移到order-manage
     * @param orderTime
     * @param shopIdList
     * @return
     */
    public Boolean testAutoOrder(String orderTime, List<Long> shopIdList){
        QYAssert.isTrue(StringUtils.isNotBlank(orderTime), "截单时间不能为空");
        QYAssert.isTrue(CollectionUtils.isNotEmpty(shopIdList), "门店idList不能为空");

        Example ex = new Example(Shop.class);
        ex.createCriteria().andIn("id", shopIdList);
        List<Shop> shopList = shopMapper.selectByExample(ex);

        // 打乱门店顺序
        Collections.shuffle(shopList);
        // 获取冻品凑整商品组
        List<Long> freezeGroupList = commodityService.getCommodityFreezeGroup();

        // 获取线程池
        ThreadPoolTaskExecutor threadPool = (ThreadPoolTaskExecutor) SpringBeanFinder.getBean(ThreadPoolBeanConstants.AUTO_ORDER_THREADPOOL);

        for(Shop shop : shopList){
            // 创建任务并提交到线程池中
            threadPool.execute(new Runnable() {
                @Override
                public void run() {
                    try {
                        createShopAutoOrder(shop.getId(), shop.getStoreId(), orderTime, freezeGroupList);
                    } catch (Exception e) {
                        log.error("门店自动订货提交订单异常 shopId: {} orderTime: {} Exception: {}", shop.getId(), orderTime, e);

                        StringBuffer sb = new StringBuffer();
                        sb.append("门店自动订货提交订单异常,门店id: " + shop.getId());
                        sb.append(" 截单时间: " + orderTime);
                        weChatSendMessageService.sendWeChatMessage(sb.toString());
                    }
                }
            });
        }

        return Boolean.TRUE;
    }

    public List<AutoShopCommodityODTO> queryAutoShopList(){
        return shopAutoOrderMapper.queryAutoShopList();
    }

    /**
     * 门店自动订货截单时间前30分钟计算提交订单
     * @param idto
     * @return
     */
    @Async
    public Boolean createShopAutoOrder(ShopAutoOrderJobIDTO idto){
        String orderTime = idto.getOrderTime();
        // 校验重复任务
        String lockKey =  "shop_auto_create_order" + orderTime.replaceAll(":","") + "_" + idto.getBatchIndex();
        RAtomicLong lockKeyRa = redissonClient.getAtomicLong(lockKey);
        long lock = lockKeyRa.incrementAndGet();
        if (lock == 1) {
            lockKeyRa.expire(10, TimeUnit.MINUTES);
        }
        if (lock > 1) {
            return false;
        }

        // 查询开启的自动订货门店
        List<AutoShopCommodityODTO> autoShopList = idto.getAutoShopCommodityODTOList();
        if(CollectionUtils.isEmpty(autoShopList)){
            return false;
        }

        // 获取冻品凑整商品组
        List<Long> freezeGroupList = commodityService.getCommodityFreezeGroup();

        // 获取线程池
        ThreadPoolTaskExecutor threadPool = (ThreadPoolTaskExecutor) SpringBeanFinder.getBean(ThreadPoolBeanConstants.AUTO_ORDER_THREADPOOL);

        for(AutoShopCommodityODTO autoShopODTO : autoShopList){
            // 创建任务并提交到线程池中
            threadPool.execute(new Runnable() {
                @Override
                public void run() {
                    try {
                         createShopAutoOrder(autoShopODTO.getShopId(), autoShopODTO.getStoreId(), orderTime, freezeGroupList);
                    } catch (Exception e) {
                        log.error("门店自动订货提交订单异常 shopId: {} orderTime: {} Exception: {}", autoShopODTO.getShopId(), orderTime, e);
                        String autoStr = autoShopODTO.getShopId() + "," + autoShopODTO.getStoreId() + "," + orderTime;
                        againAutoOrderAsync(autoStr);
                    }
                }
            });
        }

        return Boolean.TRUE;
    }


    /**
     * 门店自动订货提交订单
     * @return
     */
    public Boolean createShopAutoOrder(Long shopId, Long storeId, String orderTime, List<Long> freezeGroupList) {
        List<AutoShopCommodityODTO> autoShopCommodityList = shopAutoOrderMapper.queryAutoShopCommodityList(shopId);
        if(CollectionUtils.isEmpty(autoShopCommodityList)){
            log.warn(SHOP_AUTO_ORDER + "门店下无商品池,门店id: {} 截单时间: {}", shopId, orderTime);
            return false;
        }

        List<Long> autoShopCommodityIdList = autoShopCommodityList.stream().map(item -> item.getCommodityId()).collect(Collectors.toList());
        // 查询当前门店当天已经订的商品
        List<Long> alreadyCommodityIdList = shopAutoOrderMapper.queryAutoOrderLogCommodityIds(DateUtil.getDateFormate(new Date(), "yyyy-MM-dd") + " 00:00:00",
                                                            DateUtil.getDateFormate(new Date(), "yyyy-MM-dd") + " 23:59:59", storeId);
        if(CollectionUtils.isNotEmpty(alreadyCommodityIdList)){
            autoShopCommodityIdList = autoShopCommodityIdList.stream().filter(p -> !alreadyCommodityIdList.contains(p)).collect(Collectors.toList());
        }

        if(CollectionUtils.isEmpty(autoShopCommodityIdList)){
            log.warn(SHOP_AUTO_ORDER + "商品已经全部自动订货,门店id: {} 截单时间: {}", shopId, orderTime);
            return false;
        }

        // 查询门店可采、可售状态的商品
        CommodityListRequestVO commodityListRequestVO = new CommodityListRequestVO();
        commodityListRequestVO.setShopId(shopId);
        commodityListRequestVO.setCommodityIdList(autoShopCommodityIdList);
        List<Long> commodityPurchaseList = commodityMapper.findShopCommodityPurchaseList(commodityListRequestVO);
        if(CollectionUtils.isEmpty(commodityPurchaseList)){
            log.warn(SHOP_AUTO_ORDER + "门店无可采、可售商品.门店id: {} 截单时间: {} 商品idList: {}", shopId, orderTime, autoShopCommodityIdList);
            return false;
        }

        autoShopCommodityIdList = new ArrayList<>();
        autoShopCommodityIdList.addAll(commodityPurchaseList);

        // 调用client 去 qingyun-price 查询
        CommodityListRequestIDTO idto = new CommodityListRequestIDTO();
        idto.setStoreId(storeId + "");
        idto.setCommodityIdListAll(autoShopCommodityIdList);
        idto.setPageNo(1);
        idto.setPageSize(Integer.MAX_VALUE);
        PageInfo<CommodityResultODTO> resultPageData = productPriceModelClient.findStoreCommodityListByPage(idto);
        List<CommodityResultODTO> pageList = resultPageData.getList();
        if(CollectionUtils.isEmpty(pageList)){
            log.warn(SHOP_AUTO_ORDER + "客户价格方案列表为空,门店id: {} 截单时间: {} 商品idList: {}", shopId, orderTime, autoShopCommodityIdList);
            return false;
        }

        // 过滤掉价格为null或者0的订单商品
        // 过滤订单价格为0的商品
        pageList = pageList.stream().filter(p -> p.getCommodityPrice() != null
                                            && p.getCommodityPrice().compareTo(BigDecimal.ZERO) > 0).collect(Collectors.toList());


        if(CollectionUtils.isEmpty(pageList)){
            log.warn(SHOP_AUTO_ORDER + "所有商品价格为0，门店id: {} 截单时间: {}", shopId, orderTime);
            return false;
        }
        List<String> productPriceCommodityIdStrList = pageList.stream().map(item -> item.getCommodityId()).collect(Collectors.toList());
        List<Long> productPriceCommodityIdList = pageList.stream().map(item -> Long.valueOf(item.getCommodityId())).collect(Collectors.toList());
        Map<Long, AutoShopCommodityODTO> autoShopCommodityMap = getAutoShopCommodityMap(autoShopCommodityList, pageList);

        // 获取鲜到库存(这里的库存已经加上了在途数量)
        Map<Long, ShopCommodityInfoODTO> xdStockMap = productService.getXdShopCommodityStock2(shopId,productPriceCommodityIdList);
        // 获取门店订货通用设置
        List<MdShopOrderSettingEntry> mdShopOrderSettingList = mdShopOrderSettingService.queryMdShopOrderSettingListByIds(storeId, productPriceCommodityIdStrList);
        if(CollectionUtils.isEmpty(mdShopOrderSettingList)){
            log.warn(SHOP_AUTO_ORDER + "门店订货通用设置为空,门店id: {} 截单时间: {} 商品idList: {}", shopId, orderTime, productPriceCommodityIdStrList);
            return false;
        }

        // 此次截单范围内的 filterSettingList
        List<MdShopOrderSettingEntry> cutOffSettingList = new ArrayList<>(pageList.size());
        for(MdShopOrderSettingEntry settingEntry : mdShopOrderSettingList){
            Integer logisticsModel = settingEntry.getLogisticsModel().intValue();
            // 配送比较仓库时间
            if( logisticsModel == IogisticsModelEnums.DISPATCHING.getCode()){
                if(DateTimeUtil.compareTime(OrderTimeUtil.addMinute(settingEntry.getDefaultWarehouseEndTime(), -30),orderTime)
                        && DateTimeUtil.compareTime(DateUtil.getDateFormate(new Date(), "HH:mm"), settingEntry.getDefaultWarehouseEndTime())){
                    cutOffSettingList.add(settingEntry);
                }
            } else if(logisticsModel == IogisticsModelEnums.DIRECT_CONNECTION.getCode()){
                //直通比较供应商时间
                if(DateTimeUtil.compareTime(OrderTimeUtil.addMinute(settingEntry.getDefaultSupplierEndTime(),-30),orderTime)
                        &&  DateTimeUtil.compareTime(DateUtil.getDateFormate(new Date(), "HH:mm"), settingEntry.getDefaultSupplierEndTime())){
                    cutOffSettingList.add(settingEntry);
                }
            }
        }
        if(CollectionUtils.isEmpty(cutOffSettingList)){
            List<Long> commodityIdList = mdShopOrderSettingList.stream().map(item -> item.getCommodityId()).collect(Collectors.toList());
            log.warn(SHOP_AUTO_ORDER + "门店id: {} 截单时间: {} 商品idList: {} 没有可提交订单数据", shopId, orderTime, commodityIdList);
            return false;
        }

        List<Long> cutOffCommodityIdList = cutOffSettingList.stream().map(item -> item.getCommodityId()).collect(Collectors.toList());
        log.warn(SHOP_AUTO_ORDER + "门店id: {} 截单时间: {} 商品idList: {} 当前满足截单时间的数据", shopId, orderTime, cutOffCommodityIdList);

        // shopAutoOrderList 为所有订单商品(包括截单的和没有截单的)
        // 根据门店订货通用设置按照orderTime分组，并过滤掉限量商品
        // 并且是否速冻根据凑整商品组来定
        List<ShopAutoOrderODTO> shopAutoOrderList = getFilterShopOrderList(shopId, storeId, autoShopCommodityMap, mdShopOrderSettingList, freezeGroupList);
        if(CollectionUtils.isEmpty(shopAutoOrderList)){
            List<Long> filterLimitIdList = shopAutoOrderList.stream().distinct().map(item -> item.getCommodityId()).collect(Collectors.toList());
            log.warn(SHOP_AUTO_ORDER + "特价限量、客户/产品价格方案限量.商品被过滤完了,门店id: {} 截单时间: {} 商品idList: {}", shopId, orderTime, filterLimitIdList);
            return false;
        }else {
            // 判断原始商品个数是否和过滤后的商品个数一样
            List<Long> filterLimitIdList = shopAutoOrderList.stream().distinct().map(item -> item.getCommodityId()).collect(Collectors.toList());
            List<Long> commodityIdList = mdShopOrderSettingList.stream().map(item -> item.getCommodityId()).collect(Collectors.toList());
            if(filterLimitIdList.size() < commodityIdList.size()){
                List<Long> filterIdList = getShortCommodityIdList(commodityIdList, filterLimitIdList);
                log.warn(SHOP_AUTO_ORDER + "特价限量、客户/产品价格方案限量被过滤的商品信息如下 ,门店id: {} 截单时间: {} 商品idList: {}", shopId, orderTime, filterIdList);
            }
        }

        // B端库存依据，冻品不判断30的倍数。和普通商品一样按照缺货来定。
        // 如果当前截单时间里面冻品，,则把所有冻品在当前截单时间内提交.
        List<ShopAutoOrderODTO> resultShopAutoOrderList = getResultShopAutoOrderList(shopAutoOrderList, cutOffCommodityIdList, xdStockMap); // 最终符合的数据list

        /*//  冻品list  如果当前截单时间里面有冻品,则把所有冻品在当前截单时间内提交.
        List<ShopAutoOrderODTO> freezeShopAutoOrderList = shopAutoOrderList.stream().filter(p -> p.getCommodityIsQuickFreeze().equals(YesOrNoEnums.YES.getCode())).collect(Collectors.toList());
        if(CollectionUtils.isNotEmpty(freezeShopAutoOrderList)){
            List<ShopAutoOrderODTO> quickFreezeList = getQuickFreezeList(shopId, shopAutoOrderList, xdStockMap, freezeGroupList);
            if(CollectionUtils.isNotEmpty(quickFreezeList)){
                resultShopAutoOrderList.addAll(quickFreezeList);
            }
        }
        // 非冻品list
        List<ShopAutoOrderODTO> noFreezeShopAutoOrderList = shopAutoOrderList.stream().filter(p -> !p.getCommodityIsQuickFreeze().equals(YesOrNoEnums.YES.getCode())).collect(Collectors.toList());
        if(CollectionUtils.isNotEmpty(noFreezeShopAutoOrderList)){
            List<ShopAutoOrderODTO> noQuickFreezeList = getNoQuickFreezeList(noFreezeShopAutoOrderList, xdStockMap);
            if(CollectionUtils.isNotEmpty(noQuickFreezeList)){
                resultShopAutoOrderList.addAll(noQuickFreezeList);
            }
        }*/

        if(CollectionUtils.isEmpty(resultShopAutoOrderList)){
            List<Long> idList = shopAutoOrderList.stream().distinct().map(item -> item.getCommodityId()).collect(Collectors.toList());
            log.warn(SHOP_AUTO_ORDER + "门店id: {} 截单时间: {} 商品idList: {}  冻品、非冻品,无缺货数据", shopId, orderTime, idList);
            return false;
        }

        // 按orderTime分组，判断库存依据。不足库存的按照剩余可用库存订货
        Map<String, List<ShopAutoOrderODTO>> shopAutoOrderMap = resultShopAutoOrderList.stream().collect(Collectors.groupingBy(ShopAutoOrderODTO::getOrderTime));
        shopAutoOrderMap.forEach((entry, list)->{
            String orderDate = entry;
            // 校验B端库存依据
            Map<Long, BigDecimal> orderQuantityMap = new HashMap<>();
            list.forEach(item->{
                orderQuantityMap.put(item.getCommodityId(), item.getQuantity());
            });
            Map<Long, CommodityInventoryODTO> toBStockMap = bStockService.getbStockMap(DateUtil.parseDate(orderDate, "yyyy-MM-dd"), orderQuantityMap);
            List<BStockShortResponseVO> responseVOList = bStockService.checkBStock(storeId, OrderTypeEnum.SHOP_AUTO_ORDER.getCode(),
                    DateUtil.parseDate(orderDate, "yyyy-MM-dd"), orderQuantityMap, "自动订货", toBStockMap,-1L);
            Map<String, BigDecimal> shortStockMap;
            if(CollectionUtils.isNotEmpty(responseVOList)){
                //库存不足的，按照剩余库存来定
                shortStockMap = responseVOList.stream().collect(Collectors.toMap(BStockShortResponseVO::getCommodityId,BStockShortResponseVO::getInventoryQuantity,(key1 , key2)-> key2));
            }else {
                shortStockMap = new HashMap<>();
            }

            list.forEach(item->{
                item.setPresaleStatus(YesOrNoEnums.NO.getCode());
                CommodityInventoryODTO commodityInventoryODTO = toBStockMap.get(item.getCommodityId());
                item.setStockType(commodityInventoryODTO != null ? commodityInventoryODTO.getStockType() : StockTypeEnum.UN_LIMIT.getCode());
                if (Objects.nonNull(commodityInventoryODTO)) {
                    Integer sourceRatio = commodityInventoryODTO.getSourceRatio();
                    item.setSourceRatio(sourceRatio);
                    Integer targetRatio = commodityInventoryODTO.getTargetRatio();
                    item.setTargetRatio(targetRatio);
                    Long targetCommodityId = commodityInventoryODTO.getTargetCommodityId();
                    item.setTargetCommodityId(targetCommodityId);
                    Integer convertStatus = commodityInventoryODTO.getCanConvert() ? 1 : 0;
                    item.setConvertStatus(convertStatus);
                    BigDecimal quantity = item.getQuantity();
                    // 组合商品目标数量 : 向上取整 ( quantity / source_ratio * target_ratio)
                    if (commodityInventoryODTO.getCanConvert()) {
                        BigDecimal targetQuantity = quantity.divide(new BigDecimal(sourceRatio), 0, RoundingMode.UP).multiply(new BigDecimal(targetRatio));
                        item.setTargetQuantity(targetQuantity);
                    }
                }

                if(shortStockMap.get(item.getCommodityId() + "") != null){
                    // 可用库存数量
                    BigDecimal inventoryQuantity = shortStockMap.get(item.getCommodityId() + "");
                    item.setQuantity(inventoryQuantity.divide(item.getSalesBoxCapacity(),0, BigDecimal.ROUND_DOWN)
                            .multiply(item.getSalesBoxCapacity()));
                }
            });

        });

        List<Long> idList = resultShopAutoOrderList.stream().distinct().map(item -> item.getCommodityId()).collect(Collectors.toList());
        resultShopAutoOrderList = resultShopAutoOrderList.stream().filter(p -> p.getQuantity().compareTo(BigDecimal.ZERO) > 0 ).collect(Collectors.toList());
        if(CollectionUtils.isEmpty(resultShopAutoOrderList)){
            log.warn(SHOP_AUTO_ORDER + "门店id: {} 截单时间: {} 商品idList: {}  库存全部不足了", shopId, orderTime, idList);
            return false;
        }

        // 转成OrderDto对象
        List<OrderDto> orderDtoList = orderSplitService.convertToOrderDto(resultShopAutoOrderList, "自动推送", true);

        ThreadLocalUtils.setOrderType(OrderTypeEnum.SHOP_AUTO_ORDER.getCode());
        String autoStr = shopId + "," + storeId + "," + orderTime;
        // 循环创建订单
        for(OrderDto orderDto : orderDtoList){
            // 更新一下最新的配置: 商品价格、商品限量、商品是否可变价、商品名称
            orderService.buildProductPrice(orderDto);
            this.createOrder(orderDto, autoStr);
        }
        ThreadLocalUtils.remove();
        return Boolean.TRUE;
    }

    private List<ShopAutoOrderODTO> getResultShopAutoOrderList(List<ShopAutoOrderODTO> shopAutoOrderList, List<Long> cutOffCommodityIdList, Map<Long, ShopCommodityInfoODTO> xdStockMap) {
        List<ShopAutoOrderODTO> resultShopAutoOrderList = new ArrayList<>();
        // 判断当前截单时间的商品是否有冻品
        List<ShopAutoOrderODTO> shopAutoFreezeList = shopAutoOrderList.stream().filter(p -> p.getCommodityIsQuickFreeze().equals(YesOrNoEnums.YES.getCode())
                        && cutOffCommodityIdList.contains(p.getCommodityId())).collect(Collectors.toList());

        Boolean containFreeze = CollectionUtils.isNotEmpty(shopAutoFreezeList);
        for(ShopAutoOrderODTO orderODTO : shopAutoOrderList){
            if(cutOffCommodityIdList.contains(orderODTO.getCommodityId())
                    || (containFreeze && orderODTO.getCommodityIsQuickFreeze().equals(YesOrNoEnums.YES.getCode())) ){
                ShopCommodityInfoODTO shopCommodityInfo = xdStockMap.get(orderODTO.getCommodityId());
                if(shopCommodityInfo != null){
                    // 库存数小于安全库存
                    if(shopCommodityInfo.getStockQuantity().compareTo(orderODTO.getSafeQuantity()) < 0){
                        // (安全库存-库存数) / 销售箱规 向上取整 *总部的销售箱规
                        BigDecimal quantity = orderODTO.getSafeQuantity().subtract(shopCommodityInfo.getStockQuantity())
                                .divide(orderODTO.getSalesBoxCapacity(),0, BigDecimal.ROUND_UP)
                                .multiply(orderODTO.getSalesBoxCapacity());
                        orderODTO.setQuantity(quantity);
                        resultShopAutoOrderList.add(orderODTO);
                    }
                }
            }
        }
        return resultShopAutoOrderList;
    }

    /**
     * 对比获取第一个list有，第二个list没有的返回
     * @param commodityIdList
     * @param filterLimitIdList
     * @return
     */
    @NotNull
    private List<Long> getShortCommodityIdList(List<Long> commodityIdList, List<Long> filterLimitIdList) {
        List<Long> filterIdList = new ArrayList<>();
        commodityIdList.forEach(i -> {
           if(!filterLimitIdList.contains(i)){
               filterIdList.add(i);
           }
        });
        return filterIdList;
    }

    /**
     *过滤特价限量、客户/产品价格方案限量、库存限量商品
     * @return
     */
    private List<ShopAutoOrderODTO> getFilterShopOrderList(Long shopId, Long storeId, Map<Long, AutoShopCommodityODTO> autoShopCommodityMap, List<MdShopOrderSettingEntry> settingList, List<Long> freezeGroupList) {
        List<ShopAutoOrderODTO> shopAutoOrderList = new ArrayList<>();
        for(MdShopOrderSettingEntry settingEntry : settingList){
            ShopAutoOrderODTO shopAutoOrderODTO = getShopAutoOrderODTO(shopId, storeId, settingEntry, autoShopCommodityMap, freezeGroupList);
            shopAutoOrderList.add(shopAutoOrderODTO);
        }
        // 按orderTime分组
        Map<String, List<ShopAutoOrderODTO>> shopAutoOrderMap = shopAutoOrderList.stream().collect(Collectors.groupingBy(ShopAutoOrderODTO::getOrderTime));

        // 需要过滤限量的商品list
        List<String> filterCommodityCodeList = new ArrayList<>();
        List<String> filterCommodityIdList = new ArrayList<>();
        for(Map.Entry<String, List<ShopAutoOrderODTO>> entry : shopAutoOrderMap.entrySet()){
            String orderDate = entry.getKey();
            // 获取 特价限量商品编码list
            List<String> specialPriceCommodityCodeList = getSpecialPriceCommodity(storeId, orderDate);
            if(CollectionUtils.isNotEmpty(specialPriceCommodityCodeList)){
                filterCommodityCodeList.addAll(specialPriceCommodityCodeList);
            }
            // 获取 客户/产品价格方案限量商品id信息
            List<String> productLimitCommodityIdList = getProductLimitCommodityList(storeId, orderDate);
            if(CollectionUtils.isNotEmpty(productLimitCommodityIdList)){
                filterCommodityIdList.addAll(productLimitCommodityIdList);
            }
        }
        if(CollectionUtils.isNotEmpty(filterCommodityCodeList)){
            shopAutoOrderList = shopAutoOrderList.stream().filter(p -> !filterCommodityCodeList.contains(p.getCommodityCode())).collect(Collectors.toList());
        }
        if(CollectionUtils.isNotEmpty(filterCommodityIdList)){
            shopAutoOrderList = shopAutoOrderList.stream().filter(p -> !filterCommodityIdList.contains(p.getCommodityId() + "")).collect(Collectors.toList());
        }
        return shopAutoOrderList;
    }

    /**
     * 获取特价限量的商品信息
     * @return
     */
    public List<String> getSpecialPriceCommodity(Long storeId, String orderTime){
        List<String> commodityCodeList = new ArrayList<>();
        // 设置特价和限量
        List<Promotion> promotionList = commonService.findCommodityPromotionByStoreId(storeId + "", orderTime);
        if(CollectionUtils.isNotEmpty(promotionList)){
            promotionList.forEach(p->{ //多特价
                List<PromotionProduct> promotionProductList = commonService.findPromotionProductByPromotionId(p.getId().toString());
                if(SpringUtil.isNotEmpty(promotionProductList)){
                    promotionProductList.forEach(pp->{
                        // 如果限购才过滤(不管总限量还是客户限购，有一个限购就算限购)
                        Boolean isLimit = (pp.getTotalLimitNumber() > 0 || pp.getLimitNumber() > 0);
                        if(isLimit && !commodityCodeList.contains(pp.getProductCode())){
                            commodityCodeList.add(pp.getProductCode());
                        }
                    });
                }
            });
        }
        return commodityCodeList;
    }

    /**
     * 获取 客户/产品价格方案限量商品信息
     * @return
     */
    public List<String> getProductLimitCommodityList(Long storeId, String orderTime){
        List<String> productLimitCommodityIdList = new ArrayList<>();

        // 客户/产品价格方案限量
        List<ProductLimitEntry> limitProductList = orderService.findLimitProductByStoreId(storeId + "");
        if(CollectionUtils.isNotEmpty(limitProductList)){
            limitProductList.forEach(pp->{
                if(!productLimitCommodityIdList.contains(pp.getCommodityId())){
                    productLimitCommodityIdList.add(pp.getCommodityId());
                }
            });
        }

        // 库存限量
        /*List<ProductLimitEntry> productLimitList = commonService.findCommodityLimitByNowData(orderTime);
        if(CollectionUtils.isNotEmpty(productLimitList)){
            productLimitList.forEach(pp->{
                if(!productLimitCommodityIdList.contains(pp.getCommodityId())){
                    productLimitCommodityIdList.add(pp.getCommodityId());
                }
            });
        }*/
        return productLimitCommodityIdList;
    }

    /**
     * 门店订货商品池转map，并赋值价格
     */
    @NotNull
    private Map<Long, AutoShopCommodityODTO> getAutoShopCommodityMap(List<AutoShopCommodityODTO> autoShopCommodityList, List<CommodityResultODTO> pageList) {
        Map<String, CommodityResultODTO> productPriceResultMap = pageList.stream().collect(Collectors.toMap(CommodityResultODTO::getCommodityId, Function.identity()));
        // 给价格赋值
        for(AutoShopCommodityODTO autoShopCommodityODTO : autoShopCommodityList){
            CommodityResultODTO commodityResult = productPriceResultMap.get(autoShopCommodityODTO.getCommodityId() + "");
            if(commodityResult != null){
                autoShopCommodityODTO.setPrice(commodityResult.getCommodityPrice());
            }
        }

        Map<Long, AutoShopCommodityODTO> autoShopCommodityMap = autoShopCommodityList.stream().collect(Collectors.toMap(AutoShopCommodityODTO::getCommodityId, Function.identity()));
        return autoShopCommodityMap;
    }

    /**
     * 创建订单
     * @param orderDto
     */
    public Boolean createOrder(OrderDto orderDto, String autoStr)  {

        // 直送订单应不能参与配货/赠品方案
        if (IogisticsModelEnums.DIRECT_SENDING.getCode() != orderDto.getLogisticsModel()) {
            Store store = storeMapper.selectByPrimaryKey(orderDto.getStoreId());
            List<DictionaryODTO> dictionaryList = dictionaryClient.listDictionaryByOptionName("订单配货白名单");
            List<String> storeCodeList = dictionaryList.stream().map(item -> item.getOptionCode()).collect(Collectors.toList());
            // 如果当前客户在配货白名单里面，则不配货
            if(CollectionUtils.isEmpty(storeCodeList) || !storeCodeList.contains(store.getStoreCode())){
                // 3. 配货
                orderService.unifyProcessDistribution(orderDto.getStoreId() + "", orderDto, OrderLaunchTypeEnum.SHOP);
                // 过滤系统商品(赠送、配货商品)
                orderService.filterSystemCommodity(orderDto.getStoreId() + "", orderDto);

                // 配货处理，过滤掉依据大仓和限量的.(此处处理是因为下面的赠品条件是订单商品和配货商品和)
                List<OrderItemDto> rationItemList = orderDto.getItems().stream().filter(item -> ProductTypeEnums.RATION.getCode().equals(item.getType())).collect(Collectors.toList());
                if(CollectionUtils.isNotEmpty(rationItemList)){
                    orderService.giftAndRationDeal(rationItemList, orderDto);
                }

            }
            // 4. 赠送
            orderService.unifyProcessGifts(orderDto.getStoreId() + "", orderDto,OrderTypeEnum.STORE_ORDER);
            // 过滤系统商品(赠送、配货商品)
            orderService.filterSystemCommodity(orderDto.getStoreId() + "", orderDto);

            // 赠品(数量不够，补全当时可用库存)
            List<OrderItemDto> giftItemList = orderDto.getItems().stream().filter(item -> ProductTypeEnums.GIFT.getCode().equals(item.getType()) ).collect(Collectors.toList());
            if(CollectionUtils.isNotEmpty(giftItemList)){
                // 不包含配货，只对订单商品和赠品处理(加上订单商品就是避免订单商品和赠送商品一样。判断赠品库存不准确问题)
                List<OrderItemDto> noRationList = orderDto.getItems().stream().filter(item -> !ProductTypeEnums.RATION.getCode().equals(item.getType())).collect(Collectors.toList());
                orderService.giftAndRationDeal(noRationList, orderDto);
            }
        }

        if(CollectionUtils.isEmpty(orderDto.getItems())){
            log.warn(SHOP_AUTO_ORDER + "有效商品为空，客户id " + orderDto.getStoreId());
            return true;
        }

        grouponOrderSaveService.saveGroupOrder(orderDto, autoStr);

        // 记录日志
        List<AutoOrderLog> autoOrderLogList = new ArrayList<>();
        orderDto.getItems().forEach(i->{
            AutoOrderLog autoOrderLog = new AutoOrderLog();
            autoOrderLog.setStoreId(orderDto.getStoreId());
            autoOrderLog.setCommodityId(Long.valueOf(i.getProductId()));
            autoOrderLog.setQuantity(i.getProductNum());
            autoOrderLog.setCreateId(1L);
            autoOrderLog.setCreateTime(new Date());
            autoOrderLogList.add(autoOrderLog);
          });
        shopAutoOrderMapper.batchInsertAutoOrderLog(autoOrderLogList);
        return Boolean.TRUE;
    }


    /**
     * 设置冻品下单数量
     * @return
     */
    private List<ShopAutoOrderODTO> getQuickFreezeList(Long shopId,
                               List<ShopAutoOrderODTO> shopAutoOrderList,
                               Map<Long, ShopCommodityInfoODTO> xdStockMap,
                               List<Long> freezeGroupList) {
        // 过滤完之后的commodityIdList
        List<Long> commodityIdList = shopAutoOrderList.stream().map(item -> item.getCommodityId()).collect(Collectors.toList());
        Map<Long, ShopAutoOrderODTO> shopAutoOrderMap = shopAutoOrderList.stream().collect(Collectors.toMap(ShopAutoOrderODTO::getCommodityId, Function.identity()));

        List<ShopAutoOrderODTO> resultList = new ArrayList<>(); // 最终符合数据的list
        List<ShopAutoOrderODTO> allQuickFreezeList = new ArrayList<>();// 冻品不足的商品

        for(ShopAutoOrderODTO orderODTO : shopAutoOrderList){
            ShopCommodityInfoODTO shopCommodityInfo = xdStockMap.get(orderODTO.getCommodityId());
            // 把所有缺货的冻品在当前截单时间内提交
            if(orderODTO.getCommodityIsQuickFreeze() == YesOrNoEnums.YES.getCode()){
                orderODTO.setStockQuantity(shopCommodityInfo != null ? shopCommodityInfo.getStockQuantity() : new BigDecimal(0));
                if(orderODTO.getStockQuantity().compareTo(orderODTO.getSafeQuantity()) < 0 ){
                    allQuickFreezeList.add(orderODTO);
                }
            }
        }

        if(CollectionUtils.isNotEmpty(allQuickFreezeList)){
            // 获取上个月的销量从高往低，最多分配销量top30的且当前在自动订货池里的冻品(按orderTime进行分组)
            Map<String, List<ShopAutoOrderODTO>> top30GroupMap = getTop30GroupMap(shopId, commodityIdList, shopAutoOrderMap, freezeGroupList);
            // 按orderTime分组
            Map<String, List<ShopAutoOrderODTO>> shopAutoOrderGroupMap = allQuickFreezeList.stream().collect(Collectors.groupingBy(ShopAutoOrderODTO::getOrderTime));
            for(Map.Entry<String, List<ShopAutoOrderODTO>> entry : shopAutoOrderGroupMap.entrySet()){
                String orderTime = entry.getKey();
                List<ShopAutoOrderODTO> groupQuickFreezeList = entry.getValue();

                // 分组内不足的冻品的安全库存减去库存数大于15
                Double allFreezeSafeQuantity =  groupQuickFreezeList.stream().mapToDouble(item -> item.getSafeQuantity().doubleValue()).sum();
                Double allFreezeStockQuantity =  groupQuickFreezeList.stream().mapToDouble(item -> item.getStockQuantity().doubleValue()).sum();
                BigDecimal shortQuantity = new BigDecimal(allFreezeSafeQuantity - allFreezeStockQuantity);
                if(shortQuantity.compareTo(new BigDecimal("15")) >= 0){
                    // 此次需要定的冻品数量
                    BigDecimal totalQuantity = shortQuantity.divide(new BigDecimal("30"),0, BigDecimal.ROUND_HALF_UP).multiply(new BigDecimal("30"));

                    // 如果一共需要定的冻品数量 <= 缺的冻品数量,则把totalQuantity平均分配给当前的冻品
                    // 例如：缺了40个冻品 shortQuantity = 40 ，这次定的totalQuantity = 30
                    if(totalQuantity.compareTo(shortQuantity) <= 0){
                        for(ShopAutoOrderODTO orderODTO : groupQuickFreezeList){
                            // 单个商品补的数量
                            BigDecimal replenishmentQuantity = orderODTO.getSafeQuantity().subtract(orderODTO.getStockQuantity());
                            if(totalQuantity.compareTo(BigDecimal.ZERO) > 0){
                                if(totalQuantity.compareTo(replenishmentQuantity) > 0){
                                    orderODTO.setQuantity(replenishmentQuantity);
                                }else {
                                    orderODTO.setQuantity(totalQuantity);
                                }
                            }else {
                                orderODTO.setQuantity(BigDecimal.ZERO);
                            }
                            totalQuantity = totalQuantity.subtract(replenishmentQuantity);
                            resultList.add(orderODTO);
                        }

                    }else {
                        // 例如：缺了46个冻品 shortQuantity = 46 ，这次定的totalQuantity = 60
                        // 则先将缺的冻品补足安全库存，剩余多的冻品数量平均分配
                        BigDecimal originalReplenishmentQuantity = BigDecimal.ZERO; // 以前的缺货的冻品需要补的数量总和
                        // 首先将已有的冻品补足安全库存
                        for(ShopAutoOrderODTO orderODTO : groupQuickFreezeList){
                            originalReplenishmentQuantity = originalReplenishmentQuantity.add(orderODTO.getSafeQuantity().subtract(orderODTO.getStockQuantity()));
                            orderODTO.setQuantity(orderODTO.getSafeQuantity().subtract(orderODTO.getStockQuantity()));
                            resultList.add(orderODTO);
                        }

                        // 冻品处理补足缺货的冻品之后，剩余的数量，按照上个月的销量从高往低进行分配，最多分配销量top30的且当前在自动订货池里的冻品，
                        // 上个月有销量的商品不足5个时每个商品分配的额度自动增加.去t_md_commodity_tax查询
                        List<ShopAutoOrderODTO> replenishmentCommodityList = new ArrayList<>();
                        List<ShopAutoOrderODTO> topCommodityGroupList = top30GroupMap.get(orderTime);
                        if(CollectionUtils.isNotEmpty(topCommodityGroupList)){
                            // 注意：addAll 属于浅复制
                            replenishmentCommodityList = BeanCloneUtils.copyTo(topCommodityGroupList,ShopAutoOrderODTO.class);
                        }else {
                            // 如果上个月自动订货池里的冻品都没有销量（比如：通过盘点盘亏的方式让库存减少，从而达到自动订货的阈值），则将多余的额度平均分配给自动订货商品池里的冻品
                            // 当前门店的冻品商品list
                            replenishmentCommodityList = BeanCloneUtils.copyTo(groupQuickFreezeList,ShopAutoOrderODTO.class);
                        }

                        BigDecimal replenishmentQuantity = totalQuantity.subtract(originalReplenishmentQuantity); // 新的冻品要补的数量
                        // 平均分配补足冻品
                        BigDecimal avgQuantity = replenishmentQuantity.divide(new BigDecimal(replenishmentCommodityList.size()),0, BigDecimal.ROUND_DOWN);
                        BigDecimal splitSum = BigDecimal.ZERO;
                        for (int i = 0; i < replenishmentCommodityList.size(); i++) {
                            ShopAutoOrderODTO shopAutoOrderODTO = replenishmentCommodityList.get(i);
                            shopAutoOrderODTO.setQuantity(i + 1 == replenishmentCommodityList.size() ? replenishmentQuantity.subtract(splitSum) : avgQuantity);

                            splitSum = splitSum.add(shopAutoOrderODTO.getQuantity());
                            resultList.add(shopAutoOrderODTO);
                        }

                    }
                }
            }
            if(CollectionUtils.isNotEmpty(resultList)){
                // 过滤数量为0的商品
                resultList = resultList.stream().filter(p -> p.getQuantity() != null && p.getQuantity().compareTo(BigDecimal.ZERO) > 0).collect(Collectors.toList());
                // 商品有可能重复，进行合并
                resultList = resultList.stream()
                        .collect(Collectors.toMap(ShopAutoOrderODTO::getCommodityId, a -> a, (o1, o2)-> {
                            o1.setQuantity(o1.getQuantity().add(o2.getQuantity()));
                            return o1;
                        })).values().stream().collect(Collectors.toList());
            }
        }
        return resultList;
    }

    /**
     *获取上个月的销量从高往低，最多分配销量top30的且当前在自动订货池里的冻品
     * 按orderTime进行分组
     */
    private Map<String, List<ShopAutoOrderODTO>> getTop30GroupMap(Long shopId, List<Long> commodityIdList, Map<Long, ShopAutoOrderODTO> shopAutoOrderMap, List<Long> freezeGroupList) {
        Map<String, List<ShopAutoOrderODTO>> top30GroupMap = new HashMap<>();// 查询上月销售top30冻品
        commodityIdList.retainAll(freezeGroupList);
        if(CollectionUtils.isEmpty(commodityIdList)){
            return top30GroupMap;
        }
        ShopAutoCommodityTaxDTO oto = new ShopAutoCommodityTaxDTO(shopId, OrderTimeUtil.getLastMonthFirstDay(), OrderTimeUtil.getLastMonthLastDay(), commodityIdList);
        List<Long> topCommodityIdList = mdCommodityTaxClient.queryTopShopCommoditySale(oto);
        if(CollectionUtils.isNotEmpty(topCommodityIdList)){
            List<ShopAutoOrderODTO> top30GroupList = new ArrayList<>();
            for(Long commodityId :topCommodityIdList){
                ShopAutoOrderODTO topShopAutoOrder  = shopAutoOrderMap.get(commodityId);
                top30GroupList.add(topShopAutoOrder);
            }
            top30GroupMap =  top30GroupList.stream().collect(Collectors.groupingBy(ShopAutoOrderODTO::getOrderTime));
        }
        return top30GroupMap;
    }

    /**
     * 设置非冻品下单数量
     * @return
     */
    private List<ShopAutoOrderODTO> getNoQuickFreezeList(List<ShopAutoOrderODTO> noQuickFreezeList, Map<Long, ShopCommodityInfoODTO> xdStockMap) {
        List<ShopAutoOrderODTO> list = new ArrayList<>();
        // 非冻品处理
        for(ShopAutoOrderODTO orderODTO : noQuickFreezeList){
            ShopCommodityInfoODTO shopCommodityInfo = xdStockMap.get(orderODTO.getCommodityId());
            if(shopCommodityInfo != null){
                // 库存数小于安全库存
                if(shopCommodityInfo.getStockQuantity().compareTo(orderODTO.getSafeQuantity()) < 0){
                    // (安全库存-库存数) / 销售箱规 向上取整 *总部的销售箱规
                    BigDecimal quantity = orderODTO.getSafeQuantity().subtract(shopCommodityInfo.getStockQuantity())
                            .divide(orderODTO.getSalesBoxCapacity(),0, BigDecimal.ROUND_UP)
                            .multiply(orderODTO.getSalesBoxCapacity());
                    orderODTO.setQuantity(quantity);
                    list.add(orderODTO);
                }
            }
        }
        return list;
    }


    /**
     * 根据门店订货通用设置，赋值供应商、仓库等信息
     * @return
     */
    @NotNull
    private ShopAutoOrderODTO getShopAutoOrderODTO(Long shopId, Long storeId, MdShopOrderSettingEntry settingEntry, Map<Long, AutoShopCommodityODTO> autoShopCommodityMap, List<Long> freezeGroupList) {
        ShopAutoOrderODTO shopAutoOrderODTO = new ShopAutoOrderODTO();
        shopAutoOrderODTO.setShopId(shopId);
        shopAutoOrderODTO.setStoreId(storeId);
        shopAutoOrderODTO.setCommodityId(settingEntry.getCommodityId());
        shopAutoOrderODTO.setOrderTime(OrderTimeUtil.getBeginDeliveryTimeRange(settingEntry.getDeleveryTimeRange(), 0));
        shopAutoOrderODTO.setLogisticsModel(settingEntry.getLogisticsModel().intValue());
        shopAutoOrderODTO.setSupplierId(Long.valueOf(settingEntry.getSupplierId()));
        shopAutoOrderODTO.setWarehouseId(Long.valueOf(settingEntry.getWarehouseId()));
        shopAutoOrderODTO.setDeliveryBatch(DeliveryBatchTypeEnum.ONE_BATCH.getCode().toString());
        shopAutoOrderODTO.setDeleveryTimeRange(settingEntry.getDeleveryTimeRange());
        shopAutoOrderODTO.setEnterpriseId(78L);
        shopAutoOrderODTO.setUserId(-1L);
        shopAutoOrderODTO.setCreateName("系统");
        shopAutoOrderODTO.setDefaultSupplierEndTime(settingEntry.getDefaultSupplierEndTime());
        shopAutoOrderODTO.setDefaultWarehouseEndTime(settingEntry.getDefaultWarehouseEndTime());

        // 设置是否是冻品、安全库存、销售箱规
        AutoShopCommodityODTO autoShopCommodityODTO = autoShopCommodityMap.get(settingEntry.getCommodityId());
        shopAutoOrderODTO.setCommodityIsQuickFreeze(freezeGroupList.contains(settingEntry.getCommodityId()) ? YesOrNoEnums.YES.getCode() : YesOrNoEnums.NO.getCode());
        shopAutoOrderODTO.setSafeQuantity(autoShopCommodityODTO.getSafeQuantity());
        shopAutoOrderODTO.setSalesBoxCapacity(autoShopCommodityODTO.getSalesBoxCapacity());
        shopAutoOrderODTO.setCommodityPackageSpec(autoShopCommodityODTO.getCommodityPackageSpec());
        shopAutoOrderODTO.setPrice(autoShopCommodityODTO.getPrice());
        shopAutoOrderODTO.setCommodityCode(autoShopCommodityODTO.getCommodityCode());
        shopAutoOrderODTO.setCommodityName(autoShopCommodityODTO.getCommodityName());
        return shopAutoOrderODTO;
    }

    /**
     * 删除购物车
     * @param shoppingCartId
     */
    public void deleteAutoShoppingCart(Long  shoppingCartId){
        Example ex = new Example(ShoppingCartItem.class);
        ex.createCriteria().andEqualTo("shoppingCartId", shoppingCartId);

        //删除购物车及明细
        this.shoppingCartItemMapper.deleteByExample(ex);
        this.shoppingCartMapper.deleteByPrimaryKey(shoppingCartId);
    }

    /**
     * 门店加货单审核通过
     * @param autoPreOrderCode
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public AutoPreOrderAuditDTO autoPreOrderAuditPass(String autoPreOrderCode) throws Throwable {
        List<AutoPreOrderAuditItemDTO> autoPreOrderAuditItemDTOS = autoPreOrderItemMapper.getAutoPreOrderItemsByCode(autoPreOrderCode);
        QYAssert.isTrue(CollectionUtils.isNotEmpty(autoPreOrderAuditItemDTOS), "加货单不存在");

        // 过滤加货数量 > 0 的数据
        autoPreOrderAuditItemDTOS = autoPreOrderAuditItemDTOS.stream().filter(
                p -> p.getStockQuantity() != null && p.getStockQuantity().compareTo(BigDecimal.ZERO) > 0 )
                .collect(Collectors.toList());
        QYAssert.isTrue(CollectionUtils.isNotEmpty(autoPreOrderAuditItemDTOS), "加货单数量为0");

        Map<Long, AutoPreOrderAuditItemDTO> autoPreOrderMap = autoPreOrderAuditItemDTOS.stream().collect(Collectors.toMap(AutoPreOrderAuditItemDTO::getCommodityId, Function.identity()));
        List<String> commodityIdList = autoPreOrderAuditItemDTOS.stream().map(item -> item.getCommodityId() + "").collect(Collectors.toList());
        List<Long> commodityIds = autoPreOrderAuditItemDTOS.stream().map(item -> item.getCommodityId()).collect(Collectors.toList());

        AutoPreOrderAuditItemDTO autoPreOrderAuditItemDTO = autoPreOrderAuditItemDTOS.get(0);
        Long storeId = autoPreOrderAuditItemDTO.getStoreId();
        Long createId = autoPreOrderAuditItemDTO.getCreateId();
        Long consignmentId = autoPreOrderAuditItemDTO.getConsignmentId();

        // 校验订货时间内
        StoreDurationEntry sd = orderMapper.findStoreDurationByStoreId(storeId + "");
        if(null != sd){
            if (StringUtils.isNotBlank(sd.getBeginTime()) && StringUtils.isNotBlank(sd.getEndTime())){
                QYAssert.isTrue(DateTimeUtil.compareNewDate(sd.getBeginTime(), sd.getEndTime()), "不在订货时间内，无法审核!");
            }
        }

        // 库存不足的商品map
        Map<Long, BStockShortResponseVO> shortStockMap = new HashMap<>(commodityIds.size());
        // 库存不足的商品编码list
        List<String> shortSrockCommodityCodeList = new ArrayList<>();
        // 库存不足提示语，只有全部库存不足才提示
        StringBuffer shortStockSb = new StringBuffer();
        // 加货单审核通过判断库存
        Map<Long, BigDecimal> orderQuantityMap = new HashMap<>();
        autoPreOrderAuditItemDTOS.forEach(dto ->{
            orderQuantityMap.put(dto.getCommodityId(), dto.getStockQuantity());
        });

        // 获取门店订货通用设置
        List<MdShopOrderSettingEntry> settingList = mdShopOrderSettingService.queryMdShopOrderSettingListByIds(storeId, commodityIdList);
        if(CollectionUtils.isNotEmpty(settingList)){
            String nowTime = DateUtil.getDateFormate(new Date(), "HH:mm");
            MdShopOrderSettingEntry mdShopOrderSettingEntry = settingList.get(0);

            // 取第一条配置
            OrderRequestVo orderRequestVo = new OrderRequestVo();
            orderRequestVo.setStoreId(storeId + "");
            orderRequestVo.setOrderTime(OrderTimeUtil.getBeginDeliveryTimeRange(mdShopOrderSettingEntry.getDeleveryTimeRange(),0));
            orderRequestVo.setLogisticsModel(mdShopOrderSettingEntry.getLogisticsModel().intValue());
            orderRequestVo.setSupplierId(Long.valueOf(mdShopOrderSettingEntry.getSupplierId()));
            orderRequestVo.setWarehouseId(Long.valueOf(mdShopOrderSettingEntry.getWarehouseId()));
            orderRequestVo.setDeliveryBatch(DeliveryBatchTypeEnum.ONE_BATCH.getCode().toString());
            orderRequestVo.setDeleveryTimeRange(mdShopOrderSettingEntry.getDeleveryTimeRange());
            orderRequestVo.setEnterpriseId(78L);
            orderRequestVo.setUserId(createId);

            // 返回的是库存不够的
            List<BStockShortResponseVO> responseVOList = bStockService.checkBStock(storeId, OrderTypeEnum.SHOP_ADD_APPLY_ORDER.getCode(), DateUtil.parseDate(orderRequestVo.getOrderTime(), "yyyy-MM-dd"), orderQuantityMap, "加货单审核通过", null, createId);
            if(CollectionUtils.isNotEmpty(responseVOList)){
                Map<Long, Commodity> commMap = commodityService.findCommodityInfoByIdMap(commodityIds);
                responseVOList.forEach(item ->{
                    Commodity basicEntry = commMap.get(Long.valueOf(item.getCommodityId()));
                    item.setCommodityCode(basicEntry.getCommodityCode());
                    item.setCommodityName(basicEntry.getCommodityName());
                    shortStockMap.put(Long.valueOf(item.getCommodityId()), item);
                    //shortStockMap.put(Long.valueOf(item.getCommodityId()), basicEntry.getCommodityCode() + "库存余量不足,余量为" + item.getInventoryQuantity())
                });
            }

            List<OrderItemRequestVo> itemsList = new ArrayList<OrderItemRequestVo>();
            for(MdShopOrderSettingEntry settingEntry : settingList){
                Integer logisticsModel = settingEntry.getLogisticsModel().intValue();
                // 配送比较仓库时间
                if( logisticsModel == IogisticsModelEnums.DISPATCHING.getCode()){
                    if(DateTimeUtil.compareTime(settingEntry.getDefaultWarehouseEndTime(), nowTime)){
                        QYAssert.isTrue(false, "不在订货时间内，无法审核!");
                    }
                } else if(logisticsModel == IogisticsModelEnums.DIRECT_CONNECTION.getCode()){
                    //直通比较供应商时间
                    if(DateTimeUtil.compareTime(settingEntry.getDefaultSupplierEndTime(),nowTime)){
                        QYAssert.isTrue(false, "不在订货时间内，无法审核!");
                    }
                }

                AutoPreOrderAuditItemDTO itemDTO = autoPreOrderMap.get(settingEntry.getCommodityId());
                OrderItemRequestVo item = new OrderItemRequestVo();
                item.setProductId(settingEntry.getCommodityId() + "");
                item.setProductNum(itemDTO.getStockQuantity());

                // 库存不足
                if(shortStockMap.containsKey(settingEntry.getCommodityId())) {
                    BStockShortResponseVO shortStockVO = shortStockMap.get(settingEntry.getCommodityId());
                    shortSrockCommodityCodeList.add(shortStockVO.getCommodityCode());
                    shortStockSb.append(shortStockVO.getCommodityCode() + "库存余量不足,余量为" + shortStockVO.getInventoryQuantity() + "; ");
                }else {
                    itemsList.add(item);
                }
            }

            // 所有商品库存不足,报错提示
            if(CollectionUtils.isEmpty(itemsList)) {
                log.warn("加护审核通过，库存不足 {}", shortStockSb.toString());
                //QYAssert.isFalse(shortStockSb.toString());
                QYAssert.isFalse("订单中商品全部缺货");
            }

            orderRequestVo.setItemsList(itemsList);
            orderRequestVo.setConsignmentId(consignmentId); // 代销商户id
            orderRequestVo.setOrderRemark("门店加货申请单");

            ThreadLocalUtils.set(true);
            ThreadLocalUtils.setOrderType(OrderTypeEnum.SHOP_ADD_APPLY_ORDER.getCode());
            // 创建订单
            CreateOrderWrapperVo order = orderService.createOrder(orderRequestVo, null);

            ThreadLocalUtils.remove();

            return new AutoPreOrderAuditDTO(DateUtil.getDateFormate(order.getKafkaOrder().getOrderTime(),"yyyy-MM-dd"), order.getKafkaOrder().getOrderCode(), shortSrockCommodityCodeList);
        }else {
            QYAssert.isFalse("门店订货通用设置全部不存在，无法审核!");
        }
        return null;
    }
}
