package com.pinshang.qingyun.order.service.xda.v4;

import com.pinshang.qingyun.base.configure.expand.QYAssert;
import com.pinshang.qingyun.base.enums.YesOrNoEnums;
import com.pinshang.qingyun.box.utils.DateUtil;
import com.pinshang.qingyun.order.dto.shopcart.v3.ShoppingCartCommodityV3ODTO;
import com.pinshang.qingyun.order.dto.shopcart.v3.ShoppingCartV3ODTO;
import com.pinshang.qingyun.order.dto.shopcart.v4.ShoppingCartCommodityV4ODTO;
import com.pinshang.qingyun.order.dto.shopcart.v4.ShoppingCartV4ODTO;
import com.pinshang.qingyun.order.dto.xda.CreOrderItemDTO;
import com.pinshang.qingyun.order.mapper.OrderMapper;
import com.pinshang.qingyun.price.dto.storePromotion.StorePromotionCommodityPriceODTO;
import com.pinshang.qingyun.price.dto.storePromotion.StorePromotionCommodityPriceSearchIDTO;
import com.pinshang.qingyun.price.service.StorePromotionClient;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @Author: sk
 * @Date: 2024/4/15
 */
@Service
public class XdaSpecialPriceLimitService {

    @Autowired
    private StorePromotionClient storePromotionClient;
    @Autowired
    private OrderMapper orderMapper;

    /**
     * 鲜达限购特价V4版本(临时版本，后续会取消)
     * @param orderTime
     * @param storeId
     * @param shoppingCartV4ODTO
     */
    public void checkSpecialPriceLimit(Date orderTime, Long storeId, ShoppingCartV4ODTO shoppingCartV4ODTO){
        if(shoppingCartV4ODTO != null && shoppingCartV4ODTO.getNormalGroup() != null && CollectionUtils.isNotEmpty(shoppingCartV4ODTO.getNormalGroup().getCommodities())) {
            List<ShoppingCartCommodityV4ODTO> specialPriceList = shoppingCartV4ODTO.getNormalGroup().getCommodities().stream().filter(p -> YesOrNoEnums.YES.getCode().equals(p.getIsSpecialPrice())).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(specialPriceList)) {
                List<Long> commodityIdList = specialPriceList.stream().map(item -> item.getCommodityId()).collect(Collectors.toList());

                // 查询当前已经下的特价商品集合
                List<CreOrderItemDTO> orderedQuantityList = orderMapper.queryXdaSpecialPriceOrderedQuantity(DateUtil.getDateFormate(orderTime, "yyyy-MM-dd"), storeId, commodityIdList);
                Map<Long, BigDecimal> orderedQuantityMap = new HashMap<>();
                if (CollectionUtils.isNotEmpty(orderedQuantityList)) {
                    orderedQuantityMap = orderedQuantityList.stream().collect(Collectors.toMap(CreOrderItemDTO::getCommodityId, CreOrderItemDTO::getQuantity, (key1, key2) -> key2));

                }

                // 查询B端特价
                StorePromotionCommodityPriceSearchIDTO priceSearchIDTO = new StorePromotionCommodityPriceSearchIDTO();
                priceSearchIDTO.setStoreId(storeId);
                priceSearchIDTO.setOrderTime(orderTime);
                priceSearchIDTO.setCommodityIdList(commodityIdList);
                Map<Long, StorePromotionCommodityPriceODTO> commodityPriceMap = storePromotionClient.getStorePromotionCommodityMapByParams(priceSearchIDTO);

                for (ShoppingCartCommodityV4ODTO v4ODTO : specialPriceList) {
                    StorePromotionCommodityPriceODTO storePromotionCommodityPriceODTO = commodityPriceMap.get(v4ODTO.getCommodityId());
                    // 特价不为空
                    if (storePromotionCommodityPriceODTO != null) {
                        Integer priceLimitNum = storePromotionCommodityPriceODTO.getLimitNumber();
                        BigDecimal orderedQuantity = orderedQuantityMap.get(v4ODTO.getCommodityId()) != null ? orderedQuantityMap.get(v4ODTO.getCommodityId()) : BigDecimal.ZERO;
                        Integer orderedNum = orderedQuantity.setScale(0, BigDecimal.ROUND_UP).intValue();
                        Integer addNum = v4ODTO.getQuantity().add(orderedQuantity).setScale(0, BigDecimal.ROUND_UP).intValue();
                        if (addNum > priceLimitNum) {
                            QYAssert.isFalse(v4ODTO.getCommodityName() + "特价限购，余量为" + Math.max((priceLimitNum - orderedNum), 0));
                        }
                    }
                }
            }
        }
    }

    /**
     * 鲜达限购特价V3版本(临时版本，后续会取消)
     * @param orderTime
     * @param storeId
     * @param shoppingCartV3ODTO
     */
    public void checkSpecialPriceLimit3(Date orderTime, Long storeId, ShoppingCartV3ODTO shoppingCartV3ODTO){
        if(shoppingCartV3ODTO != null && shoppingCartV3ODTO.getNormalGroup() != null && CollectionUtils.isNotEmpty(shoppingCartV3ODTO.getNormalGroup().getCommodities())) {
            List<ShoppingCartCommodityV3ODTO> specialPriceList = shoppingCartV3ODTO.getNormalGroup().getCommodities().stream().filter(p -> YesOrNoEnums.YES.getCode().equals(p.getIsSpecialPrice())).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(specialPriceList)) {
                List<Long> commodityIdList = specialPriceList.stream().map(item -> item.getCommodityId()).collect(Collectors.toList());

                // 查询当前已经下的特价商品集合
                List<CreOrderItemDTO> orderedQuantityList = orderMapper.queryXdaSpecialPriceOrderedQuantity(DateUtil.getDateFormate(orderTime, "yyyy-MM-dd"), storeId, commodityIdList);
                Map<Long, BigDecimal> orderedQuantityMap = new HashMap<>();
                if (CollectionUtils.isNotEmpty(orderedQuantityList)) {
                    orderedQuantityMap = orderedQuantityList.stream().collect(Collectors.toMap(CreOrderItemDTO::getCommodityId, CreOrderItemDTO::getQuantity, (key1, key2) -> key2));

                }

                // 查询B端特价
                StorePromotionCommodityPriceSearchIDTO priceSearchIDTO = new StorePromotionCommodityPriceSearchIDTO();
                priceSearchIDTO.setStoreId(storeId);
                priceSearchIDTO.setOrderTime(orderTime);
                priceSearchIDTO.setCommodityIdList(commodityIdList);
                Map<Long, StorePromotionCommodityPriceODTO> commodityPriceMap = storePromotionClient.getStorePromotionCommodityMapByParams(priceSearchIDTO);

                for (ShoppingCartCommodityV3ODTO v3ODTO : specialPriceList) {
                    StorePromotionCommodityPriceODTO storePromotionCommodityPriceODTO = commodityPriceMap.get(v3ODTO.getCommodityId());
                    // 特价不为空
                    if (storePromotionCommodityPriceODTO != null) {
                        Integer priceLimitNum = storePromotionCommodityPriceODTO.getLimitNumber();
                        BigDecimal orderedQuantity = orderedQuantityMap.get(v3ODTO.getCommodityId()) != null ? orderedQuantityMap.get(v3ODTO.getCommodityId()) : BigDecimal.ZERO;
                        Integer orderedNum = orderedQuantity.setScale(0, BigDecimal.ROUND_UP).intValue();
                        Integer addNum = v3ODTO.getQuantity().add(orderedQuantity).setScale(0, BigDecimal.ROUND_UP).intValue();
                        if (addNum > priceLimitNum) {
                            QYAssert.isFalse(v3ODTO.getCommodityName() + "特价限购，余量为" + Math.max((priceLimitNum - orderedNum), 0));
                        }
                    }
                }
            }
        }
    }
}
