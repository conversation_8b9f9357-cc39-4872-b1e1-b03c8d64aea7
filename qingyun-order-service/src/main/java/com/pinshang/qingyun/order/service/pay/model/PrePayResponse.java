package com.pinshang.qingyun.order.service.pay.model;

import com.pinshang.qingyun.base.configure.expand.QYAssert;
import com.pinshang.qingyun.base.enums.xd.XdPayTypeEnum;
import com.pinshang.qingyun.box.utils.JsonUtil;
import lombok.Data;

import java.util.Map;

/**
 * 预支付响应类
 * 对接的三方支付很少没做泛化处理
 * <AUTHOR>
 * @Date 2019/11/26 16:46
 */
@Data
public class PrePayResponse {
    /**
     * 支付宝预支付订单
     */
    private String alipayPrePayBill;
    /**
     * 微信预支付订单
     */
    private Map<String, String> wechatPrePayBill;
    /**
     * 小程序预支付单
     */
    private Map<String, String> miniPrePayBill;
    /**
     * 云闪付预支付单
     */
    private  Map<String, String> unionPrePayBill;
    /**
     * 若不为空,则代表了执行生成待支付单的代码发生了异常
     */
    private RuntimeException exception;

    private String originalAppId;

    public String getPrePayJson(XdPayTypeEnum payType){
        QYAssert.isTrue(payType != null ,"获取待支付单json支付渠道参数异常!");
        switch (payType){
            case MINI:
            case WECHAT:
            case ALIPAY:
            case UNION_PAY:
                return JsonUtil.java2json(unionPrePayBill);
            default:
                return null;
        }
    }
}
