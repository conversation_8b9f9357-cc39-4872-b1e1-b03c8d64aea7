package com.pinshang.qingyun.order.service.pay.prepaidacct;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.pinshang.qingyun.order.enums.PrePaidAccountReqRefBillTypeEnum;
import com.pinshang.qingyun.order.enums.PrePaidAccountReqTypeEnum;
import com.pinshang.qingyun.order.mapper.OrderBillMapper;
import com.pinshang.qingyun.order.model.order.OrderBill;
import com.pinshang.qingyun.order.vo.prepayacct.PrePaidAccountReqVo;

@Service
public class OrderBillBatchInsert implements PrePaidAccountBatchAsyncRecordListener {

    private OrderBillMapper orderBillMapper;

	@Autowired
	public void setOrderBillMapper(OrderBillMapper orderBillMapper) {
		this.orderBillMapper = orderBillMapper;
	}

	@Override
	public void onBatchRecordSuccess(Long storeId, List<PrePaidAccountReqVo> reqs) {
		// 如果批量入账操作的关联单据是订单类，那么需要写入OrderBill
		List<OrderBill> obs = new ArrayList<>();
		for (PrePaidAccountReqVo req : reqs) {
			if (req.getRefBillType() == PrePaidAccountReqRefBillTypeEnum.ORDER) {
				OrderBill ob = new OrderBill();
                ob.setStoreId(storeId);
                ob.setOrderId(req.getRefBillId());
                ob.setOrderTime(req.getRefBillTime());
                if (req.getReqType() == PrePaidAccountReqTypeEnum.PAY) {
	                ob.setArAmount(req.getAmount());
	                ob.setPaAmount(BigDecimal.ZERO);
                } else {
                	ob.setArAmount(BigDecimal.ZERO);
	                ob.setPaAmount(req.getAmount());
                }
                ob.setBillRemark(req.getRemarks());
                ob.setStoreBalance(req.getBalance());
                ob.setCreateTime(new Date());
                ob.setCrateId(storeId);
                obs.add(ob);
			}
		}
		
		for (OrderBill ob : obs) {
			orderBillMapper.insertSelective(ob);
		}
	}

	@Override
	public void onBatchRecordFailed(Long storeId, List<PrePaidAccountReqVo> reqs) {
		// TODO Auto-generated method stub

	}


}
