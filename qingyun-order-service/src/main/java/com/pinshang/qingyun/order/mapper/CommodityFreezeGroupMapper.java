package com.pinshang.qingyun.order.mapper;

import com.pinshang.qingyun.base.mybatis.MyMapper;
import com.pinshang.qingyun.order.dto.CommodityFreezeGroupEntryLog;
import com.pinshang.qingyun.order.dto.CommodityFreezeGroupIDTO;
import com.pinshang.qingyun.order.dto.CommodityFreezeGroupODTO;
import com.pinshang.qingyun.order.mapper.entry.CommodityFreezeGroupEntry;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * @ClassName CommodityFreezeGroupMapper
 * <AUTHOR>
 * @Date 2022/9/29 10:27
 * @Description CommodityFreezeGroupMapper
 * @Version 1.0
 */
@Repository
public interface CommodityFreezeGroupMapper extends MyMapper<CommodityFreezeGroupEntry> {
    List<CommodityFreezeGroupODTO> page(CommodityFreezeGroupIDTO idto);

    List<Long> selectOldCommodity(@Param("list") List<Long> commodityIdList);

    List<CommodityFreezeGroupEntryLog> selectInfoForLog(@Param("list") List<Long> commodityIdList);
}
