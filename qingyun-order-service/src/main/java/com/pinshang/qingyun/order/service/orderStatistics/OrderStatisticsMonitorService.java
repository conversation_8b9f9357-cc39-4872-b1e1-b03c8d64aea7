package com.pinshang.qingyun.order.service.orderStatistics;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.pinshang.qingyun.base.configure.expand.QYAssert;
import com.pinshang.qingyun.base.constant.QingyunConstant;
import com.pinshang.qingyun.box.utils.SpringUtil;
import com.pinshang.qingyun.order.mapper.OrderListGiftMapper;
import com.pinshang.qingyun.order.mapper.entry.orderStatistics.OrderCompanyAndStoreTypeEntry;
import com.pinshang.qingyun.order.mapper.entry.orderStatistics.OrderMirrorSyncEntry;
import com.pinshang.qingyun.order.mapper.entry.orderStatistics.OrderStatisticsEntry;
import com.pinshang.qingyun.order.mapper.entry.orderStatistics.OrderSyncEntry;
import com.pinshang.qingyun.order.mapper.orderStatistics.OrderStatisticsMonitorMapper;
import com.pinshang.qingyun.order.model.order.OrderListGift;
import com.pinshang.qingyun.order.vo.orderStatistics.OrderStatisticsMonitorVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RequestBody;
import tk.mybatis.mapper.entity.Example;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static java.util.stream.Collectors.groupingBy;

/**
 * 订单统计查询--订单监控
 */
@Slf4j
@Service
public class OrderStatisticsMonitorService {
    @Autowired
    private OrderStatisticsMonitorMapper orderStatisticsMonitorMapper;
    @Autowired
    private OrderListGiftMapper orderListGiftMapper;

    /**
     * 查询订单统计信息
     * @param vo
     * @return
     */
    public OrderStatisticsEntry queryOrderStatisticsInfo(OrderStatisticsMonitorVo vo){
        OrderStatisticsEntry entry = orderStatisticsMonitorMapper.queryOrderStatisticsInfo(vo.getOrderCodeList(),vo);
        return entry;
    }

    /**
     * 查询订单号
     * @param vo
     * @return
     */
    public List<String> queryOrderDiffList(OrderStatisticsMonitorVo vo) {
        return orderStatisticsMonitorMapper.queryOrderDiffList(vo.getOrderCodeList(),vo);
    }

    /**
     * 订单同步查询订单信息
     * 1.先分批每次200条查出订单主表信息
     * 2.再对订单主表数据分批查询订单明细，每次查50个订单ID对应的订单明细
     * xj2.6.8 订单添加公司 OrderSyncEntry 中添加 companyId (公司id)
     * @param vo
     * @return
     */
    public PageInfo<OrderSyncEntry> queryOrderSyncList(@RequestBody OrderStatisticsMonitorVo vo) {
        PageHelper.startPage(vo.getPageNo(), vo.getPageSize());
        List<OrderSyncEntry> orderList = orderStatisticsMonitorMapper.queryOrderList(vo.getOrderCodeList(),vo);
        QYAssert.isTrue(SpringUtil.isNotEmpty(orderList),"没有查询到订单数据！");
        if(orderList.size()> QingyunConstant.SUB_LIST_SIZE){
            int loop = (orderList.size()/QingyunConstant.SUB_LIST_SIZE)+(orderList.size()%QingyunConstant.SUB_LIST_SIZE==0?0:1);
            for(int i =0; i<loop;i++){
                int fromIndex = i*QingyunConstant.SUB_LIST_SIZE;
                int toIndex   = i*QingyunConstant.SUB_LIST_SIZE+QingyunConstant.SUB_LIST_SIZE;
                if(i==loop-1){
                    toIndex = orderList.size() ;
                }
                this.batchGetOrderListGift(orderList.subList(fromIndex,toIndex));
            }
        }else{
            this.batchGetOrderListGift(orderList);
        }
        orderList.removeIf(order->SpringUtil.isEmpty(order.getItemList()));
        return new PageInfo<OrderSyncEntry>(orderList);
    }

    /**
     * 分批次查询订单明细
     * @param orderList
     */
    private void batchGetOrderListGift(List<OrderSyncEntry> orderList){
        List<Long> orderIds = orderList.stream().map(OrderSyncEntry::getId).collect(Collectors.toList());
        Example example = new Example(OrderListGift.class);
        example.createCriteria().andIn("orderId",orderIds);
        List<OrderListGift> giftList = orderListGiftMapper.selectByExample(example);
        Map<Long,List<OrderListGift>> orderItemMap = giftList.stream().collect(groupingBy(OrderListGift::getOrderId));
        orderList.forEach(order->{
            if(orderItemMap!=null && orderItemMap.get(order.getId())!=null){
                order.setOrderRemark(null);//订单备注不需要同步到统计系统
                order.setItemList(orderItemMap.get(order.getId()));
            }
        });
    }

    /**
     * 查询订单落地数据补偿需要的订单及客户信息
     * @param vo
     * @return
     */
    public PageInfo<OrderMirrorSyncEntry> queryOrderMirrorSyncList(OrderStatisticsMonitorVo vo) {
        return PageHelper.startPage(vo.getPageNo(), vo.getPageSize()).doSelectPageInfo(() -> orderStatisticsMonitorMapper.queryOrderMirrorSyncList(vo.getOrderCodeList(),vo));
    }
    public List<OrderCompanyAndStoreTypeEntry> orderByCompanyAndStoreTypeReport(String startOrderTime, String endOrderTime) {
        return orderStatisticsMonitorMapper.orderByCompanyAndStoreTypeReport(startOrderTime,endOrderTime);
    }

}
