package com.pinshang.qingyun.order.mapper;

import com.pinshang.qingyun.base.mybatis.MyMapper;
import com.pinshang.qingyun.order.dto.XdReceiveDocCommodityODTO;
import com.pinshang.qingyun.order.dto.XdReceiveDocIDTO;
import com.pinshang.qingyun.order.dto.XdReceiveOrderCommodityODTO;
import com.pinshang.qingyun.order.model.xd.XdReceiveDoc;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.util.List;

@Repository
public interface XdReceiveDocMapper extends MyMapper<XdReceiveDoc> {

    String getMaxDocNo(@Param("orderTime") String orderTime);

    void batchUpdateReceiveOrderPass(@Param("todayStr") String todayStr,@Param("shopIdList") List<Long> shopIdList);

    List<XdReceiveDocCommodityODTO> getXdReceiveDocCommodityInfo(@Param("vo") XdReceiveDocIDTO xdReceiveDocIDTO);

    XdReceiveDocCommodityODTO getXdReceiveCommodityByBarcode(@Param("shopId") Long shopId, @Param("deliveryTime") String deliveryTime, @Param("barcode") String barcode,@Param("allComm") Integer allComm);

    BigDecimal getUnreceiveQuantityByParam(@Param("shopId") Long shopId, @Param("deliveryTime") String deliveryTime, @Param("commodityId") Long commodityId);

    List<XdReceiveOrderCommodityODTO> getOrderCommodityInfo(@Param("shopId") Long shopId, @Param("deliveryTime") String deliveryTime, @Param("commodityId") Long commodityId);

    Integer updateRealReceiveQuantity(@Param("unReceiveQuantityitem") BigDecimal unReceiveQuantityitem, @Param("subOrderId") Long subOrderId, @Param("commodityId") Long commodityId);
}
