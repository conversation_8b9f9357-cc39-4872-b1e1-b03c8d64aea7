package com.pinshang.qingyun.order.service.pf;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.pinshang.qingyun.box.utils.SpringUtil;
import com.pinshang.qingyun.order.dto.pf.PfCreOrderItemDTO;
import com.pinshang.qingyun.order.mapper.PfFrequentPurchaseMapper;
import com.pinshang.qingyun.order.model.order.PfFrequentPurchase;


@Service
public class PfFrequentPurchaseService {
	
    @Autowired
    private PfFrequentPurchaseMapper pfFrequentPurchaseMapper;

    /**
     * 保存线上订单销量日统计
     * @param storeId
     * @param orderCommodityList
     */
    public void saveSaleDayStatistics(Long storeId, List<PfCreOrderItemDTO> orderCommodityList){
        // 查询客户常购清单
        Set<Long> commodityIds = orderCommodityList.stream().map(PfCreOrderItemDTO :: getCommodityId).collect(Collectors.toSet());
        List<PfFrequentPurchase> frequentPurchaseIdList = pfFrequentPurchaseMapper.queryFrequentPurchase(storeId, commodityIds);
        Map<Long, PfFrequentPurchase> frequentPurchaseMap = frequentPurchaseIdList.stream().collect(Collectors.toMap(PfFrequentPurchase::getCommodityId, Function.identity()));

        List<PfFrequentPurchase> addList = new ArrayList<>(frequentPurchaseIdList.size());
        List<PfFrequentPurchase> updateList = new ArrayList<>(frequentPurchaseIdList.size());

        orderCommodityList.forEach(p->{
        	PfFrequentPurchase frequentPurchase = new PfFrequentPurchase();
            frequentPurchase.setStoreId(storeId);
            frequentPurchase.setCommodityId(p.getCommodityId());
            PfFrequentPurchase fp =  frequentPurchaseMap.get(p.getCommodityId());
            frequentPurchase.setUpdateTime(new Date());
            if(fp != null){
                frequentPurchase.setQuantity(fp.getQuantity().add(p.saleQuantity()));
                updateList.add(frequentPurchase);
            }else {
                frequentPurchase.setQuantity(p.saleQuantity());
                addList.add(frequentPurchase);
            }
        });

        if(SpringUtil.isNotEmpty(addList)){
            pfFrequentPurchaseMapper.insertList(addList);
        }
        if(SpringUtil.isNotEmpty(updateList)){
            pfFrequentPurchaseMapper.batchUpdate(updateList);
        }

    }
}
