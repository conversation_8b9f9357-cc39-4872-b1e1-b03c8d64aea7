package com.pinshang.qingyun.order.service.xda;

import com.pinshang.qingyun.box.utils.ListUtil;
import com.pinshang.qingyun.box.utils.SpringUtil;
import com.pinshang.qingyun.order.mapper.CommoditySaleDayStatisticsMapper;
import com.pinshang.qingyun.order.mapper.CommoditySaleMultiDayStatisticsMapper;
import com.pinshang.qingyun.order.model.order.XdaCommoditySaleDayStatistics;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * Created by weican on 2017-08-02.
 */
@Service
@Slf4j
public class CommoditySaleMultiDayStatisticsService {
    @Autowired
    private CommoditySaleMultiDayStatisticsMapper commoditySaleMultiDayStatisticsMapper;

    @Autowired
    private CommoditySaleDayStatisticsMapper dayStatisticsMapper;

    /**
     * 更新销售统计数据，先删除数据，再重新统计
     */
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateSaleStatistics(){

        commoditySaleMultiDayStatisticsMapper.truncateSaleStatistics();
        //2.查询门店商品销量情况
        List<XdaCommoditySaleDayStatistics> entryList = dayStatisticsMapper.queryDayStatisticsList();
        if (SpringUtil.isNotEmpty(entryList)){
            //3.分批次insert
            List<List<XdaCommoditySaleDayStatistics>> subLists = ListUtil.splitSubList(entryList,200);
            subLists.forEach(subList->{
                commoditySaleMultiDayStatisticsMapper.insertSaleStatistics(subList);
            });
        }
        return true;
    }
}
