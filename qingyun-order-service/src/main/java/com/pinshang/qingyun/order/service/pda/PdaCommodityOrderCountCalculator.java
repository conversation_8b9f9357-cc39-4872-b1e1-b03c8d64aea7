package com.pinshang.qingyun.order.service.pda;

import com.pinshang.qingyun.order.dto.pda.PdaOrderCountODTO;
import com.pinshang.qingyun.order.mapper.entry.pda.PdaOrderQueryEntry;
import com.pinshang.qingyun.order.model.commodity.Commodity;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;
import java.util.Objects;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class PdaCommodityOrderCountCalculator {

    private List<PdaOrderQueryEntry> pdaOrderList;


    public PdaOrderCountODTO calculatePdaCommodityOrderCount(){

        BigDecimal orderCount = pdaOrderList.stream().map(PdaOrderQueryEntry::getCommodityNum).reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal notShipCount = pdaOrderList.stream()
                .filter(pdaOrderQueryEntry -> Objects.isNull(pdaOrderQueryEntry.getRealQuantity()))
                .map(PdaOrderQueryEntry::getCommodityNum).reduce(BigDecimal.ZERO, BigDecimal::add);

        BigDecimal shipNotShotCount = pdaOrderList.stream()
                .map(PdaOrderQueryEntry::getRealQuantity)
                .filter(Objects::nonNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        BigDecimal shipShotCount = orderCount.subtract(notShipCount).subtract(shipNotShotCount);

        return new PdaOrderCountODTO(notShipCount, shipShotCount, shipNotShotCount, orderCount);
    }

}
