package com.pinshang.qingyun.order.mapper;

import com.pinshang.qingyun.infrastructure.data.query.annotate.DataQuery;
import com.pinshang.qingyun.order.dto.report.*;
import com.pinshang.qingyun.order.mapper.entry.shop.ReturnOrderDetailsReportEntry;
import com.pinshang.qingyun.order.vo.shop.ReturnOrderDetailsReportVo;
import org.apache.ibatis.annotations.Mapper;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.util.List;

@Mapper
@Repository
public interface OrderReportMapper {

    List<ReturnOrderDetailsReportEntry> findReturnOrderDetails(ReturnOrderDetailsReportVo returnOrderDetailsReportVo);
    @DataQuery(value = "shortDeliveryCode")
    List<ShortDeliveryReportODto> shortDeliveryTodayReport(ShortDeliveryReportIDto vo);
    @DataQuery(value = "queryCode")
    List<RealDeliveryReportODto> realDeliveryReportCurrentDay(RealDeliveryReportDataQueryIDto vo);
    @DataQuery(value = "sumCode")
    BigDecimal realTotalDeliveryReportCurrentDay(RealDeliveryReportDataQueryIDto vo);

    @DataQuery(value = "comRealDeliveryCode")
    List<RealDeliveryReportODto> realDeliveryStoreTypeReportCurrentDay(RealDeliveryReportIDto vo);
    @DataQuery(value = "comRealDeliverySumCode")
    BigDecimal realTotalDeliveryStoreTypeReportCurrentDay(RealDeliveryReportIDto vo);

    @DataQuery(value = "shopOrderSummaryCode")
    List<ShopOrderGoodReportODto> shopOrderGoodReport(ShopOrderGoodReportIDto idto);
    @DataQuery(value = "shopRealReceiveCode")
    List<ActualReceiptAnalysisODto> actualReceiptAnalysisReport(ActualReceiptAnalysisIDto vo);
}
