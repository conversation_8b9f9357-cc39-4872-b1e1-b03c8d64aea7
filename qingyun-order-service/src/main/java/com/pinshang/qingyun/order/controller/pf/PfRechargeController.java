package com.pinshang.qingyun.order.controller.pf;

import com.pinshang.qingyun.base.api.PfTokenInfo;
import com.pinshang.qingyun.base.configure.expand.QYAssert;
import com.pinshang.qingyun.base.util.FastThreadLocalUtil;
import com.pinshang.qingyun.order.dto.PfPrePayBillODTO;
import com.pinshang.qingyun.order.mapper.entry.payType.PfPayTypeEntry;
import com.pinshang.qingyun.order.service.pf.recharge.PfRechargeService;
import com.pinshang.qingyun.order.vo.recharge.GoPayNoCheckIDTO;
import com.pinshang.qingyun.order.vo.recharge.PfRechargeVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * 批发充值
 * <AUTHOR>
 *
 */
@RestController
@RequestMapping("/pf/recharge")
@Api(value = "批发充值", tags = "pfPayType")
public class PfRechargeController {
	
	@Autowired
    private PfRechargeService rechargeService;
	/**
     * 查询有效的支付方式及促销语
     * @return
     */
    @ApiOperation(value = "查询有效的支付方式及促销语", notes = "查询有效的支付方式及促销语")
    @GetMapping("/queryValidPayMethodAndTip")
    public PfPayTypeEntry queryValidPayMethodAndTip(){
        return rechargeService.queryValidPayMethodAndTip();
    }
    
    @ApiOperation(value = "批发充值", notes = "批发充值")
    @ApiImplicitParam(name = "vo", value = "充值入参", required = true, paramType = "body", dataType = "PfRechargeVo")
    @RequestMapping(value = "/pfAppRecharge", method = RequestMethod.POST)
    public PfPrePayBillODTO pfAppRecharge(@RequestBody PfRechargeVo vo) {
        QYAssert.isTrue(vo != null,"批发入参不能为空！");
        QYAssert.isTrue(vo.getPayType() != null,"批发支付方式不能为空！");
        QYAssert.isTrue(vo.getPayAmount() != null,"批发充值金额不能为空！");
        PfTokenInfo tokenInfo = FastThreadLocalUtil.getPF();
        vo.setStoreId(tokenInfo.getStoreId());
        vo.setUserId(tokenInfo.getUserId());
        return rechargeService.pfAppRecharge(vo);
    }
    
    @ApiOperation(value = "批发补偿job", notes = "批发补偿job")
    @RequestMapping(value = "/pfAppRechargeJob", method = RequestMethod.GET)
    public void pfAppRechargeJob() {
        rechargeService.pfAppRechargeJob();
    }

    @ApiOperation(value = "查询支付状态", notes = "查询支付状态")
    @RequestMapping(value = "/pay/status", method = RequestMethod.GET)
    public boolean pfPayStatus(@RequestParam("orderCode") String orderCode) {
        QYAssert.isTrue(StringUtils.isNotBlank(orderCode),"支付单号不能为空！");
        return rechargeService.pfPayStatus(orderCode);
    }

    @ApiOperation( "小程序去支付")
    @PostMapping(  "/goPayNoCheck")
    public PfPrePayBillODTO goPayNoCheck(@RequestBody  GoPayNoCheckIDTO goPayNoCheckIDTO){
        return rechargeService.goPayNoCheck(goPayNoCheckIDTO);
    }
}
