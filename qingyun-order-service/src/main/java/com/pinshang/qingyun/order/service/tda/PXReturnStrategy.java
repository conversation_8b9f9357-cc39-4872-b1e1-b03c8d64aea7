/*
 * tramy.cn Inc.
 * Copyright (c) 2021-2024 All Rights Reserved.
 */
package com.pinshang.qingyun.order.service.tda;

import com.pinshang.qingyun.base.configure.expand.QYAssert;
import com.pinshang.qingyun.base.enums.tms.TmsBusinessTypeEnums;
import com.pinshang.qingyun.box.utils.DateUtil;
import com.pinshang.qingyun.order.dto.xda.tda.SaveReturnOrderItemDTO;
import com.pinshang.qingyun.order.dto.xda.tda.SaveReturnOrderODTO;
import com.pinshang.qingyun.order.enums.ComplaintTypeEnum;
import com.pinshang.qingyun.order.enums.tda.TDaReturnOrderTypeEnum;
import com.pinshang.qingyun.order.enums.tda.TDaReturnSourceEnum;
import com.pinshang.qingyun.order.enums.tda.TDaReturnTypeEnum;
import com.pinshang.qingyun.order.mapper.XdaReturnOrderItemMapper;
import com.pinshang.qingyun.order.mapper.XdaReturnOrderMapper;
import com.pinshang.qingyun.order.model.commodity.Commodity;
import com.pinshang.qingyun.order.model.store.Store;
import com.pinshang.qingyun.order.model.xda.XdaReturnOrder;
import com.pinshang.qingyun.order.model.xda.XdaReturnOrderItem;
import com.pinshang.qingyun.order.service.CommodityService;
import com.pinshang.qingyun.order.service.StoreService;
import com.pinshang.qingyun.order.service.tda.factory.ReturnOrderTemplate;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import static com.pinshang.qingyun.order.service.xda.XdaComplaintOrderService.getUUID;

/**
 * <p>
 * 品鲜后管申请
 * </p>
 *
 * <AUTHOR> shenyang
 * @version : 1.0.0
 * @history : modify history
 * <author>              <time>              <version>              <desc>
 * @since : 2024/05/17 23:09
 */
@Component
public class PXReturnStrategy extends ReturnOrderTemplate implements ReturnOrderStrategy {
    @Autowired
    private StoreService storeService;
    @Autowired
    private CommodityService commodityService;
    @Autowired
    private ApplicationContext applicationContext;
    @Autowired
    private XdaReturnOrderMapper xdaReturnOrderMapper;
    @Autowired
    private XdaReturnOrderItemMapper xdaReturnOrderItemMapper;

    @Override
    public Boolean execute(SaveReturnOrderODTO saveReturnOrderDTO) {
        PXReturnStrategy contextBean = applicationContext.getBean(PXReturnStrategy.class);
        return contextBean.saveReturnOrder(saveReturnOrderDTO);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean saveReturnOrder(SaveReturnOrderODTO saveReturnOrderDTO) {
        Long storeId = saveReturnOrderDTO.getStoreId();
        Store store = storeService.findStoreByStoreId(storeId);
        QYAssert.isTrue(!(storeId == null || store == null), "客户不存在！");

        if (!Objects.equals(saveReturnOrderDTO.getBusinessType(), TmsBusinessTypeEnums.TD_SALE.getCode())) {
            //不是通达销售并且是后管发起的，返回null，走原投诉逻辑
            return null;
        }

        //创建退货单
        XdaReturnOrder xdaReturnOrder = new XdaReturnOrder();
        xdaReturnOrder.setStoreId(saveReturnOrderDTO.getStoreId());
        Integer returnOrderType = (ComplaintTypeEnum.COMMON.getCode() == saveReturnOrderDTO.getComplaintType()) ?
                TDaReturnOrderTypeEnum.SHORTAGE.getCategoryCode() :
                TDaReturnOrderTypeEnum.RETURN.getCategoryCode();
        xdaReturnOrder.setReturnOrderType(returnOrderType);
        xdaReturnOrder.setReturnOrderSeq(saveReturnOrderDTO.getReturnOrderSeq());
        xdaReturnOrder.setReturnOrderCode(getUUID());
        xdaReturnOrder.setBusinessType(saveReturnOrderDTO.getBusinessType());
        xdaReturnOrder.setReturnType(TDaReturnTypeEnum.STORE_RETURN.getCode());
        xdaReturnOrder.setReturnSource(TDaReturnSourceEnum.PX_RETURN.getCode());
        xdaReturnOrder.setDriverId(saveReturnOrderDTO.getDriverId());
        xdaReturnOrder.setDriverName(saveReturnOrderDTO.getDriverName());
        xdaReturnOrder.setSourceOrderId(saveReturnOrderDTO.getOrderId());
        xdaReturnOrder.setSourceOrderCode(saveReturnOrderDTO.getOrderCode());
        xdaReturnOrder.setLogisticsCenterId(saveReturnOrderDTO.getLogisticsCenterId());
        xdaReturnOrder.setDeliveryBatch(saveReturnOrderDTO.getDeliveryBatch());
        xdaReturnOrder.setWaybillCode(saveReturnOrderDTO.getWaybillCode());
        xdaReturnOrder.setPickUpTimeRange(saveReturnOrderDTO.getPickUpTimeRange());
        xdaReturnOrder.setDeliveryTime(DateUtil.parseDate(saveReturnOrderDTO.getDeliveryDate(), "yyyy-MM-dd"));
        xdaReturnOrder.setPhone(store.getLinkmanMobile());
        xdaReturnOrder.setDeliveryAddress(store.getDeliveryAddress());
        xdaReturnOrder.setTotalApplyMoney(saveReturnOrderDTO.getComplaintTotalMoney());
        xdaReturnOrder.setBusinessType(TmsBusinessTypeEnums.TD_SALE.getCode());
        xdaReturnOrder.setRemark(saveReturnOrderDTO.getComplaintReason());

        //插入退货单
        int flag = xdaReturnOrderMapper.insertSelective(xdaReturnOrder);

        //插入退货明细
        saveXdaReturnOrderItem(saveReturnOrderDTO, xdaReturnOrder.getId());
        return flag > 0;
    }

    private void saveXdaReturnOrderItem(SaveReturnOrderODTO saveReturnOrderDTO, Long id) {
        //创建退货明细
        List<Long> commodityIdList = saveReturnOrderDTO.getComplaintItemList().stream().map(SaveReturnOrderItemDTO::getCommodityId).distinct().collect(Collectors.toList());

        //查询商品信息（包装规格）
        Map<Long, Commodity> commodityInfoByIdMap = commodityService.findCommodityInfoByIdMap(commodityIdList);

        List<XdaReturnOrderItem> xdaReturnOrderItems = saveReturnOrderDTO.getComplaintItemList()
                .stream()
                .filter(x -> Objects.nonNull(commodityInfoByIdMap.get(x.getCommodityId())))
                .map(dto -> {
                    Commodity commodity = commodityInfoByIdMap.get(dto.getCommodityId());
                    XdaReturnOrderItem xdaReturnOrderItem = new XdaReturnOrderItem();
                    xdaReturnOrderItem.setReturnOrderId(id);
                    xdaReturnOrderItem.setCommodityId(dto.getCommodityId());
                    xdaReturnOrderItem.setCommodityPrice(dto.getCommodityPrice());
                    xdaReturnOrderItem.setComplaintContent(dto.getComplaintContent());
                    //计算申请详情
                    BigDecimal applyQuantity = dto.getRealReturnQuantity().abs();
                    super.calculateApplyDetails(applyQuantity, dto, commodity, xdaReturnOrderItem);
                    xdaReturnOrderItem.setReturnReasonType(dto.getQuestionType());
                    xdaReturnOrderItem.setCommodityOrderQuantity(dto.getRealDeliveryQuantity());
                    if (dto.getComplaintType() == 1) {
                        xdaReturnOrderItem.setReturnOrderType(TDaReturnOrderTypeEnum.SHORTAGE.getCode());
                    } else if (dto.getComplaintType() == 2) {
                        xdaReturnOrderItem.setReturnOrderType(TDaReturnOrderTypeEnum.RETURN.getCode());
                    } else if (dto.getComplaintType() == 3) {
                        xdaReturnOrderItem.setReturnOrderType(TDaReturnOrderTypeEnum.OVERAGE.getCode());
                    }
                    return xdaReturnOrderItem;
                }).collect(Collectors.toList());

        if (CollectionUtils.isNotEmpty(xdaReturnOrderItems)) {
            // 插入 退货明细
            xdaReturnOrderItemMapper.batchInsertSelective(xdaReturnOrderItems);
        }
    }

    @Override
    public Integer getReturnSourceType() {
        return TDaReturnSourceEnum.PX_RETURN.getCode();
    }
}