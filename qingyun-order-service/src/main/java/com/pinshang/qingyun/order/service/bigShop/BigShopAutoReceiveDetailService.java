package com.pinshang.qingyun.order.service.bigShop;/**
 * @Author: sk
 * @Date: 2025/8/4
 */

import com.pinshang.qingyun.base.enums.YesOrNoEnums;
import com.pinshang.qingyun.base.enums.xd.StorageAreaEnum;
import com.pinshang.qingyun.box.utils.BeanCloneUtils;
import com.pinshang.qingyun.box.utils.DateUtil;
import com.pinshang.qingyun.box.utils.JsonUtil;
import com.pinshang.qingyun.order.dto.bigShop.DdReceiveOrderCommodityODTO;
import com.pinshang.qingyun.order.dto.bigShop.DdReceiveUpdateSubOrderCommodityQuantityDTO;
import com.pinshang.qingyun.order.enums.ReceiveOrderStatusEnums;
import com.pinshang.qingyun.order.mapper.CommodityMapper;
import com.pinshang.qingyun.order.mapper.ShopReceiveOrderMapper;
import com.pinshang.qingyun.order.mapper.bigShop.DdReceiveDocLogMapper;
import com.pinshang.qingyun.order.mapper.bigShop.DdReceiveDocMapper;
import com.pinshang.qingyun.order.mapper.bigShop.DdReceiveDocOrderMapper;
import com.pinshang.qingyun.order.mapper.entry.commodity.CommodityBasicEntry;
import com.pinshang.qingyun.order.model.bigShop.DdReceiveDoc;
import com.pinshang.qingyun.order.model.bigShop.DdReceiveDocLog;
import com.pinshang.qingyun.order.model.bigShop.DdReceiveDocOrder;
import com.pinshang.qingyun.order.model.shop.ShopReceiveOrder;
import com.pinshang.qingyun.order.vo.commodity.CommodityVO;
import com.pinshang.qingyun.xd.wms.dto.StockReceiptIDTO;
import com.pinshang.qingyun.xd.wms.dto.StockReceiptItemDTO;
import com.pinshang.qingyun.xd.wms.dto.bigShop.DdStockInOutExtraIDTO;
import com.pinshang.qingyun.xd.wms.service.XdStockClient;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.entity.Example;

import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025年08月04日 下午2:54
 */
@Slf4j
@Service
public class BigShopAutoReceiveDetailService {

    @Autowired
    private DdReceiveDocMapper ddReceiveDocMapper;
    @Lazy
    @Autowired
    private BigShopReceiveService bigShopReceiveService;
    @Autowired
    private ShopReceiveOrderMapper shopReceiveOrderMapper;
    @Autowired
    private DdReceiveDocOrderMapper ddReceiveDocOrderMapper;
    @Autowired
    private CommodityMapper commodityMapper;
    @Autowired
    private DdReceiveDocLogMapper ddReceiveDocLogMapper;
    @Autowired
    private XdStockClient xdStockClient;

    /**
     * 大店自动收货明细
     * @param ddReceiveDoc
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public Boolean bigShopAutoReceiveDetail(DdReceiveDoc ddReceiveDoc){
        Long userId = -1L;
        Date nowTime = new Date();

        // 大店收货单下面所有的订单都已经发货了，才能收货
        List<DdReceiveOrderCommodityODTO> orderCommodityList = ddReceiveDocMapper.getOrderListByReceiveDoc(ddReceiveDoc.getShopId(),
                ddReceiveDoc.getStallId(), DateUtil.getDateFormate(ddReceiveDoc.getOrderTime(), "yyyy-MM-dd"),
                bigShopReceiveService.getDeliveryBatchList(ddReceiveDoc.getDeliveryBatch()));

        if(CollectionUtils.isEmpty(orderCommodityList)) {
            log.warn("收货单下没有可收货的订单. docId {}", ddReceiveDoc.getId());
            return false;
        }

        List<DdReceiveOrderCommodityODTO> realDeliveryEmptyList = orderCommodityList.stream().filter(p -> p.getRealDeliveryQuantity() == null).collect(Collectors.toList());
        if(CollectionUtils.isNotEmpty(realDeliveryEmptyList)) {
            log.warn("存在没有发货的订单，不能自动收货. docId {} 未发货信息 {} ", ddReceiveDoc.getId(), JsonUtil.java2json(realDeliveryEmptyList));
            return false;
        }

        // 更新子单实收数量
        List<DdReceiveUpdateSubOrderCommodityQuantityDTO> updateList = Lists.newArrayList();
        // 大店收货订单记录表
        List<DdReceiveDocOrder> receiveDocOrderList = new ArrayList<>();
        Map<Long, BigDecimal> commodityPriceMap = new HashMap<>(orderCommodityList.size());
        Set<Long> subOrderIdList = new HashSet<>();
        // 记录订单号重复
        Set<String> orderCodeList = new HashSet<>();

        for(DdReceiveOrderCommodityODTO comm : orderCommodityList) {
            // 实收数量 = 实发数量
            updateList.add(new DdReceiveUpdateSubOrderCommodityQuantityDTO(comm.getRealDeliveryQuantity(), comm.getSubOrderItemId(), comm.getCommodityId()));

            subOrderIdList.add(comm.getSubOrderId());

            // 设置关联订单号
            if(!orderCodeList.contains(comm.getOrderCode())){
                receiveDocOrderList.add(bigShopReceiveService.getReceiveDocOrderList(userId, nowTime, ddReceiveDoc, comm.getOrderCode(), comm.getLogisticsModel(), comm.getOrderAmount()));
            }
            orderCodeList.add(comm.getOrderCode());

            commodityPriceMap.put(comm.getCommodityId(), comm.getPrice());
        }

        // 更新实收数量
        if(CollectionUtils.isNotEmpty(updateList)){
            ddReceiveDocMapper.batchUpdateRealReceiveQuantity(updateList);
        }

        // 将收货单置为已收货
        updateShopReceiveOrderPass(subOrderIdList, userId, nowTime);

        // 更新主单为已经收货
        updateDocReceive(ddReceiveDoc, userId, nowTime);

        // 大店收货订单记录表
        if(CollectionUtils.isNotEmpty(receiveDocOrderList)){
            ddReceiveDocOrderMapper.insertList(receiveDocOrderList);
        }

        // 大店收货单日志表
        List<DdReceiveDocLog> receiveDocLogList = new ArrayList<>();
        List<StockReceiptItemDTO> stockReceiptList = new ArrayList<>();

        // 组装日志和入库信息
        setAutoReceiveLogAndStockReceiptList(ddReceiveDoc, orderCommodityList, userId, nowTime,
                     receiveDocLogList, stockReceiptList, commodityPriceMap);

        // 大店收货单日志表
        if(CollectionUtils.isNotEmpty(receiveDocLogList)){
            ddReceiveDocLogMapper.insertList(receiveDocLogList);
        }

        // 调用入库操作
        stockReceipt(ddReceiveDoc, userId, stockReceiptList);
        return true;
    }


    /**
     * 调用入库操作
     * @param ddReceiveDoc
     * @param userId
     * @param stockReceiptList
     */
    private void stockReceipt(DdReceiveDoc ddReceiveDoc, Long userId, List<StockReceiptItemDTO> stockReceiptList) {
        StockReceiptIDTO stockReceiptIDTO = new StockReceiptIDTO();
        stockReceiptIDTO.setUserId(userId);
        stockReceiptIDTO.setReferId(ddReceiveDoc.getId());
        stockReceiptIDTO.setReferCode(ddReceiveDoc.getDocCode());
        stockReceiptIDTO.setWarehouseId(ddReceiveDoc.getShopId());
        stockReceiptIDTO.setCommodityList(stockReceiptList);
        xdStockClient.stockReceipt(stockReceiptIDTO);
    }

    /**
     * 更新主单为已经收货
     * @param ddReceiveDoc
     * @param userId
     * @param nowTime
     */
    private void updateDocReceive(DdReceiveDoc ddReceiveDoc, Long userId, Date nowTime) {
        ddReceiveDoc.setReceiveUserId(userId);
        ddReceiveDoc.setReceiveTime(nowTime);
        ddReceiveDoc.setDocStatus(YesOrNoEnums.YES.getCode());
        ddReceiveDoc.setUpdateId(userId);
        ddReceiveDoc.setUpdateTime(nowTime);
        ddReceiveDocMapper.updateByPrimaryKey(ddReceiveDoc);
    }

    /**
     * 将收货单置为已收货
     * @param subOrderIdList
     * @param userId
     * @param nowTime
     */
    private void updateShopReceiveOrderPass(Set<Long> subOrderIdList, Long userId, Date nowTime) {
        if(CollectionUtils.isNotEmpty(subOrderIdList)){
            ShopReceiveOrder shopReceiveOrder = new ShopReceiveOrder();
            shopReceiveOrder.setStatus(ReceiveOrderStatusEnums.PASS.getCode());
            shopReceiveOrder.setUpdateId(userId);
            shopReceiveOrder.setUpdateTime(nowTime);
            shopReceiveOrder.setReceiveId(userId);
            shopReceiveOrder.setReceiveTime(nowTime);
            Example example = new Example(ShopReceiveOrder.class);
            example.createCriteria().andIn("subOrderId", subOrderIdList);
            shopReceiveOrderMapper.updateByExampleSelective(shopReceiveOrder, example);
        }
    }

    /**
     * 组装日志和入库信息
     */
    private void setAutoReceiveLogAndStockReceiptList(DdReceiveDoc ddReceiveDoc,
                                   List<DdReceiveOrderCommodityODTO> orderCommodityList,
                                   Long userId, Date nowTime,
                                   List<DdReceiveDocLog> receiveDocLogList,
                                   List<StockReceiptItemDTO> stockReceiptList,
                                   Map<Long, BigDecimal> commodityPriceMap) {

        // 查询商品基础信息
        CommodityVO vo = new CommodityVO();
        vo.setCommodityIdList(new ArrayList<>(commodityPriceMap.keySet()));
        List<CommodityBasicEntry> commodityBasicList = commodityMapper.findCommodityBasicListByParam(vo);
        Map<String, CommodityBasicEntry> commodityBasicMap = commodityBasicList.stream().collect(Collectors.toMap(CommodityBasicEntry::getCommodityId, Function.identity()));


        // 商品实发数量map
        Map<Long, BigDecimal> commodityRealDeliveryQuantityMap = orderCommodityList.stream()
                .collect(Collectors.groupingBy(
                        DdReceiveOrderCommodityODTO::getCommodityId,
                        Collectors.reducing(BigDecimal.ZERO, DdReceiveOrderCommodityODTO::getRealDeliveryQuantity, BigDecimal::add)
                ));

        // 商品订货数量map
        Map<Long, BigDecimal> commodityOrderQuantityMap = orderCommodityList.stream()
                .collect(Collectors.groupingBy(
                        DdReceiveOrderCommodityODTO::getCommodityId,
                        Collectors.reducing(BigDecimal.ZERO, DdReceiveOrderCommodityODTO::getQuantity, BigDecimal::add)
                ));

        commodityOrderQuantityMap.forEach((commodityId, orderQuantity) -> {
            CommodityBasicEntry commodityBasicEntry = commodityBasicMap.get(commodityId.toString());
            BigDecimal commodityPackageSpec = commodityBasicEntry.getCommodityPackageSpec();
            BigDecimal realDeliveryQuantity = commodityRealDeliveryQuantityMap.get(commodityId);

            // 组装日志
            DdReceiveDocLog ddReceiveDocLog = BeanCloneUtils.copyTo(ddReceiveDoc, DdReceiveDocLog.class);
            ddReceiveDocLog.setDocId(ddReceiveDoc.getId());

            ddReceiveDocLog.setCommodityId(commodityId);
            ddReceiveDocLog.setQuantity(orderQuantity);
            ddReceiveDocLog.setRealDeliveryQuantity(realDeliveryQuantity);
            ddReceiveDocLog.setRealReceiveQuantity(realDeliveryQuantity);
            ddReceiveDocLog.setShortReceiveQuantity(BigDecimal.ZERO);
            // 自动收货默认收到排面区
            ddReceiveDocLog.setStorageArea(StorageAreaEnum.SHELF_AREA.getCode());
            ddReceiveDocLog.setCreateId(userId);
            ddReceiveDocLog.setUpdateId(userId);
            ddReceiveDocLog.setCreateTime(nowTime);
            ddReceiveDocLog.setUpdateTime(nowTime);
            receiveDocLogList.add(ddReceiveDocLog);


            // 组装入库
            StockReceiptItemDTO itemDTO = new StockReceiptItemDTO();
            itemDTO.setCommodityId(commodityId);
            itemDTO.setQuantity(realDeliveryQuantity);
            itemDTO.setNumber(itemDTO.getQuantity().divide(commodityPackageSpec, 0, BigDecimal.ROUND_UP).intValue());
            itemDTO.setNormalNumber(itemDTO.getNumber());
            itemDTO.setNormalQuantity(itemDTO.getQuantity());

            itemDTO.setAbnormalNumber(0);
            itemDTO.setAbnormalQuantity(BigDecimal.ZERO);
            itemDTO.setPrice(commodityPriceMap.get(commodityId));
            itemDTO.setTotalPrice(itemDTO.getPrice().multiply(itemDTO.getQuantity()));

            DdStockInOutExtraIDTO ddStockInOutExtraIDTO = DdStockInOutExtraIDTO.buildDdStockInOutExtraVO(commodityId,
                    ddReceiveDoc.getStallId(), StorageAreaEnum.SHELF_AREA.getCode(), null, null);
            itemDTO.setDdStockInOutExtraVO(ddStockInOutExtraIDTO);

            stockReceiptList.add(itemDTO);
        });
    }
}
