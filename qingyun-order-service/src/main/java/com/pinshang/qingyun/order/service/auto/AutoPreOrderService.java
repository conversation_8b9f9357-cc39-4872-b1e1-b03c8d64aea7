package com.pinshang.qingyun.order.service.auto;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.pinshang.qingyun.base.api.TokenInfo;
import com.pinshang.qingyun.base.configure.expand.BizLogicException;
import com.pinshang.qingyun.base.configure.expand.QYAssert;
import com.pinshang.qingyun.base.enums.IogisticsModelEnums;
import com.pinshang.qingyun.base.util.FastThreadLocalUtil;
import com.pinshang.qingyun.box.utils.DateTimeUtil;
import com.pinshang.qingyun.box.utils.DateUtil;
import com.pinshang.qingyun.box.utils.SpringUtil;
import com.pinshang.qingyun.box.utils.StringUtil;
import com.pinshang.qingyun.common.service.CodeClient;
import com.pinshang.qingyun.order.config.ShopPreOrderConstants;
import com.pinshang.qingyun.order.dto.*;
import com.pinshang.qingyun.order.enums.FilterTipEnum;
import com.pinshang.qingyun.order.enums.ShopPreOrderStatusEnums;
import com.pinshang.qingyun.order.mapper.*;
import com.pinshang.qingyun.order.mapper.auto.AutoPreOrderItemMapper;
import com.pinshang.qingyun.order.mapper.auto.AutoPreOrderMapper;
import com.pinshang.qingyun.order.mapper.entry.MdShopOrderSettingEntry;
import com.pinshang.qingyun.order.mapper.entry.StoreEntry;
import com.pinshang.qingyun.order.mapper.entry.order.CommodityInfoEntry;
import com.pinshang.qingyun.order.model.auto.AutoPreOrder;
import com.pinshang.qingyun.order.model.auto.AutoPreOrderItem;
import com.pinshang.qingyun.order.model.order.ShoppingCart;
import com.pinshang.qingyun.order.model.order.ShoppingCartItem;
import com.pinshang.qingyun.order.model.shop.Shop;
import com.pinshang.qingyun.order.service.BStockService;
import com.pinshang.qingyun.order.service.MdShopOrderSettingService;
import com.pinshang.qingyun.order.service.OrderService;
import com.pinshang.qingyun.order.util.ThreadLocalUtils;
import com.pinshang.qingyun.order.vo.order.*;
import com.pinshang.qingyun.smm.dto.user.SelectUserShopIdListIDTO;
import com.pinshang.qingyun.smm.service.SMMUserClient;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.entity.Example;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @Author: liuZhen
 * @DateTime: 2022/5/13 15:55
 */
@Slf4j
@Service
public class AutoPreOrderService {
    @Autowired
    private AutoPreOrderMapper autoPreOrderMapper;
    @Autowired
    private AutoPreOrderItemMapper autoPreOrderItemMapper;
    @Autowired
    private ShoppingCartMapper shoppingCartMapper;
    @Autowired
    private ShoppingCartItemMapper shoppingCartItemMapper;
    @Autowired
    private CodeClient codeClient;
    @Autowired
    private ShopMapper shopMapper;
    @Autowired
    private CommodityMapper commodityMapper;
    @Lazy
    @Autowired
    private ShopAutoOrderService shopAutoOrderService;
    @Lazy
    @Autowired
    private OrderService orderService;
    @Lazy
    @Autowired
    private MdShopOrderSettingService mdShopOrderSettingService;
    @Autowired
    private OrderListMapper orderListMapper;
    @Autowired
    private SMMUserClient smmUserClient;
    @Autowired
    private BStockService bStockService;

    /**
     * 提交审核校验
     * @return
     */
    public OrderDto checkSubmitAudit(ShoppingCart shoppingCart, List<ShoppingCartItem> shoppingCartItems){
        TokenInfo tokenInfo = FastThreadLocalUtil.getQY();
        ThreadLocalUtils.set(tokenInfo.getIsInternal());

        // 检查客户状态
        orderService.checkStoreStatus(shoppingCart.getStoreId(), "当前客户已停用，不允许下单");

        OrderRequestVo orderRequestVo = new OrderRequestVo();
        orderRequestVo.setStoreId(shoppingCart.getStoreId() + "");
        orderRequestVo.setOrderTime(DateTimeUtil.defaultDeliveryDate());
        orderRequestVo.setLogisticsModel(shoppingCart.getLogisticsModel());
        orderRequestVo.setSupplierId(shoppingCart.getSupplierId());
        orderRequestVo.setWarehouseId(shoppingCart.getWarehouseId());
        orderRequestVo.setDeleveryTimeRange(shoppingCart.getDeleveryTimeRange());
        List<OrderItemRequestVo> itemsList = new ArrayList<OrderItemRequestVo>();
        shoppingCartItems.forEach(i->{
            OrderItemRequestVo item = new OrderItemRequestVo();
            item.setProductId(i.getCommodityId().toString());
            item.setProductNum(i.getCommodityNum());
            itemsList.add(item);
        });
        orderRequestVo.setItemsList(itemsList);

        // 1. 检查订单商品
        OrderDto orderDto = orderService.checkOriginItems(orderRequestVo);

        // 2. 过滤商品
        orderService.filters(orderDto);

        return orderDto;
    }

    //提交审核，将购物车商品放入preOrder
    @Transactional(rollbackFor = Exception.class)
    public CreateOrderFilterVo submitAudit(Long id, Long userId, String deliveryBatch) {
        CreateOrderFilterVo createOrderODTO = new CreateOrderFilterVo(true, null, null);

        TokenInfo tokenInfo = FastThreadLocalUtil.getQY();
        //将购物车内信息转换为订单审核信息
        ShoppingCart shoppingCart = shoppingCartMapper.selectByPrimaryKey(id);
        List<ShoppingCartItem> shoppingCartItems = shoppingCartItemMapper.queryShoppingCartItemList(id);
        List<Long> storeIds = new ArrayList<>();
        storeIds.add(shoppingCart.getStoreId());
        List<StoreEntry> selectStoreList = shopMapper.selectStoreList(storeIds);

        // 提交审核校验
        OrderDto orderDto = checkSubmitAudit(shoppingCart, shoppingCartItems);
        // 设置商品的编码并去重复
        List<FilterTipVo> filterTipList = orderService.distinctFilterTips();
        List<FilterTipVo> commodityList = filterTipList.stream().filter(item -> FilterTipEnum.SETTING_CHANGE.getType().equals(item.getType())).collect(Collectors.toList());
        if(CollectionUtils.isNotEmpty(commodityList)){
            // 将配置变化的购物车删除并重新加入购物车
            orderService.batchAddShoppingCartForFilterCommodityAndDelete(shoppingCart,commodityList);
        }
        createOrderODTO.setFilterTips(filterTipList);
        ThreadLocalUtils.remove();

        QYAssert.isTrue(CollectionUtils.isNotEmpty(orderDto.getItems()) , "有效商品为空");
        Map<String, OrderItemDto> orderItemDtoMap = orderDto.getItems().stream().collect(Collectors.toMap(OrderItemDto::getProductId, Function.identity()));
        AutoPreOrder autoPreOrder = new AutoPreOrder();
        autoPreOrder.setShopId(selectStoreList.get(0).getShopId());
        String orderCode = codeClient.createCode(ShopPreOrderConstants.SHOP_PRE_ORDER_CODE_TYPE);
        orderCode = orderCode.replace(ShopPreOrderConstants.SHOP_PRE_ORDER_CODE_PRE_FIX + "20", ShopPreOrderConstants.SHOP_PRE_ORDER_CODE_PRE_FIX);
        autoPreOrder.setOrderCode(orderCode);
        autoPreOrder.setLogisticsModel(shoppingCart.getLogisticsModel());
        autoPreOrder.setStatus(ShopPreOrderStatusEnums.AUDIT_WAIT.getCode());
        autoPreOrder.setCreateId(tokenInfo.getUserId());
        autoPreOrder.setUpdateId(tokenInfo.getUserId());
        autoPreOrder.setCreateTime(new Date());
        autoPreOrder.setUpdateTime(new Date());
        List<AutoPreOrderItem> listItems = new ArrayList<>();
        BigDecimal orderAmount = new BigDecimal("0.00");
        List<Long> commodityIds = shoppingCartItems.stream().map(item -> item.getCommodityId()).collect(Collectors.toList());
        List<CommodityInfoEntry> commodityInfoEntries = commodityMapper.findCommodityInfoIds(commodityIds);
        Map<Long, CommodityInfoEntry> commoditySpecMap = commodityInfoEntries.stream().collect(Collectors.toMap(CommodityInfoEntry::getId, Function.identity()));
        for (int i = 0; i < shoppingCartItems.size(); i++) {
            OrderItemDto orderItemDto = orderItemDtoMap.get(shoppingCartItems.get(i).getCommodityId().toString());
            // 为空说明商品订货设置发生变化，被过滤了
            if(orderItemDto == null){
                continue;
            }
            orderAmount = orderAmount.add(orderItemDto.getPrice().multiply(shoppingCartItems.get(i).getCommodityNum()).setScale(2,BigDecimal.ROUND_HALF_UP));
            shoppingCartItems.get(i);
            AutoPreOrderItem autoPreOrderItem = new AutoPreOrderItem();
            autoPreOrderItem.setStockQuantity(shoppingCartItems.get(i).getCommodityNum());
            autoPreOrderItem.setStockNumber(shoppingCartItems.get(i).getCommodityNum().divide(commoditySpecMap.get(shoppingCartItems.get(i).getCommodityId()).getCommodityPackageSpec()).intValue());
            autoPreOrderItem.setCreateId(tokenInfo.getUserId());
            autoPreOrderItem.setCreateTime(new Date());
            autoPreOrderItem.setUpdateId(tokenInfo.getUserId());
            autoPreOrderItem.setUpdateTime(new Date());
            autoPreOrderItem.setCommodityId(shoppingCartItems.get(i).getCommodityId());
            autoPreOrderItem.setPrice(orderItemDtoMap.get(shoppingCartItems.get(i).getCommodityId().toString()).getPrice());
            listItems.add(autoPreOrderItem);
        }
        autoPreOrder.setOrderAmount(orderAmount);
        autoPreOrder.setConsignmentId(shoppingCart.getConsignmentId());
        autoPreOrderMapper.insertUseGeneratedKeys(autoPreOrder);
        listItems.forEach(i -> i.setAutoPreId(autoPreOrder.getId()));
        autoPreOrderItemMapper.insertList(listItems);
        shopAutoOrderService.deleteAutoShoppingCart(id);
        return createOrderODTO;
    }

    //查询列表
    public PageInfo<AutoPreOrderODTO> list(AutoPreOrderIDTO idto) {
        // 代销商判断
        TokenInfo tokenInfo = FastThreadLocalUtil.getQY();
        idto.setIsInternal(tokenInfo.getIsInternal());
        idto.setConsignmentId(tokenInfo.getConsignmentId());

        List<Long> shopIdList = smmUserClient.selectUserShopIdList(SelectUserShopIdListIDTO.onlyDb(FastThreadLocalUtil.getQY().getUserId()));
        if(CollectionUtils.isEmpty(shopIdList)){
            PageInfo info = new PageInfo();
            info.setList(null);
            return info;
        }
        idto.setShopIdList(shopIdList);

        if (idto.getOrderCode() != null && idto.getOrderCode().length() > 30) {
            idto.setOrderCode(idto.getOrderCode().substring(0, 30));
        }
        if(!StringUtil.isBlank(idto.getDeliveryStartTime()) && !StringUtil.isBlank(idto.getDeliveryEndTime())){
            idto.setDeliveryStartTime(idto.getDeliveryStartTime()+" 00:00:00");
           idto.setDeliveryEndTime(idto.getDeliveryEndTime()+" 23:59:59");
        }
        PageInfo<AutoPreOrderODTO> pageInfo = PageHelper.startPage(idto.getPageNo(), idto.getPageSize()).doSelectPageInfo(
                () -> autoPreOrderMapper.queryByIDTO(idto));
        return pageInfo;
    }

    //审核，订单编号，送货日期，状态
    //详情
    public AutoPreOrderDetailDTO detailByPreOrderId(Long preOrderId) {
        AutoPreOrderODTO orderODTO=autoPreOrderMapper.queryById(preOrderId);
        List<AutoPreOrderItemODTO> list = autoPreOrderItemMapper.queryByPreOrderId(preOrderId);
        AutoPreOrderDetailDTO autoPreOrderDetailDTO = new AutoPreOrderDetailDTO();
        Shop shop =shopMapper.selectByPrimaryKey(orderODTO.getShopId());
        String storeOrderTime="";
        List<String> commodityIdList= list.stream().map(i->i.getCommodityId().toString()).collect(Collectors.toList());
        List<MdShopOrderSettingEntry> settingList = mdShopOrderSettingService.queryMdShopOrderSettingListByIds(shop.getStoreId(), commodityIdList);
        QYAssert.isTrue(SpringUtil.isNotEmpty(settingList), "门店订货配置信息有误");
        MdShopOrderSettingEntry mdShopOrderSettingEntry=  settingList.get(0);
        //所有商品时间是一样的
        if (mdShopOrderSettingEntry.getLogisticsModel().equals(IogisticsModelEnums.DISPATCHING.getCode())) {
           storeOrderTime= mdShopOrderSettingEntry.getDefaultWarehouseBeginTime() + "-"+mdShopOrderSettingEntry.getDefaultWarehouseEndTime();
        }
        if (mdShopOrderSettingEntry.getLogisticsModel().equals(IogisticsModelEnums.DIRECT_CONNECTION.getCode())) {
            storeOrderTime= mdShopOrderSettingEntry.getDefaultSupplierBeginTime() + "-"+ mdShopOrderSettingEntry.getDefaultSupplierEndTime();

        }
        orderODTO.setStoreOrderTime(storeOrderTime);
        autoPreOrderDetailDTO.setOrder(orderODTO);
        list.forEach(i->i.setTotalPrice(i.getSupplyPrice().multiply(i.getStockQuantity()).setScale(2,BigDecimal.ROUND_HALF_UP)));
        autoPreOrderDetailDTO.setItemODTOList(list);
        return autoPreOrderDetailDTO;

    }

    @Transactional(rollbackFor = Exception.class)
    public List<String> auditPassOrFail(AutoPreOrderAuditIDTO idto) {
        AutoPreOrderAuditDTO autoPreOrderAuditDTO = new AutoPreOrderAuditDTO();
        TokenInfo tokenInfo = FastThreadLocalUtil.getQY();
        AutoPreOrder autoPreOrder = autoPreOrderMapper.selectByPrimaryKey(idto.getId());
        QYAssert.isTrue(ShopPreOrderStatusEnums.AUDIT_WAIT.getCode().equals(autoPreOrder.getStatus()), "加货申请单审核状态不是待审核");
        autoPreOrder.setAuditId(tokenInfo.getUserId());
        autoPreOrder.setAuditTime(new Date());
        if (!idto.getPassOrFail()) {
            //审核不通过
            autoPreOrder.setStatus(ShopPreOrderStatusEnums.AUDIT_FAIL.getCode());
        } else {

            // 审核通过校验客户状态
            Shop shop = shopMapper.selectByPrimaryKey(autoPreOrder.getShopId());
            orderService.checkStoreStatus(shop.getStoreId(), "当前客户已停用，不允许审核通过!");

            autoPreOrder.setStatus(ShopPreOrderStatusEnums.AUDIT_PASS.getCode());
            try {
                autoPreOrderAuditDTO = shopAutoOrderService.autoPreOrderAuditPass(autoPreOrder.getOrderCode());
                autoPreOrder.setOrderId(Long.parseLong(autoPreOrderAuditDTO.getOrderCode()));
                autoPreOrder.setDeliveryTime(DateUtil.parseDate(autoPreOrderAuditDTO.getOrderTime(), "yyyy-MM-dd"));
            } catch (Throwable e) {
                log.error("审核通过,创建订单异常", e);
                throw new BizLogicException(e.getMessage());
            }
            //修改价格
            Example example = new Example(AutoPreOrderItem.class);
            example.createCriteria().andEqualTo("autoPreId", autoPreOrder.getId());
            List<AutoPreOrderItem> autoPreOrderItems=autoPreOrderItemMapper.selectByExample(example);
            Map<Long, AutoPreOrderItem> orderItemMap = autoPreOrderItems.stream().collect(Collectors.toMap(AutoPreOrderItem::getCommodityId, Function.identity()));
            //通过orderCode 获取订单商品
            List<AutoPreOrderGiftItemODTO> itemODTOS = orderListMapper.findAllByOrderCode(autoPreOrder.getOrderId().toString());
            BigDecimal orderAmount =BigDecimal.ZERO;
            for (AutoPreOrderGiftItemODTO i :itemODTOS) {
                if (i.getType()== 1) {
                    AutoPreOrderItem   autoPreOrderItem = orderItemMap.get(i.getCommodityId());
                    //价格有变化
                    if (!orderItemMap.get(i.getCommodityId()).getPrice().equals(i.getPrice())){
                        autoPreOrderItem.setPrice(i.getPrice());
                        autoPreOrderItemMapper.updateByPrimaryKey(autoPreOrderItem);
                    }
                    orderAmount=orderAmount.add(i.getPrice().multiply(autoPreOrderItem.getStockQuantity())).setScale(2,BigDecimal.ROUND_HALF_UP);
                }
            }
            autoPreOrder.setOrderAmount(orderAmount);
        }
        autoPreOrderMapper.updateByPrimaryKey(autoPreOrder);

        // 返回库存不足的商品编码list
        return autoPreOrderAuditDTO.getCommodityCodeList();
    }


    public Boolean cancel(Long id) {
        TokenInfo tokenInfo = FastThreadLocalUtil.getQY();
        AutoPreOrder autoPreOrder = autoPreOrderMapper.selectByPrimaryKey(id);
        QYAssert.isTrue(ShopPreOrderStatusEnums.AUDIT_WAIT.getCode().equals(autoPreOrder.getStatus()), "加货申请单审核状态不是待审核");
        autoPreOrder.setAuditId(tokenInfo.getUserId());
        autoPreOrder.setAuditTime(new Date());
        autoPreOrder.setStatus(ShopPreOrderStatusEnums.CANCEL.getCode());
        autoPreOrderMapper.updateByPrimaryKey(autoPreOrder);
        return Boolean.TRUE;
    }



}
