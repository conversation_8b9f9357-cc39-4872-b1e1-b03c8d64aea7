package com.pinshang.qingyun.order.mapper;

import com.pinshang.qingyun.base.mybatis.MyMapper;
import com.pinshang.qingyun.order.model.order.ShoppingCartItem;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
@Repository
public interface ShoppingCartItemMapper extends MyMapper<ShoppingCartItem>{
	
	Integer deleteItemByCartId(@Param("shoppingCartId") Long shoppingCartId);

    List<ShoppingCartItem> queryShoppingCartItemList(@Param("shoppingCartId") Long shoppingCartId);
}