package com.pinshang.qingyun.order.mapper.entry;

import lombok.Data;

import java.util.Date;

@Data
public class MdShopOrderSettingEntry {

    private Long id;

    private Long enterpriseId;//企业ID

    private Long commodityId;
    private String productId;
    private String commodityCode;
    private String commodityName;//商品名称
    private String commoditySpec;//商品规格
    private Integer shopType;

    private String supplierId;
    private String supplierCode;
    private String supplierName;
    private String defaultSupplierBeginTime;
    private String defaultSupplierEndTime;

    private String warehouseId;
    private String warehouseName;
    private String defaultWarehouseBeginTime;
    private String defaultWarehouseEndTime;

    private Integer logisticsModel;//物流模式
    private String deleveryTimeRange;//配送范围
    private Integer changePriceStatus;//是否可变价
    private String deleveryTimeRangeName;//配送范围

    private Date createTime;

    private Date updateTime;

    private Long createId;

    private Long updateId;

    private  Integer isNew;

}
