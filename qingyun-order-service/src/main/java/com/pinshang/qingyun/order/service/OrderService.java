package com.pinshang.qingyun.order.service;

import com.alibaba.fastjson.JSON;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.pinshang.qingyun.base.api.TokenInfo;
import com.pinshang.qingyun.base.configure.expand.QYAssert;
import com.pinshang.qingyun.base.enums.*;
import com.pinshang.qingyun.base.enums.order.OrderTypeEnum;
import com.pinshang.qingyun.base.enums.order.ProductTypeEnum;
import com.pinshang.qingyun.base.enums.settlement.SettleCompaintSourceTypeEnum;
import com.pinshang.qingyun.base.enums.settlement.SettleOrderSourceTypeEnum;
import com.pinshang.qingyun.base.enums.shop.ManagementModeEnums;
import com.pinshang.qingyun.base.enums.storage.StockTypeEnum;
import com.pinshang.qingyun.base.enums.storage.ToBTypeEnums;
import com.pinshang.qingyun.base.enums.xd.StockInOutTypeEnums;
import com.pinshang.qingyun.base.enums.xd.StorageAreaEnum;
import com.pinshang.qingyun.base.enums.xda.XdaOrderProcessStatusEunm;
import com.pinshang.qingyun.base.util.FastThreadLocalUtil;
import com.pinshang.qingyun.box.utils.*;
import com.pinshang.qingyun.common.dto.DictionaryODTO;
import com.pinshang.qingyun.common.service.CodeClient;
import com.pinshang.qingyun.common.service.DictionaryClient;
import com.pinshang.qingyun.infrastructure.components.IMqSenderComponent;
import com.pinshang.qingyun.infrastructure.components.params.mq.MqMessage;
import com.pinshang.qingyun.kafka.KafkaConstant;
import com.pinshang.qingyun.kafka.MessageType;
import com.pinshang.qingyun.kafka.base.KafkaTopicConstant;
import com.pinshang.qingyun.kafka.enums.KafkaMessageOperationTypeEnum;
import com.pinshang.qingyun.kafka.enums.KafkaMessageTypeEnum;
import com.pinshang.qingyun.msg.dto.zsmd.MessageDTO;
import com.pinshang.qingyun.order.bo.StoreDeductionBO;
import com.pinshang.qingyun.order.bo.StoreRechargeBO;
import com.pinshang.qingyun.order.dto.*;
import com.pinshang.qingyun.order.dto.order.AssociationOrderODTO;
import com.pinshang.qingyun.order.dto.pda.PdaOrderCountODTO;
import com.pinshang.qingyun.order.dto.sync.SyncOrderIDTO;
import com.pinshang.qingyun.order.dto.sync.SyncOrderListODTO;
import com.pinshang.qingyun.order.dto.sync.SyncOrderODTO;
import com.pinshang.qingyun.order.dto.xda.TdaOrderSyncTmsIDTO;
import com.pinshang.qingyun.order.enums.*;
import com.pinshang.qingyun.order.mapper.*;
import com.pinshang.qingyun.order.mapper.entry.MdShopOrderSettingEntry;
import com.pinshang.qingyun.order.mapper.entry.commodity.CommodityResultEntry;
import com.pinshang.qingyun.order.mapper.entry.commodity.ProductLimitEntry;
import com.pinshang.qingyun.order.mapper.entry.commodity.ProductLogisticsEntry;
import com.pinshang.qingyun.order.mapper.entry.job.StoreOrderAmountEntry;
import com.pinshang.qingyun.order.mapper.entry.order.*;
import com.pinshang.qingyun.order.mapper.entry.order.generatePurchaseOrder.SubOrderEntry;
import com.pinshang.qingyun.order.mapper.entry.pda.PdaOrderQueryEntry;
import com.pinshang.qingyun.order.mapper.entry.store.EmployeeEntry;
import com.pinshang.qingyun.order.mapper.entry.store.OutstandingShopEntry;
import com.pinshang.qingyun.order.mapper.entry.store.StoreDurationEntry;
import com.pinshang.qingyun.order.mapper.entry.store.StoreEntry;
import com.pinshang.qingyun.order.model.commodity.Commodity;
import com.pinshang.qingyun.order.model.gift.GiftModel;
import com.pinshang.qingyun.order.model.gift.GiftModelCondition;
import com.pinshang.qingyun.order.model.gift.GiftProduct;
import com.pinshang.qingyun.order.model.order.*;
import com.pinshang.qingyun.order.model.promotion.Promotion;
import com.pinshang.qingyun.order.model.promotion.PromotionProduct;
import com.pinshang.qingyun.order.model.promotionStk.PromotionStk;
import com.pinshang.qingyun.order.model.promotionStk.PromotionStkCodeOrder;
import com.pinshang.qingyun.order.model.promotionStk.PromotionStkComm;
import com.pinshang.qingyun.order.model.shop.Shop;
import com.pinshang.qingyun.order.model.store.Store;
import com.pinshang.qingyun.order.service.auto.AutoPreOrderService;
import com.pinshang.qingyun.order.service.pda.PdaCommodityOrderCountCalculator;
import com.pinshang.qingyun.order.service.xda.v4.TdaOrderService;
import com.pinshang.qingyun.order.util.StallUtils;
import com.pinshang.qingyun.order.util.ThreadLocalUtils;
import com.pinshang.qingyun.order.vo.commodity.CreateHandleOrderRequestVO;
import com.pinshang.qingyun.order.vo.cup.StoreOrderCount;
import com.pinshang.qingyun.order.vo.order.*;
import com.pinshang.qingyun.order.vo.pda.PdaCommodityOrderQueryData;
import com.pinshang.qingyun.order.vo.pda.PdaCommodityOrderQueryRequestVo;
import com.pinshang.qingyun.order.vo.pda.PdaCommodityOrderQueryResponseVo;
import com.pinshang.qingyun.order.vo.shop.OutstandingShopVo;
import com.pinshang.qingyun.order.vo.shop.Quantity;
import com.pinshang.qingyun.order.vo.shop.ReceiveOrderVo;
import com.pinshang.qingyun.order.vo.store.StoreCompanyVo;
import com.pinshang.qingyun.price.dto.commodity.CommodityListRequestIDTO;
import com.pinshang.qingyun.price.dto.commodity.CommodityResultODTO;
import com.pinshang.qingyun.price.service.ProductPriceModelClient;
import com.pinshang.qingyun.purchase.dto.PurchaseOrderODto;
import com.pinshang.qingyun.purchase.service.PurchaseOrderClient;
import com.pinshang.qingyun.renderer.service.IRenderService;
import com.pinshang.qingyun.shop.admin.dto.ConsignmentSupplierInfoODTO;
import com.pinshang.qingyun.shop.admin.dto.CreditLimitInfoODTO;
import com.pinshang.qingyun.shop.admin.dto.CreditLimitVo;
import com.pinshang.qingyun.shop.admin.service.CreditLimitClient;
import com.pinshang.qingyun.shop.dto.ShopCommodityDetailODTO;
import com.pinshang.qingyun.shop.dto.ShopDto;
import com.pinshang.qingyun.shop.dto.ShopStockCommodityIDto;
import com.pinshang.qingyun.shop.service.ShopClient;
import com.pinshang.qingyun.shop.service.ShopCommodityClient;
import com.pinshang.qingyun.storage.dto.CommoditySupplierODto;
import com.pinshang.qingyun.storage.dto.WarehouseODto;
import com.pinshang.qingyun.storage.dto.stock.StockInOrderIDTO;
import com.pinshang.qingyun.storage.dto.stock.StockInOrderItemIDTO;
import com.pinshang.qingyun.storage.dto.stock.StockOutOrderIDTO;
import com.pinshang.qingyun.storage.dto.stock.StockOutOrderItemIDTO;
import com.pinshang.qingyun.storage.dto.tob.CommodityInventoryDetailIDTO;
import com.pinshang.qingyun.storage.dto.tob.CommodityInventoryODTO;
import com.pinshang.qingyun.storage.service.*;
import com.pinshang.qingyun.supplier.dto.FullSupplierODTO;
import com.pinshang.qingyun.supplier.dto.QuerySupplierByIdsIDTO;
import com.pinshang.qingyun.supplier.dto.SupplierODTO;
import com.pinshang.qingyun.supplier.service.SupplierClient;
import com.pinshang.qingyun.tms.dto.logisticscenterbatch.LogisticsBatchStoreInfoODTO;
import com.pinshang.qingyun.tms.dto.logisticscenterbatch.LogisticsBatchStoreSearchIDTO;
import com.pinshang.qingyun.tms.service.LogisticsCenterBatchClient;
import com.pinshang.qingyun.xd.wms.dto.StockReceiptIDTO;
import com.pinshang.qingyun.xd.wms.dto.StockReceiptItemDTO;
import com.pinshang.qingyun.xd.wms.dto.bigShop.DdStockInOutExtraIDTO;
import com.pinshang.qingyun.xd.wms.service.XdStockClient;
import jodd.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RLock;
import org.redisson.api.RSet;
import org.redisson.api.RedissonClient;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionSynchronization;
import org.springframework.transaction.support.TransactionSynchronizationAdapter;
import org.springframework.transaction.support.TransactionSynchronizationManager;
import tk.mybatis.mapper.entity.Example;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

import static java.util.stream.Collectors.toList;

@Slf4j
@Service
public class OrderService {
    @Autowired
    private OrderMapper orderMapper;
    @Autowired
    private OrderListMapper orderListMapper;
    @Autowired
    private OrderListGiftMapper orderListGiftMapper;
    @Autowired
    private CommodityMapper commodityMapper;
    @Autowired
    private ShoppingCartItemMapper shoppingCartItemMapper;
    @Autowired
    private ShoppingCartMapper shoppingCartMapper;
    @Autowired
    private StoreSettlementMapper storeSettlementMapper;
    @Autowired
    private OrderBillMapper orderBillMapper;
    @Autowired
    private SubOrderMapper subOrderMapper;
    @Autowired
    private ShoppingCartService shoppingCartService;

    @Autowired
    private OrderMirrorMapper orderMirrorMapper;
    @Autowired
    private CommonService commonService;
    @Autowired
    private PreOrderItemMapper preOrderItemMapper;
    @Autowired
    private PreOrderMapper preOrderMapper;
    @Autowired
    private CodeClient codeClient;
    @Autowired
    private StockOutOrderClient stockOutOrderClient;
    @Autowired
    private StockInOrderClient stockInOrderClient;
    @Autowired
    private ShopMapper shopMapper;
    @Autowired
    private ConsignmentSupplierService consignmentSupplierService;
    @Autowired
    private OrderAsyncKafkaService orderAsyncKafkaService;
    @Autowired
    private PurchaseOrderMapper purchaseOrderMapper;
    @Autowired
    private PurchaseOrderClient purchaseOrderClient;
    @Autowired
    private PurchaseService purchaseService;
    @Autowired
    private ProductPriceModelClient productPriceModelClient;
    @Autowired
    private OrderHistoryMapper orderHistoryMapper;

    @Autowired
    @Lazy
    private MdShopOrderSettingService mdShopOrderSettingService;

    @Autowired
    private ShopClient shopClient;

    @Autowired
    private ShopCommodityClient shopCommodityClient;

    @Autowired
    private SupplierClient supplierClient;

    @Autowired
    private CommoditySupplierClient commoditySupplierClient;

    @Autowired
    private SubOrderService subOrderService;

    @Autowired
    private StoreService storeService;

    @Autowired
    private DeliveryOrderClient deliveryOrderClient;

    @Value("${application.name.switch}")
    private String applicationNameSwitch;

    @Autowired
    private RedissonClient redissonClient;

    @Autowired
    private XdStockClient xdStockClient;
    @Autowired
    private StoreMapper storeMapper;

    @Autowired
    private WarehouseClient warehouseClient;

    @Autowired
    private ShopReceiveService shopReceiveService;

    @Autowired
    private IMqSenderComponent mqSenderComponent;

    @Autowired
    OrderListGiftMapper orderListGiftMappier;
    @Autowired
    private SubOrderItemMapper subOrderItemMapper;
    @Autowired
    private DictionaryClient dictionaryClient;
    @Autowired
    private AutoPreOrderService autoPreOrderService;
    @Autowired
    private CommodityService commodityService;
    @Autowired
    private OrderSaveService orderSaveService;
    @Autowired
    private PurchaseOrderService purchaseOrderService;
    @Autowired
    private BStockService bStockService;
    @Autowired
    private ShopService shopService;
    @Autowired
    private CreditLimitClient creditLimitClient;

    @Autowired
    private SaleReturnOrderService saleReturnOrderService;
    @Autowired
    private OrderPaymentService orderPaymentService;
    @Autowired
    private OrderBatchCancelService orderBatchCancelService;
    @Autowired
    private StoreRechargeService storeRechargeService;

    @Autowired
    private IRenderService renderService;
    @Autowired
    private LogisticsCenterBatchClient logisticsCenterBatchClient;
    @Autowired
    private TdaOrderService tdaOrderService;
    @Autowired
    private PromotionStkCodeOrderService promotionStkCodeOrderService;
    @Autowired
    private PromotionStkCommService promotionStkCommService;
    @Autowired
    private SettleKafkaService settleKafkaService;

    /**
     * 结算消息同步
     *
     * @param sourceCode
     * @param sourceType
     */
    public void fixedSettleOrder(String sourceCode, String sourceType, String topic) {
        QYAssert.isTrue(StringUtils.isNotBlank(sourceCode), "sourceCode is not null !");
        QYAssert.isTrue(StringUtils.isNotBlank(sourceType), "sourceType is not null !");
        QYAssert.isTrue(StringUtils.isNotBlank(topic), "topic is not null !");

        if (KafkaConstant.SETTLE_CANCEL_DATA_ALL_TOPIC.equals(topic)) {
            List<CancelVo> cancelVos = new ArrayList<>();
            CancelVo cancelVo = new CancelVo();
            cancelVo.setSourceCode(sourceCode);
            cancelVo.setSourceType(sourceType);
            cancelVos.add(cancelVo);
            commonService.sendKafkaMessage(cancelVos, MessageType.SETTLE_ORDER_CANCEL_SYNC, topic, null);
        } else {
            List<OrderVo> orderList = new ArrayList<>();

            List<SettleOrderVo> settleOrderList = orderMapper.querySettleOrder(sourceCode);
            if (CollectionUtils.isEmpty(settleOrderList)) {
                return;
            }

            List<ItemVo> itemList = new ArrayList<>();
            OrderVo orderVo = new OrderVo();
            BeanUtils.copyProperties(settleOrderList.get(0), orderVo);

            BigDecimal totalAmount = BigDecimal.ZERO;
            BigDecimal deliveryTotalAmount = BigDecimal.ZERO;
            Date deliveryTime = null;
            if ("RT_DIRECT_SENDING".equals(sourceType) || "ZS_ORDER".equals(sourceType)) {
                deliveryTime = new Date();
            } else {
                deliveryTime = orderVo.getOrderTime();
            }
            for (SettleOrderVo se : settleOrderList) {
                ItemVo item = new ItemVo();
                BeanUtils.copyProperties(se, item);
                item.setSourceId(se.getSourceId() + "");
                item.setDeliveryUnitPrice(se.getUnitPrice());

                BigDecimal deliveryTotalPrice = BigDecimal.ZERO;
                if ("RT_DIRECT_SENDING".equals(sourceType)) {
                    deliveryTotalPrice = se.getUnitPrice().multiply(se.getDeliveryNumber()).setScale(3, BigDecimal.ROUND_HALF_UP);
                } else {
                    deliveryTotalPrice = se.getTotalPrice();
                }

                item.setDeliveryNumber(se.getDeliveryNumber());
                item.setDeliveryTotalPrice(deliveryTotalPrice);
                itemList.add(item);
                totalAmount = totalAmount.add(se.getTotalPrice());

                deliveryTotalAmount = deliveryTotalAmount.add(deliveryTotalPrice);
            }
            orderVo.setSourceType(sourceType);
            orderVo.setTotalAmount(totalAmount);
            orderVo.setItems(itemList);
            // 新增
            orderVo.setSendMqTime(DateUtil.getDateFormate(new Date(), DateUtil.DEFAULT_DATE_FORMAT));
            orderVo.setDeliveryTime(deliveryTime);
            orderVo.setDeliveryTotalAmount(deliveryTotalAmount);

            // 是否代销订单
            if (orderVo.getConsignmentId() != null && orderVo.getConsignmentId() > 0) {
                orderVo.setSourceType("RT_DIRECT_SENDING".equals(sourceType) ? SettleCompaintSourceTypeEnum.RT_DX_ORDER.name() : SettleOrderSourceTypeEnum.DX_ORDER.name());
            }
            orderList.add(orderVo);
            if (CollectionUtils.isNotEmpty(orderList)) {
                commonService.sendKafkaMessage(orderList, MessageType.SETTLE_ORDER_SYNC, topic, null);
            }
        }
    }

    /**
     * 订单复制(新版本)
     *
     * @param orderId    订单id
     * @param isInternal 是否内部
     * @return 是否成功
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean copyOrderById2(Long orderId, Boolean isInternal) {
        QYAssert.isTrue(null != orderId, "orderId is not null !");
        Example ex = new Example(Order.class);
        ex.createCriteria().andEqualTo("id", orderId);
        List<Order> orderList = this.orderMapper.selectByExample(ex);
        QYAssert.isTrue(SpringUtil.isNotEmpty(orderList), "该订单有误,无法复制!");
        List<CommodityResultEntry> commList = this.orderMapper.findOrderProductByOrderId(orderList.get(0).getId());

        this.shoppingCartService.deleteShoppingCartAndItemByStoreId(orderList.get(0).getStoreId(), orderList.get(0).getStallId());
        ShoppingCartBatchVo batchVo = new ShoppingCartBatchVo();
        Order order = orderList.get(0);
        batchVo.setStoreId(order.getStoreId());
        batchVo.setEnterpriseId(order.getEnterpriseId());
        batchVo.setCreateId(order.getCreateId());
        batchVo.setInternal(isInternal);
        List<ShoppingCartBatchListVo> vos = new ArrayList<>();
        commList.forEach(item -> {
            ShoppingCartBatchListVo listVo = new ShoppingCartBatchListVo();
            listVo.setCommodityId(Long.parseLong(item.getCommodityId()));
            listVo.setQuantity(item.getCommodityNumber());
            vos.add(listVo);
        });
        batchVo.setList(vos);
        batchVo.setStallId(order.getStallId());
        return shoppingCartService.batchAddShoppingCart(batchVo);
    }

    @Transactional(rollbackFor = Exception.class)
    public boolean copyPreOrderById(Long orderId, Boolean isInternal) {

        QYAssert.isTrue(orderId != null, "orderId is not null !");
        PreOrder order = this.preOrderMapper.selectByPrimaryKey(orderId);
        QYAssert.isTrue(null != order, "该订单有误,无法复制!");

        Example ex = new Example(PreOrderItem.class);
        ex.createCriteria().andEqualTo("preorderId", orderId);
        List<PreOrderItem> poiList = this.preOrderItemMapper.selectByExample(ex);

        QYAssert.isTrue(SpringUtil.isNotEmpty(poiList), "该订单有误,无法复制!");

        List<CommodityResultEntry> commList = this.orderMapper.findPreOrderProductByOrderId(orderId);
        this.processCommodityPrice(order.getStoreId().toString(), commList);
        this.processCommodityPromotionPrice(order.getStoreId().toString(), commList, DateTimeUtil.defaultDeliveryDate());

        commList = commList.stream().filter(p -> {
            return (p.getCommodityType().intValue() == 1 && p.getCommodityPrice() != null && p.getCommodityPrice().compareTo(BigDecimal.ZERO) > 0);
        }).collect(Collectors.toList());

        if (SpringUtil.isNotEmpty(commList)) {
            this.shoppingCartService.deleteShoppingCartAndItemByStoreId(order.getStoreId(), order.getStallId());

            // 调用加入购物车方法
            ShoppingCartBatchVo batchVo = new ShoppingCartBatchVo();
            batchVo.setStoreId(order.getStoreId());
            batchVo.setEnterpriseId(order.getEnterpriseId());
            batchVo.setCreateId(order.getCreateId());
            batchVo.setInternal(isInternal);
            List<ShoppingCartBatchListVo> vos = new ArrayList<>();
            commList.forEach(item -> {
                ShoppingCartBatchListVo listVo = new ShoppingCartBatchListVo();
                listVo.setCommodityId(Long.parseLong(item.getCommodityId()));
                listVo.setQuantity(item.getCommodityNumber());
                vos.add(listVo);
            });
            batchVo.setList(vos);
            batchVo.setStallId(order.getStallId());
            return shoppingCartService.batchAddShoppingCart(batchVo);
        }
        return true;
    }

    /**
     * 处理商品价格
     *
     * @param storeId
     * @return
     */
    public void processCommodityPrice(String storeId, List<CommodityResultEntry> commodityResultList) {
        if (StringUtils.isNotBlank(storeId) && SpringUtil.isNotEmpty(commodityResultList)) {
            Map<String, Object> paramMap = new HashMap<String, Object>();
            paramMap.put("storeId", storeId);

            List<String> productIds = new ArrayList<String>();
            commodityResultList.forEach(i -> {
                productIds.add(i.getCommodityId());
            });
            if (SpringUtil.isNotEmpty(productIds)) {
                paramMap.put("productIds", productIds);
                //List<CommodityBaseEntry> commList = this.orderMapper.fincCommodityPriceByStoreId(paramMap);
                List<CommodityBaseEntry> commList = new ArrayList<>();

                CommodityListRequestIDTO idto = new CommodityListRequestIDTO();
                idto.setStoreId(storeId);
                List<Long> commodityIdList = productIds.stream().map(s -> Long.parseLong(s.trim())).collect(Collectors.toList());
                idto.setCommodityIdListAll(commodityIdList);
                List<CommodityResultODTO> commodityList = productPriceModelClient.findStoreCommodityList(idto);
                if (CollectionUtils.isNotEmpty(commodityList)) {
                    for (CommodityResultODTO commodityResult : commodityList) {
                        CommodityBaseEntry commodityBaseEntry = new CommodityBaseEntry();
                        commodityBaseEntry.setCommodityId(commodityResult.getCommodityId());
                        commodityBaseEntry.setCommodityCode(commodityResult.getCommodityCode());
                        commodityBaseEntry.setCommodityName(commodityResult.getCommodityName());
                        commodityBaseEntry.setCommodityPrice(commodityResult.getCommodityPrice());
                        commList.add(commodityBaseEntry);
                    }

                    commodityResultList.forEach(r -> {
                        commList.forEach(c -> {
                            if (r.getCommodityId().equals(c.getCommodityId())) {
                                r.setCommodityPrice(c.getCommodityPrice());
                            }
                        });
                    });
                }
            }
        }
    }

    /**
     * -> 促销价格
     */
    public void processCommodityPromotionPrice(String storeId, List<CommodityResultEntry> commList, String orderTime) {
        if (SpringUtil.isNotEmpty(commList) && StringUtils.isNotBlank(storeId)) {
            List<Promotion> promotionList = commonService.findCommodityPromotionByStoreId(storeId, orderTime);
            Promotion promotion = null;
            if (SpringUtil.isNotEmpty(promotionList)) {
                promotion = promotionList.get(0);
            }
            if (null != promotion) {
                List<PromotionProduct> promotionProductList = commonService.findPromotionProductByPromotionId(promotion.getId().toString());
                if (SpringUtil.isNotEmpty(promotionProductList)) {
                    promotionProductList.forEach(p -> {
                        commList.forEach(c -> {
                            if (c.getCommodityCode().equals(p.getProductCode())) {
                                c.setCommodityPrice(BigDecimal.valueOf(p.getPrice()));
                                c.setPromotionProduct(true);
                                if (p.getLimitNumber() > 0) {
                                    c.setCommodityNumberLimit(BigDecimal.valueOf(p.getLimitNumber()));
                                }
                            }
                        });
                    });
                }
            }
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public boolean preOrderCancelByOrderId(Long orderId, boolean isInternal, Long userId) throws Throwable {

        QYAssert.isTrue(null != orderId && orderId.intValue() > 0, "orderId is not null !");

        PreOrder order = this.preOrderMapper.selectByPrimaryKey(orderId);

        QYAssert.isTrue(null != order, "该订单有误,无法操作!");

        QYAssert.isTrue(order.getOrderStatus().intValue() != 0, "该订单已被取消,请刷新后再试!");

        QYAssert.isTrue(order.getReceiveStatus().intValue() == 0, "该订单已收货,无法取消!");
        QYAssert.isTrue(userId != null && (isInternal || order.getCreateId().equals(userId)), "操作失败，只能取消自己创建的订单！");

        // 检查供应商的截止时间
        log.info("---------------------orderId:{}", orderId);
        checkSupplierEndTime(order.getSupplierId(), order.getCreateTime());

        if (order.getOrderStatus().intValue() == 2) {//已生成采购单
            if (null != order.getPurchaseOrderId()) {
                //生成采购单 采购单过期没收货自动取消 这种情况不允许取消预订单
//				Integer purchaseOrderStatus = this.preOrderMapper.findPurchaseOrderStatus(order.getPurchaseOrderId());
                QYAssert.isTrue(null == order.getPurchaseOrderId(), "该预订单已经生成采购单,无法取消!");
            }
        }

        StoreDurationEntry sd = this.orderMapper.findStoreDurationByStoreId(order.getStoreId().toString());
        if (null != sd && !isInternal) {
            if (StringUtils.isNotBlank(sd.getBeginTime()) && StringUtils.isNotBlank(sd.getEndTime())) {
                QYAssert.isTrue(DateTimeUtil.compareNewDate(sd.getBeginTime(), sd.getEndTime()), "超出订货时间,无法取消该订单!");
            }
        }

        Integer rowNumber = this.preOrderMapper.updatePreOrderStatus(orderId, userId);
//        cancelOrderedQuantity(orderId ,true);
        return rowNumber > 0 ? true : false;
    }

    /**
     * 检查供应商的订货截止时间
     */
    public void checkSupplierEndTime(Long supplierId, Date createDate) {
        // 下单截止时间 = 预订单的下单日期(yyyy-MM-dd) + 供应商的截止订货时间(hh:mm:ss)
        QuerySupplierByIdsIDTO idsIDTO = new QuerySupplierByIdsIDTO();
        idsIDTO.setSupplierIds(Arrays.asList(supplierId));
        Map<Long, FullSupplierODTO> supplierIdAndODTOMap = supplierClient.querySupplierByIds(idsIDTO);
        QYAssert.isTrue(SpringUtil.isNotEmpty(supplierIdAndODTOMap), "未查询到该供应商");
        FullSupplierODTO fullSupplierODTO = supplierIdAndODTOMap.get(supplierId);
        QYAssert.isTrue(fullSupplierODTO != null, "未查询到该供应商");

        String endTime = fullSupplierODTO.getSupplyEndTime() + ":00";
        Date t1 = DateUtil.parseDate(DateUtil.get4yMd(createDate) + " " + endTime, DateUtil.DEFAULT_DATE_FORMAT);
        boolean beforeTodayDate = DateUtil.isBefore(new Date(), t1);
        QYAssert.isTrue(beforeTodayDate, "超过供应商的截止时间，不允许取消");
    }

    @Transactional(rollbackFor = Exception.class)
    public boolean orderCancelById(Long orderId, boolean isInternal, Long userId, String realName) throws Throwable {
        QYAssert.isTrue(null != orderId && orderId > 0, "orderId is error !");
        Order order = this.orderMapper.selectByPrimaryKey(orderId);
        QYAssert.isTrue(null != order, "该订单有误,无法操作!");
        QYAssert.isTrue(order.getOrderStatus().intValue() != 2, "该订单已被取消,请刷新后再试!");
        QYAssert.isTrue(userId != null && (isInternal || order.getCreateId().equals(userId)), "操作失败，只能取消自己创建的订单！");

        String createDate = DateTimeUtil.formatDate(order.getCreateTime(), "yyyy-MM-dd");
        QYAssert.isTrue(DateTimeUtil.getNowDayDate().equals(createDate), "不能取消非当天订单!");

        Boolean isBigShop = (order.getStallId() != null && order.getStallId() > 0);
        Integer logisticsModel = null;
        if (!isInternal) {
            StoreDurationEntry sd = this.orderMapper.findStoreDurationByStoreId(order.getStoreId().toString());
            if (null != sd) {
                if (StringUtils.isNotBlank(sd.getBeginTime()) && StringUtils.isNotBlank(sd.getEndTime())) {
                    QYAssert.isTrue(DateTimeUtil.compareNewDate(sd.getBeginTime(), sd.getEndTime()), "超出订货时间,无法取消该订单!");
                }
            }

            // 配送检查仓库时间、直送直通检查仓库时间
            List<CommodityResultEntry> commodityList = orderMapper.selectOrderInfoByOrderId(orderId);
            commodityList.forEach(entry -> {
                if (entry.getLogisticsModel().intValue() == IogisticsModelEnums.DISPATCHING.getCode()) {
                    // 配送检查仓库工作开始时间和工作结束时间
                    shoppingCartService.checkStoreOrderTimeByWareHouse(Long.valueOf(entry.getCommodityId()));
                } else {
                    // 直送、直通 检查供应商的截止时间
                    List<SubOrder> subOrders = getSubOrder(orderId);
                    subOrders.forEach(subOrder -> {
                        checkSupplierEndTime(subOrder.getSupplierId(), subOrder.getCreateTime());
                    });
                }
            });
        }

        // 检查物流模式
        checkLogisticsStatus(orderId, order);


        //检测单子是否发货
        Map<Long, Boolean> booleanMap = deliveryOrderClient.checkOrderGeneratePickOrder(Arrays.asList(order.getId()));
        if (booleanMap != null) {
            Boolean ifDelivery = booleanMap.get(order.getId());
            QYAssert.isTrue(!ifDelivery, "该订单已经发货,无法取消!");
        }

        // 1个t_order对应多个多个t_sub_order
        List<Long> subOrderIds = this.subOrderMapper.findSubOrderIdbyOrderId(order.getId());
        if (SpringUtil.isNotEmpty(subOrderIds)) {
            subOrderIds.forEach(subOrderId -> {
                this.subOrderMapper.updateSubOrderStatus(subOrderId);
                deliveryOrderClient.cancelDeliveryOrder(Arrays.asList(subOrderId));

                Map<String, Object> paramMap = new HashMap<String, Object>();
                paramMap.put("orderStatus", ReceiveOrderStatusEnums.Cancel.getCode());
                paramMap.put("subOrderId", subOrderId);
                this.orderMapper.updateReceiveOrderBySubOrderId(paramMap);
            });
        }

//        cancelOrderedQuantity(orderId ,false);
        Map<String, Object> paramMap = new HashMap<String, Object>();
        paramMap.put("orderId", order.getId());
        paramMap.put("orderStatus", 2);
        Integer rowNumber = this.orderMapper.updateOrderStatusByParameter(paramMap);


        // 增加取消日志
        inserOrderHistory(orderId, Long.valueOf(order.getOrderCode()), userId, realName, OperationTypeEnum.ORDER_CANCEL.getCode(), order.getOrderTime(), null, null);

        // 发送取消订单消息给统计查询
        log.info("start send kafa msg: orderId={} ", orderId);

        // 解冻B端库存
        bStockService.warehouseUnfreezeInventory(order.getId(), isBigShop ? ToBTypeEnums.BIGSHOP_SALE.getCode() : ToBTypeEnums.SALE.getCode());

        TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronization() {
            @Override
            public void afterCommit() {
                orderAsyncKafkaService.sendKafkaCancelOrder(order);

                // 更新预付款余额，订单回款记录对账明细
                orderRefund(order);

                // 业务类型：大店销售的门店订单，取消成功需要给物流发送消息
                if(isBigShop){
                    // 发送消息，通知物流
                    TdaOrderSyncTmsIDTO tdaOrderSyncTmsIDTO = TdaOrderSyncTmsIDTO.forOrder(order.getId(), XdaOrderProcessStatusEunm.CANCEL.getCode());
                    tdaOrderService.tdaOrderSyncToTms(tdaOrderSyncTmsIDTO);
                }
            }
        });
        return rowNumber > 0 ? true : false;
    }

    /**
     * 订单回款
     *
     * @param order 订单
     */
    public void orderRefund(Order order) {
        // 更新预付款余额，订单回款记录对账明细
        Boolean preStore = storeRechargeService.isPreStore(order.getStoreId());
        //预付费客户
        if (Objects.equals(preStore, Boolean.TRUE)) {
            String remark = "<--APP回款:" + order.getOrderCode() + " -->";
            int billType = StoreBillTypeEnums.APP_DISCREPANCY.getCode();
            Boolean isXsJmShop = shopService.isJmShop(order.getStoreId());
            if (BooleanUtils.isTrue(isXsJmShop)) {
                remark = "<--门店订货回款：" + order.getOrderCode() + " -->";
                billType = StoreBillTypeEnums.SHOP_ORDER_DEPOSIT.getCode();
            }
            //回款
            StoreRechargeBO rechargeBO = StoreRechargeBO.builder()
                    .orderCode(order.getOrderCode())
                    .tradeCode(order.getOrderCode())
                    .money(order.getOrderAmount().doubleValue())
                    .storeId(order.getStoreId())
                    .tradeTime(new Date())
                    .receiptDate(new Date())
                    .billType(billType)
                    .remark(remark)
                    .userId(order.getStoreId())
                    .build();
            storeRechargeService.storeRecharge(rechargeBO);
            log.info("进行订单回款orderId" + order.getId() + "完成");
        }
    }

    private List<SubOrder> getSubOrder(Long orderId) {
        Example example = new Example(SubOrder.class);
        example.createCriteria().andEqualTo("orderId", orderId);
        List<SubOrder> subOrders = this.subOrderMapper.selectByExample(example);
        QYAssert.isTrue(SpringUtil.isNotEmpty(subOrders), "未查询到该订单");
        return subOrders;
    }

    /**
     * @param operationType 11-订单_新建、12-订单_取消
     */
    @Transactional(rollbackFor = Exception.class)
    public void inserOrderHistory(Long orderId, Long orderCode, Long createId, String createName, Integer operationType, Date orderTime, String oldValue, String newValue) {
        OrderHistory orderHistory = new OrderHistory();
        orderHistory.setEntityType(1);
        orderHistory.setOperationType(operationType);
        orderHistory.setOrderId(orderId);
        orderHistory.setCode(orderCode.toString());
        orderHistory.setCreateId(createId);
        orderHistory.setCreateName(createName);
        orderHistory.setCreateTime(new Date());
        orderHistory.setName("订单日期");
        if (OperationTypeEnum.ORDER_NEW.getCode().equals(operationType)) {
            orderHistory.setNewValue(DateUtil.get4yMd(orderTime));
        } else if (OperationTypeEnum.MODIFY_DELIVER_DATE.getCode().equals(operationType)) {
            orderHistory.setOldValue(oldValue);
            orderHistory.setNewValue(newValue);
        } else if (OperationTypeEnum.PRE_ORDER_CHECK.getCode().equals(operationType)) {
            orderHistory.setNewValue(newValue);
        } else if (OperationTypeEnum.DIRECT_SENDING_BACK_ORDER.getCode().equals(operationType)) {
            orderHistory.setNewValue(newValue);
        }

        log.info("inser order history: orderId=" + orderId);
        this.orderHistoryMapper.insert(orderHistory);
    }

    private void checkLogisticsStatus(Long orderId, Order order) {
        // 审核通过的订单不允许取消
        OrderListRequestVo vo = new OrderListRequestVo();
        vo.setEnterpriseId(78L);
        vo.setStoreId(order.getStoreId());
        vo.setOrderId(orderId);
        PageInfo<OrderListEntry> orderListByPage = findOrderListByPage(vo);
        List<OrderListEntry> orderList = orderListByPage.getList();
        QYAssert.isTrue(SpringUtil.isNotEmpty(orderList), "订单号错误");
        OrderListEntry orderListEntry = orderList.get(0);
        Integer logisticsStatus = orderListEntry.getLogisticsStatus();
        // 0-待收货
        QYAssert.isTrue(logisticsStatus == 0, "订单已审核不允许取消");
    }


    public PageInfo<OrderListEntry> findPreOrderListByPage(OrderListRequestVo vo) {
        if (StringUtil.isNotEmpty(vo.getStartTime())) {
            vo.setStartTime(vo.getStartTime() + " 00:00:00");
        }
        if (StringUtil.isNotEmpty(vo.getEndTime())) {
            vo.setEndTime(vo.getEndTime() + " 23:59:59");
        }

        TokenInfo tokenInfo = FastThreadLocalUtil.getQY();
        if(StallUtils.isStallSubcontractor(tokenInfo.getManagementMode())){
            List<Long> stallIdList = consignmentSupplierService.selectUserStallIdList(tokenInfo.getShopId());
            if(CollectionUtils.isEmpty(stallIdList)){
                return new PageInfo<>();
            }else {
                vo.setStallIdList(stallIdList);
            }
        }

        PageInfo<OrderListEntry> pageData = PageHelper.startPage(vo.getPageNo(), vo.getPageSize()).doSelectPageInfo(() -> {
            this.orderMapper.findPreOrderListByPage(vo);
        });

        List<OrderListEntry> pageDataList = pageData.getList();
        if (SpringUtil.isNotEmpty(pageDataList)) {
            // 先查询所有供应商id，然后再查询对应的供应商
            List<Long> supplierIdList = pageDataList.stream().map(item -> item.getSupplierId()).collect(Collectors.toList());
            QuerySupplierByIdsIDTO idsIDTO = new QuerySupplierByIdsIDTO();
            idsIDTO.setSupplierIds(supplierIdList);
            Map<Long, FullSupplierODTO> supplierIdAndODTOMap = supplierClient.querySupplierByIds(idsIDTO);

            List<Long> stallIdList = pageDataList.stream().filter(p -> p.getStallId() != null && p.getStallId() > 0).collect(Collectors.toList())
                    .stream().map(item -> item.getStallId()).collect(Collectors.toList());
            Map<Long, String> stallIdAndNameMap = consignmentSupplierService.queryStallMapByIds(stallIdList);

            pageData.getList().forEach(i -> {
                FullSupplierODTO supplierODTO = supplierIdAndODTOMap.get(i.getSupplierId());
                if (supplierODTO != null) {
                    i.setSupplierName(supplierODTO.getSupplierName());
                }

                List<OrderItemEntry> orderItemEntryList = this.orderMapper.findPreOrderItemsByOrderId(i.getOrderId());
                if (CollectionUtils.isNotEmpty(orderItemEntryList)) {
                    for (OrderItemEntry itemEntry : orderItemEntryList) {
                        itemEntry.setShares(itemEntry.getCommodityNum().divide(itemEntry.getCommodityPackageSpec(), 0, RoundingMode.UP).longValue());
                    }
                }
                i.setOrderItems(orderItemEntryList);

                // 订单金额取所有订单项的总和
                BigDecimal orderAmount = orderItemEntryList.stream().map(item -> item.getTotalPrice()).reduce(BigDecimal.ZERO, BigDecimal::add).setScale(3, BigDecimal.ROUND_HALF_UP);
                i.setOrderAmount(orderAmount);
                i.setStallName(stallIdAndNameMap.get(i.getStallId()));
            });
        }
        return pageData;
    }


    public PageInfo<OrderListEntry> findOrderListByPage(OrderListRequestVo vo) {
        // 代销商判断
        TokenInfo tokenInfo = FastThreadLocalUtil.getQY();
        vo.setInternal(tokenInfo.getIsInternal());
        vo.setConsignmentId(tokenInfo.getConsignmentId());

        if(StallUtils.isStallSubcontractor(tokenInfo.getManagementMode())){
            List<Long> stallIdList = consignmentSupplierService.selectUserStallIdList(tokenInfo.getShopId());
            if(CollectionUtils.isEmpty(stallIdList)){
                return new PageInfo<>();
            }else {
                vo.setStallIdList(stallIdList);
            }
        }

        if (StringUtils.isBlank(vo.getOrderCode()) && null == vo.getOrderId()) {
            Boolean dateIsEmpty = StringUtils.isBlank(vo.getStartTime()) && StringUtils.isBlank(vo.getEndTime());
            QYAssert.isTrue(!dateIsEmpty, "日期不能为空");
        }
        DictionaryODTO dictionaryODTO = dictionaryClient.getDictionaryByCode("supplierCode");
        SupplierODTO supplier = supplierClient.getSupplierByCode(dictionaryODTO.getOptionValue());
        vo.setOrderType(OrderTypeEnum.STORE_ORDER.getCode());
        PageInfo<OrderListEntry> pageData = PageHelper.startPage(vo.getPageNo(), vo.getPageSize()).doSelectPageInfo(() -> {
            this.orderMapper.findOrderListByPage(vo);
        });


        List<OrderListEntry> pageDataList = pageData.getList();
        if (SpringUtil.isNotEmpty(pageDataList)) {
            // 先查询所有供应商id，然后再查询对应的供应商
            List<Long> supplierIdList = pageDataList.stream().map(item -> item.getSupplierId()).collect(Collectors.toList());
            QuerySupplierByIdsIDTO idsIDTO = new QuerySupplierByIdsIDTO();
            idsIDTO.setSupplierIds(supplierIdList);
            Map<Long, FullSupplierODTO> supplierIdAndODTOMap = supplierClient.querySupplierByIds(idsIDTO);

            List<Long> warehouseIdList = pageDataList.stream().map(item -> item.getWarehouseId()).collect(Collectors.toList());
            Map<Long, WarehouseODto> warehouseIdAndObjectMap = warehouseClient.queryWarehouseListByIds(warehouseIdList);

            List<Long> stallIdList = pageDataList.stream().filter(p -> p.getStallId() != null && p.getStallId() > 0).collect(Collectors.toList())
                    .stream().map(item -> item.getStallId()).collect(Collectors.toList());
            Map<Long, String> stallIdAndNameMap = consignmentSupplierService.queryStallMapByIds(stallIdList);

            pageData.getList().forEach(i -> {
                FullSupplierODTO supplierODTO = supplierIdAndODTOMap.get(i.getSupplierId());
                if (supplierODTO != null) {
                    i.setSupplierName(supplierODTO.getSupplierName());
                }

                if (i.getLogisticsModel() == null) {
                    i.setLogisticsModel(IogisticsModelEnums.DIRECT_SENDING.getCode());
                }
                if (i.getLogisticsModel().intValue() == IogisticsModelEnums.DIRECT_CONNECTION.getCode() || i.getLogisticsModel().intValue() == IogisticsModelEnums.DISPATCHING.getCode()) {
                    i.setSupplierName(supplier != null ? supplier.getSupplierName() : "");
                }

                List<OrderItemEntry> itemList = this.orderMapper.findOrderItemsByOrderId(i.getOrderId());
                if (CollectionUtils.isNotEmpty(itemList)) {
                    for (OrderItemEntry entry : itemList) {
                        entry.setShares(entry.getCommodityNum().divide(entry.getCommodityPackageSpec(), 0, RoundingMode.UP).longValue());
                    }
                }
                i.setOrderItems(itemList);
                if (StringUtils.isNotBlank(i.getDeliveryBatchRemark())) {
                    i.setDeliveryBatchRemark(i.getDeliveryBatchRemark().split("_")[0]);
                }
                if (SpringUtil.isNotEmpty(warehouseIdAndObjectMap)) {
                    WarehouseODto warehouseODto = warehouseIdAndObjectMap.get(i.getWarehouseId());
                    if (warehouseODto != null) {
                        i.setWarehouseName(warehouseODto.getWarehouseName());
                    }
                }
                i.setStallName(stallIdAndNameMap.get(i.getStallId()));
            });
        }
        return pageData;
    }


    /**
     * 从购物车重新查询构建订单信息
     *
     * @throws Throwable
     */
    public void buildShoppingCartToOrder(CreateOrderVo vo) {
        // 页面传的购物车商品数量
        Map<Long, BigDecimal> frontCartMap = new HashMap<>();
        vo.getItems().forEach(item -> {
            frontCartMap.put(item.getShoppingCartItemId(), item.getQuantity());
        });

        QYAssert.isTrue(null != vo.getShoppingCartId() && vo.getShoppingCartId().intValue() > 0, "购物车id不能为空 !!!");
        ShoppingCart shoppingCart = this.shoppingCartMapper.selectByPrimaryKey(vo.getShoppingCartId());
        QYAssert.isTrue(shoppingCart != null, "购物车不能为空，请刷新重试!");

        List<ShoppingCartItem> shoppingCartItemList = this.shoppingCartItemMapper.queryShoppingCartItemList(shoppingCart.getId());
        QYAssert.isTrue(SpringUtil.isNotEmpty(shoppingCartItemList), "购物车不能为空，请刷新重试!");

        // 非代理订货，需要判断不能保存代销商品
        if(!ThreadLocalUtils.getAdmin()){
            List<Long> commodityIdList = shoppingCartItemList.stream().map(item -> item.getCommodityId()).collect(Collectors.toList());
            List<ConsignmentSupplierInfoODTO> consignmentSupplierList = consignmentSupplierService.queryConsignmentSupplierList(Collections.singletonList(shoppingCart.getStoreId()), commodityIdList);
            if(CollectionUtils.isNotEmpty(consignmentSupplierList)){
                List<Long> idsList = consignmentSupplierList.stream().map(item -> item.getCommodityId()).collect(Collectors.toList());
                List<CommodityInfoEntry> commodityEntryList = commodityMapper.findCommodityInfoIds(idsList);
                log.warn("不能订货代销商品, storeId {}  commodityIdList {}", shoppingCart.getStoreId(), idsList);

                StringBuffer sb = new StringBuffer();
                commodityEntryList.forEach(commodity -> {
                    sb.append(commodity.getCommodityCode() + " ");
                });

                QYAssert.isFalse("不能订货代销商品 " + sb);
            }
        }

        OrderRequestVo orderRequestVo = new OrderRequestVo();
        orderRequestVo.setStoreId(shoppingCart.getStoreId().toString());
        orderRequestVo.setOrderTime(null == vo || StringUtils.isBlank(vo.getOrderTime()) ? DateTimeUtil.defaultDeliveryDate() : vo.getOrderTime());
        if (StringUtils.isNotBlank(shoppingCart.getOrderTime())) {
            orderRequestVo.setOrderTime(shoppingCart.getOrderTime());
        }
        orderRequestVo.setEnterpriseId(shoppingCart.getEnterpriseId());
        orderRequestVo.setUserId(vo.getCreateId());
        orderRequestVo.setCreateName(vo.getCreateName());
        orderRequestVo.setLogisticsModel(shoppingCart.getLogisticsModel());
        orderRequestVo.setSupplierId(shoppingCart.getSupplierId());
        orderRequestVo.setWarehouseId(shoppingCart.getWarehouseId());
        orderRequestVo.setDeliveryBatch(null == vo || StringUtils.isBlank(vo.getDeliveryBatch()) ? DeliveryBatchTypeEnum.NO_BATCH.getCode().toString() : vo.getDeliveryBatch());

        List<DeliveryBatchEntry> deliveryBatchList = shoppingCartMapper.findDeliveryBatchByCode(null);
        if (StringUtils.isNotBlank(shoppingCart.getDeliveryBatch())) {
            Map<String, String> deliveryBatchMap = deliveryBatchList.stream().collect(Collectors.toMap(DeliveryBatchEntry::getOptionName, DeliveryBatchEntry::getOptionCode, (key1, key2) -> key2));
            orderRequestVo.setDeliveryBatch(deliveryBatchMap.get(shoppingCart.getDeliveryBatch()));
        }
        orderRequestVo.setDeliveryBatchList(deliveryBatchList);
        orderRequestVo.setDeleveryTimeRange(shoppingCart.getDeleveryTimeRange());
        // 设置代销商户
        orderRequestVo.setConsignmentId(shoppingCart.getConsignmentId());
        orderRequestVo.setStallId(shoppingCart.getStallId());

        // 鲜食加盟的不允许提交直送商品
        if(shopService.isJmShop(shoppingCart.getStoreId())){
            List<String> commodityIdList = shoppingCartItemList.stream().map(item -> item.getCommodityId() + "").collect(Collectors.toList());
            List<MdShopOrderSettingEntry> settingList = mdShopOrderSettingService.queryMdShopOrderSettingListByIds(shoppingCart.getStoreId(), commodityIdList);
            StringBuffer sb = new StringBuffer();
            settingList.forEach(item -> {
                if (item.getLogisticsModel().intValue() == IogisticsModelEnums.DIRECT_SENDING.getCode()) {
                    sb.append(item.getCommodityCode() + " ");
                }
            });
            if (StringUtils.isNotBlank(sb.toString())){
                QYAssert.isFalse("直送商品不允许订货 " + sb);
            }

            // 鲜食加盟的不允许定当天的
            if(DateUtil.getDateFormate(new Date(), "yyyy-MM-dd").equals(orderRequestVo.getOrderTime())){
                QYAssert.isFalse("不允许订当天的货");
            }
        }

        List<OrderItemRequestVo> itemsList = new ArrayList<OrderItemRequestVo>();

        //鲜道提交订单
        //1、部分为0，手动提交成功，剔除为0商品
        //2、整单为0，手动提交时给出原因提示
        shoppingCartItemList.forEach(i -> {
            OrderItemRequestVo item = new OrderItemRequestVo();
            item.setProductId(i.getCommodityId().toString());
            item.setProductNum(frontCartMap.get(i.getId()) != null ? frontCartMap.get(i.getId()) : i.getCommodityNum());
            if (null != item.getProductNum() && item.getProductNum().compareTo(BigDecimal.ZERO) > 0) {
                itemsList.add(item);
            }
        });
        QYAssert.isTrue(CollectionUtils.isNotEmpty(itemsList), "至少1个商品的订货数量不为0!");

        orderRequestVo.setItemsList(itemsList);
        this.createOrder(orderRequestVo, vo);
    }

    /**
     * 创建订单
     *
     * @param orderRequestVo
     * @return
     * @throws Throwable
     */
    public CreateOrderWrapperVo createOrder(OrderRequestVo orderRequestVo, CreateOrderVo vo) {

        // 1. 检查订单商品
        OrderDto orderDto = checkOriginItems(orderRequestVo);

        // 2. 过滤商品
        filters(orderDto);

        QYAssert.isTrue(CollectionUtils.isNotEmpty(orderDto.getItems()), "有效商品不能为空,请删除购物车重新加购!");

        // B端库存依据,拆单、记录数据
        Map<Long, BigDecimal> orderQuantityMap = new HashMap<>();
        orderDto.getItems().forEach(dto ->{
            orderQuantityMap.put(Long.valueOf(dto.getProductId()), dto.getProductNum());
        });
        Map<Long, CommodityInventoryODTO> toBStockMap = bStockService.getbStockMap(DateUtil.parseDate(orderDto.getOrderTime(), "yyyy-MM-dd"), orderQuantityMap);
        orderDto.getItems().forEach(itemDto ->{
            // 默认非预售，只有直送审核通过、大仓配货和团购预售是预售
            itemDto.setPresaleStatus(YesOrNoEnums.NO.getCode());
            CommodityInventoryODTO commodityInventoryODTO = toBStockMap.get(Long.valueOf(itemDto.getProductId()));
            itemDto.setStockType(commodityInventoryODTO != null ? commodityInventoryODTO.getStockType() : StockTypeEnum.UN_LIMIT.getCode());
            if (Objects.nonNull(commodityInventoryODTO)) {
                Integer sourceRatio = commodityInventoryODTO.getSourceRatio();
                itemDto.setSourceRatio(sourceRatio);
                Integer targetRatio = commodityInventoryODTO.getTargetRatio();
                itemDto.setTargetRatio(targetRatio);
                Long targetCommodityId = commodityInventoryODTO.getTargetCommodityId();
                itemDto.setTargetCommodityId(targetCommodityId);
                Integer convertStatus = commodityInventoryODTO.getCanConvert() ? 1 : 0;
                itemDto.setConvertStatus(convertStatus);
                BigDecimal quantity = itemDto.getProductNum();
                // 组合商品目标数量 : 向上取整 ( quantity / source_ratio * target_ratio)
                if (commodityInventoryODTO.getCanConvert()) {
                    BigDecimal targetQuantity = quantity.divide(new BigDecimal(sourceRatio), 0, RoundingMode.UP).multiply(new BigDecimal(targetRatio));
                    itemDto.setTargetQuantity(targetQuantity);
                }
            }
        });

        // 大店不参与配货和赠品
        Boolean isBigShop = (orderRequestVo.getStallId() != null && orderRequestVo.getStallId() > 0);
        // 直送订单应不能参与配货/赠品方案
        if (IogisticsModelEnums.DIRECT_SENDING.getCode() != orderRequestVo.getLogisticsModel() && !isBigShop) {
            Store store = storeMapper.selectByPrimaryKey(orderRequestVo.getStoreId());
            List<DictionaryODTO> dictionaryList = dictionaryClient.listDictionaryByOptionName("订单配货白名单");
            List<String> storeCodeList = dictionaryList.stream().map(item -> item.getOptionCode()).collect(Collectors.toList());
            // 如果当前客户在配货白名单里面，则不配货
            if (CollectionUtils.isEmpty(storeCodeList) || !storeCodeList.contains(store.getStoreCode())) {
                // 3. 配货
                this.unifyProcessDistribution(orderRequestVo.getStoreId(), orderDto,OrderLaunchTypeEnum.SHOP);
                // 过滤系统商品(赠送、配货商品)
                this.filterSystemCommodity(orderRequestVo.getStoreId(), orderDto);

                // 配货处理，过滤掉依据大仓和限量的.(此处处理是因为下面的赠品条件是订单商品和配货商品和)
                List<OrderItemDto> rationItemList = orderDto.getItems().stream().filter(item -> ProductTypeEnums.RATION.getCode().equals(item.getType())).collect(Collectors.toList());
                if(CollectionUtils.isNotEmpty(rationItemList)){
                    giftAndRationDeal(rationItemList, orderDto);
                }
            }

            // 4. 赠送
            this.unifyProcessGifts(orderRequestVo.getStoreId(), orderDto,OrderTypeEnum.STORE_ORDER);
            // 5. 过滤系统商品(赠送、配货商品)
            this.filterSystemCommodity(orderRequestVo.getStoreId(), orderDto);

            // 赠品(数量不够，补全当时可用库存)
            List<OrderItemDto> giftItemList = orderDto.getItems().stream().filter(item -> ProductTypeEnums.GIFT.getCode().equals(item.getType())).collect(Collectors.toList());
            if(CollectionUtils.isNotEmpty(giftItemList)){
                // 不包含配货，只对订单商品和赠品处理(加上订单商品就是避免订单商品和赠送商品一样。判断赠品库存不准确问题)
                List<OrderItemDto> noRationList = orderDto.getItems().stream().filter(item -> !ProductTypeEnums.RATION.getCode().equals(item.getType())).collect(Collectors.toList());
                giftAndRationDeal(noRationList, orderDto);
            }
        }

        // 6. 最后的检查(检查购物车是否为空、商品是否重复、预付款余额是否不足等)
        // 鲜食加盟的判断信用额度
        this.finalCheck(orderDto);

        // 7. 创建订单
        return orderSaveService.saveOneOrder(orderDto, orderRequestVo, vo);
    }

    /**
     * 赠品、配货处理.只配货非限量的商品
     * 1.赋值库存依据
     * 2.赠品数量不足就设置当前可用数量
     * 3.配货商品只保留非限量商品
     */
    public void giftAndRationDeal(List<OrderItemDto> giftAndRationItemList, OrderDto orderDto) {
        List<CommodityInventoryDetailIDTO> orderCommodityList = new ArrayList<>();
        giftAndRationItemList.forEach(dto ->{
            CommodityInventoryDetailIDTO commodityInventoryDetailIDTO = new CommodityInventoryDetailIDTO();
            commodityInventoryDetailIDTO.setCommodityId(Long.valueOf(dto.getProductId()));
            commodityInventoryDetailIDTO.setQuantity(dto.getProductNum());
            commodityInventoryDetailIDTO.setLevel(ProductTypeEnum.NORMAL.getCode());
            if(ProductTypeEnums.GIFT.getCode().equals(dto.getType())){
                commodityInventoryDetailIDTO.setLevel(ProductTypeEnum.GIFT.getCode());
            }
            orderCommodityList.add(commodityInventoryDetailIDTO);
        });

        // orderCommodityList去除重复
        List<CommodityInventoryDetailIDTO> mergedList = mergeOrderItemsByLevel(orderCommodityList);

        Map<String, Object> resultMap = bStockService.checkBStockWithGift(DateUtil.parseDate(orderDto.getOrderTime(), "yyyy-MM-dd"), orderDto.getStoreId(), ThreadLocalUtils.getOrderType(), mergedList, "赠品", orderDto.getUserId());
        Map<Long, CommodityInventoryODTO> giftStockMap = (Map<Long, CommodityInventoryODTO>) resultMap.get("toBStockMap");
        List<BStockShortResponseVO> shortResponseVOS = (List<BStockShortResponseVO>) resultMap.get("shortResponseList");
        Map<String, BigDecimal> shortStockMap;
        if(CollectionUtils.isNotEmpty(shortResponseVOS)){ // 赠品、配货库存存在不足
            shortStockMap = shortResponseVOS.stream().collect(Collectors.toMap(BStockShortResponseVO::getCommodityId,BStockShortResponseVO::getInventoryQuantity,(key1 , key2)-> key2));
        }else {
            shortStockMap = new HashMap<>();
        }

        orderDto.setItems(
            orderDto.getItems().stream().filter(i -> {
                  boolean product = ProductTypeEnums.PRODUCT.getCode().equals(i.getType());
                  boolean gift = ProductTypeEnums.GIFT.getCode().equals(i.getType());
                  boolean ration = ProductTypeEnums.RATION.getCode().equals(i.getType());
                  // 这里只给赠品或者配货赋值预售和库存依据
                  if(!product){
                      // 默认非预售，只有直送审核通过、大仓配货和团购预售是预售
                      i.setPresaleStatus(YesOrNoEnums.NO.getCode());
                      CommodityInventoryODTO commodityInventoryODTO = giftStockMap.get(Long.valueOf(i.getProductId()));
                      i.setStockType(commodityInventoryODTO != null ? commodityInventoryODTO.getStockType() : StockTypeEnum.UN_LIMIT.getCode());
                      if (Objects.nonNull(commodityInventoryODTO)) {
                          Integer sourceRatio = commodityInventoryODTO.getSourceRatio();
                          i.setSourceRatio(sourceRatio);
                          Integer targetRatio = commodityInventoryODTO.getTargetRatio();
                          i.setTargetRatio(targetRatio);
                          Long targetCommodityId = commodityInventoryODTO.getTargetCommodityId();
                          i.setTargetCommodityId(targetCommodityId);
                          Integer convertStatus = commodityInventoryODTO.getCanConvert() ? 1 : 0;
                          i.setConvertStatus(convertStatus);
                          BigDecimal quantity = i.getProductNum();
                          // 组合商品目标数量 : 向上取整 ( quantity / source_ratio * target_ratio)
                          if (commodityInventoryODTO.getCanConvert()) {
                              BigDecimal targetQuantity = quantity.divide(new BigDecimal(sourceRatio), 0, RoundingMode.UP).multiply(new BigDecimal(targetRatio));
                              i.setTargetQuantity(targetQuantity);
                          }
                      }
                  }

                  // 赠品数量不足就赠送当前最大数量
                  BigDecimal giftInventoryQuantity = shortStockMap.get(i.getProductId());
                  if(gift && giftInventoryQuantity != null){
                      if(giftInventoryQuantity.compareTo(BigDecimal.ZERO) <= 0){
                          i.setProductNum(BigDecimal.ZERO);
                      }else {
                          if(giftInventoryQuantity.compareTo(i.getProductNum()) <= 0){
                              i.setProductNum(giftInventoryQuantity);
                          }
                          giftInventoryQuantity = giftInventoryQuantity.subtract(i.getProductNum());
                          shortStockMap.put(i.getProductId(), giftInventoryQuantity);
                      }
                  }
                 return product || (gift &&  i.getProductNum().compareTo(BigDecimal.ZERO) > 0 ) || (ration && StockTypeEnum.UN_LIMIT.getCode() == i.getStockType());
            }).collect(Collectors.toList())
        );
    }

    /**
     * 调用查询接口时候根据 commodityId和 level合计
     * @param orderCommodityList
     * @return
     */
    public List<CommodityInventoryDetailIDTO> mergeOrderItemsByLevel(List<CommodityInventoryDetailIDTO> orderCommodityList) {
        if (CollectionUtils.isEmpty(orderCommodityList)) {
            return Collections.emptyList();
        }
        Map<String, BigDecimal> productMap = orderCommodityList.stream()
                .collect(Collectors.groupingBy(
                        idto -> idto.getCommodityId() + "_" + idto.getLevel(),
                        Collectors.reducing(BigDecimal.ZERO, CommodityInventoryDetailIDTO::getQuantity, BigDecimal::add)
                ));
        List<CommodityInventoryDetailIDTO> mergedList = new ArrayList<>();
        // 构建聚合后的商品列表
        productMap.forEach((key, value) -> {
            String[] parts = key.split("_");
            CommodityInventoryDetailIDTO idto = new CommodityInventoryDetailIDTO();
            idto.setCommodityId(Long.parseLong(parts[0]));
            idto.setLevel(Integer.parseInt(parts[1]));
            idto.setQuantity(value);
            mergedList.add(idto);
        });
        return mergedList;
    }

    /**
     * 对赠送和配货的商品过滤状态和下单时间
     *
     * @param storeId
     * @param orderDto
     */
    public void filterSystemCommodity(String storeId, OrderDto orderDto) {
        // 1. 检查赠送和配货的商品状态和可采状态
        this.processCommodityStatus(orderDto);

        // 2. 过滤订货时间和直送模式商品
        this.filterOrderTimeRange(storeId, orderDto);
    }


    /**
     * 过滤订货时间
     * 移除不满足条件的系统商品（赠品或者配货）
     *
     * @param storeId
     * @param orderDto
     */
    private void filterOrderTimeRange(String storeId, OrderDto orderDto) {
        List<OrderItemDto> giftOrRationCommodityList = orderDto.getItems().stream().filter(item -> ProductTypeEnums.GIFT.getCode().equals(item.getType()) || ProductTypeEnums.RATION.getCode().equals(item.getType())).collect(Collectors.toList());
        if (SpringUtil.isEmpty(giftOrRationCommodityList)) {
            return;
        }

        // 如果所有配置都没找到，直接过滤掉赠送和配货的商品
        List<String> commodityIds = giftOrRationCommodityList.stream().map(item -> item.getProductId()).collect(Collectors.toList());
        List<MdShopOrderSettingEntry> settingList = mdShopOrderSettingService.queryMdShopOrderSettingListByIds(Long.valueOf(storeId), commodityIds);
        if (SpringUtil.isEmpty(settingList)) {
            List<OrderItemDto> originCommodityList = orderDto.getItems().stream().filter(item -> ProductTypeEnums.PRODUCT.getCode().equals(item.getType())).collect(Collectors.toList());
            orderDto.setItems(originCommodityList);
            return;
        }

        // 鲜道进来不校验 赠送商品的截单时间
        List<Long> removeCommodityIdList = new ArrayList<>();
        settingList.forEach(mdShopOrderSettingEntry -> {
            // 配货/赠送 不能是直送物流模式的商品
            if (mdShopOrderSettingEntry.getLogisticsModel().intValue() == IogisticsModelEnums.DIRECT_SENDING.getCode()) {
                removeCommodityIdList.add(mdShopOrderSettingEntry.getCommodityId());
            } else {
                // 配送、直通
                if (mdShopOrderSettingEntry.getLogisticsModel().intValue() == IogisticsModelEnums.DISPATCHING.getCode()) {
                    boolean flag = DateTimeUtil.compareNewDate(mdShopOrderSettingEntry.getDefaultWarehouseBeginTime(), mdShopOrderSettingEntry.getDefaultWarehouseEndTime());
                    if (!flag) {
                        removeCommodityIdList.add(mdShopOrderSettingEntry.getCommodityId());
                    }
                } else {
                    // 直通
                    boolean flag = DateTimeUtil.compareNewDate(mdShopOrderSettingEntry.getDefaultSupplierBeginTime(), mdShopOrderSettingEntry.getDefaultSupplierEndTime());
                    if (!flag) {
                        removeCommodityIdList.add(mdShopOrderSettingEntry.getCommodityId());
                    }
                }
            }
        });

        if (removeCommodityIdList.size() > 0) {
            List<OrderItemDto> newItems = new ArrayList<>();
            for (OrderItemDto item : orderDto.getItems()) {
                // 原始商品或者不在删除列表中的赠送配货商品
                if (ProductTypeEnums.PRODUCT.getCode().equals(item.getType()) || !removeCommodityIdList.contains(Long.valueOf(item.getProductId()))) {
                    newItems.add(item);
                }
            }
            orderDto.setItems(newItems);
        }
    }

    /**
     * 检查订单商品
     *
     * @param orderRequestVo
     * @return
     */
    public OrderDto checkOriginItems(OrderRequestVo orderRequestVo) {
        QYAssert.isTrue(orderRequestVo != null && SpringUtil.isNotEmpty(orderRequestVo.getItemsList()), "无有效商品,请检查!");

        boolean isInternal = ThreadLocalUtils.get();
        if (!isInternal) {
            // 1. 检查当前时间是否在门店订货时间范围内  t_store_duration.begin_time <= now <= t_store_duration.end_time
            this.checkStoreOrderTime(orderRequestVo);

            // 2.1 配送、直通 判断当前时间是否在默认仓库时间范围内  t_md_shop_order_setting.default_warehouse_begin_time <= now <= t_md_shop_order_setting.default_warehouse_end_time
            // 2.2 直送、直通 判断当前时间是否在默认供应商时间范围内 t_md_shop_order_setting.default_supplier_begin_time <= now <= t_md_shop_order_setting.default_supplier_end_time
            List<String> commodityIds = orderRequestVo.getItemsList().stream().map(OrderItemRequestVo::getProductId).collect(Collectors.toList());
            this.checkShopOrderSetting(Long.valueOf(orderRequestVo.getStoreId()), commodityIds, false);
        }

        // 3.1 检查每个商品数据是否为箱规倍数的整倍(t_commodity.sales_box_capacity)
        // 3.2 检查所有速冻商品的总数量是否是30的倍数(t_commodity.commodity_is_quick_freeze)
        OrderDto orderDto = buildOrderDto(orderRequestVo);
        this.checkStoreFrozenProduct(orderDto, true);

        // 4. 更新一下最新的配置: 商品价格、商品限量、商品是否可变价、商品名称
        this.buildProductPrice(orderDto);

        // 5. 检查 客户/产品价格方案中的限量，商品数量不能超出最大限制数量(t_product_price_model_limit.limit_number)
        this.checkCustomerProductLimit(orderDto);

        // 6. 检查商品库存限量(t_commodity_limit.limit_number)
        //this.checkProductLimit(orderDto);

        // 大店需要判断物流的截单时间,直送的不判断
        ddCheckTmsDeliveryBatchTime(orderRequestVo, isInternal, orderDto);
        return orderDto;
    }

    /**
     * 大店需要判断物流的截单时间,直送的不判断
     * 用户有代理权限，则校验客服截单时间
     * @param orderRequestVo
     * @param isInternal
     */
    private void ddCheckTmsDeliveryBatchTime(OrderRequestVo orderRequestVo, boolean isInternal, OrderDto orderDto) {
        if(orderRequestVo.getStallId() != null && orderRequestVo.getStallId() > 0 && IogisticsModelEnums.DIRECT_SENDING.getCode() != orderRequestVo.getLogisticsModel()){
            Store store = storeMapper.selectByPrimaryKey(orderRequestVo.getStoreId());
            List<DeliveryBatchEntry> deliveryBatchList = orderRequestVo.getDeliveryBatchList();
            Map<String, String> deliveryBatchMap = deliveryBatchList.stream().collect(Collectors.toMap(DeliveryBatchEntry::getOptionCode, DeliveryBatchEntry::getOptionName, (key1, key2) -> key2));

            LogisticsBatchStoreSearchIDTO idto = new LogisticsBatchStoreSearchIDTO();
            idto.setStoreId(Long.valueOf(orderRequestVo.getStoreId()));
            idto.setBusinessType(BusinessTypeEnums.BIGSHOP_SALE.getCode());
            idto.setDeliveryBatch(Integer.valueOf(orderRequestVo.getDeliveryBatch()));
            List<LogisticsBatchStoreInfoODTO> logisticsBatchList = logisticsCenterBatchClient.findLogisticsBatchStoreInfoByParams(idto);
            if(CollectionUtils.isNotEmpty(logisticsBatchList)){
                LogisticsBatchStoreInfoODTO odto = logisticsBatchList.get(0);
                orderDto.setLogisticsCenterId(odto.getLogisticsCenterId());
                orderDto.setLogisticsCenterName(odto.getLogisticsCenterName());

                SimpleDateFormat dateFormat= new SimpleDateFormat("HHmm");
                Integer currentTime = Integer.valueOf(dateFormat.format(new Date()));
                Integer storeEndTime;
                if(isInternal) {
                    storeEndTime = Integer.valueOf(odto.getKfEndTime().replace(":", ""));
                }else {
                    storeEndTime = Integer.valueOf(odto.getStoreEndTime().replace(":", ""));
                }

                if(currentTime > storeEndTime){
                    if(OrderTypeEnum.AGENT_ORDER.getCode().equals(ThreadLocalUtils.getOrderType())){
                        Map<Long, String> stallMap = consignmentSupplierService.queryStallMapByIds(Collections.singletonList(orderRequestVo.getStallId()));
                        QYAssert.isFalse(store.getStoreCode() + " " + stallMap.get(orderRequestVo.getStallId()) + (isInternal ? "已超出客服的截单时间" : "已超出物流的截单时间" ));
                    }else {
                        QYAssert.isFalse(isInternal ? "已超出客服的截单时间" : "已超出物流的截单时间");
                    }
                }
            }else {
                QYAssert.isFalse("物流没有配置配送批次");
                //QYAssert.isFalse("物流没有 " + store.getStoreCode() + " " + deliveryBatchMap.get(orderRequestVo.getDeliveryBatch()) +"的批次设置，请联系管理员！");
            }
        }
    }

    public void filters(OrderDto orderDto) {
        // 1. 过滤商品价格为0的商品(c.如果客户价格方案中不包含该商品，那么item的价格为0，此时过滤掉价格为0的商品)
        this.processCommodityPrice(orderDto);

        // 2. 过滤掉商品状态停用的或者不可采的(t_commodity.commodity_state(状态：0-停用,1-启用)、t_xs_shop_commodity_purchase_status.commodity_purchase_status)
        this.processCommodityStatus(orderDto);

        // 3. 过滤t_md_shop_order_setting表中配置信息发生变化的商品(不仅仅是物流模式)
        // 配送、直通: 检查 物流模式+仓库+送货日期范围；直送: 检查物流模式+供应商;
        List<OrderItemDto> items = orderDto.getItems();
        this.processCommodityLogistics(orderDto);

        List<OrderItemDto> filteredItems = orderDto.getItems();
        // 4. 记录门店下单设置表发生改变的商品
        handleShopOrderSettingChange(items, filteredItems);

    }

    public OrderDto buildOrderDto(OrderRequestVo orderRequestVo) {
        OrderDto orderDto = new OrderDto();
        orderDto.setSupplierId(orderRequestVo.getSupplierId());
        orderDto.setWarehouseId(orderRequestVo.getWarehouseId());
        orderDto.setOrderTime(orderRequestVo.getOrderTime());
        SpringUtil.copyProperties(orderRequestVo, orderDto);
        orderDto.setStoreId(Long.valueOf(orderRequestVo.getStoreId()));
        orderDto.setDeliveryBatch(orderRequestVo.getDeliveryBatch());
        orderDto.setDeleveryTimeRange(orderRequestVo.getDeleveryTimeRange());
        orderDto.setItems(new ArrayList<OrderItemDto>());
        orderRequestVo.getItemsList().forEach(i -> {
            orderDto.addItem(new OrderItemDto(i.getProductId(), BigDecimal.ZERO, i.getProductNum(), i.getRemark(), ProductTypeEnums.PRODUCT.getCode()));
        });

        return orderDto;
    }

    private void finalCheck(OrderDto orderDto) {
        QYAssert.isTrue(!orderDto.getItems().isEmpty(), "有效商品不能为空 ...");
        Boolean isXd = ThreadLocalUtils.getXd();
        Boolean isXsJmShop = shopService.isJmShop(orderDto.getStoreId());

        // 鲜道进来不校验
        if (!isXd) {
            // 最终金额
            double orderFinalAmount = orderDto.getItems().stream().mapToDouble(item -> item.amount().doubleValue()).sum();
            QYAssert.isTrue(orderFinalAmount > 0, "订单金额必须大于0...");

            if(!isXsJmShop){
                // 原始金额
                double orderOriginTotalAmount = orderDto.getItems().stream().filter(item -> ProductTypeEnums.PRODUCT.getCode().equals(item.getType())).mapToDouble(item -> item.amount().doubleValue()).sum();
                QYAssert.isTrue(storeService.matchBillCondition(orderDto.getStoreId(), new BigDecimal(orderOriginTotalAmount)), "预付款余额不足,无法下单!");
            }
        }

        // 鲜食加盟的判断信用额度
        if(isXsJmShop){
            Shop shop = shopService.getShopByStoreId(orderDto.getStoreId());

            Example ex = new Example(StoreSettlement.class);
            ex.createCriteria().andEqualTo("storeId", orderDto.getStoreId());
            List<StoreSettlement> ssList = storeSettlementMapper.selectByExample(ex);
            QYAssert.isTrue(CollectionUtils.isNotEmpty(ssList), "账户不存在 storeId:" + orderDto.getStoreId());
            StoreSettlement ss = ssList.get(0);
            Double collPrice = (ss.getCollectPrice() == null ? 0 : ss.getCollectPrice());

            String yMd = DateUtil.get4yMd(new Date());
            Integer countOrder = orderMapper.countCurrentDayOrder(orderDto.getStoreId(), yMd + " 00:00:00", yMd + " 23:59:59");
            if(Integer.valueOf(0).equals(countOrder)){
                // 当天首次下单
                QYAssert.isTrue(collPrice >= 0, "您尚未缴纳应付货款，暂时无法订货，请充值支付货款 " + new BigDecimal(collPrice + "").negate().stripTrailingZeros().toPlainString());
            }

            // 查询信用额度和预留额度
            CreditLimitVo creditLimitVo = new CreditLimitVo();
            creditLimitVo.setShopIdList(Arrays.asList(shop.getId()));
            List<CreditLimitInfoODTO> creditLimitInfoList = creditLimitClient.selectCreditLimitByShopIdList(creditLimitVo);
            BigDecimal creditAmount = BigDecimal.ZERO;
            if(CollectionUtils.isNotEmpty(creditLimitInfoList)){
                CreditLimitInfoODTO creditLimitInfoODTO = creditLimitInfoList.get(0);
                creditAmount = creditLimitInfoODTO.getCreditAmount().subtract(creditLimitInfoODTO.getRemainAmount());
            }

            BigDecimal leftAmount = creditAmount.add(new BigDecimal(collPrice + ""));
            QYAssert.isTrue(orderDto.amount().compareTo(leftAmount) <= 0, "您的预付货款不足，应充值支付货款" + (orderDto.amount().subtract(leftAmount)).stripTrailingZeros().toPlainString() + "元");

        }

        List<OrderItemDto> items = orderDto.getItems().stream().filter(i -> {
            return i.getType() == 1;
        }).collect(Collectors.toList());//过滤正常商品
        items.stream().collect(Collectors.groupingBy(OrderItemDto::getProductId, Collectors.counting())).entrySet().forEach(c -> {
            QYAssert.isTrue(c.getValue().intValue() == 1, "商品存在重复,请合并后再试!");
        });
    }


    /**
     * 记录门店下单设置表发生改变的商品
     *
     * @param items         原来的items
     * @param filteredItems 过滤后的items
     */
    private void handleShopOrderSettingChange(List<OrderItemDto> items, List<OrderItemDto> filteredItems) {
        List<FilterTipVo> filterTips = ThreadLocalUtils.getFilterTips();
        items.stream().forEach(item -> {
            boolean flag = filteredItems.stream().anyMatch(item2 -> item2.getProductId().equals(item.getProductId()));
            // 如果找不到，说明该商品被过滤掉了，需要记录下来
            if (!flag) {
                Long commodityId = Long.parseLong(item.getProductId());
                filterTips.add(new FilterTipVo(commodityId, null, item.getProductName(), "", item.getProductNum(), FilterTipEnum.SETTING_CHANGE.getMsg(), FilterTipEnum.SETTING_CHANGE.getType()));
            }
        });

        ThreadLocalUtils.setFilterTips(filterTips);
    }


    public List<ProductLogisticsEntry> findMdShopOrderSetting(Long storeId, List<String> commodityIds) {
        List<MdShopOrderSettingEntry> shopOrderSettingODTOs = mdShopOrderSettingService.queryMdShopOrderSettingListByIds(storeId, commodityIds);
        QYAssert.isTrue(SpringUtil.isNotEmpty(shopOrderSettingODTOs), "供应商配置信息有误");

        List<ProductLogisticsEntry> result = BeanCloneUtils.copyTo(shopOrderSettingODTOs, ProductLogisticsEntry.class);
        return result;
    }

    @Transactional(rollbackFor = Exception.class)
    public PreOrder savePreOrder(OrderDto orderDto) {
        PreOrder order = new PreOrder();
        Date date = new Date();
        order.setEnterpriseId(orderDto.getEnterpriseId());
        order.setSupplierId(orderDto.getSupplierId());
        order.setCreateId(orderDto.getUserId());
        order.setUpdateId(orderDto.getUserId());
        order.setCreateTime(date);
        order.setUpdateTime(date);
        order.setLogisticsModel(0);
        order.setOrderStatus(1);
        order.setReceiveStatus(0);
        order.setTotalPrice(orderDto.amount());
        order.setStoreId(Long.valueOf(orderDto.getStoreId()));

        Calendar c = Calendar.getInstance();
        c.add(Calendar.DAY_OF_YEAR, 1);
        if (OrderModeType.DRIECT_SENDING_BACKORDER.getCode().equals(orderDto.getModeType())) {
            order.setOrderTime(DateTimeUtil.parse(orderDto.getOrderTime(), "yyyy-MM-dd"));
            order.setReceiverId(orderDto.getUserId());
            order.setReceiverTime(new Date());
        } else {
            order.setOrderTime(DateTimeUtil.parse(DateTimeUtil.defaultDeliveryDate(), "yyyy-MM-dd"));
        }

        order.setOrderCode(IDGenerator.newOrderCode());
        // 根据storeId获取所属公司ID
        order.setCompanyId(storeMapper.getStoreCompanyByStoreId(order.getStoreId()).getCompanyId());
        order.setConsignmentId(orderDto.getConsignmentId());
        order.setStallId(orderDto.getStallId() != null ? orderDto.getStallId() : -1);
        this.preOrderMapper.insert(order);

        orderDto.getItems().forEach(i -> {
            PreOrderItem item = new PreOrderItem();
            item.setPreorderId(order.getId());
            item.setCommodityId(Long.valueOf(i.getProductId()));
            item.setPrice(i.getPrice());
            item.setRequireQuantity(i.getProductNum());
            item.setTotalPrice(i.getPrice().multiply(i.getProductNum()).setScale(2, BigDecimal.ROUND_HALF_UP));
            item.setStatus(0);
            item.setIsAdd(false);
            item.setCreateId(orderDto.getUserId());
            item.setUpdateId(orderDto.getUserId());
            item.setCreateTime(date);
            item.setUpdateTime(date);
            item.setType(i.getType());
            this.preOrderItemMapper.insert(item);
        });
        return order;
    }


    /**
     * 构建OrderDto对象(包括原始OrderDto和赠送、配货拆单出来的OrderDto)
     *不管原订单还是赠品、配货商品都需要根据stockType拆单
     * @param orderDto
     * @param
     * @return
     */
    public List<OrderDto> buildOrderDtos(OrderDto orderDto) {
        List<OrderItemDto> giftAndRationItemList = orderDto.getItems().stream().filter(item -> ProductTypeEnums.GIFT.getCode().equals(item.getType()) || ProductTypeEnums.RATION.getCode().equals(item.getType())).collect(Collectors.toList());

        List<OrderDto> orderDtoList = new ArrayList<>();
        List<OrderItemDto> originItems = orderDto.getItems().stream().filter(item -> ProductTypeEnums.PRODUCT.getCode().equals(item.getType())).collect(Collectors.toList());
        Map<Integer, List<OrderItemDto>> stockTypeMap = originItems.stream().collect(Collectors.groupingBy(OrderItemDto::getStockType));
        // 说明原始原始商品有多个库存类型，需要拆单
        if(stockTypeMap.size() > 1 ){
            stockTypeMap.entrySet().forEach(entry -> {
                List stockTypeOriginItems = entry.getValue();
                OrderDto newOderDto = BeanCloneUtils.copyTo(orderDto, OrderDto.class);
                newOderDto.setItems(stockTypeOriginItems);
                orderDtoList.add(newOderDto);
            });
        }else {
            orderDto.setItems(originItems);
            orderDtoList.add(orderDto);
        }

        if (SpringUtil.isNotEmpty(giftAndRationItemList)) {
            List<OrderDto> giftAndRationOrderDtoList = giftAndRationSplitOrder(orderDto, giftAndRationItemList);
            // 如果logisticsModel + warehouseId + stockType + supplierId 都一样，则把giftAndRationOrderDtoList中的items 加到orderDtoList的items下
            mergeGiftAndRationOrders(orderDtoList, giftAndRationOrderDtoList);
        }

        return orderDtoList;
    }
    private void mergeGiftAndRationOrders(List<OrderDto> orderDtoList, List<OrderDto> giftAndRationOrderDtoList) {
        if (CollectionUtils.isEmpty(giftAndRationOrderDtoList)){
            return;
        }
        Map<String, OrderDto> keyToOrderMap = orderDtoList.stream().collect(Collectors.toMap(this::generateOrderKey, Function.identity()));

        for (OrderDto giftAndRationOrder : giftAndRationOrderDtoList) {
            String key = generateOrderKey(giftAndRationOrder);
            OrderDto existingOrder = keyToOrderMap.get(key);
            if (Objects.nonNull(existingOrder)) {
                existingOrder.getItems().addAll(giftAndRationOrder.getItems());
            } else {
                orderDtoList.add(giftAndRationOrder);
            }
        }
    }

    private String generateOrderKey(OrderDto orderDto) {
        return orderDto.getLogisticsModel() + "_" + orderDto.getWarehouseId() + "_" + orderDto.getItems().get(0).getStockType() + "_" + orderDto.getSupplierId() + "_" + orderDto.getDeleveryTimeRange();
    }

    private List<OrderDto> giftAndRationSplitOrder(OrderDto orderDto, List<OrderItemDto> giftAndRationItemList) {
        Map<String, Integer> commodityStockTypeMap = new HashMap<>();
        giftAndRationItemList.forEach(item -> {
            commodityStockTypeMap.put(item.getProductId(), item.getStockType());
        });

        // 1. 查询门店订货配置
        List<String> commodityIds = giftAndRationItemList.stream().map(item -> item.getProductId()).distinct().collect(Collectors.toList());
        List<ProductLogisticsEntry> shopOrderSettingList = this.findMdShopOrderSetting(orderDto.getStoreId(), commodityIds);
        Map<String, ProductLogisticsEntry> commodityId2ObjMap = shopOrderSettingList.stream().collect(Collectors.toMap(ProductLogisticsEntry::getProductId, Function.identity()));

        // 2. 先对门店订单配置进行分组，门店订单配置分组了就相当于对商品进行分组了
        // 只有配送和直通才参与赠送和配货
        // 配送: storeId + logisticsModel + warehouseId + deleveryTimeRange
        // 直通: storeId + logisticsModel + warehouseId + deleveryTimeRange + supplierId
        String splitCode = "_";
        Map<String, List<ProductLogisticsEntry>> groupSettingMap = shopOrderSettingList.stream().collect(Collectors.groupingBy(item -> {
            Integer stockType = commodityStockTypeMap.get(item.getProductId());
            String groupByField = item.getLogisticsModel() + splitCode + item.getWarehouseId() + splitCode + item.getDeleveryTimeRange() + splitCode + stockType;
            if (item.getLogisticsModel() == IogisticsModelEnums.DISPATCHING.getCode()) {
                return groupByField;
            } else if (item.getLogisticsModel() == IogisticsModelEnums.DIRECT_CONNECTION.getCode()) {
                return groupByField + splitCode + item.getSupplierId();
            } else {
                return item.getLogisticsModel() + splitCode + item.getSupplierId();
            }
        }));

        // 3. 根据门店订单配置分组来对赠送和配货的商品进行分组
        List<List<OrderItemDto>> groupCommodityList = new ArrayList<>();
        // 这里赠送和配货可能会是同一个商品
        Map<String, List<OrderItemDto>> id2CommoditysMap = giftAndRationItemList.stream().collect(Collectors.groupingBy(item -> item.getProductId()));

        groupSettingMap.forEach((key, settingList) -> {
            List<OrderItemDto> commodityList = new ArrayList<>();
            settingList.forEach(setting -> {
                List<OrderItemDto> orderItemDtos = id2CommoditysMap.get(setting.getProductId());
                commodityList.addAll(orderItemDtos);
            });

            groupCommodityList.add(commodityList);
        });

        // 4. 构建OrderDto
        List<OrderDto> orderDtoList = new ArrayList<>();
        // 循环每个分组的商品构建对应的OrderDto
        groupCommodityList.forEach(orderItemDtos -> {
            OrderDto item = new OrderDto();
            item.setUserId(orderDto.getUserId());
            item.setEnterpriseId(orderDto.getEnterpriseId());
            item.setStoreId(orderDto.getStoreId());
            item.setOrderTime(orderDto.getOrderTime());
            item.setPrintType(orderDto.getPrintType());
            item.setPrintNum(orderDto.getPrintNum());
            item.setModeType(orderDto.getModeType());
            item.setDeliveryBatch(orderDto.getDeliveryBatch());
            item.setDeleveryTimeRange(orderDto.getDeleveryTimeRange());
            item.setItems(orderItemDtos);

            ProductLogisticsEntry setting = commodityId2ObjMap.get(orderItemDtos.get(0).getProductId());
            item.setLogisticsModel(setting.getLogisticsModel());
            item.setSupplierId(Long.valueOf(setting.getSupplierId()));
            item.setWarehouseId(Long.valueOf(setting.getWarehouseId()));
            orderDtoList.add(item);
        });
        return orderDtoList;
    }

    //生成子单
    @Transactional(rollbackFor = Exception.class)
    public Long createSubOrderAndItem(OrderDto orderDto, Long orderId, String orderCode, Boolean isXsJmShop, Map<Long, List<AssociationOrderODTO>> associationOrderMap) {
        SubOrderAndItemVo subOrderAndItemVo = new SubOrderAndItemVo();
        subOrderAndItemVo.setCreateId(orderDto.getUserId());
        subOrderAndItemVo.setEnterpriseId(orderDto.getEnterpriseId());
        subOrderAndItemVo.setLogisticsModel(orderDto.getLogisticsModel());
        subOrderAndItemVo.setOrderCode(orderCode);
        subOrderAndItemVo.setOrderId(orderId);
        subOrderAndItemVo.setStoreId(orderDto.getStoreId());
        if (!StringUtils.isBlank(orderDto.getOrderTime())) {
            subOrderAndItemVo.setOrderTime(DateUtil.parseDate(orderDto.getOrderTime() + " 00:00:00", "yyyy-MM-dd HH:mm:ss"));
        } else {
            Calendar c = Calendar.getInstance();
            c.add(Calendar.DAY_OF_YEAR, 1);
            subOrderAndItemVo.setOrderTime(c.getTime());
        }
        subOrderAndItemVo.setPurchaseOrderId(null);
        subOrderAndItemVo.setSupplierId(orderDto.getSupplierId());
        subOrderAndItemVo.setWarehouseId(orderDto.getWarehouseId());
        subOrderAndItemVo.setVarietyTotal(orderDto.getItems() == null ? BigDecimal.ZERO : BigDecimal.valueOf(orderDto.getItems().size()));
        List<OrderItemDto> orderItemDtos = orderDto.getItems();
        QYAssert.isTrue(null != orderItemDtos && !orderItemDtos.isEmpty(), "无有效商品不能下单");

        BigDecimal totalPriceForSubOrder = BigDecimal.ZERO;
        List<SubOrderAndItemVo.SubOrderItemVo> subOrderItemVos = new ArrayList<SubOrderAndItemVo.SubOrderItemVo>();
        for (OrderItemDto dto : orderItemDtos) {
            SubOrderAndItemVo.SubOrderItemVo vo = new SubOrderAndItemVo.SubOrderItemVo();
            vo.setCommodityId(Long.valueOf(dto.getProductId()));
            vo.setPrice(dto.getPrice());
            vo.setQuantity(dto.getProductNum());
            BigDecimal totalPriceForSubOrderItem = dto.amount();
            vo.setTotalPrice(totalPriceForSubOrderItem);
            vo.setChangePriceStatus(dto.getChangePriceStatus());
            totalPriceForSubOrder = totalPriceForSubOrder.add(totalPriceForSubOrderItem).setScale(2, BigDecimal.ROUND_HALF_UP);
            vo.setSourceRatio(dto.getSourceRatio());
            vo.setTargetRatio(dto.getTargetRatio());
            vo.setTargetCommodityId(dto.getTargetCommodityId());
            vo.setTargetQuantity(dto.getTargetQuantity());
            vo.setConvertStatus(dto.getConvertStatus());
            vo.setType(dto.getType());
            subOrderItemVos.add(vo);
        }
        subOrderAndItemVo.setItems(subOrderItemVos);
        subOrderAndItemVo.setTotalPrice(totalPriceForSubOrder);
        subOrderAndItemVo.setPresaleStatus(orderItemDtos.get(0).getPresaleStatus() != null ? orderItemDtos.get(0).getPresaleStatus() : YesOrNoEnums.NO.getCode());
        subOrderAndItemVo.setStockType(orderItemDtos.get(0).getStockType() != null ? orderItemDtos.get(0).getStockType() : StockTypeEnum.UN_LIMIT.getCode());
        // 如果是预售，则库存依据为空
        if(YesOrNoEnums.YES.getCode().equals(subOrderAndItemVo.getPresaleStatus())){
            subOrderAndItemVo.setStockType(null);
        }
        return subOrderService.createSubOrderAndItem(subOrderAndItemVo, isXsJmShop, associationOrderMap);
    }


    //生成收货单
    @Transactional(rollbackFor = Exception.class)
    public void newReceiveOrder(ShopReceiveOrderVo shopReceiveOrderVo) {
        shopReceiveService.newReceiveOrder(shopReceiveOrderVo);
    }

    /**
     * @param isPreOrder 预订单审核通过保存实发数量，实发金额
     */
    @Transactional(rollbackFor = Exception.class)
    public Order saveOrder(OrderDto orderDto, Boolean isPreOrder, Boolean isXsJmShop) {
        Order order = new Order();
        Date date = new Date();
        order.setEnterpriseId(orderDto.getEnterpriseId());
        order.setCreateId(orderDto.getUserId());
        order.setUpdateId(orderDto.getUserId());
        order.setCreateTime(date);
        order.setUpdateTime(date);
        order.setOrderAmount(orderDto.amount());
        order.setFinalAmount(orderDto.amount());
        order.setStoreId(Long.valueOf(orderDto.getStoreId()));
        order.setOrderTime(DateTimeUtil.parse(orderDto.getOrderTime(), "yyyy-MM-dd"));
        order.setOrderCode(IDGenerator.newOrderCode());
        order.setEnterpriseId(orderDto.getEnterpriseId());
        order.setPrintNum(orderDto.getPrintNum());
        order.setOrderType(ThreadLocalUtils.getOrderType());

        order.setFreightAmount(BigDecimal.ZERO); // 配送费默认为0

        if (OrderModeType.DRIECT_SENDING_BACKORDER.getCode().equals(orderDto.getModeType())) {
            order.setModeType(OrderModeType.DRIECT_SENDING_BACKORDER.getCode());
        } else {
            order.setModeType(OrderModeType.ORDER.getCode());
        }

        order.setPrintType(OrderPrintTypeEnum.getEnumByCode(orderDto.getPrintType()));
        order.setOrderRemark(orderDto.getOrderRemark());
        order.setOrderStatus(0);
        if (!StringUtils.isBlank(orderDto.getDeliveryBatch())) {
            order.setDeliveryBatch(Integer.parseInt(orderDto.getDeliveryBatch()));
            List<DeliveryBatchEntry> list = shoppingCartMapper.findDeliveryBatchByCode(orderDto.getDeliveryBatch());
            if (null != list && !list.isEmpty()) {
                DeliveryBatchEntry one = list.get(0);
                order.setDeliveryBatchRemark(one.getOptionName() + "_" + one.getMemo());
            } else {
                order.setDeliveryBatchRemark(orderDto.getDeliveryBatch() + "_");
            }
        }

        // 团购订单默认不可变价
        // 鲜食加盟默认不可变价(钱大妈)
        boolean isGroupOrder = ThreadLocalUtils.getGroupOrder();
        if (isGroupOrder) {
            order.setChangePriceStatus(YesOrNoEnums.NO.getCode());
        } else {
            // 设置订单价格是否可变
            Integer changePriceStatus = this.changePriceStatus(orderDto);
            order.setChangePriceStatus(changePriceStatus);
        }
        order.setSyncStatus(YesOrNoEnums.NO.getCode());
        order.setSettleStatus(0);

        // 根据storeId获取所属公司ID
        StoreCompanyVo storeCompanyByStoreId = storeMapper.getStoreCompanyByStoreId(order.getStoreId());
        order.setCompanyId(storeCompanyByStoreId.getCompanyId());
        order.setStoreTypeId(storeCompanyByStoreId.getStoreTypeId());
        order.setConsignmentId((orderDto.getConsignmentId() != null && orderDto.getConsignmentId() > 0) ? orderDto.getConsignmentId() : -1); // 设置代销商户
        order.setPresaleStatus(orderDto.getItems().get(0).getPresaleStatus() != null ? orderDto.getItems().get(0).getPresaleStatus() : YesOrNoEnums.NO.getCode());
        order.setTotalAmount(orderDto.originalAmount());
        order.setPromotionAmount(orderDto.promotionAmount());
        order.setSettleOrderTime(order.getOrderTime());
        order.setStallId(orderDto.getStallId() != null ? orderDto.getStallId() : -1);
        if(order.getStallId() > 0){
            order.setBusinessType(BusinessTypeEnums.BIGSHOP_SALE.getCode());
            order.setLogisticsCenterId(orderDto.getLogisticsCenterId());
            order.setLogisticsCenter(orderDto.getLogisticsCenterName());
            // 大店下单，走物流。默认待拣货状态
            order.setProcessStatus(XdaOrderProcessStatusEunm.WAITING_PICK.getCode());
        }

        // 设置是否直送
        order.setDirectStatus(orderDto.getDirectStatus() != null ? orderDto.getDirectStatus() : 0);
        this.orderMapper.insert(order);

        List<AssociationOrderODTO> associationOrderList = new ArrayList<>();
        orderDto.getItems().forEach(i -> {
            OrderList orderList = new OrderList();
            orderList.setCommodityId(Long.valueOf(i.getProductId()));
            orderList.setCommodityNum(i.getProductNum());
            orderList.setType(i.getType());
            orderList.setRemark(i.getRemark());
            if (ProductTypeEnums.PRODUCT.getCode().equals(i.getType())) {
                orderList.setRemark("订单商品");
            }
            orderList.setCommodityPrice(i.getPrice());
            orderList.setTotalPrice(i.amount());
            orderList.setOrderId(order.getId());
            // 保存赠送模式id和配货模式id
            orderList.setGiftModelId(i.getGiftModelId());
            orderList.setPromotionId(i.getPromotionId());
            orderList.setPresaleStatus(i.getPresaleStatus() != null ? i.getPresaleStatus() : YesOrNoEnums.NO.getCode());
            if(isPreOrder){
                orderList.setRealQuantity(orderList.getCommodityNum());
                orderList.setRealTotalPrice(orderList.getTotalPrice());
            }
            orderList.setOriginalPrice(i.getOriginalPrice());
            orderList.setOriginalTotalPrice(i.originalAmount());
            this.orderListMapper.insert(orderList);

            AssociationOrderODTO associationOrderODTO = AssociationOrderODTO.builder()
                    .commodityId(orderList.getCommodityId())
                    .type(orderList.getType())
                    .commodityPrice(orderList.getCommodityPrice())
                    .orderListId(orderList.getId())
                    .orderDone(false)
                    .build();
            associationOrderList.add(associationOrderODTO);
        });
        this.crateOrderMirror(order);

        // 更新预付款余额，订单扣款记录对账明细
        orderDeductPayment(isXsJmShop, order);

        Map<Long, List<AssociationOrderODTO>> associationOrderMap = associationOrderList.stream().collect(Collectors.groupingBy(AssociationOrderODTO::getCommodityId));
        order.setAssociationOrderMap(associationOrderMap);
        return order;
    }

    /**
     * 订单扣款
     *
     * @param isXsJmShop 是否是鲜食加盟店
     * @param order      订单
     */
    public void orderDeductPayment(Boolean isXsJmShop, Order order) {

        Boolean preStore = storeRechargeService.isPreStore(order.getStoreId());
        //预付费客户
        if (Objects.equals(preStore, Boolean.TRUE)) {
            String remark = "<--门店扣款:" + order.getOrderCode() + " -->";
            int billType = StoreBillTypeEnums.SHOP_DEDUCTION.getCode();
            if (BooleanUtils.isTrue(isXsJmShop)) {
                remark = "<--门店订货扣款：" + order.getOrderCode() + " -->";
                billType = StoreBillTypeEnums.SHOP_ORDER_DEDUCTION.getCode();
            }
            //扣款
            StoreDeductionBO deductionBO = StoreDeductionBO.builder()
                    .orderCode(order.getOrderCode())
                    .orderId(order.getId())
                    .orderAmount(order.getOrderAmount())
                    .storeId(order.getStoreId())
                    .tradeCode(order.getOrderCode())
                    .tradeTime(new Date())
                    .orderTime(order.getOrderTime())
                    .billType(billType)
                    .remark(remark)
                    .userId(order.getStoreId())
                    .build();
            storeRechargeService.storeDeduction(deductionBO);
            log.info("进行订单扣款orderId" + order.getId() + "完成");
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public void crateOrderMirror(Order order) {

        StoreEntry store = orderMapper.findStoreById(String.valueOf(order.getStoreId()));
        OrderMirror orderMirror = new OrderMirror();
        orderMirror.setOrderId(order.getId());
        orderMirror.setOrderCode(order.getOrderCode());

        if (null != store) {
            EmployeeEntry employee = null;
            if (null != store.getDeliverymanId()) {
                employee = orderMapper.findEmployeeById(store.getDeliverymanId());
                orderMirror.setDeliveryManId(employee.getId());
                orderMirror.setDeliveryManName(employee.getEmployeeName());
            }
            if (null != store.getSalesmanId()) {
                employee = orderMapper.findEmployeeById(store.getSalesmanId());
                orderMirror.setSalesmanId(employee.getId());
                orderMirror.setSalesmanName(employee.getEmployeeName());
            }
            if (null != store.getSupervisorId()) {
                employee = orderMapper.findEmployeeById(store.getSupervisorId());
                orderMirror.setSupervisionId(employee.getId());
                orderMirror.setSupervisionName(employee.getEmployeeName());
            }
            if (null != store.getRegionManagerId()) {
                employee = orderMapper.findEmployeeById(store.getRegionManagerId());
                orderMirror.setRegionalManagerId(employee.getId());
                orderMirror.setRegionalManagerName(employee.getEmployeeName());
            }
            if (null != store.getOfficeDirectorId()) {
                employee = orderMapper.findEmployeeById(store.getOfficeDirectorId());
                orderMirror.setDirectorId(employee.getId());
                orderMirror.setDirectorName(employee.getEmployeeName());
            }
        }
        orderMirrorMapper.insert(orderMirror);
    }

    /**
     * @param isPreOrder 预订单审核通过保存实发数量，实发金额
     */
    @Transactional(rollbackFor = Exception.class)
    public Order saveGiftOrder(OrderDto orderDto, Long orderId, Boolean isPreOrder) {

        Order kafkaOrder = this.orderMapper.selectByPrimaryKey(orderId);
        List<OrderList> orderList = new ArrayList<OrderList>();

        orderDto.getItems().forEach(i -> {
            OrderList ol = new OrderList();
            OrderListGift gift = new OrderListGift();

            gift.setCommodityId(Long.valueOf(i.getProductId()));
            gift.setCommodityNum(i.getProductNum());
            gift.setType(i.getType());
            gift.setRemark(i.getRemark());
            gift.setCommodityPrice(i.getPrice());
            gift.setOrderId(orderId);
            gift.setTotalPrice(i.amount().setScale(2, BigDecimal.ROUND_HALF_UP));

            SpringUtil.copyProperties(gift, ol);
            // order commodity sys --> start
            Commodity commodity = commodityMapper.selectByPrimaryKey(ol.getCommodityId());
            if (commodity != null) {
                ol.setCommodityCode(commodity.getCommodityCode());
                ol.setCommodityName(commodity.getCommodityName());
                ol.setCommoditySpec(commodity.getCommoditySpec());
            }
            // order commodity sys --> end
            if(isPreOrder){
                gift.setRealQuantity(gift.getCommodityNum());
                gift.setRealTotalPrice(gift.getTotalPrice());
            }
            gift.setOriginalPrice(i.getOriginalPrice());
            gift.setOriginalTotalPrice(i.originalAmount());
            this.orderListGiftMapper.insert(gift);
            // 重要代码,一定不要删除
            ol.setId(gift.getId());
            orderList.add(ol);
        });
        kafkaOrder.setOrderAmount(orderDto.amount());
        kafkaOrder.setOrderList(orderList);
        return kafkaOrder;
    }

    /**
     * 检查门店订货时间
     *
     * @param orderRequestVo
     */
    public void checkStoreOrderTime(OrderRequestVo orderRequestVo) {
        StoreDurationEntry sd = this.orderMapper.findStoreDurationByStoreId(orderRequestVo.getStoreId());
        if (null != sd) {
            if (StringUtils.isNotBlank(sd.getBeginTime()) && StringUtils.isNotBlank(sd.getEndTime())) {
                QYAssert.isTrue(DateTimeUtil.compareNewDate(sd.getBeginTime(), sd.getEndTime()), "超出订货时间,无法操作!");
            }
        }
    }

    /*
     * 检查门店订货通用设置（物流模式、供应商、时间、仓库、时间）
     */
    public List<MdShopOrderSettingEntry> checkShopOrderSetting(Long StoreId, List<String> commodityIds, boolean isInternal) {
        List<MdShopOrderSettingEntry> settingList = mdShopOrderSettingService.queryMdShopOrderSettingListByIds(StoreId, commodityIds);
        QYAssert.isTrue(SpringUtil.isNotEmpty(settingList), "门店订货配置信息有误");
        settingList.forEach(mdShopOrderSettingEntry -> {
            QYAssert.isTrue(null != mdShopOrderSettingEntry && null != mdShopOrderSettingEntry.getLogisticsModel(), "门店订货通用设置,商品物流模式未配置");
            if (mdShopOrderSettingEntry.getLogisticsModel().intValue() == IogisticsModelEnums.DISPATCHING.getCode() ||
                    mdShopOrderSettingEntry.getLogisticsModel().intValue() == IogisticsModelEnums.DIRECT_CONNECTION.getCode()) {
                // 仓库为空,调用异步方法更新订货通用设置仓库信息
                if (mdShopOrderSettingEntry.getWarehouseId() == null) {
                    mdShopOrderSettingService.updateShopOrderSettingWarehouse(mdShopOrderSettingEntry.getCommodityId());
                }
                //配送直通必须配置默认仓库;
                QYAssert.isTrue(null != mdShopOrderSettingEntry.getWarehouseId(), "门店订货通用设置,默认仓库未设置");
            }
            if (mdShopOrderSettingEntry.getLogisticsModel().intValue() == IogisticsModelEnums.DIRECT_SENDING.getCode() ||
                    mdShopOrderSettingEntry.getLogisticsModel().intValue() == IogisticsModelEnums.DIRECT_CONNECTION.getCode()) {
                // 供应商为空,调用异步方法更新订货通用设置供应商信息
                if (mdShopOrderSettingEntry.getSupplierId() == null) {
                    mdShopOrderSettingService.updateShopOrderSettingSupplier(mdShopOrderSettingEntry.getCommodityId(), StoreId);
                }
                //直通直送必须配置默认供应商;
                QYAssert.isTrue(null != mdShopOrderSettingEntry.getSupplierId(), "门店订货通用设置,默认供应商未设置");
            }

            if (!isInternal) {
                //非代理 判断 供应商时间、仓库时间
                if (mdShopOrderSettingEntry.getLogisticsModel().intValue() == IogisticsModelEnums.DISPATCHING.getCode()) {
                    QYAssert.isTrue(null != mdShopOrderSettingEntry.getDefaultWarehouseBeginTime() && null != mdShopOrderSettingEntry.getDefaultWarehouseEndTime(), "默认仓库时间未设置");
                    QYAssert.isTrue(DateTimeUtil.compareNewDate(mdShopOrderSettingEntry.getDefaultWarehouseBeginTime(), mdShopOrderSettingEntry.getDefaultWarehouseEndTime()), "超出仓库供货时间,无法操作!");
                } else {
                    QYAssert.isTrue(null != mdShopOrderSettingEntry.getDefaultSupplierBeginTime() && null != mdShopOrderSettingEntry.getDefaultSupplierEndTime(), "默认供应商时间未设置");
                    QYAssert.isTrue(DateTimeUtil.compareNewDate(mdShopOrderSettingEntry.getDefaultSupplierBeginTime(), mdShopOrderSettingEntry.getDefaultSupplierEndTime()), "超出供应商供货时间,无法操作!");
                }
            }
        });
        return settingList;
    }


    /**
     * 检查速冻商品
     *
     * @param orderDto
     */
    public void checkStoreFrozenProduct(OrderDto orderDto, boolean isOrder) {
        BigDecimal frozenProductNumber = BigDecimal.ZERO;
        List<String> productIds = new ArrayList<String>();
        orderDto.getItems().forEach(i -> {
            productIds.add(i.getProductId());
        });

        List<FrozenProductEntry> fpList = new ArrayList<>();
        Boolean isXd = shoppingCartService.getIsXd(orderDto.getStoreId());
        Shop shop = shopService.getShopByStoreId(orderDto.getStoreId());
        if (isXd || StallUtils.isStallSubcontractor(shop.getManagementMode())) {
            fpList = orderMapper.findXdFrozenProductListByProductIds(productIds);
        } else {
            fpList = orderMapper.findFrozenProductListByProductIds(productIds);
        }

        // 获取冻品凑整商品组
        List<Long> freezeGroupList = commodityService.getCommodityFreezeGroup();
        if (SpringUtil.isNotEmpty(fpList)) {
            Map<String, FrozenProductEntry> fpMap = fpList.stream().collect(Collectors.toMap(FrozenProductEntry::getProductId, Function.identity()));
            for (OrderItemDto dto : orderDto.getItems()) {
                FrozenProductEntry fp = fpMap.get(dto.getProductId());
                if (fp != null && null != fp.getSalesBoxCapacity() && fp.getSalesBoxCapacity().compareTo(BigDecimal.ZERO) > 0) {
                    QYAssert.isTrue(dto.getProductNum().divideAndRemainder(fp.getSalesBoxCapacity())[1].compareTo(BigDecimal.ZERO) == 0, fp.getProductName() + ",订货数量需是起订倍数的整倍数!");
                }
                if (freezeGroupList.contains(Long.valueOf(dto.getProductId()))) {
                    frozenProductNumber = frozenProductNumber.add(dto.getProductNum());
                }
            }
        }
        if (isOrder) {
            if (frozenProductNumber.compareTo(BigDecimal.ZERO) > 0) {
                QYAssert.isTrue(frozenProductNumber.remainder(BigDecimal.valueOf(ProductFrozenMultipleEnum.PRODUCT_FROZEN_MULTIPLE.getFrozenMultiple())).compareTo(BigDecimal.ZERO) == 0, "凑整商品的订货总数量须是" + ProductFrozenMultipleEnum.PRODUCT_FROZEN_MULTIPLE.getFrozenMultiple() + "的整倍数,当前数量是" + frozenProductNumber);
            }
        }
    }


    public void buildProductPrice(OrderDto orderDto) {
        if (null != orderDto && SpringUtil.isNotEmpty(orderDto.getItems())) {

            List<String> productIds = new ArrayList<String>();
            orderDto.getItems().forEach(i -> {
                productIds.add(i.getProductId());
            });

            if (SpringUtil.isNotEmpty(productIds)) {
                List<ProductPriceEntry> ppList = this.productPrice(orderDto.getStoreId().toString(), orderDto.getOrderTime(), productIds);
                if (SpringUtil.isNotEmpty(ppList)) {
                    orderDto.getItems().forEach(i -> {
                        ppList.forEach(p -> {
                            if (i.getProductId().equals(p.getProductId())) {
                                i.updatePrice(p.getPrice());
                                i.updateProductLimit(p.getLimitNum());
                                i.setProductName(p.getProductName());

                                //是否可变价——特价不可变价 0 ，其余从 订货设置 取
                                if (p.getChangePriceStatus() != null) {
                                    i.setChangePriceStatus(p.getChangePriceStatus());
                                }

                                i.setOriginalPrice(p.getOriginalPrice());
                            }
                        });
                    });
                }
            }
        }
    }

    private List<ProductPriceEntry> productPrice(String storeId, String orderTime, List<String> productIds) {
        if (StringUtils.isBlank(storeId) || SpringUtil.isEmpty(productIds)) {
            return null;
        }
        Map<String, Object> paramMap = new HashMap<String, Object>();
        paramMap.put("storeId", storeId);
        paramMap.put("productIds", productIds);
        //客户价格方案
        List<ProductPriceEntry> ppdList = new ArrayList<>();
        //List<ProductPriceEntry> ppdList = this.orderMapper.findStoreCommodityByStoreIdAndProductIds(paramMap);

        CommodityListRequestIDTO idto = new CommodityListRequestIDTO();
        idto.setStoreId(storeId);
        List<Long> commodityIdList = productIds.stream().map(s -> Long.parseLong(s.trim())).collect(Collectors.toList());
        idto.setCommodityIdListAll(commodityIdList);
        List<CommodityResultODTO> commodityResultList = productPriceModelClient.findStoreCommodityList(idto);
        if (CollectionUtils.isNotEmpty(commodityResultList)) {
            for (CommodityResultODTO commodityResult : commodityResultList) {
                ProductPriceEntry productPriceEntry = new ProductPriceEntry();
                productPriceEntry.setProductId(commodityResult.getCommodityId());
                productPriceEntry.setProductCode(commodityResult.getCommodityCode());
                productPriceEntry.setProductName(commodityResult.getCommodityName());
                productPriceEntry.setPrice(commodityResult.getCommodityPrice());
                productPriceEntry.setOriginalPrice(commodityResult.getCommodityPrice());
                ppdList.add(productPriceEntry);
            }
        }
        //促销特价及限量;
        processProductPrice(ppdList, storeId, orderTime);
        return ppdList;
    }

    /*
     * 特价
     */
    public void processProductPrice(List<ProductPriceEntry> ppList, String storeId, String orderTime) {

        if (SpringUtil.isNotEmpty(ppList) && StringUtils.isNotBlank(storeId) && StringUtils.isNotBlank(orderTime)) {

            List<Promotion> promotionList = commonService.findCommodityPromotionByStoreId(storeId, orderTime);

            if (SpringUtil.isNotEmpty(promotionList)) {
                promotionList.forEach(p -> { //多特价
                    List<PromotionProduct> promotionProductList = commonService.findPromotionProductByPromotionId(p.getId().toString());
                    if (SpringUtil.isNotEmpty(promotionProductList)) {
                        promotionProductList.forEach(pp -> {
                            ppList.forEach(c -> {
                                if (c.getProductCode().equals(pp.getProductCode())) {
                                    c.setPrice(BigDecimal.valueOf(pp.getPrice()));
                                    if (pp.getLimitNumber() > 0) {
                                        c.setLimitNum(BigDecimal.valueOf(pp.getLimitNumber()));
                                    }
                                    c.setChangePriceStatus(YesOrNoEnums.NO.getCode());
                                }
                            });
                        });
                    }
                });
            }
        }
    }

    //处理商品价格为空 或者 等于0
    private void processCommodityPrice(OrderDto orderDto) {
        List<FilterTipVo> filterTips = ThreadLocalUtils.getFilterTips();
        orderDto.setItems(
                orderDto.getItems().stream().filter(i -> {
                    boolean flag = (i.getPrice() != null) && (i.getPrice().compareTo(BigDecimal.ZERO) > 0);
                    if (!flag) {
                        filterTips.add(new FilterTipVo(Long.valueOf(i.getProductId()), null, i.getProductName(), "", i.getProductNum(), FilterTipEnum.REMOVE.getMsg(), FilterTipEnum.REMOVE.getType()));
                    }
                    return flag;
                }).collect(Collectors.toList())
        );
        ThreadLocalUtils.setFilterTips(filterTips);
    }


    /**
     * 赠送商品
     * 如果配置了限量表示该限量是按照门店级别的限量(即赠送按照门店级别的配置)，如果没有配置表示赠送时按照订单级别来赠送，只要满足条件每个订单都可以赠送
     * NEW  2024-12-11调整逻辑 门店下单和客服下单逻辑合并
     * @param storeId
     * @param orderDto
     * @param orderTypeEnum 订单类型 目前只支持门店下单和客服下单
     */
    public void unifyProcessGifts(String storeId, OrderDto orderDto,OrderTypeEnum orderTypeEnum) {
        //只支持门店下单和客服下单赠送商品
        if(!(Objects.equals(OrderTypeEnum.PC_ORDER,orderTypeEnum) || Objects.equals(OrderTypeEnum.STORE_ORDER,orderTypeEnum))){
            return;
        }

        // 1. 查询门店对应的 赠品方案列表 t_gift_model {id, giftModelType}可能是多个赠品方案，每个赠品方案的类型可能不一样)
        List<GiftModel> giftModelList = commonService.findGiftModelByStoreId(new HashMap<String,Object>(){
            {
                put("storeId", storeId);
                put("orderTime", orderDto.getOrderTime());
            }
        });

        if (SpringUtil.isEmpty(giftModelList)) {
            return;
        }

        for (GiftModel giftModel : giftModelList) {
            // 订单金额 or 商品数量 or 商品总金额
            BigDecimal conditionValueForOrder = this.getConditionValueForOrder(giftModel, orderDto);

            // 根据订单金额找合适的赠品方案条件
            GiftModelCondition giftModelCondition = getGiftModelCondition(giftModel, conditionValueForOrder);
            // 判断订单的条件值是否大于数据库配置的条件值
            if (giftModelCondition == null || conditionValueForOrder.compareTo(giftModelCondition.getConditionValue()) < 0) {
                continue;
            }
            // 条件类型: 1.赠送一次 2.累计赠送
            Integer conditionType = giftModelCondition.getConditionType();
            // 获取赠品方案中赠送的商品列表 t_gift_product(commodity_id、commodity_number商品数量、commodity_max_number商品最大数量)
            List<GiftProduct> giftProductList = commonService.findGiftProductByGiftModelConditionId(giftModelCondition.getId());

            if (GiftModelConditionTypeEnum.GIVE_ONE.getCode().equals(conditionType)) {
                // 赠送一次
                //兼容客服下单逻辑-----如果是客服下单，不用考虑这个商品这天是否已经赠送过，可以重复赠送
                if(Objects.equals(OrderTypeEnum.PC_ORDER,orderTypeEnum)){
                    giftProductList.forEach(giftProduct -> {
                        orderDto.addItem(new OrderItemDto(giftProduct.getCommodityId().toString(), BigDecimal.ZERO, BigDecimal.ZERO, giftProduct.getCommodityNumber(), "促销赠品", ProductTypeEnums.GIFT.getCode(), giftModel.getId(), null));
                    });
                }else{
                    List<Long> commodityIdList = giftProductList.stream().map(item -> item.getCommodityId()).collect(Collectors.toList());
                    // 查询 storeId + orderTime + 赠送类型 + commodityIdList + 方案id 对应的数量总和 commodityId -> sum(commodityNum)
                    List<OrderList> orderLists = orderListMapper.getGiftedOrStkedNumForShop(storeId, orderDto.getOrderTime(), commodityIdList, ProductTypeEnums.GIFT.getCode(), giftModel.getId());
                    Map<String, OrderList> commodityIdAndObjMap = orderLists.stream().collect(Collectors.toMap(OrderList::getModeIdAndCommodityIdKey, Function.identity()));
                    giftProductList.forEach(giftProduct -> {
                        // 如果这个商品这天没有赠送过就赠送
                        OrderList commodity = commodityIdAndObjMap.get(giftModel.getId() + "-" + giftProduct.getCommodityId());
                        if (commodity == null) {
                            orderDto.addItem(new OrderItemDto(giftProduct.getCommodityId().toString(), BigDecimal.ZERO, BigDecimal.ZERO, giftProduct.getCommodityNumber(), "促销赠品", ProductTypeEnums.GIFT.getCode(), giftModel.getId(), null));
                        }
                    });
                }

            } else if (GiftModelConditionTypeEnum.CUMULATIVE_GIVE.getCode().equals(conditionType)) {
                // 累计赠送数倍累加
                BigDecimal numberForMultiple = conditionValueForOrder.divideToIntegralValue(giftModelCondition.getConditionValue());
                //客服下单
                if(Objects.equals(OrderTypeEnum.PC_ORDER,orderTypeEnum)){
                    giftProductList.forEach(giftProduct -> {
                        BigDecimal giftCommodityNum;
                        BigDecimal totalNumber = giftProduct.getCommodityNumber().multiply(numberForMultiple);
                        //限量
                        if (Objects.nonNull(giftProduct.getCommodityMaxNumber())) {
                            //不超出 =Number
                            //超出限额
                            if (totalNumber.compareTo(giftProduct.getCommodityMaxNumber()) > 0) {
                                giftCommodityNum = giftProduct.getCommodityMaxNumber();
                            } else {
                                giftCommodityNum = totalNumber;
                            }
                        } else {
                            giftCommodityNum = totalNumber;
                        }
                        orderDto.addItem(new OrderItemDto(giftProduct.getCommodityId().toString(), BigDecimal.ZERO, BigDecimal.ZERO, giftCommodityNum, "促销赠品", ProductTypeEnums.GIFT.getCode(),giftModel.getId(), null));

                    });
                }else{
                    //门店下单
                    giftProductList.forEach(giftProduct -> {
                        // 判断是否限量，如果限量，需要计算出最终赠送的商品数量，如果不限制直接根据订单级别送
                        BigDecimal giftCommodityNum;
                        BigDecimal totalNumber = giftProduct.getCommodityNumber().multiply(numberForMultiple);
                        if (giftProduct.getCommodityMaxNumber() != null) {
                            // 限量：门店级别控制
                            giftCommodityNum = calcGiftOrStkNumForShopLevel(storeId, orderDto.getOrderTime(), giftProduct.getCommodityId(), giftProduct.getCommodityMaxNumber(), totalNumber, ProductTypeEnums.GIFT.getCode(), giftModel.getId());
                        } else {
                            giftCommodityNum = totalNumber;
                        }

                        if (giftCommodityNum.compareTo(BigDecimal.ZERO) > 0) {
                            orderDto.addItem(new OrderItemDto(giftProduct.getCommodityId().toString(), BigDecimal.ZERO, BigDecimal.ZERO, giftCommodityNum, "促销赠品", ProductTypeEnums.GIFT.getCode(), giftModel.getId(), null));
                        }
                    });
                }

            }
        }
    }

    /**
     * 找出阶梯条件中满足订单金额的条件
     *
     * @param giftModel
     * @param orderTotalPrice
     * @return
     */
    private GiftModelCondition getGiftModelCondition(GiftModel giftModel, BigDecimal orderTotalPrice) {
        // 一个赠品方案可以有多个条件 梯度（只有订单金额会有多个条件，相当于多个赠品方案每个方案有一个条件的情况）
        // 查询赠品方案条件 t_gift_model_condition(condition_type 条件类型: 1.赠送一次, 2.累计赠送; condition_value 条件值，页面中的"达到")
        List<GiftModelCondition> giftModelConditionList = commonService.findGiftModelConditionByGiftModelId(giftModel.getId());

        if (GiftModelTypeEnum.ORDERAMOUT.getCode().equals(giftModel.getGiftModelType()) && giftModelConditionList.size() > 1) {
            GiftModelCondition giftModelCondition = null;
            for (GiftModelCondition item : giftModelConditionList) {
                if (orderTotalPrice.compareTo(item.getConditionValue()) >= 0) {
                    // 如果有多个条件梯度，每个条件的梯度都必须和第一个条件保持一致，将后面的条件强制设置成第一个条件对应的类型, 只有订单金额才有可能有多个条件
                    giftModelCondition = item;
                }
            }
            return giftModelCondition;
        } else {
            return giftModelConditionList.get(0);
        }
    }

    private BigDecimal getConditionValueForOrder(GiftModel giftModel, OrderDto orderDto) {
        // 设置商品编码
        if (!GiftModelTypeEnum.ORDERAMOUT.getCode().equals(giftModel.getGiftModelType())) {
            setCommodityCodeForItems(orderDto);
        }

        BigDecimal conditionValueForOrder = BigDecimal.ZERO;
        if (GiftModelTypeEnum.ORDERAMOUT.getCode().equals(giftModel.getGiftModelType())) {
            // 订单金额
            conditionValueForOrder = this.getOrderTotalPrice(orderDto, false);
        } else if (GiftModelTypeEnum.COMMODITYNUMBER.getCode().equals(giftModel.getGiftModelType())) {
            // 商品数量
            conditionValueForOrder = this.getCommodityNumber(orderDto, giftModel.getCommmdityCodes());
        } else if (GiftModelTypeEnum.COMMODITYAMOUT.getCode().equals(giftModel.getGiftModelType())) {
            // 商品总金额
            conditionValueForOrder = this.getCommodityTotalPrice(orderDto, giftModel.getCommmdityCodes());
        }

        return conditionValueForOrder;
    }


    /**
     * 计算按照门店级别计算订单最终赠送或配货的数量
     *
     * @param storeId
     * @param orderTime
     * @param commodityId
     * @param shopLimitTotalNum     门店允许赠送的总数量
     * @param giftOrStkCommodityNum 按订单级别允许赠送的数量
     * @return
     */
    private BigDecimal calcGiftOrStkNumForShopLevel(String storeId, String orderTime, Long commodityId, BigDecimal shopLimitTotalNum, BigDecimal giftOrStkCommodityNum, Integer type, Long giftModelOrPromotionId) {
        // 查询门店orderTime这天商品的赠送的总数量
        List<OrderList> orderLists = orderListMapper.getGiftedOrStkedNumForShop(storeId, orderTime, Arrays.asList(commodityId), type, giftModelOrPromotionId);
        if (SpringUtil.isEmpty(orderLists)) {
            return giftOrStkCommodityNum.compareTo(shopLimitTotalNum) >= 0 ? shopLimitTotalNum : giftOrStkCommodityNum;
        }

        Map<Long, OrderList> commodityIdAndObjMap = orderLists.stream().collect(Collectors.toMap(OrderList::getCommodityId, Function.identity()));
        OrderList orderList = commodityIdAndObjMap.get(commodityId);
        if (orderList == null) {
            // 如果为空，表示这个商品还没有赠送，那就不用检查赠送的商品是否超过门店总数量的限制了
            return giftOrStkCommodityNum;
        }

        // 该门店已经赠送的数量
        BigDecimal giftedSumNum = orderList.getCommodityNum();
        // 门店的总数量 - 已经赠送的数据 = 门店最多还允许赠送的数量
        BigDecimal maxAllowNum = shopLimitTotalNum.subtract(giftedSumNum);
        if (maxAllowNum.equals(BigDecimal.ZERO)) {
            return BigDecimal.ZERO;
        }

        int result = maxAllowNum.compareTo(giftOrStkCommodityNum);
        if (result == 0 || result == 1) {
            return giftOrStkCommodityNum;
        } else {
            return maxAllowNum;
        }
    }

    private void setCommodityCodeForItems(OrderDto orderDto) {
        List<String> productIds = orderDto.getItems().stream().map(item -> item.getProductId()).collect(Collectors.toList());
        if (SpringUtil.isEmpty(productIds)) {
            return;
        }
        Example example = new Example(Commodity.class);
        example.createCriteria().andIn("id", productIds);
        List<Commodity> commodities = commodityMapper.selectByExample(example);
        Map<Long, Commodity> commodityIdAndObjMap = commodities.stream().collect(Collectors.toMap(Commodity::getId, Function.identity()));

        orderDto.getItems().forEach(item -> {
            Commodity commodity = commodityIdAndObjMap.get(Long.valueOf(item.getProductId()));
            if (commodity != null) {
                item.setCommodityCode(commodity.getCommodityCode());
            }
        });
    }

    /**
     * 门店下单和客服下单处理配货
     * @param storeId
     * @param orderDto
     * @param orderLaunchTypeEnum
     */
    public void unifyProcessDistribution(String storeId, OrderDto orderDto,OrderLaunchTypeEnum orderLaunchTypeEnum) {
        //只支持门店下单和客服下单配货
        if(!(Objects.equals(OrderLaunchTypeEnum.CUSTOMER_SERVICE,orderLaunchTypeEnum) || Objects.equals(OrderLaunchTypeEnum.SHOP,orderLaunchTypeEnum))){
            return;
        }

        // 1. 查询配货方案列表(可能同时有多个配货方案在生效) t_promotion_stk(id)
        List<PromotionStk> promotionStkList = commonService.findDistributionByStoreId(new HashMap<String,Object>(){{
            put("storeId", storeId);
            put("orderTime", orderDto.getOrderTime());
        }});
        if (SpringUtil.isEmpty(promotionStkList)) {
            return;
        }

        List<Long> promotionStkIdList = promotionStkList.stream().map(PromotionStk::getId).collect(toList());
        //查询出所有候选的stkCodeOrder
        List<PromotionStkCodeOrder> candidatePromotionStkCodeOrderList = commonService.findPromotionStkCodeOrderByPromotionIdList(promotionStkIdList);
        Map<Long, List<PromotionStkCodeOrder>> candidatePromotionStkCodeOrderMap = candidatePromotionStkCodeOrderList.stream().collect(Collectors.groupingBy(PromotionStkCodeOrder::getPromotionId));


        //最终满足条件的stkCodeOrder
        List<PromotionStkCodeOrder> promotionStkCodeOrderList = Lists.newArrayList();
        // 2. 获取订单金额(不限量)
        BigDecimal orderTotalPrice = this.getOrderTotalPrice(orderDto, true);
        for(PromotionStk p : promotionStkList){
            // 3. 获取配货条件(阶梯可能有多个条件, 只取最合适的一个)
            PromotionStkCodeOrder promotionStkCodeOrder = promotionStkCodeOrderService.getOnePromotionStkCodeOrder(candidatePromotionStkCodeOrderMap.get(p.getId()), orderTotalPrice);
            if(Objects.nonNull(promotionStkCodeOrder)){
                promotionStkCodeOrderList.add(promotionStkCodeOrder);
            }
        }

        if(SpringUtil.isEmpty(promotionStkCodeOrderList)){
            return;
        }

        List<Long> promotionStkCodeIdList = promotionStkCodeOrderList.stream().map(PromotionStkCodeOrder::getId).collect(toList());
        List<PromotionStkComm> promotionStkCommList = commonService.findPromotionStkCommByStkCodeIdList(promotionStkCodeIdList);
        Map<Long, List<PromotionStkComm>> promotionStkCommMap = promotionStkCommList.stream().collect(Collectors.groupingBy(PromotionStkComm::getStkCodeId));


        for (PromotionStkCodeOrder promotionStkCodeOrder : promotionStkCodeOrderList) {
            // 4. 获取配货的商品列表 t_promotion_stk_comm
            int stkType = promotionStkCodeOrder.getStkType().intValue();
            List<PromotionStkComm> pscList = promotionStkCommMap.get(promotionStkCodeOrder.getId());


            // 过滤掉不在价格方案中的配货商品
            this.findStkCommodityPrice(storeId,pscList,orderDto.getOrderTime(),orderLaunchTypeEnum);
            promotionStkCommList = this.processPromotionStkComm(pscList); //移除无价格的商品
            if (SpringUtil.isEmpty(promotionStkCommList)) {
                continue;
            }


            if(Objects.equals(PromotionStkTypeEnum.GIVE_ONE.getCode(),stkType)){
                if(Objects.equals(OrderLaunchTypeEnum.CUSTOMER_SERVICE,orderLaunchTypeEnum)){
                    pscList.forEach(promotionStkComm -> {
                        OrderItemDto itemDto = new OrderItemDto(promotionStkComm.getCommId().toString(), promotionStkComm.getOriginalPrice(), promotionStkComm.getCommodityPrice(),
                                BigDecimal.valueOf(promotionStkComm.getNumber()), "促销配货", ProductTypeEnums.RATION.getCode(), null, promotionStkCodeOrder.getPromotionId());
                        itemDto.setChangePriceStatus(promotionStkComm.getChangePriceStatus());
                        orderDto.addItem(itemDto);
                    });
                }else{
                    List<Long> commodityIdList = pscList.stream().map(item -> item.getCommId()).collect(Collectors.toList());

                    // 5. 查询门店OrderTime这天已、某配货模式下涉及到的配货商品的数量 commodityId -> sum(commodityNum)
                    List<OrderList> orderLists = orderListMapper.getGiftedOrStkedNumForShop(storeId, orderDto.getOrderTime(), commodityIdList, ProductTypeEnums.RATION.getCode(), promotionStkCodeOrder.getPromotionId());
                    Map<String, OrderList> commodityIdAndObjMap = orderLists.stream().collect(Collectors.toMap(OrderList::getModeIdAndCommodityIdKey, Function.identity()));
                    pscList.forEach(promotionStkComm -> {
                        OrderList commodity = commodityIdAndObjMap.get(promotionStkCodeOrder.getPromotionId() + "-" + promotionStkComm.getCommId());
                        // 配送一次(如果该门店该配送模式下还没有被配送过就给配送)
                        if (commodity == null) {
                            OrderItemDto itemDto = new OrderItemDto(promotionStkComm.getCommId().toString(), promotionStkComm.getCommodityPrice(), promotionStkComm.getCommodityPrice(), BigDecimal.valueOf(promotionStkComm.getNumber()), "促销配货", ProductTypeEnums.RATION.getCode(), null, promotionStkCodeOrder.getPromotionId());
                            itemDto.setChangePriceStatus(promotionStkComm.getChangePriceStatus());
                            orderDto.addItem(itemDto);
                        }
                    });

                }
            }else if(Objects.equals(PromotionStkTypeEnum.CUMULATIVE_GIVE.getCode(),stkType)){
                // 累计配货
                // 配货次数
                BigDecimal numberForMultiple = orderTotalPrice.divideToIntegralValue(BigDecimal.valueOf(promotionStkCodeOrder.getOrderReach()));
                //客服下单
                if(Objects.equals(OrderLaunchTypeEnum.CUSTOMER_SERVICE,orderLaunchTypeEnum)){
                    pscList.forEach(promotionStkComm -> {
                        BigDecimal promotionCommodityNum;
                        BigDecimal totalNumber = BigDecimal.valueOf(promotionStkComm.getNumber()).multiply(numberForMultiple);
                        //限量
                        if (Objects.nonNull(promotionStkComm.getLimes())) {
                            //不超出 =Number
                            //超出限额
                            if (totalNumber.compareTo(BigDecimal.valueOf(promotionStkComm.getLimes())) > 0) {
                                promotionCommodityNum = BigDecimal.valueOf(promotionStkComm.getLimes());
                            } else {
                                promotionCommodityNum = totalNumber;
                            }
                        } else {
                            promotionCommodityNum = totalNumber;
                        }
                        OrderItemDto itemDto = new OrderItemDto(promotionStkComm.getCommId().toString(), promotionStkComm.getCommodityPrice(), promotionStkComm.getCommodityPrice(), promotionCommodityNum, "促销配货", ProductTypeEnums.RATION.getCode(),null,promotionStkCodeOrder.getPromotionId());
                        itemDto.setChangePriceStatus(promotionStkComm.getChangePriceStatus());
                        orderDto.addItem(itemDto);
                    });
                }else{
                    //门店订货
                    pscList.forEach(promotionStkComm -> {
                        // 需要配货的总数量
                        BigDecimal totalCommodityNum = BigDecimal.valueOf(promotionStkComm.getNumber()).multiply(numberForMultiple);
                        if (promotionStkComm.getLimes() != null) {
                            // 限量 计算配货的数量
                            totalCommodityNum = calcGiftOrStkNumForShopLevel(storeId, orderDto.getOrderTime(), promotionStkComm.getCommId(), BigDecimal.valueOf(promotionStkComm.getLimes()), totalCommodityNum, ProductTypeEnums.RATION.getCode(), promotionStkCodeOrder.getPromotionId());
                        }

                        if (totalCommodityNum.compareTo(BigDecimal.ZERO) > 0) {
                            OrderItemDto itemDto = new OrderItemDto(promotionStkComm.getCommId().toString(), promotionStkComm.getCommodityPrice(), promotionStkComm.getCommodityPrice(), totalCommodityNum, "促销配货", ProductTypeEnums.RATION.getCode(), null, promotionStkCodeOrder.getPromotionId());
                            itemDto.setChangePriceStatus(promotionStkComm.getChangePriceStatus());
                            orderDto.addItem(itemDto);
                        }

                    });
                }
            }

        }
    }

    /**
     * 获取一个有效的配货条件
     *
     * @return
     */
    private PromotionStkCodeOrder getOnePromotionStkCodeOrder(Long id, BigDecimal orderTotalPrice) {
        List<PromotionStkCodeOrder> promotionStkCodeOrderList = commonService.findPromotionStkCodeOrderByPromotionId(id);
        if (promotionStkCodeOrderList.size() > 1) {
            PromotionStkCodeOrder result = null;
            for (PromotionStkCodeOrder item : promotionStkCodeOrderList) {
                if (orderTotalPrice.compareTo(BigDecimal.valueOf(item.getOrderReach())) >= 0) {
                    result = item;
                }
            }
            return result;
        } else {
            PromotionStkCodeOrder promotionStkCodeOrder = promotionStkCodeOrderList.get(0);
            if (orderTotalPrice.compareTo(BigDecimal.valueOf(promotionStkCodeOrder.getOrderReach())) >= 0) {
                return promotionStkCodeOrder;
            }
        }

        return null;
    }


    //客户/产品价格方案限量
    public void checkCustomerProductLimit(OrderDto orderDto) {
        List<ProductLimitEntry> limitProductList = this.findLimitProductByStoreId(orderDto.getStoreId().toString());
        if (SpringUtil.isNotEmpty(limitProductList)) {
            List<ProductLimitEntry> reulstLimitProductList = new ArrayList<ProductLimitEntry>();
            orderDto.getItems().forEach(i -> {
                limitProductList.forEach(l -> {
                    if (i.getProductNumLimit() != null && i.getProductNumLimit().compareTo(BigDecimal.ZERO) > 0) {
                        if (l.getCommodityId().equals(i.getProductId())) {
                            l.setLimitNumber(i.getProductNumLimit());
                        } else {
                            ProductLimitEntry dto = new ProductLimitEntry();
                            dto.setCommodityId(i.getProductId());
                            dto.setCommodityName(i.getProductName());
                            dto.setLimitNumber(i.getProductNumLimit());
                            reulstLimitProductList.add(dto);
                        }
                    }
                });
            });
            limitProductList.forEach(l -> {
                reulstLimitProductList.add(l);
            });
            reulstLimitProductList.forEach(l -> {
                orderDto.getItems().forEach(i -> {
                    if (l.getCommodityId().equals(i.getProductId())) {
                        QYAssert.isTrue(i.getProductNum().compareTo(l.getLimitNumber()) <= 0, l.getCommodityName() + "必须小于限量  :" + l.getLimitNumber());
                    }
                });
            });
        } else {
            orderDto.getItems().forEach(i -> {
                if (i.getProductNumLimit() != null && i.getProductNumLimit().compareTo(BigDecimal.ZERO) > 0) {
                    QYAssert.isTrue(i.getProductNum().compareTo(i.getProductNumLimit()) <= 0, i.getProductName() + "必须小于限量  :" + i.getProductNumLimit());
                }
            });
        }
    }


    public List<ProductLimitEntry> findLimitProductByStoreId(String storeId) {

        List<ProductLimitEntry> resultDto = new ArrayList<ProductLimitEntry>();

        if (StringUtils.isBlank(storeId)) {
            return null;
        }

        String productPriceModelId = commonService.findProductPriceModelIdByStoreId(storeId);
        //客户价格方案限量
        List<ProductLimitEntry> customerLimitProductList = commonService.findCustomerLimitByStoreId(storeId);
        //产品价格方案限量
        List<ProductLimitEntry> priceModelLimitProductList = new ArrayList<ProductLimitEntry>();

        if (null != productPriceModelId) {
            priceModelLimitProductList = commonService.findProductPriceModelLimitByPriceModelId(productPriceModelId);
        }

        if (SpringUtil.isNotEmpty(priceModelLimitProductList)) {
            priceModelLimitProductList.forEach(p -> {
                ProductLimitEntry dto = new ProductLimitEntry();
                dto.setCommodityId(p.getCommodityId());
                dto.setCommodityName(p.getCommodityName());
                dto.setLimitNumber(p.getLimitNumber());
                resultDto.add(dto);
            });
        }

        if (SpringUtil.isNotEmpty(customerLimitProductList)) {
            if (SpringUtil.isEmpty(resultDto)) {
                customerLimitProductList.forEach(c -> {
                    ProductLimitEntry dto = new ProductLimitEntry();
                    dto.setCommodityId(c.getCommodityId());
                    dto.setCommodityName(c.getCommodityName());
                    dto.setLimitNumber(c.getLimitNumber());
                    resultDto.add(dto);
                });
            } else {
                List<ProductLimitEntry> existProductList = customerLimitProductList.stream().filter(c -> {
                    return resultDto.stream().anyMatch(i -> {
                        return i.getCommodityId().equals(c.getCommodityId());
                    });
                }).collect(Collectors.toList());

                if (SpringUtil.isNotEmpty(existProductList)) {
                    existProductList.forEach(p -> {
                        resultDto.forEach(i -> {
                            if (i.getCommodityId().equals(p.getCommodityId())) {
                                i.setLimitNumber(p.getLimitNumber());
                            }
                        });
                    });
                }

                List<ProductLimitEntry> noExistProductList = customerLimitProductList.stream().filter(c -> {
                    return !resultDto.stream().anyMatch(i -> {
                        return i.getCommodityId().equals(c.getCommodityId());
                    });
                }).collect(Collectors.toList());

                if (SpringUtil.isNotEmpty(noExistProductList)) {
                    noExistProductList.forEach(p -> {
                        ProductLimitEntry dto = new ProductLimitEntry();
                        dto.setCommodityId(p.getCommodityId());
                        dto.setCommodityName(p.getCommodityName());
                        dto.setLimitNumber(p.getLimitNumber());
                        resultDto.add(dto);
                    });
                }
            }
        }
        return resultDto;
    }


    //产品限量(商品限量->产品限量策略)
    //库存限量(优先级最高);
    public void checkProductLimit(OrderDto orderDto) {
        List<ProductLimitEntry> productLimitList = commonService.findCommodityLimitByNowData(orderDto.getOrderTime());
        if (SpringUtil.isNotEmpty(productLimitList)) {
            productLimitList.forEach(p -> {
                orderDto.getItems().forEach(i -> {
                    if (p.getCommodityId().equals(i.getProductId())) {
                        Map<String, Object> paramMap = new HashMap<String, Object>();
                        paramMap.put("orderTime", orderDto.getOrderTime());
                        paramMap.put("productId", i.getProductId());
                        BigDecimal productCount = this.orderMapper.findOrderProductNumber(paramMap);
                        if (null == productCount) {
                            productCount = BigDecimal.ZERO;
                        }
                        if (productCount.compareTo(BigDecimal.ZERO) > 0) {
                            QYAssert.isTrue(p.getLimitNumber().subtract(productCount).compareTo(i.getProductNum()) >= 0, p.getCommodityName() + "库存不足，剩余库存数量：" + p.getLimitNumber().subtract(productCount));
                        } else {
                            QYAssert.isTrue(i.getProductNum().compareTo(p.getLimitNumber()) <= 0, p.getCommodityName() + "库存不足，剩余库存数量：" + p.getLimitNumber());
                        }
                    }
                });
            });
        }
    }

    /**
     *注：这个只的是订单明细行，假如一个订单里3条明细，
     * 其中A商品依据大仓库存、B商品限量供应、C商品不限量供应，
     * 该订单被配比/配货方案覆盖，则，A和B商品不考虑配比配货方案，C商品仍然计算配比配货
     * @param orderDto
     * @return
     */
    //统计订单金额
    private BigDecimal getOrderTotalPrice(OrderDto orderDto, Boolean isDistribution) {
        BigDecimal result = BigDecimal.ZERO;
        for (OrderItemDto dto : orderDto.getItems()) {
            if(isDistribution){
                if (!ProductTypeEnums.GIFT.getCode().equals(dto.getType()) && StockTypeEnum.UN_LIMIT.getCode() == dto.getStockType()) {
                    result = result.add(dto.amount());
                }
            }else {
                if (!ProductTypeEnums.GIFT.getCode().equals(dto.getType())) {
                    result = result.add(dto.amount());
                }
            }
        }
        return result.setScale(2, BigDecimal.ROUND_HALF_UP);
    }

    //统计商品数量
    private BigDecimal getCommodityNumber(OrderDto orderDto, String commmdityCodes) {
        BigDecimal result = BigDecimal.ZERO;

        List<String> commodityCodeList = Arrays.asList(commmdityCodes.split(","));
        for (OrderItemDto commodity : orderDto.getItems()) {
            if (!ProductTypeEnums.GIFT.getCode().equals(commodity.getType()) && commodityCodeList.contains(commodity.getCommodityCode())) {
                result = result.add(commodity.getProductNum());
            }
        }

        return result;
    }

    //统计商品总金额
    private BigDecimal getCommodityTotalPrice(OrderDto orderDto, String commmdityCodes) {
        BigDecimal result = BigDecimal.ZERO;
        List<String> commodityCodeList = Arrays.asList(commmdityCodes.split(","));

        for (OrderItemDto commodity : orderDto.getItems()) {
            if (!ProductTypeEnums.GIFT.getCode().equals(commodity.getType()) && commodityCodeList.contains(commodity.getCommodityCode())) {
                result = result.add(commodity.getPrice().multiply(commodity.getProductNum()));
            }
        }

        return result;
    }

    //获取配货商品价格
    private void findStkCommodityPrice(String storeId, List<PromotionStkComm> list, String orderTime,OrderLaunchTypeEnum orderLaunchTypeEnum) {

        if(Objects.equals(OrderLaunchTypeEnum.CUSTOMER_SERVICE,orderLaunchTypeEnum)){
            promotionStkCommService.findStkCommodityPrice(storeId,list,orderTime);
        }else{
            this.shopOrderFindStkCommodityPrice(storeId,list,orderTime);
        }

    }


    /**
     * 门店订货获取配货商品价格
     * 配货商品设置特价，如果配货商品存在特价。也是不可变价
     * @param storeId
     * @param list
     * @param orderTime
     */
    private void shopOrderFindStkCommodityPrice(String storeId, List<PromotionStkComm> list, String orderTime) {

        if (SpringUtil.isNotEmpty(list)) {
            List<String> productIds = new ArrayList<String>();
            list.forEach(c -> {
                productIds.add(c.getCommId().toString());
            });
            Map<String, Object> paramMap = new HashMap<String, Object>();
            paramMap.put("storeId", storeId);
            paramMap.put("productIds", productIds);
            // 查询价格方案 t_store_settlement、t_product_price_model、t_product_price_model_list
            //List<DistributionProductEntry>  dpList = this.orderMapper.findCommodityPriceByStoreIdAndCommodityIds(paramMap);
            List<DistributionProductEntry> dpList = new ArrayList<>();

            CommodityListRequestIDTO idto = new CommodityListRequestIDTO();
            idto.setStoreId(storeId);
            List<Long> commodityIdList = productIds.stream().map(s -> Long.parseLong(s.trim())).collect(Collectors.toList());
            idto.setCommodityIdListAll(commodityIdList);
            List<CommodityResultODTO> commodityResultList = productPriceModelClient.findStoreCommodityList(idto);
            if (CollectionUtils.isNotEmpty(commodityResultList)) {
                for (CommodityResultODTO commodityResult : commodityResultList) {
                    DistributionProductEntry distributionProductEntry = new DistributionProductEntry();
                    distributionProductEntry.setProductId(commodityResult.getCommodityId());
                    distributionProductEntry.setProductCode(commodityResult.getCommodityCode());
                    distributionProductEntry.setCommodityPrice(commodityResult.getCommodityPrice());
                    dpList.add(distributionProductEntry);
                }

                dpList.forEach(p -> {
                    list.forEach(l -> {
                        if (l.getCommId().toString().equals(p.getProductId())) {
                            l.setCommodityPrice(p.getCommodityPrice());
                            l.setProductCode(p.getProductCode());
                        }
                    });
                });
                this.processPromotionProductPrice(list, storeId, orderTime);
            }
        }
    }

    /**
     * 配货商品设置特价，如果配货商品存在特价。也是不可变价
     * @param dpList
     * @param storeId
     * @param orderTime
     */
    private void processPromotionProductPrice(List<PromotionStkComm> dpList, String storeId, String orderTime) {
        if (SpringUtil.isNotEmpty(dpList) && StringUtils.isNotBlank(storeId) && StringUtils.isNotBlank(orderTime)) {
            HashMap<String, Object> paramMap = new HashMap<String, Object>();
            paramMap.put("storeId", storeId);
            paramMap.put("orderTime", orderTime);

            List<Promotion> promotionList = commonService.findCommodityPromotionByStoreId(storeId, orderTime);

            Promotion promotion = null;
            if (SpringUtil.isNotEmpty(promotionList)) { //商品特价方案只取一个
                promotion = promotionList.get(0);
            }
            if (null != promotion) {
                List<PromotionProduct> promotionProductList = commonService.findPromotionProductByPromotionId(promotion.getId().toString());
                if (SpringUtil.isNotEmpty(promotionProductList)) {
                    promotionProductList.forEach(p -> {
                        dpList.forEach(c -> {
                            if (p.getProductCode().equals(c.getProductCode())) {
                                c.setCommodityPrice(BigDecimal.valueOf(p.getPrice()));
                                // 如果配货商品存在特价。也是不可变价
                                c.setChangePriceStatus(YesOrNoEnums.NO.getCode());
                            }
                        });
                    });
                }
            }
        }
    }


    //配货方案 没有价格-> 移除
    private List<PromotionStkComm> processPromotionStkComm(List<PromotionStkComm> list) {
        if (SpringUtil.isEmpty(list)) {
            return null;
        }
        return list.stream().filter(p -> {
            return (null != p.getCommodityPrice() && p.getCommodityPrice().compareTo(BigDecimal.ZERO) > 0);
        }).collect(Collectors.toList());
    }

    //处理已停用商品
    private void processCommodityStatus(OrderDto orderDto) {
        // 门店订货-过滤提示
        List<FilterTipVo> filterTips = ThreadLocalUtils.getFilterTips();

        List<Long> productIds = new ArrayList<>();
        orderDto.getItems().forEach(i -> {
            productIds.add(Long.valueOf(i.getProductId()));
        });
        if (SpringUtil.isNotEmpty(productIds)) {
            List<ProductStatusEntry> psList = commodityMapper.queryCommodityStatus(orderDto.getStoreId(), productIds);
            if (SpringUtil.isNotEmpty(psList)) {
                psList.forEach(p -> {
                    orderDto.getItems().forEach(i -> {
                        if (p.getCommodityId().equals(i.getProductId())) {
                            i.setStatus(p.getStatus());
                            i.setCommodityStatus(p.getCommodityState());
                            i.setCommodityPurchaseStatus(p.getCommodityPurchaseStatus());
                        }
                    });
                });
            }

            Boolean ifAdmin = ThreadLocalUtils.getAdmin();
            // 过滤掉商品状态停用的或者不可采的
            orderDto.setItems(
                    orderDto.getItems().stream().filter(i -> {
                        // 状态：0-停用,1-启用(赠送或者配货直接删除不进行提示，原始商品是提示的)
                        // 代理订货，不判断商品的可售状态（即停售的商品，也能下单）
                        boolean flag;
                        if (ifAdmin) {
                            flag = (i.getStatus().intValue() == 0 || i.getCommodityPurchaseStatus().intValue() == 0) && ProductTypeEnums.PRODUCT.getCode().equals(i.getType());
                        } else {
                            flag = (i.getStatus().intValue() == 0 || i.getCommodityStatus().intValue() == 0 || i.getCommodityPurchaseStatus().intValue() == 0) && ProductTypeEnums.PRODUCT.getCode().equals(i.getType());
                        }
                        if (flag) {
                            long commodityId = Long.parseLong(i.getProductId());
                            filterTips.add(new FilterTipVo(commodityId, null, i.getProductName(), "", i.getProductNum(), FilterTipEnum.REMOVE.getMsg(), FilterTipEnum.REMOVE.getType()));
                        }
                        if (ifAdmin) {
                            return (i.getStatus().intValue() == 1 && i.getCommodityPurchaseStatus().intValue() == 1);
                        } else {
                            return (i.getStatus().intValue() == 1 && i.getCommodityStatus().intValue() == 1 && i.getCommodityPurchaseStatus().intValue() == 1);
                        }
                    }).collect(Collectors.toList())
            );

        }

        ThreadLocalUtils.setFilterTips(filterTips);
    }


    /**
     * 过滤t_md_shop_order_setting表中配置信息发生变化的商品(不仅仅是物流模式)
     * 配送、直通: 检查 物流模式+仓库+送货日期范围；直送: 检查物流模式+供应商
     *
     * @param orderDto
     */
    private void processCommodityLogistics(OrderDto orderDto) {
        Boolean ifAdmin = ThreadLocalUtils.getAdmin();
        List<String> productIds = orderDto.getItems().stream().map(item -> item.getProductId()).collect(Collectors.toList());

        // 门店订货-过滤提示
        if (SpringUtil.isNotEmpty(productIds)) {
            int logisticsModel = orderDto.getLogisticsModel();
            List<ProductLogisticsEntry> shopOrderSettingList = this.findMdShopOrderSetting(orderDto.getStoreId(), productIds);
            Map<String, ProductLogisticsEntry> commodityIdAndObjMap = shopOrderSettingList.stream().collect(Collectors.toMap(ProductLogisticsEntry::getProductId, Function.identity()));

            List<OrderItemDto> newItems = orderDto.getItems().stream().filter(item -> {
                ProductLogisticsEntry setting = commodityIdAndObjMap.get(item.getProductId());
                if (setting != null) {
                    log.info("setting={}, orderODto={}", JSON.toJSONString(setting), JSON.toJSONString(orderDto));
                    boolean isSameLogisticsModel = setting.getLogisticsModel().equals(orderDto.getLogisticsModel());
                    boolean isSameDeleveryTimeRange = setting.getDeleveryTimeRange().equals(orderDto.getDeleveryTimeRange());
                    boolean isSameWarehouse = setting.getWarehouseId().equals(orderDto.getWarehouseId().toString());
                    boolean isSameSupplier = setting.getSupplierId().equals(orderDto.getSupplierId().toString());
                    if (logisticsModel == IogisticsModelEnums.DISPATCHING.getCode()) {
                        if (ifAdmin) {
                            return isSameLogisticsModel && isSameWarehouse;
                        } else {
                            return isSameLogisticsModel && isSameWarehouse && isSameDeleveryTimeRange;
                        }
                    } else if (logisticsModel == IogisticsModelEnums.DIRECT_CONNECTION.getCode()) {
                        if (ifAdmin) {
                            return isSameLogisticsModel && isSameSupplier && isSameWarehouse;
                        } else {
                            return isSameLogisticsModel && isSameSupplier && isSameDeleveryTimeRange && isSameWarehouse;
                        }
                    } else if (logisticsModel == IogisticsModelEnums.DIRECT_SENDING.getCode()) {
                        return isSameLogisticsModel && isSameSupplier;
                    } else {
                        return false;
                    }
                } else {
                    return false;
                }
            }).collect(Collectors.toList());
            orderDto.setItems(newItems);
        }
    }

    /**
     * 判断预付款余额
     *
     * @param storeId
     * @param paidAmount
     * @return
     */
    private boolean matchBillCondition(String storeId, BigDecimal paidAmount) {
        boolean result = true;
        Example ex = new Example(StoreSettlement.class);
        ex.createCriteria().andEqualTo("storeId", storeId);

        List<StoreSettlement> ssList = this.storeSettlementMapper.selectByExample(ex);
        StoreSettlement ss = null;
        if (SpringUtil.isNotEmpty(ssList)) {
            ss = ssList.get(0);
        }
        if (null != ss) {
            if (ss.getCollectStatus()) {
                if (null == ss.getCollectPrice()) {
                    ss.setCollectPrice(0.0);
                }
                result = BigDecimal.valueOf(ss.getCollectPrice()).compareTo(paidAmount) >= 0;
            }
        }
        return result;
    }

    /**
     * 更新预付款
     *
     * @param storeId
     * @param orderAmount
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean updatePayment(String storeId, BigDecimal orderAmount) {

        Example ex = new Example(StoreSettlement.class);
        ex.createCriteria().andEqualTo("storeId", storeId);

        List<StoreSettlement> ssList = this.storeSettlementMapper.selectByExample(ex);
        StoreSettlement ss = null;
        if (SpringUtil.isNotEmpty(ssList)) {
            ss = ssList.get(0);
        }
        int rowNum = 0;
        if (ss != null) {
            if (ss.getCollectStatus()) {
                if (ss.getCollectPrice() == null) {
                    ss.setCollectPrice(BigDecimal.ZERO.doubleValue());
                }

                ss.setCollectPrice((BigDecimal.valueOf(ss.getCollectPrice()).subtract(orderAmount)).doubleValue());
                rowNum = storeSettlementMapper.updateByPrimaryKey(ss);
            }
        }
        return rowNum > 0 ? true : false;
    }

    /**
     * 订单扣款 对账
     *
     * @param order
     */
    @Transactional(rollbackFor = Exception.class)
    public void saveOrderDeductions(Order order, Long storeId, Boolean isXsJmShop) {
        if (null != storeId) {
            Example ex = new Example(StoreSettlement.class);
            ex.createCriteria().andEqualTo("storeId", storeId);

            List<StoreSettlement> ssList = this.storeSettlementMapper.selectByExample(ex);
            StoreSettlement ss = null;
            if (SpringUtil.isNotEmpty(ssList)) {
                ss = ssList.get(0);
            }
            if (null != ss) {
                if (ss.getCollectStatus()) {
                    if (ss.getCollectPrice() != null) {
                        OrderBill ob = new OrderBill();
                        ob.setStoreId(order.getStoreId());
                        ob.setOrderId(order.getId());
                        ob.setArAmount(order.getOrderAmount());
                        ob.setPaAmount(BigDecimal.ZERO);
                        ob.setOrderTime(order.getOrderTime());
                        ob.setStoreBalance(BigDecimal.valueOf(ss.getCollectPrice()));
                        ob.setBillRemark("<--门店扣款:" + order.getOrderCode() + " -->");
                        if(isXsJmShop){
                            ob.setBillRemark("<--门店订货扣款：" + order.getOrderCode() + " -->");
                        }
                        ob.setCreateTime(new Date());
                        ob.setCrateId(storeId);
                        this.orderBillMapper.insert(ob);
                    }
                }
            }
        }
    }


    //订单-取消 预付款金额回补
    @Transactional(rollbackFor = Exception.class)
    public boolean addPaymentForOrder(String storeId, BigDecimal orderAmount) {
        Example ex = new Example(StoreSettlement.class);
        ex.createCriteria().andEqualTo("storeId", storeId);
        List<StoreSettlement> ssList = this.storeSettlementMapper.selectByExample(ex);
        StoreSettlement ss = null;
        if (SpringUtil.isNotEmpty(ssList)) {
            ss = ssList.get(0);
        }
        if (ss.getCollectStatus()) {
            if (null == ss.getCollectPrice()) {
                ss.setCollectPrice(0.0);
            }
            ss.setCollectPrice((BigDecimal.valueOf(ss.getCollectPrice()).add(orderAmount)).doubleValue());
            int rowNum = this.storeSettlementMapper.updateByPrimaryKey(ss);
            return rowNum > 0 ? true : false;
        } else {
            return true;
        }
    }

    /**
     * 订单回款-对账
     */
    @Transactional(rollbackFor = Exception.class)
    public void saveOrderReceivable(Order order, Long storeId) {
        if (storeId != null) {
            Boolean isXsJmShop = shopService.isJmShop(storeId);
            Example ex = new Example(StoreSettlement.class);
            ex.createCriteria().andEqualTo("storeId", storeId);
            List<StoreSettlement> ssList = this.storeSettlementMapper.selectByExample(ex);
            StoreSettlement ss = null;
            if (SpringUtil.isNotEmpty(ssList)) {
                ss = ssList.get(0);
            }
            if (null != ss) {
                if (ss.getCollectStatus()) {
                    if (ss.getCollectPrice() != null) {
                        OrderBill ob = new OrderBill();
                        ob.setStoreId(order.getStoreId());
                        ob.setOrderId(order.getId());
                        ob.setArAmount(BigDecimal.ZERO);
                        ob.setPaAmount(order.getOrderAmount());
                        ob.setOrderTime(order.getOrderTime());
                        ob.setBillRemark("<--APP回款:" + order.getOrderCode() + " -->");
                        if(isXsJmShop){
                            ob.setBillRemark("<--门店订货回款:" + order.getOrderCode() + " -->");
                        }
                        ob.setStoreBalance(BigDecimal.valueOf(ss.getCollectPrice()));
                        ob.setCreateTime(new Date());
                        ob.setCrateId(storeId);
                        this.orderBillMapper.insert(ob);
                    }
                }
            }
        }
    }

    /**
     * 创建订单(前面校验方法不加事务，后面保存方法加事务)
     *
     * @param vos
     * @return
     * @throws Throwable
     */
    public CreateOrderFilterVo createOrder(List<CreateOrderVo> vos) {
        ThreadLocalUtils.setFilterTips(new ArrayList<>());
        CreateOrderFilterVo createOrderODTO = new CreateOrderFilterVo(true, null, null);
        if (null == vos || vos.isEmpty()) {
            return createOrderODTO;
        }

        // 检查客户状态  t_store.store_status(0启用,1停用,-1刚创建了客户,待指定线路,2待指定结账客户)
        this.checkStoreStatus(vos.get(0).getStoreId(), "当前客户已停用，不允许下单");

        //下单(购物车下单),如果没有商品则跳过该购物车
        CreateOrderVo vo = vos.get(0); // 一次就一个购物车
        if (!vo.getInternal()
                && StringUtils.isNotBlank(vo.getDeliveryBatch())
                && DeliveryBatchTypeEnum.REPLENISHMENT.getCode().equals(Integer.valueOf(vo.getDeliveryBatch()))) {
            QYAssert.isTrue(false, "你没有权限选择补货批次");
        }
        if (vo.getInternal() && StringUtils.isNotBlank(vo.getDeliveryBatch())
                && (DeliveryBatchTypeEnum.REPLENISHMENT.getCode() + "").equals(vo.getDeliveryBatch())) {
            ThreadLocalUtils.setOrderType(OrderTypeEnum.AGENT_REPLENISH_ORDER.getCode());
        }

        if (null != vo.getItems() && !vo.getItems().isEmpty()) {
            ThreadLocalUtils.set(vo.getInternal());

            // 构建订单信息
            buildShoppingCartToOrder(vo);

            // 设置商品的编码并去重复
            List<FilterTipVo> filterTipList = distinctFilterTips();
            createOrderODTO.setFilterTips(filterTipList);
        }

        ThreadLocalUtils.remove();
        return createOrderODTO;
    }

    /**
     * 检查t_store.store_status(0启用,1停用,-1刚创建了客户,待指定线路,2待指定结账客户)
     *
     * @param storeId
     */
    public void checkStoreStatus(Long storeId, String remark) {
        List<Store> storeList = storeService.findStoreListByStoreIdList(Arrays.asList(storeId));
        QYAssert.isTrue(SpringUtil.isNotEmpty(storeList), "storeId错误");
        Store store = storeList.get(0);
        Integer storeStatus = store.getStoreStatus();
        QYAssert.isTrue(!StoreOpenStatusEnum.DISABLE.getCode().equals(storeStatus), remark);
    }


    public List<FilterTipVo> distinctFilterTips() {
        List<FilterTipVo> filterTips = ThreadLocalUtils.getFilterTips();
        List<FilterTipVo> filterTipList = filterTips.stream().distinct().collect(Collectors.toList());
        if (SpringUtil.isNotEmpty(filterTipList)) {
            List<String> commodityIdList = filterTipList.stream().map(item -> item.getCommodityId().toString()).collect(Collectors.toList());
            List<CommodityInfoEntry> commodityList = commodityMapper.findCommodityInfo(commodityIdList);
            Map<Long, CommodityInfoEntry> idAndObjMap = commodityList.stream().collect(Collectors.toMap(CommodityInfoEntry::getId, Function.identity()));
            filterTipList.forEach(item -> {
                CommodityInfoEntry value = idAndObjMap.get(item.getCommodityId());
                if (value != null) {
                    item.setCommodityCode(value.getCommodityCode());
                    item.setCommodityName(value.getCommodityName());
                }
            });
        }
        return filterTipList;
    }

    /**
     * 将过滤出来的购物车项（修改订单配置的商品）再次加入购物车
     *
     * @param vo
     * @param filterTips
     */
    public void batchAddShoppingCartForFilterCommodity(CreateOrderVo vo, List<FilterTipVo> filterTips, Long stallId) {
        if (SpringUtil.isEmpty(filterTips)) {
            return;
        }
        // 加入购物车
        List<FilterTipVo> commodityList = filterTips.stream().filter(item -> FilterTipEnum.SETTING_CHANGE.getType().equals(item.getType())).collect(Collectors.toList());
        if (SpringUtil.isNotEmpty(commodityList)) {
            ShoppingCartBatchVo shoppingCartVO = new ShoppingCartBatchVo();
            shoppingCartVO.setCreateId(vo.getCreateId());
            shoppingCartVO.setStoreId(vo.getStoreId());
            shoppingCartVO.setEnterpriseId(vo.getEnterpriseId());
            shoppingCartVO.setInternal(vo.getInternal());
            shoppingCartVO.setOrderTime(vo.getOrderTime());
            shoppingCartVO.setDeliveryBatch(vo.getDeliveryBatch());

            Boolean ifAdmin = ThreadLocalUtils.getAdmin();
            if (ifAdmin) {
                List<DeliveryBatchEntry> deliveryBatchList = shoppingCartMapper.findDeliveryBatchByCode(null);
                Map<String, String> deliveryBatchMap = deliveryBatchList.stream().collect(Collectors.toMap(DeliveryBatchEntry::getOptionCode, DeliveryBatchEntry::getOptionName, (key1, key2) -> key2));
                shoppingCartVO.setDeliveryBatch(deliveryBatchMap.get(vo.getDeliveryBatch()));
            }

            List<ShoppingCartBatchListVo> list = new ArrayList<>();
            commodityList.forEach(item -> {
                ShoppingCartBatchListVo shopingCartItem = new ShoppingCartBatchListVo();
                shopingCartItem.setCommodityId(item.getCommodityId());
                shopingCartItem.setQuantity(item.getQuantity());
                list.add(shopingCartItem);
            });
            shoppingCartVO.setList(list);
            shoppingCartVO.setStallId(stallId);
            shoppingCartService.batchAddShoppingCart(shoppingCartVO);
        }
    }


    /**
     * 将配置变化的购物车删除并重新加入购物车（自动订货-->提交审核）
     * 将过滤出来的购物车项（修改订单配置的商品）再次加入购物车
     *
     * @param
     */
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRES_NEW)
    public void batchAddShoppingCartForFilterCommodityAndDelete(ShoppingCart shoppingCart, List<FilterTipVo> commodityList) {
        //删除购物车及明细
        Example ex = new Example(ShoppingCartItem.class);
        ex.createCriteria().andEqualTo("shoppingCartId", shoppingCart.getId());

        this.shoppingCartItemMapper.deleteByExample(ex);
        this.shoppingCartMapper.deleteByPrimaryKey(shoppingCart.getId());

        ShoppingCartBatchVo shoppingCartVO = new ShoppingCartBatchVo();
        shoppingCartVO.setCreateId(shoppingCart.getCreateId());
        shoppingCartVO.setStoreId(shoppingCart.getStoreId());
        shoppingCartVO.setEnterpriseId(shoppingCart.getEnterpriseId());
        shoppingCartVO.setInternal(true);
        shoppingCartVO.setOrderTime(DateTimeUtil.defaultDeliveryDate());
        shoppingCartVO.setDeliveryBatch(shoppingCart.getDeliveryBatch());

        List<ShoppingCartBatchListVo> list = new ArrayList<>();
        commodityList.forEach(item -> {
            ShoppingCartBatchListVo shopingCartItem = new ShoppingCartBatchListVo();
            shopingCartItem.setCommodityId(item.getCommodityId());
            shopingCartItem.setQuantity(item.getQuantity());
            list.add(shopingCartItem);
        });
        shoppingCartVO.setList(list);
        shoppingCartVO.setStallId(shoppingCart.getStallId());
        shoppingCartService.batchAddShoppingCart(shoppingCartVO);

    }

    public String findStoreOrderTimeByStoreId(String storeId) {
        if (StringUtils.isBlank(storeId)) {
            return "";
        }
        StoreDurationEntry sd = this.orderMapper.findStoreDurationByStoreId(storeId);
        if (null == sd) {
            return "";
        }
        return sd.getBeginTime() + "-" + sd.getEndTime();
    }

    //商品明细中有驳回的则,退回到收货
    private boolean reject(PreOrder preOrder, PreOrderRequestVo orderRequestVo) {
        boolean b = false;
        for (int i = 0; i < orderRequestVo.getItemsList().size(); i++) {
            PreSaveOrderItemVo p = orderRequestVo.getItemsList().get(i);
            if (p.getStatus().intValue() == 1) {
                b = true;
                break;
            }
        }
        return b;
    }

    @Transactional(rollbackFor = Exception.class)
    public boolean preOrderCheckSuucess(PreOrderRequestVo orderRequestVo) throws Throwable {
        QYAssert.isTrue(null != orderRequestVo.getPreOrderId(), "预订单id不能为空!");
        QYAssert.isTrue(SpringUtil.isNotEmpty(orderRequestVo.getItemsList()), "收货明细不能为空");
        for (PreSaveOrderItemVo preSaveOrderItemVo : orderRequestVo.getItemsList()) {
            QYAssert.isTrue(preSaveOrderItemVo.getProductNum() != null, "请填写实收数量");
        }
        PreOrder preOrder = this.preOrderMapper.selectByPrimaryKey(orderRequestVo.getPreOrderId());
        orderRequestVo.setStoreId(preOrder.getStoreId() == null ? null : preOrder.getStoreId().toString());
        QYAssert.isTrue(null != preOrder, "该预订单有误,无法生成订单!");
        QYAssert.isTrue(orderRequestVo != null && SpringUtil.isNotEmpty(orderRequestVo.getItemsList()), "无有效商品,请检查!");
        QYAssert.isTrue(preOrder.getReceiveStatus().intValue() == ReceiveOrderStatusEnums.UNCHECKED.getCode(), "该预订单状态不对");

        Long preOrderId = preOrder.getId();
        // 查询预订单明细
        Example preOrderItemEx = new Example(PreOrderItem.class);
        preOrderItemEx.createCriteria().andEqualTo("preorderId", preOrderId);
        List<PreOrderItem> dbItems = preOrderItemMapper.selectByExample(preOrderItemEx);

        boolean isReject = reject(preOrder, orderRequestVo);
        if (isReject) {
            // 审核不通过，添加商品判断必须是档口下面的
            addCommStallCommodityCheck(preOrder, orderRequestVo.getItemsList());

            // 修改门店库存
            this.processShopStock(preOrder.getStallId(), isReject, preOrder.getOrderCode(), dbItems, orderRequestVo.getItemsList(), orderRequestVo.getStoreId(), orderRequestVo.getUserId());

            // 处理预订单
            processPreOrder(preOrder, orderRequestVo.getItemsList(), orderRequestVo.getUserId(), ReceiveOrderStatusEnums.REJECT.getCode());
            return true;
        }
        orderRequestVo.setShopId(this.preOrderMapper.finsShopIdByStore(Long.valueOf(orderRequestVo.getStoreId())));

        // 审核通过，添加商品判断必须是档口下面的
        addCommStallCommodityCheck(preOrder, orderRequestVo.getItemsList());

        //修改门店库存
        this.processShopStock(preOrder.getStallId(), isReject, preOrder.getOrderCode(), dbItems, orderRequestVo.getItemsList(), orderRequestVo.getStoreId(), orderRequestVo.getUserId());

        // 处理预订单
        this.processPreOrder(preOrder, orderRequestVo.getItemsList(), orderRequestVo.getUserId(), ReceiveOrderStatusEnums.PASS.getCode());

        // 如果实收数量均为0则不生成订单
        Double sumProductNum = orderRequestVo.getItemsList().stream().collect(Collectors.summingDouble(item -> Double.valueOf(item.getProductNum().toString())));
        if (sumProductNum == 0) {
            return true;
        }

        orderRequestVo.setPurchaseOrderId(preOrder.getPurchaseOrderId());
        orderRequestVo.setPurchaseCode(preOrder.getPurchaseCode());
        this.processOutInBill(orderRequestVo);

        Date date = new Date();
        OrderDto orderDto = new OrderDto();
        orderDto.setSupplierId(orderRequestVo.getSupplierId());
        orderDto.setUserId(preOrder.getCreateId());
        SpringUtil.copyProperties(orderRequestVo, orderDto);
        orderDto.setOrderTime(DateTimeUtil.formatDate(date, "yyyy-MM-dd"));
        orderDto.setStoreId(Long.valueOf(orderRequestVo.getStoreId()));
        orderDto.setItems(new ArrayList<OrderItemDto>());
        orderRequestVo.getItemsList().forEach(i -> {
            OrderItemDto orderItemDto = new OrderItemDto(i.getProductId(), i.getProductPrice(), i.getProductNum(), "", ProductTypeEnums.PRODUCT.getCode());
            orderItemDto.setOriginalPrice(orderItemDto.getPrice());
            orderDto.addItem(orderItemDto);
        });
        orderDto.setGiftItems(orderDto.getItems());
        //预订单转订单，不需要限制包装类型、速冻、价格与预订单明细一致;
//		this.checkProductPackedType(orderDto);

//		this.checkStoreFrozenProduct(orderDto);
//		//促销特价限量
//		this.buildPreProductPrice(orderDto);
        //
        this.processCommodityPrice(orderDto);

        QYAssert.isTrue(!orderDto.getItems().isEmpty(), "有效商品不能为空 ...");
        QYAssert.isTrue(orderDto.amount().compareTo(BigDecimal.ZERO) > 0, "订单金额必须大于0...");

        // 直送审核通过，生成正式B端订单
        //但之前货物已经到了门店，属于预售类，故订单应该加上“预售”标识，无须进大仓处理
        orderDto.getItems().forEach(dto ->{
            dto.setPresaleStatus(YesOrNoEnums.YES.getCode());
        });
        orderDto.setStallId(preOrder.getStallId());

        // 直送单审核通过是直送单
        orderDto.setDirectStatus(YesOrNoEnums.YES.getCode());
        Order o = this.saveOrder(orderDto, true, false);
        Order kafkaOrder = this.saveGiftOrder(orderDto, o.getId(), true);

        preOrder.setReceiveStatus(3);
        preOrder.setUpdateTime(date);
        preOrder.setUpdateId(orderRequestVo.getUserId());
        preOrder.setOrderId(o.getId());
        preOrder.setRealOrderCode(o.getOrderCode());
        preOrder.setApprovingId(orderRequestVo.getUserId());
        preOrder.setApprovingTime(date);
        int rowNumber = this.preOrderMapper.updateByPrimaryKey(preOrder);

        // 记录日志
        Order order = this.orderMapper.findOrderInfo(o.getOrderCode());
        Integer operationType = OrderModeType.DRIECT_SENDING_BACKORDER.getCode().equals(orderRequestVo.getModeType()) ?
                OperationTypeEnum.DIRECT_SENDING_BACK_ORDER.getCode() : OperationTypeEnum.PRE_ORDER_CHECK.getCode();
        this.inserOrderHistory(order.getId(), Long.valueOf(o.getOrderCode()), order.getCreateId(), order.getCreateUserName(),
                operationType, null, null, null);
        // 发送结算信息kafka消息
        this.fixedSettleOrder(o.getOrderCode(), "ZS_ORDER", KafkaConstant.SETTLE_ORDER_ALL_TOPIC);

        // 直送审核通过发送结算消息
        settleKafkaService.zsOrdersendSettleKafkaMsg(o.getId());
        kafkaOrder.setOrderAmount(orderDto.amount());
        TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronizationAdapter() {
            @Override
            public void afterCommit() {
                orderAsyncKafkaService.sendKafkaSaveOrderMessage(kafkaOrder);
            }
        });
        return rowNumber > 0 ? true : false;
    }


    @Transactional(rollbackFor = Exception.class)
    public void addCommStallCommodityCheck(PreOrder preOrder, List<PreSaveOrderItemVo> itemsList) {
        List<String> commodityIdList = itemsList.stream().map(item -> item.getProductId()).collect(Collectors.toList());
        Map<Long, CommodityInfoEntry> commMap = getCommodityMap(commodityIdList);

        Long stallId = preOrder.getStallId();
        Boolean isBigShop = (stallId != null && stallId > 0);
        List<Long> stallCommodityIdList = new ArrayList<>();
        if(isBigShop){
            Long storeId = preOrder.getStoreId();
            Example shopEx =new Example(Shop.class);
            shopEx.createCriteria().andEqualTo("storeId", storeId);
            Shop shop = shopMapper.selectOneByExample(shopEx);
            stallCommodityIdList = consignmentSupplierService.getCommodityIdListByStallIds(shop.getId(), stallId);
        }

        for (PreSaveOrderItemVo q : itemsList) {
            CommodityInfoEntry commodityInfoEntry = commMap.get(Long.valueOf(q.getProductId()));
            if (q.isAdd()) {
                //新增商品
                if(isBigShop && !stallCommodityIdList.contains(Long.valueOf(q.getProductId()))){
                    QYAssert.isFalse("该档口下，不允许订此商品 " + commodityInfoEntry.getCommodityCode());
                }
            }
        }
    }

    //处理预订单
    @Transactional(rollbackFor = Exception.class)
    public void processPreOrder(PreOrder preOrder, List<PreSaveOrderItemVo> itemsList, Long userId, Integer receiveStatus) {
        Example preOrderItemEx = new Example(PreOrderItem.class);
        preOrderItemEx.createCriteria().andEqualTo("preorderId", preOrder.getId());
        List<PreOrderItem> items = preOrderItemMapper.selectByExample(preOrderItemEx);
        //修改明细数据
        BigDecimal totalPrice = BigDecimal.ZERO;
        List<PreOrderItem> adds = new ArrayList<PreOrderItem>();

        List<String> commodityIdList = itemsList.stream().map(item -> item.getProductId()).collect(Collectors.toList());
        Map<Long, CommodityInfoEntry> commMap = getCommodityMap(commodityIdList);

        for (PreSaveOrderItemVo q : itemsList) {
            CommodityInfoEntry commodityInfoEntry = commMap.get(Long.valueOf(q.getProductId()));
            BigDecimal commodityPackageSpec = commodityInfoEntry.getCommodityPackageSpec();
            if (q.isAdd()) {
                //新增商品
                PreOrderItem p = new PreOrderItem();
                p.setCommodityId(Long.valueOf(q.getProductId()));
                p.setCreateId(userId);
                p.setIsAdd(true);
                p.setPreorderId(preOrder.getId());
                p.setPrice(q.getProductPrice());
                p.setRealReceiveQuantity(q.getProductNum());
                p.setRealReceiveNumber(q.getProductNum().divide(commodityPackageSpec, 0, BigDecimal.ROUND_UP).intValue());
                p.setRequireQuantity(BigDecimal.ZERO);
                p.setStatus(2);
                p.setRejectReason(q.getRejectReason());
                p.setTotalPrice(q.getProductPrice().multiply(q.getProductNum()));
                totalPrice = totalPrice.add(p.getTotalPrice());
                p.setUpdateId(userId);
                p.setType(1);
                if(ReceiveOrderStatusEnums.PASS.getCode() == receiveStatus){
                    p.setCheckQuantity(q.getProductNum());
                }
                adds.add(p);
            } else {
                for (PreOrderItem item : items) {
                    if (item.getId().toString().equals(q.getItemId())) {
                        item.setPrice(q.getProductPrice());
                        item.setRealReceiveQuantity(q.getProductNum());
                        item.setRealReceiveNumber(q.getProductNum().divide(commodityPackageSpec, 0, BigDecimal.ROUND_UP).intValue());
                        item.setTotalPrice(item.getPrice().multiply(item.getRealReceiveQuantity()));
                        item.setTotalPrice(item.getTotalPrice().setScale(2, BigDecimal.ROUND_HALF_UP));
                        //赠品不算在总计金额里;
                        if (item.getType().intValue() != 2) {
                            totalPrice = totalPrice.add(item.getTotalPrice());
                        }
                        item.setStatus(q.getStatus());
                        item.setRejectReason(q.getRejectReason());
                        item.setUpdateId(userId);
                        item.setUpdateTime(new Date());
                        if(ReceiveOrderStatusEnums.PASS.getCode() == receiveStatus){
                            item.setCheckQuantity(q.getProductNum());
                        }
                        preOrderItemMapper.updateByPrimaryKey(item);
                    }
                }
            }
        }
        if (SpringUtil.isNotEmpty(adds)) {
            preOrderItemMapper.insertList(adds);
        }
        //修改预订单表
        preOrder.setReceiveStatus(receiveStatus);
        preOrder.setUpdateTime(new Date());
        preOrder.setUpdateId(userId);
        preOrder.setTotalPrice(totalPrice);
        preOrder.setApprovingTime(new Date());
        preOrder.setApprovingId(userId);
        preOrderMapper.updateByPrimaryKey(preOrder);
    }


    //处理门店库存
    @Transactional(rollbackFor = Exception.class)
    public void processShopStock(Long stallId, Boolean isReject, String preOrderCode, List<PreOrderItem> dbItems, List<PreSaveOrderItemVo> itemsList, String storeId, Long userId) throws Throwable {
        QYAssert.isTrue(null != storeId, "storeId is not null!");
        QYAssert.isTrue(null != userId, "userId is not null!");

        // 获取门店id
        Example ex = new Example(Shop.class);
        ex.createCriteria().andEqualTo("storeId", storeId);
        List<Shop> shopList = shopMapper.selectByExample(ex);
        Long shopId = shopList.get(0).getId();

        Boolean isBigShop = (stallId != null && stallId > 0);
        List<ShopStockCommodityIDto> stockList = new ArrayList<>();
        //判断是否有驳回
        //boolean isReject = isReject(itemsList);
        // 查询商品包装规
        List<String> commodityIdList = itemsList.stream().map(item -> item.getProductId()).collect(Collectors.toList());
        Map<Long, BigDecimal> commodityPackageSpecMap = shopReceiveService.getCommodityPackageSpecMap(commodityIdList);
        for (PreSaveOrderItemVo q : itemsList) {
            if (q.isAdd()) {
                //新增商品
                ShopStockCommodityIDto shopStockI = new ShopStockCommodityIDto();
                shopStockI.setModifyQuantity(q.getProductNum());
                // 设置份数
                BigDecimal commodityPackageSpec = commodityPackageSpecMap.get(Long.valueOf(q.getProductId()));
                shopStockI.setModifyNumber(shopStockI.getModifyQuantity().divide(commodityPackageSpec, 0, BigDecimal.ROUND_UP).intValue());
                shopStockI.setCommodityId(Long.valueOf(q.getProductId()));
                //加权价
                shopStockI.setPrice(q.getProductPrice());
                shopStockI.setTotalPrice(q.getProductPrice().multiply(q.getProductNum()));
                stockList.add(shopStockI);
            } else {
                //已存在的
                dbItems.forEach(i -> {
//					if(i.getCommodityId().toString().equals(q.getProductId())){
                    if (i.getId().toString().equals(q.getItemId())) {
                        ShopStockCommodityIDto shopStockI = new ShopStockCommodityIDto();
                        BigDecimal realReceiveQuantity = null == i.getRealReceiveQuantity() ? BigDecimal.ZERO : i.getRealReceiveQuantity();
                        if (isReject) {
                            shopStockI.setModifyQuantity(realReceiveQuantity.multiply(new BigDecimal(-1)));
                        } else {
                            shopStockI.setModifyQuantity(q.getProductNum().subtract(realReceiveQuantity));
                        }
                        // 设置份数
                        BigDecimal commodityPackageSpec = commodityPackageSpecMap.get(Long.valueOf(q.getProductId()));
                        shopStockI.setModifyNumber(shopStockI.getModifyQuantity().divide(commodityPackageSpec, 0, BigDecimal.ROUND_UP).intValue());
                        shopStockI.setCommodityId(Long.valueOf(q.getProductId()));
                        //加权价
                        shopStockI.setPrice(q.getProductPrice());
                        shopStockI.setTotalPrice(q.getProductPrice().multiply(q.getProductNum()).subtract(i.getPrice().multiply(realReceiveQuantity)));
                        stockList.add(shopStockI);
                    }
                });
            }
        }
        if (SpringUtil.isNotEmpty(stockList)) {
            List<StockReceiptItemDTO> commodityList = new ArrayList<>();
            for (ShopStockCommodityIDto iDto : stockList) {
                StockReceiptItemDTO stockReceiptItemDTO = new StockReceiptItemDTO();
                stockReceiptItemDTO.setCommodityId(Long.valueOf(iDto.getCommodityId()));
                stockReceiptItemDTO.setNumber(iDto.getModifyNumber());
                stockReceiptItemDTO.setNormalNumber(stockReceiptItemDTO.getNumber());
                stockReceiptItemDTO.setAbnormalNumber(0);
                stockReceiptItemDTO.setQuantity(iDto.getModifyQuantity());
                stockReceiptItemDTO.setNormalQuantity(stockReceiptItemDTO.getQuantity());
                stockReceiptItemDTO.setAbnormalQuantity(new BigDecimal(0));
                stockReceiptItemDTO.setPrice(iDto.getPrice());
                stockReceiptItemDTO.setTotalPrice(iDto.getTotalPrice());
                commodityList.add(stockReceiptItemDTO);

                if(isBigShop){
                    DdStockInOutExtraIDTO ddStockInOutExtraIDTO = new DdStockInOutExtraIDTO();
                    ddStockInOutExtraIDTO.setCommodityId(Long.valueOf(iDto.getCommodityId()));
                    ddStockInOutExtraIDTO.setStallId(stallId);
                    ddStockInOutExtraIDTO.setStorageArea(StorageAreaEnum.SHELF_AREA.getCode());
                    stockReceiptItemDTO.setDdStockInOutExtraVO(ddStockInOutExtraIDTO);
                }
            }

            //预订单审核走wms
            StockReceiptIDTO stockReceiptIDTO = new StockReceiptIDTO();
            stockReceiptIDTO.setWarehouseId(shopId);
            stockReceiptIDTO.setReferId(Long.valueOf(preOrderCode));
            stockReceiptIDTO.setReferCode(preOrderCode);
            stockReceiptIDTO.setUserId(userId);
            stockReceiptIDTO.setStockEnums(StockInOutTypeEnums.IN_PREORDER_NORMAL);
            stockReceiptIDTO.setCommodityList(commodityList);
            xdStockClient.stockReceipt(stockReceiptIDTO);
        }
    }

    //处理出入库单
    @Transactional(rollbackFor = Exception.class)
    public void processOutInBill(PreOrderRequestVo orderRequestVo) throws Throwable {

        Date date = new Date();
        StockInOrderIDTO inOrder = new StockInOrderIDTO();

        inOrder.setCreateId(orderRequestVo.getUserId());
        inOrder.setOrderCode(codeClient.createCode("STOCK_IN_CODE"));
        inOrder.setCreateTime(date);
        inOrder.setEnterpriseId(orderRequestVo.getEnterpriseId());

        if (null != orderRequestVo.getPurchaseOrderId()) {
            PurchaseOrderODto purchaseOrderODto = purchaseOrderClient.queryPurchaseOrderByCode(orderRequestVo.getPurchaseCode());
            if (null != purchaseOrderODto) {
                inOrder.setReferId(purchaseOrderODto.getId());
                inOrder.setReferCode(purchaseOrderODto.getPurchaseCode());
            } else {
                inOrder.setReferId(-1L);
                inOrder.setReferCode("");
            }
        } else {
            inOrder.setReferId(-1L);
            inOrder.setReferCode("");
        }
        inOrder.setStorageType(1);
        inOrder.setWarehouseId(0L);
        inOrder.setReceiverId(orderRequestVo.getReceiverId());
        inOrder.setSupplierId(orderRequestVo.getSupplierId());
        inOrder.setUpdateId(orderRequestVo.getUserId());
        Long stockInOrderId = stockInOrderClient.create(inOrder);

        List<StockInOrderItemIDTO> stockInOrderItems = new ArrayList<>();
        orderRequestVo.getItemsList().forEach(i -> {
            StockInOrderItemIDTO item = new StockInOrderItemIDTO();
            item.setCommodityId(Long.valueOf(i.getProductId()));
            item.setPrice(i.getProductPrice());
            item.setQuantity(i.getProductNum());
            item.setStockInOrderId(stockInOrderId);
            stockInOrderItems.add(item);
        });
        stockInOrderClient.createItem(stockInOrderItems);

        StockOutOrderIDTO outOrder = new StockOutOrderIDTO();
        outOrder.setEnterpriseId(orderRequestVo.getEnterpriseId());
        outOrder.setOrderCode(codeClient.createCode(orderRequestVo.getEnterpriseId(), CodeTypeEnum.STOCK_OUT_CODE));
        outOrder.setReferId(inOrder.getId());
        outOrder.setReferCode(inOrder.getReferCode());
        outOrder.setPickOrderId(-1L);
        if (null != orderRequestVo.getStoreId()) {
            outOrder.setStoreId(Long.valueOf(orderRequestVo.getStoreId()));
        }
        outOrder.setOutputType(3);
        outOrder.setWarehouseId(-1L);
        outOrder.setCreateId(orderRequestVo.getUserId());
        outOrder.setCreateTime(date);
        outOrder.setUpdateId(orderRequestVo.getUserId());
        outOrder.setUpdateTime(date);

        Long stockOutOrderId = stockOutOrderClient.create(outOrder);

        List<StockOutOrderItemIDTO> stockOutOrderItems = new ArrayList<>();
        orderRequestVo.getItemsList().forEach(i -> {
            StockOutOrderItemIDTO item = new StockOutOrderItemIDTO();
            item.setCommodityId(Long.valueOf(i.getProductId()));
            item.setOutStockQuantity(i.getProductNum());
            item.setExpectQuantity(BigDecimal.valueOf(-1));
            item.setStockOutOrderId(stockOutOrderId);
            item.setCommodityPrice(i.getProductPrice());
            stockOutOrderItems.add(item);
        });
        stockOutOrderClient.createItem(stockOutOrderItems);
    }

    @Transactional(rollbackFor = Exception.class)
    public boolean createPurchaseOrderByTime(String times) throws ParseException {
        QYAssert.isTrue(StringUtils.isNotBlank(times), "times is not null !");

        List<String> commodityIds = purchaseOrderMapper.queryCommodityIds(times);
        List<CommoditySupplierODto> supplierEndTimeList = commoditySupplierClient.getCommodityDefaultSupplierList2(commodityIds);

        Calendar c = Calendar.getInstance();
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
        SimpleDateFormat format1 = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        c.add(Calendar.DAY_OF_YEAR, 1);
        String date = format.format(c.getTime());
        StringBuilder str = null;
        //下当天的单,送货日期是明天的;
        if (null != supplierEndTimeList && !supplierEndTimeList.isEmpty() && date.equals(times)) {
            for (int i = 0; i < supplierEndTimeList.size(); i++) {
                CommoditySupplierODto s = supplierEndTimeList.get(i);
                //比较供应商最晚供货时间,如果供应时间晚于现在,不执行
                if (!StringUtils.isBlank(s.getSupplyEndTime())) {
                    str = new StringBuilder(date).append(" ").append(s.getSupplyEndTime()).append(":00");
                    Date temp = format1.parse(str.toString());
                    if (c.getTime().after(temp)) {
                        List<SubOrderEntry> subOrderEntries = purchaseOrderMapper.getSubOrderBeforeDeadlineById(s.getSupplyEndTime(), s.getSupplierId().toString());
                        //预订单 正常状态
                        if (!SpringUtil.isEmpty(subOrderEntries)) {
                            subOrderEntries.forEach(j -> {
                                if (null != j.getOrderStatus() && j.getOrderStatus().intValue() == 1) {
                                    purchaseService.generatePurchaseOrder(j);
                                }
                            });
                        }
                    }
                }
            }
        } else {
            //不是当天的,是之前的数据
            str = new StringBuilder(date).append(" 00:00:00");
            Date now = format1.parse(str.toString());

            str = new StringBuilder(times).append(" 00:00:00");
            Date tiemsDate = format1.parse(str.toString());
            if (now.after(tiemsDate)) {
                for (int i = 0; i < supplierEndTimeList.size(); i++) {
                    CommoditySupplierODto s = supplierEndTimeList.get(i);
                    List<SubOrderEntry> subOrderEntries = purchaseOrderMapper.getSubOrderByDateAndId(times, s.getSupplierId().toString());
                    //预订单 正常状态
                    if (!SpringUtil.isEmpty(subOrderEntries)) {
                        subOrderEntries.forEach(j -> {
                            if (null != j.getOrderStatus() && j.getOrderStatus().intValue() == 1) {
                                purchaseService.generatePurchaseOrder(j);
                            }
                        });
                    }
                }
            }
        }
        return true;
    }

    public PageInfo<OutstandingShopEntry> findOutstandingShopList(OutstandingShopVo vo) {
        if (StringUtils.isBlank(vo.getOrderTime())) {
            vo.setOrderTime(LocalDate.now().plusDays(1).toString());
        }
        PageInfo<OutstandingShopEntry> pageInfo = PageHelper.startPage(vo.getPageNo(), vo.getPageSize()).doSelectPageInfo(
                () -> orderMapper.findOutstandingShopList(vo.getEmployeeId(), vo.getOrderTime())
        );
        return pageInfo;
    }


    /**
     * 判断订单中的商品是否可变价格，只要订单中有一个商品是可变价的，那么这个订单就是可变价的
     *
     * @param orderDto
     * @return
     */
    private Integer changePriceStatus(OrderDto orderDto) {
        List<String> commodityIds = new ArrayList();
        orderDto.getItems().forEach(item -> {
            commodityIds.add(item.getProductId());
        });

        List<MdShopOrderSettingEntry> shopOrderSettingODTOs = mdShopOrderSettingService.queryMdShopOrderSettingListByCommodityIds(commodityIds, orderDto.getStoreId());
        if (CollectionUtils.isEmpty(shopOrderSettingODTOs)) {
            Example ex = new Example(Commodity.class);
            ex.createCriteria().andIn("id", commodityIds);
            List<Commodity> commList = commodityMapper.selectByExample(ex);
            StringBuffer sb = new StringBuffer("");
            commList.forEach(comm -> {
                sb.append(comm.getCommodityCode() + " ");
            });
            // 默认供应商发生变化后，更新门店订货通用设置默认供应商
            mdShopOrderSettingService.updateShopOrderSettingSupplier(Long.valueOf(commodityIds.get(0)), orderDto.getStoreId());

            QYAssert.isTrue(false, "没有查询到相应的店铺订单设置~" + sb.toString());
        }
        //QYAssert.isTrue(shopOrderSettingODTOs != null && !shopOrderSettingODTOs.isEmpty(), "没有查询到相应的店铺订单设置-" + commodityIds + "-" + orderDto.getStoreId());
        Integer changePriceStatus = YesOrNoEnums.NO.getCode();
        for (MdShopOrderSettingEntry item : shopOrderSettingODTOs) {
            if (YesOrNoEnums.YES.getCode().equals(item.getChangePriceStatus())) {
                changePriceStatus = YesOrNoEnums.YES.getCode();
                break;
            }
        }

        return changePriceStatus;
    }

    public PageInfo<ModifyDeliverDateEntry> queryModifyDeliverDateList(ModifyDeliverDateVo vo) {
        Date orderTime = DateUtil.addDay(new Date(), 1);
        String dateFormate = DateUtil.getDateFormate(orderTime, "yyyy-MM-dd");
        vo.setOrderTime2(dateFormate);
        PageInfo<ModifyDeliverDateEntry> pageData = PageHelper.startPage(vo.getPageNo(), vo.getPageSize()).doSelectPageInfo(() -> {
            this.orderMapper.queryModifyDeliverDateList(vo);
        });

        List<Long> orderIds = new ArrayList<>();
        pageData.getList().forEach(entry -> {
            orderIds.add(entry.getOrderId());
        });

        Map<Long, Boolean> map = deliveryOrderClient.checkOrderGeneratePickOrder(orderIds);

        pageData.getList().forEach(entry -> {
            for (Map.Entry<Long, Boolean> delivery : map.entrySet()) {
                if (entry.getOrderId().equals(delivery.getKey())) {
                    entry.setDeliveryStatus(delivery.getValue() == true ? "已发货" : "未发货");
                }
            }
        });

        return pageData;
    }

    /**
     * 修改发货时间
     *
     * @param orderCode
     * @param dateStr
     */
    @Transactional(rollbackFor = Exception.class)
    public void modifyDeliverDate(String orderCode, String dateStr) {
        // 0. 参数检查
        QYAssert.isTrue(StringUtil.isNotEmpty(orderCode), "订单号不为空");
        QYAssert.isTrue(StringUtil.isNotEmpty(dateStr), "日期不能为空");
        Date modifyDate = DateUtil.parseDate(dateStr, "yyyy-MM-dd");
        Date now = null;
        try {
            now = DateUtil.getDatePart(new Date());
        } catch (ParseException e) {

        }
        int differ = DateUtil.getDayDif(modifyDate, now);
        QYAssert.isTrue(differ >= 0, "修改后的送货日期>=T");

        ModifyDeliverDateVo vo = new ModifyDeliverDateVo();
        vo.setOrderCode(orderCode);
        Date orderTime = DateUtil.addDay(new Date(), 1);
        String dateFormate = DateUtil.getDateFormate(orderTime, "yyyy-MM-dd");
        vo.setOrderTime2(dateFormate);
        List<ModifyDeliverDateEntry> modifyDeliverDateEntries = this.orderMapper.queryModifyDeliverDateList(vo);
        QYAssert.isTrue(SpringUtil.isNotEmpty(modifyDeliverDateEntries), "该订单不允许修改送货日期");
        ModifyDeliverDateEntry modifyDeliverDateEntry = modifyDeliverDateEntries.get(0);
        QYAssert.isTrue(modifyDeliverDateEntry.getReferOrderId() == null, "发货单已生成，不能修改送货时间");

        Order order = this.orderMapper.findOrderInfo(orderCode);

        // 1. 修改t_order.order_time
        this.orderMapper.modifyDeliverDate(orderCode, dateStr);

        // 2. 修改t_sub_order.order_time
        this.subOrderMapper.modifyDeliverDate(orderCode, dateStr);

        // 3. 记录日志
        this.inserOrderHistory(order.getId(), Long.valueOf(orderCode), order.getCreateId(), order.getCreateUserName(),
                OperationTypeEnum.MODIFY_DELIVER_DATE.getCode(), null, DateUtil.get4yMd(order.getOrderTime()), dateStr);


        // 发消息给统计查询,修改订单
        Order kafkaOrder = orderMapper.selectByPrimaryKey(order.getId());
        orderAsyncKafkaService.sendKafkaUpdateOrderMessage(kafkaOrder);
    }

    @Transactional(rollbackFor = Exception.class)
    public PreOrder createPreOrder(DirectSendingBackOrderVo backOrderVo) {
        // 查询storeId
        ShopDto shopDto = shopClient.findShopById(backOrderVo.getShopId());
        QYAssert.isTrue(shopDto != null, "未查询到该门店");
        backOrderVo.setStoreId(Long.valueOf(shopDto.getStoreId()));

        if(StallUtils.isStallSubcontractor(shopDto.getManagementMode())){
            QYAssert.isFalse("大店不允许直送补单");
        }

        // 查询商品信息
        backOrderVo.getItems().forEach(item -> {
            ShopCommodityDetailODTO shopCommodityDetailODTO = shopCommodityClient.queryCommodityDetail(Long.valueOf(item.getProductId()), backOrderVo.getShopId(), backOrderVo.getEnterpriseId());
            QYAssert.isTrue(shopCommodityDetailODTO != null, "未查询到该商品");
            item.setCommodityCode(shopCommodityDetailODTO.getCommodityCode());
            item.setProductName(shopCommodityDetailODTO.getCommodityName());
        });

        // 1. 创建预订单(t_md_preorder、t_md_preorder_item)
        PreOrder preOrder = null;
        try {
            OrderDto orderDto = this.buildOrderDto(backOrderVo);
            preOrder = this.savePreOrder(orderDto);
        } catch (Throwable throwable) {
            log.error("直送补单创建失败:{}", backOrderVo);
        }

        return preOrder;
    }

    @Transactional(rollbackFor = Exception.class)
    public void deletePreOrder(Long preOrderId) {
        PreOrder preOrder = new PreOrder();
        preOrder.setId(preOrderId);
        this.preOrderMapper.delete(preOrder);

        PreOrderItem preOrderItem = new PreOrderItem();
        preOrderItem.setPreorderId(preOrderId);
        this.preOrderItemMapper.delete(preOrderItem);
    }

    /**
     * 直送单补货
     *
     * @param backOrderVo
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean createDirectSendingBackOrder(DirectSendingBackOrderVo backOrderVo, PreOrder preOrder) throws Throwable {
        QYAssert.isTrue(backOrderVo != null && preOrder != null, "参数不能为空");
        Long preOrderId = preOrder.getId();
        OrderDto orderDto = this.buildOrderDto(backOrderVo);
//        this.modifyOrderedQuantity(preOrderId, orderDto);

        // 2. 直送预订单收货
        ReceiveOrderVo receiveOrderVo = this.buildReceiveOrderIDTO(backOrderVo, preOrderId);
        shopReceiveService.addPreReceive(receiveOrderVo);

        ThreadLocalUtils.setOrderType(OrderTypeEnum.DIRECT_REPLENISH_ORDER.getCode());

        // 3. 审核预订单(内部调用this.saveOrder(orderDto) t_order)
        PreOrderRequestVo preOrderRequestVo = this.buildPreOrderRequestVo(backOrderVo, preOrder);
        this.preOrderCheckSuucess(preOrderRequestVo);

        // 4. 生成采购单(t_dc_purchase_order/t_dc_purchase_order_item)和采购入库单
        // 查询预订单
        SubOrderEntry entry = purchaseOrderMapper.getSubOrderBeforeDeadlineByPreorderId(preOrderId);
        entry.setModeType(OrderModeType.DRIECT_SENDING_BACKORDER.getCode());
        purchaseService.generatePurchaseOrder(entry);

        ThreadLocalUtils.remove();
        return true;
    }

    private PreOrderRequestVo buildPreOrderRequestVo(DirectSendingBackOrderVo backOrderVo, PreOrder preOrder) {
        PreOrderRequestVo preOrderRequestVo = new PreOrderRequestVo();
        // 3: 直送补单
        preOrderRequestVo.setModeType(OrderModeType.DRIECT_SENDING_BACKORDER.getCode());
        preOrderRequestVo.setEnterpriseId(backOrderVo.getEnterpriseId());
        preOrderRequestVo.setPrintType(3);
        preOrderRequestVo.setPrintNum(1);
        preOrderRequestVo.setModeType(OrderModeType.DRIECT_SENDING_BACKORDER.getCode());
        preOrderRequestVo.setUserId(backOrderVo.getUserId());
        preOrder.setSupplierId(backOrderVo.getSupplierId());
        preOrderRequestVo.setSupplierId(backOrderVo.getSupplierId());
        preOrderRequestVo.setPreOrderId(preOrder.getId());
        preOrderRequestVo.setReceiverId(backOrderVo.getUserId());
        List<PreSaveOrderItemVo> itemsList = new ArrayList<>();
        backOrderVo.getItems().forEach(item -> {
            PreOrderItem condition = new PreOrderItem();
            condition.setPreorderId(preOrder.getId());
            condition.setCommodityId(Long.valueOf(item.getProductId()));
            PreOrderItem preOrderItem = this.preOrderItemMapper.selectOne(condition);

            PreSaveOrderItemVo preSaveOrderItemVo = new PreSaveOrderItemVo();
            preSaveOrderItemVo.setItemId(preOrderItem.getId().toString());
            preSaveOrderItemVo.setProductId(item.getProductId());
            preSaveOrderItemVo.setProductNum(item.getProductNum());
            preSaveOrderItemVo.setProductPrice(item.getPrice());
            preSaveOrderItemVo.setAdd(false);
            preSaveOrderItemVo.setStatus(2);

            itemsList.add(preSaveOrderItemVo);
        });
        preOrderRequestVo.setItemsList(itemsList);
        return preOrderRequestVo;
    }

    private OrderDto buildOrderDto(DirectSendingBackOrderVo backOrderVo) {
        // 供应商固定为“水果批发市场”（供应商编码=00074）
        SupplierODTO supplierODTO = supplierClient.getSupplierByCode("00074");
        QYAssert.isTrue(supplierODTO != null, "'水果批发市场'供应商不存在");
        Long supplierId = supplierODTO.getId();

        Long warehouseId = 9999L;
        backOrderVo.setSupplierId(warehouseId);

        OrderDto orderDto = new OrderDto();
        // 送货日期固定为：今天
        orderDto.setOrderTime(DateUtil.get4yMd(new Date()) + " 00:00:00");
        orderDto.setOrderRemark("直送补单");
        // storeId为门店的id
        orderDto.setStoreId(backOrderVo.getStoreId());
        orderDto.setEnterpriseId(backOrderVo.getEnterpriseId());
        orderDto.setUserId(backOrderVo.getUserId());
        // 打印类型(1：本地,2：送货员,3：不打印)
        orderDto.setPrintType(3);
        orderDto.setPrintNum(1);
        // 0:普通订单 1：补货订单(直送订单好像用不到该值)
        orderDto.setModeType(OrderModeType.DRIECT_SENDING_BACKORDER.getCode());
        orderDto.setItems(backOrderVo.getItems());
        orderDto.setGiftItems(backOrderVo.getItems());
        // 物流模式：0=直送
        orderDto.setLogisticsModel(0);
        // 供应商固定为“水果批发市场”（供应商编码=00074）
        orderDto.setSupplierId(supplierId);
        // 仓库固定为：直送仓库
        orderDto.setWarehouseId(warehouseId);
        orderDto.setDeliveryBatch("0");

        return orderDto;
    }

    private ReceiveOrderVo buildReceiveOrderIDTO(DirectSendingBackOrderVo backOrderVo, Long preOrderId) {
        ReceiveOrderVo receiveOrderVo = new ReceiveOrderVo();

        receiveOrderVo.setStoreId(backOrderVo.getStoreId().toString());
        receiveOrderVo.setReceiveTime(new Date());
        receiveOrderVo.setReceiveId(backOrderVo.getUserId().toString());
        receiveOrderVo.setLogisticsModel(0);
        receiveOrderVo.setPreOrderId(preOrderId.toString());
        receiveOrderVo.setCreateId(backOrderVo.getUserId());
        receiveOrderVo.setShopId(backOrderVo.getShopId());

        // 查询商品包装规格
        List<String> commodityIdList = backOrderVo.getItems().stream().map(item -> item.getProductId()).collect(Collectors.toList());
        Map<Long, BigDecimal> commodityPackageSpecMap = getCommodityPackageSpecMap(commodityIdList);
        List<Quantity> receiveQuantities = new ArrayList<>();
        backOrderVo.getItems().forEach(item -> {
            PreOrderItem condition = new PreOrderItem();
            condition.setPreorderId(preOrderId);
            condition.setCommodityId(Long.valueOf(item.getProductId()));
            PreOrderItem preOrderItem = this.preOrderItemMapper.selectOne(condition);

            Quantity quantity = new Quantity();
            quantity.setItemId(preOrderItem.getId().toString());
            quantity.setCommodityCode(item.getCommodityCode());
            quantity.setCommodityId(item.getProductId());
            quantity.setRealReceiveQuantity(item.getProductNum());
            quantity.setIsAdd(false);
            quantity.setPrice(item.getPrice());
            quantity.setRequireQuantity(item.getProductNum());
            BigDecimal commodityPackageSpec = commodityPackageSpecMap.get(Long.valueOf(item.getProductId()));
            quantity.setRealReceiveNumber(item.getProductNum().divide(commodityPackageSpec, 0, BigDecimal.ROUND_UP).intValue());
            receiveQuantities.add(quantity);
        });
        receiveOrderVo.setReceiveQuantities(receiveQuantities);

        return receiveOrderVo;
    }


    public void checkDeliveryBatchTime(OrderDto orderDto) {
        if (DeliveryBatchTypeEnum.NEW_SHOP_BATCH.getCode().toString().equals(orderDto.getDeliveryBatch())) {
            // 当选择“新开店”批次时，下单时间的限制条件有变化
            // t≤ T1-1 9:30时，允许下单
            // t>T1-1 9:30时，不允许下单——仍提示已超出本批次的下单时间，用线上原文案即可。
            // 获取门店状态和开业日期
            Example example = new Example(Shop.class);
            example.createCriteria().andEqualTo("storeId", orderDto.getStoreId());
            Shop shop = shopMapper.selectOneByExample(example);
            if (shop.getShopStatus() == 2) {
                Date openDate = shop.getOpenDate();
                Date previousOneDay = DateUtil.addDay(openDate, -1);
                String previousOneDayStr = DateUtil.get4yMd(previousOneDay) + " 9:30:00";
                Date targetDate = DateUtil.parseDate(previousOneDayStr, DateUtil.DEFAULT_DATE_FORMAT);
                Date currentDate = new Date();
                boolean result = DateUtil.isBefore(currentDate, targetDate);
                QYAssert.isTrue(result, "提交失败！已超出该配送批次的下单时间。");
            }
        } else {
            List<DeliveryBatchEntry> deliveryBatchList = shoppingCartService.findDeliveryBatch(orderDto.getStoreId(), orderDto.getOrderTime());
            QYAssert.isTrue(SpringUtil.isNotEmpty(deliveryBatchList), "未查询到配送批次");
            String deliveryBatch = orderDto.getDeliveryBatch();
            List<DeliveryBatchEntry> deliveryBatchEntries = deliveryBatchList.stream().filter(item ->
                            item.getOptionCode().equals(deliveryBatch))
                    .collect(Collectors.toList());
            QYAssert.isTrue(SpringUtil.isNotEmpty(deliveryBatchEntries), "配送批次错误");
            DeliveryBatchEntry deliveryBatchEntry = deliveryBatchEntries.get(0);
            String optionValue = deliveryBatchEntry.getOptionValue();
            String[] startAndEndTimes = optionValue.split("-");
            String startTime = startAndEndTimes[0];
            String endTime = startAndEndTimes[1];

            String now = DateUtil.getDateFormate(new Date(), "HH:mm");
            // 判断时分时分在某个时分范围内
            try {
                boolean result = DateUtil.compareHourAndMin(now, startTime) == 1 && DateUtil.compareHourAndMin(endTime, now) == 1;
                QYAssert.isTrue(result, "提交失败！已超出该配送批次的下单时间。");
            } catch (ParseException e) {
                log.error("checkDeliveryBatch 时间解析错误");
            }
        }
    }

    //查找限量商品
    public CreateOrderVo getLimitCommoditys(List<CreateOrderVo> vos) {
        //只有一个主购物车
        CreateOrderVo vo = vos.get(0);
        CreateOrderVo delVo = new CreateOrderVo();//定义返回限量商品信息购物车主
        BeanUtils.copyProperties(vo, delVo);
        String orderTime = vo.getOrderTime();
        List<ProductLimitEntry> productLimitList = commonService.findCommodityLimitByNowData(orderTime);
        List<CreateOrderVo.CreateOrderItemIDTO> itemList = new ArrayList<>();//定义返回限量商品信息购物车明细信息
        if (SpringUtil.isNotEmpty(productLimitList)) {
            for (ProductLimitEntry p : productLimitList) {
                for (CreateOrderVo.CreateOrderItemIDTO i : vo.getItems()) {
                    if (p.getCommodityId().equals(i.getCommodityId() + "")) {
                        CreateOrderVo.CreateOrderItemIDTO item = new CreateOrderVo.CreateOrderItemIDTO();//定义返回购物车明细行
                        BeanUtils.copyProperties(i, item);
                        Map<String, Object> paramMap = new HashMap<String, Object>();
                        paramMap.put("orderTime", orderTime);
                        paramMap.put("productId", i.getCommodityId());
                        BigDecimal productCount = this.orderMapper.findOrderProductNumber(paramMap);
                        if (null == productCount) {
                            productCount = BigDecimal.ZERO;
                        }
                        if (productCount.compareTo(BigDecimal.ZERO) > 0) {
                            if (p.getLimitNumber().subtract(productCount).compareTo(i.getQuantity()) < 0) {//
                                itemList.add(item);
                            }
                        } else {
                            if (i.getQuantity().compareTo(p.getLimitNumber()) > 0) {
                                itemList.add(item);
                            }
                        }
                    }
                }
            }
        }
        delVo.setItems(itemList);
        return delVo;
    }

    //根据购物车ID获取购物车详细信息
    public List<CreateOrderVo.CreateOrderItemIDTO> findShoppingCartItems(Long storeId, Long shoppingCartId) {
        List<CreateOrderVo.CreateOrderItemIDTO> items = new ArrayList<>();
        List<ShoppingCartEntry> list = shoppingCartMapper.shoppingCartDetail(storeId, shoppingCartId, false, false, null);
        commonService.setShopCartItemInfo(list);
        if (CollectionUtils.isNotEmpty(list)) {
            List<ShoppingCartItemEntry> itemList = list.get(0).getItems();
            for (ShoppingCartItemEntry entry : itemList) {
                CreateOrderVo.CreateOrderItemIDTO i = new CreateOrderVo.CreateOrderItemIDTO();
                BeanUtils.copyProperties(entry, i);
                items.add(i);
            }
        }
        return items;
    }

    //(手持)端提交订单
    public CreateOrderFilterVo createHandleOrder(CreateHandleOrderRequestVO vo) {
        Boolean isXd = shoppingCartService.getIsXd(vo.getStoreId());
        ThreadLocalUtils.setXd(isXd);
        ThreadLocalUtils.setOrderType(OrderTypeEnum.SHOP_PDA_ORDER.getCode());

        CreateOrderFilterVo createOrderFilterVo = new CreateOrderFilterVo();
        //根据购物车id获取购物车提交信息
        List<CreateOrderVo.CreateOrderItemIDTO> shoppList = new ArrayList<>();
        List<ShoppingCartEntry> list = shoppingCartMapper.shoppingCartDetail(vo.getStoreId(), vo.getShoppingCartId(), false, false, null);
        QYAssert.isTrue(!CollectionUtils.isEmpty(list), "购物车不能为空，请刷新重试!");

        ShoppingCartEntry cartEntry = list.get(0);
        // 自动订货商品
        if (cartEntry.getAutoCommodity()) {
            Boolean isBigShop = ManagementModeEnums.档口分包.getCode().equals(cartEntry.getManagementMode());
            if(isBigShop) {
                QYAssert.isFalse("大店不允许加货");
            }
            createOrderFilterVo = autoPreOrderService.submitAudit(vo.getShoppingCartId(), vo.getUserId(), vo.getDeliveryBatch());
            //返回信息有值，则获取商品规格
            if (CollectionUtils.isNotEmpty(createOrderFilterVo.getFilterTips())) {
                List<String> commodityIdList = createOrderFilterVo.getFilterTips().stream().map(item -> item.getCommodityId() + "").collect(Collectors.toList());
                List<CommodityInfoEntry> commodityList = commodityMapper.findCommodityInfo(commodityIdList);
                Map<Long, String> idAndCodeMap = commodityList.stream().collect(Collectors.toMap(CommodityInfoEntry::getId, CommodityInfoEntry::getCommoditySpec));
                for (FilterTipVo filterTipVo : createOrderFilterVo.getFilterTips()) {
                    filterTipVo.setCommoditySpec(idAndCodeMap.get(filterTipVo.getCommodityId()));
                }
            }
            return createOrderFilterVo;
        }

        // 判断B端库存依据
        Map<Long, BigDecimal> orderQuantityMap = new HashMap<>();
        list.get(0).getItems().forEach(entry->{
            orderQuantityMap.put(entry.getCommodityId(), entry.getQuantity());
        });
        List<BStockShortResponseVO> responseVOList = bStockService.checkBStock(vo.getStoreId(), OrderTypeEnum.SHOP_PDA_ORDER.getCode(), DateUtil.parseDate(vo.getOrderTime(), "yyyy-MM-dd"), orderQuantityMap, "PDA提交购物车", null,vo.getUserId());
        if(CollectionUtils.isNotEmpty(responseVOList)){
            createOrderFilterVo.setShortStockList(responseVOList);
            return createOrderFilterVo;
        }

        Integer logisticsModel = cartEntry.getLogisticsModel();
        commonService.setShopCartItemInfo(list);
        if (CollectionUtils.isNotEmpty(list)) {
            List<ShoppingCartItemEntry> itemList = cartEntry.getItems();
            for (ShoppingCartItemEntry entry : itemList) {
                CreateOrderVo.CreateOrderItemIDTO i = new CreateOrderVo.CreateOrderItemIDTO();
                BeanUtils.copyProperties(entry, i);
                shoppList.add(i);
            }
        }
        QYAssert.isTrue(!CollectionUtils.isEmpty(shoppList), "购物车不能为空，请刷新重试!");
        QYAssert.isTrue(shoppList.size() <= 200, "提交失败,品项数不能超过200条。");

        //组装提交订单数据
        List<CreateOrderVo> vos = new ArrayList<>();
        CreateOrderVo orderVo = new CreateOrderVo();
        BeanUtils.copyProperties(vo, orderVo);
        orderVo.setCreateId(vo.getUserId());
        orderVo.setCreateName(vo.getRealName());
        orderVo.setItems(shoppList);
        ////直送的默认给时间,不是直送的判断空
        if (IogisticsModelEnums.DIRECT_SENDING.getCode() == logisticsModel) {
            orderVo.setOrderTime(DateTimeUtil.defaultDeliveryDate());
        } else {
            QYAssert.isTrue(StringUtils.isNotBlank(orderVo.getDeliveryBatch()), "配送批次不能为空!");
            QYAssert.isTrue(StringUtils.isNotBlank(orderVo.getOrderTime()), "送货日期不能为空!");
        }
        vos.add(orderVo);

        createOrderFilterVo = createOrder(vos);
        //返回信息有值，则获取商品规格
        if (CollectionUtils.isNotEmpty(createOrderFilterVo.getFilterTips())) {
            List<String> commodityIdList = createOrderFilterVo.getFilterTips().stream().map(item -> item.getCommodityId() + "").collect(Collectors.toList());
            List<CommodityInfoEntry> commodityList = commodityMapper.findCommodityInfo(commodityIdList);
            Map<Long, String> idAndCodeMap = commodityList.stream().collect(Collectors.toMap(CommodityInfoEntry::getId, CommodityInfoEntry::getCommoditySpec));
            for (FilterTipVo filterTipVo : createOrderFilterVo.getFilterTips()) {
                filterTipVo.setCommoditySpec(idAndCodeMap.get(filterTipVo.getCommodityId()));
            }
        }

        ThreadLocalUtils.remove();
        return createOrderFilterVo;
    }

    public String getLimitCommodityNames(List<CreateOrderVo.CreateOrderItemIDTO> itemList) {
        //定义返回错误信息
        StringBuffer errorList = new StringBuffer();
        errorList.append("如下商品已超过总部的订购限量，从购物车删除这些商品并提交订单吗?" + "\n");
        for (int i = 0; i < itemList.size(); i++) {
            CreateOrderVo.CreateOrderItemIDTO vo = itemList.get(i);
            if (i < 2) {
                errorList.append(vo.getCommodityName() + "\n");
            }
            if (i == 2) {
                errorList.append("......." + "\n");
            }
        }
        return errorList.toString();
    }

    @Transactional(rollbackFor = Exception.class)
    public void orderNotCommitTips() {

        // 1. 查询所有门店的所有购物车(因为只用到了主购物车里面的几个字段，所有查询购物车方法重新写。
        // 以前的查询购物车把购物车明细也级联查询出来了，耗时)
        List<ShoppingCartEntry> shoppingCartEntries = shoppingCartMapper.queryAllShoppingCart();
        if (SpringUtil.isEmpty(shoppingCartEntries)) {
            return;
        }

        List<Long> supplyIdList = shoppingCartEntries.stream().map(item -> item.getSupplyId()).collect(Collectors.toList());
        // 批量查询供应商
        QuerySupplierByIdsIDTO querySupplierByIdsIDTO = new QuerySupplierByIdsIDTO();
        querySupplierByIdsIDTO.setSupplierIds(supplyIdList);
        Map<Long, FullSupplierODTO> supplyMap = supplierClient.querySupplierByIds(querySupplierByIdsIDTO);

        RSet<Object> tips = redissonClient.getSet("tips");
        List<MessageDTO> messageList = new ArrayList<>();
        for (ShoppingCartEntry shoppingCartEntry : shoppingCartEntries) {
            // 2. 先判断是否快到提醒时间(查询供应商的时间)
            FullSupplierODTO supplierODTO = supplyMap.get(shoppingCartEntry.getSupplyId());
            // 供应商订货结束时间(hh:mm)
            String supplyEndTime = supplierODTO.getSupplyEndTime();
            // 门店订货结束时间(hh:mm)
            String shoppingCartEndTime = shoppingCartEntry.getEndTime();
            // 求门店订货结束时间和供应商订货结束时间的最小值(hh:mm:00)
            String endTime = (supplyEndTime.compareTo(shoppingCartEndTime) <= 0 ? supplyEndTime : shoppingCartEndTime) + ":00";

            Date nowDate = new Date();
            Date endDate = DateUtil.parseDate(DateUtil.get4yMd(nowDate) + " " + endTime, DateUtil.DEFAULT_DATE_FORMAT);
            long minuteDif = DateUtil.getMinuteDif(endDate, nowDate);
            if (minuteDif <= 7) {
                // 3. 判断有没有提醒过
                String shoppingCartId = shoppingCartEntry.getId();
                if (!tips.contains(shoppingCartId)) {
                    Long userId = shoppingCartEntry.getCreateId();
                    String title = "购物车里，部分商品将在" + endTime + "截止下单（还有5分钟）！";
//                    jPushUtil.pushMsgForAlias("有订单即将超过订货时间!", userId + "",  title);
                    MessageDTO dto = new MessageDTO();
                    dto.setTitle("有订单即将超过订货时间!");
                    dto.setTargetUrl("有订单即将超过订货时间!");
                    dto.setContent(title);
                    List<Long> userIds = new ArrayList<>();
                    userIds.add(userId);
                    dto.setUserIds(userIds);
                    //zsMessageClient.sendMessage(dto);
                    messageList.add(dto);

                    tips.add(shoppingCartEntry.getId());
                }

                if (tips.size() == 1) {
                    // 注意：设置过期时间必选保证key有值，过期时间当天00点
                    Date expireAtDate = DateUtil.parseDate(DateUtil.get4yMd(nowDate) + " 23:59:59", DateUtil.DEFAULT_DATE_FORMAT);
                    tips.expireAt(expireAtDate);
                }
            }
        }

        if (CollectionUtils.isNotEmpty(messageList)) {
            // 发送消息
            commonService.sendMessage(messageList);
        }
    }

    public List<OrderLogListEntry> getOrderLogList(Long orderId) {
        return orderHistoryMapper.getOrderLogList(orderId);
    }


    /**
     * 获取 T+2 时间
     *
     * @return
     */
    public String getT2Time() {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.DATE, 2);
        calendar.getTime();
        return sdf.format(calendar.getTime());
    }

    /**
     * 任务创建年货订单
     */
    @Transactional(rollbackFor = Exception.class)
    public void createOrderYear() throws Throwable {
        String orderTime = getT2Time();
        String beginTime = orderTime + " 00:00:00";
        String endTime = orderTime + " 23:59:59";

        ThreadLocalUtils.set(true);
        // 查询年货订单信息
        List<OrderYearEntry> yearEntryList = orderMapper.queryOrderYearEntryList(beginTime, endTime);
        if (CollectionUtils.isEmpty(yearEntryList)) {
            return;
        }

        for (OrderYearEntry yearEntry : yearEntryList) {
            List<String> commodityIds = new ArrayList<>();
            commodityIds.add(yearEntry.getCommodityId() + "");
            List<MdShopOrderSettingEntry> settingList = mdShopOrderSettingService.queryMdShopOrderSettingListByIds(yearEntry.getStoreId(), commodityIds);
            QYAssert.isTrue(SpringUtil.isNotEmpty(settingList), "门店订货通用设置配置信息有误");
            MdShopOrderSettingEntry mdShopOrderSettingEntry = settingList.get(0);

            OrderRequestVo orderRequestVo = new OrderRequestVo();
            orderRequestVo.setStoreId(yearEntry.getStoreId().toString());
            orderRequestVo.setOrderTime(orderTime);
            orderRequestVo.setEnterpriseId(78L);
            orderRequestVo.setUserId(-1L);
            orderRequestVo.setCreateName("系统");
            orderRequestVo.setLogisticsModel(mdShopOrderSettingEntry.getLogisticsModel());
            orderRequestVo.setSupplierId(Long.valueOf(mdShopOrderSettingEntry.getSupplierId()));
            orderRequestVo.setWarehouseId(Long.valueOf(mdShopOrderSettingEntry.getWarehouseId()));
            orderRequestVo.setDeliveryBatch(DeliveryBatchTypeEnum.ONE_BATCH.getCode().toString());
            orderRequestVo.setDeleveryTimeRange(mdShopOrderSettingEntry.getDeleveryTimeRange());

            List<OrderItemRequestVo> itemsList = new ArrayList<>();
            //shoppingCartItemList.forEach(i->{
            OrderItemRequestVo item = new OrderItemRequestVo();
            item.setProductId(yearEntry.getCommodityId().toString());
            item.setProductNum(yearEntry.getQuantity());
            itemsList.add(item);
            //});
            orderRequestVo.setItemsList(itemsList);
            this.createOrder(orderRequestVo, null);
        }

        ThreadLocalUtils.remove();
    }

    /**
     * 获取商品包装规格
     *
     * @param commodityIds
     * @return
     */
    public Map<Long, BigDecimal> getCommodityPackageSpecMap(List<String> commodityIds) {
        List<CommodityInfoEntry> commodityInfoEntryList = commodityMapper.findCommodityInfo(commodityIds);
        return commodityInfoEntryList.stream().collect(Collectors.toMap(CommodityInfoEntry::getId, CommodityInfoEntry::getCommodityPackageSpec, (key1, key2) -> key2));
    }


    public Map<Long, CommodityInfoEntry> getCommodityMap(List<String> commodityIds) {
        List<CommodityInfoEntry> commodityInfoEntryList = commodityMapper.findCommodityInfo(commodityIds);
        return commodityInfoEntryList.stream().collect(Collectors.toMap(CommodityInfoEntry::getCommodityId, Function.identity()));
    }

    /**
     * 发送消息到 统计查询系统中, 更新订单价格信息
     *
     * @param orderCodeList 待更新的订单code
     */
    @Transactional(rollbackFor = Exception.class)
    public void sendStatisticsKafkaMessage(List<String> orderCodeList) {
        if (SpringUtil.isEmpty(orderCodeList)) {
            log.error("更新价格的订单数量为0.");
            return;
        }
        Example orderExample = new Example(Order.class);
        orderExample.createCriteria().andIn("orderCode", orderCodeList);
        List<Order> orderList = orderMapper.selectByExample(orderExample);
        if (SpringUtil.isNotEmpty(orderList)) {
            for (Order order : orderList) {
                Example itemExample = new Example(OrderListGift.class);
                itemExample.clear();
                itemExample.createCriteria().andEqualTo("orderId", order.getId());
                List<OrderListGift> gifts = orderListGiftMappier.selectByExample(itemExample);
                order.setOrderList(gifts.stream().map(OrderListGift::covert).collect(toList()));
                String topic = applicationNameSwitch + KafkaTopicConstant.STATISTICAL_ORDER_TOPIC;
                // KafkaMessageWrapper wrapper = new KafkaMessageWrapper(KafkaMessageTypeEnum.ORDER, order, KafkaMessageOperationTypeEnum.UPDATE);
                //ObjectMapper mapper = new ObjectMapper();
                try {
                    mqSenderComponent.sendByKey(topic,
                            order,
                            MqMessage.MQ_KAFKA,
                            KafkaMessageTypeEnum.ORDER.name(),
                            KafkaMessageOperationTypeEnum.UPDATE.name(), order.getId().toString());
                } catch (Exception e) {
                    log.error("序列化JSON时出错:{}", e);
                }
            }
        }
    }

    public Double getSumRealQuantity(Long subOrderId) {
        return subOrderItemMapper.getSumRealQuantity(subOrderId);
    }


    /**
     * 门店配送单打印
     *
     * @param storeId
     * @param realName
     * @param orderTime
     * @return
     */
    public PrintOrderODTO printOrderInfo(Long storeId, String realName, String orderTime) {

        PrintOrderODTO printOrderODTO = new PrintOrderODTO();
        printOrderODTO.setOrderTime(orderTime);
        printOrderODTO.setPrinter(realName);
        printOrderODTO.setPrintDate(DateUtil.getDateFormate(new Date(), "yyyy-MM-dd HH:mm:ss"));

        // 获取门店
        Example ex = new Example(Shop.class);
        ex.createCriteria().andEqualTo("storeId", storeId);
        List<Shop> shopList = shopMapper.selectByExample(ex);
        Shop shop = shopList.get(0);
        printOrderODTO.setShopCode(shop.getShopCode());
        printOrderODTO.setShopName(shop.getShopName());

        List<PrintOrderItemODTO> itemList = new ArrayList<>();
        // 查询门店送货日期下配送单信息
        List<XdReceiveDocCommodityODTO> infoList = orderMapper.queryPrintOrderInfo(storeId, orderTime);
        if (CollectionUtils.isNotEmpty(infoList)) {
            for (XdReceiveDocCommodityODTO odto : infoList) {
                PrintOrderItemODTO printOrderItemODTO = new PrintOrderItemODTO();
                BeanUtils.copyProperties(odto, printOrderItemODTO);
                itemList.add(printOrderItemODTO);
            }

            // 查询门店送货日期下配送单号
            List<String> orderCodeList = orderMapper.queryPrintOrderCodeList(storeId, orderTime);
            printOrderODTO.setOrderCodeList(CollectionUtils.isNotEmpty(orderCodeList) ? orderCodeList : new ArrayList<>());
        }
        printOrderODTO.setItemList(itemList);
        return printOrderODTO;
    }


    /****
     *
     * 场景：
     * 修改客户的所属公司后，需将改该客户下的订单修改成对应所属公司
     * 修改订单的条件为：客户下的订单、订单送货时间>当前时间、订单所属公司不等于客户所属公司
     * 修改的表为 t_order、t_md_preorder
     * @param storeId
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public Integer modifyOrderCompanyByStore(Long storeId, Long companyId) {

        /*** 获取当前时间 */
        LocalDate localDate = LocalDate.now();
        Date now = DateUtil.parseDate(localDate.toString(), "yyyy-MM-dd");

        Example queryOrderByExample = new Example(Order.class);
        queryOrderByExample.createCriteria().andEqualTo("storeId", storeId).andGreaterThan("orderTime", now).andNotEqualTo("companyId", companyId);
        List<Order> orderList = orderMapper.selectByExample(queryOrderByExample);
        if (orderList != null && orderList.size() > 0) {
            List<Long> orderIds = orderList.stream().map(Order::getId).collect(Collectors.toList());
            /*** 修改t_order 的所属公司*/
            Example orderByExample = new Example(Order.class);
            orderByExample.createCriteria().andIn("id", orderIds);
            Order order = new Order();
            order.setCompanyId(companyId);
            orderMapper.updateByExampleSelective(order, orderByExample);
        }


        Example queryPreOrderByExample = new Example(PreOrder.class);
        queryPreOrderByExample.createCriteria().andEqualTo("storeId", storeId).andGreaterThan("orderTime", now).andNotEqualTo("companyId", companyId);
        List<PreOrder> preOrderList = preOrderMapper.selectByExample(queryPreOrderByExample);
        if (preOrderList != null && preOrderList.size() > 0) {
            List<Long> orderIds = preOrderList.stream().map(PreOrder::getId).collect(Collectors.toList());
            /*** 修改t_md_preorder 的所属公司*/
            Example preOrderByExample = new Example(PreOrder.class);
            preOrderByExample.createCriteria().andIn("id", orderIds);
            PreOrder preOrder = new PreOrder();
            preOrder.setCompanyId(companyId);
            preOrderMapper.updateByExampleSelective(preOrder, preOrderByExample);
        }
        return 1;
    }


    /**
     * 获取需要补偿订单数量的subOrderId
     *
     * @param vo vo
     * @return list
     */
    public List<Long> getSubOrderId(CompensateOrderQuantityReqVO vo) {
        return orderMapper.getSubOrderId(vo);
    }

    @Transactional(rollbackFor = Exception.class)
    public OrderClientVO getOrderById(Long orderId) {
        Order order = orderMapper.selectByPrimaryKey(orderId);
        OrderClientVO vo = new OrderClientVO();
        if (order != null) {
            vo = BeanCloneUtils.copyTo(order, OrderClientVO.class);
            if (order.getPrintType() != null) {
                vo.setPrintType(order.getPrintType().getCode());
            }
            Example se = new Example(SubOrder.class);
            se.createCriteria().andEqualTo("orderId", orderId);
            List<SubOrder> subOrders = subOrderMapper.selectByExample(se);
            vo.setSubOrders(subOrders);
            if (SpringUtil.isNotEmpty(subOrders)) {
                List<Long> subOrderIds = subOrders.stream().map(SubOrder::getId).collect(toList());
                Example ie = new Example(SubOrderItem.class);
                ie.createCriteria().andIn("subOrderId", subOrderIds);
                List<SubOrderItem> subOrderItems = subOrderItemMapper.selectByExample(ie);
                vo.setSubOrderItems(subOrderItems);
            }
        }
        return vo;
    }

    public Long selectOrderListGiftJob(OrderReportJobVO vo){
        return orderListGiftMapper.selectOrderListGiftJob(vo);
    }

    /***
     * 根据订单查询订单明细
     * @param vo
     */
    public List<OrderListInfoEntry> selectOrderListGiftByOrderIdAndCommodityIdList(OrderListOrderIdAndCommVO vo){
        List<OrderListInfoEntry> all = new ArrayList<>();
        List<OrderListInfoEntry> orderGiftList= orderListMapper.selectOrderListGiftByOrderIdAndCommodityIdList(vo,true);
        if(SpringUtil.isNotEmpty(orderGiftList)){
            all.addAll(orderGiftList);
        }
        List<OrderListInfoEntry> orderList= orderListMapper.selectOrderListGiftByOrderIdAndCommodityIdList(vo,false);
        if(SpringUtil.isNotEmpty(orderList)){
            all.addAll(orderList);
        }
        return all;
    }


    public Integer updateSettleOrderDateByDate(String orderTime) {
        return orderMapper.updateSettleOrderDateByDate(orderTime);
    }

    public Integer updateStoreTypeIdByDate(String orderTime) {
        return orderMapper.updateStoreTypeIdByDate(orderTime);
    }

    /**
     * 预付款扣款/回款
     * @param saveOrderDeductionsIDTO
     * @return
     */
    @Deprecated
    @Transactional(rollbackFor = Exception.class)
    public Boolean saveOrderDeductions(SaveOrderDeductionsIDTO saveOrderDeductionsIDTO){

        Long storeId = saveOrderDeductionsIDTO.getStoreId();
        XdaPayTypeEnum typeEnum = saveOrderDeductionsIDTO.getTypeEnum();
        String remark = saveOrderDeductionsIDTO.getRemark();
        Long orderId = saveOrderDeductionsIDTO.getOrderId();

        if(null != storeId){
            Example ex = new Example(StoreSettlement.class);
            ex.createCriteria().andEqualTo("storeId", storeId);
            List<StoreSettlement> ssList = this.storeSettlementMapper.selectByExample(ex);
            StoreSettlement ss = null;
            if(SpringUtil.isNotEmpty(ssList)){
                ss = ssList.get(0);
            }
            if(null != ss && ss.getCollectStatus() ){
                //上送扣款/回款金额
                BigDecimal orderAmount = saveOrderDeductionsIDTO.getAmount();
                if(ss.getCollectPrice()==null){
                    ss.setCollectPrice(BigDecimal.ZERO.doubleValue());
                }

                OrderBill orderBill = new OrderBill();
                orderBill.setStoreId(storeId);
                orderBill.setOrderId(orderId);
                orderBill.setOrderTime(new Date());
                orderBill.setBillRemark(remark);
                orderBill.setCreateTime(new Date());
                orderBill.setCrateId(-1L);

                if(XdaPayTypeEnum.DEDUCTION.equals(typeEnum)){
                    BigDecimal balance = BigDecimal.valueOf(ss.getCollectPrice()).subtract(orderAmount);
                    ss.setCollectPrice(balance.doubleValue());
                    storeSettlementMapper.updateByPrimaryKey(ss);
                    OrderBill ob = BeanCloneUtils.copyTo(orderBill, OrderBill.class);
                    ob.setArAmount(orderAmount);
                    ob.setPaAmount(BigDecimal.ZERO);
                    ob.setStoreBalance(balance);
                    orderBillMapper.insertSelective(ob);


                }else {
                    BigDecimal balance = BigDecimal.valueOf(ss.getCollectPrice()).add(orderAmount);
                    ss.setCollectPrice(balance.doubleValue());
                    storeSettlementMapper.updateByPrimaryKey(ss);
                    OrderBill ob = BeanCloneUtils.copyTo(orderBill, OrderBill.class);
                    ob.setPaAmount(orderAmount);
                    ob.setStoreBalance(balance);
                    orderBillMapper.insertSelective(ob);
                }
            }
        }

        return true;
    }

    /**
     * 代销香烟
     * 是否存在历史数据待收货的代销订单或者待确认的代销退货单(pos用)
     * @param shopId
     * @return  true 存在  false 不存在
     */
    public Boolean existConsignmentOrder(Long shopId) {
        return purchaseOrderService.existNotReceiveConsignmentOrder(shopId)
                || saleReturnOrderService.existNotConfirmSaleReturnOrder(shopId);
    }


    public OrderBatchCancelODTO batchCancelOrder(OrderBatchCancelIDTO idto) {
        User user = idto.getUser();
        OrderBatchCancelODTO odto = new OrderBatchCancelODTO();
        List<String> cancelFailErrors = new ArrayList<>();

        QYAssert.isTrue(null != idto,"参数有误");
        List<String> codeList = idto.getOrderCodeList();
        QYAssert.isTrue(SpringUtil.isNotEmpty(codeList),"订单号不能为空");
        if(codeList.size() > 10){
            QYAssert.isTrue(false,"每次最多10个订单号！");
        }

        Example example = new Example(Order.class);
        example.createCriteria().andIn("orderCode",codeList);
        List<Order> orders = orderMapper.selectByExample(example);
        List<String> orderCodeList = orders.stream().map(Order::getOrderCode).collect(toList());
        List<String> excludeOrderCodes = new ArrayList<>();

        List<String> noExistOrderCodeList = codeList.stream().filter(it -> !orderCodeList.contains(it)).collect(Collectors.toList());
        if(SpringUtil.isNotEmpty(noExistOrderCodeList)){
            noExistOrderCodeList.forEach(
                    noExistOrderCode->{
                        cancelFailErrors.add("订单编码【"+noExistOrderCode+"】不存在");
                        excludeOrderCodes.add(noExistOrderCode);
                    }
            );
        }

        //校验送货日期必须是今天
        List<String> orderTimeNotTodayOrderCodes  =  checkOrderTimeIfToday(orders);
        if(SpringUtil.isNotEmpty(orderTimeNotTodayOrderCodes)){
            orderTimeNotTodayOrderCodes.forEach(
                    orderTimeNotTodayOrderCode->{
                        cancelFailErrors.add("订单编码【"+orderTimeNotTodayOrderCode+"】送货日期必须是今天");
                        excludeOrderCodes.add(orderTimeNotTodayOrderCode);
                    }
            );
        }

        //校验订单日期必须是正常状态
        List<String> orderStatusAbnormalOrderCodes  =  checkOrderStatusIfAbnormal(orders);
        if(SpringUtil.isNotEmpty(orderStatusAbnormalOrderCodes)){
            orderStatusAbnormalOrderCodes.forEach(
                    orderStatusAbnormalOrderCode->{
                        cancelFailErrors.add("订单编码【"+orderStatusAbnormalOrderCode+"】必须是正常状态");
                        excludeOrderCodes.add(orderStatusAbnormalOrderCode);
                    }
            );
        }

        odto.setCancelFailErrors(cancelFailErrors);

        List<Order> processOrders = orders.stream().filter(o->!excludeOrderCodes.contains(o.getOrderCode())).collect(toList());
        List<String> cancelFailErrs = doBatchCancelOrder(processOrders,user);
        if(SpringUtil.isNotEmpty(cancelFailErrs)){
            odto.addCancelFailError(cancelFailErrs);
            return odto;
        }

        return odto;
    }

    public List<String> doBatchCancelOrder(List<Order> orders,User user) {

        List<String> cancelFailErrors = Lists.newArrayList();

        for (Order order : orders) {

            // 根据orderId 加锁10秒
            RLock lock = redissonClient.getLock("batchCancelOrder" + order.getId());

            try {
                // 锁60秒，子方法开启新的独立事务
                lock.lock(60L, TimeUnit.SECONDS);
                orderBatchCancelService.cancelOrder(order,user);
            } catch(Exception e){
                log.warn("批量取消订单异常,订单号:{},异常原因:{}",order.getOrderCode(),e.getMessage());
                cancelFailErrors.add("订单编码【"+order.getOrderCode()+"】"+e.getMessage());
            }
            finally {
                if (lock.isLocked()) {
                    lock.unlock();
                }
            }
        }

        return cancelFailErrors;
    }

    private List<String> checkOrderStatusIfAbnormal(List<Order> orders) {

        List<String> orderStatusAbnormalOrderCodes = orders
                .stream()
                .filter(
                        order -> 0 != order.getOrderStatus()
                ).map(Order::getOrderCode)
                .collect(toList());
        return orderStatusAbnormalOrderCodes;
    }

    private List<String> checkOrderTimeIfToday(List<Order> orders) {

        List<String> orderTimeNotTodayOrderCodes = orders
                .stream()
                .filter(
                        order -> !DateUtil.isToday(order.getOrderTime())
                ).map(Order::getOrderCode)
                .collect(toList());
        return orderTimeNotTodayOrderCodes;
    }


    public boolean orderBusinessTypeIsTda(Order order){
      return   DeliveryOrderTypeEnums.TD_SALE.getCode().equals(order.getBusinessType());
    }


    public List<SyncOrderODTO> syncOrderList(SyncOrderIDTO syncOrderIDTO){
        List<SyncOrderODTO> syncOrderODTOS = orderMapper.syncOrderList(syncOrderIDTO);
        if(SpringUtil.isEmpty(syncOrderODTOS)){
            return null;
        }
        List<SyncOrderListODTO> syncOrderListODTOS = orderListGiftMapper.syncOrderItemGiftList(syncOrderIDTO);
        Map<Long, List<SyncOrderListODTO>> collect = syncOrderListODTOS.stream().collect(Collectors.groupingBy(SyncOrderListODTO::getOrderId));
        syncOrderODTOS.forEach(item->{
            if(collect.containsKey(item.getOrderId())){
                List<SyncOrderListODTO> syncOrderListODTOS1 = collect.get(item.getOrderId());
                item.setSyncOrderListODTOList(syncOrderListODTOS1);
            }
        });
        return syncOrderODTOS;
    }


//    public List<SyncOrderListODTO> syncOrderItemList(SyncOrderIDTO syncOrderIDTO){
//        return orderListGiftMapper.syncOrderItemGiftList(syncOrderIDTO);
//    }

    /**
     * 查询客户特定日期的已下单数量
     * @param storeId
     * @param orderTime
     * @return
     */
    public StoreOrderCount countOrderNumberByStoreId(Long storeId, String orderTime) {
        QYAssert.isTrue(storeId != null, "storeId不能为空");
        QYAssert.isTrue(StringUtils.isNotBlank(orderTime), "orderTime不能为空");

        Integer orderCount = orderMapper.countOrderNumberByStoreId(storeId, orderTime);
        StoreOrderCount storeOrderCount = new StoreOrderCount(orderCount);
        return storeOrderCount;
    }

    public Order findOrderById(Long orderId) {
       return orderMapper.selectByPrimaryKey(orderId);
    }

    public List<Order> findListByOrderIdAndOrderStatus(Long orderId,Integer orderStatus) {
        Example example = new Example(Order.class);
        example.createCriteria().andEqualTo("id",orderId)
                .andEqualTo("orderStatus",orderStatus);
        return orderMapper.selectByExample(example);
    }

    @Transactional(rollbackFor = Exception.class)
    public void deleteOrder(Long orderId) {
        Order order = new Order();
        order.setId(orderId);
        this.orderMapper.delete(order);

    }

    public List<Order> findStoreSearch(Map<String,Object> params){
        return orderMapper.findStoreSearch(params);
    }

    public PdaCommodityOrderQueryResponseVo findPdaCommodityOrder(PdaCommodityOrderQueryRequestVo vo) {

        vo.check();

        PdaCommodityOrderQueryResponseVo result = new PdaCommodityOrderQueryResponseVo();
        result.setPdaCommodityOrderQueryData(processPdaOrderQuery(vo));

        return result;
    }


    private PdaCommodityOrderQueryData processPdaOrderQuery(PdaCommodityOrderQueryRequestVo vo) {

        List<PdaOrderQueryEntry> pdaOrderList = this.orderMapper.findPdaOrder(vo);
        if(SpringUtil.isNotEmpty(pdaOrderList)){
            Long commodityId = pdaOrderList.get(0).getCommodityId();
            Commodity commodity = commodityMapper.selectByPrimaryKey(commodityId);

            PdaOrderCountODTO pdaOrderCountODTO = calculatePdaCommodityOrderCount(pdaOrderList);

            PdaCommodityOrderQueryData result = BeanCloneUtils.copyTo(pdaOrderCountODTO, PdaCommodityOrderQueryData.class);
            result.setCommodityId(commodityId);
            result.setCommodityUnitId(commodity.getCommodityUnitId());
            renderService.render(result,"/order/findPdaCommodityOrder");
            return result;
        }

        return null;
    }


    private PdaOrderCountODTO calculatePdaCommodityOrderCount(List<PdaOrderQueryEntry> pdaOrderList) {

        PdaCommodityOrderCountCalculator pdaOrderCountCalculator = new PdaCommodityOrderCountCalculator(pdaOrderList);
        PdaOrderCountODTO pdaOrderCountODTO = pdaOrderCountCalculator.calculatePdaCommodityOrderCount();
        return pdaOrderCountODTO;

    }

    public List<StoreOrderAmountEntry> queryOrder(String dateStr,Integer orderStatus,Integer collectStatus){
        return orderMapper.queryOrder(dateStr,orderStatus,collectStatus);
    }


    @Transactional(rollbackFor = Exception.class)
    public Map<String, BigDecimal> getOrderAmountByOrderId(Long orderId) {
        Map<String, BigDecimal> amountMap = new HashMap<>();

        BigDecimal orderAmount = BigDecimal.ZERO;
        BigDecimal realTotalAmount = BigDecimal.ZERO;
        OrderList record = new OrderList();
        record.setOrderId(orderId);
        List<OrderList> orderLists = orderListMapper.select(record);
        for(OrderList ol : orderLists) {
            orderAmount = orderAmount.add(ol.getTotalPrice());
            if(ol.getRealTotalPrice() != null) {
                realTotalAmount = realTotalAmount.add(ol.getRealTotalPrice());
            }
        }

        amountMap.put("orderAmount", orderAmount);
        amountMap.put("realTotalAmount", realTotalAmount);
        return  amountMap;
    }

    /**
     * 查询可变价订单列表
     */
    public List<Order> getOrdersByStoreIdAndCpsEq1(Long storeId) {
        String needUpdateOrderGreaterThanTime = DateUtil.get4yMdHms(DateUtil.getEndDateByDate(new Date()));

        // 改价逻辑变更——> (送货日期 = T+1 & 当前时间 <= 22:00) || 送货日期 > T+1
        return orderMapper.queryChangePriceOrderList(storeId,needUpdateOrderGreaterThanTime);
    }

    @Transactional(rollbackFor = Exception.class)
    public void updateOrderTotalPrice(Order order, BigDecimal finalAmount, BigDecimal orderAmount, Date orderUpdateTime, BigDecimal realTotalAmount) {
        if (finalAmount.compareTo(order.getOrderAmount()) == 0) {
            // 最终金额等于付款金额，不操作
            return;
        }
        Order updateOrder = new Order();
        updateOrder.setId(order.getId());
        updateOrder.setFinalAmount(finalAmount);
        // 取明细上的金额合计
        updateOrder.setOrderAmount(orderAmount);
        updateOrder.setUpdateTime(orderUpdateTime);
        // 设置空，不然更新时候给改成-1了
        updateOrder.setStallId(null);
        // 订单改价，重新设置订单实发金额
        updateOrder.setRealTotalPrice(realTotalAmount);
        orderMapper.updateByPrimaryKeySelective(updateOrder);
    }

}
