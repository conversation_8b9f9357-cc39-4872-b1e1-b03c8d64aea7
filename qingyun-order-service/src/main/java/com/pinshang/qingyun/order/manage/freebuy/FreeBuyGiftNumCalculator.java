package com.pinshang.qingyun.order.manage.freebuy;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Map;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class FreeBuyGiftNumCalculator {

    /**
     * 客户单日限量或总限量 0表示不限制
     */
    /**  鲜达会有小数 int->BigDecimal  */
    private BigDecimal limitNum;

    /**
     * 已占用的赠品数量 客户单日限量或者总限量记录数map
     * key 为商品id
     * value 为已占用的赠品数量
     */
    private Map<Long, BigDecimal> usedGiftNumMap;

    /**
     *  商品id
     */
    Long commodityId;

    /**
     * 需要赠送的商品数量
     */
    /**  鲜达会有小数 int->BigDecimal  */
    BigDecimal giftNum;

    /**
     * 获取最大可用的赠品数量
     * @return
     */
    public BigDecimal getMaxAvailableGiftNum(){

        BigDecimal finalGiftNum = BigDecimal.ZERO;
        if(null != limitNum && limitNum.compareTo(BigDecimal.ZERO) != 0){
            //赠品限量
            BigDecimal giftLimitQuantity = limitNum;
            //已赠数量
            BigDecimal usedQuantity = BigDecimal.ZERO;
            if(usedGiftNumMap.containsKey(commodityId)){
                usedQuantity = usedGiftNumMap.get(commodityId);
            }
            BigDecimal subtract = giftLimitQuantity.subtract(usedQuantity);
            if(subtract.compareTo(giftNum)<0){
                BigDecimal commodityGiftQuantity = subtract.compareTo(BigDecimal.ZERO) > 0 ? subtract:BigDecimal.ZERO;
                finalGiftNum = commodityGiftQuantity;
            }else{
                finalGiftNum = giftNum;
            }
        }else{
            finalGiftNum = giftNum;
        }
        return finalGiftNum;
    }
}
