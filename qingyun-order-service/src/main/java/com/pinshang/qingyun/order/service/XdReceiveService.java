package com.pinshang.qingyun.order.service;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.pinshang.qingyun.base.configure.expand.QYAssert;
import com.pinshang.qingyun.base.enums.ShopShopStatusEnums;
import com.pinshang.qingyun.base.enums.shop.ShopTypeEnums;
import com.pinshang.qingyun.box.utils.DateTimeUtil;
import com.pinshang.qingyun.order.dto.*;
import com.pinshang.qingyun.order.enums.ShopReceiveTypeEnums;
import com.pinshang.qingyun.order.mapper.*;
import com.pinshang.qingyun.order.mapper.entry.commodity.CommodityBasicEntry;
import com.pinshang.qingyun.order.model.commodity.Commodity;
import com.pinshang.qingyun.order.model.shop.Shop;
import com.pinshang.qingyun.order.model.xd.XdReceiveDoc;
import com.pinshang.qingyun.order.model.xd.XdReceiveDocLog;
import com.pinshang.qingyun.order.vo.commodity.CommodityVO;
import com.pinshang.qingyun.renderer.client.dto.DictionaryODTO;
import com.pinshang.qingyun.xd.wms.dto.*;
import com.pinshang.qingyun.xd.wms.service.GrouponClient;
import com.pinshang.qingyun.xd.wms.service.XdStockClient;
import com.pinshang.qingyun.xd.wms.service.XdWarehouseShelfCommodityClient;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.entity.Example;

import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.text.NumberFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @Author: sk
 * @Date: 2020/3/17
 */
@Service
@Slf4j
public class XdReceiveService {

    @Autowired
    private XdReceiveDocMapper xdReceiveDocMapper;

    @Autowired
    private ShopMapper shopMapper;

    @Autowired
    private XdWarehouseShelfCommodityClient xdWarehouseShelfCommodityClient;

    @Autowired
    private ShopReceiveService shopReceiveService;

    @Autowired
    private XdStockClient xdStockClient;

    @Autowired
    SubOrderItemMapper subOrderItemMapper;

    @Autowired
    private CommodityMapper commodityMapper;

    @Autowired
    private XdReceiveDocLogMapper xdReceiveDocLogMapper;

    @Autowired
    private ShopReceiveSettingService shopReceiveSettingService;

    @Autowired
    private GrouponClient grouponClient;
    @Autowired
    private ShopService shopService;
    @Autowired
    private CommonService commonService;

    /**
     * 创建收货单(关闭今天的收货单，创建明天的收货单)
     * @param timeStr
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public Boolean createXdReceiveDoc(String timeStr) {
        QYAssert.notNull(timeStr, "日期不能为空");

        String tomorrowDay = getTomorrowDay(timeStr);

        //删除数据
        Example deleteExample = new Example(XdReceiveDoc.class);
        deleteExample.createCriteria().andEqualTo("deliveryTime", tomorrowDay);
        xdReceiveDocMapper.deleteByExample(deleteExample);
        // 创建收货单
        List<Long> shopIdList = shopReceiveSettingService.getShopIdList(ShopReceiveTypeEnums.HANDLE.getCode());
        if(CollectionUtils.isNotEmpty(shopIdList)){
            NumberFormat format = new DecimalFormat("000000");
            Long num = 1L;

            List<XdReceiveDoc> docList = new ArrayList<>();
            for(Long shopId : shopIdList){
                XdReceiveDoc xdReceiveDoc = new XdReceiveDoc();
                xdReceiveDoc.setShopId(shopId);
                xdReceiveDoc.setDocCode("SH" + tomorrowDay.replaceAll("-","") + format.format(num));
                xdReceiveDoc.setDeliveryTime(DateTimeUtil.parse(tomorrowDay,"yyyy-MM-dd"));
                xdReceiveDoc.setDocStatus(0);
                xdReceiveDoc.setCreateId(-1L);
                xdReceiveDoc.setCreateTime(new Date());
                num ++;
                docList.add(xdReceiveDoc);
            }
            xdReceiveDocMapper.insertList(docList);
        }

        //今天的收货单关闭
        XdReceiveDoc xdReceiveDoc = new XdReceiveDoc();
        xdReceiveDoc.setDocStatus(1);
        Example example = new Example(XdReceiveDoc.class);
        example.createCriteria().andEqualTo("deliveryTime", timeStr);
        xdReceiveDocMapper.updateByExampleSelective(xdReceiveDoc,example);

        // 查询今天的收货单所关联的所有门店
        Example ex = new Example(XdReceiveDoc.class);
        ex.createCriteria().andEqualTo("deliveryTime", timeStr);
        List<XdReceiveDoc> docList = xdReceiveDocMapper.selectByExample(ex);

        if(CollectionUtils.isNotEmpty(docList)){
            List<Long> shopIds = docList.stream().map(item -> item.getShopId()).collect(Collectors.toList());

            //批量更新今天的收货单状态为完成
            xdReceiveDocMapper.batchUpdateReceiveOrderPass(timeStr,shopIds);
        }
        return true;
    }

    /**
     * 批量更新手动收货的门店单子为完成收货
     * @param orderTime
     * @param shopIds
     */
    @Transactional(rollbackFor = Exception.class)
    public void batchUpdateReceiveOrderPass(String orderTime, List<Long> shopIds){
        //批量更新orderTime的收货单状态为完成
        xdReceiveDocMapper.batchUpdateReceiveOrderPass(orderTime,shopIds);
    }

    /**
     * 自动收掉成功的门店，关闭其大的收货单(t_xd_receive_doc)，然后创建一个新的收货单
     * @param orderTime
     * @param autoShopIdList
     */
    @Transactional(rollbackFor = Exception.class)
    public void createReceiveDoc(String orderTime, List<Long> autoShopIdList) {
        //收货单关闭
        XdReceiveDoc xdReceiveDoc = new XdReceiveDoc();
        xdReceiveDoc.setDocStatus(1);
        Example example = new Example(XdReceiveDoc.class);
        example.createCriteria().andEqualTo("deliveryTime", orderTime).andIn("shopId",autoShopIdList);
        xdReceiveDocMapper.updateByExampleSelective(xdReceiveDoc,example);

        // 创建新的收货单
        Long num = 1L;
        NumberFormat format = new DecimalFormat("000000");
        String maxDocNo = xdReceiveDocMapper.getMaxDocNo(orderTime);
        if(StringUtils.isNotBlank(maxDocNo)){
            num = Long.valueOf(maxDocNo.substring(10)) + 1;
        }

        List<XdReceiveDoc> docList = new ArrayList<>();
        for(Long shopId : autoShopIdList){
            XdReceiveDoc receiveDoc = new XdReceiveDoc();
            receiveDoc.setShopId(shopId);
            receiveDoc.setDocCode("SH" + orderTime.replaceAll("-","") + format.format(num));
            receiveDoc.setDeliveryTime(DateTimeUtil.parse(orderTime,"yyyy-MM-dd"));
            receiveDoc.setDocStatus(0);
            receiveDoc.setCreateId(-1L);
            receiveDoc.setCreateTime(new Date());
            num ++;
            docList.add(receiveDoc);
        }
        xdReceiveDocMapper.insertList(docList);
    }

    /**
     * 获取开业前，营业中的门店
     * @return
     */
    public List<Shop> getAllXDShop(){
        Example shopEx = new Example(Shop.class);
        List<Integer> shopStatusList = new ArrayList<>();
        shopStatusList.add(ShopShopStatusEnums.BEFORE_OPEN.getCode());
        shopStatusList.add(ShopShopStatusEnums.OPEN.getCode());
        shopEx.createCriteria().andEqualTo("shopType", ShopTypeEnums.XD.getCode()).andIn("shopStatus",shopStatusList);
        return this.shopMapper.selectByExample(shopEx);
    }

    /**
     * 获取指定日期的下一天
     * @param timeStr
     * @return
     */
    private String getTomorrowDay(String timeStr){
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        Calendar calendar = Calendar.getInstance();
        Date date = null;
        try {
            date = sdf.parse(timeStr);
        } catch (ParseException e) {
            log.info("转换时间出错....");
        }
        calendar.setTime(date);
        int day = calendar.get(Calendar.DATE);
        calendar.set(Calendar.DATE,day + 1);
        String lastDay = sdf.format(calendar.getTime());
        return lastDay;
    }

    /**
     * 查询收货单列表
     * @param shopId
     * @return
     */
    public List<XdReceiveDocODTO> getXdReceiveDocList(Long shopId) {
        List<XdReceiveDocODTO> docODTOList = new ArrayList<>();
        Example docEx = new Example(XdReceiveDoc.class);
        // 查询门店下待收货的收货单
        docEx.createCriteria().andEqualTo("shopId", shopId).andEqualTo("docStatus",0);
        List<XdReceiveDoc> list = xdReceiveDocMapper.selectByExample(docEx);
        if(CollectionUtils.isNotEmpty(list)){
            for(XdReceiveDoc doc : list){
                XdReceiveDocODTO docODTO = new XdReceiveDocODTO();
                BeanUtils.copyProperties(doc,docODTO);
                docODTO.setDocId(String.valueOf(doc.getId()));
                docODTOList.add(docODTO);
            }
        }
        return docODTOList;
    }

    /**
     * 查看收货情况(分页)
     * @param xdReceiveDocIDTO
     * @return
     */
    public PageInfo<XdReceiveDocCommodityODTO> getXdReceiveDocCommodityInfo(XdReceiveDocIDTO xdReceiveDocIDTO) {
        PageInfo <XdReceiveDocCommodityODTO> pageDate = null ;

        QYAssert.isTrue( null != xdReceiveDocIDTO.getDocId(),"单据id不能为空");
        XdReceiveDoc xdReceiveDoc = xdReceiveDocMapper.selectByPrimaryKey(xdReceiveDocIDTO.getDocId());
        QYAssert.isTrue( null != xdReceiveDoc,"单据不存在");

        xdReceiveDocIDTO.setDeliveryTime(DateTimeUtil.formatDate(xdReceiveDoc.getDeliveryTime(),"yyyy-MM-dd"));
        xdReceiveDocIDTO.setShopId(xdReceiveDoc.getShopId());
        // 货位不为空
        if(null != xdReceiveDocIDTO.getShelfId()){
            Long commodityId = xdWarehouseShelfCommodityClient.queryCommodityIdByShelf(xdReceiveDocIDTO.getShelfId());
            xdReceiveDocIDTO.setCommodityId(commodityId);
            if(null == commodityId){
                return new PageInfo<XdReceiveDocCommodityODTO>();
            }
        }

        if(StringUtils.isNotBlank(xdReceiveDocIDTO.getCommodityKey())){
            CommodityVO vo = new CommodityVO();
            vo.setCommodityKey(xdReceiveDocIDTO.getCommodityKey());
            List<CommodityBasicEntry> commBasicList = commodityMapper.findCommodityBasicListByParam(vo);
            if(CollectionUtils.isNotEmpty(commBasicList)){
                List<Long> commodityIdList = commBasicList.stream().map(item -> Long.valueOf(item.getCommodityId())).collect(Collectors.toList());
                xdReceiveDocIDTO.setCommodityIdList(commodityIdList);
            }else {
                return new PageInfo<XdReceiveDocCommodityODTO>();
            }
        }

        pageDate = PageHelper.startPage(xdReceiveDocIDTO.getPageNo(), xdReceiveDocIDTO.getPageSize()).doSelectPageInfo(() -> {
            xdReceiveDocMapper.getXdReceiveDocCommodityInfo(xdReceiveDocIDTO);
        });

        List<XdReceiveDocCommodityODTO> list = pageDate.getList();
        if(CollectionUtils.isNotEmpty(list)){
            Long storeId = list.get(0).getStoreId();

            List<Long> commodityIdList = list.stream().map(item -> Long.valueOf(item.getCommodityId())).collect(Collectors.toList());
            // 查询商品基础信息
            CommodityVO vo = new CommodityVO();
            vo.setCommodityIdList(commodityIdList);
            List<CommodityBasicEntry> commBasicList = commodityMapper.findCommodityBasicListByParam(vo);
            Map<String, CommodityBasicEntry> commBasicMap = commBasicList.stream().collect(Collectors.toMap(CommodityBasicEntry::getCommodityId, Function.identity()));
            List<CommodityBasicEntry> commBarCodeList = commodityMapper.findCommodityBarCodeByIds(commodityIdList);
            Map<String, String> barCodeMap = commBarCodeList.stream().collect(Collectors.toMap(CommodityBasicEntry::getCommodityId,CommodityBasicEntry::getBarCodes,(key1 , key2)-> key2));


            //List<DictionaryTreeODTO> dictionaryList = dictionaryClient.findAllDictionaryList();
            //Map<String, DictionaryTreeODTO> dictionaryMap = dictionaryList.stream().collect(Collectors.toMap(DictionaryTreeODTO::getId, Function.identity()));

            Shop shop = shopService.getShopByStoreId(storeId);

            // 调用鲜道接口查询商品的货位
            // 获取商品货位
            Map<Long,String> shelfMap = shopReceiveService.getShopShelf(xdReceiveDoc.getShopId(),commodityIdList);
            // 获取货位列表
            List<QueryWarehouseShelfResult> shelfResultList = xdWarehouseShelfCommodityClient.queryShelfList(xdReceiveDoc.getShopId());
            List<WarehouseShelfResultODTO> shelfList = new ArrayList<>();
            if(CollectionUtils.isNotEmpty(shelfResultList)){
                for(QueryWarehouseShelfResult shelfResultODTO : shelfResultList){
                    WarehouseShelfResultODTO warehouseShelfResultODTO = new WarehouseShelfResultODTO();
                    BeanUtils.copyProperties(shelfResultODTO,warehouseShelfResultODTO);
                    shelfList.add(warehouseShelfResultODTO);
                }
            }

            // 查询收货日志
            Map<Long, XdReceiveDocLog> logMap = getXdReceiveDocLogMap(xdReceiveDoc.getId(),null);

            for(XdReceiveDocCommodityODTO odto : list){
                CommodityBasicEntry commodityBasic = commBasicMap.get(odto.getCommodityId());
                if(commodityBasic != null){
                    BeanUtils.copyProperties(commodityBasic,odto);
                }
                odto.setBarCodes(barCodeMap.get(odto.getCommodityId()));
                odto.setShopId(shop.getId());
                odto.setShopCode(shop.getShopCode());
                odto.setShopName(shop.getShopName());

               odto.setDocId(String.valueOf(xdReceiveDocIDTO.getDocId()));
               odto.setDocCode(xdReceiveDoc.getDocCode());
               odto.setDeliveryTime(xdReceiveDoc.getDeliveryTime());

               odto.setOrderShares(odto.getQuantity().divide(odto.getCommodityPackageSpec(),0,BigDecimal.ROUND_UP));
               odto.setRealDeliveryShares(odto.getRealDeliveryQuantity().divide(odto.getCommodityPackageSpec(),0,BigDecimal.ROUND_UP));

               XdReceiveDocLog docLog = logMap.get(Long.valueOf(odto.getCommodityId()));
               if(docLog != null){
                   odto.setReceiveShares(new BigDecimal(docLog.getNormalShares()));
                   odto.setUnReceiveShares(odto.getRealDeliveryShares().subtract(odto.getReceiveShares()));
               }

               if(!org.springframework.util.StringUtils.isEmpty(odto.getBarCodes())){
                   String[] barCode = odto.getBarCodes().trim().split(",");
                   odto.setBarCode(barCode[0]);
                   List barCodeList = java.util.Arrays.asList(barCode);
                   odto.setBarCodeList(barCodeList);
               }

               // 设置货位号
               odto.setShelfNo(shelfMap.get(Long.valueOf(odto.getCommodityId())));
               odto.setShelfList(shelfList);
           }
        }
        return pageDate;
    }

    /**
     * 查看收货情况(不分页)
     * @param xdReceiveDocIDTO
     * @return
     */
    public XdReceiveDocDetailODTO getXdReceiveDocCommodityDetail(XdReceiveDocIDTO xdReceiveDocIDTO) {
        QYAssert.isTrue( null != xdReceiveDocIDTO.getDocId(),"单据id不能为空");
        XdReceiveDoc xdReceiveDoc = xdReceiveDocMapper.selectByPrimaryKey(xdReceiveDocIDTO.getDocId());
        QYAssert.isTrue( null != xdReceiveDoc,"单据不存在");
        XdReceiveDocDetailODTO docDetailODTO = new XdReceiveDocDetailODTO();

        xdReceiveDocIDTO.setDeliveryTime(DateTimeUtil.formatDate(xdReceiveDoc.getDeliveryTime(),"yyyy-MM-dd"));
        xdReceiveDocIDTO.setShopId(xdReceiveDoc.getShopId());
        // 货位不为空
        if(null != xdReceiveDocIDTO.getShelfId()){
            Long commodityId = xdWarehouseShelfCommodityClient.queryCommodityIdByShelf(xdReceiveDocIDTO.getShelfId());
            xdReceiveDocIDTO.setCommodityId(commodityId);
        }

        if(StringUtils.isNotBlank(xdReceiveDocIDTO.getCommodityKey())){
            CommodityVO vo = new CommodityVO();
            vo.setCommodityKey(xdReceiveDocIDTO.getCommodityKey());
            List<CommodityBasicEntry> commBasicList = commodityMapper.findCommodityBasicListByParam(vo);
            if(CollectionUtils.isNotEmpty(commBasicList)){
                List<Long> commodityIdList = commBasicList.stream().map(item -> Long.valueOf(item.getCommodityId())).collect(Collectors.toList());
                xdReceiveDocIDTO.setCommodityIdList(commodityIdList);
            }else {
                return docDetailODTO;
            }
        }
        PageInfo <XdReceiveDocCommodityODTO> pageDate = null ;
        pageDate = PageHelper.startPage(xdReceiveDocIDTO.getPageNo(), xdReceiveDocIDTO.getPageSize()).doSelectPageInfo(() -> {
            xdReceiveDocMapper.getXdReceiveDocCommodityInfo(xdReceiveDocIDTO);
        });

        List<XdReceiveDocCommodityODTO> list = pageDate.getList();
        if(CollectionUtils.isNotEmpty(list)){
            Long storeId = list.get(0).getStoreId();

            // 调用鲜道接口查询商品的货位
            List<Long> commodityIdList = list.stream()
                    .map(item -> Long.valueOf(item.getCommodityId()))
                    .collect(Collectors.toList());
            // 获取商品货位
            Map<Long,String> shelfMap = shopReceiveService.getShopShelf(xdReceiveDoc.getShopId(),commodityIdList);

            // 查询收货日志
            Map<Long, XdReceiveDocLog> logMap = getXdReceiveDocLogMap(xdReceiveDoc.getId(),null);

            // 查询商品基础信息
            CommodityVO vo = new CommodityVO();
            vo.setCommodityIdList(commodityIdList);
            List<CommodityBasicEntry> commBasicList = commodityMapper.findCommodityBasicListByParam(vo);
            Map<String, CommodityBasicEntry> commBasicMap = commBasicList.stream().collect(Collectors.toMap(CommodityBasicEntry::getCommodityId, Function.identity()));
            List<CommodityBasicEntry> commBarCodeList = commodityMapper.findCommodityBarCodeByIds(commodityIdList);
            Map<String, String> barCodeMap = commBarCodeList.stream().collect(Collectors.toMap(CommodityBasicEntry::getCommodityId,CommodityBasicEntry::getBarCodes,(key1 , key2)-> key2));

            Set<Long> dictionaryIdSet = new HashSet<>();
            for (CommodityBasicEntry commodityBasicEntry : commBasicList) {
                dictionaryIdSet.add(commodityBasicEntry.getCommodityUnitId());
            }
            Map<String, DictionaryODTO> dictionaryMap = commonService.listDictionarysByIdSet(dictionaryIdSet);

            Shop shop = shopService.getShopByStoreId(storeId);

            for(XdReceiveDocCommodityODTO odto : list){
                CommodityBasicEntry commodityBasic = commBasicMap.get(odto.getCommodityId());
                if(commodityBasic != null){
                    BeanUtils.copyProperties(commodityBasic,odto);
                }
                odto.setUnit(null != dictionaryMap.get(odto.getCommodityUnitId() + "" ) ? dictionaryMap.get(odto.getCommodityUnitId() + "").getOptionName() : "");
                odto.setBarCodes(barCodeMap.get(odto.getCommodityId()));

                odto.setShopId(shop.getId());
                odto.setShopCode(shop.getShopCode());
                odto.setShopName(shop.getShopName());

                odto.setDocId(String.valueOf(xdReceiveDocIDTO.getDocId()));
                odto.setDocCode(xdReceiveDoc.getDocCode());
                odto.setDeliveryTime(xdReceiveDoc.getDeliveryTime());

                odto.setOrderShares(odto.getQuantity().divide(odto.getCommodityPackageSpec(),0,BigDecimal.ROUND_UP));
                odto.setRealDeliveryShares(odto.getRealDeliveryQuantity().divide(odto.getCommodityPackageSpec(),0,BigDecimal.ROUND_UP));

                XdReceiveDocLog docLog = logMap.get(Long.valueOf(odto.getCommodityId()));
                if(docLog != null){
                    odto.setReceiveShares(new BigDecimal(docLog.getNormalShares()));
                    odto.setUnReceiveShares(odto.getRealDeliveryShares().subtract(odto.getReceiveShares()));
                }

                if(!org.springframework.util.StringUtils.isEmpty(odto.getBarCodes())){
                    String[] barCode = odto.getBarCodes().trim().split(",");
                    odto.setBarCode(barCode[0]);
                    List barCodeList = java.util.Arrays.asList(barCode);
                    odto.setBarCodeList(barCodeList);
                }

                // 设置货位号
                odto.setShelfNo(shelfMap.get(Long.valueOf(odto.getCommodityId())));
//               odto.setShelfList(shelfList);
            }

            docDetailODTO.setList(list);

            // 获取货位列表
            List<QueryWarehouseShelfResult> shelfResultList = xdWarehouseShelfCommodityClient.queryShelfList(xdReceiveDoc.getShopId());
            List<WarehouseShelfResultODTO> shelfList = new ArrayList<>();
            if(CollectionUtils.isNotEmpty(shelfResultList)){
                for(QueryWarehouseShelfResult shelfResultODTO : shelfResultList){
                    WarehouseShelfResultODTO warehouseShelfResultODTO = new WarehouseShelfResultODTO();
                    BeanUtils.copyProperties(shelfResultODTO,warehouseShelfResultODTO);
                    shelfList.add(warehouseShelfResultODTO);
                }
            }

            docDetailODTO.setShelfList(shelfList);
        }

        return docDetailODTO;
    }

    /**
     * 获取收货日志信息 map
     * @return
     */
    private Map<Long, XdReceiveDocLog> getXdReceiveDocLogMap(Long docId, String barCode) {
        Map<Long, XdReceiveDocLog> logMap = new HashMap<>();
        List<XdReceiveDocLog> logList = xdReceiveDocLogMapper.getXdReceiveDocLogList(docId, barCode);
        if(CollectionUtils.isNotEmpty(logList)){
            logMap = logList.stream().collect(Collectors.toMap(XdReceiveDocLog::getCommodityId, Function.identity()));
        }
        return logMap;
    }

    /**
     * 点击收货按钮
     * @param docId
     * @return
     */
    public XdReceiveDocODTO getXdReceiveDocInfo(Long docId) {
        QYAssert.isTrue( null != docId,"单据id不能为空");

        XdReceiveDocODTO xdReceiveDocODTO = new XdReceiveDocODTO();
        XdReceiveDoc xdReceiveDoc = xdReceiveDocMapper.selectByPrimaryKey(docId);
        Shop shop = shopMapper.selectByPrimaryKey(xdReceiveDoc.getShopId());
        BeanUtils.copyProperties(xdReceiveDoc,xdReceiveDocODTO);
        xdReceiveDocODTO.setShopName(shop.getShopName());
        return xdReceiveDocODTO;
    }

    /**
     *根据商品条码精确查找
     * @param docId
     * @param barcode
     * @return
     */
    public List<XdReceiveDocCommodityODTO> getXdReceiveCommodityByBarcode(Long docId,String barcode) {
        QYAssert.isTrue( null != docId,"单据id不能为空");
        List<XdReceiveDocCommodityODTO> receiveDocCommodityODTOList = new ArrayList<>();

        // 是否为称重码(称重码为2打头 18位)
        boolean isWeightCode = barcode.length() == 18 && barcode.startsWith("2");
        if(isWeightCode){
            // 获取条形码
            barcode = barcode.substring(1, 7);
        }
        XdReceiveDoc xdReceiveDoc = xdReceiveDocMapper.selectByPrimaryKey(docId);
        String deliveryTime = DateTimeUtil.formatDate(xdReceiveDoc.getDeliveryTime(),"yyyy-MM-dd");
        XdReceiveDocCommodityODTO commdto = xdReceiveDocMapper.getXdReceiveCommodityByBarcode(xdReceiveDoc.getShopId(),deliveryTime,barcode,1);
        QYAssert.isTrue( null != commdto,"今日订货不存在此商品,或者商品未发货!");

        XdReceiveDocCommodityODTO commdto2 = xdReceiveDocMapper.getXdReceiveCommodityByBarcode(xdReceiveDoc.getShopId(),deliveryTime,barcode,0);
        commdto.setQuantity(commdto2.getQuantity());

        // 获取商品基本信息
        Long commodityId = Long.valueOf(commdto.getCommodityId());
        Long storeId = commdto.getStoreId();
        CommodityVO vo = new CommodityVO();
        vo.setCommodityId(commodityId);
        List<CommodityBasicEntry>  commodityList = commodityMapper.findCommodityBasicListByParam(vo);
        BeanUtils.copyProperties(commodityList.get(0),commdto);

        List<Long> commodityIdList = new ArrayList<>();
        commodityIdList.add(commodityId);
        List<CommodityBasicEntry> commBarCodeList = commodityMapper.findCommodityBarCodeByIds(commodityIdList);
        Map<String, String> barCodeMap = commBarCodeList.stream().collect(Collectors.toMap(CommodityBasicEntry::getCommodityId,CommodityBasicEntry::getBarCodes,(key1 , key2)-> key2));
        commdto.setBarCodes(barCodeMap.get(commdto.getCommodityId()));

        // 设置门店信息
        Shop shop = shopService.getShopByStoreId(storeId);
        commdto.setShopCode(shop.getShopCode());
        commdto.setShopName(shop.getShopName());

        commdto.setOrderShares(commdto.getQuantity().divide(commdto.getCommodityPackageSpec(),0,BigDecimal.ROUND_UP));

        // 查询收货日志
        Map<Long, XdReceiveDocLog> logMap = getXdReceiveDocLogMap(xdReceiveDoc.getId(), barcode);
        XdReceiveDocLog docLog = logMap.get(Long.valueOf(commdto.getCommodityId()));
        if(docLog != null){
            commdto.setReceiveShares(new BigDecimal(docLog.getNormalShares()));
            commdto.setUnReceiveShares(commdto.getOrderShares().subtract(commdto.getReceiveShares()));
        }

        commdto.setRealDeliveryShares(commdto.getRealDeliveryQuantity().divide(commdto.getCommodityPackageSpec(),0,BigDecimal.ROUND_UP));

        if(!org.springframework.util.StringUtils.isEmpty(commdto.getBarCodes())){
            String[] barCode = commdto.getBarCodes().trim().split(",");
            commdto.setBarCode(barCode[0]);
            List barCodeList = java.util.Arrays.asList(barCode);
            commdto.setBarCodeList(barCodeList);
        }

        // 调用鲜道接口查询商品的货位
        // 获取商品货位
        Map<Long,String> shelfMap = shopReceiveService.getShopShelf(xdReceiveDoc.getShopId(),commodityIdList);
        String shelfNo = shelfMap.get(Long.valueOf(commdto.getCommodityId()));
        if(StringUtils.isBlank(shelfNo)){
            List<QueryWarehouseShelfResult> shelfResultList = xdWarehouseShelfCommodityClient.queryShelfList(xdReceiveDoc.getShopId());
            List<WarehouseShelfResultODTO> shelfList = new ArrayList<>();
            if(CollectionUtils.isNotEmpty(shelfResultList)){
                for(QueryWarehouseShelfResult shelfResultODTO : shelfResultList){
                    WarehouseShelfResultODTO warehouseShelfResultODTO = new WarehouseShelfResultODTO();
                    BeanUtils.copyProperties(shelfResultODTO,warehouseShelfResultODTO);
                    shelfList.add(warehouseShelfResultODTO);
                }
            }
            commdto.setShelfList(shelfList);

        }else {
            commdto.setShelfNo(shelfNo);
        }
        receiveDocCommodityODTOList.add(commdto);
        return receiveDocCommodityODTOList;
    }

    /**
     * 收货操作(鲜道前置仓)
     * @param
     */
    @Transactional(rollbackFor = Exception.class)
    public void addXdReceive(List<XdReceiveDocCommodityIDTO> xdReceiveDocCommodityIDTOList, Long userId) {
        QYAssert.isTrue( CollectionUtils.isNotEmpty(xdReceiveDocCommodityIDTOList),"收货列表不能为空");
        List<String> commodityIds = xdReceiveDocCommodityIDTOList.stream().map(XdReceiveDocCommodityIDTO::getCommodityId).distinct().collect(Collectors.toList());
        QYAssert.isTrue(commodityIds.size() == xdReceiveDocCommodityIDTOList.size(), "商品重复");

        XdReceiveDocCommodityIDTO xdReceiveDocCommodityIDTO = xdReceiveDocCommodityIDTOList.get(0);
        QYAssert.isTrue( null != xdReceiveDocCommodityIDTO.getDocId(),"单据id不能为空");
        XdReceiveDoc xdReceiveDoc = xdReceiveDocMapper.selectByPrimaryKey(xdReceiveDocCommodityIDTO.getDocId());
        String deliveryTime = DateTimeUtil.formatDate(xdReceiveDoc.getDeliveryTime(),"yyyy-MM-dd");

        // 收货日志 list
        List<XdReceiveDocLog> logList = new ArrayList<>();
        // 需要绑定的货位
        List<ShelfCommodityDTO> shelfCommoditylist = new ArrayList<>();

        // 回写实收商品数量(有可能包含团购订单)
        List<XdReceiveOrderCommodityODTO> groupOrderCommodityList = new ArrayList<>();
        for(XdReceiveDocCommodityIDTO idto : xdReceiveDocCommodityIDTOList){
            QYAssert.isTrue( null != idto.getCommodityId(),"商品id不能为空");

            Commodity commodity = commodityMapper.selectByPrimaryKey(Long.valueOf(idto.getCommodityId()));

            Boolean shelfEmpty = (null == idto.getShelfNo() && null == idto.getShelfId());
            QYAssert.isTrue(!shelfEmpty,"商品名称:"+commodity.getCommodityName()+"\n规格:"+commodity.getCommoditySpec()+"\n货位不能为空");

            // 判断当前商品实收数量是否大于未收数量，大于不允许超收
            BigDecimal unReceiveQuantity = xdReceiveDocMapper.getUnreceiveQuantityByParam(xdReceiveDoc.getShopId(),deliveryTime,Long.valueOf(idto.getCommodityId()));
            QYAssert.isTrue( null != unReceiveQuantity,"今日订货不存在此商品,或者商品未发货!"+commodity.getCommodityName()+"\n规格:"+commodity.getCommoditySpec());
            // 收货总数量
            BigDecimal receiveTotalQuantity = idto.getNormalQuantity().add(idto.getAbnormalQuantity());
            QYAssert.isTrue( receiveTotalQuantity.compareTo(BigDecimal.ZERO) > 0,"收货数必须大于0");

            String msg = "商品名称:"+commodity.getCommodityName()+"\n规格:"+commodity.getCommoditySpec();
            // 1:标品  2:散称商品 3:称重包装品
            if(idto.getCommodityType().equals(1) || idto.getCommodityType().equals(3)){
                msg = msg + "\n本次收份数:"+receiveTotalQuantity.divide(idto.getCommodityPackageSpec(),0,BigDecimal.ROUND_UP)+"\n待收货份数:"+unReceiveQuantity.divide(idto.getCommodityPackageSpec(),0,BigDecimal.ROUND_UP) +"\n超收货份数:"+receiveTotalQuantity.subtract(unReceiveQuantity).divide(idto.getCommodityPackageSpec(),0,BigDecimal.ROUND_UP)+",请确认";

            }else if(idto.getCommodityType().equals(2)){
                 msg = msg + "\n本次收货量:"+receiveTotalQuantity+"\n待收货量:"+unReceiveQuantity +"\n超收货量:"+receiveTotalQuantity.subtract(unReceiveQuantity)+",请确认";
            }
            QYAssert.isTrue(receiveTotalQuantity.compareTo(unReceiveQuantity) <= 0 ,msg);

            // 如果前端是选择货位的，则重新绑定货位
            if(idto.getShelfId() != null){
                ShelfCommodityDTO  shelfCommodityDTO = new ShelfCommodityDTO();
                shelfCommodityDTO.setCommodityId(Long.valueOf(idto.getCommodityId()));
                shelfCommodityDTO.setShelfId(idto.getShelfId());
                shelfCommoditylist.add(shelfCommodityDTO);
            }

            // 回写订货单
            //按照扫码的条码填写的订单中该商品的实收数（正常+异常），若该商品有多个订单，且存在正常订货，配货，赠品三种订货的类型，
            // 需先满足正常订货数，后配货，最后再赠品的逻辑进行回写，通过商品明细ID进行区分。
            //若正常订货的商品出现在多个订单，则优先满足订货少的订单，再满足订单多的订单；若订货数一样，优先按照订单号小的先回写
            // 1,查询当前门店，订货时间，当前商品关联的订单信息
            List<XdReceiveOrderCommodityODTO> orderCommodityList = xdReceiveDocMapper.getOrderCommodityInfo(xdReceiveDoc.getShopId(),deliveryTime,Long.valueOf(idto.getCommodityId()));
            CommodityVO vo = new CommodityVO();
            vo.setCommodityId(orderCommodityList.get(0).getCommodityId());
            List<CommodityBasicEntry> commodityBasicList = commodityMapper.findCommodityBasicListByParam(vo);
            for(XdReceiveOrderCommodityODTO comm : orderCommodityList){
                comm.setCommodityPackageSpec(commodityBasicList.get(0).getCommodityPackageSpec());
                comm.setCommodityName(commodityBasicList.get(0).getCommodityName());

                Boolean isGroupCommodity = "团购".equals(comm.getOrderRemark()) && idto.getAbnormalQuantity().compareTo(BigDecimal.ZERO) > 0;
                QYAssert.isTrue(!isGroupCommodity,comm.getCommodityName() + " 商品是团购商品，只能收到正常库！");

                XdReceiveOrderCommodityODTO group = new XdReceiveOrderCommodityODTO();
                BeanUtils.copyProperties(comm,group);

                // 如果实收数量小于实发数量，说明此次就要收进该单里面
                if(comm.getRealReceiveQuantity().compareTo(comm.getRealDeliveryQuantity()) < 0){

                    //如果实际总收的数量大于此订单未收的数量，则继续下一单。否则break
                    BigDecimal unReceiveQuantityItem = comm.getRealDeliveryQuantity().subtract(comm.getRealReceiveQuantity());
                    if(receiveTotalQuantity.compareTo(unReceiveQuantityItem) > 0){
                        xdReceiveDocMapper.updateRealReceiveQuantity(unReceiveQuantityItem, comm.getSubOrderItemId(), comm.getCommodityId());
                        receiveTotalQuantity = receiveTotalQuantity.subtract(unReceiveQuantityItem);
                        group.setQuantity(unReceiveQuantityItem);
                        groupOrderCommodityList.add(group);
                    }else {
                        xdReceiveDocMapper.updateRealReceiveQuantity(receiveTotalQuantity, comm.getSubOrderItemId(), comm.getCommodityId());
                        group.setQuantity(receiveTotalQuantity);
                        groupOrderCommodityList.add(group);
                        break;
                    }
                }
            }

            // 设置日志
            setXdReceiveDocLog(userId, xdReceiveDoc, logList, idto);
        }

        // 批量记录日志
        if(CollectionUtils.isNotEmpty(logList)){
            xdReceiveDocLogMapper.insertList(logList);
        }

        // 批量绑定货位
        if(CollectionUtils.isNotEmpty(shelfCommoditylist)){
            List<Long> shelfIdList = shelfCommoditylist.stream().map(ShelfCommodityDTO::getShelfId).distinct().collect(Collectors.toList());
            QYAssert.isTrue(shelfIdList.size() == shelfCommoditylist.size(), "货位不能重复");
            WarehouseShelfCommodityDTO warehouseShelfCommodityDTO = new WarehouseShelfCommodityDTO();
            warehouseShelfCommodityDTO.setWarehouseId(xdReceiveDoc.getShopId());
            warehouseShelfCommodityDTO.setDtoList(shelfCommoditylist);
            xdWarehouseShelfCommodityClient.batchInsert(warehouseShelfCommodityDTO);
        }

        // 调用鲜到入库操作(包含团购商品)
        addStockReceipt(xdReceiveDocCommodityIDTOList,groupOrderCommodityList,xdReceiveDoc.getId(),xdReceiveDoc.getDocCode(),xdReceiveDoc.getShopId(),userId);
    }


    /**
     * 调用鲜到入库操作(包含团购商品)
     */
    @Transactional(rollbackFor = Exception.class)
    public void addStockReceipt(List<XdReceiveDocCommodityIDTO> xdReceiveDocCommodityIDTOList,List<XdReceiveOrderCommodityODTO> groupOrderCommodityList,Long docId,String docCode,Long shopId,Long userId){
        List<XdReceiveOrderCommodityODTO> groupList = getGroupOrderList(groupOrderCommodityList,true);
        List<XdReceiveOrderCommodityODTO> orderList = getGroupOrderList(groupOrderCommodityList,false);
        Map<String, XdReceiveDocCommodityIDTO> idtoMap = xdReceiveDocCommodityIDTOList.stream().collect(Collectors.toMap(XdReceiveDocCommodityIDTO::getCommodityId, Function.identity()));

        List<StockReceiptItemDTO> groupItemList = new ArrayList<>(); // 团购的商品
        List<StockReceiptItemDTO> orderItemList = new ArrayList<>(); // 非团购商品

        if(CollectionUtils.isNotEmpty(groupList)) {
            for (XdReceiveOrderCommodityODTO odto : groupList) {
                StockReceiptItemDTO itemDTO = getStockReceiptItemDTO(idtoMap, odto);
                groupItemList.add(itemDTO);
            }
        }

        if(CollectionUtils.isNotEmpty(orderList)){
            for(XdReceiveOrderCommodityODTO odto : orderList){
                StockReceiptItemDTO itemDTO = getStockReceiptItemDTO(idtoMap, odto);
                orderItemList.add(itemDTO);
            }
        }

        StockReceiptIDTO stockReceiptIDTO = new StockReceiptIDTO();
        stockReceiptIDTO.setUserId(userId);
        stockReceiptIDTO.setReferId(docId);
        //stockReceiptIDTO.setReferCode(docCode);
        stockReceiptIDTO.setWarehouseId(shopId);
        if(CollectionUtils.isNotEmpty(groupItemList)){
            stockReceiptIDTO.setReferCode("TG"+docCode);
            stockReceiptIDTO.setCommodityList(groupItemList);
            grouponClient.groupStockReceipt(stockReceiptIDTO);
        }
        if(CollectionUtils.isNotEmpty(orderItemList)){
            stockReceiptIDTO.setReferCode(docCode);
            stockReceiptIDTO.setCommodityList(orderItemList);
            xdStockClient.stockReceipt(stockReceiptIDTO);

        }
    }

    /**
     * 把实际总收的正常品数量和异常品数量 分摊到对应的团购订单和普通订单
     * 正常品数量 优先分配到团购订单上面
     * @param idtoMap
     * @param odto
     * @return
     */
    private StockReceiptItemDTO getStockReceiptItemDTO(Map<String, XdReceiveDocCommodityIDTO> idtoMap, XdReceiveOrderCommodityODTO odto) {
        StockReceiptItemDTO itemDTO = new StockReceiptItemDTO();
        Long commodityId = odto.getCommodityId();
        itemDTO.setCommodityId(commodityId);

        XdReceiveDocCommodityIDTO doc = idtoMap.get(commodityId + "");
        if (doc.getAbnormalQuantity().compareTo(odto.getQuantity()) <= 0) {
            itemDTO.setAbnormalQuantity(doc.getAbnormalQuantity());
            itemDTO.setNormalQuantity(odto.getQuantity().subtract(doc.getAbnormalQuantity()));
            setQuantity(odto, itemDTO);

            doc.setAbnormalQuantity(BigDecimal.ZERO);
        } else {
            itemDTO.setAbnormalQuantity(odto.getQuantity());
            itemDTO.setNormalQuantity(BigDecimal.ZERO);
            setQuantity(odto, itemDTO);

            doc.setAbnormalQuantity(doc.getAbnormalQuantity().subtract(odto.getQuantity()));
        }
        idtoMap.put(commodityId + "", doc);
        return itemDTO;
    }

    /**
     * 设置数量
     * @param odto
     * @param itemDTO
     */
    private void setQuantity(XdReceiveOrderCommodityODTO odto, StockReceiptItemDTO itemDTO) {
        itemDTO.setQuantity(itemDTO.getNormalQuantity().add(itemDTO.getAbnormalQuantity()));
        itemDTO.setNormalNumber(itemDTO.getNormalQuantity().divide(odto.getCommodityPackageSpec(), 0, BigDecimal.ROUND_UP).intValue());
        itemDTO.setAbnormalNumber(itemDTO.getAbnormalQuantity().divide(odto.getCommodityPackageSpec(), 0, BigDecimal.ROUND_UP).intValue());
        itemDTO.setNumber(itemDTO.getNormalNumber() + itemDTO.getAbnormalNumber());
        itemDTO.setPrice(odto.getPrice());
        itemDTO.setTotalPrice(odto.getPrice().multiply(itemDTO.getQuantity()));
    }

    /**
     * 获取团购非团购商品
     * @param groupOrderCommodityList
     * @param isGroupOrder
     * @return
     */
    public List<XdReceiveOrderCommodityODTO> getGroupOrderList(List<XdReceiveOrderCommodityODTO> groupOrderCommodityList, Boolean isGroupOrder){
        List<XdReceiveOrderCommodityODTO> groupList = new ArrayList<>();
        if(isGroupOrder){
            groupList = groupOrderCommodityList.stream().filter(p -> "团购".equals(p.getOrderRemark())).collect(Collectors.toList());
        }else {
            groupList = groupOrderCommodityList.stream().filter(p ->  !"团购".equals(p.getOrderRemark())).collect(Collectors.toList());
        }

        // 合并重复
        List<XdReceiveOrderCommodityODTO> result = groupList.stream()
                .collect(Collectors.toMap(XdReceiveOrderCommodityODTO::getCommodityId, a -> a, (o1,o2)-> {
                    o1.setQuantity(o1.getQuantity().add(o2.getQuantity()));
                    return o1;
                })).values().stream().collect(Collectors.toList());
        return result;
    }

    /**
     * 记录收货日志
     * @param userId
     * @param xdReceiveDoc
     * @param logList
     * @param idto
     */
    private void setXdReceiveDocLog(Long userId, XdReceiveDoc xdReceiveDoc, List<XdReceiveDocLog> logList, XdReceiveDocCommodityIDTO idto) {
        XdReceiveDocLog xdReceiveDocLog = new XdReceiveDocLog();
        xdReceiveDocLog.setDocId(xdReceiveDoc.getId());
        xdReceiveDocLog.setShopId(xdReceiveDoc.getShopId());
        xdReceiveDocLog.setDeliveryTime(xdReceiveDoc.getDeliveryTime());
        xdReceiveDocLog.setCommodityId(Long.valueOf(idto.getCommodityId()));
        xdReceiveDocLog.setNormalQuantity(idto.getNormalQuantity());
        xdReceiveDocLog.setNormalShares(idto.getNormalShares().longValue());
        xdReceiveDocLog.setAbnormalQuantity(idto.getAbnormalQuantity());
        xdReceiveDocLog.setAbnormalShares(idto.getAbnormalShares().longValue());
        xdReceiveDocLog.setCreateId(userId);
        xdReceiveDocLog.setCreateTime(new Date());
        logList.add(xdReceiveDocLog);
    }
}
