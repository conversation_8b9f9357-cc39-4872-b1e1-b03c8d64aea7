package com.pinshang.qingyun.order.controller;

import com.pinshang.qingyun.order.mapper.entry.settlement.SettlementEntry;
import com.pinshang.qingyun.order.service.SettlementService;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping("/settlement")
@Slf4j
public class SettlementController {

	@Autowired
	private SettlementService settlementService;

	@RequestMapping(value ="/upXsStartBillDateBySettleId",method = RequestMethod.POST)
	public String upXsStartBillDateBySettleId(@RequestParam(value = "id",required = false)Long id, @RequestParam(value = "date",required = false)String date){
		settlementService.upXsStartBillDateBySettleId(id, date);
		
		return "";
	}

	@RequestMapping(value ="/findXsStartBillDateById",method = RequestMethod.POST)
	public String findXsStartBillDateById( @RequestParam(value = "id",required = false)Long id){
		return  settlementService.findXsStartBillDateById(id);
	}
	@RequestMapping(value ="/findStoreIdsBySettleId",method = RequestMethod.POST)
	public List<Long> findStoreIdsBySettleId(@RequestParam(value = "settleId",required = false)Long settleId){
		return  settlementService.findStoreIdsBySettleId(settleId);
	}
	@RequestMapping(value ="/findByCodeOrName",method = RequestMethod.POST)
	public List<SettlementEntry> findByCodeOrName(@RequestParam(value = "str",required = false)String str){
		return  settlementService.findByStr(str);
	}
}
