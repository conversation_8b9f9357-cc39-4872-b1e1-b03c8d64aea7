package com.pinshang.qingyun.order.manage.delivery.context;

import com.pinshang.qingyun.box.utils.SpringUtil;
import com.pinshang.qingyun.order.manage.delivery.dto.DeliveryOrderDTO;
import com.pinshang.qingyun.order.manage.delivery.dto.StoreInfo;
import com.pinshang.qingyun.order.manage.delivery.service.StoreDeliverySettingService;
import com.pinshang.qingyun.order.manage.delivery.setting.StoreDeliverySetting;
import com.pinshang.qingyun.order.manage.delivery.strategy.DeliveryStrategy;
import com.pinshang.qingyun.order.manage.delivery.strategy.impl.RealTimeOverShipmentStrategy;
import com.pinshang.qingyun.order.manage.delivery.strategy.impl.RealTimeUnderDeliveryRefundStrategy;
import com.pinshang.qingyun.order.model.order.Order;
import com.pinshang.qingyun.order.service.OrderListService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 处理多发短交的策略上下文
 */
@Service
@Slf4j
public class DeliveryContext {

    /**
     * 处理超发的策略
     */
    private final DeliveryStrategy realTimeOverShipmentStrategy;
    /**
     * 处理少发的策略
     */
    private final DeliveryStrategy realTimeUnderDeliveryRefundStrategy;
    /**
     * 查询客户类型信息的服务
     */
    private final StoreDeliverySettingService storeDeliverySettingService;


    //不同方法间共享
    private ThreadLocal<List<DeliveryOrderDTO>> overDeliveryOrderDtosHolder = new ThreadLocal<>();
    private ThreadLocal<List<DeliveryOrderDTO>> underDeliveryOrderDtosHolder = new ThreadLocal<>();

    @Autowired
    public DeliveryContext(RealTimeOverShipmentStrategy realTimeOverShipmentStrategy,
                           RealTimeUnderDeliveryRefundStrategy realTimeUnderDeliveryRefundStrategy,
                           StoreDeliverySettingService storeDeliverySettingService) {
        this.realTimeOverShipmentStrategy = realTimeOverShipmentStrategy;
        this.realTimeUnderDeliveryRefundStrategy = realTimeUnderDeliveryRefundStrategy;
        this.storeDeliverySettingService = storeDeliverySettingService;
    }

    public void executeStrategy(List<DeliveryOrderDTO> deliveryOrderDTOS) {

        //校验是否存在超发少发的数据，如果不存在，提前结束
        if(checkConditionNotPass(deliveryOrderDTOS)){
            return;
        }

        /**
        获取客户类型设置的信息
        collectStatus
        settleBillCalcType //结算时处理短交
        settleBillReturnAmountState  //结算类型
        jmFlag
        storeType
        deliveryOrderDTO.getStoreTypeId
        storeTypeCode
        */
        Map<Long, StoreDeliverySetting> storeDeliverySettingMap = getStoreDeliverySettingMap(deliveryOrderDTOS);

        try {

            //处理多发
            processOverDelivery(storeDeliverySettingMap);

            //处理短交
            processUnderDelivery(storeDeliverySettingMap);

        }finally {
            overDeliveryOrderDtosHolder.remove();
            underDeliveryOrderDtosHolder.remove();
        }

    }

    private void processOverDelivery(Map<Long, StoreDeliverySetting> storeDeliverySettingMap) {

        List<DeliveryOrderDTO> overDeliveryOrderDTOS = overDeliveryOrderDtosHolder.get();
        if(SpringUtil.isNotEmpty(overDeliveryOrderDTOS)){
            overDeliveryOrderDTOS.forEach(
                    overDeliveryOrderDTO -> {
                        log.info(String.format("开始处理订单[id=%s]超发", overDeliveryOrderDTO.getOrderId()));
                        realTimeOverShipmentStrategy.handle(overDeliveryOrderDTO, storeDeliverySettingMap.get(overDeliveryOrderDTO.getOrderId()));
                    }
            );
        }
    }

    private void processUnderDelivery(Map<Long, StoreDeliverySetting> storeDeliverySettingMap) {

        List<DeliveryOrderDTO> underDeliveryOrderDTOS = underDeliveryOrderDtosHolder.get();
        if(SpringUtil.isNotEmpty(underDeliveryOrderDTOS)){
            underDeliveryOrderDTOS.forEach(
                    underDeliveryOrderDTO -> {
                        log.info(String.format("开始处理订单[id=%s]少发", underDeliveryOrderDTO.getOrderId()));
                        realTimeUnderDeliveryRefundStrategy.handle(underDeliveryOrderDTO, storeDeliverySettingMap.get(underDeliveryOrderDTO.getOrderId()));
                    }
            );
        }
    }

    private Map<Long, StoreDeliverySetting> getStoreDeliverySettingMap(List<DeliveryOrderDTO> deliveryOrderDTOS) {
        Map<Long,StoreDeliverySetting> storeDeliverySettingMap = storeDeliverySettingService
                .getStoreDeliverySetting(deliveryOrderDTOS);
        return storeDeliverySettingMap;
    }

    /**
     * 是否需要处理多发少发，如果没有满足条件的数据，提前结束，返回校验未通过
     * @param deliveryOrderDTOS
     * @return
     */
    private boolean checkConditionNotPass(List<DeliveryOrderDTO> deliveryOrderDTOS) {

        if(SpringUtil.isEmpty(deliveryOrderDTOS)){
            return true;
        }
        // 处理多发
        List<DeliveryOrderDTO> overDeliveryOrderDtos = deliveryOrderDTOS.stream().filter(
                deliveryOrderDTO -> deliveryOrderDTO.getOverDeliveryMoney().compareTo(BigDecimal.ZERO) > 0
        ).collect(Collectors.toList());

        // 处理少发
        List<DeliveryOrderDTO> underDeliveryOrderDtos = deliveryOrderDTOS.stream().filter(
                deliveryOrderDTO -> deliveryOrderDTO.getUnderDeliveryMoney().compareTo(BigDecimal.ZERO) > 0
        ).collect(Collectors.toList());

        if(SpringUtil.isNotEmpty(overDeliveryOrderDtos)){
            overDeliveryOrderDtosHolder.set(overDeliveryOrderDtos);
        }

        if(SpringUtil.isNotEmpty(underDeliveryOrderDtos)){
            underDeliveryOrderDtosHolder.set(underDeliveryOrderDtos);
        }

        return false;
    }

}
