package com.pinshang.qingyun.order.service;

import com.pinshang.qingyun.infrastructure.components.IMqSenderComponent;
import com.pinshang.qingyun.infrastructure.components.params.mq.MqMessage;
import com.pinshang.qingyun.kafka.base.KafkaTopicConstant;
import com.pinshang.qingyun.kafka.enums.KafkaMessageOperationTypeEnum;
import com.pinshang.qingyun.kafka.enums.KafkaMessageTypeEnum;
import com.pinshang.qingyun.order.vo.splitOrder.SplitOrderKafkaVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

/**
 * @Author: sk
 * @Date: 2024/11/14
 */
@Slf4j
@Service
public class SplitOrderSendKfkService {

    @Autowired
    private IMqSenderComponent mqSenderComponent;
    @Value("${application.name.switch}")
    private String applicationNameSwitch;

    /**
     * 发送拆单消息(1期保存订单、1期修改订单、1期复制订单、1期创建补货单、1期导入保存订单、1期取消订单、
     * 批量取消订单、批发创建订单、鲜达创建订单、鲜达创建预订单转正式订单)
     * @param kafkaVo
     */
    public void sendSplitOrderKfkMsg(SplitOrderKafkaVo kafkaVo){

        mqSenderComponent.send(applicationNameSwitch + KafkaTopicConstant.SPLIT_TOPIC,
                kafkaVo,
                MqMessage.MQ_KAFKA,
                KafkaMessageTypeEnum.SPLIT_ORDER.name(),
                KafkaMessageOperationTypeEnum.INSERT.name());
    }

}
