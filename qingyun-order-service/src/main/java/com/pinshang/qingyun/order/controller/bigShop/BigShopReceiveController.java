package com.pinshang.qingyun.order.controller.bigShop;

import com.github.pagehelper.PageInfo;
import com.pinshang.qingyun.base.api.TokenInfo;
import com.pinshang.qingyun.base.configure.expand.QYAssert;
import com.pinshang.qingyun.base.util.FastThreadLocalUtil;
import com.pinshang.qingyun.common.dto.DelayMsgIDTO;
import com.pinshang.qingyun.common.service.DelayMsgClient;
import com.pinshang.qingyun.order.dto.bigShop.*;
import com.pinshang.qingyun.order.service.ConsigneeCommonService;
import com.pinshang.qingyun.order.service.bigShop.BigShopAutoReceiveService;
import com.pinshang.qingyun.order.service.bigShop.BigShopReceiveService;
import com.pinshang.qingyun.renderer.annotation.MethodRender;
import com.pinshang.qinyun.cache.enums.RedisDelayQueueEnum;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * @Author: sk
 * @Date: 2024/10/11
 */

@RestController
@RequestMapping("/bigShopReceive")
@Api("大店收货管理")
public class BigShopReceiveController {

    @Autowired
    private BigShopReceiveService bigShopReceiveService;
    @Autowired
    private RedissonClient redissonClient;
    @Autowired
    private DelayMsgClient delayMsgClient;
    @Autowired
    private BigShopAutoReceiveService bigShopAutoReceiveService;
    @Autowired
    private ConsigneeCommonService consigneeCommonService;

    /**
     * 查询收货单列表
     * @return
     */
    @MethodRender
    @ApiOperation(value = "查询收货单列表", notes = "查询收货单列表", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @RequestMapping(value = "/getReceiveDocList", method = RequestMethod.POST)
    public PageInfo<DdReceiveDocODTO> getReceiveDocList(@RequestBody DdReceiveDocPageIDTO idto) {
        TokenInfo tokenInfo = FastThreadLocalUtil.getQY();
        idto.setShopId(tokenInfo.getShopId());
        return bigShopReceiveService.getReceiveDocList(idto);
    }

    @MethodRender
    @ApiOperation(value = "收货详情、去收货", notes = "收货详情、去收货", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @RequestMapping(value = "/detailReceiveDocCommodityList", method = RequestMethod.GET)
    public DdReceiveDocODTO detailReceiveDocCommodityList(@RequestParam("docId") Long docId,
                                                          @RequestParam(value = "commodityBarCode", required = false) String commodityBarCode,
                                                          @RequestParam(value = "isQueryPendingItems", required = false) Boolean isQueryPendingItems) {
        return bigShopReceiveService.getReceiveDocCommodityList(docId, commodityBarCode,isQueryPendingItems);
    }


    @ApiOperation(value = "打印收货清单", notes = "打印收货清单", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @RequestMapping(value = "/printReceiveDocCommodityList", method = RequestMethod.GET)
    public DdReceiveDocODTO printReceiveDocCommodityList(@RequestParam("docId") Long docId) {
        return bigShopReceiveService.printReceiveDocCommodityList(docId);
    }

    @ApiOperation(value = "关联订单号", notes = "关联订单号", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @RequestMapping(value = "/receiveOrderList", method = RequestMethod.GET)
    public List<DdReceiveDocOrderODTO> receiveOrderList(@RequestParam("docId") Long docId) {
        return bigShopReceiveService.receiveOrderList(docId);
    }

    /**
     * 收货操作(大店)
     * @param
     * @return
     */
    @ApiOperation(value = "收货操作(大店)", notes = "收货操作(大店)", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @RequestMapping(value = "/addBigShopReceive",method = RequestMethod.POST)
    public Boolean addBigShopReceive(@RequestBody DdReceiveDocSaveIDTO idto){
        QYAssert.notNull(idto.getDocId(), "单据id不能为空");

        RLock lock = redissonClient.getLock("order:addBigShopReceive:" + idto.getDocId());
        if (lock.tryLock()) {
            try {
                TokenInfo tokenInfo = FastThreadLocalUtil.getQY();
                idto.setUserId(tokenInfo.getUserId());
                idto.setEnterpriseId(tokenInfo.getEnterpriseId());
                bigShopReceiveService.addBigShopReceive(idto);
            } finally {
                lock.unlock();
            }
        }else {
            QYAssert.isFalse("系统繁忙,请勿频繁操作!");
        }
        return true;
    }

    @ApiOperation(value = "testAddRedissDelay", notes = "testAddRedissDelay", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @RequestMapping(value = "/testAddRedissDelay",method = RequestMethod.POST)
    public Boolean testAddRedissDelay(@RequestParam(value = "data") String data){
        // 如果存在少货，则放入延长队列3分钟，等待处理
        DelayMsgIDTO delayMsgIDTO = new DelayMsgIDTO(
                RedisDelayQueueEnum.DD_RECEIVE_SHORT_RETURN.getCode(),
                data,
                1L,
                TimeUnit.SECONDS);
        delayMsgClient.addDelayQueue(delayMsgIDTO);
        return true;
    }

    @ApiOperation(value = "testAddBigShopShortReturnDelay", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @RequestMapping(value = "/testAddBigShopShortReturnDelay",method = RequestMethod.GET)
    public void testAddBigShopShortReturnDelay(@RequestParam(value = "docId") Long docId,
                                               @RequestParam(value = "enterpriseId") Long enterpriseId,
                                               @RequestParam(value = "userId") Long userId){
        consigneeCommonService.addBigShopShortReturnDelay(docId, enterpriseId, userId);
    }


    @RequestMapping(value = "/autoReceiveBigShop",method = RequestMethod.POST)
    @ApiOperation(value="大店自动收货")
    public Boolean autoReceiveBigShop(@RequestParam(value = "orderTime",required = false) String orderTime)  {
        return bigShopAutoReceiveService.autoReceiveBigShopOrder(orderTime);
    }

    /**
     * 登记收货单里单个商品
     * @param idto 确认数量
     * @return 操作结果
     */
    @ApiOperation(value = "登记收货单里单个商品", notes = "登记收货单里单个商品", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @RequestMapping(value = "/recordSingleCommodity", method = RequestMethod.POST)
    public Boolean recordSingleCommodity(@RequestBody DdReceiveDocRecordIDTO idto) {
        RLock lock = redissonClient.getLock("order:recordSingleCommodity:" + idto.getDocId() + ":" + idto.getCommodityId());
        if (lock.tryLock()) {
            try {
                return bigShopReceiveService.recordSingleCommodity(idto);
            } finally {
                lock.unlock();
            }
        } else {
            QYAssert.isFalse("系统繁忙,请勿频繁操作!");
            return false;
        }
    }

    /**
     * PDA整单收货
     * @param idto 整单收货信息
     * @return 操作结果
     */
    @ApiOperation(value = "PDA整单收货", notes = "PDA整单收货", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @RequestMapping(value = "/pdaBigShopReceive", method = RequestMethod.POST)
    public Boolean pdaBigShopReceive(@RequestBody DdReceiveDocBatchRecordIDTO idto) {
        RLock lock = redissonClient.getLock("order:pdaBigShopReceive:" + idto.getDocId());
        if (lock.tryLock()) {
            try {
                return bigShopReceiveService.pdaBigShopReceive(idto);
            } finally {
                lock.unlock();
            }
        } else {
            QYAssert.isFalse("系统繁忙,请勿频繁操作!");
            return null;
        }
    }
}
