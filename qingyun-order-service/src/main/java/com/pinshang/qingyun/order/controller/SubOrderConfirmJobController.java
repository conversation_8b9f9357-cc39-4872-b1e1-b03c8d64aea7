package com.pinshang.qingyun.order.controller;

import com.pinshang.qingyun.order.service.syncOrderJob.SubOrderConfirmService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;


@RestController
@RequestMapping("/subOrder")
@Slf4j
public class SubOrderConfirmJobController {
	@Autowired
	private SubOrderConfirmService subOrderConfirmService;

	/**
     * 定时检测已生成发货单的子单是否在大仓系统产生相应的发货单
	 */
	@PostMapping("/confirmSO2DO")
	public void confirmSO2DO(){
		subOrderConfirmService.confirmSO2DO();
	}
	
}
