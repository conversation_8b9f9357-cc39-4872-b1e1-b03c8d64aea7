package com.pinshang.qingyun.order.mapper.auto;

import com.pinshang.qingyun.base.mybatis.MyMapper;
import com.pinshang.qingyun.order.dto.AutoPreOrderAuditItemDTO;
import com.pinshang.qingyun.order.dto.AutoPreOrderItemODTO;
import com.pinshang.qingyun.order.model.auto.AutoPreOrderItem;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * @Author: liuZhen
 * @DateTime: 2022/5/13 16:02
 */
@Repository
public interface AutoPreOrderItemMapper extends MyMapper<AutoPreOrderItem> {
    List<AutoPreOrderItemODTO> queryByPreOrderId(Long preOrderId);

    List<AutoPreOrderAuditItemDTO> getAutoPreOrderItemsByCode(@Param("autoPreOrderCode") String autoPreOrderCode);
}
