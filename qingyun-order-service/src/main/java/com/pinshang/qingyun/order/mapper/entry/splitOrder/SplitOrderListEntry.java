package com.pinshang.qingyun.order.mapper.entry.splitOrder;

import lombok.Data;

import java.math.BigDecimal;

/*
 * 拆单明细列表
 */
@Data
public class SplitOrderListEntry {
	private Long orderListId;// t_order_list 主键id
	//商品id
	private Long commodityId;
	//物流模式
	private Integer logisticsModel;
	//仓库id
	private Long warehouseId;
	//供应商id
	private Long supplierId;
	//数量
	private BigDecimal quantity;
	//单价
	private BigDecimal price;
	private BigDecimal totalPrice;

	private BigDecimal originalPrice;// 原价

	/** 类型:1订单商品,2赠品,3配货商品,5特惠商品 */
	private Integer type;

	/** 商品类型-1-非组合 2-组合  3-组合子品 */
	private Integer combType;
	private Long combCommodityId; // 属于组合商品id


	private Integer presaleStatus; // 是否预售  0 否   1 是
	private Integer stockType; // 库存依据  1=依据大仓, 2=不限量订货, 3=限量供应

	/**
	 * 原材料比例
	 */
	private Integer sourceRatio;
	/**
	 * 转成品比例
	 */
	private Integer targetRatio;

	/**
	 * 组合商品转换的最小单位商品ID
	 */
	private Long targetCommodityId;

	/**
	 * 组合商品转换状态：0=无转换，1=有转换
	 */
	private Integer convertStatus;

	/**
	 * 组合商品目标数量 : 向上取整 ( quantity / source_ratio * target_ratio)
	 */
	private BigDecimal targetQuantity;

	private Long pricePromotionId;
}
