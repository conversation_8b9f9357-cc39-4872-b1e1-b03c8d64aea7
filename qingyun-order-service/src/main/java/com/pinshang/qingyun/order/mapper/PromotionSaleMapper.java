package com.pinshang.qingyun.order.mapper;

import com.pinshang.qingyun.base.mybatis.MyMapper;
import com.pinshang.qingyun.order.model.promotionSale.PromotionSale;
import com.pinshang.qingyun.order.model.promotionStk.PromotionStk;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * @Author: sk
 * @Date: 2024/7/18
 */
@Repository
public interface PromotionSaleMapper extends MyMapper<PromotionSale> {

    List<PromotionSale> findRatioByStoreId(@Param("storeId") Long storeId,
                                           @Param("orderTime") String orderTime);
}
