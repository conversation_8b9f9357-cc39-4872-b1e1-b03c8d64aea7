package com.pinshang.qingyun.order.mapper.entry.shop;

import java.math.BigDecimal;

public class SubOrderItemInfoEntry {
  private String commodityCode;
  private String commodityId;
  private BigDecimal quantity;
  private BigDecimal price;
  private BigDecimal realReceiveQuantity;
public String getCommodityCode() {
	return commodityCode;
}
public void setCommodityCode(String commodityCode) {
	this.commodityCode = commodityCode;
}
public String getCommodityId() {
	return commodityId;
}
public void setCommodityId(String commodityId) {
	this.commodityId = commodityId;
}
public BigDecimal getQuantity() {
	return quantity;
}
public void setQuantity(BigDecimal quantity) {
	this.quantity = quantity;
}
public BigDecimal getPrice() {
	return price;
}
public void setPrice(BigDecimal price) {
	this.price = price;
}
public BigDecimal getRealReceiveQuantity() {
	return realReceiveQuantity;
}
public void setRealReceiveQuantity(BigDecimal realReceiveQuantity) {
	this.realReceiveQuantity = realReceiveQuantity;
}
}
