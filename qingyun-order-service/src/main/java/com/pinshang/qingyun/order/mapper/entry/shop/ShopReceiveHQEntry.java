package com.pinshang.qingyun.order.mapper.entry.shop;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2017/6/7.
 */
@Data
public class ShopReceiveHQEntry {

    private String id;

    private String shopCode;

    private String shopName;

    private Integer logisticsModel;

    private String orderCode;

    private String subOrderCode;

    private BigDecimal totalPrice;

    private String supplierId;

    private String supplierName;

    private String status;

    private Date orderTime;

    private Date receiveTime;

    private BigDecimal orderAmount;

    private String receiveId;

    private String realName;
    
    //下单时间
    private Date createTime;

    //门店类型
    private Integer shopType;

    //下单人
    private String createName;

}
