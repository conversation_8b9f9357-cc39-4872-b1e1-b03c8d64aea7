package com.pinshang.qingyun.order.service;

import com.pinshang.qingyun.base.enums.order.OrderTypeEnum;
import com.pinshang.qingyun.base.enums.storage.ToBTypeEnums;
import com.pinshang.qingyun.order.dto.FreezeGiftODTO;
import com.pinshang.qingyun.order.enums.OperationTypeEnum;
import com.pinshang.qingyun.order.enums.ProductTypeEnums;
import com.pinshang.qingyun.order.model.order.Order;
import com.pinshang.qingyun.order.vo.order.OrderDto;
import com.pinshang.qingyun.order.vo.order.ShopReceiveOrderVo;
import com.pinshang.qingyun.storage.dto.tob.ToBOrderDetailIDTO;
import com.pinshang.qingyun.storage.dto.tob.WarehouseFreezeInventoryIDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionSynchronizationAdapter;
import org.springframework.transaction.support.TransactionSynchronizationManager;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @Author: sk
 * @Date: 2020/12/18
 */
@Service
@Slf4j
public class GrouponOrderSaveService {


    @Autowired
    private OrderService orderService;
    @Autowired
    private OrderAsyncKafkaService orderAsyncKafkaService;
    @Lazy
    @Autowired
    private BStockService bStockService;
    @Lazy
    @Autowired
    private OrderSaveService orderSaveService;

    /**
     * 保存团购订单、日日鲜自动订货、提货卡提交订单、自动订货
     * @param orderDto
     * @return
     * @throws Throwable
     */
    @Transactional(rollbackFor = Exception.class,propagation = Propagation.REQUIRES_NEW)
    public Order saveGroupOrder(OrderDto orderDto, String autoStr){
        Order order = saveOrder(orderDto, autoStr);
        return order;
    }


    @Transactional(rollbackFor = Exception.class)
    public Order saveXiaoetongOrder(OrderDto orderDto, String autoStr){
        Order order = saveOrder(orderDto, autoStr);
        return order;
    }

    private @NotNull Order saveOrder(OrderDto orderDto, String autoStr) {
        Order order = orderService.saveOrder(orderDto, false, false);
        Order kafkaOrder = orderService.saveGiftOrder(orderDto, order.getId(), false);
        kafkaOrder.setOrderAmount(orderDto.amount());

        Long subOrderId = orderService.createSubOrderAndItem(orderDto, order.getId(), order.getOrderCode(), false, order.getAssociationOrderMap());

        //创建收货单
        ShopReceiveOrderVo receiveDto = new ShopReceiveOrderVo(orderDto.getEnterpriseId(), subOrderId, orderDto.getUserId());
        orderService.newReceiveOrder(receiveDto);

        // 记录订单日志
        orderService.inserOrderHistory(order.getId(), Long.valueOf(order.getOrderCode()), orderDto.getUserId(), "系统", OperationTypeEnum.ORDER_NEW.getCode(), order.getOrderTime(), null, null);

        // 只有自动订货、小鹅通 提交订单冻结库存
        if(OrderTypeEnum.SHOP_AUTO_ORDER.getCode().equals(order.getOrderType())
                || OrderTypeEnum.XIAO_E_TONG_AUTO_ORDER.getCode().equals(order.getOrderType())){
            // 依据大仓和限量的才进行冻结
            List<FreezeGiftODTO> freezeODTOList = orderSaveService.getFreezeGiftList(orderDto);

            if(CollectionUtils.isNotEmpty(freezeODTOList)){
                List<FreezeGiftODTO> giftProductList = freezeODTOList.stream().filter(p -> ProductTypeEnums.GIFT.getCode().equals(p.getType())).collect(Collectors.toList());
                Map<Long, BigDecimal> giftProductMap = giftProductList.stream().collect(Collectors.toMap(FreezeGiftODTO::getCommodityId,FreezeGiftODTO::getOrderQuantity,(key1 , key2)-> key2));

                // 组装冻结明细list
                List<ToBOrderDetailIDTO> toBOrderDetailList = orderSaveService.getToBOrderDetailList(giftProductMap, freezeODTOList);
                WarehouseFreezeInventoryIDTO freezeInventoryIDTO = WarehouseFreezeInventoryIDTO.builder()
                        .orderId(order.getId())
                        .orderCode(order.getOrderCode())
                        .orderTime(order.getOrderTime())
                        .type(ToBTypeEnums.SALE.getCode())
                        .sourceType(order.getOrderType())
                        .deliveryBatch(order.getDeliveryBatch())
                        .storeId(order.getStoreId())
                        .storeCode(null)
                        .storeName(null)
                        .orderCommodityDetailList(toBOrderDetailList)
                        .build();
                // 去掉了保底数量，赠品不足也报错
                bStockService.warehouseFreezeInventory(freezeInventoryIDTO, orderDto.getUserId(), autoStr);
                /*// 冻结赠品判断是否都冻结成功，如果赠品冻结失败。则更新订单明细里面的赠品数量
                if(!giftProductMap.isEmpty() && CollectionUtils.isNotEmpty(responseFreezeList)){
                    orderSaveService.dealFreezeResponse(orderDto, giftProductMap, responseFreezeList, toBOrderDetailList, order.getId());
                }*/
            }
        }
        TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronizationAdapter() {
            @Override
            public void afterCommit() {
                orderAsyncKafkaService.sendKafkaSaveOrderMessage(kafkaOrder);
            }
        });
        return order;
    }
}
