package com.pinshang.qingyun.order.controller;

import com.pinshang.qingyun.order.mapper.entry.EmployeePrinterEntry;
import com.pinshang.qingyun.order.mapper.entry.StorePrinterEntry;
import com.pinshang.qingyun.order.mapper.entry.SystemPrinterEntry;
import com.pinshang.qingyun.order.service.SystemPrinterService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping("/systemPrinter")
@Slf4j
public class SystemPrinterController {

	@Autowired
	private SystemPrinterService systemPrinterService;

	@GetMapping("/querySystemPrinterByUserId/{userId}")
	public List<SystemPrinterEntry> queryUnDeliverySubOrderList(@PathVariable("userId") Long userId){
		return systemPrinterService.querySystemPrinterByUserId(userId);
	}

	@GetMapping("/queryEmployeePrinterByDeliveryManCode/{employeeCode}")
	public EmployeePrinterEntry queryEmployeePrinterByDeliveryManCode(@PathVariable("employeeCode") String employeeCode){
		return systemPrinterService.queryEmployeePrinterByDeliveryManCode(employeeCode);
	}

	@GetMapping("/queryStorePrinterByStoreCode/{storeCode}")
	public List<StorePrinterEntry> queryStorePrinterByStoreCode(@PathVariable("storeCode") String storeCode){
		return systemPrinterService.queryStorePrinterByStoreCode(storeCode);
	}
}
