package com.pinshang.qingyun.order.controller;

import com.pinshang.qingyun.base.page.TablePageInfo;
import com.pinshang.qingyun.order.dto.GroupOrderIDTO;
import com.pinshang.qingyun.order.dto.GroupOrderODTO;
import com.pinshang.qingyun.order.enums.ExcelSheetTitleEnum;
import com.pinshang.qingyun.order.service.GrouponOrderService;
import com.pinshang.qingyun.order.util.ViewExcel;
import com.pinshang.qingyun.renderer.annotation.MethodRender;
import com.pinshang.qingyun.renderer.service.IRenderService;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.servlet.ModelAndView;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * @Author: sk
 * @Date: 2021/12/28
 *
 * 迁移至order-manage
 */
@Deprecated
@RestController
@RequestMapping("/grouponOrder")
@Slf4j
public class GroupOrderController {


    @Autowired
    private GrouponOrderService grouponOrderService;

    @Autowired
    private IRenderService renderService;

    /**
     * B 端团购订单日汇总
     * @param timeStamp
     * @return
     * @throws
     */
    @RequestMapping(value = "/grouponOrderDay", method = RequestMethod.POST)
    public Boolean grouponOrderDay(@RequestParam(value = "timeStamp",required = false) String timeStamp){
        return grouponOrderService.grouponOrderDay(timeStamp);
    }


    @MethodRender
    @ApiOperation(value = " 商品订货分析表(团购)", notes = " 商品订货分析表(团购)",produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @RequestMapping(value = "/commodityGroupOrder", method = RequestMethod.GET)
    public TablePageInfo<GroupOrderODTO> commodityGroupOrder(GroupOrderIDTO idto) {
        idto.setGroupType(1);
        return grouponOrderService.shopCommodityGroupOrder(idto);
    }

    @ApiOperation(value = "商品订货分析表(团购)-导出", notes = "商品订货分析表(团购)-导出", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @RequestMapping(value = "/exportInfo/commodityGroupOrder", method = RequestMethod.GET)
    public ModelAndView exportInfoCommodityGroupOrder(GroupOrderIDTO idto) {
        idto.setPageNo(1);
        idto.setPageSize(65536);
        idto.setGroupType(1);
        TablePageInfo<GroupOrderODTO> result = grouponOrderService.shopCommodityGroupOrder(idto);

        List<GroupOrderODTO> list = result.getList();
        Map<String, List<String>> data = new HashMap<>();
        List<String> dataLst = new ArrayList<>();
        int i = 0;
        BigDecimal realTotalAmount = BigDecimal.ZERO;
        if (null != list && !list.isEmpty()) {
            renderService.render(list, "/exportInfo/commodityGroupOrder");
            GroupOrderODTO header = (GroupOrderODTO) result.getHeader();
            header.setGrouponCode("合计");
            list.add(0, header);
            for (GroupOrderODTO dto : list) {
                dataLst = new ArrayList<>();
                dataLst.add(dto.getGrouponCode());
                dataLst.add(dto.getGrouponTime());
                dataLst.add(dto.getShopCode());
                dataLst.add(dto.getShopName());
                dataLst.add(dto.getCommodityCode());
                dataLst.add(dto.getCommodityName());
                dataLst.add(dto.getCommoditySpec());
                dataLst.add(dto.getBarCode());
                dataLst.add(dto.getCommodityUnitName());
                dataLst.add(dto.getGrouponQuantity() == null ? "" : dto.getGrouponQuantity().toString());
                dataLst.add(dto.getGrouponAmount() == null ? "" : dto.getGrouponAmount().toString());
                dataLst.add(dto.getOrderQuantity() == null ? "" : dto.getOrderQuantity().toString());
                dataLst.add(dto.getOrderAmount() == null ? "" : dto.getOrderAmount().toString());
                data.put("key_" + i++, dataLst);
            }
        }
        Map<String, Object> map = new HashMap<>();
        // 文件名
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmss");
        String filename = "商品订货分析表\n" + "_" + sdf.format(new Date());
        // 参数设置
        map.put("filename", filename);
        map.put("sheetTitle", ExcelSheetTitleEnum.COMMODITY_GROUP_ORDER);
        map.put("data", data);
        //map.put("title", "商品实发汇总报表");
        //map.put("titleCells", (short)3);
        ViewExcel viewExcel = new ViewExcel();
        return new ModelAndView(viewExcel, map);
    }


    @MethodRender
    @ApiOperation(value = " 门店订货分析表(团购)", notes = " 门店订货分析表(团购)",produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @RequestMapping(value = "/shopGroupOrder", method = RequestMethod.GET)
    public TablePageInfo<GroupOrderODTO> shopGroupOrder(GroupOrderIDTO idto) {
        idto.setGroupType(2);
        return grouponOrderService.shopCommodityGroupOrder(idto);
    }

    @ApiOperation(value = "门店订货分析表(团购)-导出", notes = "门店订货分析表(团购)-导出", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @RequestMapping(value = "/exportInfo/shopGroupOrder", method = RequestMethod.GET)
    public ModelAndView exportInfoShopGroupOrder(GroupOrderIDTO idto) {
        idto.setPageNo(1);
        idto.setPageSize(65536);
        idto.setGroupType(2);
        TablePageInfo<GroupOrderODTO> result = grouponOrderService.shopCommodityGroupOrder(idto);

        List<GroupOrderODTO> list = result.getList();
        Map<String, List<String>> data = new HashMap<>();
        List<String> dataLst = new ArrayList<>();
        int i = 0;
        BigDecimal realTotalAmount = BigDecimal.ZERO;
        if (null != list && !list.isEmpty()) {
            renderService.render(list, "/exportInfo/shopGroupOrder");
            GroupOrderODTO header = (GroupOrderODTO) result.getHeader();
            header.setShopCode("合计");
            list.add(0, header);
            for (GroupOrderODTO dto : list) {
                dataLst = new ArrayList<>();
                dataLst.add(dto.getShopCode());
                dataLst.add(dto.getShopName());
                dataLst.add(dto.getGrouponQuantity() == null ? "" : dto.getGrouponQuantity().toString());
                dataLst.add(dto.getGrouponAmount() == null ? "" : dto.getGrouponAmount().toString());
                dataLst.add(dto.getOrderQuantity() == null ? "" : dto.getOrderQuantity().toString());
                dataLst.add(dto.getOrderAmount() == null ? "" : dto.getOrderAmount().toString());
                data.put("key_" + i++, dataLst);
            }
        }
        Map<String, Object> map = new HashMap<>();
        // 文件名
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmss");
        String filename = "门店订货分析表\n" + "_" + sdf.format(new Date());
        // 参数设置
        map.put("filename", filename);
        map.put("sheetTitle", ExcelSheetTitleEnum.SHOP_GROUP_ORDER);
        map.put("data", data);
        //map.put("title", "商品实发汇总报表");
        //map.put("titleCells", (short)3);
        ViewExcel viewExcel = new ViewExcel();
        return new ModelAndView(viewExcel, map);
    }

}
