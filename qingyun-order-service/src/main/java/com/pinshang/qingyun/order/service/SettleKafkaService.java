package com.pinshang.qingyun.order.service;

import com.pinshang.qingyun.base.enums.settlement.SettleOrderSourceTypeEnum;
import com.pinshang.qingyun.box.utils.DateUtil;
import com.pinshang.qingyun.box.utils.SpringUtil;
import com.pinshang.qingyun.common.dto.DictionaryODTO;
import com.pinshang.qingyun.common.service.DictionaryClient;
import com.pinshang.qingyun.kafka.KafkaConstant;
import com.pinshang.qingyun.kafka.MessageOperationType;
import com.pinshang.qingyun.kafka.MessageType;
import com.pinshang.qingyun.order.dto.xda.tda.LogisticsDeliveryMessage;
import com.pinshang.qingyun.order.mapper.SubOrderMapper;
import com.pinshang.qingyun.order.model.order.SubOrder;
import com.pinshang.qingyun.order.vo.order.ItemVo;
import com.pinshang.qingyun.order.vo.order.OrderVo;
import com.pinshang.qingyun.order.vo.order.PickSubOrderVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @Author: sk
 * @Date: 2025/4/7
 *
 * B 端发送结算消息，子单要全部实发完之后再发送结算消息
 * 包含直送审核通过、通达实发回写、普通B端实发回写
 */
@Slf4j
@Service
public class SettleKafkaService {

    @Autowired
    private SubOrderMapper subOrderMapper;
    @Lazy
    @Autowired
    private CommonService commonService;
    @Lazy
    @Autowired
    private SubOrderQuantityService subOrderQuantityService;

    /**
     * 直送审核通过
     */
    public void zsOrdersendSettleKafkaMsg(Long orderId){
        List<OrderVo> orderVoList = subOrderMapper.queryOrderListDetailByOrderId(orderId);
        if (SpringUtil.isEmpty(orderVoList)) {
            return;
        }

        String sendMqTime = DateUtil.getDateFormate(new Date(), DateUtil.DEFAULT_DATE_FORMAT);
        Date sendTime = new Date();
        // 计算totalAmount和deliveryTotalAmount 单价 * 数量
        for (OrderVo orderVo : orderVoList) {
            orderVo.setStockOutOrderCode(orderVo.getSourceCode());
            BigDecimal totalAmount = BigDecimal.ZERO;
            BigDecimal deliveryTotalAmount = BigDecimal.ZERO;
            List<ItemVo> items = orderVo.getItems();
            if (SpringUtil.isEmpty(items)) {
                continue;
            }
            for (ItemVo itemVo : items) {
                BigDecimal totalPrice = itemVo.getUnitPrice().multiply(itemVo.getNumber()).setScale(2, BigDecimal.ROUND_HALF_UP);
                totalAmount = totalAmount.add(totalPrice);
                BigDecimal deliveryTotalPrice = itemVo.getDeliveryUnitPrice().multiply(itemVo.getDeliveryNumber()).setScale(2, BigDecimal.ROUND_HALF_UP);
                deliveryTotalAmount = deliveryTotalAmount.add(deliveryTotalPrice);
            }

            orderVo.setTotalAmount(totalAmount);
            orderVo.setDeliveryTotalAmount(deliveryTotalAmount);
            orderVo.setDeliveryTime(sendTime);
            orderVo.setSendMqTime(sendMqTime);
            orderVo.setSourceType(SettleOrderSourceTypeEnum.T_ORDER.name());

            commonService.sendKafkaMessage(orderVoList, MessageType.SETTLE_ORDER_SYNC, KafkaConstant.SETTLE_ORDER_ALL_TOPIC, MessageOperationType.INSERT.getCode());
        }
    }

    /**
     * 通达实发回写(子单全部实发再发送消息)
     * @param subOrderIds
     * @param dto
     */
    public void sendTdaSettleKafkaMsg(List<Long> subOrderIds, LogisticsDeliveryMessage dto, List<Long> excludeOrderTypeList){
        if (CollectionUtils.isEmpty(subOrderIds)) {
            return;
        }

        List<PickSubOrderVo> paramSubOrderList = new ArrayList<>();
        subOrderIds.forEach(subOrderId -> {
            PickSubOrderVo pickSubOrderVo = new PickSubOrderVo();
            pickSubOrderVo.setSubOrderId(subOrderId);
            pickSubOrderVo.setStockOutOrderCode(dto.getWaybillCode());
            pickSubOrderVo.setStockOutTime(dto.getDeliveryDate());
            pickSubOrderVo.setOptionType(MessageOperationType.UPDATE.getCode());
            paramSubOrderList.add(pickSubOrderVo);
        });

        sendSettleKafkaMsg(paramSubOrderList, excludeOrderTypeList);
    }

    /**
     * 普通B端实发回写(子单全部实发再发送消息)
     * @param paramSubOrderList
     */
    public void sendSettleKafkaMsg(List<PickSubOrderVo> paramSubOrderList, List<Long> excludeOrderTypeList) {
        if (CollectionUtils.isEmpty(paramSubOrderList)) {
            return;
        }

        // 鲜食店(配送、直通类型), 注意：像totalAmount和deliveryTotalAmount除了大仓发货这一种情况两个值不一样，其它都完全一样
        String sendMqTime = DateUtil.getDateFormate(new Date(), DateUtil.DEFAULT_DATE_FORMAT);
        for (PickSubOrderVo subOrderVo : paramSubOrderList) {
            Long subOrderId = subOrderVo.getSubOrderId();
            SubOrder subOrder = subOrderMapper.selectByPrimaryKey(subOrderId);
            if (subOrder == null) {
                continue;
            }

            // 如果全部出库
            if (subOrderQuantityService.isAllOut(subOrder.getOrderId())) {
                String stockOutOrderCode = subOrderVo.getStockOutOrderCode();
                List<OrderVo> orderVoList = subOrderMapper.listOrderDetailByOrderId(subOrder.getOrderId(), excludeOrderTypeList);
                if (SpringUtil.isEmpty(orderVoList)) {
                    continue;
                }

                // 计算totalAmount和deliveryTotalAmount 单价 * 数量
                for (OrderVo orderVo : orderVoList) {
                    orderVo.setStockOutOrderCode(stockOutOrderCode);
                    BigDecimal totalAmount = BigDecimal.ZERO;
                    BigDecimal deliveryTotalAmount = BigDecimal.ZERO;
                    List<ItemVo> items = orderVo.getItems();
                    if (SpringUtil.isEmpty(items)) {
                        continue;
                    }
                    for (ItemVo itemVo : items) {
                        BigDecimal totalPrice = itemVo.getUnitPrice().multiply(itemVo.getNumber()).setScale(2, BigDecimal.ROUND_HALF_UP);
                        totalAmount = totalAmount.add(totalPrice);
                        BigDecimal deliveryTotalPrice = itemVo.getDeliveryUnitPrice().multiply(itemVo.getDeliveryNumber()).setScale(2, BigDecimal.ROUND_HALF_UP);
                        deliveryTotalAmount = deliveryTotalAmount.add(deliveryTotalPrice);
                    }

                    orderVo.setTotalAmount(totalAmount);
                    orderVo.setDeliveryTotalAmount(deliveryTotalAmount);
                    orderVo.setDeliveryTime(subOrderVo.getStockOutTime());
                    orderVo.setSendMqTime(sendMqTime);
                    orderVo.setSourceType(SettleOrderSourceTypeEnum.T_ORDER.name());

                    commonService.sendKafkaMessage(orderVoList, MessageType.SETTLE_ORDER_SYNC, KafkaConstant.SETTLE_ORDER_ALL_TOPIC, subOrderVo.getOptionType());
                }

            }
        }
    }

}
