package com.pinshang.qingyun.order.controller.cup;

import com.pinshang.qingyun.common.dto.DictionaryODTO;
import com.pinshang.qingyun.order.dto.cup.LineGroupODTO;
import com.pinshang.qingyun.order.dto.cup.NoOrderSearchPageIDTO;
import com.pinshang.qingyun.order.dto.cup.NoOrderStoreResultODTO;
import com.pinshang.qingyun.order.service.DictionaryService;
import com.pinshang.qingyun.order.service.cup.OrderCupReportService;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * @Author: sk
 * @Date: 2024/7/16
 */
@RestController
@RequestMapping("/orderCup")
@Slf4j
public class OrderCupReportController {

    @Autowired
    private OrderCupReportService orderCupReportService;
    @Autowired
    private DictionaryService dictionaryService;

    /**
     * 未订货查询
     * @param idto
     * @return
     */
    @ApiOperation(value = "未订货查询", notes = "未订货查询")
    @PostMapping("/noRationCustomerList")
    public NoOrderStoreResultODTO findOrderListByPage(@RequestBody NoOrderSearchPageIDTO idto){
        return orderCupReportService.findNoOrderStorePage(idto);
    }

    /**
     * 获取发货时间
     * @return
     */
    @ApiOperation(value = "获取发货时间", notes = "获取发货时间")
    @RequestMapping(value = "/getDeliveryTime",method = RequestMethod.GET)
    public List<String> getDeliveryTime(){
        return orderCupReportService.getDeliveryTime();
    }

    /**
     * 获取线路组
     * @return
     */
    @ApiOperation(value = "获取线路组", notes = "获取线路组")
    @RequestMapping(value = "/findLineGroups",method = RequestMethod.GET)
    public List<LineGroupODTO> findLineGroups(){
        return dictionaryService.findLineGroups();
    }

    /*
    *//**
     * 获取客户类型
     * @return
     *//*
    @ApiOperation(value = "获取客户类型", notes = "获取客户类型")
    @RequestMapping(value = "/findStoreTypes",method = RequestMethod.GET)
    public List<DictionaryODTO> findStoreTypes(){
        return dictionaryService.findStoreTypes();
    }*/
}
