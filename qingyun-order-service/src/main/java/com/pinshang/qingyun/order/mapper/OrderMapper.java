package com.pinshang.qingyun.order.mapper;

import com.pinshang.qingyun.base.mybatis.MyMapper;
import com.pinshang.qingyun.order.bo.ExportLastMonthLineGroupBO;
import com.pinshang.qingyun.order.bo.QueryOrderBo;
import com.pinshang.qingyun.order.bo.pda.PdaCommodityOrderQueryBo;
import com.pinshang.qingyun.order.dto.*;
import com.pinshang.qingyun.order.dto.pf.PfOrder4AppODTO;
import com.pinshang.qingyun.order.dto.pf.PfQueryOrder4AppParam;
import com.pinshang.qingyun.order.dto.sync.SyncOrderIDTO;
import com.pinshang.qingyun.order.dto.sync.SyncOrderODTO;
import com.pinshang.qingyun.order.dto.tob.ToBOrderDataODTO;
import com.pinshang.qingyun.order.dto.xda.CreOrderItemDTO;
import com.pinshang.qingyun.order.dto.xda.MtCouponDayStatisticsODTO;
import com.pinshang.qingyun.order.dto.xda.XdaOrder4AppODTO;
import com.pinshang.qingyun.order.dto.xda.XdaQueryOrder4AppParam;
import com.pinshang.qingyun.order.dto.xda.v2.XdaOrderAppV2ODTO;
import com.pinshang.qingyun.order.dto.xda.v2.XdaQueryOrderAppParamV2;
import com.pinshang.qingyun.order.dto.xda.v3.XdaOrderAppV3ODTO;
import com.pinshang.qingyun.order.dto.xda.v3.XdaQueryOrderAppParamV3;
import com.pinshang.qingyun.order.dto.xda.v4.XdaOrderAppV4ODTO;
import com.pinshang.qingyun.order.dto.xda.v4.XdaQueryOrderAppParamV4;
import com.pinshang.qingyun.order.manage.delivery.dto.OrderDeliveryFinishDTO;
import com.pinshang.qingyun.order.mapper.entry.commodity.CommodityResultEntry;
import com.pinshang.qingyun.order.mapper.entry.deliveryBill.DeliveryBillEntry;
import com.pinshang.qingyun.order.mapper.entry.deliveryBill.DeliveryOrderItemStkPrintEntry;
import com.pinshang.qingyun.order.mapper.entry.deliveryBill.DeliveryOrderStkPrintEntry;
import com.pinshang.qingyun.order.mapper.entry.job.StoreOrderAmountEntry;
import com.pinshang.qingyun.order.mapper.entry.order.*;
import com.pinshang.qingyun.order.mapper.entry.order.V2.OrderItemV2Entry;
import com.pinshang.qingyun.order.mapper.entry.pda.PdaOrderQueryEntry;
import com.pinshang.qingyun.order.mapper.entry.shop.SettlementDetailsReportEntry;
import com.pinshang.qingyun.order.mapper.entry.store.EmployeeEntry;
import com.pinshang.qingyun.order.mapper.entry.store.OutstandingShopEntry;
import com.pinshang.qingyun.order.mapper.entry.store.StoreDurationEntry;
import com.pinshang.qingyun.order.mapper.entry.store.StoreEntry;
import com.pinshang.qingyun.order.model.commodity.Commodity;
import com.pinshang.qingyun.order.model.gift.GiftModel;
import com.pinshang.qingyun.order.model.gift.GiftModelCondition;
import com.pinshang.qingyun.order.model.gift.GiftProduct;
import com.pinshang.qingyun.order.model.order.Order;
import com.pinshang.qingyun.order.vo.deliveryBill.DeliveryBillSearchVo;
import com.pinshang.qingyun.order.vo.order.*;
import com.pinshang.qingyun.order.vo.pda.PdaCommodityOrderQueryRequestVo;
import com.pinshang.qingyun.order.vo.shop.SettlementDetailsReportVo;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;

@Repository
public interface OrderMapper extends MyMapper<Order>{

	List<FrozenProductEntry> findFrozenProductListByProductIds(@Param("productIds") List<String> productIds);
	List<FrozenProductEntry> findXdFrozenProductListByProductIds(@Param("productIds") List<String> productIds);

	StoreDurationEntry findStoreDurationByStoreId(@Param("storeId")String storeId);
	
	List<GiftModel> findGiftModelByStoreId(Map<String,Object> paramMap);

	List<GiftProduct> findGiftProductByGiftModelConditionId(@Param("giftModelConditionId") Long giftModelConditionId);

	BigDecimal findOrderProductNumber(Map<String,Object> paramMap);

	List<OrderListEntry> findOrderListByPage(OrderListRequestVo orderListRequestVo);
	
	List<OrderListEntry> findPreOrderListByPage(OrderListRequestVo orderListRequestVo);
	
	List<OrderItemEntry> findOrderItemsByOrderId(@Param("orderId")Long orderId);
	
	List<OrderItemEntry> findPreOrderItemsByOrderId(@Param("orderId")Long orderId);
	
	Integer updateOrderStatusByParameter(Map<String,Object> paramMap);

	List<CommodityResultEntry> selectOrderInfoByOrderId(@Param("orderId")Long orderId);

	List<CommodityResultEntry> findOrderProductByOrderId(@Param("orderId")Long orderId);
	List<CommodityResultEntry> findPreOrderProductByOrderId(@Param("orderId")Long orderId);

	Integer updateReceiveOrderBySubOrderId(Map<String,Object> paramMap);
	
	StoreEntry findStoreById(@Param("storeId")String storeId);
	
	EmployeeEntry findEmployeeById(@Param("employeeId")Long employeeId);
	

	/**
	 * 查找没有订单的店铺信息
	 * @param employeeId 查询者权限
	 * @param orderTime 没有订单的日期 yyyy-MM-dd
	 * @return
	 */
	List<OutstandingShopEntry> findOutstandingShopList(@Param("employeeId") Long employeeId,
													   @Param("orderTime") String orderTime);


	/**
	 * 查询修改送货日期列表
	 * @param vo
	 * @return
	 */
    List<ModifyDeliverDateEntry> queryModifyDeliverDateList(ModifyDeliverDateVo vo);

	int modifyDeliverDate(@Param("orderCode") String orderCode, @Param("date") String date);

	List<OrderReplenishmentListEntry> orderReplenishmentList(OrderReplenishmentListVo orderReplenishmentListVo);

	Order findOrderInfo(@Param("orderCode") String orderCode);

    List<SettleOrderVo> querySettleOrder(@Param("orderCode") String orderCode);


	/**
	 * 结算明细报表
	 * @param vo
	 * @return
	 */
	List<SettlementDetailsReportEntry> settlementDetailsReport(SettlementDetailsReportVo vo);

	/**
	 * 查询结算总金额
	 * @param vo
	 * @return
	 */
	BigDecimal findTotalSettlePrice(SettlementDetailsReportVo vo);

	/**
	 * 查询可变价订单列表
	 * @param storeId
	 * @param orderTime
	 * @return
	 */
	List<Order> queryChangePriceOrderList(@Param("storeId") Long storeId,@Param("orderTime") String orderTime);

	XdProposedOrderSettingEntry getXdProposedOrderSettingEntry();

	/**
	 * 查询年货订单信息
	 * @param beginTime
	 * @param endTime
	 * @return
	 */
	List<OrderYearEntry> queryOrderYearEntryList(@Param("beginTime") String beginTime,@Param("endTime") String endTime);

	/**
	 * 查询可用的赠品策略
	 * @param giftModelId
	 * @param applyConditionValue
	 * @return
	 */
	GiftModelCondition queryUseGiftModelCondition(@Param("giftModelId") Long giftModelId, @Param("applyConditionValue") BigDecimal applyConditionValue);

	List<OrderItemEntry> queryOrderItem4Copy(@Param("orderId")Long orderId);

	List<OrderItemV2Entry> queryOrderItemCopyV2(@Param("orderId")Long orderId);

	List<XdaOrder4AppODTO> queryOrder4App(@Param("param") XdaQueryOrder4AppParam param);

	List<XdaOrderAppV2ODTO>  queryOrderXdaApp(@Param("param") XdaQueryOrderAppParamV2 param);

	List<XdaOrderAppV3ODTO>  queryOrderXdaAppV3(@Param("param") XdaQueryOrderAppParamV3 param);

	List<XdaOrderAppV4ODTO>  queryOrderXdaAppV4(@Param("param") XdaQueryOrderAppParamV4 param);


	List<XdaOrderAppV2ODTO> queryOrderDetailByCodeV2(@Param("param") XdaQueryOrder4AppParam param);

	List<XdaOrderAppV3ODTO> queryOrderDetailByCodeV3(@Param("param") XdaQueryOrder4AppParam param);

	List<XdaOrderAppV4ODTO> queryOrderDetailByCodeV4(@Param("param") XdaQueryOrder4AppParam param);

	List<PfOrder4AppODTO> queryOrder4PfApp(@Param("param") PfQueryOrder4AppParam param);

	List<Order> queryOrderByOrderTime(@Param("storeId") Long storeId,@Param("orderTime") Date orderTime);

	List<OrderChangePriceVo> findOrderBySubOrderId(@Param("subOrderIds")List<Long> subOrderIds);

	List<XdReceiveDocCommodityODTO> queryPrintOrderInfo(@Param("storeId") Long storeId, @Param("orderTime")String orderTime);

	List<String> queryPrintOrderCodeList(@Param("storeId") Long storeId, @Param("orderTime")String orderTime);

	/**
	 * 查询上月线路组汇总报表
	 *
	 * @param startOrderTime 开始时间
	 * @param endOrderTime	 结束时间
	 * @param month 		 月份
	 * @return				 list
	 */
	List<ExportLastMonthLineGroupBO> getExportLastMonthLineGroupList(@Param("startOrderTime")String startOrderTime,@Param("endOrderTime")String endOrderTime,@Param("month")String month);

	/**
	 * 获取需要补偿订单数量的subOrderId
	 *
	 * @param vo 实体
	 * @return list
	 */
	List<Long> getSubOrderId(CompensateOrderQuantityReqVO vo);

	BigDecimal getCommodityDeliveryQuantity(@Param("shopId")Long shopId, @Param("commodityId")Long commodityId, @Param("orderTime")String orderTime, @Param("stallId") Long stallId);
	List<SaleReturnItemEntry> getCommodityDeliveryQuantityList(@Param("shopId")Long shopId, @Param("commodityIdList")List<String> commodityIdList, @Param("orderTime")String orderTime, @Param("stallId") Long stallId);

	/**
	 * 送货单列表查询
	 * @param vo
	 * @return
	 */
	List<DeliveryBillEntry> findDeliveryBillPageInfoByParams(DeliveryBillSearchVo vo);

	DeliveryOrderStkPrintEntry selectPrintViewById(Long id);

	List<DeliveryOrderItemStkPrintEntry> selectItemPrintViewByOrderId(Long orderId);

	int deleteShopOrderedQuantity(@Param("orderTime") String orderTime, @Param("shopId") Long shopId);
	int insertShopOrderedQuantity(@Param("orderTime") String orderTime, @Param("shopId") Long shopId, @Param("orderTypeList") List<Integer> orderTypeList);

    List<ShopOrderedQuantityODTO> selectShopOrderQuantity(@Param("shopId") Long shopId, @Param("commodityIdList") List<Long> commodityIdList, @Param("orderTime") String orderTime);

	List<ShopCountStockPageODTO> selectShopCommodityVarietyTotal(ShopCountStockPageIDTO idto);

	List<ShopCountStockDetailItemODTO> selectShopCommodityVarietyDetail(ShopCountStockDetailIDTO idto);

    List<ShopOrderedQuantityODTO> queryShopOrderedQuantity(@Param("commodityIdList") List<Long> commodityIdList, @Param("orderTime") String orderTime);

	int deleteCommodityFreshOrders(@Param("orderTime") String orderTime);
	int batchInsertCommodityFreshOrders(@Param("orderTime") String orderTime, @Param("commodityIdList") List<Long> commodityIdList);
	List<CommodityFreshOrderODTO> queryCommodityFreshList(@Param("orderTime") String orderTime);
	List<CommodityFreshOrderODTO> queryOrderedFreshList(@Param("orderTime") String orderTime, @Param("commodityIdList") List<Long> commodityIdList);
    int updateCommodityFreshOrdered(@Param("orderTime") String orderTime, @Param("shopId") Long shopId, @Param("commodityIdList") List<Long> commodityIdList);

    List<ShopOrderedQuantityODTO> queryOrderDetailByOrderCode(@Param("orderCode") String orderCode, @Param("orderTypeList") List<Integer> orderTypeList);
	List<ShopOrderedQuantityODTO> queryShopOrderedQuantityList(@Param("orderTime") String orderTime, @Param("shopId") Long shopId, @Param("commodityIdList") List<Long> commodityIdList);
    int batchInsertShopOrderQuantity(@Param("insertList") List<ShopOrderedQuantityODTO> insertList);
	int batchUpdateShopOrderQuantity(@Param("updateList") List<ShopOrderedQuantityODTO> updateList);

	List<OrderSyncToDcRespVo> queryOrderSyncToDcList(OrderSyncToDcReqVo vo);

	List<OrderSyncToDcRespVo> queryOrderSyncToDcListV2(OrderSyncToDcReqVo vo);


	/**
	 * 根据订单状态查询数量和份数
	 * @param queryOrderBo
	 * @return
	 */
	List<ToBOrderDataODTO> selectCountByOrderStatus(QueryOrderBo queryOrderBo);

	// 查询鲜达特价商品下单总量
	List<CreOrderItemDTO> queryXdaSpecialPriceOrderedQuantity(@Param("orderTime") String orderTime, @Param("storeId") Long storeId, @Param("commodityIdList") List<Long> commodityIdList);


	/**
	 * 基于指定日期 手动 刷新 订单结算日期
	 * @param orderTime
	 * @return
	 */
	Integer updateSettleOrderDateByDate(@Param("orderTime")String orderTime);
	Integer updateStoreTypeIdByDate(@Param("orderTime")String orderTime);

    Integer countCurrentDayOrder(@Param("storeId") Long storeId, @Param("beginTime") String beginTime, @Param("endTime") String endTime);
	List<SubOrderInfoListEntry> selectOrderByOrderIdList(@Param("orderIdList") Set<Long> orderIdList);
    List<SubOrderInfoListEntry> selectOrderIds(@Param("startTime") Date startTime,@Param("endTime") Date endTime);

	List<SyncOrderODTO> syncOrderList(SyncOrderIDTO syncOrderIDTO);

	Integer countOrderNumberByStoreId(@Param("storeId")Long storeId, @Param("orderTime") String orderTime);

	List<Commodity> findCommodityByOrderId(@Param("orderId")Long orderId,@Param("orderTime")String orderTime,@Param("productType") Integer productType);

	List<Order> findStoreSearch(Map<String, Object> params);

	List<PdaOrderQueryEntry> findPdaOrder(PdaCommodityOrderQueryRequestVo vo);

	List<StoreOrderAmountEntry> queryOrder(@Param("dateStr")String dateStr,
										   @Param("orderStatus")Integer orderStatus,
										   @Param("collectStatus")Integer collectStatus);

	List<OrderDeliveryFinishDTO> findOrderDeliveryFinishList(@Param("orderIds") List<Long> orderIdList);

	List<MtCouponDayStatisticsODTO> queryOrderCouponList(@Param("beginTime") String beginTime, @Param("endTime") String endTime);


	List<StoreOftenBuyProductDTO> queryStoreOftenBuyProductDTO(@Param("storeId") Long storeId,
															   @Param("beginDate")String beginDate,
															   @Param("endDate")String endDate);
    BigDecimal queryOrderPrice(@Param("storeId") Long storeId, @Param("commodityId") Long commodityId, @Param("orderTime") String orderTime);

	OrderInfoODTO queryOrderByOrderCode(@Param("orderCode") String orderCode);
}

