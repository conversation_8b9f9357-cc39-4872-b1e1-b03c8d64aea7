package com.pinshang.qingyun.order.mapper.entry.purchase;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@NoArgsConstructor
public class ShoppingCartKafkaEntry {

    private Long shopId;

    private List<Long> commodityIdList;

    public ShoppingCartKafkaEntry(Long shopId,List<Long> commodityIdList){
        this.shopId = shopId;
        this.commodityIdList = commodityIdList;
    }
}
