package com.pinshang.qingyun.order.mapper.entry.orderStatistics;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.pinshang.qingyun.order.model.statistics.OrderCompanyCommodityStatistics;
import lombok.Data;

import java.math.BigDecimal;

/**
 * @ClassName OrderCompanyCommodityStatisticsEntry
 * @Description TODO
 * <AUTHOR>
 * @Date 2021/3/26 14:54
 **/
@Data
public class OrderCompanyCommodityStatisticsEntry {
    @ExcelIgnore
    private String companyName;
    @ExcelProperty(value = "工厂")
    private String factoryName;
    @ExcelProperty(value = "商品编码")
    private String commodityCode;
    @ExcelProperty(value = "商品名称")
    private String commodityName;
    @ExcelProperty(value = "计量单位")
    private String unitName;
    @ExcelIgnore
    private String showOrderTime;
    @ExcelProperty(value = "订货数量")
    private BigDecimal orderQuantity;
    @ExcelProperty(value = "订货金额")
    private BigDecimal orderTotalAmount;
    private BigDecimal realQuantity;
    private BigDecimal realTotalAmount;

}
