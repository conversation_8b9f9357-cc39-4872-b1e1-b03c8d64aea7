package com.pinshang.qingyun.order.service.giftLimit;/**
 * @Author: sk
 * @Date: 2025/4/17
 */

import com.pinshang.qingyun.base.configure.expand.QYAssert;
import com.pinshang.qingyun.box.utils.BeanCloneUtils;
import com.pinshang.qingyun.order.dto.giftLimit.GiftLeftQuantityODTO;
import com.pinshang.qingyun.order.dto.giftLimit.GiftLimitQuantityQueryIDTO;
import com.pinshang.qingyun.order.dto.giftLimit.GiftLimitQuantitySaveIDTO;
import com.pinshang.qingyun.order.mapper.giftLimit.GiftLimitQuantityMapper;
import com.pinshang.qingyun.order.model.giftLimit.GiftLimitQuantity;
import com.pinshang.qingyun.order.model.order.OrderListGift;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.entity.Example;

import java.util.*;

/**
 * <AUTHOR>
 * @date 2025年04月17日 上午10:37
 */
@Slf4j
@Service
public class GiftLimitService {

    @Autowired
    private GiftLimitQuantityMapper giftLimitQuantityMapper;

    /**
     * 查询赠品总限量剩余数量
     * promotionId 必传
     * commodityIdList 必传
     * orderId 修改必传
     * @return
     */
    public List<GiftLeftQuantityODTO> queryGiftLeftQuantityList(GiftLimitQuantityQueryIDTO queryIdto){

        Set<Long> promotionIdList = new HashSet<>();
        Set<Long> comodityIdList = new HashSet<>();
        queryIdto.getDetailList().forEach(detail -> {
            QYAssert.isTrue(detail.getGiftPrommotionId() != null, "赠品id不能为空!");
            QYAssert.isTrue(CollectionUtils.isNotEmpty(detail.getCommodityIdList()), "赠品商品信息不能为空!");

            promotionIdList.add(detail.getGiftPrommotionId());
            comodityIdList.addAll(detail.getCommodityIdList());
        });

        List<GiftLeftQuantityODTO> leftQuantityList = giftLimitQuantityMapper.queryGiftUsedQuantityList(new ArrayList<>(promotionIdList), new ArrayList<>(comodityIdList), queryIdto.getOrderId());

        return leftQuantityList;
    }


    /**
     * 新增修改订单维护已赠数量
     * @param saveList
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public Boolean saveGiftLimitQuantity(List<GiftLimitQuantitySaveIDTO> saveList ){
        QYAssert.isTrue(CollectionUtils.isNotEmpty(saveList), "订单维护已赠数量不能为空!");

        List<GiftLimitQuantity> giftLimitQuantityList = new ArrayList<>();
        Set<Long> orderIdList = new HashSet<>();
        saveList.forEach(item -> {
            QYAssert.isTrue(item.getGiftPrommotionId() != null, "赠品id不能为空!");
            QYAssert.isTrue(item.getCommodityId() != null, "赠品商品id不能为空!");
            QYAssert.isTrue(item.getTotalQuantity() != null, "赠品数量不能为空!");
            QYAssert.isTrue(item.getOrderId() != null, "订单id不能为空!");

            GiftLimitQuantity giftLimitQuantity = BeanCloneUtils.copyTo(item, GiftLimitQuantity.class);
            giftLimitQuantity.setPromotionId(item.getGiftPrommotionId());
            giftLimitQuantity.setQuantity(item.getTotalQuantity());
            giftLimitQuantity.setCreateId(-1L);
            giftLimitQuantity.setCreateTime(new Date());
            giftLimitQuantityList.add(giftLimitQuantity);

            orderIdList.add(item.getOrderId());
        });

        // 修改订单先删除以前的记录，再重新新增
        Example delExp = new Example(GiftLimitQuantity.class);
        delExp.createCriteria().andIn("orderId", orderIdList);
        Integer count = giftLimitQuantityMapper.selectCountByExample(delExp);
        if(count > 0 ) {
            giftLimitQuantityMapper.deleteByExample(delExp);
        }

        giftLimitQuantityMapper.insertList(giftLimitQuantityList);
        return true;
    }


    /**
     * 订单取消回退已经赠送数量
     * @param orderId
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public Boolean deleteGiftLimitQuantity(List<Long> orderIdList) {
        log.info("订单取消回退已经赠送数量, orderIdList: {}", orderIdList);

        Example delExp = new Example(GiftLimitQuantity.class);
        delExp.createCriteria().andIn("orderId", orderIdList);
        Integer count = giftLimitQuantityMapper.selectCountByExample(delExp);
        if(count > 0 ) {
            giftLimitQuantityMapper.deleteByExample(delExp);
        }
        return true;
    }

}
