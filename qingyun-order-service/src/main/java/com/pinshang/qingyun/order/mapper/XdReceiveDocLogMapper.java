package com.pinshang.qingyun.order.mapper;

import com.pinshang.qingyun.base.mybatis.MyMapper;
import com.pinshang.qingyun.order.model.xd.XdReceiveDocLog;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface XdReceiveDocLogMapper extends MyMapper<XdReceiveDocLog> {


    List<XdReceiveDocLog> getXdReceiveDocLogList(@Param("docId") Long docId, @Param("barcode") String barcode);
}
