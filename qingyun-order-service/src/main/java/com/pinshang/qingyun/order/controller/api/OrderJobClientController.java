package com.pinshang.qingyun.order.controller.api;

import com.pinshang.qingyun.order.dto.sync.SyncOrderIDTO;
import com.pinshang.qingyun.order.dto.sync.SyncOrderODTO;
import com.pinshang.qingyun.order.service.OrderService;
import com.pinshang.qingyun.order.service.job.AmountFlowCompService;
import com.pinshang.qingyun.order.service.xda.XdaCancelOrderAmountCompareService;
import com.pinshang.qingyun.order.vo.order.CompensateOrderQuantityReqVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/07/01 17:38
 */
@RestController
@RequestMapping("/orderJob")
public class OrderJobClientController {

    @Autowired
    private OrderService orderService;

    @Autowired
    private AmountFlowCompService amountFlowCompService;
    @Autowired
    private XdaCancelOrderAmountCompareService xdaCancelOrderAmountCompareService;

    @PostMapping("/compensateOrderQuantity")
    public List<Long> compensateOrderQuantity(@RequestBody CompensateOrderQuantityReqVO vo) {
        return orderService.getSubOrderId(vo);
    }

    @RequestMapping(value = "/syncOrderList", method = RequestMethod.POST)
    public List<SyncOrderODTO> syncOrderList(@RequestBody SyncOrderIDTO syncOrderIDTO) {
        return orderService.syncOrderList(syncOrderIDTO);
    }

    //    @RequestMapping(value = "/syncOrderItemList", method = RequestMethod.POST)
//    public List<SyncOrderListODTO> syncOrderItemList(@RequestBody SyncOrderIDTO syncOrderIDTO){
//        return orderService.syncOrderItemList(syncOrderIDTO);
//    }
    @PostMapping("/amountFlowComp")
    public Boolean amountFlowComp() {
        return amountFlowCompService.doAmountFlowComp();
    }


    /**
     * 鲜达取消订单金额对比
     * @param timeStamp
     * @return
     */
    @PostMapping("/cancelOrderAmountCompare")
    public Boolean amountFlowComp(@RequestParam("timeStamp") String timeStamp) {
        return xdaCancelOrderAmountCompareService.compareCancelOrderAmount(timeStamp);
    }
}
