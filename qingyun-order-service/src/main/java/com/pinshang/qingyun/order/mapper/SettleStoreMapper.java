package com.pinshang.qingyun.order.mapper;

import com.pinshang.qingyun.order.mapper.entry.SettlementStoreEntry;
import com.pinshang.qingyun.order.mapper.entry.StoreEntry;
import com.pinshang.qingyun.order.vo.SettlementStoreVo;
import com.pinshang.qingyun.order.vo.StoreVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Mapper
@Repository
public interface SettleStoreMapper {

    /**
     * 条件查询客户信息
     * @param vo
     * @return
     */
    List<StoreEntry> selectStore(StoreVo vo);

    /**
     * 条件查询结账客户
     * @param vo
     * @return
     */
    List<SettlementStoreEntry> selectSettleStore(SettlementStoreVo vo);

    /**
     * 批量添加客户
     * @param storeCode
     * @return
     */
    StoreEntry selectStoreByStores(@Param("storeCode") String storeCode);
}
