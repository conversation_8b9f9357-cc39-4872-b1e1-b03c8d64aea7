package com.pinshang.qingyun.order.service;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.pinshang.qingyun.base.configure.expand.QYAssert;
import com.pinshang.qingyun.base.enums.shop.ShopTypeEnums;
import com.pinshang.qingyun.box.utils.BeanCloneUtils;
import com.pinshang.qingyun.box.utils.SpringUtil;
import com.pinshang.qingyun.common.dto.DictionaryODTO;
import com.pinshang.qingyun.common.service.DictionaryClient;
import com.pinshang.qingyun.order.mapper.CommodityPurchaseStatusMapper;
import com.pinshang.qingyun.order.mapper.entry.purchase.ShopCommodityAppStatusEntry;
import com.pinshang.qingyun.order.mapper.entry.purchase.ShopCommodityPurchaseStatusEntry;
import com.pinshang.qingyun.order.mapper.entry.purchase.ShoppingCartKafkaEntry;
import com.pinshang.qingyun.order.mapper.entry.shop.*;
import com.pinshang.qingyun.order.model.commodity.CommodityPurchaseStatus;
import com.pinshang.qingyun.order.vo.commodity.CommodityPurchaseStatusVo;
import com.pinshang.qingyun.order.vo.purchase.ShopCommodityModifyPurchaseStatusVo;
import com.pinshang.qingyun.order.vo.purchase.ShopCommodityPurchaseStatusVo;
import com.pinshang.qingyun.order.vo.shop.PriceModelLogVo;
import com.pinshang.qingyun.order.vo.shop.ShopQtOrderProductVo;
import com.pinshang.qingyun.order.vo.shop.XsShopCommodityPurchaseStatusVo;
import com.pinshang.qingyun.product.dto.commodity.CommodityODTO;
import com.pinshang.qingyun.product.service.CommodityClient;
import com.pinshang.qingyun.shop.dto.ProductPriceModelODTO;
import com.pinshang.qingyun.shop.dto.ShopODTO;
import com.pinshang.qingyun.shop.dto.shopCommodity.XsShopCommodityAppStatusIDTO;
import com.pinshang.qingyun.shop.dto.shopCommodity.XsShopCommodityAppStatusODTO;
import com.pinshang.qingyun.shop.service.PriceModelLogClient;
import com.pinshang.qingyun.shop.service.ShopClient;
import com.pinshang.qingyun.shop.service.ShopCommodityUpDownManagerClient;
import com.pinshang.qingyun.xd.product.dto.UpdateXdShopAppStatusIDTO;
import com.pinshang.qingyun.xd.product.dto.xdShopCommodity.XdShopCommodityAppStatusIDTO;
import com.pinshang.qingyun.xd.product.dto.xdShopCommodity.XdShopCommodityAppStatusODTO;
import com.pinshang.qingyun.xd.product.service.XdShopCommodityClient;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.entity.Example;

import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class ShopCommodityPurchaseStatusService {
    @Autowired
    private CommodityPurchaseStatusMapper commodityPurchaseStatusMapper;

    @Autowired
    private PriceModelLogClient priceModelLogClient;

    @Autowired
    private DictionaryClient dictionaryClient;

    @Autowired
    private CommodityClient commodityClient;

    @Autowired
    private ShopClient shopClient;

    @Autowired
    private XdShopCommodityClient xdShopCommodityClient;

    @Autowired
    private ShopCommodityUpDownManagerClient shopCommodityUpDownManagerClient;

    @Autowired
    private CommonService commonService;
    /**
     * 修改门店商品是否可采
     * @param vos
     * @return
     */
    public int updateShopCommodityPurchaseStatus(List<CommodityPurchaseStatusVo> vos){
        return commodityPurchaseStatusMapper.updateByShopCommodity(vos);
    }

    /**
     * 保存门店商品是否可采状态
     * @param vos
     * @return
     */
    @Transactional
    public int saveShopCommodityPurchaseStatus(List<CommodityPurchaseStatusVo> vos){
        //查询之前存在的数据 进行修改
        List<String> collect = vos.stream().map(p -> p.getShopId() + "_" + p.getCommodityId()).collect(Collectors.toList());
        List<String> shopIdAndCommodityIdList = commodityPurchaseStatusMapper.selectShopCommodityPurchaseStatusListByShopIdAndCommodityId(collect);
        List<CommodityPurchaseStatus>  list = new ArrayList <>();
        //排除存在的数据 进行新增
        vos.stream().forEach(p->{
            if(!shopIdAndCommodityIdList.contains(p.getShopId() + "_" + p.getCommodityId())){
                CommodityPurchaseStatus commodityPurchaseStatus = new CommodityPurchaseStatus();
                commodityPurchaseStatus.setShopId(p.getShopId());
                commodityPurchaseStatus.setCommodityId(p.getCommodityId());
                commodityPurchaseStatus.setCommodityPurchaseStatus(p.getCommodityPurchaseStatus());
                commodityPurchaseStatus.setCreateId(p.getCreateId());
                commodityPurchaseStatus.setCreateTime(p.getCreateTime());
                commodityPurchaseStatus.setUpdateId(p.getUpdateId());
                commodityPurchaseStatus.setUpdateTime(p.getUpdateTime());
                commodityPurchaseStatus.setShopCommodity(p.getShopId() + "_" + p.getCommodityId());
                list.add(commodityPurchaseStatus);
            }
        });
        //修改存在的
        commodityPurchaseStatusMapper.updateCommodityPurchaseStatusByShopIdAndCommodityId(shopIdAndCommodityIdList);
        if(null != list && list.size() > 0 ){
            return commodityPurchaseStatusMapper.insertList(list);
        }
        return 0;

    }

    public List<ShopQtOrderProductEntry> selectShopQtOrderProduct(ShopQtOrderProductVo vo){
        return commodityPurchaseStatusMapper.selectShopQtOrderProduct(vo);
    }

    /**
     * 门店订货价格调整
     * @param vo
     * @return
     */
    public PageInfo<PriceModelLogEntry> selectProductPriceModel(PriceModelLogVo vo){
        ProductPriceModelODTO productPriceModel = priceModelLogClient.selectProductPriceModelByshop(vo.getShopId());
        if(!StringUtils.isBlank(vo.getBeginDate())){
            vo.setBeginDate(vo.getBeginDate() +" 00:00:00");
        }
        if(!StringUtils.isBlank(vo.getEndDate())){
            vo.setEndDate(vo.getEndDate() +" 23:59:59");
        }
        if(null!= productPriceModel.getProductPriceModelId()){
           vo.setProductPriceModelId(productPriceModel.getProductPriceModelId());
        }else{
            return null;
        }
       PageInfo<PriceModelLogEntry> pageDate  = PageHelper.startPage(vo.getPageNo(), vo.getPageSize()).doSelectPageInfo(() -> {
            commodityPurchaseStatusMapper.selectProductPriceModel(vo);
        });

        if (pageDate.getList().size() > 0) {
            List<Long> commodityIds = pageDate.getList().stream().map(PriceModelLogEntry::getCommodityId).collect(Collectors.toList());
            Map<Long,String> barCodeMap = commonService.getCommodityBarCodeMap(commodityIds, null);
            pageDate.getList().forEach(p->{
                if(null!= productPriceModel.getProductPriceModelId()){
                    p.setShopName(productPriceModel.getShopName());
                }
                if (barCodeMap.containsKey(p.getCommodityId())) {
                    p.setBarCodes(barCodeMap.get(p.getCommodityId()));
                }
            });
        }

//        pageDate.getList().forEach(p->{
//            if(null!= productPriceModel.getProductPriceModelId()){
//               p.setShopName(productPriceModel.getShopName());
//            }
//        });
        return pageDate;
    }


    public List<CommodityPriceEntry> selectProductPriceModelListByPriceModeClode(String priceModeClode){
        return commodityPurchaseStatusMapper.selectProductPriceModelListByPriceModeClode(priceModeClode);
    }

    public List<CommodityListEntry> selectCommodityByProductPriceModelId(Long productPriceModelId){
        return  commodityPurchaseStatusMapper.selectCommodityByProductPriceModelId(productPriceModelId);
    }

    public List<XsShopCommodityPurchaseStatusEntry> selectXsShopCommodityPurchaseStatus(XsShopCommodityPurchaseStatusVo vo){
        return commodityPurchaseStatusMapper.selectXsShopCommodityPurchaseStatus(vo);
    }

    public PageInfo<ShopCommodityPurchaseStatusEntry> selectShopCommodityPurchaseStatusList(ShopCommodityPurchaseStatusVo vo){
        QYAssert.isTrue(null != vo, "参数有误");
        if(null == vo.getBarCode() && null == vo.getCommodityId() && null == vo.getShopId()){
            QYAssert.isTrue(false, "搜索全部门店时，商品或条码必填!");
        }
        List<ShopCommodityAppStatusEntry> commodityAppStatus = new ArrayList<>();
        //查询门店商品 app上下架状态
        XsShopCommodityAppStatusIDTO xsShopCommodityAppStatusIDTO = new XsShopCommodityAppStatusIDTO(
                null != vo.getShopId()?vo.getShopId():null,
                null != vo.getAppStatus()?vo.getAppStatus():null,
                null != vo.getCommodityId()?vo.getCommodityId():null,
                null != vo.getBarCode()? vo.getBarCode():null);
        List<XsShopCommodityAppStatusODTO> xsObjects= shopCommodityUpDownManagerClient.selectXsShopCommodityAppStatus(xsShopCommodityAppStatusIDTO);
        commodityAppStatus = BeanCloneUtils.copyTo(xsObjects,ShopCommodityAppStatusEntry.class);

        //将门店+商品 带入查询条件
        if(SpringUtil.isNotEmpty(commodityAppStatus)){
            vo.setShopIdCommodityList(commodityAppStatus.stream().map(p->p.getShopId()+"_"+p.getCommodityId()).collect(Collectors.toList()));
        }else {
            return new PageInfo<ShopCommodityPurchaseStatusEntry>();
        }
        PageInfo<ShopCommodityPurchaseStatusEntry> pageDate  = PageHelper.startPage(vo.getPageNo(), vo.getPageSize()).doSelectPageInfo(() -> {
            commodityPurchaseStatusMapper.selectShopCommodityPurchaseStatusList(vo);
        });
        if(SpringUtil.isNotEmpty(pageDate.getList())){
            List<ShopCommodityAppStatusEntry> commodityAppNameList = new ArrayList<>();
            List<Long> commodityIdList = pageDate.getList().stream().map(ShopCommodityPurchaseStatusEntry::getCommodityId).distinct().collect(Collectors.toList());
            //如果不是鲜食数据 去查前台品名
            if(!vo.getShopType().equals(ShopTypeEnums.XS.getCode()) && !vo.getShopType().equals(ShopTypeEnums.XJ.getCode())){//鲜食数据
                XdShopCommodityAppStatusIDTO xdShopCommodityAppStatusIDTO = new XdShopCommodityAppStatusIDTO(commodityIdList);
                //调用鲜到接口查询 门店id+商品id+AppStatus
                List<XdShopCommodityAppStatusODTO> xdObjects= xdShopCommodityClient.selectXdShopCommodityAppStatus(xdShopCommodityAppStatusIDTO) ;
                commodityAppNameList = BeanCloneUtils.copyTo(xdObjects,ShopCommodityAppStatusEntry.class);
            }
            Map<String,ShopCommodityAppStatusEntry> shopCommodityMap = commodityAppStatus.stream().collect(Collectors.toMap(item -> item.getShopId()+"_"+item.getCommodityId(),p->p));

            Map<Long,ShopCommodityAppStatusEntry> commodityAppNameMap = commodityAppNameList.stream().collect(Collectors.toMap(item ->item.getCommodityId(),p->p));
            List<Long> commodityPackageIdList = pageDate.getList().stream().map(ShopCommodityPurchaseStatusEntry :: getCommodityPackageId).distinct().collect(Collectors.toList());
            List<Long> commodityUnitIdList = pageDate.getList().stream().map(ShopCommodityPurchaseStatusEntry :: getCommodityUnitId).distinct().collect(Collectors.toList());
            commodityPackageIdList.addAll(commodityUnitIdList);

            List<DictionaryODTO> dictionaryODTOList = dictionaryClient.findListByIds(commodityPackageIdList.stream().distinct().collect(Collectors.toList()));
            Map<String ,DictionaryODTO> map = dictionaryODTOList.stream().collect(Collectors.toMap(DictionaryODTO::getId, p->p));
            pageDate.getList().forEach(p->{
                if(map.containsKey(p.getCommodityUnitId().toString())){
                    p.setCommodityUnitName(map.get(p.getCommodityUnitId().toString()).getOptionName());
                }
                if(map.containsKey(p.getCommodityPackageId().toString())){
                    p.setCommodityPackageKind(map.get(p.getCommodityPackageId().toString()).getOptionName());
                }
                //app上下架状态
                if(shopCommodityMap.containsKey(p.getShopId()+"_"+p.getCommodityId())){
                    p.setAppStatus(shopCommodityMap.get(p.getShopId()+"_"+p.getCommodityId()).getAppStatus());
                }
                //前台品名赋值
                if(!vo.getShopType().equals(ShopTypeEnums.XS.getCode()) && !vo.getShopType().equals(ShopTypeEnums.XJ.getCode())){
                    if(commodityAppNameMap.containsKey(p.getCommodityId())){
                        p.setCommodityAppName(commodityAppNameMap.get(p.getCommodityId()).getCommodityAppName());
                    }
                }else {
                    if(shopCommodityMap.containsKey(p.getShopId()+"_"+p.getCommodityId())){
                        p.setCommodityAppName(shopCommodityMap.get(p.getShopId()+"_"+p.getCommodityId()).getCommodityAppName());
                    }
                }
            });
        }
        return pageDate;
    }

    @Transactional(rollbackFor = Exception.class)
    public Integer modifyShopCommodityPurchaseStatusList(ShopCommodityModifyPurchaseStatusVo vo) {
        QYAssert.isTrue(null != vo, "参数有误");
        QYAssert.isTrue(null != vo.getShopId(), "请选择门店");
        QYAssert.isTrue(null != vo.getCommodityCodes(), "请输入商品编码");
        QYAssert.isTrue(null != vo.getReason(), "请输入原因");
        List<Long> commodityIdList = selectCommodityByCommodityCode(vo.getCommodityCodes());
        List<Long> shopIdList = selectShopByShopTypeAndShopId(null,vo.getShopId());
        CommodityPurchaseStatus purchaseStatus = new CommodityPurchaseStatus(vo.getPurchaseStatus(),vo.getUserId(),new Date());
        Example example = new Example(CommodityPurchaseStatus.class);
        example.createCriteria().andIn("shopId",shopIdList).andIn("commodityId",commodityIdList);
        Integer integer = commodityPurchaseStatusMapper.updateByExampleSelective(purchaseStatus, example);
        if(integer>0){
            //xdShopCommodityClient.updateXdShopAppStatus(commodityIdList,shopIdList,vo.getPurchaseStatus());
            UpdateXdShopAppStatusIDTO updateXdShopAppStatusIDTO = new UpdateXdShopAppStatusIDTO();
            updateXdShopAppStatusIDTO.setCommodityIdList(commodityIdList);
            updateXdShopAppStatusIDTO.setShopIdList(shopIdList);
            updateXdShopAppStatusIDTO.setPurchaseStatus(vo.getPurchaseStatus());
            xdShopCommodityClient.updateXdShopAppStatus2(updateXdShopAppStatusIDTO);
        }
        return integer;
        //日志记录待确定
    }

    public List<Long> selectShopByShopTypeAndShopId(Integer shopType,List<Long> shopIdList) {
        List<Long> shopIds = new ArrayList<>();
        List<ShopODTO> shopList = shopClient.getShopListById(shopIdList);
        if(null == shopType){
            shopIds = shopList.stream().map(ShopODTO::getId).collect(Collectors.toList());
        }else if(null != shopType){
            shopIds = shopList.stream().filter(p-> p.getShopType().equals(shopType)).map(ShopODTO :: getId).collect(Collectors.toList());
        }
        return shopIds;
    }
    public List<Long> selectCommodityByCommodityCode(String commodityCodes){
        List<String> codesList = Arrays.asList(commodityCodes.split("\n"));
        QYAssert.isTrue(SpringUtil.isNotEmpty(codesList), "请以回车分割商品编码!");
        List<CommodityODTO> commodityList = commodityClient.findCommodityListByCommodityCodeList(codesList);
        QYAssert.isTrue(SpringUtil.isNotEmpty(commodityList), "商品编码不存在!");
        return commodityList.stream().map(CommodityODTO :: getId).collect(Collectors.toList());
    }

    public List<ShoppingCartKafkaEntry> selectShopCommodityByShopCommodityStatus(List<Long> xdShopId,List<Long> commodityIdList){
        List<ShoppingCartKafkaEntry> shoppingCartKafkaEntries = new ArrayList<>();
        Example example = new Example(CommodityPurchaseStatus.class);
        example.createCriteria().andEqualTo("commodityPurchaseStatus",1).andIn("shopId",xdShopId).andIn("commodityId",commodityIdList);
        List<CommodityPurchaseStatus> commodityPurchaseStatuses = commodityPurchaseStatusMapper.selectByExample(example);
        List<Long> shopIdList= commodityPurchaseStatuses.stream().map(CommodityPurchaseStatus::getShopId).distinct().collect(Collectors.toList());
        shopIdList.stream().forEach(p->{
            shoppingCartKafkaEntries.add(new ShoppingCartKafkaEntry(p,commodityPurchaseStatuses.stream().filter(s ->s.getShopId().equals(p)).map(CommodityPurchaseStatus::getCommodityId).collect(Collectors.toList())));
        });
        return shoppingCartKafkaEntries;

    }
}
