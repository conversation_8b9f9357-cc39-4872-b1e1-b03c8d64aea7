package com.pinshang.qingyun.order.listener;

import com.alibaba.fastjson.JSON;
import com.pinshang.qingyun.base.annotations.OnlineSwitchWatcher;
import com.pinshang.qingyun.base.api.QYApplicationContext;
import com.pinshang.qingyun.box.utils.SpringUtil;
import com.pinshang.qingyun.kafka.base.BaseKafkaOnlineSwitchProcessor;
import com.pinshang.qingyun.kafka.base.KafkaMessageWrapper;
import com.pinshang.qingyun.kafka.base.KafkaTopicConstant;
import com.pinshang.qingyun.order.model.order.SubOrder;
import com.pinshang.qingyun.order.service.SubOrderService;
import com.pinshang.qingyun.order.service.WeChatSendMessageService;
import com.pinshang.qingyun.order.vo.order.PickSubOrderVo;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.TimeUnit;

@Component
@Slf4j
@OnlineSwitchWatcher
public class SubOrderListener extends BaseKafkaOnlineSwitchProcessor {
    @Autowired
    private SubOrderService subOrderService;
    @Autowired
    private RedissonClient redissonClient;
    @Autowired
    private WeChatSendMessageService weChatSendMessageService;

    @KafkaListener(id="${application.name.switch}"+ KafkaTopicConstant.PICK_UPDATE_SUB_ORDER_QUANTITY_TOPIC ,
            topics = {"${application.name.switch}"+ KafkaTopicConstant.PICK_UPDATE_SUB_ORDER_QUANTITY_TOPIC})
    public void batchUpdateDeliveryQuantityV2(String message) {
        try {
            KafkaMessageWrapper messageWrapper = JSON.parseObject(message, KafkaMessageWrapper.class);
            List<PickSubOrderVo> subOrderList = JSON.parseArray(messageWrapper.getData().toString(), PickSubOrderVo.class);
            if(SpringUtil.isEmpty(subOrderList)){
                return;
            }

            for (PickSubOrderVo pickSubOrderVo : subOrderList) {
                SubOrder subOrder = subOrderService.querySubOrderBySubOrderId(pickSubOrderVo.getSubOrderId());
                if (subOrder == null) {
                    continue;
                }

                // 如果已经回写过实发数量和实收数量，则跳过
                if(subOrder.getWritebackRealQtyFlag() != null && subOrder.getWritebackRealQtyFlag()){
                    continue;
                }

                // 根据orderId 加锁60秒
                RLock lock = redissonClient.getLock("updateOrderDeliveryQuantity" + subOrder.getOrderId());
                lock.lock(60L, TimeUnit.SECONDS);
                try {

                    subOrderService.batchUpdateDeliveryQuantityV2(pickSubOrderVo, subOrder.getOrderId());
                } catch (Exception e){
                    log.error("拣货出库修改subOrder实发数量和实收数量消息消费异常, subOrderId {}", pickSubOrderVo.getSubOrderId() , e);
                    weChatSendMessageService.sendWeChatMessage("实发回写异常, subOrderId : " + pickSubOrderVo.getSubOrderId());
                }finally {
                    if (lock.isLocked()) {
                        lock.unlock();
                    }
                }
            }
        } catch (Exception e) {
            log.error("拣货出库修改subOrder实发数量和实收数量消息消费异常", e);
        }
    }

    @Override
    public List<String> getKafkaIds() {
        return Arrays.asList(
                QYApplicationContext.applicationNameSwitch + KafkaTopicConstant.PICK_UPDATE_SUB_ORDER_QUANTITY_TOPIC
        );
    }

}
