//package com.pinshang.qingyun.order.thread;
//
//import com.pinshang.qingyun.box.utils.SpringUtil;
//import com.pinshang.qingyun.order.model.order.DeliveryTime;
//import com.pinshang.qingyun.order.service.SplitOrderService;
//import com.pinshang.qingyun.order.service.syncOrderJob.SyncOrderJobService;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.web.context.support.WebApplicationContextUtils;
//
//import javax.servlet.ServletContextEvent;
//import javax.servlet.ServletContextListener;
//import java.util.List;
//
////@WebListener
//public class ContextWebListener implements ServletContextListener{
//	@Autowired
//	SyncOrderJobService syncOrderJobService;
//	@Autowired
//	SplitOrderService splitOrderService;
//
//	@Override
//	public void contextInitialized(ServletContextEvent sce) {
//		WebApplicationContextUtils.getRequiredWebApplicationContext(sce.getServletContext()).getAutowireCapableBeanFactory().autowireBean(this);
//		syncOrderListener();
//		//splitOrderListener();
//	}
//
//	@Override
//	public void contextDestroyed(ServletContextEvent sce) {
//
//	}
//
//	private void syncOrderListener(){
//		List<DeliveryTime> deliveryTimes =syncOrderJobService.getDeliveryTimeList(null);
//		if(SpringUtil.isEmpty(deliveryTimes)){
//			return ;
//		}
//		deliveryTimes.forEach((deliveryTime) -> {
//			String lineGroupId = deliveryTime.getLineGroupId();
//			syncOrderJobService.execute(lineGroupId, syncOrderJobService);
//		});
//	}
////	private void splitOrderListener(){
////		List<Long> orderIds =splitOrderService.queryUnSplitOrderId();
////		if(null ==orderIds || orderIds.isEmpty()){
////			return ;
////		}
////		orderIds.forEach((orderId) -> {
////			SplitOrderKafkaVo vo =new SplitOrderKafkaVo();
////			vo.setOrderId(orderId);
////			vo.setType(KafkaMessageOperationTypeEnum.UPDATE);
////			splitOrderService.execute(vo, splitOrderService);
////		});
////	}
//
//}
