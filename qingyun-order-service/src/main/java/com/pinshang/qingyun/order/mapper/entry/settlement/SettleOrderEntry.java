package com.pinshang.qingyun.order.mapper.entry.settlement;

import com.pinshang.qingyun.base.enums.settlement.SettleOrderSourceTypeEnum;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Data
public class SettleOrderEntry {
    private String kid;
    private String uid;
    private String sourceType;
    private String orderCode;
    private String sourceId;
    private BigDecimal deliveryTotalAmount;
    private String storeCode;
    private Long supervisorId;
    private Long storeTypeId;
    private String stockOutOrderCode;
    private Long officeDirectorId;
    private Long salesmanId;
    private Long deliverymanId;
    private Long storeLineId;
    private String totalAmount;
    private String storeId;
    private String storeName;
    private Long settlmentId;
    private Long regionManagerId;
    private String settlementName;
    private Date deliveryTime;
    private Date orderTime;
    private int logisticsModel;
    private String deliveryAddress;

    public String getSourceType() {
        SettleOrderSourceTypeEnum settleOrderSourceTypeEnum = SettleOrderSourceTypeEnum.fromCode(logisticsModel);
        return settleOrderSourceTypeEnum == null ? "未知类型：" + logisticsModel : settleOrderSourceTypeEnum.name();
    }
}
