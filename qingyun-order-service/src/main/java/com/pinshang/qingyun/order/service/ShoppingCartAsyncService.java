package com.pinshang.qingyun.order.service;

import com.pinshang.qingyun.base.spring.SpringBeanFinder;
import com.pinshang.qingyun.order.constant.ThreadPoolBeanConstants;
import com.pinshang.qingyun.order.mapper.ShoppingCartMapper;
import com.pinshang.qingyun.order.mapper.entry.order.ShoppingCartEntry;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * @Author: sk
 * @Date: 2022/6/15
 */
@Slf4j
@Service
public class ShoppingCartAsyncService {

    @Autowired
    private ShoppingCartMapper shoppingCartMapper;
    @Autowired
    private ShoppingCartUpdateService shoppingCartUpdateService;
    @Autowired
    private WeChatSendMessageService weChatSendMessageService;

    /**
     * 处理自动订货购物车、代销商户购物车
     * @return
     */
    @Async
    public Boolean dealAutoShoppingCart(){
        // 查询所有没有提交的购物车
        List<ShoppingCartEntry> shoppingCartList = shoppingCartMapper.queryAutoShoppingCart();
        if(CollectionUtils.isEmpty(shoppingCartList)){
            log.warn("没有需要处理的自动订货购物车、代销商户购物车");
            return Boolean.FALSE;
        }

        // 根据storeId分组
        Map<Long, List<ShoppingCartEntry>> shoppingCartMap = shoppingCartList.stream().collect(Collectors.groupingBy(ShoppingCartEntry::getStoreId));

        // 获取线程池
        ThreadPoolTaskExecutor threadPool = (ThreadPoolTaskExecutor) SpringBeanFinder.getBean(ThreadPoolBeanConstants.ORDER_THREADPOOL);

        for(Map.Entry<Long, List<ShoppingCartEntry>> entry : shoppingCartMap.entrySet()){
            // 创建任务并提交到线程池中
            threadPool.execute(new Runnable() {
                @Override
                public void run() {
                    try {
                        Long storeId = entry.getKey();
                        List<ShoppingCartEntry> list = entry.getValue();
                        // 一次处理一个storeId下面的购物车
                        shoppingCartUpdateService.dealAutoShoppingCart(storeId, list);
                    } catch (Exception e) {
                        log.error("处理自动订货购物车、代销商户购物车异常：storeId={}",entry.getKey(),e);

                        StringBuffer sb = new StringBuffer();
                        sb.append("处理自动订货购物车、代销商户购物车异常,客户id " + entry.getKey());
                        weChatSendMessageService.sendWeChatMessage(sb.toString());
                    }
                }
            });
        }

        return Boolean.TRUE;
    }
}
