package com.pinshang.qingyun.order.mapper.entry.store;

import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2019/3/11 16:15
 */
@Data
public class StoreSettlementEntry {
    /**
     * 客户id
     */
    private Long storeId;
    /**
     * 余额
     */
    private Double collectPrice;

    /**
     * 结账客户ID
     */
    private Long settlementId;

    /**
     * 结账客户 编码
     */
    private String settlementCode;

    /**
     * 结账客户 名称
     */
    private String settlementName;

    /**
     * B端 结账客户 起始时间
     */
    private Date startBillDate;

    /**
     * 鲜食 结账客户 起始时间
     */
    private Date xsStartBillDate;





}
