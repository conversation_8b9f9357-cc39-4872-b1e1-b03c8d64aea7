package com.pinshang.qingyun.order.mapper;

import com.pinshang.qingyun.base.mybatis.MyMapper;
import com.pinshang.qingyun.order.model.commodity.ShopCommodity;
import com.pinshang.qingyun.order.vo.auto.AutoCommodityVO;
import com.pinshang.qingyun.order.vo.auto.ImportStockQuantityVO;
import com.pinshang.qingyun.order.vo.shop.ShopCommodityWeightPriceInfoVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Mapper
@Repository
public interface ShopCommodityMapper extends MyMapper<ShopCommodity>{

    /**
     * 根据门店和商品code查询商品信息
     * @param shopId
     * @param commodityCodes
     * @return
     */
    List<ImportStockQuantityVO> listByShopAndCommodityCode(@Param("shopId") Long shopId, @Param("list") List<String> commodityCodes);


    AutoCommodityVO commodityInfo(@Param("shopId") Long shopId, @Param("barCode") String barCode);


    List<ShopCommodityWeightPriceInfoVO> queryShopCommodityWeightPrice(@Param("commodityIdList")List<Long> commodityIdList, @Param("shopIdList")List<Long> shopIdList);


}
