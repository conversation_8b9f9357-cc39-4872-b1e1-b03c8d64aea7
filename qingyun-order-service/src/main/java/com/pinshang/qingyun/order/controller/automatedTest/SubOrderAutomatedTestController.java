package com.pinshang.qingyun.order.controller.automatedTest;

import com.github.pagehelper.PageInfo;
import com.pinshang.qingyun.base.api.TokenInfo;
import com.pinshang.qingyun.base.configure.expand.QYAssert;
import com.pinshang.qingyun.base.constant.QingyunConstant;
import com.pinshang.qingyun.base.enums.DeliveryOrderTypeEnums;
import com.pinshang.qingyun.base.util.FastThreadLocalUtil;
import com.pinshang.qingyun.box.utils.SpringUtil;
import com.pinshang.qingyun.order.dto.OrderCancelMockDTO;
import com.pinshang.qingyun.order.dto.OrderMockDTO;
import com.pinshang.qingyun.order.mapper.entry.order.SubOrderItemListEntry;
import com.pinshang.qingyun.order.mapper.entry.order.SubOrderListEntry;
import com.pinshang.qingyun.order.model.order.SubOrder;
import com.pinshang.qingyun.order.service.SubOrderService;
import com.pinshang.qingyun.order.vo.OrderMockVO;
import com.pinshang.qingyun.order.vo.order.PickSubOrderItemRespVo;
import com.pinshang.qingyun.order.vo.order.PickSubOrderVo;
import com.pinshang.qingyun.order.vo.order.SubOrderSearchVo;
import com.pinshang.qingyun.order.vo.order.SubOrderVo;
import com.pinshang.qingyun.order.vo.subOrder.UnDeliverySubOrderJobReqVo;
import com.pinshang.qingyun.renderer.annotation.MethodRender;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;
import java.util.concurrent.TimeUnit;

@RestController
@RequestMapping("/subOrderAutomatedTest")
@Slf4j
public class SubOrderAutomatedTestController {

	@Autowired
	private SubOrderService subOrderService;
	@Resource
	private RedissonClient redissonClient;

	//查询未生成发货单的子订单列表
	@PostMapping("/queryUnDeliverySubOrderList")
	@MethodRender
	public PageInfo<SubOrderListEntry> queryUnDeliverySubOrderList(@RequestBody SubOrderVo dto){
		QYAssert.isTrue(dto!=null && dto.getOrderTime()!=null,"送货日期不能为空");
		TokenInfo ti = FastThreadLocalUtil.getQY();
		dto.setEnterpriseId(ti.getEnterpriseId());
		return subOrderService.queryUnDeliverySubOrderListV2(dto);
	}

	/*
	 * 查询未生成发货单的子订单明细
	 */
	@PostMapping("/querySubOrderItemList")
	@MethodRender
	public List<SubOrderItemListEntry> querySubOrderItemList(@RequestBody SubOrderSearchVo vo){
		return subOrderService.querySubOrderItemList(vo);
	}

	/*
	 * so单转do单
	 */
	@PostMapping("/createDeliveryOrder")
	public boolean creatDeliveryOrder(@RequestBody SubOrderSearchVo vo)  {
		QYAssert.notNull(vo!=null && SpringUtil.isNotEmpty(vo.getOrderIds()),"请至少选择一个订单");
		QYAssert.isTrue(vo.getWarehouseId()!=null,"请选择仓库");
		String redisLockKey = String.format("%s:%s:%s", QingyunConstant.CREATE_DELIVERY_ORDER_LOCK, DeliveryOrderTypeEnums.SALE.getName(),vo.getWarehouseId());
		RLock lock = redissonClient.getLock(redisLockKey);
		if (lock.tryLock()) {
			try {
				subOrderService.creatDeliveryOrder(vo);
			} finally {
				lock.unlock();
			}
		}
		return true;
	}

	//修改子单状态
	@PostMapping("/updateSubOrderStatus")
	public Integer updateSubOrderStatus(@RequestBody List<Long> subOrderIdList,@RequestParam(value = "subOrderStatus",required = false) Integer subOrderStatus){
		return subOrderService.updateSubOrderStatus(subOrderIdList,subOrderStatus);
	}


	@RequestMapping(value = "/findItemsBySubOrderId/{subOrderId}", method = RequestMethod.GET)
	List<PickSubOrderItemRespVo> findItemsBySubOrderId(@PathVariable("subOrderId") Long subOrderId){
        if (subOrderId == null) {
            return null;
        }
        return subOrderService.findItemsBySubOrderId(subOrderId);
	}


    /**
     * 批量更新subOrder的实发数量
     * @param deliveryItemList
     * @return
     */
    @RequestMapping(value = "/batchUpdateDeliveryQuantity", method = RequestMethod.POST)
    @Deprecated
    int batchUpdateDeliveryQuantity(@RequestBody List<PickSubOrderItemRespVo> deliveryItemList) {
        if (SpringUtil.isEmpty(deliveryItemList)) {
            return 0;
        }
        return subOrderService.batchUpdateDeliveryQuantity(deliveryItemList);
    }

	@RequestMapping(value = "/batchUpdateSubOrderQuantity", method = RequestMethod.POST)
	boolean batchUpdateDeliveryQuantityV2(@RequestBody List<PickSubOrderVo> subOrderList) {
		if(SpringUtil.isEmpty(subOrderList)){
			return false;
		}

		for (PickSubOrderVo pickSubOrderVo : subOrderList) {
			SubOrder subOrder = subOrderService.querySubOrderBySubOrderId(pickSubOrderVo.getSubOrderId());
			if (subOrder == null) {
				continue;
			}

			// 如果已经回写过实发数量和实收数量，则跳过
			if(subOrder.getWritebackRealQtyFlag() != null && subOrder.getWritebackRealQtyFlag()){
				continue;
			}

			// 根据orderId 加锁60秒
			RLock lock = redissonClient.getLock("updateOrderDeliveryQuantity" + subOrder.getOrderId());
			lock.lock(60L, TimeUnit.SECONDS);
			try {

				subOrderService.batchUpdateDeliveryQuantityV2(pickSubOrderVo, subOrder.getOrderId());
			} catch (Exception e){
				log.error("自动化测试拣货出库修改subOrder实发数量和实收数量消异常, subOrderId {}", pickSubOrderVo.getSubOrderId() , e);
			}finally {
				if (lock.isLocked()) {
					lock.unlock();
				}
			}
		}

		return true;
    }




}
