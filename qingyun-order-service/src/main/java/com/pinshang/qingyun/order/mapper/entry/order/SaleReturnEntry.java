package com.pinshang.qingyun.order.mapper.entry.order;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * Created by crell on 2017/11/6.
 */
@Data
public class SaleReturnEntry {

    private String id;
    @ApiModelProperty("物流配送模式0=直送，1＝配送，2＝直通")
    private Integer logisticsModel;
    @ApiModelProperty("退货单号")
    private String orderCode;
    @ApiModelProperty("供应商ID")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long supplierId;
    @ApiModelProperty("供应商")
    private String supplierName;
    @ApiModelProperty("状态-0＝取消，1＝待入库(待确认)，2＝已入库（已确认）,3=收货中  ")
    private Integer status;
    @ApiModelProperty("退货金额")
    private BigDecimal returnAmount;

    private String createName;
    
    private Date createTime;

    private String shopName;

    private String updateName;

    private Date updateTime;

    private Integer categoryNum;

    private Long createId;
    @ApiModelProperty("客户ID")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long storeId;
    @JsonSerialize(using = ToStringSerializer.class)
    private Long shopId;

    private Long stallId;
    private String stallName;
}
