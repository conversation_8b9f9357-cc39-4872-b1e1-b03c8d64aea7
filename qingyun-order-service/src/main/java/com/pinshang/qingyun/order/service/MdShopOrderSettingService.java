package com.pinshang.qingyun.order.service;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.pinshang.qingyun.base.configure.expand.QYAssert;
import com.pinshang.qingyun.base.enums.IogisticsModelEnums;
import com.pinshang.qingyun.base.enums.OperateTypeEnums;
import com.pinshang.qingyun.base.enums.YesOrNoEnums;
import com.pinshang.qingyun.base.enums.shop.ShopTypeEnums;
import com.pinshang.qingyun.base.exception.ParamException;
import com.pinshang.qingyun.box.utils.*;
import com.pinshang.qingyun.common.dto.DictionaryODTO;
import com.pinshang.qingyun.common.service.DictionaryClient;
import com.pinshang.qingyun.order.dto.XDCommodityODTO;
import com.pinshang.qingyun.order.dto.order.SupplierBlackODTO;
import com.pinshang.qingyun.order.dto.order.SupplierBlackVo;
import com.pinshang.qingyun.order.mapper.CommodityMapper;
import com.pinshang.qingyun.order.mapper.MdShopOrderSettingLogMapper;
import com.pinshang.qingyun.order.mapper.MdShopOrderSettingMapper;
import com.pinshang.qingyun.order.mapper.entry.CommodityEntry;
import com.pinshang.qingyun.order.mapper.entry.MdShopOrderSettingEntry;
import com.pinshang.qingyun.order.mapper.entry.MdShopOrderSettingLogEntry;
import com.pinshang.qingyun.order.model.shop.MdShopOrderSetting;
import com.pinshang.qingyun.order.model.shop.MdShopOrderSettingLog;
import com.pinshang.qingyun.order.vo.shop.MdShopOrderSettingLogVo;
import com.pinshang.qingyun.order.vo.shop.MdShopOrderSettingVo;
import com.pinshang.qingyun.order.vo.shop.ShopOrderSettingDealVo;
import com.pinshang.qingyun.order.vo.shop.ShopOrderSettingDetailsDealVo;
import com.pinshang.qingyun.renderer.service.IRenderService;
import com.pinshang.qingyun.storage.dto.CommoditySupplierODto;
import com.pinshang.qingyun.storage.dto.CommodityWarehouseODto;
import com.pinshang.qingyun.storage.dto.SupplierCommodityListODTO;
import com.pinshang.qingyun.storage.dto.WarehouseODto;
import com.pinshang.qingyun.storage.service.CommoditySupplierClient;
import com.pinshang.qingyun.storage.service.CommodityWarehouseClient;
import com.pinshang.qingyun.storage.service.WarehouseClient;
import com.pinshang.qingyun.supplier.dto.SupplierODTO;
import com.pinshang.qingyun.supplier.service.SupplierClient;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.joda.time.DateTime;
import org.joda.time.format.DateTimeFormat;
import org.redisson.api.*;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import tk.mybatis.mapper.entity.Example;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Service
public class MdShopOrderSettingService {

    @Autowired
    private MdShopOrderSettingMapper mdShopOrderSettingMapper;

    @Autowired
    private MdShopOrderSettingLogMapper mdShopOrderSettingLogMapper;

    @Autowired
    private DictionaryClient dictionaryClient;

    @Autowired
    private SupplierClient supplierClient;

    @Autowired
    private CommoditySupplierClient commoditySupplierClient;

    @Autowired
    private WarehouseClient warehouseClient;

    @Autowired
    private CommodityWarehouseClient commodityWarehouseClient;

    @Autowired
    private JobService jobService;
    @Autowired
    private WeChatSendMessageService weChatSendMessageService;
    @Autowired
    private SendLogService sendLogService;
    @Autowired
    private IRenderService renderService;
    @Autowired
    private CommodityMapper commodityMapper;
    @Autowired
    private ShopService shopService;
    @Autowired
    private RedissonClient redissonClient;

    /**
     *分页查询门店下单设置表
     * @param shopOrderSettingVo
     * @return
     */
    public PageInfo<MdShopOrderSettingEntry> queryMdShopOrderSettingListByParams(MdShopOrderSettingVo shopOrderSettingVo){
        PageInfo<MdShopOrderSettingEntry> pageDate = null ;
        pageDate = PageHelper.startPage(shopOrderSettingVo.getPageNo(), shopOrderSettingVo.getPageSize()).doSelectPageInfo(() -> {
            mdShopOrderSettingMapper.queryMdShopOrderSettingListByParams(shopOrderSettingVo);
        });
        if(!CollectionUtils.isEmpty(pageDate.getList())){
            Map<String, String> dictionaryMap = getDeleveryTimeRangeMap();

            Map<Long, WarehouseODto> warehouseMap = new HashMap<>();
            List<Long> warehouseIds = new ArrayList<>();
            for(MdShopOrderSettingEntry entry:pageDate.getList()){
                if(StringUtils.isNotBlank(entry.getWarehouseId())){
                    warehouseIds.add(Long.valueOf(entry.getWarehouseId()));
                }
            }
            if(!CollectionUtils.isEmpty(warehouseIds)){
                warehouseMap = warehouseClient.queryWarehouseListByIds(warehouseIds);
            }
            for(MdShopOrderSettingEntry entry:pageDate.getList()){
                entry.setDeleveryTimeRangeName(dictionaryMap.get(entry.getDeleveryTimeRange()));
                if(null != warehouseMap && StringUtils.isNotBlank(entry.getWarehouseId())){
                    WarehouseODto warehouseODto = warehouseMap.get(Long.valueOf(entry.getWarehouseId()));
                    entry.setWarehouseName(null != warehouseODto ? warehouseODto.getWarehouseName() : "");
                }
            }
        }
        return pageDate;
    }

    /**
     * 查询商品通用设置， 返回的结果是一个商品只唯一对应一条记录
     * @param shopType
     * @param commodityIds
     * @return
     */
    public List<MdShopOrderSettingEntry> queryMdShopOrderSettingListByShopType(Integer shopType, List<String> commodityIds){
        return getMdShopOrderSettingFromRedission(shopType, commodityIds);
    }

    /**
     * 查询商品通用设置， 返回的结果是一个商品只唯一对应一条记录
     * @param storeId
     * @param commodityIds
     * @return
     */
    public List<MdShopOrderSettingEntry> queryMdShopOrderSettingListByIds(Long storeId, List<String> commodityIds){
        Integer shopType = ( storeId > 0 ? shopService.getShopType(storeId) : -1 );
        return getMdShopOrderSettingFromRedission(shopType, commodityIds);
    }

    /**
     * 从缓存获取订货设置(缓存了1分钟)
     * @param shopType
     * @param commodityIds
     * @return
     */
    public List<MdShopOrderSettingEntry> getMdShopOrderSettingFromRedission(Integer shopType, List<String> commodityIds){
        // 商品ids去重
        commodityIds = commodityIds.stream().distinct().collect(Collectors.toList());

        // 批量从缓存获取
        List<MdShopOrderSettingEntry> resultList = batchGet(shopType, commodityIds);

        // 缓存里面不存在，则重新查询并放入缓存1分钟
        if(CollectionUtils.isEmpty(resultList)) {
            resultList = getMdShopOrderSettingEntries(shopType, commodityIds);

            if(!CollectionUtils.isEmpty(resultList)){
                // 批量缓存1分钟
                batchSave(shopType, resultList);
            }
        }else {
            // 缓存里面存在的商品
            List<Long> cacheCommodityIdList = resultList.stream().map(item -> item.getCommodityId()).collect(Collectors.toList());

            List<String> notInCacheCommodityIds = commodityIds.stream().filter(p -> !cacheCommodityIdList.contains(Long.valueOf(p))).collect(Collectors.toList());
            if(!CollectionUtils.isEmpty(notInCacheCommodityIds)) {
                List<MdShopOrderSettingEntry> list = getMdShopOrderSettingEntries(shopType, notInCacheCommodityIds);
                if(!CollectionUtils.isEmpty(list)) {
                    resultList.addAll(list);
                    // 批量缓存1分钟
                    batchSave(shopType, list);
                }
            }
        }
        return resultList;
    }

    /**
     * 批量获取订货通用设置
     * @param shopType
     * @param commodityIds
     * @return
     */
    private List<MdShopOrderSettingEntry> batchGet(Integer shopType, List<String> commodityIds) {

        //结果集
        List<MdShopOrderSettingEntry> result = new ArrayList<>();
        //future列表
        LinkedList<RFuture<Object>> futures = new LinkedList<>();

        RBatch batch = redissonClient.createBatch();
        for (String commodityId : commodityIds) {
            String key = buildKey(shopType, commodityId);
            RFuture<Object> async =  batch.getBucket(key).getAsync();
            futures.add(async);
        }
        //批量执行
        batch.execute();

        while (!futures.isEmpty()){
            RFuture<Object> first = futures.removeFirst();
            // 获取当前值,未完成时值为null,使用isDone方法区分value不存在还是任务未完成
            Object o = first.getNow();
            if (o != null){
                result.add((MdShopOrderSettingEntry) o);
            }else {
                if (!first.isDone()){
                    futures.addLast(first);
                }
            }
        }

        return result;
    }

    /**
     * 订货通用设置,批量缓存1分钟
     */
    private void batchSave(Integer shopType, List<MdShopOrderSettingEntry> mdShopOrderSettingList){
        RBatch saveBatch = redissonClient.createBatch();
        for(MdShopOrderSettingEntry entry : mdShopOrderSettingList) {
            String key = buildKey(shopType, entry.getCommodityId() + "");
            saveBatch.getBucket(key).setAsync(entry);
            saveBatch.getBucket(key).expireAsync(60, TimeUnit.SECONDS);
        }
        // 执行批量操作
        saveBatch.execute();
    }

    /**
     * 批量删除
     * @param shopType
     * @param commodityIds
     */
    public  void batchDeleteCommodities(Integer shopType, List<String> commodityIds) {
        RBatch batch = redissonClient.createBatch();
        // 构建批量删除请求
        for (String commodityId : commodityIds) {
            String key = buildKey(shopType, commodityId);
            batch.getBucket(key).deleteAsync();
        }
        // 执行批量操作
        batch.execute();
    }

    /**
     * 构建Redis键
     */
    private String buildKey(Integer shopType, String commodityId) {
        return "order:shopOrderSetting:" + shopType + ":" + commodityId;
    }

    public List<MdShopOrderSettingEntry> getMdShopOrderSettingEntries(Integer shopType, List<String> commodityIds) {
        // 0. 自定义配置 + 通用配置
        List<MdShopOrderSettingEntry> result = new ArrayList<>();

        // 1. 查询门店商品自定义配置
        List<MdShopOrderSettingEntry> customSettings = mdShopOrderSettingMapper.queryMdShopOrderSettingListByCommodityIds(shopType, commodityIds, true);
        result.addAll(customSettings);

        // 2. 查询门店商品通用配置
        List<String> commonCommodityIdList = null;
        if (SpringUtil.isEmpty(customSettings)) {
            // 自定义配置为空表示都是通用设置
            commonCommodityIdList = commodityIds;
        } else {
            // 可能都是自定义配置也有可能是 部分自定义配置和部分通用配置
            // 求差集: 找出哪些商品为通用设置 commonCommodityIdList = allCommodityIdSet - customCommodityIdSet
            Set<String> allCommodityIdSet = new HashSet<>(commodityIds);
            Set<String> customCommodityIdSet = customSettings.stream().map(item -> item.getCommodityId().toString()).collect(Collectors.toSet());
            allCommodityIdSet.removeAll(customCommodityIdSet);

            // 不为空表示一部分自定义配置一部分通用配置
            if (!SpringUtil.isEmpty(allCommodityIdSet)) {
                commonCommodityIdList = new ArrayList<>(allCommodityIdSet);
            }
        }

        if (SpringUtil.isNotEmpty(commonCommodityIdList)) {
            List<MdShopOrderSettingEntry> commonSettings = mdShopOrderSettingMapper.queryMdShopOrderSettingListByCommodityIds(shopType, commonCommodityIdList, false);
            result.addAll(commonSettings);
        }
        return result;
    }

    /**
     * 获取商品id对应的设置
     * @param storeId
     * @param commodityIds
     * @return
     */
    public Map<Long, MdShopOrderSettingEntry> getCommodityIdAndSettingMap(Long storeId, List<String> commodityIds){
        List<MdShopOrderSettingEntry> list = this.queryMdShopOrderSettingListByIds(storeId, commodityIds);
        Map<Long, MdShopOrderSettingEntry> map = list.stream().collect(Collectors.toMap(MdShopOrderSettingEntry::getCommodityId, Function.identity()));
        return map;
    }

    /**
     * 根据主键ID查询门店订货通用设置
     * @param id
     * @return
     */
    public MdShopOrderSetting findMdShopOrderSettingById(Long id) {
        QYAssert.isTrue(null !=id , "id is not null!");
        return mdShopOrderSettingMapper.selectByPrimaryKey(id);
    }

    /**
     * 更新门店订货通用设置
     * @param vo
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public Integer updateMdShopOrderSettingById(MdShopOrderSetting vo) {
        QYAssert.isTrue(null != vo.getId() , "id is not null!");
        //直送模式不允许可变价
        validateLogisticsModel(vo.getLogisticsModel(),vo.getChangePriceStatus());
        //记录门店订货通用设置日志
        List<MdShopOrderSetting>  list=new ArrayList<>();
        list.add(vo);
        insertMdShopOrderSettingLog(list);
        return mdShopOrderSettingMapper.updateMdShopOrderSettingByPrimaryKey(vo);
    }

    /**
     * 新增门店订货通用设置
     * @param vo
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public Integer insertMdShopOrderSetting(MdShopOrderSetting vo) {
        //QYAssert.isTrue(null !=vo.getId() , "id is not null!");
        List<MdShopOrderSetting>  list = new ArrayList<>();
        list.add(vo);
        insertMdShopOrderSettingLog(list);
        return mdShopOrderSettingMapper.insert(vo);
    }

    /**
     * 设置物流模式
     * @param commodityList
     * @param logisticsModel
     */
    public void setMdOrderSettingLogisticsModel(List<Long> commodityList,Integer logisticsModel){
        Map<Long, CommodityWarehouseODto> warehouseMap = commodityWarehouseClient.queryCommodityDefaultWarehouse(commodityList);
        for (Long commodityId : commodityList) {
            //获取商品信息
            XDCommodityODTO commodityODTO = commodityMapper.getXDCommodityODTOById(commodityId);
            //获取默认供应商
            Long supplierId = commoditySupplierClient.getDefaultSupplierId(commodityId);
            QYAssert.isTrue(null != supplierId,"请设置商品默认供应商");
            //获取供应商信息
            SupplierODTO supplier = supplierClient.getSupplierById(supplierId);
            commodityODTO.setLogisticsModel(logisticsModel);
            MdShopOrderSetting bean = setMdShopOrderSetting(commodityODTO, supplier);
            CommodityWarehouseODto commodityWarehouseODto = warehouseMap.get(commodityId);
            if(null != commodityWarehouseODto){
                bean.setWarehouseId(commodityWarehouseODto.getWarehouseId());
                bean.setDefaultWarehouseBeginTime(commodityWarehouseODto.getWorkBeginTime());
                bean.setDefaultWarehouseEndTime(commodityWarehouseODto.getWorkEndTime());
            }

            //获取全部门店类型的那条
            List<MdShopOrderSettingEntry> list = mdShopOrderSettingMapper.queryDefaultMdShopOrderSetting(commodityId, bean.getSupplierId());

            if (SpringUtil.isEmpty(list)) {
                this.insertMdShopOrderSetting(bean);
            }else{
                MdShopOrderSettingEntry entry =  list.get(0);
                bean.setId(entry.getId());
                bean.setDeleveryTimeRange(entry.getDeleveryTimeRange());
                bean.setChangePriceStatus(entry.getChangePriceStatus());
                bean.setCreateId(null);
                bean.setCreateTime(null);

                //记录门店订货通用设置日志
                List<MdShopOrderSetting>  logList = new ArrayList<>();
                logList.add(bean);
                insertMdShopOrderSettingLog(logList);

                mdShopOrderSettingMapper.updateByPrimaryKeySelective(bean);
            }
        }
    }

    private MdShopOrderSetting setMdShopOrderSetting(XDCommodityODTO commodity, SupplierODTO supplier) {
        MdShopOrderSetting bean = new MdShopOrderSetting();
        bean.setEnterpriseId(78L);
        bean.setCommodityId(Long.valueOf(commodity.getCommodityId()));
        bean.setCommodityCode(commodity.getCommodityCode());
        bean.setCommodityName(commodity.getCommodityName());
        bean.setCommoditySpec(commodity.getCommoditySpec());
        bean.setShopType(-1);
        bean.setSupplierId(supplier.getId());
        bean.setSupplierCode(supplier.getSupplierCode());
        bean.setSupplierName(supplier.getSupplierName());
        bean.setDefaultSupplierBeginTime(supplier.getSupplyStartTime());
        bean.setDefaultSupplierEndTime(supplier.getSupplyEndTime());
        bean.setLogisticsModel(commodity.getLogisticsModel());
        //默认0-1
        bean.setDeleveryTimeRange("0-1");
        bean.setChangePriceStatus(YesOrNoEnums.NO.getCode());
        bean.setCreateId(1L);
        bean.setCreateTime(new Date());
        bean.setUpdateId(1L);
        bean.setUpdateTime(new Date());
        bean.setIsNew(YesOrNoEnums.YES.getCode());//是否最新标记(0: 否 1: 是)
        return bean;
    }

    /**
     * 设置首选供应商
     * @param commodityList
     * @param supplierId
     */
    public void  setMdOrderSettingDefaultSupplier(List<Long> commodityList,Long supplierId,Integer logisticsModel,String deleveryTimeRange,Integer changePriceStatus){
        //获取供应商信息
        SupplierODTO supplier = supplierClient.getSupplierById(supplierId);
        Map<Long, CommodityWarehouseODto> warehouseMap = commodityWarehouseClient.queryCommodityDefaultWarehouse(commodityList);
        for (Long commodityId : commodityList) {
            Example ex =new Example(MdShopOrderSetting.class);
            ex.createCriteria().andEqualTo("commodityId", commodityId).andEqualTo("supplierId",supplierId);
            List<MdShopOrderSetting> list = mdShopOrderSettingMapper.selectByExample(ex);

            if(!CollectionUtils.isEmpty(list)){
                //isNew: 是否最新标记(0: 否 1: 是)
                batchUpdateIsNew(list, YesOrNoEnums.YES.getCode(),logisticsModel,deleveryTimeRange,changePriceStatus);

            }else{
                //获取商品信息
                XDCommodityODTO commodityODTO = commodityMapper.getXDCommodityODTOById(commodityId);

                MdShopOrderSetting bean = setMdShopOrderSetting(commodityODTO, supplier);
                CommodityWarehouseODto commodityWarehouseODto = warehouseMap.get(commodityId);
                if(null != commodityWarehouseODto){
                    bean.setWarehouseId(commodityWarehouseODto.getWarehouseId());
                    bean.setDefaultWarehouseBeginTime(commodityWarehouseODto.getWorkBeginTime());
                    bean.setDefaultWarehouseEndTime(commodityWarehouseODto.getWorkEndTime());
                }
                bean.setLogisticsModel(logisticsModel);
                bean.setDeleveryTimeRange(deleveryTimeRange);
                bean.setChangePriceStatus(changePriceStatus);
                this.insertMdShopOrderSetting(bean);
            }

            Example ex1 =new Example(MdShopOrderSetting.class);
            ex1.createCriteria().andEqualTo("commodityId", commodityId).andNotEqualTo("supplierId",supplierId);
            List<MdShopOrderSetting> list2 = mdShopOrderSettingMapper.selectByExample(ex1);
            if(!CollectionUtils.isEmpty(list2)){
                //isNew: 是否最新标记(0: 否 1: 是)
                batchUpdateIsNew(list2, YesOrNoEnums.NO.getCode(),null,null,null);
            }
        }
    }

    /**
     * 设置首选供应商批量操作
     * @param list
     * @param isNew
     */
    private void batchUpdateIsNew(List<MdShopOrderSetting> list, Integer isNew,Integer logisticsModel,String deliveryTimeRange,Integer changePriceStatus) {
        //记录日志
        batchInsertMdShopOrderSettingLog(list, logisticsModel, deliveryTimeRange, changePriceStatus);

        List<Long> ids = list.stream().map(MdShopOrderSetting::getId).collect(Collectors.toList());
        MdShopOrderSetting updateSetting = new MdShopOrderSetting();
        updateSetting.setIsNew(isNew);
        updateSetting.setUpdateId(1L);
        updateSetting.setUpdateTime(new Date());
        if(isNew.equals(YesOrNoEnums.YES.getCode())){
            updateSetting.setLogisticsModel(logisticsModel);
            updateSetting.setDeleveryTimeRange(deliveryTimeRange);
            updateSetting.setChangePriceStatus(changePriceStatus);
        }
        Example updateExample = new Example(MdShopOrderSetting.class);
        updateExample.createCriteria().andIn("id", ids);
        mdShopOrderSettingMapper.updateByExampleSelective(updateSetting, updateExample);
    }

    /**
     * 设置首选供应商,设置首选仓库,设置仓库时间，设置供应商时间。
     * 批量记录日志
     * @param list
     */
    public void batchInsertMdShopOrderSettingLog(List<MdShopOrderSetting>  list, Integer logisticsModel,String deliveryTimeRange,Integer changePriceStatus){
        List<MdShopOrderSettingLog> logList=new ArrayList<>();
        for(MdShopOrderSetting vo:list){
            MdShopOrderSettingLog logVo=new MdShopOrderSettingLog();
            BeanUtils.copyProperties(vo,logVo);
            logVo.setCreateTime(new Date());
            logVo.setFromLogisticsModel(vo.getLogisticsModel());
            logVo.setFromDeleveryTimeRange(vo.getDeleveryTimeRange());
            logVo.setFromChangePriceStatus(vo.getChangePriceStatus());
            logVo.setCreateId(1L);
            logVo.setOpreateType(2);
            logVo.setToLogisticsModel(logisticsModel != null ? logisticsModel : vo.getLogisticsModel());
            logVo.setToDeleveryTimeRange(StringUtils.isNotBlank(deliveryTimeRange) ? deliveryTimeRange : vo.getDeleveryTimeRange());
            logVo.setToChangePriceStatus(changePriceStatus != null ? changePriceStatus : vo.getChangePriceStatus());

            logList.add(logVo);
        }
        if(!CollectionUtils.isEmpty(logList)){
            mdShopOrderSettingLogMapper.insertList(logList);
        }
    }
    /**
     * 设置首选仓库
     * @param commodityList
     * @param wareHouseId
     */
    public void setMdOrderSettingDefaultWarehouse(List<Long> commodityList,Long wareHouseId){
        //获取仓库信息
        List<Long> warehouseIds = new ArrayList<>();
        warehouseIds.add(wareHouseId);
        Map<Long, WarehouseODto> warehouseMap = warehouseClient.queryWarehouseListByIds(warehouseIds);
        WarehouseODto warehouseODto = warehouseMap.get(wareHouseId);

        for (Long commodityId : commodityList) {
            Example ex =new Example(MdShopOrderSetting.class);
            ex.createCriteria().andEqualTo("commodityId", commodityId);
            List<MdShopOrderSetting> list = mdShopOrderSettingMapper.selectByExample(ex);
            if(!CollectionUtils.isEmpty(list)){
                List<Long> ids = list.stream().map(MdShopOrderSetting::getId).collect(Collectors.toList());

                MdShopOrderSetting updatemos = new MdShopOrderSetting();
                updatemos.setWarehouseId(wareHouseId);
                updatemos.setDefaultWarehouseBeginTime(warehouseODto.getWorkBeginTime());
                updatemos.setDefaultWarehouseEndTime(warehouseODto.getWorkEndTime());
                updatemos.setUpdateId(1L);
                updatemos.setUpdateTime(new Date());
                Example updateExample = new Example(MdShopOrderSetting.class);
                updateExample.createCriteria().andIn("id",ids);
                mdShopOrderSettingMapper.updateByExampleSelective(updatemos, updateExample);

                //记录日志
                batchInsertMdShopOrderSettingLog(list,null,null,null);
            }
        }
    }

    /**
     * 修改供应商时间
     * @param supplierId
     * @param defaultSupplierBeginTime
     * @param defaultSupplierEndTime
     */
    public  void setMdOrderSettingDefaultSupplierTime(Long supplierId,String defaultSupplierBeginTime,String defaultSupplierEndTime){
        Example ex =new Example(MdShopOrderSetting.class);
        ex.createCriteria().andEqualTo("supplierId",supplierId);
        List<MdShopOrderSetting> list = mdShopOrderSettingMapper.selectByExample(ex);
        if(!CollectionUtils.isEmpty(list)){
            List<Long> ids = list.stream().map(MdShopOrderSetting::getId).collect(Collectors.toList());

            MdShopOrderSetting updatemos = new MdShopOrderSetting();
            updatemos.setDefaultSupplierBeginTime(defaultSupplierBeginTime);
            updatemos.setDefaultSupplierEndTime(defaultSupplierEndTime);
            updatemos.setUpdateId(1L);
            updatemos.setUpdateTime(new Date());
            Example updateExample = new Example(MdShopOrderSetting.class);
            updateExample.createCriteria().andIn("id",ids);
            mdShopOrderSettingMapper.updateByExampleSelective(updatemos, updateExample);

            //记录日志
            batchInsertMdShopOrderSettingLog(list,null,null,null);
        }
    }

    /**
     * 修改仓库时间
     * @param warehouseId
     * @param defaultWarehouseBeginTime
     * @param defaultWarehouseEndTime
     */
    public  void setMdOrderSettingDefaultWareHouseTime(Long warehouseId,String defaultWarehouseBeginTime,String defaultWarehouseEndTime){
        Example ex =new Example(MdShopOrderSetting.class);
        ex.createCriteria().andEqualTo("warehouseId",warehouseId);
        List<MdShopOrderSetting> list = mdShopOrderSettingMapper.selectByExample(ex);
        if(!CollectionUtils.isEmpty(list)){
            List<Long> ids = list.stream().map(MdShopOrderSetting::getId).collect(Collectors.toList());

            MdShopOrderSetting updatemos = new MdShopOrderSetting();
            updatemos.setDefaultWarehouseBeginTime(defaultWarehouseBeginTime);
            updatemos.setDefaultWarehouseEndTime(defaultWarehouseEndTime);
            updatemos.setUpdateId(1L);
            updatemos.setUpdateTime(new Date());
            Example updateExample = new Example(MdShopOrderSetting.class);
            updateExample.createCriteria().andIn("id",ids);
            mdShopOrderSettingMapper.updateByExampleSelective(updatemos, updateExample);

            //记录日志
            batchInsertMdShopOrderSettingLog(list,null,null,null);
        }
    }
    /**
     * 处理门店订货通用设置
     * (用户设置物流模式，设置首选供应商，修改供应商时间，设置首选仓库，修改仓库时间)
     * @param vo
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public Integer dealMdShopOrderSetting(ShopOrderSettingDealVo vo) {
        List<Long> commodityList = vo.getCommodityIdList();
        Long supplierId = vo.getSupplierId();//供应商
        String defaultSupplierBeginTime = vo.getDefaultSupplierBeginTime();
        String defaultSupplierEndTime = vo.getDefaultSupplierEndTime();
        Integer logisticsModel = vo.getLogisticsModel();//物流模式
        Long warehouseId = vo.getWarehouseId();//仓库
        String defaultWarehouseBeginTime = vo.getDefaultWarehouseBeginTime();
        String defaultWarehouseEndTime = vo.getDefaultWarehouseEndTime();

        if(!CollectionUtils.isEmpty(commodityList)){
            //设置物流模式
            if(null != logisticsModel && null == supplierId ){
                setMdOrderSettingLogisticsModel(commodityList,logisticsModel);
            }

            //设置首选供应商
            if(null != supplierId){
                setMdOrderSettingDefaultSupplier(commodityList,supplierId,logisticsModel,vo.getDeleveryTimeRange(),vo.getChangePriceStatus());
            }

            //设置首选仓库
            if(null != warehouseId){
                setMdOrderSettingDefaultWarehouse(commodityList,warehouseId);
            }
        }
        //修改供应商时间
        if(null != supplierId && StringUtils.isNotBlank(defaultSupplierBeginTime) && StringUtils.isNotBlank(defaultSupplierEndTime)){
            generateJob(defaultSupplierEndTime);
            setMdOrderSettingDefaultSupplierTime(supplierId,defaultSupplierBeginTime,defaultSupplierEndTime);
        }

        //修改仓库时间
        if(null != warehouseId && StringUtils.isNotBlank(defaultWarehouseBeginTime) && StringUtils.isNotBlank(defaultWarehouseEndTime)){
           generateJob(defaultWarehouseEndTime);
           setMdOrderSettingDefaultWareHouseTime(warehouseId,defaultWarehouseBeginTime,defaultWarehouseEndTime);
        }
        return 1;
    }

    /**
     * 修改供应商和仓库时间种植任务，自动提交提货卡预约单
     * @param endTime
     */
    private void generateJob(String endTime) {
        if(DateTimeUtil.compareTime(DateTimeUtil.formatDate(new Date(),"HH:mm"), endTime)){
            String nowHour = DateUtil.getDateFormate(new Date(),"HH:mm");
            List<String> endTimeList = mdShopOrderSettingMapper.getEndTimeList(nowHour);
            if(!endTimeList.contains(endTime)){
                List<String> timeList = new ArrayList<>();
                timeList.add(endTime);
                //generateCreateTakeCardOrderTasks.generateJob(timeList);
                jobService.addTakeCardJob(timeList);
            }
        }
    }

    /**
     * 处理门店订货通用设置
     * @param list
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public Integer batchDealMdShopOrderSettingDetails(List<ShopOrderSettingDetailsDealVo> list){
        QYAssert.isTrue(SpringUtil.isNotEmpty(list),"参数异常");
        List<MdShopOrderSetting> shopOrderSettingList = new ArrayList<>();
        Integer result = 0;
        Date date = new Date();
        for(ShopOrderSettingDetailsDealVo vo : list){
            MdShopOrderSetting bean = new MdShopOrderSetting();
            SpringUtil.copyProperties(vo,bean);
            bean.setEnterpriseId(78L);
            bean.setShopType(-1);
            //默认0-1
            bean.setDeleveryTimeRange("0-1");
            bean.setChangePriceStatus(YesOrNoEnums.NO.getCode());
            bean.setCreateId(1L);
            bean.setCreateTime(date);
            bean.setUpdateId(1L);
            bean.setUpdateTime(date);
            bean.setIsNew(1);//是否最新标记(0: 否 1: 是)

            shopOrderSettingList.add(bean);

        }
        //判断集合 是否为空
        if(SpringUtil.isNotEmpty(shopOrderSettingList)){
            insertMdShopOrderSettingLog(shopOrderSettingList);
            mdShopOrderSettingMapper.insertList(shopOrderSettingList);
            result ++;
        }
        return result;
    }

    /**
     * 删除门店订货通用设置
     * @param id
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public Integer deleteMdShopOrderSettingById(Long id,Long createId) {
        QYAssert.isTrue(null !=id , "id is not null!");
        //记录门店订货通用设置日志
        MdShopOrderSetting vo=new MdShopOrderSetting();
        vo.setId(id);
        vo.setCreateId(createId);
        List<MdShopOrderSetting>  list=new ArrayList<>();
        list.add(vo);
        insertMdShopOrderSettingLog(list);
        return mdShopOrderSettingMapper.deleteByPrimaryKey(id);
    }

    /**
     * 记录门店订货通用设置日志
     */
    @Transactional(rollbackFor = Exception.class)
    public void insertMdShopOrderSettingLog(List<MdShopOrderSetting>  list){
        List<MdShopOrderSettingLog> logList = new ArrayList<MdShopOrderSettingLog>();
        for(MdShopOrderSetting vo:list){
            MdShopOrderSettingLog logVo = new MdShopOrderSettingLog();
            MdShopOrderSetting fromVO = (null != vo.getId() ? findMdShopOrderSettingById(vo.getId()) : vo);

            BeanUtils.copyProperties(fromVO,logVo);
            logVo.setCreateTime(new Date());
            logVo.setFromLogisticsModel(fromVO.getLogisticsModel());
            logVo.setFromDeleveryTimeRange(fromVO.getDeleveryTimeRange());
            logVo.setFromChangePriceStatus(fromVO.getChangePriceStatus());
            if(null ==vo.getId()){//新增
                logVo.setOpreateType(0);//0：新增1:删除  2：修改
                logVo.setToLogisticsModel(vo.getLogisticsModel());
                logVo.setToDeleveryTimeRange(vo.getDeleveryTimeRange());
                logVo.setToChangePriceStatus(vo.getChangePriceStatus());
            }
            if(null !=vo.getId() && null==vo.getUpdateId()){//删除
                QYAssert.isTrue( fromVO.getShopType() != -1 , "全部门店类型不能删除!");
                logVo.setCreateId(vo.getCreateId());
                logVo.setOpreateType(1);
                logVo.setToLogisticsModel(fromVO.getLogisticsModel());
                logVo.setToDeleveryTimeRange(fromVO.getDeleveryTimeRange());
                logVo.setToChangePriceStatus(fromVO.getChangePriceStatus());
            }
            if(null !=vo.getId() && null != vo.getUpdateId()){//修改
                logVo.setCreateId(vo.getUpdateId());
                logVo.setOpreateType(2);
                logVo.setToLogisticsModel(vo.getLogisticsModel());
                logVo.setToDeleveryTimeRange(vo.getDeleveryTimeRange());
                logVo.setToChangePriceStatus(vo.getChangePriceStatus());
            }
            logList.add(logVo);
        }
        if(!CollectionUtils.isEmpty(logList)){
            mdShopOrderSettingLogMapper.insertList(logList);
        }
    }

    private void validateLogisticsModel(Integer logisticsModel,Integer changePriceStatus) {
        boolean flag=( logisticsModel.equals(IogisticsModelEnums.DIRECT_SENDING.getCode()) && changePriceStatus.equals(YesOrNoEnums.YES.getCode()) );
        QYAssert.isTrue(!flag , "直送模式不允许可变价!");
    }

    /**
     * 分页查询门店下单设置日志表
     * @param shopOrderSettingLogVo
     * @return
     */
    public PageInfo<MdShopOrderSettingLogEntry> queryMdShopOrderSettingLogListByParams(MdShopOrderSettingLogVo shopOrderSettingLogVo){
        QYAssert.isTrue(StringUtils.isNotBlank(shopOrderSettingLogVo.getBeginDate())
                                && StringUtils.isNotBlank(shopOrderSettingLogVo.getEndDate()), "操作日期不能为空");

        if(!StringUtil.isBlank(shopOrderSettingLogVo.getBeginDate()) && !StringUtil.isBlank(shopOrderSettingLogVo.getEndDate())){
            DateTime beginDate = DateTime.parse(shopOrderSettingLogVo.getBeginDate(), DateTimeFormat.forPattern("yyyy-MM-dd"));
            DateTime endDate = DateTime.parse(shopOrderSettingLogVo.getEndDate(), DateTimeFormat.forPattern("yyyy-MM-dd"));
            QYAssert.isTrue(!beginDate.plusDays(30).isBefore(endDate), "查询时间段不能大于31天");

            shopOrderSettingLogVo.setBeginDate(shopOrderSettingLogVo.getBeginDate()+ " 00:00:00");
            shopOrderSettingLogVo.setEndDate(shopOrderSettingLogVo.getEndDate()+ " 23:59:59");
        }
        PageInfo<MdShopOrderSettingLogEntry> pageDate = null ;
        pageDate = PageHelper.startPage(shopOrderSettingLogVo.getPageNo(), shopOrderSettingLogVo.getPageSize()).doSelectPageInfo(() -> {
            mdShopOrderSettingLogMapper.queryMdShopOrderSettingLogListByParams(shopOrderSettingLogVo);
        });
        if(!CollectionUtils.isEmpty(pageDate.getList())){
            Map<String, String> dictionaryMap = getDeleveryTimeRangeMap();

            for(MdShopOrderSettingLogEntry entry:pageDate.getList()){
                entry.setToDeleveryTimeRangeName(dictionaryMap.get(entry.getToDeleveryTimeRange()));
            }
        }
        return pageDate;
    }
    //获取送货日期范围数据字典
    private Map<String, String> getDeleveryTimeRangeMap() {
        List<DictionaryODTO> dictionaryList=dictionaryClient.listDictionaryByOptionName("deleveryTimeRange");
        return dictionaryList.stream().collect(
                Collectors.toMap(DictionaryODTO::getOptionCode, DictionaryODTO::getOptionName));
    }

    /**
     * 批量导入新增
     * @param list
     * @return
     * @throws ParamException
     */
    @Transactional(rollbackFor = Exception.class)
    public Integer saveMdShopOrderSettingList(List<MdShopOrderSettingVo> list) {
        if(!CollectionUtils.isEmpty(list)){
            List<String> commodityCodeList = new ArrayList<String>();
            //验证
            valid(list,commodityCodeList);

            //查找要导入的商品list
            List<CommodityEntry> commodityList = mdShopOrderSettingMapper.queryCommodityEntryByCommodityCodes(commodityCodeList);
            Map<String, CommodityEntry> commodityEntryMap = commodityList.stream().collect(Collectors.toMap(CommodityEntry::getCommodityCode, (p) -> p));
            List<Long> commodityIdList = commodityList.stream().map(item -> item.getCommodityId()).collect(Collectors.toList());

            Map<String, MdShopOrderSettingEntry> mdMap = new HashMap<>();
            if(!CollectionUtils.isEmpty(commodityIdList)){
                //判断与数据库重复数据
                List<MdShopOrderSettingEntry> mdList = mdShopOrderSettingMapper.queryMdShopOrderSettingByCommIds(commodityIdList);
                if(!CollectionUtils.isEmpty(mdList)){
                    mdList.forEach(e -> {
                        mdMap.put(e.getShopType() + "" + e.getCommodityId() + "" + e.getSupplierId(), e);
                    });
                }
            }

            //数据库层面校验
            List<MdShopOrderSetting> mdShopOrderSettingList=new ArrayList<>();

            List<Long> commodityIds = new ArrayList<>();//商品id list
            validDBRepeat(list, commodityEntryMap, mdMap, mdShopOrderSettingList,commodityIds);

            //设置商品默认仓库信息
            setCommodityDefaultWarehouse(mdShopOrderSettingList,commodityIds);

            //批量记录导入日志
            insertMdShopOrderSettingLog(mdShopOrderSettingList);

            //批量插入门店订货通用设置
            mdShopOrderSettingMapper.insertList(mdShopOrderSettingList);

        }
        return list.size();
    }
    //设置商品默认仓库信息
    private  void setCommodityDefaultWarehouse(List<MdShopOrderSetting> mdShopOrderSettingList,List<Long> commodityIds){
        if(!CollectionUtils.isEmpty(commodityIds)){
            Map<Long, CommodityWarehouseODto> warehouseMap = commodityWarehouseClient.queryCommodityDefaultWarehouse(commodityIds);
            for(MdShopOrderSetting vo:mdShopOrderSettingList){
                CommodityWarehouseODto commodityWarehouseODto = warehouseMap.get(vo.getCommodityId());
                if(null != commodityWarehouseODto){
                    vo.setWarehouseId(commodityWarehouseODto.getWarehouseId());
                    vo.setDefaultWarehouseBeginTime(commodityWarehouseODto.getWorkBeginTime());
                    vo.setDefaultWarehouseEndTime(commodityWarehouseODto.getWorkEndTime());
                }
            }
        }

    }
    //数据库层面校验
    private void validDBRepeat(List<MdShopOrderSettingVo> list, Map<String, CommodityEntry> commodityEntryMap, Map<String, MdShopOrderSettingEntry> mdMap, List<MdShopOrderSetting> mdShopOrderSettingList,List<Long> commodityIds) {
        for(int i = 0;i < list.size();i++){
            MdShopOrderSettingVo vo = list.get(i);
            MdShopOrderSetting mdShopOrderSetting = new MdShopOrderSetting();
            BeanUtils.copyProperties(vo, mdShopOrderSetting);

            CommodityEntry commodityEntry = commodityEntryMap.get(vo.getCommodityCode());
            if(commodityEntry == null){
                QYAssert.isTrue(false, "导入失败,第"+(i+2)+"行商品编码不存在");
            }else{
                commodityIds.add(commodityEntry.getCommodityId());
                mdShopOrderSetting.setCommodityId(commodityEntry.getCommodityId());
                mdShopOrderSetting.setCommodityCode(commodityEntry.getCommodityCode());
                mdShopOrderSetting.setCommodityName(commodityEntry.getCommodityName());
                mdShopOrderSetting.setCommoditySpec(commodityEntry.getCommoditySpec());
            }

            SupplierCommodityListODTO supplierProductEntry = commoditySupplierClient.searchSupplierByName(vo.getSupplierName());
            if(supplierProductEntry == null){
                QYAssert.isTrue(false, "导入失败,第"+(i+2)+"行供应商名称不存在");
            }else{
                mdShopOrderSetting.setSupplierId(supplierProductEntry.getId());
                mdShopOrderSetting.setSupplierCode(supplierProductEntry.getSupplierCode());
                mdShopOrderSetting.setSupplierName(supplierProductEntry.getSupplierName());
                mdShopOrderSetting.setDefaultSupplierBeginTime(supplierProductEntry.getSupplyStartTime());
                mdShopOrderSetting.setDefaultSupplierEndTime(supplierProductEntry.getSupplyEndTime());
            }

            String mdKey = mdShopOrderSetting.getShopType() + "" + mdShopOrderSetting.getCommodityId() + "" + mdShopOrderSetting.getSupplierId();
            if(mdMap.get(mdKey) != null){
                QYAssert.isTrue(false, "导入失败,第"+(i+2)+"行与系统设置重复");
            }

            //判断是否有一条公共的数据
            List<MdShopOrderSettingEntry> commList  = mdShopOrderSettingMapper.queryCommonSettingByParam(mdShopOrderSetting.getCommodityId(), mdShopOrderSetting.getSupplierId());
            if(CollectionUtils.isEmpty(commList)){
                QYAssert.isTrue(false, "导入失败,第"+(i+2)+"行只能导入默认供应商的相关配置");
            }else{
                MdShopOrderSettingEntry mdShopOrderSettingEntry = commList.get(0);
                mdShopOrderSetting.setIsNew(mdShopOrderSettingEntry.getIsNew());
            }
            mdShopOrderSettingList.add(mdShopOrderSetting);
        }
    }

    /**
     * 物流配送模式0=直送，1＝配送，2＝直通'
     * 是否可变价：0不可变价  1可变价
     * @param list
     */
    //校验excel内数据重复
    public void valid(List<MdShopOrderSettingVo> list, List<String> commodityCodeList){
        //判断送货日期范围数据字典是否存在
        Map<String, String> dictionaryMap = getDeleveryTimeRangeMap();

        Map<String,String> dataMap=new HashMap<String,String>();
        for(int i = 0;i < list.size();i++){
            MdShopOrderSettingVo vo = list.get(i);
            if("全部".equals(vo.getShopTypeName())){
                vo.setShopType(-1);
            }else {
                Integer shopType = ShopTypeEnums.getCode(vo.getShopTypeName());
                QYAssert.isTrue(shopType != null,"门店类型不正确");
                vo.setShopType(shopType);
            }

            String key = vo.getCommodityCode() + vo.getSupplierName() + vo.getShopType();
            if(dataMap != null && dataMap.get(key) != null){
                QYAssert.isTrue(false, "导入失败,第"+(i+2)+"行模板内容有重复");
            }
            dataMap.put(key, key);

            if(dictionaryMap.get(vo.getDeleveryTimeRange())==null){
                QYAssert.isTrue(false, "导入失败,第"+(i+2)+"行送货日期范围不存在");
            }
            String logisticsModelName=vo.getLogisticsModelName();
            if(IogisticsModelEnums.DISPATCHING.getName().equals(logisticsModelName)){
                vo.setLogisticsModel(IogisticsModelEnums.DISPATCHING.getCode());
            }else if(IogisticsModelEnums.DIRECT_SENDING.getName().equals(logisticsModelName)){
                vo.setLogisticsModel(IogisticsModelEnums.DIRECT_SENDING.getCode());
            }else if(IogisticsModelEnums.DIRECT_CONNECTION.getName().equals(logisticsModelName)){
                vo.setLogisticsModel(IogisticsModelEnums.DIRECT_CONNECTION.getCode());
            }else{
                QYAssert.isTrue(false, "导入失败,第"+(i+2)+"行物流模式不存在");
            }
            String changePriceStatusName=vo.getChangePriceStatusName();
            if(YesOrNoEnums.YES.getName().equals(changePriceStatusName)){
                vo.setChangePriceStatus(YesOrNoEnums.YES.getCode());
            }else if(YesOrNoEnums.NO.getName().equals(changePriceStatusName)){
                vo.setChangePriceStatus(YesOrNoEnums.NO.getCode());
            }else{
                QYAssert.isTrue(false, "导入失败,第"+(i+2)+"行是否可变价请输入是或者否");
            }
            //直送模式不允许可变价
            boolean flag=( vo.getLogisticsModel().equals(IogisticsModelEnums.DIRECT_SENDING.getCode()) && vo.getChangePriceStatus().equals(YesOrNoEnums.YES.getCode()) );
            if(flag){
                QYAssert.isTrue(false, "导入失败,第"+(i+2)+"行直送模式不允许可变价");
            }
            commodityCodeList.add(vo.getCommodityCode());
        }
    }

    /**
     * 实时检查，判断商品默认供应商是否和设置里面一致
     * @param commodityIds
     * @param storeId
     * @return
     */
    public List<MdShopOrderSettingEntry> queryMdShopOrderSettingListByCommodityIds(List<String> commodityIds, Long storeId){
        Integer shopType = shopService.getShopType(storeId);
        List<CommoditySupplierODto> supplierListODTOS = commoditySupplierClient.getCommodityDefaultSupplierList2(commodityIds);
        Map<String, String> map = new HashMap<>();
        for (CommoditySupplierODto supplierListODTO : supplierListODTOS) {
            map.put(supplierListODTO.getCommodityId().toString(), supplierListODTO.getSupplierId().toString());
        }

        return mdShopOrderSettingMapper.queryMdShopOrderSettingListByMap(shopType, map);
    }

    /**
     * 默认供应商发生变化后，更新门店订货通用设置默认供应商
     */
    @Async
    public Boolean updateShopOrderSettingSupplier(Long commodityId, Long storeId){
        Long supplierId = commoditySupplierClient.getDefaultSupplierId(commodityId);
        Integer shopType = shopService.getShopType(storeId);
        try{
            SupplierODTO supplier = supplierClient.getSupplierById(supplierId);
            MdShopOrderSetting update = new MdShopOrderSetting();
            update.setSupplierId(supplier.getId());
            update.setSupplierCode(supplier.getSupplierCode());
            update.setSupplierName(supplier.getSupplierName());
            update.setDefaultSupplierBeginTime(supplier.getSupplyStartTime());
            update.setDefaultSupplierEndTime(supplier.getSupplyEndTime());
            update.setUpdateId(1L);
            update.setUpdateTime(new Date());
            Example updateExample = new Example(MdShopOrderSetting.class);
            updateExample.createCriteria().andEqualTo("commodityId", commodityId)
                    .andIn("shopType", Arrays.asList(shopType, -1))
                    .andEqualTo("isNew", YesOrNoEnums.YES.getCode());
            mdShopOrderSettingMapper.updateByExampleSelective(update, updateExample);

        }catch (Exception e){
            StringBuffer sb = new StringBuffer("商品:" + commodityId + "默认供应商:");
            sb.append(supplierId == null ? "无": supplierId);
            sb.append(" ,默认供应商发生变化,更新通用设置异常");
            log.warn(sb.toString());
            weChatSendMessageService.sendWeChatMessage(sb.toString());
        }

        return Boolean.TRUE;
    }

    /**
     * 仓库为空后，更新仓库信息
     */
    @Async
    public Boolean updateShopOrderSettingWarehouse(Long commodityId){
        try{
            Long wareHouseId = commodityWarehouseClient.getDefaultWarehouseId(commodityId);
            //获取仓库信息
            List<Long> warehouseIds = new ArrayList<>();
            warehouseIds.add(wareHouseId);
            Map<Long, WarehouseODto> warehouseMap = warehouseClient.queryWarehouseListByIds(warehouseIds);
            WarehouseODto warehouseODto = warehouseMap.get(wareHouseId);

            MdShopOrderSetting updatemos = new MdShopOrderSetting();
            updatemos.setWarehouseId(wareHouseId);
            updatemos.setDefaultWarehouseBeginTime(warehouseODto.getWorkBeginTime());
            updatemos.setDefaultWarehouseEndTime(warehouseODto.getWorkEndTime());
            updatemos.setUpdateId(1L);
            updatemos.setUpdateTime(new Date());
            Example updateExample = new Example(MdShopOrderSetting.class);
            updateExample.createCriteria().andEqualTo("commodityId",commodityId);
            mdShopOrderSettingMapper.updateByExampleSelective(updatemos, updateExample);

        }catch (Exception e){
            log.warn("商品:" + commodityId + " 仓库为空后,更新仓库信息异常");
            StringBuffer sb = new StringBuffer();
            sb.append("商品:" + commodityId + " 仓库为空后,更新仓库信息异常");
            weChatSendMessageService.sendWeChatMessage(sb.toString());
        }
        return Boolean.TRUE;
    }


    /**
     * 分页查询日日鲜自动订货供应商黑名单
     * @param vo
     * @return
     */
    public PageInfo<SupplierBlackODTO> queryOrderSupplierBlackPage(SupplierBlackVo vo) {
        PageInfo<SupplierBlackODTO> pageDate = null ;
        List<SupplierBlackODTO> list = mdShopOrderSettingMapper.queryOrderSupplierBlackPage(vo);
        if(!CollectionUtils.isEmpty(list)){
            renderService.render(list, "/mdShopOrderSetting/queryOrderSupplierBlackPage");

            list = list.stream().sorted(Comparator.comparing(SupplierBlackODTO::getSupplierCode)).collect(Collectors.toList());
        }
        pageDate = ListToPageInfoUtil.convert(list, vo.getPageSize(), vo.getPageNo());
        return pageDate;
    }

    /**
     * 查询所有日日鲜自动订货供应商黑名单
     * @return
     */
    public List<Long> queryAllOrderSupplierBlackList() {
        return mdShopOrderSettingMapper.queryAllOrderSupplierBlackList();
    }

    /**
     * 添加日日鲜自动订货供应商黑名单
     * @param supplierId
     * @return
     */
    public boolean addOrderSupplierBlack(Long supplierId, Long userId, String userName) {
        SupplierODTO supplierODTO = supplierClient.getSupplierById(supplierId);
        QYAssert.isTrue(supplierODTO != null, "供应商不存在");

        // 判重
        Long count = mdShopOrderSettingMapper.countOrderSupplierBlack(supplierId);
        QYAssert.isTrue(count == 0, "请不要重复添加");

        mdShopOrderSettingMapper.insertOrderSupplierBlack(supplierId, userId);

        SupplierBlackODTO log = new SupplierBlackODTO();
        log.setSupplierId(supplierODTO.getId() + "");
        log.setSupplierCode(supplierODTO.getSupplierCode());
        log.setSupplierName(supplierODTO.getSupplierName());
        //1-添加,2-删除
        log.setType(OperateTypeEnums.新增.getCode());
        log.setCreateId(userId);
        log.setCreateName(userName);
        log.setCreateTime(DateUtil.get4yMdHms(new Date()));
        sendLogService.sendLog(Collections.singletonList(log), "t_log_order_supplier_blacklist");
        return Boolean.TRUE;
    }

    /**
     * 删除日日鲜自动订货供应商黑名单
     * @param supplierId
     * @return
     */
    public boolean deleteOrderSupplierBlack(Long supplierId, Long userId, String userName) {
        SupplierODTO supplierODTO = supplierClient.getSupplierById(supplierId);
        QYAssert.isTrue(supplierODTO != null, "供应商不存在");

        mdShopOrderSettingMapper.deleteOrderSupplierBlack(supplierId);

        SupplierBlackODTO log = new SupplierBlackODTO();
        log.setSupplierId(supplierODTO.getId() + "");
        log.setSupplierCode(supplierODTO.getSupplierCode());
        log.setSupplierName(supplierODTO.getSupplierName());
        //1-添加,2-删除
        log.setType(OperateTypeEnums.删除.getCode());
        log.setCreateId(userId);
        log.setCreateName(userName);
        log.setCreateTime(DateUtil.get4yMdHms(new Date()));
        sendLogService.sendLog(Collections.singletonList(log), "t_log_order_supplier_blacklist");
        return Boolean.TRUE;
    }
}
