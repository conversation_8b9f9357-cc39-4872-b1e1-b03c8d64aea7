package com.pinshang.qingyun.order.manage.delivery.setting;

import lombok.Data;

@Data
public class StoreDeliverySetting {

    private Boolean isPrePayStoreAccount;

    //结算类型
    private Integer settleBillReturnAmountState;

    //结算时处理短交
    private Integer settleBillCalcType;

    private Boolean isXsjm;

    /**
     * 客户类型1-内部，2-外部
     */
    private Integer storeType;

//    private Integer orderType;

    private Long storeTypeId;

    private String storeTypeCode;


}
