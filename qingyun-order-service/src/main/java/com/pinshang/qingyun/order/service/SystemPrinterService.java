package com.pinshang.qingyun.order.service;

import com.pinshang.qingyun.order.mapper.SystemPrinterMapper;
import com.pinshang.qingyun.order.mapper.entry.EmployeePrinterEntry;
import com.pinshang.qingyun.order.mapper.entry.StorePrinterEntry;
import com.pinshang.qingyun.order.mapper.entry.SystemPrinterEntry;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class SystemPrinterService {
	@Autowired
	private SystemPrinterMapper systemPrinterMapper;

	public List<SystemPrinterEntry> querySystemPrinterByUserId(Long userId){
		return  systemPrinterMapper.querySystemPrinterByUserId(userId);
	}

	public EmployeePrinterEntry queryEmployeePrinterByDeliveryManCode(String employeeCode){
		return  systemPrinterMapper.queryEmployeePrinterByDeliveryManCode(employeeCode);
	}

	public List<StorePrinterEntry> queryStorePrinterByStoreCode(String storeCode){
		return  systemPrinterMapper.queryStorePrinterByStoreCode(storeCode);
	}
}
