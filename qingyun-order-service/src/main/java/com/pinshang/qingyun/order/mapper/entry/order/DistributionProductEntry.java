package com.pinshang.qingyun.order.mapper.entry.order;

import java.math.BigDecimal;

public class DistributionProductEntry {
	
	private String productId;
	
	private BigDecimal commodityPrice;
	
	private String productCode;

	public String getProductId() {
		return productId;
	}

	public void setProductId(String productId) {
		this.productId = productId;
	}

	public BigDecimal getCommodityPrice() {
		return commodityPrice;
	}

	public void setCommodityPrice(BigDecimal commodityPrice) {
		this.commodityPrice = commodityPrice;
	}

	public String getProductCode() {
		return productCode;
	}

	public void setProductCode(String productCode) {
		this.productCode = productCode;
	}
}
