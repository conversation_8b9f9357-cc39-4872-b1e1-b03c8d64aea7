package com.pinshang.qingyun.order.mapper;

import com.pinshang.qingyun.order.mapper.entry.government.FactoryRespEntry;
import com.pinshang.qingyun.order.mapper.entry.government.GovernmentCommdityRespEntry;
import com.pinshang.qingyun.order.mapper.entry.government.GovernmentOrderTraceBackRespEntry;
import com.pinshang.qingyun.order.mapper.entry.government.GovernmentStoreRespEntry;
import com.pinshang.qingyun.order.vo.government.GovernmentOrderTraceBackRepVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/2/4 14:39
 */
@Repository
@Mapper
public interface GovernmentOrderTraceBackMapper {

    List<GovernmentOrderTraceBackRespEntry> selectGovernmentOrderTraceBackList(GovernmentOrderTraceBackRepVO vo);

    List<GovernmentStoreRespEntry> selectGovernmentStoreByStoreId(@Param("storeIds") List<Long> storeIds);

    List<FactoryRespEntry> selectFactoryById(@Param("factoryIds") List<Long> factoryIds);

    List<GovernmentCommdityRespEntry> selectGovernmentCommodityList(@Param("factoryId") Long factoryId);
}
