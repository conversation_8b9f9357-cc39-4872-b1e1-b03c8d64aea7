package com.pinshang.qingyun.order.service.order;/**
 * @Author: sk
 * @Date: 2025/6/25
 */

import com.pinshang.qingyun.base.enums.YesOrNoEnums;
import com.pinshang.qingyun.order.constant.RedissKeyConstant;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RBucket;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date 2025年06月25日 上午10:40
 */
@Slf4j
@Service
public class OrderReferenceCheckService {

    @Autowired
    private RedissonClient redissonClient;

    private static final String ORDER_REFERENCE_ERROR_COUNT = "order:orderReferenceCheck:errorCount";
    private static final String ORDER_REFERENCE_ENABLE = "order:orderReferenceCheck:enable";


    /**
     * 订货参考调用大数据报错次数统计
     */
    public void errorCount(){
        RBucket<Integer> countBucket = redissonClient.getBucket(ORDER_REFERENCE_ERROR_COUNT);
        Integer errorCount  = countBucket.get();
        if(errorCount == null) {
            errorCount = 1;
        }else {
            errorCount = errorCount + 1;
        }
        countBucket.set(errorCount, 1, TimeUnit.MINUTES);

        // 1分钟内报错次数大于2次，则放入redisson30分钟,30分钟内不再调用大数据
        if(errorCount > 2) {
            RBucket<Integer> enableBucket = redissonClient.getBucket(ORDER_REFERENCE_ENABLE);
            enableBucket.set(YesOrNoEnums.YES.getCode(), 30, TimeUnit.MINUTES);
        }
    }

    /**
     * 校验大数据服务是否异常：1分钟内报错2次，30分钟内不再调用
     *   1.门店订货列表、购物车、提交订单调用大数据
     *   2.日日鲜订货参考调用大数据
     * @return
     */
    public Boolean enableOrderReference(){
        RBucket<Integer> enableBucket = redissonClient.getBucket(ORDER_REFERENCE_ENABLE);
        Integer enable  = enableBucket.get();
        if(enable == null){
            return true;
        }else {
            return false;
        }
    }
}
