package com.pinshang.qingyun.order.service.pay.model.log;

import com.pinshang.qingyun.base.enums.PayLogTypeEnum;
import com.pinshang.qingyun.base.enums.PayOperateTypeEnum;
import com.pinshang.qingyun.order.service.pay.callback.PfThirdPartyPayCallbackEvent;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @Date 2019/11/24 16:22
 */
@Data
@NoArgsConstructor
public class PfThirdPartyPayLogEvent {
    private String orderCode;
    private PayLogTypeEnum logType;
    private PayOperateTypeEnum operateType;
    /**
     * 请求/响应报文json
     */
    private String json;

    public PfThirdPartyPayLogEvent(PfThirdPartyPayCallbackEvent event){
        this.json = event.getJson();
        this.logType = PayLogTypeEnum.RESPONSE;
        this.operateType = PayOperateTypeEnum.PAY;
        this.orderCode = event.getOrderCode();
    }
}
