package com.pinshang.qingyun.order.service.syncOrderJob;

import com.pinshang.qingyun.base.constant.QingyunConstant;
import com.pinshang.qingyun.box.utils.DateTimeUtil;
import com.pinshang.qingyun.box.utils.SpringUtil;
import com.pinshang.qingyun.order.mapper.DeliveryTimeMapper;
import com.pinshang.qingyun.order.model.order.DeliveryTime;
import com.pinshang.qingyun.order.util.RedissonLockExecutor;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.Executor;


/**
 * 订单覆盖 service
 *
 * <AUTHOR>
 */
@Service
public class SyncOrderJobService {
    private final static String SYNC_ORDER_JOB_COVER_REDIS_PREFIX = "sync_order_job_cover_redis_prefix";
    @Autowired
    private DeliveryTimeMapper deliveryTimeMapper;
    @Autowired
    private SyncOrderService syncOrderService;
    @Autowired
    private RedissonLockExecutor redissonLockExecutor;
    @Autowired
    @Qualifier(value = "orderThreadPoolExecutor")
    private Executor threadPoolExecutor;

    /**
     * 执行订单覆盖逻辑
     *
     * @param coverTime 覆盖时间
     */
    public void execute(String coverTime) {
        // 获取符合条件的线路组列表
        List<DeliveryTime> deliveryTimes = getDeliveryTimeList(coverTime);
        if (CollectionUtils.isEmpty(deliveryTimes)) {
            return;
        }

        // 并行处理每个线路组的覆盖逻辑
        deliveryTimes.forEach(deliveryTime ->
                threadPoolExecutor.execute(() -> {
                    String lockKey = SYNC_ORDER_JOB_COVER_REDIS_PREFIX + deliveryTime.getLineGroupId();
                    // 覆盖订单
                    redissonLockExecutor.exec(lockKey, 10, 60, () -> syncOrderService.rewriteOrderData(deliveryTime));
                })
        );

    }

    /**
     * 获取线路组列表
     * 根据当前时间倒退15分钟，获取区间作为条件
     * 1. 如果参数 coverTime 有值，则只查某个覆盖时间的线路组
     * 2. 如果未传值，需要考虑当前时间是否是 00:03：
     * - 查询区间为 [23:48 -- 00:03] 时，需拆分为 [23:48 -- 23:59] 和 [00:00 -- 00:03] 两个区间条件
     * 3. 非 00:03 时间可直接使用区间，例如 20:33 --> [20:18 -- 20:33]
     *
     * @param coverTime 覆盖时间
     * @return 符合条件的线路组列表
     */
    public List<DeliveryTime> getDeliveryTimeList(String coverTime) {
        // 如果 coverTime 不为空，直接根据覆盖时间查询
        if (StringUtils.isNotBlank(coverTime)) {
            String formattedTime = DateTimeUtil.formatHour(coverTime);
            Example example = new Example(DeliveryTime.class);
            example.createCriteria()
                    .andEqualTo("status", 0)
                    .andEqualTo("coverTime", formattedTime);
            return deliveryTimeMapper.selectByExample(example);
        }

        // 获取查询时间区间（当前时间前15分钟 至 当前时间）
        String beginTime = DateTimeUtil.getHourAndMinute(QingyunConstant.SYNC_ORDER_JOB_MINUTE);
        String endTime = DateTimeUtil.getHourAndMinute(0);
        String beginHour = beginTime.split(":")[0];
        String endHour = endTime.split(":")[0];

        List<DeliveryTime> deliveryTimeList = new ArrayList<>();

        // 如果跨越日期（23:48 -- 00:03）
        if ("23".equals(beginHour) && "00".equals(endHour)) {
            List<DeliveryTime> deliveryTimes = queryDeliveryTimes(beginTime, "23:59");
            if (SpringUtil.isNotEmpty(deliveryTimes)) {
                deliveryTimes.forEach(deliveryTime -> deliveryTime.setCoverFlag(true));
            }
            deliveryTimeList.addAll(deliveryTimes);
            deliveryTimeList.addAll(queryDeliveryTimes("00:00", endTime));
        } else {
            deliveryTimeList.addAll(queryDeliveryTimes(beginTime, endTime));
        }

        return deliveryTimeList;
    }

    /**
     * 根据时间区间查询 DeliveryTime 列表
     *
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return 符合条件的 DeliveryTime 列表
     */
    private List<DeliveryTime> queryDeliveryTimes(String startTime, String endTime) {
        Example example = new Example(DeliveryTime.class);
        example.createCriteria()
                .andEqualTo("status", 0)
                .andBetween("coverTime", startTime, endTime);

        List<DeliveryTime> deliveryTimes = deliveryTimeMapper.selectByExample(example);
        return CollectionUtils.isNotEmpty(deliveryTimes) ? deliveryTimes : new ArrayList<>();
    }

}
