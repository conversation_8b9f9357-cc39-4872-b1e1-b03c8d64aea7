package com.pinshang.qingyun.order.service;

import com.pinshang.qingyun.base.enums.YesOrNoEnums;
import com.pinshang.qingyun.base.enums.groupon.CloudGrouponTypeEnum;
import com.pinshang.qingyun.base.enums.order.OrderTypeEnum;
import com.pinshang.qingyun.base.enums.storage.StockTypeEnum;
import com.pinshang.qingyun.base.enums.storage.ToBTypeEnums;
import com.pinshang.qingyun.box.utils.BeanCloneUtils;
import com.pinshang.qingyun.box.utils.DateTimeUtil;
import com.pinshang.qingyun.box.utils.DateUtil;
import com.pinshang.qingyun.box.utils.JsonUtil;
import com.pinshang.qingyun.order.dto.GrouponCommodtyOrderDTO;
import com.pinshang.qingyun.order.enums.GrouponTypeEnum;
import com.pinshang.qingyun.order.mapper.groupon.GrouponOrderLogMapper;
import com.pinshang.qingyun.order.model.order.Order;
import com.pinshang.qingyun.order.model.shop.Shop;
import com.pinshang.qingyun.order.util.StallUtils;
import com.pinshang.qingyun.order.util.ThreadLocalUtils;
import com.pinshang.qingyun.order.vo.order.OrderDto;
import com.pinshang.qingyun.storage.dto.tob.COrderToBOrderDealInventoryIDTO;
import com.pinshang.qingyun.storage.dto.tob.CommodityInventoryODTO;
import com.pinshang.qingyun.xd.order.dto.CloudGrouponCommodityIDTO;
import com.pinshang.qingyun.xd.order.dto.CloudGrouponCommodityODTO;
import com.pinshang.qingyun.xd.order.service.XdOrderClient;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Author: sk
 * @Date: 2023/9/1
 */
@Service
@Slf4j
public class CloudGrouponOrderService {

    @Autowired
    private XdOrderClient xdOrderClient;
    @Autowired
    private GrouponOrderSaveService grouponOrderSaveService;
    @Autowired
    private GrouponOrderLogMapper grouponOrderLogMapper;
    @Autowired
    private WeChatSendMessageService weChatSendMessageService;
    @Autowired
    private OrderSplitService orderSplitService;
    @Lazy
    @Autowired
    private BStockService bStockService;
    @Autowired
    private ShopService shopService;

    /**
     * 云超团购提交订单
     * @return
     */
    @Async
    @Transactional(rollbackFor = Exception.class)
    public Boolean createCloudGrouponOrder(String timeStamp) {
        // 刷过了截单时间的 云超团购配送单  待拣货 改为 出库中
        xdOrderClient.batchUpdateCloudOrderOutStock();

        String beginTime = timeStamp + " 00:00:00";
        String endTime = timeStamp + " 23:59:59";

        // 首先查询已经提交订单的 groupIdList
        List<Long> dbGroupIdList = grouponOrderLogMapper.queryGrouponOrderLog(beginTime,endTime, GrouponTypeEnum.CLOUD.getCode());

        // 获取团购信息
        CloudGrouponCommodityIDTO groupIDTO = new CloudGrouponCommodityIDTO();
        groupIDTO.setGrouponIdList(dbGroupIdList);
        groupIDTO.setBeginTime(beginTime);
        groupIDTO.setEndTime(endTime);
        List<CloudGrouponCommodityODTO> grouponCommodityODTOList = xdOrderClient.queryCloudGrouponCommodityList(groupIDTO);


        if(CollectionUtils.isEmpty(grouponCommodityODTOList)){
            return false;
        }

        // 云超团购过滤大店
        Set<Long> shopIdList = grouponCommodityODTOList.stream().map(item -> item.getShopId()).collect(Collectors.toSet());
        List<Shop> shopList = shopService.getShopByIdList(new ArrayList<>(shopIdList));
        Map<Long, Long> bigShopIdMap = new HashMap<>(shopList.size());
        shopList.forEach(item -> {
            if(StallUtils.isStallSubcontractor(item.getManagementMode())) {
                bigShopIdMap.put(item.getId(), item.getId());
            }
        });

        grouponCommodityODTOList = grouponCommodityODTOList.stream().filter(p -> !bigShopIdMap.containsKey(p.getShopId())).collect(Collectors.toList());
        if(CollectionUtils.isEmpty(grouponCommodityODTOList)){
            return false;
        }


        List<GrouponCommodtyOrderDTO> groupCommodityList = BeanCloneUtils.copyTo(grouponCommodityODTOList,GrouponCommodtyOrderDTO.class);

        // 设置storeId、物流模式、供应商、仓库
        orderSplitService.setGroupOrderSetting(groupCommodityList);


        // B端库存依据,拆单使用
        Map<Long, BigDecimal> orderQuantityGiftMap = new HashMap<>();
        groupCommodityList.forEach(dto ->{
            orderQuantityGiftMap.put(dto.getCommodityId(), dto.getQuantity());
        });
        Map<Long, CommodityInventoryODTO> getbStockMap = bStockService.getbStockMap(DateUtil.parseDate(DateTimeUtil.defaultDeliveryDate(), "yyyy-MM-dd"), orderQuantityGiftMap);
        groupCommodityList.forEach(dto ->{
            dto.setPresaleStatus(CloudGrouponTypeEnum.PRESALE.getCode() == dto.getGroupType() ? YesOrNoEnums.YES.getCode() : YesOrNoEnums.NO.getCode());
            CommodityInventoryODTO commodityInventoryODTO = getbStockMap.get(dto.getCommodityId());
            dto.setStockType(commodityInventoryODTO != null ? commodityInventoryODTO.getStockType() : StockTypeEnum.UN_LIMIT.getCode());
            if (Objects.nonNull(commodityInventoryODTO)) {
                Integer sourceRatio = commodityInventoryODTO.getSourceRatio();
                dto.setSourceRatio(sourceRatio);
                Integer targetRatio = commodityInventoryODTO.getTargetRatio();
                dto.setTargetRatio(targetRatio);
                Long targetCommodityId = commodityInventoryODTO.getTargetCommodityId();
                dto.setTargetCommodityId(targetCommodityId);
                Integer convertStatus = commodityInventoryODTO.getCanConvert() ? 1 : 0;
                dto.setConvertStatus(convertStatus);
                BigDecimal quantity = dto.getQuantity();
                // 组合商品目标数量 : 向上取整 ( quantity / source_ratio * target_ratio)
                if (commodityInventoryODTO.getCanConvert()) {
                    BigDecimal targetQuantity = quantity.divide(new BigDecimal(sourceRatio), 0, RoundingMode.UP).multiply(new BigDecimal(targetRatio));
                    dto.setTargetQuantity(targetQuantity);
                }
            }
            dto.setOriginOrderId(dto.getOrderId());
            dto.setOriginCommodityId(dto.getCommodityId());
        });

        // 转成OrderDto对象
        List<OrderDto> orderDtoList = orderSplitService.convertToOrderDto(groupCommodityList, "云超团购" , true);

        // 记录团购id 日志
        List<Long> insertGroupIdList = groupCommodityList.stream().map(item -> item.getGrouponId()).collect(Collectors.toList());
        orderSplitService.insertGrouponOrderLog(insertGroupIdList, GrouponTypeEnum.CLOUD.getCode());


        ThreadLocalUtils.setGroupOrder(true);
        ThreadLocalUtils.setOrderType(OrderTypeEnum.CLOUD_GROUP_AUTO_ORDER.getCode());
        // 循环创建订单
        for(OrderDto orderDto : orderDtoList){
            // 保存团购订单
            try{
                Order order = grouponOrderSaveService.saveGroupOrder(orderDto, null);

                COrderToBOrderDealInventoryIDTO cOrderToBOrder = COrderToBOrderDealInventoryIDTO.builder()
                        .oriderIdMap(orderDto.getOriginOrderMap())
                        .orderType(ToBTypeEnums.CLOUD.getCode())
                        .orderId(order.getId())
                        .orderCode(order.getOrderCode())
                        .orderTime(order.getOrderTime())
                        .type(ToBTypeEnums.SALE.getCode())
                        .sourceType(OrderTypeEnum.CLOUD_GROUP_AUTO_ORDER.getCode())
                        .deliveryBatch(order.getDeliveryBatch())
                        .storeId(order.getStoreId())
                        .storeCode(null)
                        .storeName(null)
                        .build();
                bStockService.cOrderToBOrderDealInventory(cOrderToBOrder);


            }catch (Throwable e){
                log.error("保存云超团购订单异常:" + JsonUtil.java2json(orderDto) + e.getMessage());
                // 异常发送微信消息
                weChatSendMessageService.sendWeChatMessage("保存云超团购订单异常");
            }
        }
        ThreadLocalUtils.remove();

        return true;
    }


}
