package com.pinshang.qingyun.order.service;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.pinshang.qingyun.base.api.QYApplicationContext;
import com.pinshang.qingyun.base.enums.OperateTypeEnums;
import com.pinshang.qingyun.infrastructure.components.IMqSenderComponent;
import com.pinshang.qingyun.infrastructure.components.params.mq.MqMessage;
import com.pinshang.qingyun.kafka.base.KafkaMessageWrapper;
import com.pinshang.qingyun.kafka.base.KafkaTopicConstant;
import com.pinshang.qingyun.kafka.enums.KafkaMessageOperationTypeEnum;
import com.pinshang.qingyun.kafka.enums.KafkaMessageTypeEnum;
import com.pinshang.qingyun.order.constant.RedissKeyConstant;
import com.pinshang.qingyun.order.model.order.Order;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RBucket;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@Component
@Slf4j
public class OrderAsyncKafkaService {

    @Lazy
    @Autowired
    private OrderShelvesRecommendService orderShelvesRecommendService;

    @Autowired
    private IMqSenderComponent mqSenderComponent;

    @Autowired
    private RedissonClient redissonClient;

    @Value("${application.name.switch}")
    private String applicationNameSwitch;

    public void sendKafkaSaveOrderMessage(Order kafkaOrder) {
        if( kafkaOrder == null ){
            log.info("直送订单，不拆单，不需要同步到《统计查询系统》和《大仓系统》");
            return ;
        }
        String topic = applicationNameSwitch + KafkaTopicConstant.STATISTICAL_ORDER_TOPIC;
        KafkaMessageWrapper wrapper = new KafkaMessageWrapper(KafkaMessageTypeEnum.ORDER, kafkaOrder, KafkaMessageOperationTypeEnum.INSERT);
        ObjectMapper mapper = new ObjectMapper();
        //String jsonStr;
        try {
            //jsonStr = mapper.writeValueAsString(wrapper);
            mqSenderComponent.sendByKey(topic,
                    kafkaOrder,
                    MqMessage.MQ_KAFKA,
                    KafkaMessageTypeEnum.ORDER.name(),
                    KafkaMessageOperationTypeEnum.INSERT.name(), kafkaOrder.getId().toString());
        } catch (Exception e) {
            log.error("JSON转换时异常,{}", e);
        }

        //异步 维护在途数量
        orderShelvesRecommendService.insertOrUpdateShopOrderedQuantity(kafkaOrder.getOrderCode(), OperateTypeEnums.新增.getCode());
    }

    /**
     * 取消订单消息
     *
     * @param order 订单
     */
    public void sendKafkaCancelOrder(Order order) {
        order.setOrderStatus(2);
//        sendKafkaUpdateOrderMessage(order);
        String topic = applicationNameSwitch + KafkaTopicConstant.STATISTICAL_ORDER_TOPIC;
        KafkaMessageWrapper wrapper = new KafkaMessageWrapper(KafkaMessageTypeEnum.ORDER, order, KafkaMessageOperationTypeEnum.CANCEL);
        ObjectMapper mapper = new ObjectMapper();
        //String jsonStr;
        try {
            //jsonStr = mapper.writeValueAsString(wrapper);
            mqSenderComponent.sendByKey(topic,
                    order,
                    MqMessage.MQ_KAFKA,
                    KafkaMessageTypeEnum.ORDER.name(),
                    KafkaMessageOperationTypeEnum.CANCEL.name(), order.getId().toString());
        } catch (Exception e) {
            log.error("JSON转换时异常,{}", e);
        }

        //异步 维护在途数量
        orderShelvesRecommendService.insertOrUpdateShopOrderedQuantity(order.getOrderCode(), OperateTypeEnums.删除.getCode());
    }

    public void sendKafkaUpdateOrderMessage(Order order) {
        String topic = applicationNameSwitch + KafkaTopicConstant.STATISTICAL_ORDER_TOPIC;
        KafkaMessageWrapper wrapper = new KafkaMessageWrapper(KafkaMessageTypeEnum.ORDER, order, KafkaMessageOperationTypeEnum.UPDATE);
        ObjectMapper mapper = new ObjectMapper();
        //String jsonStr;
        try {
            //jsonStr = mapper.writeValueAsString(wrapper);
            mqSenderComponent.sendByKey(topic,
                    order,
                    MqMessage.MQ_KAFKA,
                    KafkaMessageTypeEnum.ORDER.name(),
                    KafkaMessageOperationTypeEnum.UPDATE.name(), order.getId().toString());
        } catch (Exception e) {
            log.error("JSON转换时异常,{}", e);
        }
    }

    public void sendKafkaAsyncOrderToDCMessage(Order order) {
        RBucket<Boolean> bucket = redissonClient.getBucket(QYApplicationContext.redisKeyProfile + RedissKeyConstant.TOB_ORDER_SYNC_DC_KAFKA);
        boolean flag = Boolean.FALSE;
        if (bucket.get() != null) {
            flag = bucket.get();
        }
        if (!flag) {
            return;
        }
        String topic = applicationNameSwitch + KafkaTopicConstant.DC_ASYNC_ORDER;
        try {
            mqSenderComponent.sendByKey(topic,
                    order,
                    MqMessage.MQ_KAFKA,
                    KafkaMessageTypeEnum.ORDER.name(),
                    KafkaMessageOperationTypeEnum.INSERT.name(), order.getId().toString());
        } catch (Exception e) {
            log.error("JSON转换时异常,{}", e);
        }
    }

}
