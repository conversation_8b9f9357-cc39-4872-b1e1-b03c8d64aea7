package com.pinshang.qingyun.order.controller;

import com.pinshang.qingyun.box.utils.SpringUtil;
import com.pinshang.qingyun.order.model.distribution.DistributionLine;
import com.pinshang.qingyun.order.service.StoreService;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2018/10/12 9:28.
 * @blog http://linuxsogood.org
 */
@RestController
@RequestMapping("/storeLine")
public class StoreLineController {

    private final StoreService storeService;

    public StoreLineController(StoreService storeService) {
        this.storeService = storeService;
    }

    /**
     * 根据客户ids查询客户对应的线路信息
     * @param storeIds 客户的id列表
     * @return 返回map, key为客户id, value为客户对应的线路
     */
    @RequestMapping(value = "queryStoreLineByStoreIds", method = RequestMethod.POST)
    public Map<Long, DistributionLine> queryStoreLineByStoreIds(@RequestBody List<Long> storeIds) {
        if (SpringUtil.isEmpty(storeIds)) {
            return null;
        } else {
            return storeService.queryStoreLineByStoreIds(storeIds);
        }
    }
}
