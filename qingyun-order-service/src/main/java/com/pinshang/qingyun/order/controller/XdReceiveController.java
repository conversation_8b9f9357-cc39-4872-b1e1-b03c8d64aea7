package com.pinshang.qingyun.order.controller;

import com.github.pagehelper.PageInfo;
import com.pinshang.qingyun.base.annotations.RepeatSubmitAnno;
import com.pinshang.qingyun.base.api.QYApplicationContext;
import com.pinshang.qingyun.base.api.TokenInfo;
import com.pinshang.qingyun.base.enums.RepeatSubmitBusinessCode;
import com.pinshang.qingyun.base.util.FastThreadLocalUtil;
import com.pinshang.qingyun.infrastructure.components.IMqSenderComponent;
import com.pinshang.qingyun.kafka.base.KafkaTopicConstant;
import com.pinshang.qingyun.kafka.enums.KafkaMessageOperationTypeEnum;
import com.pinshang.qingyun.kafka.enums.KafkaMessageTypeEnum;
import com.pinshang.qingyun.order.dto.*;
import com.pinshang.qingyun.order.service.SplitOrderService;
import com.pinshang.qingyun.order.service.SubOrderService;
import com.pinshang.qingyun.order.service.XdReceiveService;
import com.pinshang.qingyun.order.service.syncOrderJob.SyncOrderService;
import com.pinshang.qingyun.order.vo.order.PickSubOrderVo;
import com.pinshang.qingyun.renderer.annotation.MethodRender;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.collections.CollectionUtils;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * @Author: sk
 * @Date: 2020/3/17
 */
@RestController
@RequestMapping("/xdReceive")
@Api("鲜道收货管理")
public class XdReceiveController {

      @Autowired
      private XdReceiveService xdReceiveService;
      @Autowired
      private SplitOrderService splitOrderService;
      @Resource
      private RedissonClient redissonClient;
      @Autowired
      private SubOrderService subOrderService;
      @Autowired
      private IMqSenderComponent mqSenderComponent;
      @Autowired
      private SyncOrderService syncOrderService;

      /**
       * 创建收货单
       * @param timeStr
       * @return
       */
      @ApiOperation(value = "创建收货单", notes = "创建收货单", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
      @RequestMapping(value = "/createXdReceiveDoc", method = RequestMethod.POST)
      public Boolean createXdReceiveDoc(@RequestParam(value = "timeStr",required = false) String  timeStr){
            return  xdReceiveService.createXdReceiveDoc(timeStr);
      }


      /**
       * 查询收货单列表
       * @return
       */
      @ApiOperation(value = "查询收货单列表", notes = "查询收货单列表", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
      @RequestMapping(value = "/getXdReceiveDocList", method = RequestMethod.GET)
      public List<XdReceiveDocODTO> getXdReceiveDocList() {
            TokenInfo tokenInfo = FastThreadLocalUtil.getQY();
            List<XdReceiveDocODTO> list =  xdReceiveService.getXdReceiveDocList(tokenInfo.getShopId());
            return CollectionUtils.isNotEmpty(list) ? list : new ArrayList<>();
      }


      /**
       *查看收货情况(分页)
       * @return
       */
      @MethodRender
      @ApiOperation(value = "查看收货情况(分页)", notes = "查看收货情况(分页)", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
      @RequestMapping(value = "/getXdReceiveDocCommodityInfo", method = RequestMethod.POST)
      public PageInfo<XdReceiveDocCommodityODTO> getXdReceiveDocCommodityInfo(@RequestBody XdReceiveDocIDTO xdReceiveDocIDTO) {
            return  xdReceiveService.getXdReceiveDocCommodityInfo(xdReceiveDocIDTO);
      }

      /**
       *查看收货情况(不分页)
       * @return
       */
      @ApiOperation(value = "查看收货情况(不分页)", notes = "查看收货情况(不分页)", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
      @RequestMapping(value = "/getXdReceiveDocCommodityInfoList", method = RequestMethod.POST)
      public XdReceiveDocDetailODTO getXdReceiveDocCommodityInfoList(@RequestBody XdReceiveDocIDTO xdReceiveDocIDTO) {
            xdReceiveDocIDTO.setPageNo(1);
            xdReceiveDocIDTO.setPageSize(Integer.MAX_VALUE);
            XdReceiveDocDetailODTO docDetailODTO = xdReceiveService.getXdReceiveDocCommodityDetail(xdReceiveDocIDTO);
            return docDetailODTO;
      }

      /**
       * 点击收货按钮(查看收货主单信息)
       * @param docId
       * @return
       */
      @ApiOperation(value = "点击收货按钮", notes = "点击收货按钮", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
      @RequestMapping(value = "/getXdReceiveDocInfo", method = RequestMethod.GET)
      public XdReceiveDocODTO getXdReceiveDocInfo(@RequestParam(value = "docId",required = false) Long docId) {
            XdReceiveDocODTO odto = xdReceiveService.getXdReceiveDocInfo(docId);
            return  odto != null ? odto : new XdReceiveDocODTO();
      }

      /**
       * 根据商品条码精确查找
       * @param barcode
       * @param docId
       * @return
       */
      @MethodRender
      @ApiOperation(value = "根据商品条码精确查找", notes = "根据商品条码精确查找", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
      @RequestMapping(value = "/getXdReceiveCommodityByBarcode", method = RequestMethod.GET)
      public List<XdReceiveDocCommodityODTO> getXdReceiveCommodityByBarcode(@RequestParam(value = "barcode",required = false) String barcode, @RequestParam(value = "docId",required = false) Long docId) {
            List<XdReceiveDocCommodityODTO> list = xdReceiveService.getXdReceiveCommodityByBarcode(docId,barcode);
            return  CollectionUtils.isNotEmpty(list) ? list : new ArrayList<>();
      }


      /**
       * 收货操作(鲜道前置仓)
       * @param
       * @return
       * @throws Throwable
       */
      @ApiOperation(value = "收货操作(鲜道前置仓)", notes = "收货操作(鲜道前置仓)", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
      @RequestMapping(value = "/addXdReceive",method = RequestMethod.POST)
      @RepeatSubmitAnno(value = RepeatSubmitBusinessCode.SHOP_RECEIVE_ORDER,expireTime = 2 )
      public Boolean addXdReceive(@RequestBody List<XdReceiveDocCommodityIDTO> xdReceiveDocCommodityIDTOList) throws Throwable{
            TokenInfo tokenInfo = FastThreadLocalUtil.getQY();
            xdReceiveService.addXdReceive(xdReceiveDocCommodityIDTOList,tokenInfo.getUserId());
            return true;
      }




      @ApiOperation(value = "批量更新t_order、t_order_list、t_order_list_gift 实发数量,实发总金额", notes = "批量更新t_order、t_order_list、t_order_list_gift 实发数量,实发总金额", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
      @RequestMapping(value = "/batchUpdateOrderRealQuantity",method = RequestMethod.POST)
      public Boolean batchUpdateOrderRealQuantity(@RequestBody List<PickSubOrderVo> paramSubOrderList) throws Throwable{
            subOrderService.batchUpdateOrderRealQuantity(paramSubOrderList);
            return true;
      }

      @ApiOperation(value = "订单覆盖", notes = "订单覆盖", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
      @RequestMapping(value = "/syncOrderService",method = RequestMethod.POST)
      public Boolean syncOrderService(@RequestParam("orderId") Long orderId) throws Throwable{
            syncOrderService.syncOrderListGift(orderId);
            return true;
      }

      @ApiOperation(value = "测试发送实发回填消息", notes = "测试发送实发回填消息", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
      @RequestMapping(value = "/sendBatchUpdateOrderRealQuantityMsg",method = RequestMethod.POST)
      public Boolean sendBatchUpdateOrderRealQuantityMsg(@RequestBody List<PickSubOrderVo> paramSubOrderList) throws Throwable{
            mqSenderComponent.send(QYApplicationContext.applicationNameSwitch+ KafkaTopicConstant.PICK_UPDATE_SUB_ORDER_QUANTITY_TOPIC,
                    paramSubOrderList,mqSenderComponent.preferredMqService(),KafkaMessageTypeEnum.PICK_UPDATE_SUB_ORDER_QUANTITY.name(),KafkaMessageOperationTypeEnum.UPDATE.getCode());
            return true;
      }

      /*@Autowired
      private OrderMapper orderMapper;
      @ApiOperation(value = "测试拆单", notes = "测试拆单", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
      @RequestMapping(value = "/splitOrder",method = RequestMethod.POST)
      public Boolean splitOrder(@RequestParam("orderId") Long orderId) throws Throwable{
            Order order = orderMapper.selectByPrimaryKey(orderId);
            splitOrderService.createSubOrder(order ,1);
            return true;
      }*/
}
