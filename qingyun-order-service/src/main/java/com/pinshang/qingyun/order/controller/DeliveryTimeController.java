package com.pinshang.qingyun.order.controller;

import com.pinshang.qingyun.order.mapper.entry.DeliveryTimeEntry;
import com.pinshang.qingyun.order.model.order.DeliveryTime;
import com.pinshang.qingyun.order.service.DeliveryTimeService;
import com.pinshang.qingyun.order.vo.DeliveryTimeLineVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;


@RestController
@RequestMapping("/deliveryTime")
public class DeliveryTimeController {

    @Autowired
    private DeliveryTimeService deliveryTimeService;

    @RequestMapping(value = "/list",method = RequestMethod.GET)
    public List<DeliveryTimeEntry> deliveryTimeList(){
         return deliveryTimeService.deliveryTimeList();
    }

    /***
     * 根据线路组id 查询线路
     * @param deliveryTimeId
     * @return
     */
    @GetMapping(value = "/selectEndTimeById")
    public String selectEndTimeById(@RequestParam("deliveryTimeId") Long deliveryTimeId){
        return deliveryTimeService.selectEndTimeById(deliveryTimeId);
    }

    /***
     * 根据线路id集合获取线路截单时间
     * @param vo
     * @return
     */
    @PostMapping(value = "/selectEndTimeByLineIds")
    public String selectDeliveryTimeEndTimeByLineIds(@RequestBody DeliveryTimeLineVO vo){
        return deliveryTimeService.selectDeliveryTimeEndTimeByLineIds(vo);
    }
}
