package com.pinshang.qingyun.order.mapper;

import com.pinshang.qingyun.base.mybatis.MyMapper;
import com.pinshang.qingyun.order.mapper.entry.payType.PayTypeAndTipEntry;
import com.pinshang.qingyun.order.model.payType.PayType;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * Created by Ruansi on 2019/8/15 10:10.
 */
@Repository
public interface PayTypeMapper extends MyMapper<PayType> {

    List<PayTypeAndTipEntry> queryValidPayMethodAndTip();
}
