package com.pinshang.qingyun.order.service;

import com.pinshang.qingyun.base.configure.expand.QYAssert;
import com.pinshang.qingyun.base.constant.GlobalSupplierNameConstant;
import com.pinshang.qingyun.base.enums.IogisticsModelEnums;
import com.pinshang.qingyun.base.enums.shop.ShopTypeEnums;
import com.pinshang.qingyun.box.utils.DateTimeUtil;
import com.pinshang.qingyun.order.mapper.*;
import com.pinshang.qingyun.order.mapper.entry.MdShopOrderSettingEntry;
import com.pinshang.qingyun.order.mapper.entry.order.*;
import com.pinshang.qingyun.order.model.shop.Shop;
import com.pinshang.qingyun.order.util.OrderUtil;
import com.pinshang.qingyun.order.vo.order.ShoppingCartDetailVo;
import com.pinshang.qingyun.storage.dto.WarehouseODto;
import com.pinshang.qingyun.storage.service.WarehouseClient;
import com.pinshang.qingyun.supplier.dto.FullSupplierODTO;
import com.pinshang.qingyun.supplier.dto.QuerySupplierByIdsIDTO;
import com.pinshang.qingyun.supplier.dto.SupplierODTO;
import com.pinshang.qingyun.supplier.service.SupplierClient;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import tk.mybatis.mapper.entity.Example;

import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @Author: sk
 * @Date: 2022/3/1
 */
@Service
public class ShoppingCartAdminService {
    @Autowired
    ShoppingCartMapper shoppingCartMapper;
    @Autowired
    OrderService orderService;
    @Autowired
    ShoppingCartItemMapper itemMapper;
    @Autowired
    CommodityMapper commodityMapper;
    @Autowired
    OrderMapper orderMapper;
    @Autowired
    ShopMapper shopMapper;
    @Lazy
    @Autowired
    private MdShopOrderSettingService mdShopOrderSettingService;
    @Autowired
    private SupplierClient supplierClient;
    @Autowired
    private ShoppingCartService shoppingCartService;
    @Autowired
    private ShoppingCartDeleteService shoppingCartDeleteService;
    @Autowired
    private WarehouseClient warehouseClient;
    @Autowired
    private ConsignmentSupplierService consignmentSupplierService;

    public ShoppingCartDetailVo queryShoppingCartAdmin(List<Long> storeIdList, Long userId, Boolean bigShop){
        ShoppingCartDetailVo vo = new ShoppingCartDetailVo();

        List<ShoppingCartAdminEntry> shoppingCartAdminList = shoppingCartMapper.shoppingCartAdminByStoreIdList(storeIdList, userId, bigShop);

        Set<String> commodityIdList = new HashSet<>();
        Set<Integer> shopTypeList = new HashSet<>();
        Set<Long> supplierIdList = new HashSet<>();
        Set<Long> warehouseIdList = new HashSet<>();
        List<Long> shoppingCartIdList = new ArrayList<>();
        shoppingCartAdminList.forEach(item ->{
            commodityIdList.add(item.getCommodityId() + "");
            shopTypeList.add(item.getShopType());
            supplierIdList.add(item.getSupplyId());
            warehouseIdList.add(item.getWarehouseId());
            if(!shoppingCartIdList.contains(item.getShoppingCartId())){
                shoppingCartIdList.add(item.getShoppingCartId());
            }
        });

        // 查询门店订货通用设置
        Map<Integer, List<MdShopOrderSettingEntry>> settingMap = new HashMap<>();
        for(Integer shopType : shopTypeList){
            List<MdShopOrderSettingEntry> settingList = mdShopOrderSettingService.queryMdShopOrderSettingListByShopType(shopType, new ArrayList<>(commodityIdList));
            if(!CollectionUtils.isEmpty(settingList)){
                settingMap.put(shopType, settingList);
            }
        }

        // 供应商和仓库信息
        QuerySupplierByIdsIDTO idsIDTO = new QuerySupplierByIdsIDTO();
        idsIDTO.setSupplierIds(new ArrayList<>(supplierIdList));
        Map<Long, FullSupplierODTO> supplierIdAndODTOMap = supplierClient.querySupplierByIds(idsIDTO);
        Map<Long, WarehouseODto> warehouseIdAndODtoMap = warehouseClient.queryWarehouseListByIds(new ArrayList<>(warehouseIdList));
        SupplierODTO globalEntry = supplierClient.getSupplierByCode(GlobalSupplierNameConstant.XS_SUPPLIER_CODE);

        List<Long> stallIdList = shoppingCartAdminList.stream().filter(p -> p.getStallId() != null && p.getStallId() > 0).collect(Collectors.toList())
                .stream().map(item -> item.getStallId()).collect(Collectors.toList());
        Map<Long, String> stallIdAndNameMap = consignmentSupplierService.queryStallMapByIds(stallIdList);

        // 按购物车id分组
        Map<Long, List<ShoppingCartAdminEntry>> shoppingCartAdminEntryMap = shoppingCartAdminList.stream().collect(Collectors.groupingBy(ShoppingCartAdminEntry::getShoppingCartId));

        ShoppingCartDetailVo.TableData[] sites =new ShoppingCartDetailVo.TableData[shoppingCartAdminEntryMap.size()];
        vo.setSites(sites);
        int i=0;

        for (Long shoppingCartId : shoppingCartIdList) {
            List<ShoppingCartAdminEntry> entries = shoppingCartAdminEntryMap.get(shoppingCartId);
            ShoppingCartAdminEntry shoppingCartEntry = entries.get(0);

            List<MdShopOrderSettingEntry> settingList = settingMap.get(shoppingCartEntry.getShopType());
            Map<Long, MdShopOrderSettingEntry> settMap = settingList.stream().collect(Collectors.toMap(MdShopOrderSettingEntry::getCommodityId, Function.identity()));
            MdShopOrderSettingEntry mdShopOrderSettingEntry = settMap.get(shoppingCartEntry.getCommodityId());

            ShoppingCartDetailVo.TableData table =new ShoppingCartDetailVo.TableData();
            sites[i++] = table;
            ShoppingCartDetailVo.TitleRow[] titleRows =new ShoppingCartDetailVo.TitleRow[1];
            table.setTitle(titleRows);
            ShoppingCartDetailVo.TitleRow title= new ShoppingCartDetailVo.TitleRow();
            BeanUtils.copyProperties(shoppingCartEntry,title);
            title.setLogisticsModelName(IogisticsModelEnums.getName(shoppingCartEntry.getLogisticsModel()));
            title.setShoppingCartId(shoppingCartEntry.getShoppingCartId() + "");
            title.setDisable(false);
            FullSupplierODTO fullSupplierODTO = supplierIdAndODTOMap.get(shoppingCartEntry.getSupplyId());
            if (fullSupplierODTO != null) {
                 title.setCompanyName(fullSupplierODTO.getSupplierName());
            }
            WarehouseODto warehouseODto = warehouseIdAndODtoMap.get(shoppingCartEntry.getWarehouseId());
            if (warehouseODto != null) {
                title.setWarehouseName(warehouseODto.getWarehouseName());
            }

            if((shoppingCartEntry.getLogisticsModel().intValue() ==IogisticsModelEnums.DISPATCHING.getCode())){
                title.setCompanyName(globalEntry.getSupplierName());
                title.setSupplyTime((mdShopOrderSettingEntry.getDefaultWarehouseBeginTime() ==null?"":mdShopOrderSettingEntry.getDefaultWarehouseBeginTime()) +"-"+(mdShopOrderSettingEntry.getDefaultWarehouseEndTime()==null?"":mdShopOrderSettingEntry.getDefaultWarehouseEndTime()));
            }else if(shoppingCartEntry.getLogisticsModel().intValue() ==IogisticsModelEnums.DIRECT_SENDING.getCode()) {
                title.setSupplyTime((mdShopOrderSettingEntry.getDefaultSupplierBeginTime() == null ? "" : mdShopOrderSettingEntry.getDefaultSupplierBeginTime()) + "-" + (mdShopOrderSettingEntry.getDefaultSupplierEndTime() == null ? "" : mdShopOrderSettingEntry.getDefaultSupplierEndTime()));
            }else if(shoppingCartEntry.getLogisticsModel().intValue() ==IogisticsModelEnums.DIRECT_CONNECTION.getCode()) {
                title.setSupplyTime((mdShopOrderSettingEntry.getDefaultSupplierBeginTime() == null ? "" : mdShopOrderSettingEntry.getDefaultSupplierBeginTime()) + "-" + (mdShopOrderSettingEntry.getDefaultSupplierEndTime() == null ? "" : mdShopOrderSettingEntry.getDefaultSupplierEndTime()));
            }

            if(shoppingCartEntry.getLogisticsModel().intValue() ==IogisticsModelEnums.DIRECT_SENDING.getCode()){
                // 直送的仓库名字为空
                title.setWarehouseName("");
            }

            titleRows[0] = title;
            table.setTitle(titleRows);

            List<ShoppingCartDetailVo.DeliveryBatchDay> deliveryBatchDays = shoppingCartService.getDeliveryBatchDays(shoppingCartEntry.getDeleveryTimeRange(), true);
            table.setDeliveryBatchDays(deliveryBatchDays);
            title.setVarietyTotal(entries.size());
            Double aa = entries.stream().mapToDouble(item -> Double.valueOf(item.getQuantity() + "")).sum();
            title.setQuantityTotal(new BigDecimal(aa));
            title.setStallName(stallIdAndNameMap.get(shoppingCartEntry.getStallId()));
        }
        return vo;
    }


    public ShoppingCartDetailVo queryShoppingCart(Long storeId, boolean isInternal,Long shoppingCartId,Boolean handle,Long userId){
        Example shopEx = new Example(Shop.class);
        shopEx.createCriteria().andEqualTo("storeId", storeId);
        List<Shop> shopList = shopMapper.selectByExample(shopEx);
        // 是否是鲜道前置仓
        Boolean isXd = shopList.get(0).getShopType().equals(ShopTypeEnums.XD.getCode());

        ShoppingCartDetailVo vo = new ShoppingCartDetailVo();

        List<ShoppingCartEntry> entry = shoppingCartMapper.shoppingCartDetailAdmin(storeId,shoppingCartId,isXd,isInternal,userId,false);

        if(null != entry && !entry.isEmpty()){
            // 设置供应商和仓库信息
            shoppingCartService.setSupplierAndWarehouseInfo(entry);
            List<Long> ids =new ArrayList<Long>();
            entry.forEach(shoppingCartEntry ->{
                List<ShoppingCartItemEntry> items =shoppingCartEntry.getItems();
                if(null !=items && !items.isEmpty()){
                    items.forEach(shoppingCartItemEntry ->{
                        ids.add(shoppingCartItemEntry.getCommodityId());
                    });
                }
            });

            //查找所有商品价格;
            Map<String, ProductPriceEntry>  priceHashMap = shoppingCartService.getCommodityPrices(ids, storeId, DateTimeUtil.defaultDeliveryDate());

            ShoppingCartDetailVo.TableData[] sites =new ShoppingCartDetailVo.TableData[entry.size()];
            vo.setSites(sites);
            int i=0;

            SupplierODTO globalEntry = supplierClient.getSupplierByCode(GlobalSupplierNameConstant.XS_SUPPLIER_CODE);
            Boolean isXsShop= true;

            // 获取批次信息
            List<DeliveryBatchEntry> deliveryBatchList =shoppingCartMapper.findDeliveryBatchByCode(null);
            Map<String, DeliveryBatchEntry> deliveryBatchMap = deliveryBatchList.stream().collect(Collectors.toMap(DeliveryBatchEntry::getOptionName, Function.identity()));

            for(ShoppingCartEntry shoppingCartEntry: entry){
                // 同一个购物车里面仓库，供应商，物流模式都是一样的.所以随便取第一个商品
                List<String> commodityIds = new ArrayList<>();
                if(CollectionUtils.isEmpty(shoppingCartEntry.getItems())){
                    shoppingCartDeleteService.deleteShoppingCart(Long.valueOf(shoppingCartEntry.getId()));
                    QYAssert.isTrue(false,"请刷新购物车");
                }
                commodityIds.add(shoppingCartEntry.getItems().get(0).getCommodityId()+"");
                List<MdShopOrderSettingEntry> list = mdShopOrderSettingService.queryMdShopOrderSettingListByIds(storeId,commodityIds);
                MdShopOrderSettingEntry mdShopOrderSettingEntry = new MdShopOrderSettingEntry();
                if(!CollectionUtils.isEmpty(list)){
                    mdShopOrderSettingEntry = list.get(0);
                }
                ShoppingCartDetailVo.TableData table =new ShoppingCartDetailVo.TableData();
                sites[i++] =table;
                ShoppingCartDetailVo.TitleRow[] titleRows =new ShoppingCartDetailVo.TitleRow[1];
                table.setTitle(titleRows);
                ShoppingCartDetailVo.TitleRow title= new ShoppingCartDetailVo.TitleRow();
                BeanUtils.copyProperties(shoppingCartEntry,title);
                title.setLogisticsModelName(IogisticsModelEnums.getName(shoppingCartEntry.getLogisticsModel()));
                title.setShoppingCartId(shoppingCartEntry.getId());
                title.setDisable(false);
                title.setShowDeliveryBatch(false);
                title.setOrderTime(DateTimeUtil.defaultDeliveryDate());
                title.setXdOrderTime(OrderUtil.getOrderTime(shoppingCartEntry.getDeleveryTimeRange()));
                title.setXdDeliveryBatch(shoppingCartService.getXdDeliveryBatch(deliveryBatchList));
                // orderTime,deliveryBatch 不为空表示代理购物车
                if(StringUtils.isNotBlank(shoppingCartEntry.getOrderTime()) && StringUtils.isNotBlank(shoppingCartEntry.getDeliveryBatch())){
                    title.setXdOrderTime(shoppingCartEntry.getOrderTime());
                    DeliveryBatchEntry deliveryBatchEntry = deliveryBatchMap.get(shoppingCartEntry.getDeliveryBatch());
                    if(deliveryBatchEntry != null){
                        title.setXdDeliveryBatch(deliveryBatchEntry.getOptionName() + "_" + deliveryBatchEntry.getMemo());
                    }
                    title.setDeliveryBatch(null);
                }

                List<DeliveryBatchEntry> batchEntryList = shoppingCartService.findDeliveryBatch(storeId, title.getOrderTime());
                if(!CollectionUtils.isEmpty(batchEntryList)){
                    title.setAdminDeliveryBatch(batchEntryList.get(0).getOptionCode());
                }
                if((shoppingCartEntry.getLogisticsModel().intValue() ==IogisticsModelEnums.DISPATCHING.getCode())){
                    title.setCompanyName(globalEntry.getSupplierName());
                    title.setSupplyTime((mdShopOrderSettingEntry.getDefaultWarehouseBeginTime() ==null?"":mdShopOrderSettingEntry.getDefaultWarehouseBeginTime()) +"-"+(mdShopOrderSettingEntry.getDefaultWarehouseEndTime()==null?"":mdShopOrderSettingEntry.getDefaultWarehouseEndTime()));
                    if(isXsShop){title.setShowDeliveryBatch(true);}
                    table.setXdCloseTime(mdShopOrderSettingEntry.getDefaultWarehouseEndTime());
                }else if(shoppingCartEntry.getLogisticsModel().intValue() ==IogisticsModelEnums.DIRECT_SENDING.getCode()) {
                    title.setShowDeliveryBatch(false);
                    title.setSupplyTime((mdShopOrderSettingEntry.getDefaultSupplierBeginTime() == null ? "" : mdShopOrderSettingEntry.getDefaultSupplierBeginTime()) + "-" + (mdShopOrderSettingEntry.getDefaultSupplierEndTime() == null ? "" : mdShopOrderSettingEntry.getDefaultSupplierEndTime()));
                    table.setXdCloseTime(mdShopOrderSettingEntry.getDefaultSupplierEndTime());
                }else if(shoppingCartEntry.getLogisticsModel().intValue() ==IogisticsModelEnums.DIRECT_CONNECTION.getCode()) {
                    if(isXsShop){ title.setShowDeliveryBatch(true);}
                    title.setSupplyTime((mdShopOrderSettingEntry.getDefaultSupplierBeginTime() == null ? "" : mdShopOrderSettingEntry.getDefaultSupplierBeginTime()) + "-" + (mdShopOrderSettingEntry.getDefaultSupplierEndTime() == null ? "" : mdShopOrderSettingEntry.getDefaultSupplierEndTime()));
                    table.setXdCloseTime(mdShopOrderSettingEntry.getDefaultSupplierEndTime());
                }
                if(!isInternal){
                    //检查客户下单时间
                    if(!DateTimeUtil.compareNewDate(shoppingCartEntry.getBeginTime(), shoppingCartEntry.getEndTime())){
                        title.setDisable(true);
                    }

                    //直送，直通 检查供应商时间
                    if(shoppingCartEntry.getLogisticsModel().intValue() ==IogisticsModelEnums.DIRECT_SENDING.getCode()
                            || shoppingCartEntry.getLogisticsModel().intValue() ==IogisticsModelEnums.DIRECT_CONNECTION.getCode()){
                        if(StringUtils.isBlank(mdShopOrderSettingEntry.getDefaultSupplierBeginTime()) || StringUtils.isBlank(mdShopOrderSettingEntry.getDefaultSupplierEndTime())){
                            title.setDisable(true);
                        }
                        if(!DateTimeUtil.compareNewDate(mdShopOrderSettingEntry.getDefaultSupplierBeginTime(), mdShopOrderSettingEntry.getDefaultSupplierEndTime())){
                            title.setDisable(true);
                        }
                    }
                    //配送模式 时间校验 仓库时间
                    if(shoppingCartEntry.getLogisticsModel().intValue() == IogisticsModelEnums.DISPATCHING.getCode()){
                        if(!DateTimeUtil.compareNewDate(mdShopOrderSettingEntry.getDefaultWarehouseBeginTime(), mdShopOrderSettingEntry.getDefaultWarehouseEndTime())){
                            title.setDisable(true);
                        }
                    }
                }
                if(shoppingCartEntry.getLogisticsModel().intValue() !=IogisticsModelEnums.DIRECT_SENDING.getCode()){
                    title.setWarehouseName(shoppingCartEntry.getWarehouseName());
                }else{ // 直送的仓库名字为空
                    title.setWarehouseName("");
                }
                titleRows[0] =title;
                table.setTitle(titleRows);

                List<ShoppingCartItemEntry> items =shoppingCartEntry.getItems();
                BigDecimal totalPrice =BigDecimal.ZERO;
                BigDecimal totalVarietyTotal =BigDecimal.ZERO;
                BigDecimal totalQuantity =BigDecimal.ZERO;
                if(null != items && !items.isEmpty()){
                    ShoppingCartDetailVo.CommodityRow[] crs =new ShoppingCartDetailVo.CommodityRow[items.size()];
                    table.setCommoditys(crs);
                    int k=0;
                    for(ShoppingCartItemEntry shoppingCartItemEntry:items ){
                        totalVarietyTotal =totalVarietyTotal.add(BigDecimal.valueOf(1L));
                        totalQuantity =totalQuantity.add(shoppingCartItemEntry.getQuantity());
                        ShoppingCartDetailVo.CommodityRow cr =new ShoppingCartDetailVo.CommodityRow();
                        BigDecimal  price = priceHashMap.get(shoppingCartItemEntry.getCommodityId()+"") ==null ? BigDecimal.ZERO : priceHashMap.get(shoppingCartItemEntry.getCommodityId()+"").getPrice();
                        //取实时价格
                        cr.setPrice(price);
                        cr.setTotalPrice(cr.getPrice().multiply(shoppingCartItemEntry.getQuantity()).setScale(2, BigDecimal.ROUND_HALF_UP));
                        totalPrice =totalPrice.add(cr.getTotalPrice());
                        crs[k++] =cr;
                    };
                }
                List<ShoppingCartDetailVo.DeliveryBatchDay> deliveryBatchDays = shoppingCartService.getDeliveryBatchDays(shoppingCartEntry.getDeleveryTimeRange(), isInternal);
                table.setDeliveryBatchDays(deliveryBatchDays);

                // 送货日期默认会选中deliveryBatchDays中的第一条数据，此时配送批次也要是默认日期对应的批次
                List<DeliveryBatchEntry> entrys = shoppingCartService.findDeliveryBatch(storeId, title.getOrderTime());
                table.setEntrys(entrys);
                title.setTotalPrice(totalPrice);
                title.setVarietyTotal(totalVarietyTotal.intValue());
                title.setQuantityTotal(totalQuantity);
            };
        }
        // 前置仓按照截单时间倒序排
        shoppingCartService.sortShoppingCart(vo,isXd);
        return vo;
    }
}
