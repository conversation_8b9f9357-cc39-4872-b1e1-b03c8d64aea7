package com.pinshang.qingyun.order.service.pf.recharge;

import com.pinshang.qingyun.base.configure.expand.QYAssert;
import com.pinshang.qingyun.base.enums.OrderCodeEnums;
import com.pinshang.qingyun.base.enums.StoreBillTypeEnums;
import com.pinshang.qingyun.base.enums.XSPayBillStatusEnums;
import com.pinshang.qingyun.base.enums.YesOrNoEnums;
import com.pinshang.qingyun.base.enums.pay.AppCodeEnum;
import com.pinshang.qingyun.base.enums.pay.PayTypeIntEnum;
import com.pinshang.qingyun.base.util.FastThreadLocalUtil;
import com.pinshang.qingyun.base.util.OrderCodeUtil;
import com.pinshang.qingyun.box.utils.JsonUtil;
import com.pinshang.qingyun.box.utils.SpringUtil;
import com.pinshang.qingyun.box.utils.VersionUtils;
import com.pinshang.qingyun.common.dto.DictionaryODTO;
import com.pinshang.qingyun.common.service.DictionaryClient;
import com.pinshang.qingyun.kafka.enums.KafkaMessageOperationTypeEnum;
import com.pinshang.qingyun.order.bo.StoreRechargeBO;
import com.pinshang.qingyun.order.config.LockConstants;
import com.pinshang.qingyun.order.constant.VersionConstant;
import com.pinshang.qingyun.order.dto.PfPrePayBillODTO;
import com.pinshang.qingyun.order.mapper.PfPayBillJobMapper;
import com.pinshang.qingyun.order.mapper.PfPayBillMapper;
import com.pinshang.qingyun.order.mapper.PfPayInvokeLogMapper;
import com.pinshang.qingyun.order.mapper.entry.payType.PayTypeAndTipEntry;
import com.pinshang.qingyun.order.mapper.entry.payType.PfPayTypeEntry;
import com.pinshang.qingyun.order.mapper.entry.pf.recharge.PfPayBillEntry;
import com.pinshang.qingyun.order.mapper.entry.pf.recharge.PfPayBillJobEntry;
import com.pinshang.qingyun.order.mapper.entry.pf.recharge.PfPayInvokeLogEntity;
import com.pinshang.qingyun.order.model.pf.recharge.PfPayBill;
import com.pinshang.qingyun.order.model.pf.recharge.PfPayBillJob;
import com.pinshang.qingyun.order.service.StoreRechargeService;
import com.pinshang.qingyun.order.service.pay.ThirdPartyPayService;
import com.pinshang.qingyun.order.service.pay.callback.PfThirdPartyPayCallbackEvent;
import com.pinshang.qingyun.order.service.pay.model.PfPrePayReqParam;
import com.pinshang.qingyun.order.service.pay.model.PfPrePayResponse;
import com.pinshang.qingyun.order.service.pay.model.PfTradeQueryParam;
import com.pinshang.qingyun.order.service.pay.model.log.PfThirdPartyPayLogEvent;
import com.pinshang.qingyun.order.vo.recharge.GoPayNoCheckIDTO;
import com.pinshang.qingyun.order.vo.recharge.PfRechargeVo;
import com.pinshang.qingyun.pay.dto.GoPayIDTO;
import com.pinshang.qingyun.pay.dto.PayTypeODTO;
import com.pinshang.qingyun.pay.dto.PrePayODTO;
import com.pinshang.qingyun.pay.dto.QueryODTO;
import com.pinshang.qingyun.pay.service.AppPayClient;
import com.pinshang.qingyun.settlementTb.service.StoreRechargeClient;
import com.pinshang.qingyun.store.dto.storePlaceOrder.StoreIsRechargeODTO;
import com.pinshang.qingyun.store.service.StorePlaceOrderClient;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.aop.framework.AopContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.entity.Example;

import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 批发充值
 * <AUTHOR>
 *
 */
@Slf4j
@Service
public class PfRechargeService {

	@Autowired
    private AppPayClient payClient;
	
	@Autowired
    private DictionaryClient dictionaryClient;
	
	@Autowired
	private PfPayBillMapper pfPayBillMapper;
	
	@Autowired
	private PfPayBillJobMapper pfPayBillJobMapper;
	
	@Autowired
    private ThirdPartyPayService thirdPartyPayService;
	
	@Autowired
    private RedissonClient redissonClient;
	
	@Autowired
    private StoreRechargeClient storeRechargeClient;
	
	@Autowired
	private PfPayInvokeLogMapper pfPayInvokeLogMapper;

    @Autowired
    private StorePlaceOrderClient storePlaceOrderClient;

    @Autowired
    private StoreRechargeService storeRechargeService;

	/**
     * 查询有效的支付方式及促销语
     * @return
     */
	public PfPayTypeEntry queryValidPayMethodAndTip() {
		List<PayTypeAndTipEntry> payTypeList = new ArrayList<>();
        List<PayTypeODTO>  payTypeODTOS = payClient.findAllByAppCode(AppCodeEnum.PF.getCode());
        payTypeODTOS = payTypeODTOS.stream().sorted(Comparator.comparing(PayTypeODTO::getSortNum)).collect(Collectors.toList());
        AtomicReference<Boolean> flag = new AtomicReference<>(false);
        payTypeODTOS.forEach( i-> {
            //聚合微信小程序支付需要判断版本
            if (PayTypeIntEnum.UNIONPAY_MINI.getCode() == i.getPayType()) {
                //判断版本
                if (VersionUtils.compareVersions(VersionConstant.PF_UNION_MINI_PAY_VERSION,FastThreadLocalUtil.getPF().getAppVersion())) {
                    return;
                }
                //添加了聚合微信小程序
                flag.set(true);
            }
            PayTypeAndTipEntry payTypeAndTipEntry = new PayTypeAndTipEntry();
            payTypeAndTipEntry.setPayType(i.getPayType().longValue());
            payTypeAndTipEntry.setTips(i.getTips());
            payTypeAndTipEntry.setName(i.getName());
            payTypeAndTipEntry.setStatus(YesOrNoEnums.YES.getCode());
            payTypeList.add(payTypeAndTipEntry);
        });
        //如果添加了聚合微信小程序
        if (flag.get()){
            //移除微信支付
            payTypeList.removeIf(item -> PayTypeIntEnum.UNIONPAY_WECHAT.getCode()== item.getPayType());
        }

        List<DictionaryODTO> rechargeList = queryRechargeDetail();
        QYAssert.isTrue(SpringUtil.isNotEmpty(payTypeList) && SpringUtil.isNotEmpty(rechargeList),"获取充值方式失败！");

        return new PfPayTypeEntry(payTypeList,rechargeList);
	}

	/**
     * 获取充值说明
     * */
    @SuppressWarnings("unchecked")
	public List<DictionaryODTO> queryRechargeDetail() {
        List<DictionaryODTO> dictionaryList = dictionaryClient.querySubDictionaryByParentOptionCode("pf_recharge"); // 需要配置字典
        if(dictionaryList != null  && dictionaryList.size() > 0){
            return dictionaryList;
        }
        return Collections.EMPTY_LIST;
    }

    /**
     * 批发充值
     * @param vo
     */
    @Transactional(rollbackFor = Exception.class)
	public PfPrePayBillODTO pfAppRecharge(PfRechargeVo vo) {
        StoreIsRechargeODTO storeIsRechargeODTO = storePlaceOrderClient.checkPfStoreIsRechargeByStoreId(vo.getStoreId());
        QYAssert.isTrue(null != storeIsRechargeODTO,"登录信息有误,请重新登录！");
        QYAssert.isTrue(storeIsRechargeODTO.getSuccess(),storeIsRechargeODTO.getMessage());
        String confPayAmount = this.queryRechargeMoney();
        vo.checkPayAmount( confPayAmount );
        String payBillCode = String.valueOf(OrderCodeUtil.genAppOrderCodeLong(OrderCodeEnums.PF_PAY_BILL_PREFIX));
        //1、生成批发第三方交易支付流水表（t_pf_pay_bill）数据
        PfPayBill pfPayBill = PfPayBill.getInstance(vo.getUserId(),KafkaMessageOperationTypeEnum.INSERT)
                .setStoreId(vo.getStoreId())
                .setBillCode(payBillCode)
                .setBillStatus(XSPayBillStatusEnums.WAITING_PAY.getCode())
                .setPayAmount(vo.getPayAmount())
                .setPayType(vo.getPayType())
                .setReferType(1);
        int insertNum = pfPayBillMapper.insert(pfPayBill);
        QYAssert.isTrue(insertNum > 0,"充值失败！");
        //2、生成鲜达第三方交易支付定时补偿表（t_pf_pay_bill_job）数据
        Long pfPayBillId = pfPayBill.getId();
        PfPayBillJob job = new PfPayBillJob();
        job.setPayBillId(pfPayBillId);
        int insertJobNum = pfPayBillJobMapper.insert(job);
        QYAssert.isTrue(insertJobNum > 0,"充值失败！");
        return handleThirdPartyPrePay(vo, payBillCode, pfPayBillId);
	}
    
    private PfPrePayBillODTO handleThirdPartyPrePay(PfRechargeVo vo, String payBillCode, Long pfPayBillId){
        PfPrePayBillODTO bill = new PfPrePayBillODTO();
        PfPrePayReqParam prePayReqParam = new PfPrePayReqParam();
        prePayReqParam.setOrderCode(payBillCode);
        prePayReqParam.setPayAmount(vo.getPayAmount());
        prePayReqParam.setPayType(vo.getPayType());
        prePayReqParam.setUid(vo.getUserId());
        PfPrePayResponse prePayResponse = thirdPartyPayService.createPfPrePayBill(prePayReqParam, null);
        if(prePayResponse.getException() != null){
            throw prePayResponse.getException();
        }
        //回填三方支待付单json
        try {
            PayTypeIntEnum enums=PayTypeIntEnum.getByCode(vo.getPayType());
            backFillPrepayJson(prePayResponse.getPrePayJson(enums)
                    , pfPayBillId, enums);
        } catch (Exception e) {
            log.warn("保存三方预支付单失败");
        }
        if(vo.getPayType().equals(PayTypeIntEnum.ALIPAY.getCode())){
            bill.setAlipayPrePayBill(prePayResponse.getAlipayPrePayBill());

        }else if(vo.getPayType().equals(PayTypeIntEnum.WECHAT.getCode())){
            bill.setWechatPrePayBill(prePayResponse.getWechatPrePayBill());
        }else if(vo.getPayType().equals(PayTypeIntEnum.UNION_PAY.getCode())){
            bill.setUnionPayPrePayBill(prePayResponse.getUnionPrePayBill());
        }else if(vo.getPayType().equals(PayTypeIntEnum.UNIONPAY_ALI.getCode())){
            bill.setUnionAlipayPrePayBill(prePayResponse.getUnionPrePayBill());
        }else if(vo.getPayType().equals(PayTypeIntEnum.UNIONPAY_WECHAT.getCode())){
            bill.setUnionWechatPrePayBill(prePayResponse.getUnionPrePayBill());
        }else if(vo.getPayType().equals(PayTypeIntEnum.UNIONPAY_UNION.getCode())){
            bill.setUnionPayAggregationPrePayBill(prePayResponse.getUnionPrePayBill());
        }
        bill.setOrderCode(prePayReqParam.getOrderCode());
        bill.setPayBillId(prePayResponse.getPayBillId());
        return bill;
    }
    
    /**
     * 回填三方预支付单json
     * @param json
     * @param payBillId
     * @param payType
     */
    public void backFillPrepayJson(String json, Long payBillId, PayTypeIntEnum payType){
        PfPayBill pfPayBill = new PfPayBill();
        pfPayBill.setId(payBillId);
        switch (payType){
            case ALIPAY:
                pfPayBill.setPrepayJsonAlipay(json);
                break;
            case WECHAT:
                pfPayBill.setPrepayJsonWechat(json);
                break;
            case UNION_PAY:
            case UNIONPAY_ALI:
            case UNIONPAY_WECHAT:
            case UNIONPAY_UNION:
                pfPayBill.setPrepayJsonUnion(json);
                break;
            default:
                throw new RuntimeException("支付类型异常!");
        }
        pfPayBillMapper.updateByPrimaryKeySelective(pfPayBill);
    }
    
    /**
     * 获取最小充值金额
     * */
    public String queryRechargeMoney() {
        List<DictionaryODTO> rechargeList = this.queryRechargeDetail();
        QYAssert.isTrue(SpringUtil.isNotEmpty(rechargeList),"获取充值限额信息失败！");
        String confPayAmount = null;
        for (DictionaryODTO item : rechargeList) {
            if("pf-money".equals(item.getOptionCode())){
                confPayAmount = item.getOptionValue();
            }
        }
        return confPayAmount;
    }

    /**
     * 批发支付补偿job
     */
    public void pfAppRechargeJob() {
        List<PfPayBillJobEntry> jobList = pfPayBillJobMapper.findPfPayBillJob();
        if (SpringUtil.isNotEmpty(jobList)) {
            for (PfPayBillJobEntry entry : jobList) {
                if (entry.getBillStatus() == XSPayBillStatusEnums.WAITING_PAY.getCode()) {
                    //查询是否存在已支付的订单
                    Example ex = new Example(PfPayBill.class);
                    ex.createCriteria().andEqualTo("billCode", entry.getBillCode()).andEqualTo("billStatus", XSPayBillStatusEnums.PAY_FINISHED.getCode());
                    List<PfPayBill> entries = pfPayBillMapper.selectByExample(ex);
                    if (SpringUtil.isEmpty(entries)) {
                        PfTradeQueryParam tradeQueryParam = new PfTradeQueryParam(entry);
                        QueryODTO queryODTO = thirdPartyPayService.tradePfQuery(tradeQueryParam, AppCodeEnum.PF, null);
                        if (queryODTO.getException() == null && queryODTO.getBizOk()) {
                            PfThirdPartyPayCallbackEvent payCallbackEvent = PfThirdPartyPayCallbackEvent.build(queryODTO.getBillCode()
                                    , entry.getBillCode(), null, true, PayTypeIntEnum.getByCode(entry.getPayType()), queryODTO.getPayTime()).withoutLog();
                            callBackEvent(payCallbackEvent);
                            continue;
                        } else {
                            //  订单关闭，或者订单不存在处理
                            log.warn("pfAppRechargeJob订单未支付 bill{}", JsonUtil.java2json(entry));
                        }
                    }

                }
                Example delExp = new Example(PfPayBillJob.class);
                delExp.createCriteria().andEqualTo("payBillId", entry.getPayBillId());
                pfPayBillJobMapper.deleteByExample(delExp);
            }
        }
    }

    public void callBackEvent(PfThirdPartyPayCallbackEvent event) {
        PfRechargeService pfRechargeService = ((PfRechargeService) AopContext.currentProxy());
        RLock lock = redissonClient.getLock(LockConstants.generateCallbackLockKey(event.getOrderCode()));
        lock.lock(5L, TimeUnit.SECONDS);
        try {
            pfRechargeService.processCallBackEvent(event);
        } finally {
            if (lock.isLocked() && lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
    }

	/**
	 * 支付中心回调
	 * @param event
	 */
    @Transactional(rollbackFor = Exception.class)
	public void processCallBackEvent(PfThirdPartyPayCallbackEvent event) {
        try {
            log.warn("callBackEvent orderCoder:{}", event.getOrderCode());
            List<String> billCodes = Stream.of(event.getOrderCode()).collect(Collectors.toList());
            List<PfPayBillEntry> payBillList = pfPayBillMapper.findPfPayBillByBillCodes(billCodes);
            if (SpringUtil.isNotEmpty(payBillList)) {
                PfPayBillEntry payBill = payBillList.get(0);
                log.warn("订单状态BillStatus:{}", payBill.getBillStatus());
                if (payBill.getBillStatus().equals(XSPayBillStatusEnums.WAITING_PAY.getCode())) {
                    paySuccessPostHandle(event, payBill);
                } else {
                    log.error("订单状态=支付完成orderCode:{}", event.getOrderCode());
                }
            } else {
                log.error("订单不存在orderCoder:{}", event.getOrderCode());
            }
        } catch (Exception e) {
            log.error("callBackEvent异常 ", e);
            throw e;
        }
    }
	
	/**
     * 支付成功后续处理
     */
    @Transactional(rollbackFor = Exception.class)
    public Boolean paySuccessPostHandle(PfThirdPartyPayCallbackEvent event, PfPayBillEntry payBill) {
    	addRecharge(payBill);
//        //1、查询客户价格方案信息表余额（t_store_settlement）
//        StoreSettlement storeSettlement = storeSettlementMapper.getStoreSettlementByStoreId(payBill.getStoreId());
//        //当前用户充值后金额
//        BigDecimal storeBalance = payBill.getPayAmount().add(BigDecimal.valueOf(storeSettlement.getCollectPrice()));
//        //2、新增订单账款表信息（t_order_bill）
//        OrderBill orderBill = OrderBill.builder(payBill,storeBalance);
//        orderBillMapper.recordTheTransaction(orderBill);
        //3、修改鲜达第三方交易支付流水表（t_pf_pay_bill）
        log.warn("修改批发第三方交易支付流水表 orderCode:{}",event.getOrderCode());
        PfPayBill pfPayBill = PfPayBill.getInstance(1L,KafkaMessageOperationTypeEnum.INSERT)
                .setStoreId(payBill.getStoreId())
                .setTradeBillCode(event.getTransactionId())
                .setPayTime(event.getPayTime())
                .setBillStatus(XSPayBillStatusEnums.PAY_FINISHED.getCode());
        pfPayBill.setId(payBill.getId());
        pfPayBillMapper.updateByPrimaryKeySelective(pfPayBill);
//        //4、修改用户余额（t_store_settlement）
//        storeSettlementMapper.updateCollectPriceTwo(payBill.getPayAmount(), storeSettlement.getId());
//        //5、记录客户充值信息（t_store_recharge）
//        StoreRecharge storeRecharge = StoreRecharge.builder(payBill,storeBalance);
//        storeRechargeMapper.insertStoreRecharge(storeRecharge);
        //6、清除第三方交易支付定时补偿
        Example delExp = new Example(PfPayBillJob.class);
        delExp.createCriteria().andEqualTo("payBillId", payBill.getId());
        pfPayBillJobMapper.deleteByExample(delExp);
        //7、记录支付完成得日志
        this.handleLogEvent(new PfThirdPartyPayLogEvent(event));
        return Boolean.TRUE;
    }
    
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRES_NEW)
    public void handleLogEvent(PfThirdPartyPayLogEvent logEvent){
    	PfPayInvokeLogEntity logEntity = new PfPayInvokeLogEntity();
        logEntity.setCreateTime(new Date());
        logEntity.setIsRequest(logEvent.getLogType().getCode());
        logEntity.setReferType(logEvent.getOperateType().getCode());
        logEntity.setReferCode(logEvent.getOrderCode());
        if(StringUtils.isNotBlank(logEvent.getJson())){
            logEntity.setData(logEvent.getJson());
        }else {
            logEntity.setData("预支付订单无返回参数");
        }

        pfPayInvokeLogMapper.insert(logEntity);
    }
    
    
 // 充值
    private void addRecharge(PfPayBillEntry payBill) {
    	// 参见 StoreRecharge
    	Long paymentMethod = 0L;
    	if (payBill.getPayType().equals(PayTypeIntEnum.ALIPAY.getCode())){
    		paymentMethod = 9131078242860601319L;
     	} else if (payBill.getPayType().equals(PayTypeIntEnum.WECHAT.getCode())){
             paymentMethod = 9131078242860601318L;
     	} else if (payBill.getPayType().equals(PayTypeIntEnum.UNION_PAY.getCode())){
     		paymentMethod = 9131078242860601330L;
    	}else if (payBill.getPayType().equals(PayTypeIntEnum.UNIONPAY_ALI.getCode())){
            paymentMethod = 9131078242860601330L;
        }else if (payBill.getPayType().equals(PayTypeIntEnum.UNIONPAY_WECHAT.getCode())){
            paymentMethod = 9131078242860601330L;
        }else if (payBill.getPayType().equals(PayTypeIntEnum.UNIONPAY_UNION.getCode())){
            paymentMethod = 9131078242860601330L;
        }

    	Long receiptType = 9131078242860601331L;
        StoreRechargeBO rechargeBO = StoreRechargeBO.builder()
                .storeId(payBill.getStoreId())
                .money(payBill.getPayAmount().doubleValue())
                .remark("批发app充值:" + payBill.getBillCode() + ":" + new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()))
                .receiptType(receiptType)
                .paymentMethod(paymentMethod)
                .receiptDate(new Date())
                .tradeCode(payBill.getBillCode())
                .tradeTime(new Date())
                .thirdPartyTradeCode(payBill.getBillCode())
                .billType((StoreBillTypeEnums.PF_DEPOSIT.getCode()))
                .build();
        Boolean result = storeRechargeService.storeRecharge(rechargeBO);
        QYAssert.isTrue(result, "充值失败！");
    }
    @Transactional(rollbackFor = Exception.class)
    public boolean pfPayStatus(String billCode) {
        Example example = new Example(PfPayBill.class);
        example.createCriteria().andEqualTo("billCode", billCode);
        PfPayBill pfPayBill = pfPayBillMapper.selectOneByExample(example);
        QYAssert.isTrue(pfPayBill != null, "未查询此支付订单信息记录");
        if (XSPayBillStatusEnums.PAY_FINISHED.getCode() == pfPayBill.getBillStatus()) {
            return true;
        }
        PfTradeQueryParam tradeQueryParam = new PfTradeQueryParam();
        tradeQueryParam.setBillCode(billCode);
        tradeQueryParam.setPayType(pfPayBill.getPayType());
        QueryODTO queryODTO = thirdPartyPayService.tradePfQuery(tradeQueryParam, AppCodeEnum.PF, null);
        if (queryODTO.getException() == null && queryODTO.getBizOk()) {
            PfThirdPartyPayCallbackEvent payCallbackEvent = PfThirdPartyPayCallbackEvent.build(queryODTO.getBillCode()
                    , billCode, null, true, PayTypeIntEnum.getByCode(pfPayBill.getPayType()), queryODTO.getPayTime()).withoutLog();
            try {
                callBackEvent(payCallbackEvent);
                return true;
            } catch (Exception e) {
                e.printStackTrace();
                return false;
            }
        }
        return false;
    }

    public PfPrePayBillODTO goPayNoCheck(GoPayNoCheckIDTO goPayNoCheckIDTO) {
        GoPayIDTO goPayIDTO = new GoPayIDTO();
        goPayIDTO.setJsCode(goPayNoCheckIDTO.getJsCode());
        goPayIDTO.setClientIp(goPayNoCheckIDTO.getClientIp());
        String id = goPayNoCheckIDTO.getBillId().replaceAll("\"", "");
        goPayIDTO.setBillId(Long.valueOf(id));
        PrePayODTO prePayODTO = payClient.goPay(goPayIDTO);
        PfPrePayBillODTO prePayBillODTO = new PfPrePayBillODTO();
        prePayBillODTO.setWechatPrePayBill(prePayODTO.getMiniPrePayBill());
        return prePayBillODTO;
    }

}
