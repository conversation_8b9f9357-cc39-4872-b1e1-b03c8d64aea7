package com.pinshang.qingyun.order.mapper;

import com.pinshang.qingyun.order.mapper.entry.shop.SubOrder4DistributionEntry;
import com.pinshang.qingyun.order.mapper.entry.shop.SubOrderItem4DistributionEntry;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Mapper
@Repository
public interface SubOrder4DistributionMapper {
    /**
     * 查询当前配送已完成子订单集合
     * @param subOrderIds
     * @return
     */
    List<SubOrder4DistributionEntry> find4DistributionOk(@Param("subOrderIds") List<Long> subOrderIds);

    List<SubOrderItem4DistributionEntry> findItem(@Param("subOrderId") Long subOrderId);
}
