package com.pinshang.qingyun.order.controller;

import com.github.pagehelper.PageInfo;
import com.pinshang.qingyun.order.mapper.entry.store.StoreAccountEntry;
import com.pinshang.qingyun.order.mapper.entry.store.StoreEntry;
import com.pinshang.qingyun.order.mapper.entry.store.StoreInviceEntry;
import com.pinshang.qingyun.order.model.store.Store;
import com.pinshang.qingyun.order.service.StoreInvoiceService;
import com.pinshang.qingyun.order.service.StoreService;
import com.pinshang.qingyun.order.vo.shop.DistributionStoreAccountQueryVo;
import com.pinshang.qingyun.order.vo.shop.DistributionStoreAccountVo;
import com.pinshang.qingyun.order.vo.shop.StoreAccountVo;
import org.springframework.beans.factory.annotation.Autowired;
import com.pinshang.qingyun.base.configure.expand.QYAssert;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 *
 * <AUTHOR>
 * @Date 2018/4/8 16:18
 */
@RestController
@RequestMapping("/storeAccount")
public class StoreAccountController {

    @Autowired
    private StoreService storeService;
    @Autowired
    private StoreInvoiceService storeInvoiceService;

    @RequestMapping(value = "/list", method = RequestMethod.POST)
    public PageInfo<StoreAccountEntry> list(@RequestBody StoreAccountVo vo){
        QYAssert.notNull(vo,"查询客户账号管理列表参数为空!");
        return storeService.getStoreAccountList(vo);
    }

    @RequestMapping(value = "/find/{storeCode}", method = RequestMethod.GET)
    public Store getByCode(@PathVariable("storeCode") String storeCode){
        return storeService.findByStoreCode(storeCode);
    }

    /**
     * 新增配送客户排序待选列表
     * @param vo
     * @return
     */
    @RequestMapping(value = "/distribution/list", method = RequestMethod.POST)
    public PageInfo<DistributionStoreAccountVo> distributionList(@RequestBody DistributionStoreAccountQueryVo vo){
        return storeService.distributionList(vo);
    }

    /**
     * 配送客户排序 excel导入 信息预处理
     * @param storeCodes
     * @return
     */
    @RequestMapping(value = "/distribution/excel", method = RequestMethod.POST)
    public List<Long> handleExcelImportInfo(@RequestBody List<String> storeCodes){
        if(storeCodes == null || storeCodes.isEmpty()){
            QYAssert.isTrue(false,"导入客户排序信息参数为空异常!");
        }
        return storeService.handleExcelImportInfo(storeCodes);
    }

    /**
     * 根据客户id集合 查询客户详细信息
     * @param storeIdList
     * @return
     */
    @RequestMapping(value = "/findStoreListByStoreIdList", method = RequestMethod.POST)
    public List<Store> findStoreListByStoreIdList(@RequestBody List<Long> storeIdList){
        return storeService.findStoreListByStoreIdList(storeIdList);
    }

    /**
     * 条件查询客户列表
     * @param storeName
     * @return
     */
    @RequestMapping(value = "/findStoreListByStoreName",method = RequestMethod.GET)
    public List<StoreEntry> findStoreListByStoreName(@RequestParam(value = "storeName",required = false)String storeName){
        return storeService.findStoreListByStoreName(storeName);
    }

    /**
     * 根据客户 id 查询客户信息
     * @param storeId
     * @return
     */
    @RequestMapping(value = "/findStoreByStoreId",method = RequestMethod.GET)
    public Store findStoreByStoreId(@RequestParam(value = "storeId",required = false) Long storeId){
        return storeService.findStoreByStoreId(storeId);
    }

    /**
     * 查询所有绑定门店的客户
     * @return
     */
    @RequestMapping(value = "/findAllShopStoreList",method = RequestMethod.GET)
    public List<StoreEntry> findAllShopStoreList(){
        return storeService.findAllShopStoreList();
    }


    @RequestMapping(value = "/findListByStoreIds",method = RequestMethod.POST)
    public List<StoreInviceEntry> findListByStoreIds(@RequestBody List<Long> storeIds){
        return storeInvoiceService.findListByStoreIds(storeIds);
    }
    @RequestMapping(value = "/findInvoiceById",method = RequestMethod.POST)
    public StoreInviceEntry findInvoiceById(@RequestParam(value = "uid",required = false)  String uid){
        return storeInvoiceService.findInvoiceById(uid);
    }


}
