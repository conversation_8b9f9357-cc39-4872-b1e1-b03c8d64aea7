package com.pinshang.qingyun.order.service.externalDocking;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.pinshang.qingyun.base.configure.expand.QYAssert;
import com.pinshang.qingyun.box.utils.ConcurrentDateUtil;
import com.pinshang.qingyun.box.utils.SpringUtil;
import com.pinshang.qingyun.order.mapper.RtOutboundOrderMapper;
import com.pinshang.qingyun.order.mapper.entry.externalDocking.RtOutboundOrderEntry;
import com.pinshang.qingyun.order.vo.externalDocking.RtOutboundOrderVo;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.text.DateFormat;
import java.text.DecimalFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 大润发出库单
 * <AUTHOR>
 * @date 2023年2月15日10:00:09
 */
@Service
public class RtOutboundOrderService {

    private final RtOutboundOrderMapper rtOutboundOrderMapper;

    public RtOutboundOrderService(RtOutboundOrderMapper rtOutboundOrderMapper) {
        this.rtOutboundOrderMapper = rtOutboundOrderMapper;
    }

    /**
     * 大润发出库单列表分页查询
     * @param vo
     * @return
     */
    public PageInfo<RtOutboundOrderEntry> selectRtOutboundOrderPageInfo(RtOutboundOrderVo vo){
        QYAssert.isTrue(null != vo, "参数异常");
        QYAssert.isTrue(null != vo.getDeliveryTime(), "送货日期不能为空");
        QYAssert.isTrue(null != vo.getStoreShortName(), "客户简称不能为空");
        PageInfo<RtOutboundOrderEntry> pageDate = PageHelper.startPage(vo.getPageNo(), vo.getPageSize()).doSelectPageInfo(() -> {
            rtOutboundOrderMapper.selectRtOutboundOrderList(vo);
        });
        if(SpringUtil.isNotEmpty(pageDate.getList())){
            List<RtOutboundOrderEntry> list = pageDate.getList();
            try {
                DateFormat sdf = ConcurrentDateUtil.SDF_FULL_DATE_TIME.get();
                SimpleDateFormat dateFormat = new SimpleDateFormat("HH:mm:ss");
                String hms = dateFormat.format(new Date());

                Date parseDate = sdf.parse(vo.getDeliveryTime() + " " + hms);
                Calendar begin=Calendar.getInstance();
                begin.setTime(parseDate);
                begin.add(Calendar.DATE,-1);
                System.out.println(begin.getTime());

                //获取所有的收货单位(去重)
                List<String> receivingUnitList = list.stream().map(RtOutboundOrderEntry::getReceivingUnit).distinct().collect(Collectors.toList());
                Collections.sort(receivingUnitList);
                Map<String,Date> map = new HashMap<>();

                //循环一个收货单位<->对应一个时间
                for(String receivingUnit : receivingUnitList){
                    begin.add(Calendar.MINUTE,1);
                    map.put(receivingUnit,begin.getTime());
                }
                for(RtOutboundOrderEntry entry : list) {
                    //处理时间
                    Date deliveryTime = map.get(entry.getReceivingUnit());
                    entry.setDeliveryTime(sdf.format(deliveryTime));
                    entry.setRegistrar("诸杰");
                    // 处理出库数量
                    BigDecimal rtCommodityNum = entry.getRtCommodityNum();
                    BigDecimal conversionRate = entry.getConversionRate();
                    if(null != rtCommodityNum && null != conversionRate){
                        BigDecimal multiply = rtCommodityNum.multiply(conversionRate);
                        DecimalFormat decimalFormat =new DecimalFormat("0.00");
                        entry.setDeliveryNumber(decimalFormat.format(multiply));
                    }
                }
            } catch (ParseException e) {
                e.printStackTrace();
            }

        }
        return pageDate;
    }
}
