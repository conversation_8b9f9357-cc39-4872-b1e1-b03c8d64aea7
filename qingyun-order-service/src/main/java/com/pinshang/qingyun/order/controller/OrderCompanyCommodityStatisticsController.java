package com.pinshang.qingyun.order.controller;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.write.style.column.LongestMatchColumnWidthStyleStrategy;
import com.github.pagehelper.PageInfo;
import com.pinshang.qingyun.base.configure.expand.QYAssert;
import com.pinshang.qingyun.base.util.ExcelUtil;
import com.pinshang.qingyun.order.mapper.entry.orderStatistics.OrderCompanyCommodityStatisticsEntry;
import com.pinshang.qingyun.order.service.OrderCompanyCommodityStatisticsService;
import com.pinshang.qingyun.order.util.OrderTimeUtil;
import com.pinshang.qingyun.order.vo.orderStatistics.OrderCompanyCommodityStatisticsVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.joda.time.LocalDate;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.CustomAutowireConfigurer;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.Date;
import java.util.List;


/**
 * 客户所属公司+商品两个维度，统计商品订单数据
 */
@Slf4j
@RestController
@RequestMapping("/orderCompanyCommodityStatistics")
public class OrderCompanyCommodityStatisticsController {
    @Autowired
    private OrderCompanyCommodityStatisticsService statisticsService;
    @Autowired
    private RedissonClient redissonClient;

    /**
     * 定时生成商品订单统计数据
     * @return
     */
    @PostMapping("/createOrderCompanyCommodityStatisticsJob")
    public void createOrderCompanyCommodityStatisticsJob(@RequestBody(required = false) String[] orderTimes) {
        RLock lock = redissonClient.getLock("ORDER_COMPANY_COMMODITY_JOB");
        if (!lock.isLocked()) {
            boolean locked = lock.tryLock();
            if( locked ){
                try {
                    List<Date> orderTimeList = OrderTimeUtil.processOrderTimeParams(orderTimes);
                    for(Date orderTime : orderTimeList){
                        try{
                            statisticsService.saveOrderCompanyCommodityStatistics(orderTime);
                        }catch (Exception e){
                            log.error("定时统计商品订单数据异常，orderTime：",orderTime,e);
                        }
                    }
                }finally {
                    lock.unlock();
                }
            }
        }
    }
    @RequestMapping(value = "/list", method = RequestMethod.POST)
    public PageInfo<OrderCompanyCommodityStatisticsEntry> findListByCondition(@RequestBody OrderCompanyCommodityStatisticsVo vo) {
        PageInfo <OrderCompanyCommodityStatisticsEntry> pageInfo = statisticsService.findListByCondition(vo);
        return pageInfo;
    }

    /**
     * 公司商品订货导出
     */
    @GetMapping("/exportOrderCommodity")
    public void exportOrderCommodity(OrderCompanyCommodityStatisticsVo vo, HttpServletResponse httpServletResponse){
        vo.setPageNo(1);
        vo.setPageSize(Integer.MAX_VALUE);
        PageInfo<OrderCompanyCommodityStatisticsEntry> pageInfo = statisticsService.findListByCondition(vo);
        if(pageInfo==null && CollectionUtils.isEmpty(pageInfo.getList())){
            QYAssert.isFalse("未查询到数据");
        }
        try {
            List<OrderCompanyCommodityStatisticsEntry> list = pageInfo.getList();
            String fileName;
            if (vo.getShowType() == null || vo.getShowType() == 1) {
                fileName = "公司商品订货明细" + LocalDate.now().toString("yyyyMMdd");
            } else {
                fileName = "公司商品订货汇总" + LocalDate.now().toString("yyyyMMdd");
            }
            ExcelUtil.setFileNameAndHead(httpServletResponse, fileName);
            EasyExcel.write(httpServletResponse.getOutputStream()).autoCloseStream(Boolean.FALSE).sheet("公司商品订货导出")
                    .head(statisticsService.createExportHead(vo,list)).doWrite(statisticsService.createExportData(vo,list));
        }catch (IOException e) {
            log.error("公司商品订货导出异常",e);
        }
    }


}
