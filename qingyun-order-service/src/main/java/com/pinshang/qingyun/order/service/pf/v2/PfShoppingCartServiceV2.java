package com.pinshang.qingyun.order.service.pf.v2;

import com.pinshang.qingyun.base.api.ApiErrorCodeEnum;
import com.pinshang.qingyun.base.api.PfTokenInfo;
import com.pinshang.qingyun.base.configure.expand.BizLogicException;
import com.pinshang.qingyun.base.configure.expand.QYAssert;
import com.pinshang.qingyun.base.enums.order.OrderTypeEnum;
import com.pinshang.qingyun.base.util.FastThreadLocalUtil;
import com.pinshang.qingyun.box.utils.DateUtil;
import com.pinshang.qingyun.order.dto.pf.PfCommodityLimit4AppODTO;
import com.pinshang.qingyun.order.dto.shopcart.*;
import com.pinshang.qingyun.order.enums.StockType;
import com.pinshang.qingyun.order.mapper.OrderListMapper;
import com.pinshang.qingyun.order.mapper.OrderMapper;
import com.pinshang.qingyun.order.mapper.PfShoppingCartMapper;
import com.pinshang.qingyun.order.mapper.StoreDurationMapper;
import com.pinshang.qingyun.order.model.pf.PfShoppingCart;
import com.pinshang.qingyun.order.service.BStockShortService;
import com.pinshang.qingyun.order.service.StoreService;
import com.pinshang.qingyun.order.service.pf.BuildPfShoppingCart;
import com.pinshang.qingyun.order.service.xda.v4.ToBService;
import com.pinshang.qingyun.order.vo.BStockLackVO;
import com.pinshang.qingyun.order.vo.PfCommodityLimit4AppIDTO;
import com.pinshang.qingyun.pf.product.dto.front.PfCommodityAppODTO;
import com.pinshang.qingyun.pf.product.service.PfCommodityFrontClient;
import com.pinshang.qingyun.storage.dto.tob.CommodityInventoryODTO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.entity.Example;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2024/3/12 16:54
 */
@Service
public class PfShoppingCartServiceV2 {
    @Autowired
    private PfShoppingCartMapper pfShoppingCartMapper;

    @Autowired
    private OrderListMapper orderListMapper;
    @Autowired
    private OrderMapper orderMapper;

    @Autowired
    private PfCommodityFrontClient pfCommodityFrontClient;
    @Autowired
    private StoreService storeService;
    @Autowired
    private StoreDurationMapper storeDurationMapper;
    @Autowired
    private ToBService toBService;
    @Autowired
    private BStockShortService bStockShortService;
    /**
     * 购物车可添加最大数量
     */
    public static BigDecimal ShoppingCart_Max_Value = BigDecimal.valueOf(9999.99);

    @Transactional(rollbackFor = Exception.class)
    public NotShoppingCartPageODTO addShopCart(ShoppingCartAddIDTO idto, BuildPfShoppingCartV2 buildPfShoppingCart){
        if(idto.getAppendQuantity() != null){
            QYAssert.isTrue(idto.getAppendQuantity().compareTo(BigDecimal.ZERO) > 0 && idto.getAppendQuantity().compareTo(ShoppingCart_Max_Value) <= 0
                    , "设置数量超量异常!");
        }
        QYAssert.isTrue(idto.getCommodityId() != null, "商品参数异常.");
        QYAssert.isTrue(idto.getOrderTime() != null, "订货日期不能为空.");
        NotShoppingCartPageODTO odto = new NotShoppingCartPageODTO();
        odto.setCommodityId(idto.getCommodityId());
        List<PfShoppingCart> pfShoppingCarts = queryShoppingCart(idto.getStoreId(), idto.getCommodityId());
        /**
         * 如果购物车中没有当前客户的对应数据,直接创建一条购物车数据
         */
        PfTokenInfo pfTokenInfo = FastThreadLocalUtil.getPF();
        if(pfShoppingCarts.isEmpty()){
            PfShoppingCart shoppingCart = PfShoppingCart.builder()
                    .commodityId(idto.getCommodityId())
                    .storeId(idto.getStoreId())
                    .quantity(idto.getAppendQuantity())
                    .createId(pfTokenInfo.getUserId())
                    .createName(pfTokenInfo.getUserName())
                    .createTime(new Date())
                    .updateTime(new Date())
                    .updateId(pfTokenInfo.getUserId())
                    .updateName(pfTokenInfo.getUserName())
                    .build();
            odto.setQuantity(idto.getAppendQuantity());
            checkCommodityInfo(shoppingCart, idto.getOrderTime(),odto, buildPfShoppingCart);
            if(null != odto.getAbleAdd() && !odto.getAbleAdd()){
                odto.setQuantity(BigDecimal.ZERO);
                return odto;
            }
            pfShoppingCartMapper.insert(shoppingCart);
            return odto;
        }

        /**
         * 购物车添加商品 判断商品数量是否合法
         */
        PfShoppingCart pfShoppingCart = pfShoppingCarts.get(0);
        BigDecimal quantity = pfShoppingCart.getQuantity();
        pfShoppingCart.setQuantity(idto.getAppendQuantity().add(quantity));

        QYAssert.isTrue( pfShoppingCart.getQuantity().compareTo(ShoppingCart_Max_Value) <= 0
                , "商品数量最大"+ShoppingCart_Max_Value);
        odto.setQuantity(pfShoppingCart.getQuantity());

        checkCommodityInfo(pfShoppingCart, idto.getOrderTime(),odto, buildPfShoppingCart);

        /**
         * 如果超过限量则不修改商品数量
         */
        if((null != odto.getAbleAdd() && !odto.getAbleAdd()) ||null != odto.getStockWarningTips() ){
            odto.setQuantity(quantity);
            return odto;
        }
        int count = pfShoppingCartMapper.updateByPrimaryKeySelective(pfShoppingCart);
        QYAssert.isTrue(count != 0, "购物车更新失败");
        return odto;
    }

    @Transactional(rollbackFor = Exception.class)
    public NotShoppingCartPageODTO minus(ShoppingCartMinusIDTO idto, BuildPfShoppingCartV2 buildPfShoppingCart){
        QYAssert.isTrue(idto.getCommodityId() != null, "商品参数异常.");
        List<PfShoppingCart> pfShoppingCarts = queryShoppingCart(idto.getStoreId(), idto.getCommodityId());
        if(pfShoppingCarts == null || pfShoppingCarts.isEmpty()){
            QYAssert.isFalse("购物车中未找到指定商品!");
        }
        NotShoppingCartPageODTO odto = new NotShoppingCartPageODTO();
        odto.setCommodityId(idto.getCommodityId());
        PfShoppingCart pfShoppingCart = pfShoppingCarts.get(0);
        BigDecimal minusNum = pfShoppingCart.getQuantity().subtract(idto.getQuantity());
        odto.setQuantity(minusNum);
        if(minusNum.compareTo(BigDecimal.ZERO) <= 0){
            pfShoppingCartMapper.deleteByPrimaryKey(pfShoppingCart);
            return odto;
        }
        PfTokenInfo pfTokenInfo = FastThreadLocalUtil.getPF();
        pfShoppingCart.setQuantity(minusNum);
        pfShoppingCart.setUpdateId(pfTokenInfo.getUserId());
        pfShoppingCart.setUpdateName(pfTokenInfo.getUserName());
        pfShoppingCart.setUpdateTime(new Date());
        checkCommodityInfo(pfShoppingCart, idto.getOrderTime(), odto, buildPfShoppingCart);
        if((null != odto.getAbleAdd() && !odto.getAbleAdd()) || null != odto.getStockWarningTips()){
            odto.setStockWarningTips(odto.getStockWarningTips());
        }
        checkCommodityInfo(pfShoppingCart, idto.getOrderTime(),odto, buildPfShoppingCart);

        int count = pfShoppingCartMapper.updateByPrimaryKeySelective(pfShoppingCart);
        QYAssert.isTrue(count != 0, "购物车更新失败");
        return odto;
    }
    public int clear(Long storeId, List<Long> commodityIds) {
        Example ex = new Example(PfShoppingCart.class);
        Example.Criteria criteria = ex.createCriteria().andEqualTo("storeId", storeId);
        if(!commodityIds.isEmpty()){
            criteria.andIn("commodityId", commodityIds);
        }
        return pfShoppingCartMapper.deleteByExample(ex);
    }
    /**
     * 判断商品状态 和 限量
     * @param cart
     * @param orderTime
     * @param pageODTO
     * @param buildPfShoppingCart
     */
    public void checkCommodityInfo(PfShoppingCart cart, Date orderTime, NotShoppingCartPageODTO pageODTO, BuildPfShoppingCartV2 buildPfShoppingCart){
        List<PfCommodityAppODTO> pfCommodityAppODTOS = buildPfShoppingCart.queryCommodityBaseInfo(Collections.singletonList(cart.getCommodityId()), orderTime, cart.getStoreId(), false);
        QYAssert.notEmpty(pfCommodityAppODTOS, "商品已下架，请刷新当前商品页。");
        List<PfCommodityAppODTO> collect = pfCommodityAppODTOS.stream().filter(item -> item.getIsCanOrder()).collect(Collectors.toList());
        QYAssert.notEmpty(collect, "商品已下架，请刷新当前商品页。");

        /**
         * 一次只能添加一个所以get(0)
         */
        PfCommodityAppODTO odto = pfCommodityAppODTOS.get(0);

        /**
         * 去掉速冻商品30整倍数的逻辑,原先商品的“速冻”标志依然保留
         */
        BigDecimal remainder = cart.getQuantity().divideAndRemainder(odto.getSalesBoxCapacity())[1];
        QYAssert.isTrue(remainder.compareTo(BigDecimal.ZERO) == 0, odto.getCommodityName() + "商品数量必须是箱规的倍数");
        if(null != odto.getIsCanOrder() && !odto.getIsCanOrder()){
            throw new BizLogicException(ApiErrorCodeEnum.XDA_INVALID_COMM_WARN.getRemark(), ApiErrorCodeEnum.XDA_INVALID_COMM_WARN);
        }
        if(odto.getIsSpecialPrice() == 1){
            checkLimitNumber(cart,odto,pageODTO,true);
        }
        Map<Long, CommodityInventoryODTO> commodityInventoryODTOMap = toBService.queryCommodityInventoryOne(orderTime, cart.getCommodityId(), cart.getQuantity(),null,null,null,null);
        CommodityInventoryODTO commodityInventoryODTO = commodityInventoryODTOMap.get(cart.getCommodityId());
        if(!commodityInventoryODTO.getStockType().equals(StockType.UNLIMITED.getCode())){
            if(!commodityInventoryODTO.getHaveInventory()){
                List<BStockLackVO> voList = new ArrayList<>();
                BStockLackVO bStockLackVO = new BStockLackVO();
                bStockLackVO.setStoreId(cart.getStoreId());
                bStockLackVO.setCommodityId(cart.getCommodityId());
                bStockLackVO.setStockQuantity(commodityInventoryODTO.getInventoryQuantity());
                bStockLackVO.setNeedQuantity(pageODTO.getQuantity());
                bStockLackVO.setOrderType(OrderTypeEnum.PF_APP_ORDER.getCode());
                bStockLackVO.setStockType(commodityInventoryODTO.getStockType());
                bStockLackVO.setCreateId(cart.getStoreId());
                voList.add(bStockLackVO);
                bStockShortService.stockShort(voList);
                BigDecimal bigDecimal = new BigDecimal(commodityInventoryODTO.getInventoryQuantity().stripTrailingZeros().toPlainString());
                pageODTO.setStockWarningTips("库存不足，余量为" + bigDecimal);
            }
        }
//        if(odto.getIsLimit() == 1){
//            checkLimitNumber(cart,odto,pageODTO,false);
//        }

    }

    /**
     * 判断限量
     * @param odto 限量
     * @param pageODTO
     */
    private void checkLimitNumber(PfShoppingCart cart,PfCommodityAppODTO odto,NotShoppingCartPageODTO pageODTO,Boolean isSpecialPrice){
        /**
         * 当前可订货数量
         */
        Long commodityId = cart.getCommodityId();
        BigDecimal quantity =cart.getQuantity();
        BigDecimal limitNumber = isSpecialPrice ? odto.getSpecialPriceLimit() : odto.getLimitNumber();
        BigDecimal canUseCommNum = limitNumber.subtract(isSpecialPrice ? BigDecimal.ZERO : getUsedQuantity(odto.getLimitStartTime(),odto.getLimitEndTime(), commodityId));
        canUseCommNum = canUseCommNum.compareTo(BigDecimal.ZERO) >= 0 ? canUseCommNum : BigDecimal.ZERO;
        boolean canAddComm = quantity.compareTo(canUseCommNum) > 0;
        boolean ableAddComm = quantity.compareTo(canUseCommNum) <= 0;
        if(null == pageODTO.getAbleAdd() || !ableAddComm){
            pageODTO.setAbleAdd(ableAddComm);
        }
        if(canAddComm){
            String error = isSpecialPrice ? "商品特价限量"+canUseCommNum+odto.getCommodityUnitName():"商品库存不足，仅剩" + canUseCommNum;
            pageODTO.setStockWarningTips(error);
        }
    }
    /**
     * 查询当前时间 商品订货数量
     */
    private BigDecimal getUsedQuantity(Date limitStartTime,Date limitEndTime,Long commodityId){
        String startTime = DateUtil.getDateFormate(limitStartTime, "yyyy-MM-dd");
        String endTime = DateUtil.getDateFormate(limitEndTime, "yyyy-MM-dd");
        List<PfCommodityLimit4AppIDTO> pfCommodityLimit4AppIDTOS = new ArrayList<>();
        pfCommodityLimit4AppIDTOS.add(PfCommodityLimit4AppIDTO.builder().startTime(startTime).endTime(endTime).commodityId(commodityId).build());
        List<PfCommodityLimit4AppODTO> pfCommodityLimit4AppODTOS = orderListMapper.queryPfCommodityLimit(pfCommodityLimit4AppIDTOS);
        BigDecimal usedQuantity = BigDecimal.ZERO;
        if(!pfCommodityLimit4AppODTOS.isEmpty() && pfCommodityLimit4AppODTOS.get(0) != null){
            usedQuantity = pfCommodityLimit4AppODTOS.get(0).getTotalQuantity();
        }
        return usedQuantity;
    }

    /**
     * 设置数量
     * @param idto
     */
    @Transactional(rollbackFor = Exception.class)
    public NotShoppingCartPageODTO setNum(ShoppingCartSetNumIDTO idto, BuildPfShoppingCartV2 buildPfShoppingCart){
        QYAssert.isTrue(idto.getCommodityId() != null, "商品参数异常.");
        List<PfShoppingCart> pfShoppingCarts = queryShoppingCart(idto.getStoreId(), idto.getCommodityId());
        if(pfShoppingCarts == null || pfShoppingCarts.isEmpty()){
            QYAssert.isFalse("购物车中未找到指定商品!");
        }
        NotShoppingCartPageODTO odto = new NotShoppingCartPageODTO();
        odto.setCommodityId(idto.getCommodityId());
        odto.setQuantity(idto.getQuantity());
        PfShoppingCart pfShoppingCart = pfShoppingCarts.get(0);
        odto.setOldQuantity(pfShoppingCart.getQuantity());
        odto.setId(pfShoppingCart.getId());
        pfShoppingCart.setQuantity(idto.getQuantity());
        QYAssert.isTrue(idto.getQuantity().compareTo(ShoppingCart_Max_Value) <= 0
                , "设置数量超量异常!");
        if(idto.getQuantity().compareTo(BigDecimal.ZERO) == 0){
            pfShoppingCartMapper.deleteByPrimaryKey(pfShoppingCart);
            return odto;
        }
        checkCommodityInfo(pfShoppingCart, idto.getOrderTime(), odto, buildPfShoppingCart);
        if((null != odto.getAbleAdd() && !odto.getAbleAdd()) || null != odto.getStockWarningTips()){
            odto.setQuantity(pfShoppingCart.getQuantity());
            return odto;
        }
        PfTokenInfo pfTokenInfo = FastThreadLocalUtil.getPF();
        pfShoppingCart.setUpdateId(pfTokenInfo.getUserId());
        pfShoppingCart.setUpdateName(pfTokenInfo.getUserName());
        pfShoppingCart.setUpdateTime(new Date());
        int count = pfShoppingCartMapper.updateByPrimaryKeySelective(pfShoppingCart);
        QYAssert.isTrue(count != 0, "购物车更新失败");
        return odto;
    }
    @Transactional(rollbackFor = Exception.class)
    public void setNumV(Long id,BigDecimal quantity){
        if(quantity.compareTo(BigDecimal.ZERO) == 0){
            pfShoppingCartMapper.deleteByPrimaryKey(id);
        }
        PfShoppingCart pfShoppingCart = new PfShoppingCart();
        pfShoppingCart.setId(id);
        pfShoppingCart.setQuantity(quantity);
        pfShoppingCartMapper.updateByPrimaryKeySelective(pfShoppingCart);
    }
    /**
     * 清除失效商品
     * @param deliveryDate
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public ShoppingCartODTO clearInvalidCommodity(Date deliveryDate){
        PfTokenInfo tokenInfo = FastThreadLocalUtil.getPF();
        Long storeId =  tokenInfo.getStoreId();
        QYAssert.isTrue(storeId != null, "未能获取到用户信息,请重新登录!");
        BuildPfShoppingCart buildPfShoppingCart = new BuildPfShoppingCart(storeId, deliveryDate, pfShoppingCartMapper, orderMapper, orderListMapper, pfCommodityFrontClient, storeService, storeDurationMapper);
        ShoppingCartODTO cartODTO = buildPfShoppingCart.shopCartList();

        List<ShoppingCartCommodityODTO> commodities = cartODTO.getInvalidateGroup().getCommodities();
        List<Long> shopCartList = commodities.stream().map(ShoppingCartCommodityODTO :: getCommodityId).collect(Collectors.toList());
        if(shopCartList.isEmpty()){
            cartODTO.getInvalidateGroup().setCommodities(Collections.emptyList());
            return cartODTO;
        }
        pfShoppingCartMapper.deleteShopCart(storeId, shopCartList);
        cartODTO.getInvalidateGroup().setCommodities(Collections.emptyList());
        return cartODTO;
    }

    /**
     * 根据客户id 商品id 查询pf购物车数据
     * @param storeId
     * @param commodityId
     * @return
     */
    public List<PfShoppingCart> queryShoppingCart(Long storeId, Long commodityId){
        Example ex = new Example(PfShoppingCart.class);
        Example.Criteria criteria = ex.createCriteria()
                .andEqualTo("storeId", storeId)
                .andEqualTo("commodityId", commodityId);
        return pfShoppingCartMapper.selectByExample(ex);

    }

    /**
     *
     * @param storeId
     * @param orderTime
     * @param buildPfShoppingCart
     * @return
     */
    public Integer getValidCommoditySum(Long storeId, Date orderTime, BuildPfShoppingCartV2 buildPfShoppingCart){
        List<PfShoppingCart> shoppingCarts = buildPfShoppingCart.queryItems(storeId);
        if(shoppingCarts.isEmpty()){
            return 0;
        }
        List<ShoppingCartCommodityODTO> commodityODTOS = buildPfShoppingCart.getValidCommodity(shoppingCarts, orderTime, storeId);
        return commodityODTOS.size();
    }
}
