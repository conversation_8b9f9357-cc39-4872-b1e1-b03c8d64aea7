package com.pinshang.qingyun.order.service;

import com.pinshang.qingyun.order.enums.ProductTypeEnums;
import com.pinshang.qingyun.order.mapper.OrderListGiftMapper;
import com.pinshang.qingyun.order.model.order.OrderList;
import com.pinshang.qingyun.order.model.order.OrderListGift;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.entity.Example;

import java.util.List;

/**
 * @Author: sk
 * @Date: 2024/7/18
 */
@Service
public class OrderListGiftService {

    @Autowired
    private OrderListGiftMapper orderGiftMapper;

    public List<OrderListGift> getOrderGiftRationByOrderId(Long orderId) {
        Example example = new Example(OrderListGift.class);
        example.createCriteria()
                .andEqualTo("orderId", orderId)
                .andEqualTo("type", ProductTypeEnums.RATION.getCode());
        return orderGiftMapper.selectByExample(example);
    }

    public List<OrderListGift> getOrderListGiftByCommodityIdAndOrderId(Long orderId, Long commodityId) {
        Example ex = new Example(OrderList.class);
        ex.createCriteria()
                .andEqualTo("commodityId", commodityId)
                .andEqualTo("orderId", orderId);
        return orderGiftMapper.selectByExample(ex);
    }

    @Transactional(rollbackFor = Exception.class)
    public void updateBatchOrderListGift(List<OrderListGift> updateList) {
        orderGiftMapper.updateBatchOrderListGift(updateList);
    }

    public List<OrderListGift> getOrderGiftProductByOrderId(Long orderId) {
        Example example = new Example(OrderListGift.class);
        example.createCriteria()
                .andEqualTo("orderId", orderId)
                .andEqualTo("type", ProductTypeEnums.PRODUCT.getCode());
        return orderGiftMapper.selectByExample(example);
    }

    public List<OrderListGift> getOrderGiftByOrderId(Long orderId) {
        Example example = new Example(OrderListGift.class);
        example.createCriteria()
                .andEqualTo("orderId", orderId);
        return orderGiftMapper.selectByExample(example);
    }
}
