package com.pinshang.qingyun.order.mapper.entry.order;

import java.math.BigDecimal;

public class CommodityInfoEntry {
	 //商品id
	 private Long id;
	 //客户价格方案商品id
	 private Long commodityId;
	 private String commodityCode;
	 private String commodityName;
	 //物流模式
	 private Integer logisticsModel;
	 //供应商id
	 private Long supplierId;
	 
	 private Integer commodityStatus;
	 //仓库id
	 private Long warehouseId;
	private String workBeginTime;
	private String workEndTime;

	 //供应商开始时间
	 private String supplierStartTime;
	 //供应商结束时间
	 private String supplierEndTime;

	 // 是否可采:1-可采,0-否,不可采
	 private Integer commodityPurchaseStatus;

	// 产品价格方案 价格
	 private BigDecimal price;

	 private  String commoditySpec;
	 /** 包装规格**/
	private BigDecimal commodityPackageSpec;

	private Integer isWeight; // 是否称重0-不称量,1-称重

	/** 档口编码 */
	private String stallCode;

	public String getStallCode() {
		return stallCode;
	}

	public void setStallCode(String stallCode) {
		this.stallCode = stallCode;
	}

	public Integer getIsWeight() {
		return isWeight;
	}

	public void setIsWeight(Integer isWeight) {
		this.isWeight = isWeight;
	}

	public BigDecimal getCommodityPackageSpec() {
		return commodityPackageSpec;
	}

	public void setCommodityPackageSpec(BigDecimal commodityPackageSpec) {
		this.commodityPackageSpec = commodityPackageSpec;
	}

	public String getCommoditySpec() {
		return commoditySpec;
	}

	public void setCommoditySpec(String commoditySpec) {
		this.commoditySpec = commoditySpec;
	}

	public Long getId() {
		return id;
	}
	public void setId(Long id) {
		this.id = id;
	}
	public Long getCommodityId() {
		return commodityId;
	}
	public void setCommodityId(Long commodityId) {
		this.commodityId = commodityId;
	}
	public Integer getLogisticsModel() {
		return logisticsModel;
	}
	public void setLogisticsModel(Integer logisticsModel) {
		this.logisticsModel = logisticsModel;
	}
	public Long getSupplierId() {
		return supplierId;
	}
	public void setSupplierId(Long supplierId) {
		this.supplierId = supplierId;
	}
	public Long getWarehouseId() {
		return warehouseId;
	}
	public void setWarehouseId(Long warehouseId) {
		this.warehouseId = warehouseId;
	}
	public String getSupplierStartTime() {
		return supplierStartTime;
	}
	public void setSupplierStartTime(String supplierStartTime) {
		this.supplierStartTime = supplierStartTime;
	}
	public String getSupplierEndTime() {
		return supplierEndTime;
	}
	public void setSupplierEndTime(String supplierEndTime) {
		this.supplierEndTime = supplierEndTime;
	}
	public String getCommodityCode() {
		return commodityCode;
	}
	public void setCommodityCode(String commodityCode) {
		this.commodityCode = commodityCode;
	}
	public Integer getCommodityStatus() {
		return commodityStatus;
	}
	public void setCommodityStatus(Integer commodityStatus) {
		this.commodityStatus = commodityStatus;
	}

	public String getCommodityName() {
		return commodityName;
	}

	public void setCommodityName(String commodityName) {
		this.commodityName = commodityName;
	}

	public Integer getCommodityPurchaseStatus() {
		return commodityPurchaseStatus;
	}

	public void setCommodityPurchaseStatus(Integer commodityPurchaseStatus) {
		this.commodityPurchaseStatus = commodityPurchaseStatus;
	}

	public BigDecimal getPrice() {
		return price;
	}

	public void setPrice(BigDecimal price) {
		this.price = price;
	}

	public String getWorkBeginTime() {
		return workBeginTime;
	}

	public void setWorkBeginTime(String workBeginTime) {
		this.workBeginTime = workBeginTime;
	}

	public String getWorkEndTime() {
		return workEndTime;
	}

	public void setWorkEndTime(String workEndTime) {
		this.workEndTime = workEndTime;
	}
}
