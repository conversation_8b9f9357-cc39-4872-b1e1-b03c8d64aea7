package com.pinshang.qingyun.order.listener;

import com.alibaba.fastjson.JSON;
import com.pinshang.qingyun.base.api.QYApplicationContext;
import com.pinshang.qingyun.box.utils.JsonUtil;
import com.pinshang.qingyun.kafka.base.BaseKafkaOnlineSwitchProcessor;
import com.pinshang.qingyun.kafka.base.KafkaMessageWrapper;
import com.pinshang.qingyun.kafka.base.KafkaTopicConstant;
import com.pinshang.qingyun.order.service.pf.PfOrderService;
import com.pinshang.qingyun.order.vo.kafka.SubOrderRealQtyWrittenBackMessage;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;

/**
 * 批发订单超发与短交处理器
 *
 *
 */
@Component
@Slf4j
public class PfOverIssueAndShotDeliveryHandler extends BaseKafkaOnlineSwitchProcessor {

    private PfOrderService pfOrderService;

    @KafkaListener(topics = {"${application.name.switch}"+ KafkaTopicConstant.ORDER_LIST_REAL_QUANTITY_UPDATED_TOPIC},
    groupId = "${application.name.switch}" + "PfOverIssueAndShotDeliveryHandle")
    public void handle(String message) {
        log.info(String.format("接受到子单实发数量回填消息：%s", message));
        SubOrderRealQtyWrittenBackMessage orderItemRealQuantityUpdatedMessage;
        try {
            orderItemRealQuantityUpdatedMessage = parseMessage(message);
        } catch (Exception e) {
            log.error(String.format("PfOverIssueAndShotDeliveryHandler消息[%s]解析异常", message), e);
            return;
        }

        pfOrderService.handleOverIssueAndShotDelivery(orderItemRealQuantityUpdatedMessage);
    }

    private SubOrderRealQtyWrittenBackMessage parseMessage(String message) {
        KafkaMessageWrapper messageWrapper = JSON.parseObject(message, KafkaMessageWrapper.class);
        return JsonUtil.json2java(JsonUtil.java2json( messageWrapper.getData()), SubOrderRealQtyWrittenBackMessage.class);
    }

    @Override
    public List<String> getKafkaIds() {
        return Collections.singletonList(
                QYApplicationContext.applicationNameSwitch + KafkaTopicConstant.ORDER_LIST_REAL_QUANTITY_UPDATED_TOPIC);
    }

    @Autowired
    public void setPfOrderService(PfOrderService pfOrderService) {
        this.pfOrderService = pfOrderService;
    }

}
