package com.pinshang.qingyun.order.service;

import com.pinshang.qingyun.base.enums.YesOrNoEnums;
import com.pinshang.qingyun.order.mapper.ShoppingCartItemMapper;
import com.pinshang.qingyun.order.mapper.ShoppingCartMapper;
import com.pinshang.qingyun.order.mapper.entry.order.ShoppingCartEntry;
import com.pinshang.qingyun.order.model.order.ShoppingCart;
import com.pinshang.qingyun.order.model.order.ShoppingCartItem;
import com.pinshang.qingyun.order.service.auto.AutoShopCommodityService;
import com.pinshang.qingyun.order.util.ThreadLocalUtils;
import com.pinshang.qingyun.order.vo.order.ShoppingCartVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionSynchronizationAdapter;
import org.springframework.transaction.support.TransactionSynchronizationManager;
import tk.mybatis.mapper.entity.Example;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @Author: sk
 * @Date: 2022/6/15
 */
@Slf4j
@Service
public class ShoppingCartUpdateService {

    @Autowired
    private ShoppingCartMapper shoppingCartMapper;
    @Autowired
    private ShoppingCartService shoppingCartService;
    @Lazy
    @Autowired
    private AutoShopCommodityService autoShopCommodityService;
    @Autowired
    private ShoppingCartItemMapper itemMapper;
    @Autowired
    private WeChatSendMessageService weChatSendMessageService;
    @Autowired
    private ConsigneeCommonService consigneeCommonService;

    /**
     * 一次处理一个storeId下面的购物车
     * @param storeId
     * @param list
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public Boolean dealAutoShoppingCart(Long storeId, List<ShoppingCartEntry> list){
        // 商品由 自动订货 变为 非自动订货，或者 由 非自动订货 变为 自动订货，门店购物车里的分组和“自动”标识都会自动更新
        List<Long> autoCommodityIdList = autoShopCommodityService.getAutoCommodityList(storeId);

        // 1。当前代销商为空，查询出来的不为空
        // 2.当前代销商不为空，查询出来的代销商为空，或者不相等。删除购物车，重新加
        //Map<Long,Long> consigneeCommMap = consigneeCommonService.getConsigneeCommMap(null, storeId);

        List<ShoppingCartEntry> addCommodityList = new ArrayList<>(); //需要重新加购物车的商品
        // storeId下面的购物车根据shopping_cart_id 进行分组
        Map<Long, List<ShoppingCartEntry>> shoppingCartIdMap = list.stream().collect(Collectors.groupingBy(ShoppingCartEntry::getShoppingCartId));
        for(Map.Entry<Long, List<ShoppingCartEntry>> entry : shoppingCartIdMap.entrySet()){
            Long shoppingCartId = entry.getKey();
            List<ShoppingCartEntry> shoppingCartItemList = entry.getValue();
            List<ShoppingCartEntry> deleteCommodityList = new ArrayList<>(); //需要删除的
            for(ShoppingCartEntry cartItem : shoppingCartItemList){
                // 情况1 商品非自动订货加入购物车，然后再设置为自动订货商品
                // 情况2 自动订货商品加入购物车，然后再设置为非自动订货
                Boolean boolean1 = cartItem.getAutoStatus().equals(YesOrNoEnums.NO.getCode())
                        && CollectionUtils.isNotEmpty(autoCommodityIdList) && autoCommodityIdList.contains(cartItem.getCommodityId());
                Boolean boolean2 = cartItem.getAutoStatus().equals(YesOrNoEnums.YES.getCode())
                        && (CollectionUtils.isEmpty(autoCommodityIdList) || !autoCommodityIdList.contains(cartItem.getCommodityId()));

                /*// 情况3当前代销商为空，查询出来的不为空
                // 情况4当前代销商不为空，查询出来的代销商为空，或者不相等。删除购物车，重新加
                Boolean boolean3 = cartItem.getConsignmentId() == null && consigneeCommMap.get(cartItem.getCommodityId()) != null;
                Boolean boolean4 = cartItem.getConsignmentId() != null
                           && ( (consigneeCommMap.get(cartItem.getCommodityId()) == null && cartItem.getConsignmentId() > 0 )
                                   ||
                                 ( consigneeCommMap.get(cartItem.getCommodityId()) != null && !cartItem.getConsignmentId().equals(consigneeCommMap.get(cartItem.getCommodityId())) )
                             );*/

                if(boolean1 || boolean2){
                    addCommodityList.add(cartItem);
                    deleteCommodityList.add(cartItem);
                }
            }

            // 如果当前购物车下，需要重新加购物车的商品size = 购物车明细size.则删除主购物车
            // 否则重新更新主购物车的商品品类数
            if(shoppingCartItemList.size() == deleteCommodityList.size()){
                shoppingCartMapper.deleteByPrimaryKey(shoppingCartId);
            }else {
                if(deleteCommodityList.size() > 0){
                    ShoppingCart shoppingCart = new ShoppingCart();
                    shoppingCart.setId(shoppingCartId);
                    shoppingCart.setVarietyTotal(shoppingCartItemList.size() - deleteCommodityList.size());
                    shoppingCartMapper.updateByPrimaryKeySelective(shoppingCart);
                }
            }
            // 删除购物车明细
            if(CollectionUtils.isNotEmpty(deleteCommodityList)){
                List<Long> commodityIdList = deleteCommodityList.stream().map(item -> item.getCommodityId()).collect(Collectors.toList());
                // 先删除购物车明细商品
                Example ex = new Example(ShoppingCartItem.class);
                ex.createCriteria().andEqualTo("shoppingCartId", shoppingCartId).andIn("commodityId", commodityIdList);
                itemMapper.deleteByExample(ex);
            }
        }

        // 事务完成后，重新加入购物车
        if(CollectionUtils.isNotEmpty(addCommodityList)){
            TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronizationAdapter() {
                @Override
                public void afterCommit() {
                    List<ShoppingCartEntry> adminCartList = addCommodityList.stream().filter(p -> p.getAdminStatus().equals(YesOrNoEnums.YES.getCode())).collect(Collectors.toList());
                    List<ShoppingCartEntry> normalCartList = addCommodityList.stream().filter(p -> p.getAdminStatus().equals(YesOrNoEnums.NO.getCode())).collect(Collectors.toList());

                    if(CollectionUtils.isNotEmpty(normalCartList)){
                        // 循环添加购物车
                        addShoppingCart(normalCartList);
                    }

                    if(CollectionUtils.isNotEmpty(adminCartList)){
                        ThreadLocalUtils.setAdmin(Boolean.TRUE);
                        addShoppingCart(adminCartList);
                        ThreadLocalUtils.remove();
                    }
                }
            });
        }
        return Boolean.TRUE;
    }

    private void addShoppingCart(List<ShoppingCartEntry> cartList) {
        // 循环添加购物车
        for (ShoppingCartEntry cartEntry : cartList) {
            try {
                ShoppingCartVo cartVo = new ShoppingCartVo();
                cartVo.setCreateId(cartEntry.getCreateId());
                cartVo.setStoreId(cartEntry.getStoreId());
                cartVo.setEnterpriseId(78L);
                cartVo.setInternal(true);
                cartVo.setCommodityId(cartEntry.getCommodityId());
                cartVo.setQuantity(cartEntry.getQuantity());
                cartVo.setStallId(cartEntry.getStallId());
                shoppingCartService.addShoppingCart(cartVo);
            } catch (Exception e) {
                log.error("处理自动订货购物车、代销商户购物车异常：storeId:" + cartEntry.getStoreId() + "commodityId:" + cartEntry.getCommodityId(), e);

                StringBuffer sb = new StringBuffer();
                sb.append("处理自动订货购物车、代销商户购物车异常：storeId:" + cartEntry.getStoreId() + "commodityId:" + cartEntry.getCommodityId());
                weChatSendMessageService.sendWeChatMessage(sb.toString());
            }
        }
    }
}
