package com.pinshang.qingyun.order.mapper;

import com.pinshang.qingyun.base.mybatis.MyMapper;
import com.pinshang.qingyun.order.dto.OrderInfoODTO;
import com.pinshang.qingyun.order.dto.pf.PfOrderItemInfoODTO;
import com.pinshang.qingyun.order.dto.sync.SyncOrderIDTO;
import com.pinshang.qingyun.order.dto.sync.SyncOrderListODTO;
import com.pinshang.qingyun.order.dto.xda.XdaOrderItemInfoODTO;
import com.pinshang.qingyun.order.model.order.OrderListGift;
import com.pinshang.qingyun.order.vo.order.OrderReportJobVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
@Mapper
@Repository
public interface OrderListGiftMapper extends MyMapper<OrderListGift>{
    Integer batchUpdateOrderGiftList(@Param("orderGiftUpdateList") List<OrderListGift> orderGiftUpdateList);

    /**
     * 查询  订单项列表
     * 
     * @param orderId
     * @return
     */
     List<XdaOrderItemInfoODTO> selectOrderItemList(@Param("orderId")Long orderId);

	List<PfOrderItemInfoODTO> selectPfOrderItemList(@Param("orderId") Long orderId);

    int deleteGiftOrderList(@Param("orderId") Long orderId, @Param("commodityIdList") List<Long> commodityIdList);

    int deleteOrderList(@Param("orderId") Long orderId, @Param("commodityIdList") List<Long> commodityIdList);

    List<OrderInfoODTO> selectGiftOrderList(@Param("orderId") Long orderId, @Param("commodityIdList") List<Long> commodityIdLi);

    int deleteSubOrderItem(@Param("subOrderItemIdList") List<Long> subOrderItemIdList);

    List<OrderListGift> queryOrderGiftListByOrderId(@Param("orderId") Long orderId);

    Long selectOrderListGiftJob(OrderReportJobVO vo);

    List<SyncOrderListODTO> syncOrderItemGiftList(SyncOrderIDTO syncOrderIDTO);

    Integer updateBatchOrderListGift(@Param("orderUpdateList") List<OrderListGift> orderUpdateList);

}
