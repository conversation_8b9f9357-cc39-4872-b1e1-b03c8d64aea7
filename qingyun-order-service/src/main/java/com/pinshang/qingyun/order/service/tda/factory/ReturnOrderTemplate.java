package com.pinshang.qingyun.order.service.tda.factory;

import com.pinshang.qingyun.base.configure.expand.QYAssert;
import com.pinshang.qingyun.base.enums.YesOrNoEnums;
import com.pinshang.qingyun.order.dto.xda.tda.ReturnOrderItemODTO;
import com.pinshang.qingyun.order.dto.xda.tda.SaveReturnOrderItemDTO;
import com.pinshang.qingyun.order.dto.xda.tda.SaveReturnOrderODTO;
import com.pinshang.qingyun.order.enums.CombTypeEnum;
import com.pinshang.qingyun.order.mapper.OrderListMapper;
import com.pinshang.qingyun.order.mapper.OrderMapper;
import com.pinshang.qingyun.order.mapper.XdaReturnOrderItemMapper;
import com.pinshang.qingyun.order.model.commodity.Commodity;
import com.pinshang.qingyun.order.model.order.Order;
import com.pinshang.qingyun.order.model.order.OrderList;
import com.pinshang.qingyun.order.model.xda.XdaReturnOrder;
import com.pinshang.qingyun.order.model.xda.XdaReturnOrderItem;
import com.pinshang.qingyun.order.service.CommodityService;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 保存退货单模板
 *
 * <AUTHOR>
 */
@Component
public abstract class ReturnOrderTemplate {
    @Autowired
    private OrderMapper orderMapper;
    @Autowired
    private OrderListMapper orderListMapper;
    @Autowired
    private CommodityService commodityService;
    @Autowired
    private XdaReturnOrderItemMapper xdaReturnOrderItemMapper;

    protected abstract Boolean execute(SaveReturnOrderODTO saveReturnOrderDTO);

    /**
     * 模板方法，执行保存退货单流程
     */
    protected abstract Boolean saveReturnOrder(SaveReturnOrderODTO saveReturnOrderDTO);

    /**
     * 判断是否有投诉过的商品（称重品可以重复投诉）
     */
    protected void checkConflictCommodity(SaveReturnOrderODTO saveReturnOrderDTO, XdaReturnOrder x) {
        if (CollectionUtils.isEmpty(saveReturnOrderDTO.getComplaintItemList())) {
            return;
        }
        List<String> complaintCommodityIdList = saveReturnOrderDTO.getComplaintItemList().stream()
                // 过滤掉称重品
                .filter(item -> item.getIsWeight() == 0)
                .map(y -> y.getCommodityId() + "_" + y.getCommodityPrice().stripTrailingZeros())
                .distinct()
                .collect(Collectors.toList());

        List<ReturnOrderItemODTO> xdaReturnOrderItems = xdaReturnOrderItemMapper.queryByReturnOrderId(x.getId());

        // 创建一个商品ID到商品名称的映射
        Map<String, String> itemCommodityIdNameMap = xdaReturnOrderItems.stream()
                // 过滤掉称重品 和已取消的 已删除的
                .filter(item -> Objects.equals(item.getStatus(), 0) && Objects.equals(item.getDelFlag(), 0) && Objects.equals(item.getIsWeight(), 0))
                .collect(Collectors.toMap(
                        item -> item.getCommodityId() + "_" + item.getCommodityPrice().stripTrailingZeros(),
                        ReturnOrderItemODTO::getCommodityAppName
                ));

        // 获取退货订单项的商品ID列表
        Set<String> itemCommodityIdSet = itemCommodityIdNameMap.keySet();

        // 找到交集
        Set<String> intersection = complaintCommodityIdList.stream()
                .filter(itemCommodityIdSet::contains)
                .collect(Collectors.toSet());

        if (!intersection.isEmpty()) {
            // 获取第一个冲突的商品ID和商品名称
            String conflictCommodityId = intersection.iterator().next();
            String conflictCommodityName = itemCommodityIdNameMap.get(conflictCommodityId);

            // 抛出异常，包含商品ID和商品名称
            QYAssert.notNull(null, "商品 " + conflictCommodityName + " 选择的送货日期这天已发起投诉，不可再投诉!");
        }
    }


    protected void buildComplaintItemList(SaveReturnOrderODTO saveReturnOrderDTO) {
        Order order = orderMapper.selectByPrimaryKey(saveReturnOrderDTO.getOrderId());
        if (Objects.isNull(order)) {
            return;
        }
        List<OrderList> orderLists = orderListMapper.queryOrderListByOrderId(saveReturnOrderDTO.getOrderId());
        if (CollectionUtils.isEmpty(orderLists)) {
            return;
        }
        //过滤掉组合子品，退货单只投诉非组合品或者组合品
        List<OrderList> nonCombChildOrderList = orderLists.stream()
                .filter(item -> Objects.equals(item.getCombType(), CombTypeEnum.COMB.getCode()) || Objects.equals(item.getCombType(), CombTypeEnum.NOT_COMB.getCode()))
                .collect(Collectors.toList());
        //回写订单实发数量
        saveReturnOrderDTO.setComplaintTotalMoney(order.getRealTotalPrice());
        List<SaveReturnOrderItemDTO> complaintItemList = new ArrayList<>();
        for (OrderList orderList : nonCombChildOrderList) {
            SaveReturnOrderItemDTO saveReturnOrderItemDTO = new SaveReturnOrderItemDTO();
            saveReturnOrderItemDTO.setCommodityId(orderList.getCommodityId());
            saveReturnOrderItemDTO.setRealDeliveryQuantity(orderList.getRealQuantity());
            saveReturnOrderItemDTO.setCommodityPrice(orderList.getCommodityPrice());
            complaintItemList.add(saveReturnOrderItemDTO);
        }
        saveReturnOrderDTO.setComplaintItemList(complaintItemList);
    }

    protected void buildIsWeight(SaveReturnOrderODTO saveReturnOrderDTO) {
        if (CollectionUtils.isEmpty(saveReturnOrderDTO.getComplaintItemList())) {
            return;
        }
        List<Long> commodityIdList = saveReturnOrderDTO.getComplaintItemList().stream().map(SaveReturnOrderItemDTO::getCommodityId).distinct().collect(Collectors.toList());
        //查询商品信息（是否是称重品）
        Map<Long, Commodity> commodityInfoByIdMap = commodityService.findCommodityInfoByIdMap(commodityIdList);
        saveReturnOrderDTO.getComplaintItemList().forEach(item -> {
            Commodity commodity = commodityInfoByIdMap.get(item.getCommodityId());
            if (commodity != null) {
                item.setIsWeight(commodity.getIsWeight());
            }
        });
    }

    /**
     * 计算申请详情
     */
    protected void calculateApplyDetails(BigDecimal applyQuantity, SaveReturnOrderItemDTO dto, Commodity commodity, XdaReturnOrderItem xdaReturnOrderItem) {
        BigDecimal commodityPackageSpec = commodity.getCommodityPackageSpec();
        int applyNumber;
        BigDecimal applyMoney;
        //处理 品鲜后管投诉 无单价的情况
        BigDecimal commodityPrice = Objects.isNull(dto.getCommodityPrice()) ? BigDecimal.ZERO : dto.getCommodityPrice();
        if (Objects.equals(commodity.getIsWeight(), YesOrNoEnums.YES.getCode())) {
            //通达称重品份数永远是1
            applyNumber = 1;
            //比较实发数量和包装规格
            if (applyQuantity.compareTo(commodityPackageSpec) <= 0) {
                //实发小于包装规格，按实发数量计算金额，因为少发的钱已经退了
                applyMoney = applyQuantity.multiply(commodityPrice).setScale(2, RoundingMode.HALF_UP);
            } else {
                //实发大于包装规格，按包装规格计算金额，因为多发不收钱
                applyMoney = commodityPackageSpec.multiply(commodityPrice).setScale(2, RoundingMode.HALF_UP);
            }
        } else {
            //申请份数=申请数量/包装规格 向上取整
            applyNumber = applyQuantity.divide(commodityPackageSpec, 0, RoundingMode.CEILING).intValue();
            //申请金额= 申请数量*促销后单价
            applyMoney = applyQuantity.multiply(commodityPrice).setScale(2, RoundingMode.HALF_UP);
        }
        xdaReturnOrderItem.setApplyNumber(applyNumber);
        xdaReturnOrderItem.setApplyMoney(applyMoney);
        xdaReturnOrderItem.setApplyQuantity(applyQuantity);
    }

}