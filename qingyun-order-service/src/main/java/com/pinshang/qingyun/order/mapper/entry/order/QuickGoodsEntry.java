package com.pinshang.qingyun.order.mapper.entry.order;

import com.pinshang.qingyun.order.mapper.entry.SupplyBaseEntry;
import com.pinshang.qingyun.renderer.annotation.FieldRender;
import com.pinshang.qingyun.renderer.constant.RenderFieldHelper;
import com.pinshang.qingyun.renderer.enums.FieldTypeEnum;

import java.math.BigDecimal;

/*
 * 快速订货
 */
public class QuickGoodsEntry extends SupplyBaseEntry {
	private Long shopId;
	private String shopCode;
	private String shopName;
	private Long commodityId;
	private String commodityCode;
	private String commodityName;
	private String commoditySpec;
//	private String supplyStartTime;
//	private String supplyEndTime;
	private String supplyStartTime;
	private String supplyEndTime;
	private BigDecimal stockQuantity;
	private BigDecimal price;
	private BigDecimal quantity;
	private Boolean pass;
	private String remark;
	private Integer logisticsModel;
	private BigDecimal salesBoxCapacity;
	@FieldRender(fieldType = FieldTypeEnum.DICTIONARY,fieldName = RenderFieldHelper.Dictionary.optionName,keyName = "commodityUnitId")
	private String commodityUnit;
	private String supplyTime;
    private BigDecimal commodityPackageSpec;
    private Long shares;
    private String storeCode;
	private Long storeId;
	private Integer shopType;
	@FieldRender(fieldType = FieldTypeEnum.COMMODITY_KIND,fieldName = RenderFieldHelper.CommodityKind.commodityFirstKindName,keyName = "commodityId")
	private String commodityFirstCateName;
	private Long commodityUnitId;
	private Long stallId;
	private String stallCode;
	private String stallName;
	private Integer managementMode;

	public Integer getManagementMode() {
		return managementMode;
	}

	public void setManagementMode(Integer managementMode) {
		this.managementMode = managementMode;
	}

	public Long getStallId() {
		return stallId;
	}

	public void setStallId(Long stallId) {
		this.stallId = stallId;
	}

	public String getStallCode() {
		return stallCode;
	}

	public void setStallCode(String stallCode) {
		this.stallCode = stallCode;
	}

	public String getStallName() {
		return stallName;
	}

	public void setStallName(String stallName) {
		this.stallName = stallName;
	}

	public String getCommodityFirstCateName() {
		return commodityFirstCateName;
	}

	public void setCommodityFirstCateName(String commodityFirstCateName) {
		this.commodityFirstCateName = commodityFirstCateName;
	}

	public Integer getShopType() {
		return shopType;
	}

	public void setShopType(Integer shopType) {
		this.shopType = shopType;
	}

	private String orderTime;
	private String deliveryBatch;

	public String getOrderTime() {
		return orderTime;
	}

	public void setOrderTime(String orderTime) {
		this.orderTime = orderTime;
	}

	public String getDeliveryBatch() {
		return deliveryBatch;
	}

	public void setDeliveryBatch(String deliveryBatch) {
		this.deliveryBatch = deliveryBatch;
	}

	public Long getShopId() {
		return shopId;
	}

	public void setShopId(Long shopId) {
		this.shopId = shopId;
	}

	public Long getStoreId() {
		return storeId;
	}

	public void setStoreId(Long storeId) {
		this.storeId = storeId;
	}

	public Long getShares() {
		return shares;
	}

	public void setShares(Long shares) {
		this.shares = shares;
	}

	public BigDecimal getCommodityPackageSpec() {
		return commodityPackageSpec;
	}

	public void setCommodityPackageSpec(BigDecimal commodityPackageSpec) {
		this.commodityPackageSpec = commodityPackageSpec;
	}

	public String getStoreCode() {
		return storeCode;
	}

	public void setStoreCode(String storeCode) {
		this.storeCode = storeCode;
	}

	public String getCommodityUnit() {
		return null == commodityUnit ? "" : commodityUnit;
	}
	public void setCommodityUnit(String commodityUnit) {
		this.commodityUnit = commodityUnit;
	}
	public String getCommodityCode() {
		return commodityCode;
	}
	public void setCommodityCode(String commodityCode) {
		this.commodityCode = commodityCode;
	}
	public String getCommodityName() {
		return commodityName;
	}
	public void setCommodityName(String commodityName) {
		this.commodityName = commodityName;
	}
	public String getCommoditySpec() {
		return commoditySpec;
	}
	public void setCommoditySpec(String commoditySpec) {
		this.commoditySpec = commoditySpec;
	}
//	public String getSupplyStartTime() {
//		return supplyStartTime;
//	}
//	public void setSupplyStartTime(String supplyStartTime) {
//		this.supplyStartTime = supplyStartTime;
//	}
//	public String getSupplyEndTime() {
//		return supplyEndTime;
//	}
//	public void setSupplyEndTime(String supplyEndTime) {
//		this.supplyEndTime = supplyEndTime;
//	}
	public BigDecimal getPrice() {
		return price;
	}
	public void setPrice(BigDecimal price) {
		this.price = price;
	}
	public BigDecimal getQuantity() {
		return quantity;
	}
	public void setQuantity(BigDecimal quantity) {
		this.quantity = quantity;
	}
	public Boolean getPass() {
		return pass;
	}
	public void setPass(Boolean pass) {
		this.pass = pass;
	}
	public String getRemark() {
		return remark;
	}
	public void setRemark(String remark) {
		this.remark = remark;
	}
	@Override
    public Integer getLogisticsModel() {
		return logisticsModel;
	}
	@Override
    public void setLogisticsModel(Integer logisticsModel) {
		this.logisticsModel = logisticsModel;
	}
	public BigDecimal getSalesBoxCapacity() {
		return salesBoxCapacity;
	}
	public void setSalesBoxCapacity(BigDecimal salesBoxCapacity) {
		this.salesBoxCapacity = salesBoxCapacity;
	}

	public Long getCommodityId() {
		return commodityId;
	}

	public void setCommodityId(Long commodityId) {
		this.commodityId = commodityId;
		if (commodityId != null) {
			this.productId = commodityId.toString();
		}
	}

	public BigDecimal getStockQuantity() {
		return stockQuantity;
	}

	public void setStockQuantity(BigDecimal stockQuantity) {
		this.stockQuantity = stockQuantity;
	}

	public String getSupplyTime() {
		return supplyTime;
	}

	public void setSupplyTime(String supplyTime) {
		this.supplyTime = supplyTime;
	}

	public String getShopCode() {
		return shopCode;
	}

	public void setShopCode(String shopCode) {
		this.shopCode = shopCode;
	}

	public String getShopName() {
		return shopName;
	}

	public void setShopName(String shopName) {
		this.shopName = shopName;
	}

	public Long getCommodityUnitId() {
		return commodityUnitId;
	}

	public void setCommodityUnitId(Long commodityUnitId) {
		this.commodityUnitId = commodityUnitId;
	}
}
