package com.pinshang.qingyun.order.mapper;

import com.pinshang.qingyun.base.mybatis.MyMapper;
import com.pinshang.qingyun.order.dto.order.SupplierBlackODTO;
import com.pinshang.qingyun.order.dto.order.SupplierBlackVo;
import com.pinshang.qingyun.order.mapper.entry.CommodityEntry;
import com.pinshang.qingyun.order.mapper.entry.MdShopOrderSettingEntry;
import com.pinshang.qingyun.order.model.shop.MdShopOrderSetting;
import com.pinshang.qingyun.order.vo.shop.MdShopOrderSettingVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

@Mapper
@Repository
public interface MdShopOrderSettingMapper extends MyMapper<MdShopOrderSetting> {
    /**
     * 获取门店下单设置列表
     * @param shopOrderSettingVo
     * @return
     */
     List<MdShopOrderSettingEntry> queryMdShopOrderSettingListByParams(MdShopOrderSettingVo shopOrderSettingVo);


     List<MdShopOrderSettingEntry> queryDefaultMdShopOrderSetting(@Param("commodityId") Long commodityId, @Param("supplierId")Long supplierId);

     List<CommodityEntry> queryCommodityEntryByCommodityCodes(@Param("commodityCodeList")List<String> commodityCodeList);

     List<MdShopOrderSettingEntry> queryMdShopOrderSettingByCommIds(@Param("commodityIdList") List<Long> commodityIdList);

    Integer updateMdShopOrderSettingByPrimaryKey(MdShopOrderSetting vo);

    List<MdShopOrderSettingEntry> queryMdShopOrderSettingListByMap(@Param("shopType")Integer shopType, @Param("map")Map<String, String> map);

    List<MdShopOrderSettingEntry> queryMdShopOrderSettingListByCommodityIds(@Param("shopType") Integer shopType, @Param("commodityIds") List<String> commodityIds, @Param("isCustom") boolean isCustom);

    List<MdShopOrderSettingEntry> queryCommonSettingByParam(@Param("commodityId")Long commodityId,@Param("supplierId") Long supplierId);

    List<String> getEndTimeList(@Param("nowHour") String nowHour);

    List<Long> queryCommodityIdsByStoreLogisticsModel(@Param("shopType") Integer shopType, @Param("logisticsModel") Integer logisticsModel);

    List<Long> findDirectCommodityList(@Param("shopType") Integer shopType, @Param("supplierId") Long supplierId);

    List<SupplierBlackODTO> queryOrderSupplierBlackPage(SupplierBlackVo vo);
    int deleteOrderSupplierBlack(@Param("supplierId") Long supplierId);
    Long countOrderSupplierBlack(@Param("supplierId") Long supplierId);
    int insertOrderSupplierBlack(@Param("supplierId") Long supplierId, @Param("userId") Long userId);
    List<Long> queryAllOrderSupplierBlackList();
}
