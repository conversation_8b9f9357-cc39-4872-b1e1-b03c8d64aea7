package com.pinshang.qingyun.order.service.cup.strategy;

import com.pinshang.qingyun.base.enums.YesOrNoEnums;
import com.pinshang.qingyun.box.utils.SpringUtil;
import com.pinshang.qingyun.order.dto.cup.EditOrderDto;
import com.pinshang.qingyun.order.mapper.StoreSettlementMapper;
import com.pinshang.qingyun.order.mapper.common.EmployeeUserMapper;
import com.pinshang.qingyun.order.mapper.entry.store.StoreSettlementEntry;
import com.pinshang.qingyun.order.model.order.Order;
import com.pinshang.qingyun.order.model.store.Store;
import com.pinshang.qingyun.order.service.StoreService;
import com.pinshang.qingyun.store.dto.storeSettlement.PaymentParentChildStoreSettlementODTO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.List;

@Component
public abstract class AbstractOrderDetail {

    @Autowired
    StoreService storeService;

    @Autowired
    EmployeeUserMapper employeeUserMapper;

    @Autowired
    StoreSettlementMapper storeSettlementMapper;


    abstract Order detailCheck(Long orderId);
    abstract void processDetail(Order order, EditOrderDto dto);


    public EditOrderDto detail(Long orderId){

        if (null == orderId) {
            return null;
        }

        Order order = detailCheck(orderId);

        EditOrderDto dto = new EditOrderDto();
        SpringUtil.copyProperties(order, dto);
        dto.setStoreIdStr(order.getStoreId().toString());
        Store store = storeService.findStoreByStoreId(order.getStoreId());
        String employeeName = null;
        if (null != store.getDeliverymanId()) {
            employeeName = employeeUserMapper.queryEmployeeUserByEmployeeId(store.getDeliverymanId());
            if (org.apache.commons.lang3.StringUtils.isNotBlank(employeeName))
                store.setDeliverymanName(employeeName);
        }
        if (null != store.getSupervisorId()) {
            employeeName = employeeUserMapper.queryEmployeeUserByEmployeeId(store.getSupervisorId());
            if (org.apache.commons.lang3.StringUtils.isNotBlank(employeeName))
                store.setSupervisorName(employeeName);
        }
        dto.setStore(store);

        PaymentParentChildStoreSettlementODTO storeSettlementODTO = storeService.queryStoreCollectPriceByStoreId(order.getStoreId(), false);
        if(YesOrNoEnums.YES.getCode().equals(storeSettlementODTO.getCollectStatus())) {
            dto.setCollectPrice(new Double(storeSettlementODTO.getCollectPrice() + ""));
        }

        processDetail(order,dto);

        return dto;
    }

}
