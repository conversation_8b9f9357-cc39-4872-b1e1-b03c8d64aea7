package com.pinshang.qingyun.order.mapper.entry.splitOrder;

import com.pinshang.qingyun.box.utils.SpringUtil;
import com.pinshang.qingyun.storage.dto.SubOrder2DeliveryOrderItemEntryIDTO;
import com.pinshang.qingyun.storage.dto.SubOrder2DeliveryOrderListEntryIDTO;
import lombok.Data;

import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/*
 * 查询subOrder记录,用于生成do,临时查询对象
 */
@Data
public class SubOrder2DeliveryOrderListEntry {
    private Long storeId;
    private Long subOrderId;
    private Long enterpriseId;
    private Long warehouseId;
    private Integer logisticsModel;
    private Integer varietyTotal;
    private Long supplierId;
    private Long referOrderId;
    private String referOrderCode;
    private Integer deliveryBatch;
    private Date orderTime;
    private Long userId;
    private Integer shopType;
    private Integer orderType;
    private Long storeTypeId;
    private Long shopId;
    private Long shiftStoreTypeId;
    private Integer presaleStatus;
    private Integer stockType;
    private Integer businessType;
    private Long stallId;

    //so明细
    private List<SubOrder2DeliveryOrderItemEntry> itemList;

    public static SubOrder2DeliveryOrderListEntryIDTO convert(SubOrder2DeliveryOrderListEntry entry) {
        SubOrder2DeliveryOrderListEntryIDTO entryIDTO = new SubOrder2DeliveryOrderListEntryIDTO();
        if (entry != null && SpringUtil.isNotEmpty(entry.getItemList())) {
            SpringUtil.copyProperties(entry, entryIDTO);
            List<SubOrder2DeliveryOrderItemEntryIDTO> itemEntryIDTOList = entry.getItemList().stream().map(SubOrder2DeliveryOrderItemEntry::convert).collect(Collectors.toList());
            entryIDTO.setItemList(itemEntryIDTOList);
        }
        return entryIDTO;
    }
}
