package com.pinshang.qingyun.order.mapper.entry.order;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;

import javax.persistence.Transient;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2018/10/12 17:05
 */
@Data
public class SubOrderMonitorEntry {
    /**
     * 主键id
     */
    private Long subOrderId;
    /**
     * 子单编号
     */
    private String subOrderCode;
    /**
     * 创建时间
     */
    private Date createTime;
    /**
     * 状态：0-未生成do单, 1-已生成do单， 2-取消
     */
    private Integer status;
    /**
     * 物流模式：0=直送，1＝配送，2＝直通
     */
    private Integer logisticsModel;
    /**
     * 仓库id
     */
    private Long warehouseId;
    /**
     * 仓库
     */
    private String warehouseName;

    /**
     * 供应商id
     */
    private Long supplierId;
    /**
     * 供应商
     */
    private String supplierName;

    /**
     * 品类数
     */
    private Integer varietyTotal;

    /**
     * 修改时间
     */
    private Date updateTime;
    /**
     *
     */
    private List<SubOrderItemMonitorEntry> subOrderItem;

    @Transient
    @JsonIgnore
    private transient Long commodityId;

    /**
     * 1=依据大仓, 2=不限量订货,3=限量供应
     */
    private Integer stockType;

    /**
     * 库存依据描述
     */
    private String stockTypeDesc;
}
