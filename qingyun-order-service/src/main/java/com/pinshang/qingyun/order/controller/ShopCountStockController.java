package com.pinshang.qingyun.order.controller;

import com.alibaba.excel.EasyExcel;
import com.github.pagehelper.PageInfo;
import com.pinshang.qingyun.base.util.ExcelUtil;
import com.pinshang.qingyun.box.utils.SpringUtil;
import com.pinshang.qingyun.order.dto.*;
import com.pinshang.qingyun.order.model.shop.Shop;
import com.pinshang.qingyun.order.service.ShopCountStockService;
import com.pinshang.qingyun.order.service.ShopService;
import com.pinshang.qingyun.renderer.annotation.MethodRender;
import com.pinshang.qingyun.renderer.service.IRenderService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * @ClassName ShopCountStockController
 * <AUTHOR>
 * @Date 2022/12/8 18:14
 * @Description ShopCountStockController
 * @Version 1.0
 *
 * 迁移至order-manage
 */
@Deprecated
@RequestMapping("/shopCountStock")
@RestController
public class ShopCountStockController {
    @Autowired
    private ShopCountStockService shopCountStockService;

    @Autowired
    private IRenderService renderService;

    @Autowired
    private ShopService shopService;

    @MethodRender
    @RequestMapping(value = "/page", method = RequestMethod.POST)
    public PageInfo<ShopCountStockPageODTO> page(@RequestBody ShopCountStockPageIDTO idto){
        return shopCountStockService.page(idto);
    }

    @MethodRender
    @RequestMapping(value = "/detail", method = RequestMethod.POST)
    public ShopCountStockDetailODTO detail(@RequestBody ShopCountStockDetailIDTO idto){
        ShopCountStockDetailODTO odto = shopCountStockService.detail(idto);
        if(null != odto.getItemList() && SpringUtil.isNotEmpty(odto.getItemList().getList())){
            renderService.render(odto.getItemList().getList(), "/shopCountStock/detail");
        }
        return odto;
    }

    @RequestMapping(value = "/export", method = RequestMethod.GET)
    public void export(ShopCountStockDetailIDTO idto, HttpServletResponse response) throws IOException{
        long qS = System.currentTimeMillis();

        idto.setPageNo(1);
        idto.setPageSize(Integer.MAX_VALUE);
        ShopCountStockDetailODTO odto = shopCountStockService.detail(idto);
        List<ShopCountStockDetailItemODTO> list = new ArrayList<>();
        if(null != odto.getItemList()){
            list = odto.getItemList().getList();
        }
        if(SpringUtil.isNotEmpty(list)){
            renderService.render(list, "/shopCountStock/export");
        }else{
            list = new ArrayList<>();
        }
        List<Shop> shopInfo = shopService.getShopByIdList(Collections.singletonList(odto.getShopId()));
        if(SpringUtil.isNotEmpty(shopInfo)){
            odto.setShopName(shopInfo.get(0).getShopName());
        }
        String fileName = idto.getOrderTime() +  odto.getShopName() + "送货点数" + qS;
        try {
            ExcelUtil.setFileNameAndHead( response,fileName);
            if (list.size() == 0){
                EasyExcel.write(response.getOutputStream()).autoCloseStream(Boolean.FALSE);
            }else {
                EasyExcel.write(response.getOutputStream(),ShopCountStockDetailItemODTO.class).autoCloseStream(Boolean.FALSE).sheet("送货点数").doWrite(list);
            }
        } catch (Exception e) {
            ExcelUtil.setExceptionResponse( response );
        }
    }
}
