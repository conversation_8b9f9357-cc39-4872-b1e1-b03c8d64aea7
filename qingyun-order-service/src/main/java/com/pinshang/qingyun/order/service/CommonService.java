package com.pinshang.qingyun.order.service;

import com.alibaba.fastjson.JSON;
import com.pinshang.qingyun.base.enums.YesOrNoEnums;
import com.pinshang.qingyun.base.enums.shop.ShopTypeEnums;
import com.pinshang.qingyun.box.utils.BeanCloneUtils;
import com.pinshang.qingyun.box.utils.SpringUtil;
import com.pinshang.qingyun.common.service.DictionaryClient;
import com.pinshang.qingyun.infrastructure.components.IMqSenderComponent;
import com.pinshang.qingyun.infrastructure.components.params.mq.MqMessage;
import com.pinshang.qingyun.kafka.MessageOperationType;
import com.pinshang.qingyun.kafka.MessageType;
import com.pinshang.qingyun.kafka.MessageWrapper;
import com.pinshang.qingyun.msg.dto.zsmd.MessageDTO;
import com.pinshang.qingyun.msg.service.zsmd.ZSMessageClient;
import com.pinshang.qingyun.order.mapper.CommodityMapper;
import com.pinshang.qingyun.order.mapper.UserMapper;
import com.pinshang.qingyun.order.mapper.entry.commodity.CommodityBasicEntry;
import com.pinshang.qingyun.order.mapper.entry.commodity.CommodityResultEntry;
import com.pinshang.qingyun.order.mapper.entry.commodity.ProductLimitEntry;
import com.pinshang.qingyun.order.mapper.entry.order.ShoppingCartEntry;
import com.pinshang.qingyun.order.mapper.entry.order.ShoppingCartItemEntry;
import com.pinshang.qingyun.order.model.commodity.Commodity;
import com.pinshang.qingyun.order.model.gift.GiftModel;
import com.pinshang.qingyun.order.model.gift.GiftModelCondition;
import com.pinshang.qingyun.order.model.gift.GiftProduct;
import com.pinshang.qingyun.order.model.order.User;
import com.pinshang.qingyun.order.model.promotion.Promotion;
import com.pinshang.qingyun.order.model.promotion.PromotionProduct;
import com.pinshang.qingyun.order.model.promotionStk.PromotionStk;
import com.pinshang.qingyun.order.model.promotionStk.PromotionStkCodeOrder;
import com.pinshang.qingyun.order.model.promotionStk.PromotionStkComm;
import com.pinshang.qingyun.order.util.StallUtils;
import com.pinshang.qingyun.order.vo.commodity.CommodityListRequestVO;
import com.pinshang.qingyun.order.vo.commodity.CommodityVO;
import com.pinshang.qingyun.price.dto.commodity.*;
import com.pinshang.qingyun.price.service.CommodityPriceClient;
import com.pinshang.qingyun.product.dto.category.CategoryInfoODTO;
import com.pinshang.qingyun.product.dto.commodity.CommodityDetailODTO;
import com.pinshang.qingyun.product.service.CategoryClient;
import com.pinshang.qingyun.product.service.CommodityClient;
import com.pinshang.qingyun.renderer.client.dto.DictionaryODTO;
import com.pinshang.qingyun.renderer.enums.FieldTypeEnum;
import com.pinshang.qingyun.renderer.service.IRenderService;
import com.pinshang.qingyun.smm.dto.org.OrgAndParentInfoODTO;
import com.pinshang.qingyun.smm.dto.org.SelectShopOrgInfoListIDTO;
import com.pinshang.qingyun.smm.service.OrgClient;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;

import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Service
public class CommonService {

    @Autowired
    private IMqSenderComponent mqSenderComponent;

    @Autowired
    private CommodityMapper commodityMapper;

    @Value("${application.name.switch}")
    private String applicationNameSwitch;
    @Autowired
    private CommodityPriceClient commodityPriceClient;
    @Autowired
    private DictionaryClient dictionaryClient;
    @Autowired
    private CategoryClient categoryClient;
    @Autowired
    private CommodityClient commodityClient;
    @Autowired
    private UserMapper userMapper;
    @Autowired
    private OrgClient orgClient;
    @Autowired
    private ZSMessageClient zsMessageClient;
    @Autowired
    private CommodityService commodityService;

    @Autowired
    private IRenderService renderService;

    @Async
    public void sendKafkaMessage(Object data,MessageType messageType,String topic,String optionType){
        try {
            Thread.sleep(3000);
        } catch (InterruptedException e) {
            log.error("Thread.sleep error:{}", e);
        }
        MessageWrapper messageWrapper = new MessageWrapper();
        messageWrapper.setUuid(UUID.randomUUID().toString());
        messageWrapper.setType(messageType);
        if(StringUtils.isBlank(optionType)){
            messageWrapper.setOptionType(MessageOperationType.INSERT);
        }else {
            if(optionType.equals(MessageOperationType.INSERT.getCode())){
                messageWrapper.setOptionType(MessageOperationType.INSERT);
            }else if(optionType.equals(MessageOperationType.UPDATE.getCode())){
                messageWrapper.setOptionType(MessageOperationType.UPDATE);
            }else if (optionType.equals(MessageOperationType.CANCEL.getCode())){
                messageWrapper.setOptionType(MessageOperationType.CANCEL);
            }else{
                messageWrapper.setOptionType(MessageOperationType.INSERT);
            }
        }
        messageWrapper.setData(data);

        String message = JSON.toJSONString(messageWrapper);
        log.info("topic = {}, message = {}", applicationNameSwitch + topic, message);
        try {
            mqSenderComponent.send(applicationNameSwitch + topic,
                    data,
                    MqMessage.MQ_KAFKA,
                    messageType.name(),
                    messageWrapper.getOptionType().name());
        }catch (Exception e){
            log.error("【发送结算相关信息】异常：\n error=" + e + "\n dto=" + JSON.toJSONString(data) + "\n message=" + message);
        }
    }


    /**
     * 获取商品条码map
     * @return
     */
    public Map<Long,String> getCommodityBarCodeMap(List<Long> commodityIdList, String barcode){
        List<Commodity> list = commodityMapper.findCommodityBarCodeByParam(commodityIdList,barcode);
        Map<Long, String> map = list.stream().collect(
                Collectors.toMap(Commodity::getId, Commodity::getBarCode));
        return map;
    }


    /**
     * 获取类别信息
     * @param cateIdList
     * @return
     */
    public Map<String, String> getCategroyMap(List<Long> cateIdList){
        Map<String, String> cateMap = new HashMap<>();
        List<CategoryInfoODTO> cateList = categoryClient.findCategoryByIds(cateIdList);
        if(CollectionUtils.isNotEmpty(cateList)){
            cateMap = cateList.stream().collect(Collectors.toMap(CategoryInfoODTO::getId,CategoryInfoODTO::getCateName,(key1 , key2)-> key2));
        }
        return cateMap;
    }

    /**
     * 获取工厂、生产组、车间信息
     * @param commodityIdList
     * @return
     */
    public Map<String, CommodityDetailODTO> getFactoryMap(List<Long> commodityIdList){
        Map<String, CommodityDetailODTO> factoryCommodityMap = new HashMap<>();
        List<CommodityDetailODTO> factoryCommodityList = commodityClient.findCommodityInfoByIdList(commodityIdList);
        if(CollectionUtils.isNotEmpty(factoryCommodityList)){
            factoryCommodityMap = factoryCommodityList.stream().collect(Collectors.toMap(CommodityDetailODTO::getCommodityId, Function.identity()));
        }
        return factoryCommodityMap;
    }

    /**
     * 获取用户信息
     * @param userIdList
     * @return
     */
    public Map<Long, User> getUserMap(List<Long> userIdList){
        Map<Long, User> userMap = new HashMap<>();
        List<User> userList = userMapper.getEmployeeUserByUserId(userIdList);
        if(CollectionUtils.isNotEmpty(userList)){
           userMap = userList.stream().collect(Collectors.toMap(User::getUserId, Function.identity()));
        }
        return userMap;
    }

    /**
     * 根据门店idList 获取上级组织
     * @param shopIdList
     * @return
     */
    public Map<Long, OrgAndParentInfoODTO> getOrgMap(List<Long> shopIdList){
        Map<Long, OrgAndParentInfoODTO> orgMap = new HashMap<>();
        SelectShopOrgInfoListIDTO selectShopOrgInfoListIDTO = new SelectShopOrgInfoListIDTO();
        selectShopOrgInfoListIDTO.setShopIdList(shopIdList);
        List<OrgAndParentInfoODTO> orgList = orgClient.selectShopOrgInfoList(selectShopOrgInfoListIDTO);
        if(CollectionUtils.isNotEmpty(orgList)){
            orgMap = orgList.stream().collect(Collectors.toMap(OrgAndParentInfoODTO::getRefObjId, Function.identity()));
        }
        return orgMap;
    }

    /**
     * 设置购物车明细----商品信息
     * @param entryList
     */
    public void setShopCartItemInfo(List<ShoppingCartEntry> entryList){
        if(CollectionUtils.isNotEmpty(entryList)){
            // 并且是否速冻根据凑整商品组来定
            List<Long> freezeGroupList = commodityService.getCommodityFreezeGroup();

            List<Long> commodityIdList = new ArrayList<>();
            for(ShoppingCartEntry cartEntry : entryList){
                List<ShoppingCartItemEntry> cartItemList = cartEntry.getItems();
                if(CollectionUtils.isNotEmpty(cartItemList)){
                    for(ShoppingCartItemEntry itemEntry : cartItemList){
                        commodityIdList.add(itemEntry.getCommodityId());
                    }
                }
            }

            CommodityVO vo = new CommodityVO();
            vo.setCommodityIdList(commodityIdList);
            List<CommodityBasicEntry> basicEntryList = commodityMapper.findCommodityBasicListByParam(vo);
            Map<String, CommodityBasicEntry> commMap = basicEntryList.stream().collect(Collectors.toMap(CommodityBasicEntry::getCommodityId, Function.identity()));

            Set<Long> dictionaryIdSet = new HashSet<>();
            for (CommodityBasicEntry commodityBasicEntry : basicEntryList) {
                dictionaryIdSet.add(commodityBasicEntry.getCommodityUnitId());
                dictionaryIdSet.add(commodityBasicEntry.getCommodityCycleId());
            }
            Map<String, DictionaryODTO> dictionaryMap = listDictionarysByIdSet(dictionaryIdSet);

            for(ShoppingCartEntry cartEntry : entryList){
                List<ShoppingCartItemEntry> cartItemList = cartEntry.getItems();
                Integer shopType = cartEntry.getShopType();
                if(CollectionUtils.isNotEmpty(cartItemList)){
                    for(ShoppingCartItemEntry itemEntry : cartItemList){
                        CommodityBasicEntry basicEntry = commMap.get(itemEntry.getCommodityId() + "");
                        if(basicEntry != null){
                            BeanUtils.copyProperties(basicEntry,itemEntry);
                            if(ShopTypeEnums.XD.getCode().equals(shopType) || StallUtils.isStallSubcontractor(cartEntry.getManagementMode())){
                                itemEntry.setSalesBoxCapacity(basicEntry.getXdSalesBoxCapacity());
                            }

                            itemEntry.setCommodityUnit(null != dictionaryMap.get(itemEntry.getCommodityUnitId() + "" ) ? dictionaryMap.get(itemEntry.getCommodityUnitId() + "").getOptionName() : "");
                            itemEntry.setNewProductFlag(null != dictionaryMap.get(itemEntry.getCommodityCycleId() + "" ) ? dictionaryMap.get(itemEntry.getCommodityCycleId() + "").getOptionName() : "");
                            // 冻品凑整商品组
                            itemEntry.setFrozen(freezeGroupList.contains(itemEntry.getCommodityId()) ? YesOrNoEnums.YES.getCode() : YesOrNoEnums.NO.getCode());
                        }
                    }
                }
            }
        }
    }


    /**
     * 从renderService获取字典信息
     *
     * @return
     */
    public Map<String, DictionaryODTO> listDictionarysByIdSet(Set<Long> dictionaryIdSet) {
        if (SpringUtil.isEmpty(dictionaryIdSet)) {
            return Collections.emptyMap();
        }
        dictionaryIdSet.remove(null);
        if (SpringUtil.isEmpty(dictionaryIdSet)) {
            return Collections.emptyMap();
        }

        List<Long> dictionaryIdList = new ArrayList<>(dictionaryIdSet);
        List<DictionaryODTO> dataByIds = renderService.getDataById(dictionaryIdList, FieldTypeEnum.DICTIONARY, DictionaryODTO.class);
        if (SpringUtil.isEmpty(dataByIds)) {
            return Collections.emptyMap();
        }

        return dataByIds.stream().collect(Collectors.toMap(DictionaryODTO::getId, Function.identity(), (key1, key2) -> key2));
    }

    /**
     * 查询门店的所有特价商品code和price
     * @param vo
     * @return
     */
    public List<CommodityResultEntry> findPromotionCommodityCodeList(CommodityListRequestVO vo){
        List<CommodityResultEntry> entryList = new ArrayList<>();
        //List<CommodityResultODTO> list = commodityPriceClient.findPromotionCommodityCodeList(vo.getStoreId(), vo.getOrderTime());
        List<PromotionODTO> promotionList = commodityPriceClient.findCommodityPromotionByStoreId(vo.getStoreId(), vo.getOrderTime());
        Map<String, PromotionProduct> priceMap = new HashMap<>();
        if(SpringUtil.isNotEmpty(promotionList)){
            promotionList.forEach(p->{
                List<PromotionProduct> promotionProductList = findPromotionProductByPromotionId(p.getId().toString());
                if(SpringUtil.isNotEmpty(promotionProductList)){
                    promotionProductList.forEach(pp->{
                        priceMap.put(pp.getProductCode(), pp);
                    });
                }
            });
        }

        priceMap.forEach((key, value) -> {
            CommodityResultEntry resultEntry = new CommodityResultEntry();
            resultEntry.setCommodityCode(value.getProductCode());
            resultEntry.setCommodityPrice(new BigDecimal(value.getPrice()));
            resultEntry.setCommodityNumberLimit(new BigDecimal(value.getLimitNumber()));
            entryList.add(resultEntry);
        });
        /*if(CollectionUtils.isNotEmpty(list)){
            entryList = BeanUtil.copyProperties(list,CommodityResultEntry.class);
        }*/
        return entryList;
    }

    /**
     * 促销价格
     * @param storeId
     * @param orderTime
     * @return
     */
    public List<Promotion> findCommodityPromotionByStoreId(String storeId,String orderTime){
        List<Promotion> promotionList = new ArrayList<>();
        List<PromotionODTO> list = commodityPriceClient.findCommodityPromotionByStoreId(storeId,orderTime);
        if(CollectionUtils.isNotEmpty(list)){
            promotionList = BeanCloneUtils.copyTo(list,Promotion.class);
        }
        return promotionList;
    }

    /**
     * 促销价格
     * @param promotionId
     * @return
     */
    public List<PromotionProduct> findPromotionProductByPromotionId(String promotionId){
        List<PromotionProduct> promotionProductList = new ArrayList<>();
        List<PromotionProductODTO> list = commodityPriceClient.findPromotionProductByPromotionId(promotionId);
        if(CollectionUtils.isNotEmpty(list)){
            promotionProductList = BeanCloneUtils.copyTo(list,PromotionProduct.class);
        }
        return promotionProductList;
    }

    /**
     * 根据storeId、orderTime 获取所有特价商品信息列表
     * @param storeId
     * @param orderTime
     * @return
     */
    public Map<String, PromotionProduct> findPromotionPrice(String storeId,String orderTime){
        Map<String, PromotionProduct> priceMap = new HashMap<>();
        // 多特价
        List<PromotionODTO> promotionList = commodityPriceClient.findCommodityPromotionByStoreId(storeId,orderTime);
        if(SpringUtil.isNotEmpty(promotionList)){
            promotionList.forEach(p->{
                List<PromotionProduct> promotionProductList = findPromotionProductByPromotionId(p.getId().toString());
                if(SpringUtil.isNotEmpty(promotionProductList)){
                    promotionProductList.forEach(pp->{
                        pp.setStartTime(p.getStartTime());
                        pp.setEndTime(p.getEndTime());
                        priceMap.put(pp.getProductCode(), pp);
                    });
                }
            });
        }
        return priceMap;
    }

    /**
     * 查询配货方案列表(可能同时有多个配货方案在生效) t_promotion_stk(id)
     * @param paramMap
     * @return
     */
    public List<PromotionStk> findDistributionByStoreId(HashMap<String,Object> paramMap){
        List<PromotionStk> promotionStkList = new ArrayList<>();
        String storeId = (String)paramMap.get("storeId");
        String orderTime = (String)paramMap.get("orderTime");
        List<PromotionStkODTO> list = commodityPriceClient.findDistributionByStoreId(storeId, orderTime);
        if(CollectionUtils.isNotEmpty(list)){
            promotionStkList = BeanCloneUtils.copyTo(list,PromotionStk.class);
        }
        return promotionStkList;
    }

    /**
     * 获取一个有效的配货条件
     * @param promotionId
     * @return
     */
    public List<PromotionStkCodeOrder> findPromotionStkCodeOrderByPromotionId(Long promotionId){
        List<PromotionStkCodeOrder> promotionStkCodeOrderList = new ArrayList<>();
        List<PromotionStkCodeOrderODTO> list = commodityPriceClient.findPromotionStkCodeOrderByPromotionId(promotionId);
        if(CollectionUtils.isNotEmpty(list)){
            promotionStkCodeOrderList = BeanCloneUtils.copyTo(list,PromotionStkCodeOrder.class);
        }
        return promotionStkCodeOrderList;
    }

    /**
     * 批量获取有效的配货条件
     * @param promotionIdList
     * @return
     */
    public List<PromotionStkCodeOrder> findPromotionStkCodeOrderByPromotionIdList(List<Long> promotionIdList){
        List<PromotionStkCodeOrder> promotionStkCodeOrderList = new ArrayList<>();
        List<PromotionStkCodeOrderODTO> list = commodityPriceClient.findPromotionStkCodeOrderByPromotionIdList(promotionIdList);
        if(CollectionUtils.isNotEmpty(list)){
            promotionStkCodeOrderList = BeanCloneUtils.copyTo(list,PromotionStkCodeOrder.class);
        }
        return promotionStkCodeOrderList;
    }

    /**
     * 获取配货的商品列表 t_promotion_stk_comm
     * @param stkCodeId
     * @return
     */
    public List<PromotionStkComm> findPromotionStkCommByStkCodeId(Long stkCodeId){
        List<PromotionStkComm> promotionStkCommList = new ArrayList<>();
        List<PromotionStkCommODTO> list = commodityPriceClient.findPromotionStkCommByStkCodeId(stkCodeId);
        if(CollectionUtils.isNotEmpty(list)){
            promotionStkCommList = BeanCloneUtils.copyTo(list,PromotionStkComm.class);
        }
        return promotionStkCommList;
    }

    /**
     * 批量获取配货的商品列表 t_promotion_stk_comm
     * @param stkCodeIdList
     * @return
     */
    public List<PromotionStkComm> findPromotionStkCommByStkCodeIdList(List<Long> stkCodeIdList){
        List<PromotionStkComm> promotionStkCommList = new ArrayList<>();
        List<PromotionStkCommODTO> list = commodityPriceClient.findPromotionStkCommByStkCodeIdList(stkCodeIdList);
        if(CollectionUtils.isNotEmpty(list)){
            promotionStkCommList = BeanCloneUtils.copyTo(list,PromotionStkComm.class);
        }
        return promotionStkCommList;
    }



    /**
     *查找产品限量(创建订单检查限量,购物车详情查询限量,订货列表限量)
     * @param storeId
     * @return
     */
    public String findProductPriceModelIdByStoreId(String storeId){
        return commodityPriceClient.findProductPriceModelIdByStoreId(storeId);
    }
    /**
     *查找客户价格方案限量(创建订单检查限量,购物车详情查询限量,订货列表限量)
     * @param storeId
     * @return
     */
    public List<ProductLimitEntry> findCustomerLimitByStoreId(String storeId){
        List<ProductLimitEntry> productLimitList = new ArrayList<>();
        List<ProductLimitODTO> list = commodityPriceClient.findCustomerLimitByStoreId(storeId);
        if(CollectionUtils.isNotEmpty(list)){
            productLimitList = BeanCloneUtils.copyTo(list,ProductLimitEntry.class);
        }
        return productLimitList;
    }

    /**
     * 产品价格方案限量(创建订单检查限量,购物车详情查询限量,订货列表限量)
     * @param productPriceModelId
     * @return
     */
    public List<ProductLimitEntry> findProductPriceModelLimitByPriceModelId(String productPriceModelId){
        List<ProductLimitEntry> productLimitList = new ArrayList<>();
        List<ProductLimitODTO> list = commodityPriceClient.findProductPriceModelLimitByPriceModelId(productPriceModelId);
        if(CollectionUtils.isNotEmpty(list)){
            productLimitList = BeanCloneUtils.copyTo(list,ProductLimitEntry.class);
        }
        return productLimitList;
    }

    /**
     * 库存限量(创建订单检查限量,购物车详情查询限量,订货列表限量)
     * @return
     */
    public List<ProductLimitEntry> findCommodityLimitByNowData(String orderTime){
        List<ProductLimitEntry> productLimitList = new ArrayList<>();
        List<ProductLimitODTO> list = commodityPriceClient.findCommodityLimitByNowData(orderTime);
        if(CollectionUtils.isNotEmpty(list)){
            productLimitList = BeanCloneUtils.copyTo(list,ProductLimitEntry.class);
        }
        return productLimitList;
    }

    /**
     * 查询门店对应的 赠品方案列表(创建订单)
     * @param paramMap
     * @return
     */
    public List<GiftModel> findGiftModelByStoreId(Map<String, Object> paramMap){
        String storeId = (String)paramMap.get("storeId");
        String orderTime = (String)paramMap.get("orderTime");
        List<GiftModel> giftModelList = new ArrayList<>();
        List<GiftModelODTO> list = commodityPriceClient.findGiftModelByStoreId(storeId, orderTime);
        if(CollectionUtils.isNotEmpty(list)){
            giftModelList = BeanCloneUtils.copyTo(list,GiftModel.class);
        }
        return giftModelList;
    }

    /**
     * 赠品方案 找出阶梯条件中满足订单金额的条件(创建订单)
     * @param giftModelId
     * @return
     */
    public List<GiftModelCondition> findGiftModelConditionByGiftModelId(Long giftModelId){
        List<GiftModelCondition> giftModelConditionList = new ArrayList<>();
        List<GiftModelConditionODTO> list = commodityPriceClient.findGiftModelConditionByGiftModelId(giftModelId);
        if(CollectionUtils.isNotEmpty(list)){
            giftModelConditionList = BeanCloneUtils.copyTo(list,GiftModelCondition.class);
        }
        return giftModelConditionList;
    }
    /**
     * 赠品方案 查询赠品方案商品(创建订单)
     * @return
     */
    public List<GiftProduct> findGiftProductByGiftModelConditionId(Long giftModelConditionId){
        List<GiftProduct> giftProductList = new ArrayList<>();
        List<GiftProductODTO> list = commodityPriceClient.findGiftProductByGiftModelConditionId(giftModelConditionId);
        if(CollectionUtils.isNotEmpty(list)){
            giftProductList = BeanCloneUtils.copyTo(list,GiftProduct.class);
        }
        return giftProductList;
    }

    /**
     * 未提交的订单消息提醒-----> 异步发送消息
     * @param messageList
     * @return
     */
    @Async
    public Boolean sendMessage(List<MessageDTO> messageList){
        zsMessageClient.sendMultiMessage(messageList);
        return Boolean.TRUE;
    }

    public User getUserById(Long userId) {
        Example example = new Example(User.class);
        example.selectProperties("userId", "employeeCode", "employeeName");
        example.createCriteria().andEqualTo("userId", userId);
        return userMapper.selectOneByExample(example);
    }
}
