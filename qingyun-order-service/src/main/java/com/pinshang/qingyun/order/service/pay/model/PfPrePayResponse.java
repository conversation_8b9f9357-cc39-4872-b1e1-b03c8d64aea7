package com.pinshang.qingyun.order.service.pay.model;

import com.pinshang.qingyun.base.configure.expand.QYAssert;
import com.pinshang.qingyun.base.enums.pay.PayTypeIntEnum;
import com.pinshang.qingyun.box.utils.JsonUtil;
import lombok.Data;

import java.util.Map;

/**
 * 预支付响应类
 * 对接的三方支付很少没做泛化处理
 * <AUTHOR>
 * @Date 2019/11/26 16:46
 */
@Data
public class PfPrePayResponse {
    private String payBillId;
    /**
     * 支付宝预支付订单
     */
    private String alipayPrePayBill;
    /**
     * 微信预支付订单
     */
    private Map<String, String> wechatPrePayBill;

    /**
     * 云闪付
     */
    private  Map<String, String> unionPrePayBill;



    /**
     * 若不为空,则代表了执行生成待支付单的代码发生了异常
     */
    private RuntimeException exception;

    public String getPrePayJson(PayTypeIntEnum payType){
        QYAssert.isTrue(payType != null ,"获取待支付单json支付渠道参数异常!");
        switch (payType){
            case WECHAT:
                return JsonUtil.java2json(wechatPrePayBill);
            case ALIPAY:
                return JsonUtil.java2json(alipayPrePayBill);
            case UNION_PAY:
            case UNIONPAY_ALI:
            case UNIONPAY_WECHAT:
            case UNIONPAY_UNION:
                return JsonUtil.java2json(unionPrePayBill);
            default:
                return null;
        }
    }
}
