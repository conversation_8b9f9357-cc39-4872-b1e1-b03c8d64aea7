package com.pinshang.qingyun.order.service;

import com.pinshang.qingyun.base.configure.expand.QYAssert;
import com.pinshang.qingyun.base.enums.YesOrNoEnums;
import com.pinshang.qingyun.base.enums.order.OrderTypeEnum;
import com.pinshang.qingyun.box.utils.DateTimeUtil;
import com.pinshang.qingyun.box.utils.DateUtil;
import com.pinshang.qingyun.box.utils.SpringUtil;
import com.pinshang.qingyun.order.dto.CloudOrderAllocationResultOrderODTO;
import com.pinshang.qingyun.order.dto.CloudOrderAllocationSaveIDTO;
import com.pinshang.qingyun.order.dto.MsShopOrderSettingBaseDTO;
import com.pinshang.qingyun.order.enums.DeliveryBatchTypeEnum;
import com.pinshang.qingyun.order.enums.OperationTypeEnum;
import com.pinshang.qingyun.order.mapper.CommodityMapper;
import com.pinshang.qingyun.order.mapper.ShopMapper;
import com.pinshang.qingyun.order.mapper.entry.MdShopOrderSettingEntry;
import com.pinshang.qingyun.order.mapper.entry.order.CommodityInfoEntry;
import com.pinshang.qingyun.order.model.order.Order;
import com.pinshang.qingyun.order.model.shop.Shop;
import com.pinshang.qingyun.order.util.ThreadLocalUtils;
import com.pinshang.qingyun.order.vo.order.OrderDto;
import com.pinshang.qingyun.order.vo.order.ShopReceiveOrderVo;
import com.pinshang.qingyun.storage.dto.tob.CommodityInventoryODTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionSynchronizationAdapter;
import org.springframework.transaction.support.TransactionSynchronizationManager;
import tk.mybatis.mapper.entity.Example;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @Author: sk
 * @Date: 2022/9/2
 */
@Slf4j
@Service
public class CloudOrderAllocationService {

    @Autowired
    private ShopMapper shopMapper;
    @Lazy
    @Autowired
    private MdShopOrderSettingService mdShopOrderSettingService;
    @Autowired
    private OrderAsyncKafkaService orderAsyncKafkaService;
    @Autowired
    private OrderService orderService;
    @Autowired
    private CommodityMapper commodityMapper;
    @Autowired
    private OrderSplitService orderSplitService;
    @Autowired
    private BStockService bStockService;

    /**
     * 云超配货单分配客户提交订单
     *
     * 大仓配货生成的门店订单逻辑调整如下：
     * 生成预售类型的门店订单，订单中的商品不需要记录库存依据，也不需要检查库存和冻结库存
     */
    @Transactional(rollbackFor = Exception.class)
    public List<CloudOrderAllocationResultOrderODTO> cloudOrderAllocationCreateOrder(List<CloudOrderAllocationSaveIDTO>  list) throws Throwable {
        List<CloudOrderAllocationResultOrderODTO> resultList = new ArrayList<>();

        // 设置storeId、物流模式、供应商、仓库
        setMdShopOrderSetting(list);

        // B端库存依据,拆单、记录数据
        Map<Long, BigDecimal> orderQuantityMap = new HashMap<>();
        // 配货固定设置成预售
        list.forEach(item -> {
            item.setPresaleStatus(YesOrNoEnums.YES.getCode());
            orderQuantityMap.put(item.getCommodityId(), item.getQuantity());
        });
        Map<Long, CommodityInventoryODTO> toBStockMap = bStockService.getbStockMap(DateUtil.parseDate(list.get(0).getOrderTime(), "yyyy-MM-dd"), orderQuantityMap);
        list.forEach(item -> {
            CommodityInventoryODTO commodityInventoryODTO = toBStockMap.get(item.getCommodityId());
            if (Objects.nonNull(commodityInventoryODTO)) {
                Integer sourceRatio = commodityInventoryODTO.getSourceRatio();
                item.setSourceRatio(sourceRatio);
                Integer targetRatio = commodityInventoryODTO.getTargetRatio();
                item.setTargetRatio(targetRatio);
                Long targetCommodityId = commodityInventoryODTO.getTargetCommodityId();
                item.setTargetCommodityId(targetCommodityId);
                Integer convertStatus = commodityInventoryODTO.getCanConvert() ? 1 : 0;
                item.setConvertStatus(convertStatus);
                BigDecimal quantity = item.getQuantity();
                // 组合商品目标数量 : 向上取整 ( quantity / source_ratio * target_ratio)
                if (commodityInventoryODTO.getCanConvert()) {
                    BigDecimal targetQuantity = quantity.divide(new BigDecimal(sourceRatio), 0, RoundingMode.UP).multiply(new BigDecimal(targetRatio));
                    item.setTargetQuantity(targetQuantity);
                }
            }
        });

        // 1.根据订单时间、门店id和门店订货通用设置进行拆单
        // 2.转成OrderDto对象
        List<OrderDto> orderDtoList = orderSplitService.convertToOrderDto(list, "云超配货",false);

        ThreadLocalUtils.setOrderType(OrderTypeEnum.DC_ALLOCATION_ORDER.getCode());
        // 循环创建订单
        for(OrderDto orderDto : orderDtoList){
            // 更新一下最新的配置: 商品价格、商品限量、商品是否可变价、商品名称
            orderService.buildProductPrice(orderDto);

            // 云超配货单分配客户提交订单
            Order order = orderService.saveOrder(orderDto, false, false);
            Order kafkaOrder = orderService.saveGiftOrder(orderDto, order.getId(), false);
            kafkaOrder.setOrderAmount(orderDto.amount());

            Long subOrderId = orderService.createSubOrderAndItem(orderDto, order.getId(), order.getOrderCode(), false, order.getAssociationOrderMap());

            //创建收货单
            ShopReceiveOrderVo receiveDto = new ShopReceiveOrderVo(orderDto.getEnterpriseId(), subOrderId, orderDto.getUserId());
            orderService.newReceiveOrder(receiveDto);

            // 记录订单日志
            orderService.inserOrderHistory(order.getId(), Long.valueOf(order.getOrderCode()), orderDto.getUserId(), "系统", OperationTypeEnum.ORDER_NEW.getCode(), order.getOrderTime(), null, null);

            CloudOrderAllocationResultOrderODTO resultODTO = new CloudOrderAllocationResultOrderODTO();
            resultODTO.setOrderId(order.getId());
            resultODTO.setOrderCode(order.getOrderCode());
            resultODTO.setOrderTime(DateTimeUtil.parse(orderDto.getOrderTime(), "yyyy-MM-dd"));
            resultODTO.setStoreId(order.getStoreId());
            resultList.add(resultODTO);

            TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronizationAdapter() {
                @Override
                public void afterCommit() {
                    orderAsyncKafkaService.sendKafkaSaveOrderMessage(kafkaOrder);
                }
            });
        }

        ThreadLocalUtils.remove();
        return resultList;
    }

    /**
     * 根据门店订货通用设置
     * 设置storeId、物流模式、供应商、仓库
     */
    public <T extends MsShopOrderSettingBaseDTO> void setMdShopOrderSetting(List<T> list){
        List<Long> shopIdList = list.stream().map(item -> item.getShopId()).collect(Collectors.toList());

        Example shopEx = new Example(Shop.class);
        shopEx.createCriteria().andIn("id", shopIdList);
        List<Shop> shopList = shopMapper.selectByExample(shopEx);
        Map<Long, Shop> shopMap = shopList.stream().collect(Collectors.toMap(Shop::getId, Function.identity()));

        List<Long> idList = list.stream().map(item -> item.getCommodityId()).collect(Collectors.toList());
        List<CommodityInfoEntry> commodityInfoEntryList = commodityMapper.findCommodityInfoIds(idList);
        Map<Long, CommodityInfoEntry> commodityInfoEntryMap = commodityInfoEntryList.stream().collect(Collectors.toMap(CommodityInfoEntry::getId, Function.identity()));

        list.forEach(odto -> {
            Shop shop = shopMap.get(odto.getShopId());
            odto.setStoreId(shop.getStoreId());

            List<String> commodityIdList = new ArrayList<>();
            commodityIdList.add(odto.getCommodityId() + "");
            List<MdShopOrderSettingEntry> settingList = mdShopOrderSettingService.queryMdShopOrderSettingListByIds(shop.getStoreId(), commodityIdList);
            QYAssert.isTrue(SpringUtil.isNotEmpty(settingList), shop.getShopName() + "门店 "+ commodityInfoEntryMap.get(odto.getCommodityId()).getCommodityName() +"商品订货配置信息有误");
            MdShopOrderSettingEntry settingEntry = settingList.get(0);

            odto.setLogisticsModel(settingEntry.getLogisticsModel().intValue());
            odto.setSupplierId(Long.valueOf(settingEntry.getSupplierId()));
            odto.setWarehouseId(Long.valueOf(settingEntry.getWarehouseId()));
            odto.setDeleveryTimeRange(settingEntry.getDeleveryTimeRange());


            odto.setEnterpriseId(78L);
            odto.setUserId(odto.getUserId());
            odto.setCreateName("系统");
            odto.setDeliveryBatch(DeliveryBatchTypeEnum.ONE_BATCH.getCode().toString());
        });

    }
}
