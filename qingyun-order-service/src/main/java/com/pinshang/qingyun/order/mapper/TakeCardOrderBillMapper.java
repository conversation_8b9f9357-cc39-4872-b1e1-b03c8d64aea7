package com.pinshang.qingyun.order.mapper;

import com.pinshang.qingyun.base.mybatis.MyMapper;
import com.pinshang.qingyun.order.model.order.OrderBill;
import com.pinshang.qingyun.order.model.order.TakeCardOrderBill;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface TakeCardOrderBillMapper extends MyMapper<TakeCardOrderBill> {

    Long insertTakeCardOrderBills(@Param("list")List<TakeCardOrderBill> list);

    List<TakeCardOrderBill> findTakeCardOrderBillByStatus();

    long upStatusById(TakeCardOrderBill takeCardOrderBill);



}
