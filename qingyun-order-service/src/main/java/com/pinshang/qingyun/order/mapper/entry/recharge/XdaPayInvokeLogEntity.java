package com.pinshang.qingyun.order.mapper.entry.recharge;

import com.pinshang.qingyun.base.enums.PayLogTypeEnum;
import com.pinshang.qingyun.base.enums.PayOperateTypeEnum;
import lombok.Data;

import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2019/11/26 13:58
 */
@Data
@Entity
@Table(name = "t_xda_pay_invoke_log")
public class XdaPayInvokeLogEntity {
    @Id
    private Long id;
    /**
     * 订单或退货单 code
     */
    private String referCode;
    /**
     * 1=支付查询，2=退款查询，3=预支付，4=退款，5=支付
     */
    private Integer referType;
    /**
     * 1=请求，2=响应
     */
    private Integer isRequest;
    /**
     * json格式的支付请求或支付响应数据
     */
    private String data;
    private Date createTime;
}
