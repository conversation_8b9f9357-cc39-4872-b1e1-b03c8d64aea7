package com.pinshang.qingyun.order.controller.pf.V2;

import com.pinshang.qingyun.base.api.PfTokenInfo;
import com.pinshang.qingyun.base.configure.expand.QYAssert;
import com.pinshang.qingyun.base.enums.YesOrNoEnums;
import com.pinshang.qingyun.base.util.FastThreadLocalUtil;
import com.pinshang.qingyun.box.utils.DateUtil;
import com.pinshang.qingyun.box.utils.SpringUtil;
import com.pinshang.qingyun.order.config.LockConstants;
import com.pinshang.qingyun.order.dto.shopcart.*;
import com.pinshang.qingyun.order.mapper.OrderListMapper;
import com.pinshang.qingyun.order.mapper.OrderMapper;
import com.pinshang.qingyun.order.mapper.PfShoppingCartMapper;
import com.pinshang.qingyun.order.mapper.StoreDurationMapper;
import com.pinshang.qingyun.order.service.StoreService;
import com.pinshang.qingyun.order.service.pf.v2.BuildPfShoppingCartV2;
import com.pinshang.qingyun.order.service.pf.v2.PfShoppingCartServiceV2;
import com.pinshang.qingyun.order.service.xda.v4.ToBService;
import com.pinshang.qingyun.pf.product.service.PfCommodityFrontClient;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang.StringUtils;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024/3/12 16:47
 */
@RestController
@RequestMapping("/pf/shoppingCartV2")
public class PfShopcartControllerV2 {
    @Autowired
    private PfShoppingCartServiceV2 pfShoppingCartServiceV2;
    @Autowired
    private RedissonClient redissonClient;

    @Autowired
    private PfShoppingCartMapper pfShoppingCartMapper;
    @Autowired
    private OrderMapper orderMapper;
    @Autowired
    private OrderListMapper orderListMapper;
    @Autowired
    private PfCommodityFrontClient pfCommodityFrontClient;
    @Autowired
    private StoreService storeService;
    @Autowired
    private StoreDurationMapper storeDurationMapper;
    @Autowired
    private ToBService toBService;
    @PostMapping("/addV2")
    @ApiOperation(value = "购物车--加")
    public ShoppingCartODTO addShopCartV2(@RequestBody ShoppingCartAddIDTO idto){
        PfTokenInfo pfTokenInfo = FastThreadLocalUtil.getPF();
        Long storeId = pfTokenInfo.getStoreId();
        QYAssert.isTrue(!pfTokenInfo.getIsTouristStore(), "游客无法下单。");
        QYAssert.isTrue(storeId != null, "未能获取到用户信息,请重新登录!");
        idto.setStoreId(storeId);
        String lockKey = LockConstants.generateShoppingCartAddLockKey(storeId);
        RLock lock = redissonClient.getLock(lockKey);
        BuildPfShoppingCartV2 buildPfShoppingCartV2 = null;
        NotShoppingCartPageODTO odto = null;
        ShoppingCartODTO shoppingCartODTO = new ShoppingCartODTO();
        if(lock.tryLock()){
            try {
                buildPfShoppingCartV2 = new BuildPfShoppingCartV2(storeId, idto.getOrderTime(),toBService, pfShoppingCartMapper, orderMapper, orderListMapper, pfCommodityFrontClient, storeService, storeDurationMapper);
                odto = pfShoppingCartServiceV2.addShopCart(idto, buildPfShoppingCartV2);

            } finally {
                lock.unlock();
            }
        }else {
            QYAssert.isFalse("系统繁忙,请勿频繁操作!");
        }
        if (YesOrNoEnums.YES.equals(idto.getShopCartPage()) ){
            /**
             * 刷新购物车
             */
            shoppingCartODTO = buildPfShoppingCartV2.shopCartList();
            if(null != odto.getStockWarningTips()){
                List<ShoppingCartCommodityODTO> commodities = shoppingCartODTO.getNormalGroup().getCommodities();
                for (ShoppingCartCommodityODTO commodity : commodities) {
                    if(commodity.getCommodityId().equals(odto.getCommodityId())){
                        if(StringUtils.isBlank(commodity.getStockWarningTips())){
                            commodity.setStockWarningTips(odto.getStockWarningTips());
                        }
                    }
                }
            }
            return shoppingCartODTO;
        }
        shoppingCartODTO.setVarietySum(pfShoppingCartServiceV2.getValidCommoditySum(storeId, idto.getOrderTime(), buildPfShoppingCartV2));
        if(null != odto.getStockWarningTips()){
            shoppingCartODTO.setStockWarningTips(odto.getStockWarningTips());
        }
        shoppingCartODTO.setNotShoppingCartPageODTO(odto);
        return shoppingCartODTO;

    }

    @PostMapping("/minusV2")
    @ApiOperation(value = "购物车--减")
    public ShoppingCartODTO minusShopCartV2(@RequestBody ShoppingCartMinusIDTO idto){
        PfTokenInfo pfTokenInfo = FastThreadLocalUtil.getPF();
        Long storeId = pfTokenInfo.getStoreId();
        QYAssert.isTrue(!pfTokenInfo.getIsTouristStore(), "游客无法下单。");
        QYAssert.isTrue(storeId != null, "未能获取到用户信息,请重新登录!");
        idto.setStoreId(storeId);
        String lockKey = LockConstants.generateShoppingCartAddLockKey(storeId);
        RLock lock = redissonClient.getLock(lockKey);
        BuildPfShoppingCartV2 buildPfShoppingCartV2 = null;
        NotShoppingCartPageODTO odto = null;
        if(lock.tryLock()){
            try {
                buildPfShoppingCartV2 = new BuildPfShoppingCartV2(storeId, idto.getOrderTime(), toBService,pfShoppingCartMapper, orderMapper, orderListMapper, pfCommodityFrontClient, storeService, storeDurationMapper);
                odto = pfShoppingCartServiceV2.minus(idto, buildPfShoppingCartV2);
            } finally {
                lock.unlock();
            }
        }else {
            QYAssert.isFalse("系统繁忙,请勿频繁操作!");
        }
        if (YesOrNoEnums.YES.equals(idto.getShopCartPage()) ){
            /**
             * 刷新购物车
             */
            ShoppingCartODTO shoppingCartODTO = buildPfShoppingCartV2.shopCartList();
            if(null != odto.getStockWarningTips()){
                List<ShoppingCartCommodityODTO> commodities = shoppingCartODTO.getNormalGroup().getCommodities();
                for (ShoppingCartCommodityODTO commodity : commodities) {
                    if(commodity.getCommodityId().equals(odto.getCommodityId())){
                        if(StringUtils.isBlank(commodity.getStockWarningTips())){
                            commodity.setStockWarningTips(odto.getStockWarningTips());
                        }
                    }
                }
            }
            return shoppingCartODTO;
        }
        ShoppingCartODTO shoppingCartODTO = new ShoppingCartODTO();
        shoppingCartODTO.setVarietySum(pfShoppingCartServiceV2.getValidCommoditySum(storeId, idto.getOrderTime(), buildPfShoppingCartV2));
        /**
         * 非购物车页访问购物车接口时返回 当前商品购物车的数量
         */
        if(null != odto.getStockWarningTips()){
            shoppingCartODTO.setStockWarningTips(odto.getStockWarningTips());
        }
        shoppingCartODTO.setNotShoppingCartPageODTO(odto);
        return shoppingCartODTO;
    }

    @PostMapping("/setNumV2")
    @ApiOperation(value = "购物车--设置数量")
    public ShoppingCartODTO setNumShopCartV2(@RequestBody ShoppingCartSetNumIDTO idto){
        PfTokenInfo pfTokenInfo = FastThreadLocalUtil.getPF();
        Long storeId = pfTokenInfo.getStoreId();
        QYAssert.isTrue(!pfTokenInfo.getIsTouristStore(), "游客无法下单。");
        QYAssert.isTrue(storeId != null, "未能获取到用户信息,请重新登录!");
        idto.setStoreId(storeId);
        BuildPfShoppingCartV2 buildXdaShoppingCart = new BuildPfShoppingCartV2(storeId, idto.getOrderTime(), toBService,pfShoppingCartMapper, orderMapper, orderListMapper, pfCommodityFrontClient, storeService, storeDurationMapper);
        NotShoppingCartPageODTO odto = pfShoppingCartServiceV2.setNum(idto, buildXdaShoppingCart);
        if (YesOrNoEnums.YES.equals(idto.getShopCartPage()) ){
            ShoppingCartODTO shoppingCartODTO = buildXdaShoppingCart.shopCartList();

            if(SpringUtil.isNotEmpty(shoppingCartODTO.getNormalGroup().getCommodities())){
                List<ShoppingCartCommodityODTO> commodities = shoppingCartODTO.getNormalGroup().getCommodities();
                commodities.forEach(item->{
                    if(item.getCommodityId().equals(odto.getCommodityId()) && null != item.getStockWarningTips()){
                        pfShoppingCartServiceV2.setNumV(odto.getId(), odto.getOldQuantity());
                        item.setQuantity(odto.getOldQuantity());
                        odto.setStockWarningTips(item.getStockWarningTips());
                        shoppingCartODTO.setStockWarningTips(item.getStockWarningTips());
                    }
                });
            }
            if(null != odto.getStockWarningTips()){
                shoppingCartODTO.setStockWarningTips(odto.getStockWarningTips());
                List<ShoppingCartCommodityODTO> commodities = shoppingCartODTO.getNormalGroup().getCommodities();
                commodities.forEach(item->{
                    if(item.getCommodityId().equals(odto.getCommodityId())){
                        if(StringUtils.isBlank(item.getStockWarningTips())){
                            item.setStockWarningTips(odto.getStockWarningTips());
                        }
                    }
                });
            }
            return shoppingCartODTO;
        }
        ShoppingCartODTO shoppingCartODTO = new ShoppingCartODTO();
        shoppingCartODTO.setVarietySum(pfShoppingCartServiceV2.getValidCommoditySum(storeId, idto.getOrderTime(), buildXdaShoppingCart));
        shoppingCartODTO.setNotShoppingCartPageODTO(odto);
        if(StringUtils.isBlank(odto.getStockWarningTips())){
            shoppingCartODTO.setStockWarningTips(odto.getStockWarningTips());
            shoppingCartODTO.setBottomTips(odto.getStockWarningTips());
        }
        return shoppingCartODTO;
    }

    @PostMapping("/clearV2")
    @ApiOperation(value = "购物车--清空")
    public ShoppingCartODTO clearV2(@RequestBody ShoppingCartClearIDTO idto){
        PfTokenInfo pfTokenInfo = FastThreadLocalUtil.getPF();
        Long storeId = pfTokenInfo.getStoreId();
        QYAssert.isTrue(!pfTokenInfo.getIsTouristStore(), "游客无法下单。");
        QYAssert.isTrue(storeId != null, "未能获取到用户信息,请重新登录!");
        pfShoppingCartServiceV2.clear(storeId, idto.getCommodityIds());
        BuildPfShoppingCartV2 buildXdaShoppingCart = new BuildPfShoppingCartV2(storeId, idto.getOrderTime(), toBService,pfShoppingCartMapper, orderMapper, orderListMapper, pfCommodityFrontClient, storeService, storeDurationMapper);
        return buildXdaShoppingCart.shopCartList();
    }

    @GetMapping("/refreshV2")
    @ApiOperation(value = "购物车--刷新")
    public ShoppingCartODTO refreshV2(@RequestParam(value = "orderDate",required = false) String orderDate){
        PfTokenInfo pfTokenInfo = FastThreadLocalUtil.getPF();
        Long storeId = pfTokenInfo.getStoreId();
        QYAssert.isTrue(!pfTokenInfo.getIsTouristStore(), "游客无法下单。");
        QYAssert.isTrue(storeId != null, "未能获取到用户信息,请重新登录!");
        //String lockKey = LockConstants.generateShoppingCartAddLockKey(storeId);
        //RLock lock = redissonClient.getLock(lockKey);
        //if(lock.tryLock()){
           // try {
                BuildPfShoppingCartV2 buildPfShoppingCartV2 = new BuildPfShoppingCartV2(storeId, DateUtil.parseDate(orderDate, "yyyy-MM-dd"),toBService, pfShoppingCartMapper, orderMapper, orderListMapper, pfCommodityFrontClient, storeService, storeDurationMapper);
                return buildPfShoppingCartV2.shopCartList();
            //} finally {
                //lock.unlock();
            //}
        //}else {
          //  QYAssert.isFalse("系统繁忙,请勿频繁操作!");
        //}
        //return null;
    }

    @GetMapping("/clear/invalidV2")
    @ApiOperation(value = "购物车--清空失效商品")
    public ShoppingCartODTO clearInvalidCommodityV2(@RequestParam(value = "orderDate",required = false) String orderDate){
        return pfShoppingCartServiceV2.clearInvalidCommodity(DateUtil.parseDate(orderDate, "yyyy-MM-dd"));
    }

    @GetMapping("/categoryV2")
    @ApiOperation(value = "购物车--品类数量")
    public Integer getCategoryNumV2(@RequestParam(value = "orderDate",required = false) String orderDate, @RequestParam(value = "storeId",required = false) Long storeId){
        BuildPfShoppingCartV2 buildPfShoppingCart =  new BuildPfShoppingCartV2(storeId, DateUtil.parseDate(orderDate, "yyyy-MM-dd"),toBService, pfShoppingCartMapper, orderMapper, orderListMapper, pfCommodityFrontClient, storeService, storeDurationMapper);
        return pfShoppingCartServiceV2.getValidCommoditySum(storeId, DateUtil.parseDate(orderDate, "yyyy-MM-dd"), buildPfShoppingCart);
    }
}
