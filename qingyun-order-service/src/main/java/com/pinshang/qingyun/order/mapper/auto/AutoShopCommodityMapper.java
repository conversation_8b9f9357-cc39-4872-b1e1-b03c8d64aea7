package com.pinshang.qingyun.order.mapper.auto;


import com.pinshang.qingyun.base.mybatis.MyMapper;
import com.pinshang.qingyun.order.dto.AutoShopCommodityODTO;
import com.pinshang.qingyun.order.model.auto.AutoShopCommodity;
import com.pinshang.qingyun.order.vo.auto.AutoShopCommodityListVO;
import com.pinshang.qingyun.order.vo.auto.AutoShopCommodityRequestVO;
import com.pinshang.qingyun.order.vo.auto.DirectSendingCommodityVO;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 */
@Repository
public interface AutoShopCommodityMapper extends MyMapper<AutoShopCommodity> {

    /**
     * 查询门店自动订货的商品
     * @param vo
     * @return
     */
    List<AutoShopCommodityListVO> commodityByShopId(@Param("vo") AutoShopCommodityRequestVO vo);

    Long checkCommodityAuto(@Param("storeId") Long storeId, @Param("commodityId")Long commodityId);

    List<Long> getAutoCommodityList(@Param("shopId") Long shopId);

    /**
     * 商品基本信息
     * @param vo
     * @return
     */
    List<AutoShopCommodityListVO> commodityInfoList(@Param("vo") AutoShopCommodityRequestVO vo);

    /**
     * 获取商品信息
     * @param iogisticsModel
     * @return
     */
    List<DirectSendingCommodityVO> directSendingCommodityList(@Param("iogisticsModel") Integer iogisticsModel);

    List<AutoShopCommodityODTO> queryAllAutoShopCommodity();
}