package com.pinshang.qingyun.order.mapper;

import com.pinshang.qingyun.order.vo.order.HandleReceiveOrderLogVo;
import com.pinshang.qingyun.order.vo.shop.AuditItem;
import com.pinshang.qingyun.order.vo.shop.ShopReceiveItemVo;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;
import org.springframework.stereotype.Repository;

import com.pinshang.qingyun.base.mybatis.MyMapper;
import com.pinshang.qingyun.order.model.order.SubOrderItem;

import java.math.BigDecimal;
import java.util.List;

@Repository
public interface SubOrderItemMapper extends MyMapper<SubOrderItem>{

    int batchUpdate(List<ShopReceiveItemVo> list);

    @Update("UPDATE t_sub_order_item\n" +
            "SET real_receive_quantity = #{realReceiveQuantity} \n" +
            "WHERE\n" +
            "\tsub_order_id = #{subOrderId} \n" +
            "AND commodity_Id = #{commodityId}")
    Integer updateRealReceiveQuantity(@Param("realReceiveQuantity")BigDecimal realReceiveQuantity, @Param("subOrderId") String subOrderId, @Param("commodityId")String commodityId);

    @Update("UPDATE t_sub_order_item\n" +
            "SET real_receive_quantity = real_delivery_quantity \n" +
            "WHERE\n" +
            "\tsub_order_id = #{subOrderId} \n" +
            "AND commodity_Id = #{commodityId}")
    Integer updateHandleRealReceiveQuantity(@Param("subOrderId") String subOrderId, @Param("commodityId")String commodityId);


    @Update("UPDATE t_sub_order_item SET status=#{auditItem.isPass},reject_reason = #{auditItem.rejectReason} where id =#{auditItem.itemId}")
    Integer updateAuditStatus(@Param("auditItem") AuditItem auditItem);


    @Update("UPDATE t_md_receive_order set status = #{status} where sub_order_id = #{subOrderId} ")
    Integer updateStatus(@Param("subOrderId") String subOrderId,@Param("status") Integer status);

    @Select("SELECT shop_name from t_md_shop where store_id = #{storeId}")
    String getShopName(String storeId);

    @Select("SELECT SUM(real_delivery_quantity) FROM t_sub_order_item WHERE sub_order_id = #{subOrderId}")
    Double getSumRealQuantity(@Param("subOrderId")Long subOrderId);

    int batchInsertHandleReceiveOrderLog(List<HandleReceiveOrderLogVo> handleReceiveOrderLogList);

    List<SubOrderItem> queryBySubOrderId(@Param("subOrderId") Long subOrderId);

    List<SubOrderItem> queryByOrderIdAndCommodityIds(@Param("orderId") Long orderId,
                                                    @Param("commodityList") List<Long> commodityList);

    List<SubOrderItem> queryByOrderId(@Param("orderId") Long orderId);

}
