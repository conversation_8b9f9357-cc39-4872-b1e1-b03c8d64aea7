package com.pinshang.qingyun.order.mapper;

import com.pinshang.qingyun.base.mybatis.MyMapper;
import com.pinshang.qingyun.order.mapper.entry.DeliveryTimeEntry;
import com.pinshang.qingyun.order.model.order.DeliveryTime;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
@Repository
public interface DeliveryTimeMapper extends MyMapper<DeliveryTime>{
    List<DeliveryTimeEntry> queryDeliveryTimeList();

    DeliveryTime getDeliveryTimeByStoreId(@Param("storeId") long storeId);

    List<String> selectDeliveryTimeEndTimeByLineIds(@Param("lineIdList") List<Long> lineIdList);
}
