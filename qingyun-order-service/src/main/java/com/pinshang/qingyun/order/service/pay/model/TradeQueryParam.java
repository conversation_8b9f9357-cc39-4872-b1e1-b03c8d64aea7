package com.pinshang.qingyun.order.service.pay.model;

import com.pinshang.qingyun.base.enums.xd.XdPayTypeEnum;
import com.pinshang.qingyun.order.mapper.entry.pf.recharge.PfPayBillJobEntry;
import com.pinshang.qingyun.order.mapper.entry.recharge.XdaPayBillJobEntry;
import com.pinshang.qingyun.order.model.recharge.XDAPayBill;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @Date 2019/11/27 17:40
 */
@Data
@NoArgsConstructor
public class TradeQueryParam {
    /**
     * 支付单code
     */
    private String billCode;
    /**
     * 外部的交易流水号（支付宝、微信或云闪付返回的支付单号）
     */
    private String tradeBillCode;

    private XdPayTypeEnum payType;
    private Long storeId;

    public TradeQueryParam(XdaPayBillJobEntry entry){
        this.billCode = entry.getBillCode();
        this.tradeBillCode = entry.getTradeBillCode();
        this.payType = XdPayTypeEnum.getByCode(entry.getPayType());
        this.storeId = entry.getStoreId();
    }

    public TradeQueryParam(PfPayBillJobEntry entry){
        this.billCode = entry.getBillCode();
        this.tradeBillCode = entry.getTradeBillCode();
        this.payType = XdPayTypeEnum.getByCode(entry.getPayType());
    }


    public TradeQueryParam(XDAPayBill entry){
        this.billCode = entry.getBillCode();
        this.tradeBillCode = entry.getTradeBillCode();
        this.payType = XdPayTypeEnum.getByCode(entry.getPayType());
        this.storeId = entry.getStoreId();
    }
}
