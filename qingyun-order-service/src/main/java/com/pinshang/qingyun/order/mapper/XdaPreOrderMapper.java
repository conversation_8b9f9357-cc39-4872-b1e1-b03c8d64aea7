package com.pinshang.qingyun.order.mapper;

import com.pinshang.qingyun.base.mybatis.MyMapper;
import com.pinshang.qingyun.order.dto.xda.v4.XdaOrderAppV4ODTO;
import com.pinshang.qingyun.order.dto.xda.v4.XdaOrderItemAppV4ODTO;
import com.pinshang.qingyun.order.model.order.XdaPreOrder;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface XdaPreOrderMapper extends MyMapper<XdaPreOrder>{
    XdaOrderAppV4ODTO queryXdaNoPayPreOrder(@Param("storeId") Long storeId);

    Integer countXdaNoPayPreOrder(@Param("storeId") Long storeId);
    List<XdaOrderItemAppV4ODTO> queryXdaPreOrderItemXdaAppV4(@Param("orderIds") List<Long> orderIds);
}

