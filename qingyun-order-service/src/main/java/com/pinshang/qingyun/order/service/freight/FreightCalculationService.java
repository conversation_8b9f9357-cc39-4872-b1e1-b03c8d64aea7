package com.pinshang.qingyun.order.service.freight;

import com.pinshang.qingyun.base.enums.StoreFreightTypeEnums;
import com.pinshang.qingyun.box.utils.DateUtil;
import com.pinshang.qingyun.box.utils.SpringUtil;
import com.pinshang.qingyun.order.dto.shopcart.v4.ShoppingCartCommodityV4ODTO;
import com.pinshang.qingyun.order.dto.shopcart.v4.ShoppingCartV4ODTO;
import com.pinshang.qingyun.store.dto.storeFreightSetting.StoreFreightSettingIDTO;
import com.pinshang.qingyun.store.dto.storeFreightSetting.StoreFreightSettingODTO;
import com.pinshang.qingyun.store.service.StoreFreightSettingClient;
import lombok.Builder;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.time.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;

/**
 * 计算配送费
 *
 * <AUTHOR>
 * @since 2025-01-14
 */
@Slf4j
@Service
public class FreightCalculationService {

    @Autowired
    private StoreFreightSettingClient storeFreightSettingClient;

    @Data
    @Builder
    public static class FreightResult {
        private BigDecimal amount;
        private String reason;
    }

    /**
     * 计算配送费
     */
    public FreightResult calculateFreight(Long storeId, Date orderTime, ShoppingCartV4ODTO shopCart) {
        // 1. 检查当日自提
        if (isSameDayPickup(shopCart, orderTime)) {
            return FreightResult.builder()
                    .amount(BigDecimal.ZERO)
                    .reason("当日自提免配送费")
                    .build();
        }

        // 2. 获取配送费设置
        StoreFreightSettingODTO freightSetting = getFreightSetting(storeId);
        if (freightSetting == null) {
            return FreightResult.builder()
                    .amount(BigDecimal.ZERO)
                    .reason("未配置配送费")
                    .build();
        }

        // 券前应付金额
        BigDecimal couponAmount = Objects.nonNull(shopCart.getCouponAmount()) ? shopCart.getCouponAmount() : BigDecimal.ZERO;
        BigDecimal preCouponAmount = shopCart.getSummation().add(couponAmount);

        // 3. 根据配送类型计算费用
        return calculateByFreightType(storeId, preCouponAmount, freightSetting);
    }

    /**
     * 根据配送类型计算费用
     */
    private FreightResult calculateByFreightType(Long storeId, BigDecimal orderAmount, StoreFreightSettingODTO setting) {
        StoreFreightTypeEnums freightType = StoreFreightTypeEnums.fromName(setting.getFreightType());

        switch (freightType) {
            case NO:
                return FreightResult.builder()
                        .amount(BigDecimal.ZERO)
                        .reason("配送类型为无")
                        .build();

            case PERCENT:
                return calculatePercentageFreight(orderAmount, setting.getFreightPercent());

            default:
                log.error("客户[{}]配送类型[{}]未知,配送费设置为0", storeId, setting.getFreightType());
                return FreightResult.builder()
                        .amount(BigDecimal.ZERO)
                        .reason("未知配送类型")
                        .build();
        }
    }

    /**
     * 检查是否当日自提
     */
    private boolean isSameDayPickup(ShoppingCartV4ODTO shopCart, Date orderTime) {
        if (!DateUtils.isSameDay(orderTime, DateUtil.getNowDate())) {
            return false;
        }

        List<ShoppingCartCommodityV4ODTO> allCommodities = getAllCommodities(shopCart);
        return allCommodities.stream()
                .anyMatch(commodity ->
                        commodity.getBeginDeliveryTime() != null &&
                                commodity.getBeginDeliveryTime().equals(DateUtil.getNowDate())
                );
    }

    /**
     * 获取所有商品列表
     */
    private List<ShoppingCartCommodityV4ODTO> getAllCommodities(ShoppingCartV4ODTO shopCart) {
        List<ShoppingCartCommodityV4ODTO> result = new ArrayList<>();

        if (SpringUtil.isNotEmpty(shopCart.getNormalGroup().getCommodities())) {
            result.addAll(shopCart.getNormalGroup().getCommodities());
        }

        if (SpringUtil.isNotEmpty(shopCart.getThGroups().getCommodities())) {
            result.addAll(shopCart.getThGroups().getCommodities());
        }

        shopCart.getPromotionGroup().forEach(group -> {
            if (SpringUtil.isNotEmpty(group.getCommodities())) {
                result.addAll(group.getCommodities());
            }
        });

        return result;
    }

    /**
     * 计算百分比配送费
     */
    private FreightResult calculatePercentageFreight(BigDecimal orderAmount, BigDecimal freightPercent) {
        BigDecimal amount = orderAmount.multiply(freightPercent).divide(new BigDecimal("100"), 2, RoundingMode.HALF_UP);

        return FreightResult.builder()
                .amount(amount)
                .reason(String.format("按订单金额%s%%收取", freightPercent))
                .build();
    }

    /**
     * 获取配送费设置
     */
    private StoreFreightSettingODTO getFreightSetting(Long storeId) {
        StoreFreightSettingIDTO query = new StoreFreightSettingIDTO();
        query.setStoreIdList(Collections.singletonList(storeId));

        return storeFreightSettingClient
                .findStoreFreightSettingByStoreIdList(query)
                .stream()
                .filter(setting -> setting.getStoreId().equals(storeId))
                .findFirst()
                .orElse(null);
    }
} 