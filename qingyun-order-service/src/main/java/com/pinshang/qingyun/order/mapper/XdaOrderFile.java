package com.pinshang.qingyun.order.mapper;

import java.util.Date;

import javax.persistence.Entity;
import javax.persistence.Table;

import lombok.Data;
import lombok.NoArgsConstructor;

import com.pinshang.qingyun.base.po.BaseSimplePO;

@Data
@Entity
@NoArgsConstructor
@Table(name = "t_order_file")
public class XdaOrderFile extends BaseSimplePO {
	// 订单ID
	private Long orderId;
	// 文件Url
	private String fileUrl;
	
	public XdaOrderFile (Long orderId) {
		this.orderId = orderId;
	}
	
	public static XdaOrderFile forInsert(Long orderId, String fileUrl, Long createId, Date createTime) {
		XdaOrderFile model = new XdaOrderFile();
		model.setOrderId(orderId);
		model.setFileUrl(fileUrl);
		model.setCreateId(createId);
		model.setCreateTime(createTime);
		return model;
	}
	
}
