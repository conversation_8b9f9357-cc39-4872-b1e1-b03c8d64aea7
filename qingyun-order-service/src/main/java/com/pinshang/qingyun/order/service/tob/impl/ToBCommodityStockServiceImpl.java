package com.pinshang.qingyun.order.service.tob.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.pinshang.qingyun.order.dto.tob.ToBStockRepairIDTO;
import com.pinshang.qingyun.order.dto.tob.ToBStockRepairODTO;
import com.pinshang.qingyun.order.mapper.TobCommodityStockMapper;
import com.pinshang.qingyun.order.model.tob.TobCommodityStock;
import com.pinshang.qingyun.order.service.tob.ToBCommodityStockService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.entity.Example;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * @Description：
 * @Author：ZhangHui
 * @Package：com.pinshang.qingyun.order.service.tob.impl
 * @Date: 2024/4/10
 */
@Service
@Slf4j
public class ToBCommodityStockServiceImpl implements ToBCommodityStockService {


    @Autowired
    private TobCommodityStockMapper tobCommodityStockMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void stockRepair(List<ToBStockRepairIDTO> repairIDTOList) {
        Map<Long, ToBStockRepairIDTO> stockMap = repairIDTOList.stream().collect(Collectors.toMap(ToBStockRepairIDTO::getCommodityId, e -> e, (oldValue, newValue) -> oldValue));
        Set<Long> commodityIds = stockMap.keySet();
        Example example = new Example(TobCommodityStock.class);
        example.createCriteria().andIn("commodityId", commodityIds);
        List<TobCommodityStock> tobCommodityStockList = tobCommodityStockMapper.selectByExample(example);


        //定义两个集合，updateList是更新的集合，insertList是需要插入的集合
        List<ToBStockRepairIDTO> updateList = new ArrayList<>();
        List<Long> updateCommodityIds = new ArrayList<>();
        List<ToBStockRepairIDTO> insertList = new ArrayList<>();
        for (TobCommodityStock tobCommodityStock : tobCommodityStockList) {
            Long commodityId = tobCommodityStock.getCommodityId();
            if (commodityIds.contains(commodityId)) {
                updateCommodityIds.add(commodityId);
                updateList.add(stockMap.get(commodityId));
            }
        }


        List<Long> insertCommodityIds = new ArrayList<>();
        for (ToBStockRepairIDTO toBStockRepairIDTO : repairIDTOList) {
            Long commodityId = toBStockRepairIDTO.getCommodityId();
            if (!updateCommodityIds.contains(commodityId) && !insertCommodityIds.contains(commodityId)) {
                insertCommodityIds.add(commodityId);
                insertList.add(toBStockRepairIDTO);
            }
        }

        if (CollectionUtils.isNotEmpty(updateList)) {
            tobCommodityStockMapper.batchUpdate(updateList);
        }
        if (CollectionUtils.isNotEmpty(insertList)) {
            insertList.forEach(e -> {
                e.setTdaStockStatus(e.getStockStatus());
            });
            tobCommodityStockMapper.batchInsert(insertList);
        }
    }

    @Override
    public Integer queryStockCount() {
        TobCommodityStock tobCommodityStock = new TobCommodityStock();
        return tobCommodityStockMapper.selectCount(tobCommodityStock);
    }

    @Override
    public PageInfo<ToBStockRepairODTO> queryStockList(Integer pageNo, Integer pageSize) {
        Example example = new Example(TobCommodityStock.class);
        example.setOrderByClause("commodity_id desc");
        return PageHelper.startPage(pageNo, pageSize)
                .doSelectPageInfo(() -> tobCommodityStockMapper.selectByExample(example));
    }


}
