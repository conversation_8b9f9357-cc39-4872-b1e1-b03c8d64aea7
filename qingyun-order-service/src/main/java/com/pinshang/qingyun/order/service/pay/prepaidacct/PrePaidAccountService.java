package com.pinshang.qingyun.order.service.pay.prepaidacct;

import com.github.pagehelper.PageHelper;
import com.pinshang.qingyun.base.enums.StoreBillTypeEnums;
import com.pinshang.qingyun.order.bo.StoreDeductionBO;
import com.pinshang.qingyun.order.bo.StoreRechargeBO;
import com.pinshang.qingyun.order.config.LockConstants;
import com.pinshang.qingyun.order.enums.PrePaidAccountReqRefBillTypeEnum;
import com.pinshang.qingyun.order.enums.PrePaidAccountReqStatusEnum;
import com.pinshang.qingyun.order.enums.PrePaidAccountReqTypeEnum;
import com.pinshang.qingyun.order.mapper.PrePaidAccountReqMapper;
import com.pinshang.qingyun.order.mapper.StoreSettlementMapper;
import com.pinshang.qingyun.order.model.order.PrePaidAccountReq;
import com.pinshang.qingyun.order.model.order.StoreSettlement;
import com.pinshang.qingyun.order.service.StoreRechargeService;
import com.pinshang.qingyun.order.vo.prepayacct.PrePaidAccountReqVo;
import com.pinshang.qingyun.order.vo.prepayacct.PrePayAccountVo;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.entity.Example;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.Map.Entry;
import java.util.concurrent.TimeUnit;

/**
 * 预付费账户服务
 * 对应的是表t_store_settlement中collect_status为“预收”的数据
 * 
 * <AUTHOR>
 *
 */
@Service
@Slf4j
public class PrePaidAccountService {

	private StoreSettlementMapper storeSettlementMapper;


	private PrePaidAccountReqMapper prePaidAccountReqMapper;

	private RedissonClient redissonClient;


	private PrePaidAccountBatchService prePaidAccountBatchService;

	private Long prePaidAccountRecordLockWaitTimeSec;

	private StoreRechargeService storeRechargeService;

	public PrePayAccountVo findByStoreId(Long storeId) {
		Example exp = new Example(StoreSettlement.class);
		exp.createCriteria()
			.andEqualTo("storeId", storeId)
			.andEqualTo("collectStatus", true);
		List<StoreSettlement> records = storeSettlementMapper.selectByExample(exp);
		if (records.isEmpty()) {
			return null;
		}
		StoreSettlement record = records.get(0);
		return buildPrePayAccountVo(record);
	}

	private PrePayAccountVo buildPrePayAccountVo(StoreSettlement record) {
		PrePayAccountVo result = new PrePayAccountVo();
		result.setStoreId(record.getStoreId());
		BigDecimal collectPrice = record.getCollectPrice() != null ? BigDecimal.valueOf(record.getCollectPrice()).setScale(2, RoundingMode.HALF_UP) : BigDecimal.ZERO;
		result.setCollectPrice(collectPrice);
		return result;
	}

	/**
	 * 账户付款
	 * @param storeId 客户
	 * @param amount 金额
	 * @param promoterId 发起人
	 * @param remarks 备注
	 * @return 返回的付款请求中有表示是否已付款的标识；如果是未付款，标识本次付款操作转为异步
	 */
	@Transactional
	public PrePaidAccountReqVo pay(Long storeId, BigDecimal amount, Long promoterId, String remarks,
			PrePaidAccountReqRefBillTypeEnum refBillType, Long refBillId, Date refBillTime) {
		// 1. 生成扣款请求
		PrePaidAccountReqVo req = new PrePaidAccountReqVo(PrePaidAccountReqTypeEnum.PAY ,storeId, amount, promoterId, remarks, PrePaidAccountReqStatusEnum.INIT, 
				refBillType, refBillId, refBillTime);
		
		return doExchange(req);
	}
	
	/**
	 * 
	 * @param storeId 客户
	 * @param amount 金额
	 * @param promoterId 发起人
	 * @param remarks 备注
	 * @param refBillType 单据类型
	 * @param refBillId 单据id
	 * @param refBillTime 单据时间
	 * @return 付款请求
	 * @throws PrePaidAccountLockedException 账号被锁定
	 */
	public PrePaidAccountReqVo paySync(Long storeId, BigDecimal amount, Long promoterId, String remarks,
			PrePaidAccountReqRefBillTypeEnum refBillType, Long refBillId, Date refBillTime) {
		// 1. 生成扣款请求
		PrePaidAccountReqVo req = new PrePaidAccountReqVo(PrePaidAccountReqTypeEnum.PAY ,storeId, amount, promoterId, remarks, PrePaidAccountReqStatusEnum.INIT, 
				refBillType, refBillId, refBillTime);
		return doExchangeSync(req);
	}

	/**
	 * 
	 * @param storeId 客户
	 * @param amount 金额
	 * @param promoterId 发起人
	 * @param remarks 备注
	 * @param refBillType 单据类型
	 * @param refBillId 单据id
	 * @param refBillTime 单据时间
	 * @return 付款请求
	 * @throws PrePaidAccountLockedException 账号被锁定
	 */
	public PrePaidAccountReqVo receiveSync(Long storeId, BigDecimal amount, Long promoterId, String remarks,
			PrePaidAccountReqRefBillTypeEnum refBillType, Long refBillId, Date refBillTime) {
		// 1. 生成收款请求
		PrePaidAccountReqVo req = new PrePaidAccountReqVo(PrePaidAccountReqTypeEnum.RECEIVE ,storeId, amount, promoterId, remarks, PrePaidAccountReqStatusEnum.INIT, 
				refBillType, refBillId, refBillTime);
		
		return doExchangeSync(req);
	}

	/**
	 * @param storeId     客户
	 * @param amount      金额
	 * @param promoterId  发起人
	 * @param remarks     备注
	 * @param refBillType 单据类型
	 * @param refBillId   单据id
	 * @param refBillCode 单据编号
	 * @param refBillTime 单据时间
	 * @return 付款请求
	 * @throws PrePaidAccountLockedException 账号被锁定
	 */
	public PrePaidAccountReqVo paySync(Long storeId, BigDecimal amount, Long promoterId, String remarks,
									   PrePaidAccountReqRefBillTypeEnum refBillType, Long refBillId, String refBillCode, Date refBillTime) {
		// 1. 生成扣款请求
		PrePaidAccountReqVo req = new PrePaidAccountReqVo(PrePaidAccountReqTypeEnum.PAY, storeId, amount, promoterId, remarks, PrePaidAccountReqStatusEnum.INIT,
				refBillType, refBillId, refBillCode, refBillTime);
		return doExchangeSync(req);
	}

	/**
	 * @param storeId     客户
	 * @param amount      金额
	 * @param promoterId  发起人
	 * @param remarks     备注
	 * @param refBillType 单据类型
	 * @param refBillId   单据id
	 * @param refBillCode 单据编号
	 * @param refBillTime 单据时间
	 * @return 付款请求
	 * @throws PrePaidAccountLockedException 账号被锁定
	 */
	public PrePaidAccountReqVo receiveSync(Long storeId, BigDecimal amount, Long promoterId, String remarks,
										   PrePaidAccountReqRefBillTypeEnum refBillType, Long refBillId, String refBillCode, Date refBillTime) {
		// 1. 生成收款请求
		PrePaidAccountReqVo req = new PrePaidAccountReqVo(PrePaidAccountReqTypeEnum.RECEIVE, storeId, amount, promoterId, remarks, PrePaidAccountReqStatusEnum.INIT,
				refBillType, refBillId, refBillCode, refBillTime);

		return doExchangeSync(req);
	}
	
	/**
	 * 同步执行
	 * @param req 请求
	 * @return 请求信息
	 * @throws PrePaidAccountLockedException 账号被锁定
	 */
	private PrePaidAccountReqVo doExchangeSync(PrePaidAccountReqVo req) {
		PrePaidAccountReq reqDto = buildReqDto(req);
		prePaidAccountReqMapper.insertRecord(reqDto);
		req.setId(reqDto.getId()); // 回填id
		
		// 2. 获取预付费账户锁
		String lockKey = LockConstants.generatePrePaidAccountLock(req.getStoreId());
		RLock lock = redissonClient.getLock(lockKey);
		try {
			if (lock.tryLock(prePaidAccountRecordLockWaitTimeSec, TimeUnit.SECONDS)) {
				// 2.1 获取锁成功
				doExchangeOnLock(req);
				
			} else {
				// 2.2 获取锁失败
				throw new PrePaidAccountLockedException(String.format("当前预付款账号[storeId=%s]繁忙", req.getStoreId()));
			}
		} catch (InterruptedException e) {
			
			// 2.2 相当于获取锁失败
			log.error(String.format("处理预付账户扣款[reqId=%s]时中断", req.getId()), e);

			Thread.currentThread().interrupt();
			throw new PrePaidAccountLockedException(String.format("当前预付款账号[storeId=%s]繁忙", req.getStoreId()));
		} finally {
			if (lock.isHeldByCurrentThread()) {
				lock.unlock();
			}
		}
		
		return req;
	}

	/**
	 * 在获取锁的情况下执行转账
	 * @param req 转账请求
	 */
	private void doExchangeOnLock(PrePaidAccountReqVo req) {
		// 2.1.1 标记扣款请求成功
		req.setStatus(PrePaidAccountReqStatusEnum.RECORDED);
		// 更新
		PrePaidAccountReq reqDto4Update = new PrePaidAccountReq();
		reqDto4Update.setId(req.getId());
		reqDto4Update.setReqStatus(req.getStatus().getCode());
		prePaidAccountReqMapper.updateByPrimaryKeySelective(reqDto4Update);

		// 2.1.2 执行扣款
		try {
			doRequest(req);
		} catch (PrePaidAccountReqException e) {
			log.error("执行账户付款异常", e);
			// 标记为异常
			req.setStatus(PrePaidAccountReqStatusEnum.EXCEPTION);
			// 保存req
			PrePaidAccountReq reqDto4UpdateErr = new PrePaidAccountReq();
			reqDto4UpdateErr.setId(req.getId());
			reqDto4UpdateErr.setReqStatus(req.getStatus().getCode());
			reqDto4UpdateErr.setErrMsg(e.getMessage());
			prePaidAccountReqMapper.updateByPrimaryKeySelective(reqDto4UpdateErr);
		}
	}

	/**
	 * 执行转账（当账户锁可用时），否则仅标记待转账信息
	 * @param req 请求
	 * @return 转账信息
	 */
	private PrePaidAccountReqVo doExchange(PrePaidAccountReqVo req) {
		PrePaidAccountReq reqDto = buildReqDto(req);
		prePaidAccountReqMapper.insertRecord(reqDto);
		req.setId(reqDto.getId()); // 回填id
		
		// 2. 获取预付费账户锁
		String lockKey = LockConstants.generatePrePaidAccountLock(req.getStoreId());
		RLock lock = redissonClient.getLock(lockKey);
		try {
			if (lock.tryLock(prePaidAccountRecordLockWaitTimeSec, TimeUnit.SECONDS)) {
				// 2.1 获取锁成功
				doExchangeOnLock(req);
				
			} else {
				// 2.2 获取锁失败
				// 2.2.1 异步扣款
				req.setStatus(PrePaidAccountReqStatusEnum.MARK_TO_ASYNC_RECORD);
				// 保存req
				PrePaidAccountReq updateReqDto;
				updateReqDto = new PrePaidAccountReq();
				updateReqDto.setId(req.getId());
				updateReqDto.setReqStatus(req.getStatus().getCode());
				prePaidAccountReqMapper.updateByPrimaryKeySelective(updateReqDto);
			}
		} catch (InterruptedException e) {
			
			// 2.2 相当于获取锁失败
			log.error(String.format("处理预付账户扣款[reqId=%s]时中断", req.getId()), e);
			
			// 2.2.1 异步扣款
			req.setStatus(PrePaidAccountReqStatusEnum.MARK_TO_ASYNC_RECORD);
			// 保存req
			PrePaidAccountReq updateReqDto;
			updateReqDto = new PrePaidAccountReq();
			updateReqDto.setId(req.getId());
			updateReqDto.setReqStatus(req.getStatus().getCode());
			prePaidAccountReqMapper.updateByPrimaryKeySelective(updateReqDto);

			Thread.currentThread().interrupt();
		} finally {
			if (lock.isHeldByCurrentThread()) {
				lock.unlock();
			}
		}
		
		return req;
	}

	/*
	 * 
	 * 
	 */
	private void doRequest(PrePaidAccountReqVo req) throws PrePaidAccountReqException {
		Long storeId = req.getStoreId();
		Example ex = new Example(StoreSettlement.class);
        ex.createCriteria().andEqualTo("storeId", storeId);
        List<StoreSettlement> ssList = this.storeSettlementMapper.selectByExample(ex);
        if(ssList.isEmpty()){
            throw new PrePaidAccountReqException(String.format("预付费账户扣款异常，未找到预付款账户[storeId=%s]", storeId));
        }
        StoreSettlement ss = ssList.get(0);
        if (!ss.getCollectStatus()) { // 非预付费账户
        	throw new PrePaidAccountReqException(String.format("预付费账户扣款异常，当前账户[storeId=%s]不是一个预付费账户", storeId));
        }
        
		if (req.getReqType() == PrePaidAccountReqTypeEnum.PAY) {
			//扣款
			StoreDeductionBO deductionBO = StoreDeductionBO.builder()
					.orderCode(req.getRefBillCode())
					.orderId(req.getRefBillId())
					.orderAmount(req.getAmount())
					.storeId(storeId)
					.tradeCode(req.getRefBillCode())
					.tradeTime(new Date())
					.orderTime(req.getRefBillTime())
					.billType(StoreBillTypeEnums.PF_ORDER_DEDUCTION.getCode())
					.remark(req.getRemarks())
					.userId(storeId)
					.build();
			storeRechargeService.storeDeduction(deductionBO);
		} else {
			//充值
			StoreRechargeBO rechargeBO = StoreRechargeBO.builder()
					.orderCode(req.getRefBillCode())
					.tradeCode(req.getRefBillCode())
					.money(req.getAmount().doubleValue())
					.storeId(storeId)
					.tradeTime(new Date())
					.receiptDate(new Date())
					.billType(StoreBillTypeEnums.PF_REFUNDMENT.getCode())
					.remark(req.getRemarks())
					.userId(storeId)
					.build();
			storeRechargeService.storeRecharge(rechargeBO);
		}
	}

	private PrePaidAccountReq buildReqDto(PrePaidAccountReqVo req) {
		Date now = new Date();
		PrePaidAccountReq dto = new PrePaidAccountReq();
		dto.setReqType(req.getReqType().getCode());
		dto.setStoreId(req.getStoreId());
		dto.setAmount(req.getAmount());
		dto.setRemarks(req.getRemarks());
		dto.setReqStatus(req.getStatus().getCode());
		
		dto.setRefBillType(req.getRefBillType().getCode());
		dto.setRefBillId(req.getRefBillId());
		dto.setRefBillTime(req.getRefBillTime());
		
		dto.setCreaterId(req.getPromoterId());
		dto.setCreateTime(now);
		dto.setUpdateTime(now);
		return dto;
	}

	/**
	 * 账户收款
	 * @param storeId 客户
	 * @param amount 金额
	 * @param remarks 备注
	 * @return 收款请求
	 */
	public PrePaidAccountReqVo receive(Long storeId, BigDecimal amount, Long promoterId, String remarks, 
			PrePaidAccountReqRefBillTypeEnum refBillType, Long refBillId, Date refBillTime) {
		// 1. 生成收款请求
		PrePaidAccountReqVo req = new PrePaidAccountReqVo(PrePaidAccountReqTypeEnum.RECEIVE ,storeId, amount, promoterId, remarks, PrePaidAccountReqStatusEnum.INIT, 
				refBillType, refBillId, refBillTime);
		
		return doExchange(req);
	}
	
	/**
	 * 异步入账
	 * 目前：定时任务机制
	 */
	public void asyncRecord() {
		Example exp = new Example(PrePaidAccountReq.class);
		exp.createCriteria()
			.andEqualTo("reqStatus", PrePaidAccountReqStatusEnum.MARK_TO_ASYNC_RECORD.getCode());
		exp.orderBy("id");
		int pageNum = 1;
		int pageSize = 1000;
		
		List<PrePaidAccountReqVo> currentPage = queryMarkToAsyncRecordByPage(pageNum, pageSize);
		
		while (!currentPage.isEmpty()) {
			Map<Long, List<PrePaidAccountReqVo>> mapByStoreId = mapPrePaidAccountReqVoByStoreId(currentPage);
			for (Entry<Long, List<PrePaidAccountReqVo>> e : mapByStoreId.entrySet()) {
				Long storeId = e.getKey();
				List<PrePaidAccountReqVo> reqs = e.getValue();
				try {
					prePaidAccountBatchService.batchRecord(storeId, reqs);
				} catch (InterruptedException ex) {
					log.error(String.format("批处理预付费账户入账时中断，当前客户[storeId=%s]", storeId), ex);
					return; // 如果当前线程被中断，直接退出
				} catch (Exception e2) {
					log.error(String.format("批处理预付费账户入账时发生异常，当前客户[storeId=%s]", storeId), e2);
					// 记录日志后，继续下一个客户
				}
			}
			
			// next page
			pageNum++;
			currentPage = queryMarkToAsyncRecordByPage(pageNum, pageSize);
		}
		
	}
	
	

	private List<PrePaidAccountReqVo> queryMarkToAsyncRecordByPage(int pageNum, int pageSize) {
		Example exp = new Example(PrePaidAccountReq.class);
		exp.createCriteria()
			.andEqualTo("reqStatus", PrePaidAccountReqStatusEnum.MARK_TO_ASYNC_RECORD.getCode());
		exp.orderBy("id");
		PageHelper.startPage(pageNum, pageSize);
		List<PrePaidAccountReq> reqDtoList = prePaidAccountReqMapper.selectByExample(exp);
		return buildPrePaidAccountReqVoList(reqDtoList);
	}


	private Map<Long, List<PrePaidAccountReqVo>> mapPrePaidAccountReqVoByStoreId(List<PrePaidAccountReqVo> reqList) {
		Map<Long, List<PrePaidAccountReqVo>> result = new HashMap<>();
		for (PrePaidAccountReqVo r : reqList) {
			Long storeId = r.getStoreId();
            List<PrePaidAccountReqVo> reqs = result.computeIfAbsent(storeId, k -> new ArrayList<>());
            reqs.add(r);
		}
		return result;
	}

	private List<PrePaidAccountReqVo> buildPrePaidAccountReqVoList(List<PrePaidAccountReq> reqDtoList) {
		List<PrePaidAccountReqVo> result = new ArrayList<>();
		for (PrePaidAccountReq dto : reqDtoList) {
			result.add(new PrePaidAccountReqVo(dto.getId(), PrePaidAccountReqTypeEnum.fromCode(dto.getReqType()), 
					dto.getStoreId(), dto.getAmount(), dto.getCreaterId(), dto.getRemarks(), 
					PrePaidAccountReqStatusEnum.fromCode(dto.getReqStatus()), 
					PrePaidAccountReqRefBillTypeEnum.fromCode(dto.getRefBillType()), dto.getRefBillId(), dto.getRefBillTime()));
		}
		return result;
	}

	@Autowired
	public void setStoreSettlementMapper(StoreSettlementMapper storeSettlementMapper) {
		this.storeSettlementMapper = storeSettlementMapper;
	}

	@Autowired
	public void setPrePaidAccountReqMapper(PrePaidAccountReqMapper prePaidAccountReqMapper) {
		this.prePaidAccountReqMapper = prePaidAccountReqMapper;
	}

	@Autowired
	public void setRedissonClient(RedissonClient redissonClient) {
		this.redissonClient = redissonClient;
	}

	@Autowired
	public void setPrePaidAccountBatchService(PrePaidAccountBatchService prePaidAccountBatchService) {
		this.prePaidAccountBatchService = prePaidAccountBatchService;
	}

	@Autowired
	public void setStoreRechargeService(StoreRechargeService storeRechargeService) {
		this.storeRechargeService = storeRechargeService;
	}


	@Value("${prePaidAccountRecordLockWaitTimeSec:10}")
	public void setPrePaidAccountRecordLockWaitTimeSec(Long prePaidAccountRecordLockWaitTimeSec) {
		this.prePaidAccountRecordLockWaitTimeSec = prePaidAccountRecordLockWaitTimeSec;
	}
}
