package com.pinshang.qingyun.order.mapper.cup;

import com.pinshang.qingyun.order.dto.cup.*;
import com.pinshang.qingyun.order.model.commodity.Commodity;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;

@Repository
public interface OrderCupReportMapper{

    List<NoOrderStoreODTO> noOrderPageSearch(@Param("vo") NoOrderSearchPageIDTO idto);

    List<OrderTimeCountODTO> findStoreOrder(@Param("storeId") Long storeId, @Param("startDate") Date startDate, @Param("endDate") Date endDate);

    List<DeliveryTimeODTO> getDeliveryTime();

    List<Commodity> findStoreCommodityByStoreIdAndCommodityCode(@Param("storeId") String storeId, @Param("orderTime") String orderTime, @Param("commodityCodeList") List<String> commodityCodeList, @Param("addCommodityType") Integer addCommodityType);

    List<OrderCupPageResultODTO> findOrderListByPage(@Param("vo") OrderCupPageQueryIDTO vo);
}
