package com.pinshang.qingyun.order.service;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.pinshang.qingyun.base.api.TokenInfo;
import com.pinshang.qingyun.base.configure.expand.QYAssert;
import com.pinshang.qingyun.base.enums.shop.ShopTypeEnums;
import com.pinshang.qingyun.base.page.TablePageInfo;
import com.pinshang.qingyun.base.util.FastThreadLocalUtil;
import com.pinshang.qingyun.box.utils.DateTimeUtil;
import com.pinshang.qingyun.box.utils.DateUtil;
import com.pinshang.qingyun.box.utils.StringUtil;
import com.pinshang.qingyun.common.dto.DictionaryODTO;
import com.pinshang.qingyun.common.service.DictionaryClient;
import com.pinshang.qingyun.order.dto.report.*;
import com.pinshang.qingyun.order.mapper.CommodityMapper;
import com.pinshang.qingyun.order.mapper.OrderReportMapper;
import com.pinshang.qingyun.order.mapper.entry.StoreEntry;
import com.pinshang.qingyun.order.mapper.entry.commodity.CommodityBasicEntry;
import com.pinshang.qingyun.order.mapper.entry.shop.ReturnOrderDetailsReportEntry;
import com.pinshang.qingyun.order.model.shop.Shop;
import com.pinshang.qingyun.order.service.bigShop.UserStallServiceImpl;
import com.pinshang.qingyun.order.util.BeanUtil;
import com.pinshang.qingyun.order.util.StallUtils;
import com.pinshang.qingyun.order.vo.commodity.CommodityVO;
import com.pinshang.qingyun.order.vo.shop.ReturnOrderDetailsReportVo;
import com.pinshang.qingyun.price.service.CommodityPriceClient;
import com.pinshang.qingyun.product.dto.commodity.CommodityDetailODTO;
import com.pinshang.qingyun.product.service.CommodityClient;
import com.pinshang.qingyun.shop.dto.ShopDto;
import com.pinshang.qingyun.shop.service.ShopClient;
import com.pinshang.qingyun.smm.dto.userstall.SelectUserStallIdListIDTO;
import com.pinshang.qingyun.smm.service.UserStallClient;
import com.pinshang.qingyun.storage.dto.CommoditySupplierODto;
import com.pinshang.qingyun.storage.dto.CommodityWarehouseODto;
import com.pinshang.qingyun.storage.service.CommoditySupplierClient;
import com.pinshang.qingyun.storage.service.CommodityWarehouseClient;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.text.ParseException;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@Slf4j
public class OrderReportService {
    @Autowired
    OrderReportMapper orderReportMapper;

    @Autowired
    private DictionaryClient dictionaryClient;
    @Autowired
    private ShopService shopService;
    @Autowired
    private UserStallServiceImpl userStallServiceImpl;
    @Autowired
    private CommodityMapper commodityMapper;
    @Autowired
    private CommonService commonService;
    @Autowired
    private CommodityWarehouseClient commodityWarehouseClient;
    @Autowired
    private CommodityClient commodityClient;
    @Autowired
    private CommodityPriceClient commodityPriceClient;
    @Autowired
    private CommoditySupplierClient commoditySupplierClient;

    @Autowired
    private ShopClient shopClient;
    @Autowired
    private UserStallClient userStallClient;

    public PageInfo<ReturnOrderDetailsReportEntry> findReturnOrderDetailsReport(ReturnOrderDetailsReportVo returnOrderDetailsReportVo) throws ParseException {
        String orderTimeStartStr = returnOrderDetailsReportVo.getOrderTimeStartStr();
        String orderTimeEndStr = returnOrderDetailsReportVo.getOrderTimeEndStr();

        if(StringUtils.isNotBlank(orderTimeStartStr) && StringUtils.isNotBlank(orderTimeEndStr)){
            returnOrderDetailsReportVo.setOrderTimeStartStr(orderTimeStartStr + " 00:00:00");
            returnOrderDetailsReportVo.setOrderTimeEndStr(orderTimeEndStr + " 23:59:59");
        }

        TokenInfo tokenInfo = FastThreadLocalUtil.getQY();
        if (StallUtils.isSingleShop(tokenInfo.getShopId()) && StallUtils.isStallSubcontractor(tokenInfo.getManagementMode()) && Objects.isNull(returnOrderDetailsReportVo.getStallId())) {
            //单门店 大店，没传档口，则查询用户权限下的档口
            List<Long> stallIdList = userStallServiceImpl.selectUserStallIdList(tokenInfo.getUserId(), tokenInfo.getShopId());
            if (CollectionUtils.isEmpty(stallIdList)) {
                //没有档口，返回空的列表
                return PageInfo.of(Collections.emptyList());
            }
            returnOrderDetailsReportVo.setStallIdList(stallIdList);
        }

        PageInfo<ReturnOrderDetailsReportEntry> pageData = PageHelper.startPage(returnOrderDetailsReportVo.getPageNo(), returnOrderDetailsReportVo.getPageSize()).doSelectPageInfo(() -> {
            orderReportMapper.findReturnOrderDetails(returnOrderDetailsReportVo);
        });
        List<ReturnOrderDetailsReportEntry> list = pageData.getList();
        if(CollectionUtils.isNotEmpty(list)){
            List<Long> storeIdList = list.stream().distinct().map(item -> item.getStoreId()).collect(Collectors.toList());
            List<Long> commodityIdList = list.stream().distinct().map(item -> item.getCommodityId()).collect(Collectors.toList());

            List<DictionaryODTO> dictList = dictionaryClient.listDictionaryByOptionName("门店退货原因");
            Map<String, String> dictMap = dictList.stream().collect(Collectors.toMap(DictionaryODTO::getOptionCode,DictionaryODTO::getOptionName,(key1 , key2)-> key2));

            List<Shop> shopList = shopService.getShopByStoreIdList(storeIdList);
            Map<Long, String> shopMap = shopList.stream().collect(Collectors.toMap(Shop::getStoreId,Shop::getShopName,(key1 , key2)-> key2));

            List<CommodityBasicEntry> commodityBasicList = commodityMapper.findCommodityBarCodeByIds(commodityIdList);
            Map<String, String> commodityBasicMap = commodityBasicList.stream().collect(Collectors.toMap(CommodityBasicEntry::getCommodityId,CommodityBasicEntry::getBarCodes,(key1 , key2)-> key2));

            for(ReturnOrderDetailsReportEntry entry : list){
                entry.setShopName(shopMap.get(entry.getStoreId()));
                entry.setReturnReasonName(dictMap.get(entry.getReturnReason() + ""));
                entry.setBarCodes(commodityBasicMap.get(entry.getCommodityId() + ""));

                BigDecimal price = entry.getPrice();
                BigDecimal quantity = entry.getReturnQuantity();
                if(price != null && quantity != null) {
                    entry.setTotalPrice(price.multiply(quantity).setScale(2,BigDecimal.ROUND_HALF_UP));
                }
            }
        }
        return pageData;
    }


    /**
     * 短交报表
     * @param vo
     * @return
     */
    public PageInfo<ShortDeliveryReportODto> shortDeliveryReport(ShortDeliveryReportIDto vo) {
        PageInfo<ShortDeliveryReportODto> pageDate = new PageInfo<>();

        QYAssert.isTrue(!StringUtil.isBlank(vo.getBeginDate()), "请选择送货日期");
        QYAssert.isTrue(!StringUtil.isBlank(vo.getEndDate()), "请选择送货日期");
        QYAssert.isTrue(DateUtil.isAfter(DateUtil.addMonth(DateTimeUtil.parse(vo.getBeginDate(), "yyyy-MM-dd"),1), DateTimeUtil.parse(vo.getEndDate(), "yyyy-MM-dd")), "送货日期范围不能超过一个月!");
        vo.setShopId((vo.getShopId() != null && vo.getShopId() == 0 ) ? null : vo.getShopId());
        /*if(!StringUtil.isBlank(vo.getBeginDate()) && !StringUtil.isBlank(vo.getEndDate())){
            vo.setBeginDate(vo.getBeginDate()+ " 00:00:00");
            vo.setEndDate(vo.getEndDate()+ " 23:59:59");
        }*/

        if (!StringUtil.isNullOrEmpty(vo.getOrgCode())) {
            List<ShopDto> shopDtos = shopClient.selectShopListByParentOrgCode(vo.getOrgCode());
            if (!org.springframework.util.CollectionUtils.isEmpty(shopDtos)) {
                List<Long> orgShopIdList = shopDtos.stream().map(ShopDto::getId).collect(Collectors.toList());
                if(CollectionUtils.isNotEmpty(orgShopIdList)){
                    vo.setShopIdList(orgShopIdList);
                }else {
                    return pageDate;
                }
            }else {
                return pageDate;
            }
        }

        if(vo.getShopType() != null){
           List<Long> storeIdList = shopService.getStoreIdListByShopType(vo.getShopType());
           if(CollectionUtils.isNotEmpty(storeIdList)){
               vo.setStoreIdList(storeIdList);
           }else {
               return pageDate;
           }
        }
        if(vo.getShopType() == null && vo.getShopId() != null){
            //判断登录人门店是不是工坊店
            ShopDto shopDto = shopClient.findShopById(vo.getShopId());
            QYAssert.isTrue(shopDto!=null,"没有查到登录人门店权限");
            if(ShopTypeEnums.GF.getCode().equals(shopDto.getShopType())){
                vo.setIsStall(Boolean.TRUE);
                //非总部的档口查询需要判断用户档口权限
                SelectUserStallIdListIDTO userStallIdListIDTO = new SelectUserStallIdListIDTO();
                userStallIdListIDTO.setUserId(FastThreadLocalUtil.getQY().getUserId());
                userStallIdListIDTO.setShopId(FastThreadLocalUtil.getQY().getShopId());
                userStallIdListIDTO.setStallStatus(1);
                List<Long> stallList = userStallClient.selectUserStallIdList(userStallIdListIDTO);
                if(CollectionUtils.isNotEmpty(stallList)){
                    if(vo.getStallId() == null){
                        vo.setStallList(stallList);
                    }
                    if(vo.getStallId() != null && !stallList.contains(vo.getStallId())){
                        return new PageInfo();
                    }
                }else {
                    //沒有档口权限直接返回空
                    return pageDate;
                }
            }
        }
        List<Long> commodityIdListAll = new ArrayList<>();
        Boolean isCheck = false;
        if(StringUtils.isNotBlank(vo.getFactoryCode())){
            isCheck = true;
            List<Long> commodityIdList = commodityClient.findCommodityIdListByParam(vo.getFactoryCode(),"","");
            if(CollectionUtils.isNotEmpty(commodityIdList)){
                if(CollectionUtils.isNotEmpty(commodityIdListAll)){
                    commodityIdListAll.retainAll(commodityIdList);
                }else {
                    commodityIdListAll.addAll(commodityIdList);
                }
            }else {
                return pageDate;
            }
        }
        if(vo.getCateId1() != null || vo.getCateId2() != null || vo.getCateId3() != null
                || StringUtils.isNotBlank(vo.getCommodityKey()) || StringUtils.isNotBlank(vo.getBarCode())){
            isCheck = true;
            CommodityVO commodityVO = new CommodityVO();
            BeanUtils.copyProperties(vo,commodityVO);
            List<CommodityBasicEntry> commList = commodityMapper.findCommodityBasicListByParam(commodityVO);
            if(CollectionUtils.isNotEmpty(commList)){
                List<Long> commodityIdList = commList.stream().map(item -> Long.valueOf(item.getCommodityId())).collect(Collectors.toList());
                if(CollectionUtils.isNotEmpty(commodityIdListAll)){
                    commodityIdListAll.retainAll(commodityIdList);
                }else {
                    commodityIdListAll.addAll(commodityIdList);
                }
            }else {
                return  pageDate;
            }
        }
        if(isCheck && CollectionUtils.isEmpty(commodityIdListAll)){
            return  pageDate;
        }
        vo.setCommodityIdList(commodityIdListAll);

        pageDate  = PageHelper.startPage(vo.getPageNo(), vo.getPageSize()).doSelectPageInfo(() -> {
            orderReportMapper.shortDeliveryTodayReport(vo);
        });

        List<ShortDeliveryReportODto> list = pageDate.getList();
        // 设置返回值
        setShortDelivery(list);
        return pageDate ;
    }

    /**
     * 短交报表
     * @param list
     */
    private void setShortDelivery(List<ShortDeliveryReportODto> list) {
        if(CollectionUtils.isNotEmpty(list)){
            List<Long> commodityIdList = list.stream().map(item -> Long.valueOf(item.getCommodityId())).distinct().collect(Collectors.toList());
            Map<Long, CommodityWarehouseODto> warehouseMap = commodityWarehouseClient.queryCommodityDefaultWarehouse(commodityIdList);
            Map<Long,String> barCodeMap = commonService.getCommodityBarCodeMap(commodityIdList,null);

            /*CommodityVO commodityVO = new CommodityVO();
            commodityVO.setCommodityIdList(commodityIdList);
            List<CommodityBasicEntry> commList = commodityMapper.findCommodityBasicListByParam(commodityVO);
            Map<String, CommodityBasicEntry> commMap = commList.stream().collect(Collectors.toMap(CommodityBasicEntry::getCommodityId, Function.identity()));

            List<Long> cateIdList = new ArrayList<>();
            cateIdList.addAll(commList.stream().map(item -> Long.valueOf(item.getCommodityFirstId())).collect(Collectors.toList()));
            cateIdList.addAll(commList.stream().map(item -> Long.valueOf(item.getCommoditySecondId())).collect(Collectors.toList()));
            cateIdList.addAll(commList.stream().map(item -> Long.valueOf(item.getCommodityThirdId())).collect(Collectors.toList()));
            Map<String, String> cateMap = commonService.getCategroyMap(cateIdList);*/

            //Map<String, String> dictionaryMap = commonService.getDictionaryMap();
            Map<String, CommodityDetailODTO> factoryCommodityMap = commonService.getFactoryMap(commodityIdList);

           /* List<Long> createIdList = list.stream().map(item -> item.getCreateId()).distinct().collect(Collectors.toList());
            Map<Long, User> userMap = commonService.getUserMap(createIdList);*/

            List<Long> storeIdList = list.stream().map(item -> item.getStoreId()).distinct().collect(Collectors.toList());
            List<StoreEntry> storeList = shopService.getStoreByStoreIdList(storeIdList);
            Map<Long, StoreEntry> storeMap = storeList.stream().collect(Collectors.toMap(StoreEntry::getStoreId, Function.identity()));

            for(ShortDeliveryReportODto entry : list){
                CommodityWarehouseODto commodityWarehouseODto = warehouseMap.get(Long.valueOf(entry.getCommodityId()));
                if(commodityWarehouseODto != null){
                    entry.setWarehouseName(commodityWarehouseODto.getWarehouseName());
                }
                /*CommodityBasicEntry commodityBasic = commMap.get(entry.getCommodityId());
                if(commodityBasic != null){
                    BeanUtils.copyProperties(commodityBasic,entry);
                    entry.setCategoryName(cateMap.get(commodityBasic.getCommodityFirstId() + ""));
                    entry.setSecondCategoryName(cateMap.get(commodityBasic.getCommoditySecondId() + ""));
                    entry.setThirdCategoryName(cateMap.get(commodityBasic.getCommodityThirdId() + ""));
                }*/
                CommodityDetailODTO commodityDetail = factoryCommodityMap.get(entry.getCommodityId());
                if(commodityDetail != null){
                    BeanUtils.copyProperties(commodityDetail,entry);
                }
               /* User user = userMap.get(entry.getCreateId());
                if(user != null){
                    entry.setCreateName(user.getEmployeeName());
                }*/
                StoreEntry storeEntry = storeMap.get(entry.getStoreId());
                if(storeEntry != null){
                    BeanUtils.copyProperties(storeEntry,entry);
                }
                String barCodes = barCodeMap.get(Long.valueOf(entry.getCommodityId()));
                entry.setBarCodes(barCodes);
                entry.setBarCode(barCodes != null ? barCodes.split(",")[0] : "");

                // entry.setCommodityUnitName(dictionaryMap.get(entry.getCommodityUnitId() + ""));

            }
        }
    }




    /**
     * 商品实发汇总表(当日)
     * @param vo
     * @return
     */
    public TablePageInfo<RealDeliveryReportODto> realDeliveryReportCurrentDay(RealDeliveryReportIDto vo) {
        TablePageInfo tablePageInfo = new TablePageInfo();
        QYAssert.isTrue(!StringUtil.isBlank(vo.getBeginDate()), "请选择送货日期");
        QYAssert.isTrue(!StringUtil.isBlank(vo.getEndDate()), "请选择送货日期");

        List<Long> commodityIdListAll = new ArrayList<>();
        Boolean isCheck = false;

        if(StringUtils.isNotBlank(vo.getFactoryCode())
                || StringUtils.isNotBlank(vo.getWorkshopCodeOrName())
                || StringUtils.isNotBlank(vo.getFlowshopCodeOrName())){
            isCheck = true;
            List<Long> commodityIdList = commodityClient.findCommodityIdListByParam(vo.getFactoryCode(),vo.getWorkshopCodeOrName(),vo.getFlowshopCodeOrName());
            if(CollectionUtils.isNotEmpty(commodityIdList)){
                if(CollectionUtils.isNotEmpty(commodityIdListAll)){
                    commodityIdListAll.retainAll(commodityIdList);
                }else {
                    commodityIdListAll.addAll(commodityIdList);
                }
            }else {
                return tablePageInfo;
            }
        }
        if(vo.getCategoryId() != null || StringUtils.isNotBlank(vo.getCommodityKey()) || StringUtils.isNotBlank(vo.getBarCode())){
            isCheck = true;
            CommodityVO commodityVO = new CommodityVO();
            commodityVO.setCommodityKey(vo.getCommodityKey());
            commodityVO.setCateId1(vo.getCategoryId());
            commodityVO.setBarCode(vo.getBarCode());
            List<CommodityBasicEntry> commList = commodityMapper.findCommodityBasicListByParam(commodityVO);
            if(CollectionUtils.isNotEmpty(commList)){
                List<Long> commodityIdList = commList.stream().map(item -> Long.valueOf(item.getCommodityId())).collect(Collectors.toList());
                if(CollectionUtils.isNotEmpty(commodityIdListAll)){
                    commodityIdListAll.retainAll(commodityIdList);
                }else {
                    commodityIdListAll.addAll(commodityIdList);
                }
            }else {
                return  tablePageInfo;
            }
        }
        if(isCheck && CollectionUtils.isEmpty(commodityIdListAll)){
            return  tablePageInfo;
        }
        vo.setCommodityIdList(commodityIdListAll);

        RealDeliveryReportDataQueryIDto iDto = new RealDeliveryReportDataQueryIDto();
        iDto.setBeginDate(vo.getBeginDate());
        iDto.setEndDate(vo.getEndDate());
        iDto.setDiffer(vo.getDiffer());
        iDto.setCommodityIdList(vo.getCommodityIdList());
        iDto.setPageNo(vo.getPageNo());
        iDto.setPageSize(vo.getPageSize());
        iDto.setConsignmentId(vo.getConsignmentId());

        PageInfo<RealDeliveryReportODto>  pageDate = PageHelper.startPage(vo.getPageNo(), vo.getPageSize()).doSelectPageInfo(() -> {
            orderReportMapper.realDeliveryReportCurrentDay(iDto);
        });   // 设置返回值
        BigDecimal realTotalAmount = setRealDeliveryReport(iDto, pageDate.getList());
        tablePageInfo = BeanUtil.pageInfo2TablePageInfo(pageDate, TablePageInfo.class);
        tablePageInfo.setHeader(realTotalAmount);
        return tablePageInfo;
    }

    /**
     * 商品实发汇总表(当日)
     * @param vo
     * @param list
     * @return
     */
    private BigDecimal setRealDeliveryReport(RealDeliveryReportDataQueryIDto vo, List<RealDeliveryReportODto> list) {
        BigDecimal realTotalAmount = BigDecimal.ZERO;
        if(null !=list && !list.isEmpty()){
            List<Long> commodityIdList = list.stream().map(item -> Long.valueOf(item.getCommodityId())).distinct().collect(Collectors.toList());
            Map<Long,String> barCodeMap = commonService.getCommodityBarCodeMap(commodityIdList,null);

            CommodityVO commodityVO = new CommodityVO();
            commodityVO.setCommodityIdList(commodityIdList);
            List<CommodityBasicEntry> commList = commodityMapper.findCommodityBasicListByParam(commodityVO);
            Map<String, CommodityBasicEntry> commMap = commList.stream().collect(Collectors.toMap(CommodityBasicEntry::getCommodityId, Function.identity()));

            List<Long> cateIdList = new ArrayList<>();
            cateIdList.addAll(commList.stream().map(item -> Long.valueOf(item.getCommodityFirstId())).collect(Collectors.toList()));
            //Map<String, String> cateMap = commonService.getCategroyMap(cateIdList);
            Map<String, CommodityDetailODTO> factoryCommodityMap = commonService.getFactoryMap(commodityIdList);
           // Map<String, String> dictionaryMap = commonService.getDictionaryMap();

            for(RealDeliveryReportODto entry:list){
                CommodityBasicEntry commodityBasic = commMap.get(entry.getCommodityId() + "");
                if(commodityBasic != null){
                    BeanUtils.copyProperties(commodityBasic,entry);
                }
                CommodityDetailODTO commodityDetail = factoryCommodityMap.get(entry.getCommodityId() + "");
                if(commodityDetail != null){
                    entry.setFactoryName(commodityDetail.getFactoryName());
                    entry.setWorkshopName(commodityDetail.getWorkshopName());
                    entry.setFlowshopName(commodityDetail.getFlowshopName());
                }

                String barCodes = barCodeMap.get(Long.valueOf(entry.getCommodityId()));
                entry.setBarCodes(barCodes);
                entry.setBarCode(barCodes != null ? barCodes.split(",")[0] : "");
                entry.setRealDeliveryAmount(null != entry.getRealDeliveryAmount() ? entry.getRealDeliveryAmount().setScale(2, BigDecimal.ROUND_HALF_UP) : BigDecimal.ZERO);

            }
            //获取实发总金额
            realTotalAmount = orderReportMapper.realTotalDeliveryReportCurrentDay(vo);
            if(realTotalAmount==null){
                realTotalAmount = BigDecimal.ZERO;
            }
            realTotalAmount = realTotalAmount.setScale(2, BigDecimal.ROUND_HALF_UP);
        }
        return realTotalAmount;
    }




    /**
     * 商品实发汇总表(当日)---------------(按客户类型)
     * @param vo
     * @return
     */
    public TablePageInfo<RealDeliveryReportODto> realDeliveryStoreTypeReportCurrentDay(RealDeliveryReportIDto vo) {
        TablePageInfo tablePageInfo = new TablePageInfo();
        QYAssert.isTrue(!StringUtil.isBlank(vo.getBeginDate()), "请选择送货日期");
        QYAssert.isTrue(!StringUtil.isBlank(vo.getEndDate()), "请选择送货日期");

        List<Long> commodityIdListAll = new ArrayList<>();
        Boolean isCheck = false;
        if(vo.getWarehouseId() != null){
            isCheck = true;
            List<Long> commodityIdList = commodityWarehouseClient.queryCommodityIdsByDefaultWarehouseid(vo.getWarehouseId());
            if(CollectionUtils.isNotEmpty(commodityIdList)){
                if(CollectionUtils.isNotEmpty(commodityIdListAll)){
                    commodityIdListAll.retainAll(commodityIdList);
                }else {
                    commodityIdListAll.addAll(commodityIdList);
                }
            }else {
                return tablePageInfo;
            }
        }

        if(StringUtils.isNotBlank(vo.getFactoryCode())
                || StringUtils.isNotBlank(vo.getWorkshopCodeOrName())
                || StringUtils.isNotBlank(vo.getFlowshopCodeOrName())){
            isCheck = true;
            List<Long> commodityIdList = commodityClient.findCommodityIdListByParam(vo.getFactoryCode(),vo.getWorkshopCodeOrName(),vo.getFlowshopCodeOrName());
            if(CollectionUtils.isNotEmpty(commodityIdList)){
                if(CollectionUtils.isNotEmpty(commodityIdListAll)){
                    commodityIdListAll.retainAll(commodityIdList);
                }else {
                    commodityIdListAll.addAll(commodityIdList);
                }
            }else {
                return tablePageInfo;
            }
        }
        if(vo.getCategoryId() != null || StringUtils.isNotBlank(vo.getCommodityKey()) || StringUtils.isNotBlank(vo.getBarCode())){
            isCheck = true;
            CommodityVO commodityVO = new CommodityVO();
            commodityVO.setCommodityKey(vo.getCommodityKey());
            commodityVO.setCateId1(vo.getCategoryId());
            commodityVO.setBarCode(vo.getBarCode());
            List<CommodityBasicEntry> commList = commodityMapper.findCommodityBasicListByParam(commodityVO);
            if(CollectionUtils.isNotEmpty(commList)){
                List<Long> commodityIdList = commList.stream().map(item -> Long.valueOf(item.getCommodityId())).collect(Collectors.toList());
                if(CollectionUtils.isNotEmpty(commodityIdListAll)){
                    commodityIdListAll.retainAll(commodityIdList);
                }else {
                    commodityIdListAll.addAll(commodityIdList);
                }
            }else {
                return  tablePageInfo;
            }
        }

        if(isCheck && CollectionUtils.isEmpty(commodityIdListAll)){
            return  tablePageInfo;
        }
        vo.setCommodityIdList(commodityIdListAll);

        PageInfo<RealDeliveryReportODto> pageDate = null;
        pageDate = PageHelper.startPage(vo.getPageNo(), vo.getPageSize()).doSelectPageInfo(() -> {
            orderReportMapper.realDeliveryStoreTypeReportCurrentDay(vo);
        });
        List<RealDeliveryReportODto> list = pageDate.getList();
        // 设置返回值
        BigDecimal realTotalAmount = setRealDeliveryStoreTypeReport(vo, list);
        tablePageInfo = BeanUtil.pageInfo2TablePageInfo(pageDate, TablePageInfo.class);
        tablePageInfo.setHeader(realTotalAmount);
        return tablePageInfo;
    }

    /**
     * 商品实发汇总表(当日)---------------(按客户类型)
     * @param vo
     * @param list
     * @return
     */
    private BigDecimal setRealDeliveryStoreTypeReport(RealDeliveryReportIDto vo, List<RealDeliveryReportODto> list) {
        BigDecimal realTotalAmount = BigDecimal.ZERO;
        if(CollectionUtils.isNotEmpty(list)){
            List<Long> commodityIdList = list.stream().map(item -> Long.valueOf(item.getCommodityId())).distinct().collect(Collectors.toList());
            Map<Long, CommodityWarehouseODto> wareHouseMap = commodityWarehouseClient.queryCommodityDefaultWarehouse(commodityIdList);
            Map<Long,String> barCodeMap = commonService.getCommodityBarCodeMap(commodityIdList,null);

            CommodityVO commodityVO = new CommodityVO();
            commodityVO.setCommodityIdList(commodityIdList);
            List<CommodityBasicEntry> commList = commodityMapper.findCommodityBasicListByParam(commodityVO);
            Map<String, CommodityBasicEntry> commMap = commList.stream().collect(Collectors.toMap(CommodityBasicEntry::getCommodityId, Function.identity()));

            /*List<Long> cateIdList = new ArrayList<>();
            cateIdList.addAll(commList.stream().map(item -> Long.valueOf(item.getCommodityFirstId())).collect(Collectors.toList()));
            Map<String, String> cateMap = commonService.getCategroyMap(cateIdList);*/

            Map<String, CommodityDetailODTO> factoryCommodityMap = commonService.getFactoryMap(commodityIdList);

            // Map<String, String> dictionaryMap = commonService.getDictionaryMap();

            for(RealDeliveryReportODto entry:list){
                CommodityBasicEntry commodityBasic = commMap.get(entry.getCommodityId() + "");
                if(commodityBasic != null){
                    BeanUtils.copyProperties(commodityBasic,entry);
                }
                CommodityDetailODTO commodityDetail = factoryCommodityMap.get(entry.getCommodityId() + "");
                if(commodityDetail != null){
                    entry.setFactoryName(commodityDetail.getFactoryName());
                    entry.setWorkshopName(commodityDetail.getWorkshopName());
                    entry.setFlowshopName(commodityDetail.getFlowshopName());
                }

                // entry.setStoreTypeName(dictionaryMap.get(entry.getStoreTypeId() + ""));
                String barCodes = barCodeMap.get(Long.valueOf(entry.getCommodityId()));
                entry.setBarCodes(barCodes);
                entry.setBarCode(barCodes != null ? barCodes.split(",")[0] : "");
                entry.setRealDeliveryAmount(null != entry.getRealDeliveryAmount() ? entry.getRealDeliveryAmount().setScale(2, BigDecimal.ROUND_HALF_UP) : BigDecimal.ZERO);

                CommodityWarehouseODto warehouseODto = wareHouseMap.get(entry.getCommodityId());
                if(warehouseODto != null){
                    entry.setWarehouseName(warehouseODto.getWarehouseName());
                }
            }
            //获取实发总金额
            realTotalAmount = orderReportMapper.realTotalDeliveryStoreTypeReportCurrentDay(vo);
            realTotalAmount = realTotalAmount.setScale(2, BigDecimal.ROUND_HALF_UP);
        }
        return realTotalAmount;
    }





    /**
     * 门店订货汇总表(当日)
     * @param idto
     * @return
     */
    public PageInfo<ShopOrderGoodReportODto> shopOrderGoodReportCurrentDay(ShopOrderGoodReportIDto idto) {
        idto.check();

        if(idto.getShopType() != null){
            List<Long> storeIdList = shopService.getStoreIdListByShopType(idto.getShopType());
            if(CollectionUtils.isNotEmpty(storeIdList)){
                idto.setStoreIdList(storeIdList);
            }else {
                return new PageInfo<>();
            }
        }

        List<Long> commodityIdListAll = idto.getCommodityIdList();
        if(idto.getCate1() != null || idto.getCate2() != null || idto.getCate3() != null || StringUtils.isNotBlank(idto.getBarCode())){
            CommodityVO commodityVO = new CommodityVO();
            commodityVO.setBarCode(idto.getBarCode());
            commodityVO.setCateId1(idto.getCate1());
            commodityVO.setCateId2(idto.getCate2());
            commodityVO.setCateId3(idto.getCate3());
            List<CommodityBasicEntry> commList = commodityMapper.findCommodityBasicListByParam(commodityVO);
            if(CollectionUtils.isNotEmpty(commList)){
                List<Long> commodityIdList = commList.stream().map(item -> Long.valueOf(item.getCommodityId())).collect(Collectors.toList());
                if(CollectionUtils.isNotEmpty(commodityIdListAll)){
                    commodityIdListAll.retainAll(commodityIdList);
                }else {
                    commodityIdListAll.addAll(commodityIdList);
                }
            }else {
                return  new PageInfo<>();
            }
        }
        if(StringUtils.isNotBlank(idto.getFactoryCode())){
            List<Long> commodityIdList = commodityClient.findCommodityIdListByParam(idto.getFactoryCode(),"","");
            if(CollectionUtils.isNotEmpty(commodityIdList)){
                if(CollectionUtils.isNotEmpty(commodityIdListAll)){
                    commodityIdListAll.retainAll(commodityIdList);
                }else {
                    commodityIdListAll.addAll(commodityIdList);
                }
            }else {
                return new PageInfo<>();
            }
        }
        if(CollectionUtils.isEmpty(commodityIdListAll)){
            return  new PageInfo<>();
        }
        idto.setCommodityIdList(commodityIdListAll);

        PageInfo<ShopOrderGoodReportODto> pageDate = null;
        pageDate = PageHelper.startPage(idto.getPageNo(), idto.getPageSize()).doSelectPageInfo(() -> {
            orderReportMapper.shopOrderGoodReport(idto);
        });

        List<ShopOrderGoodReportODto> list = pageDate.getList();
        // 设置 门店订货汇总表(当日) 返回值
        setShopOrderGoodReportData(list);
        return pageDate;
    }

    /**
     * 门店订货汇总表(当日)
     * @param list
     */
    private void setShopOrderGoodReportData(List<ShopOrderGoodReportODto> list) {
        if(CollectionUtils.isNotEmpty(list)){
            List<Long> commodityIdList = list.stream().map(item -> Long.valueOf(item.getCommodityId())).distinct().collect(Collectors.toList());
            Map<Long,String> barCodeMap = commonService.getCommodityBarCodeMap(commodityIdList,null);

            CommodityVO commodityVO = new CommodityVO();
            commodityVO.setCommodityIdList(commodityIdList);
            List<CommodityBasicEntry> commList = commodityMapper.findCommodityBasicListByParam(commodityVO);
            Map<String, CommodityBasicEntry> commMap = commList.stream().collect(Collectors.toMap(CommodityBasicEntry::getCommodityId, Function.identity()));

            /*List<Long> cateIdList = new ArrayList<>();
            cateIdList.addAll(commList.stream().map(item -> Long.valueOf(item.getCommodityFirstId())).distinct().collect(Collectors.toList()));
            cateIdList.addAll(commList.stream().map(item -> Long.valueOf(item.getCommoditySecondId())).distinct().collect(Collectors.toList()));
            cateIdList.addAll(commList.stream().map(item -> Long.valueOf(item.getCommodityThirdId())).distinct().collect(Collectors.toList()));
            Map<String, String> cateMap = commonService.getCategroyMap(cateIdList);*/

            List<Long> storeIdList = list.stream().map(item -> item.getStoreId()).distinct().collect(Collectors.toList());
            List<StoreEntry> storeList = shopService.getStoreByStoreIdList(storeIdList);
            Map<Long, StoreEntry> storeMap = storeList.stream().collect(Collectors.toMap(StoreEntry::getStoreId, Function.identity()));

            /*List<Long> shopIdList = storeList.stream().map(item -> item.getShopId()).distinct().collect(Collectors.toList());
            Map<Long, OrgAndParentInfoODTO> orgMap = commonService.getOrgMap(shopIdList);*/
            Map<String, CommodityDetailODTO> factoryCommodityMap = commonService.getFactoryMap(commodityIdList);
            // Map<String, String> dictionaryMap = commonService.getDictionaryMap();

            for(ShopOrderGoodReportODto entry : list){
                CommodityBasicEntry commodityBasic = commMap.get(entry.getCommodityId() + "");
                if(commodityBasic != null){
                    BeanUtils.copyProperties(commodityBasic,entry);
                   /* entry.setCommodityFirstName(cateMap.get(commodityBasic.getCommodityFirstId() + ""));
                    entry.setCommoditySecondName(cateMap.get(commodityBasic.getCommoditySecondId() + ""));
                    entry.setCommodityThirdName(cateMap.get(commodityBasic.getCommodityThirdId() + ""));
                    entry.setCommodityUnit(dictionaryMap.get(commodityBasic.getCommodityUnitId() + ""));*/
                }
                CommodityDetailODTO commodityDetail = factoryCommodityMap.get(entry.getCommodityId() + "");
                if(commodityDetail != null){
                    entry.setFactoryName(commodityDetail.getFactoryName());
                    entry.setWorkshopName(commodityDetail.getWorkshopName());
                }

                StoreEntry storeEntry = storeMap.get(entry.getStoreId());
                if(storeEntry != null){
                    entry.setShopId(storeEntry.getShopId());
                    entry.setShopName(storeEntry.getShopName());
                    entry.setShopType(storeEntry.getShopType());
                    entry.setShopCode(storeEntry.getShopCode());
                }

                String[] barCode = barCodeMap.get(Long.valueOf(entry.getCommodityId())).split(",");
                entry.setBarCode(barCode[0]);
                List barCodeList = java.util.Arrays.asList(barCode);
                entry.setBarCodeList(barCodeList);
                entry.setRealDeliveryAmount(null != entry.getRealDeliveryAmount() ? entry.getRealDeliveryAmount().setScale(2, BigDecimal.ROUND_HALF_UP) : BigDecimal.ZERO);

                /*OrgAndParentInfoODTO orgODTO = orgMap.get(entry.getShopId());
                if(orgODTO != null){
                    entry.setOrgName(orgODTO.getParentOrgName());
                }*/
            }
        }
    }





    /**
     * 门店实收商品分析表
     * @param vo
     * @return
     * @throws ParseException
     */
    public PageInfo<ActualReceiptAnalysisODto> actualReceiptAnalysisReport(ActualReceiptAnalysisIDto vo) throws ParseException{
        PageInfo<ActualReceiptAnalysisODto> pageDate = new PageInfo<>();
        QYAssert.isTrue(!StringUtil.isBlank(vo.getBeginDate()) && !StringUtil.isBlank(vo.getEndDate()), "请选择日期");
        //vo.setOrderBeginDate(DateUtils.parseDate(vo.getBeginDate() + " 00:00:00", "yyyy-MM-dd HH:mm:ss"));
        //vo.setOrderEndDate(DateUtils.parseDate(vo.getEndDate() + " 23:59:59", "yyyy-MM-dd HH:mm:ss"));

        if(vo.getCateId1() != null || vo.getCateId2() != null || vo.getCateId3() != null
                || StringUtils.isNotBlank(vo.getSearchWord())
                || StringUtils.isNotBlank(vo.getBarCode())){
            CommodityVO commodityVO = new CommodityVO();
            BeanUtils.copyProperties(vo,commodityVO);
            commodityVO.setCommodityKey(vo.getSearchWord());
            List<CommodityBasicEntry> commList = commodityMapper.findCommodityBasicListByParam(commodityVO);
            if(CollectionUtils.isNotEmpty(commList)){
                List<Long> commodityIdList = commList.stream().map(item -> Long.valueOf(item.getCommodityId())).collect(Collectors.toList());
                vo.setCommodityIdList(commodityIdList);
            }else {
                return  pageDate;
            }
        }

        pageDate = PageHelper.startPage(vo.getPageNo(), vo.getPageSize()).doSelectPageInfo(() -> {
            orderReportMapper.actualReceiptAnalysisReport(vo);
        });
        setActualReceptList(pageDate);

        return pageDate;
    }

    /**
     * 门店实收商品分析表
     * @param pageDate
     */
    private void setActualReceptList(PageInfo<ActualReceiptAnalysisODto> pageDate) {
        if(CollectionUtils.isNotEmpty(pageDate.getList())){
            List<Long> commodityIdList = pageDate.getList().stream().map(item -> item.getCommodityId()).distinct().collect(Collectors.toList());
            Map<Long,String> barCodeMap = commonService.getCommodityBarCodeMap(commodityIdList,null);
            Map<Long,BigDecimal> priceMap = commodityPriceClient.getCommoditySupplyPrice();
            Map<Long, CommoditySupplierODto> commodityIdAndSupplierODTOMap = commoditySupplierClient.queryCommodityDefaultSupplier(commodityIdList);

            CommodityVO commodityVO = new CommodityVO();
            commodityVO.setCommodityIdList(commodityIdList);
            List<CommodityBasicEntry> commList = commodityMapper.findCommodityBasicListByParam(commodityVO);
            Map<String, CommodityBasicEntry> commMap = commList.stream().collect(Collectors.toMap(CommodityBasicEntry::getCommodityId, Function.identity()));

            List<Long> cateIdList = new ArrayList<>();
            cateIdList.addAll(commList.stream().map(item -> Long.valueOf(item.getCommodityFirstId())).distinct().collect(Collectors.toList()));
            Map<String, String> cateMap = commonService.getCategroyMap(cateIdList);

            List<Long> storeIdList = pageDate.getList().stream().distinct().map(item -> item.getStoreId()).collect(Collectors.toList());
            List<Shop> shopList = shopService.getShopByStoreIdList(storeIdList);
            Map<Long, String> shopMap = shopList.stream().collect(Collectors.toMap(Shop::getStoreId,Shop::getShopName,(key1 , key2)-> key2));

            pageDate.getList().forEach(a -> {
                CommodityBasicEntry commodityBasic = commMap.get(a.getCommodityId() + "");
                if(commodityBasic != null){
                    a.setCommodityCode(commodityBasic.getCommodityCode());
                    a.setCommodityName(commodityBasic.getCommodityName());
                    a.setCommoditySpec(commodityBasic.getCommoditySpec());
                    a.setCateName(cateMap.get(commodityBasic.getCommodityFirstId() + ""));
                }
                a.setShopName(shopMap.get(a.getStoreId()));

                if(null != priceMap){
                    a.setSupplyPrice(priceMap.get(a.getCommodityId()));
                }
                BigDecimal supplyPrice = a.getSupplyPrice();
                BigDecimal totalRealReceiveQuantity = a.getTotalRealReceiveQuantity();
                if(null != supplyPrice) {
                    a.setTotalSupplyPrice(supplyPrice.multiply(totalRealReceiveQuantity).setScale(2, BigDecimal.ROUND_HALF_UP));
                }
                BigDecimal totalRealDeliveryQuantity = a.getTotalRealDeliveryQuantity();
                a.setQuantityDifference(totalRealDeliveryQuantity.subtract(totalRealReceiveQuantity));

                CommoditySupplierODto commoditySupplierODto = commodityIdAndSupplierODTOMap.get(a.getCommodityId());
                if(commoditySupplierODto != null){
                    a.setSupplierName(commoditySupplierODto.getSupplierName());
                    a.setRealName(commoditySupplierODto.getRealName());
                }

                String barCodes = barCodeMap.get(Long.valueOf(a.getCommodityId()));
                a.setBarCodes(barCodes);
                a.setBarCode(barCodes!=null?barCodes.split(",")[0]:"");
            });
        }
    }


}
