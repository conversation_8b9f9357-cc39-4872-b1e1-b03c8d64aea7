package com.pinshang.qingyun.order.service.xda.v4;

import com.pinshang.qingyun.base.api.ApiErrorCodeEnum;
import com.pinshang.qingyun.base.configure.expand.BizLogicException;
import com.pinshang.qingyun.base.configure.expand.QYAssert;
import com.pinshang.qingyun.base.constant.XdaAppVersionConstant;
import com.pinshang.qingyun.base.enums.TerminalSourceTypeEnum;
import com.pinshang.qingyun.base.enums.YesOrNoEnums;
import com.pinshang.qingyun.base.enums.xda.XdaStoreTypeEnum;
import com.pinshang.qingyun.box.utils.BeanCloneUtils;
import com.pinshang.qingyun.box.utils.DateUtil;
import com.pinshang.qingyun.box.utils.SpringUtil;
import com.pinshang.qingyun.order.dto.shopcart.v4.ShoppingCartCommodityV4ODTO;
import com.pinshang.qingyun.order.dto.shopcart.v4.ShoppingCartGroupV4ODTO;
import com.pinshang.qingyun.order.dto.shopcart.v4.ShoppingCartV4ODTO;
import com.pinshang.qingyun.order.dto.xda.v4.CreOrderItemV4DTO;
import com.pinshang.qingyun.order.dto.xda.v4.TdaDeliveryTimeRangeODTO;
import com.pinshang.qingyun.order.dto.xda.v4.XdaCreatePrePayOrderV4IDTO;
import com.pinshang.qingyun.order.enums.CombTypeEnum;
import com.pinshang.qingyun.order.enums.ProductTypeEnums;
import com.pinshang.qingyun.order.mapper.CommodityMapper;
import com.pinshang.qingyun.order.mapper.OrderMapper;
import com.pinshang.qingyun.order.mapper.StoreMapper;
import com.pinshang.qingyun.order.mapper.entry.commodity.CommodityItemEntry;
import com.pinshang.qingyun.order.mapper.entry.commodity.CommodityItemEntryList;
import com.pinshang.qingyun.order.mapper.entry.order.CommodityInfoEntry;
import com.pinshang.qingyun.order.model.order.Order;
import com.pinshang.qingyun.order.service.freight.FreightCalculationService;
import com.pinshang.qingyun.order.service.StoreService;
import com.pinshang.qingyun.order.service.freight.FreightCalculationService;
import com.pinshang.qingyun.order.service.xda.util.XdaShoppingCartToolUtil;
import com.pinshang.qingyun.order.util.EmojiFilter;
import com.pinshang.qingyun.order.util.OrderTimeUtil;
import com.pinshang.qingyun.price.dto.commodity.CommodityListRequestIDTO;
import com.pinshang.qingyun.price.dto.commodity.CommodityResultODTO;
import com.pinshang.qingyun.price.service.ProductPriceModelClient;
import com.pinshang.qingyun.storage.dto.CommodityDefaultDcODto;
import com.pinshang.qingyun.storage.service.CommodityWarehouseClient;
import com.pinshang.qingyun.store.dto.storeSettlement.PaymentParentChildStoreSettlementODTO;
import com.pinshang.qingyun.store.dto.xda.QueryXdaUserAccountDTO;
import com.pinshang.qingyun.store.dto.xda.XdaPayPasswordDTO;
import com.pinshang.qingyun.store.dto.xda.XdaPayResultDTO;
import com.pinshang.qingyun.store.dto.xda.XdaUserAccountDTO;
import com.pinshang.qingyun.store.service.StoreCompanyClient;
import com.pinshang.qingyun.store.service.XdaStoreUserClient;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.function.Function;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * @Author: sk
 * @Date: 2024/12/18
 */
@Slf4j
@Service
public class XdaSaveOrderCheckService {

    @Autowired
    private StoreCompanyClient storeCompanyClient;
    @Lazy
    @Autowired
    private XdaOrderV4Service  xdaOrderV4Service;
    @Lazy
    @Autowired
    private XdaShoppingCartToolUtil xdaShoppingCartToolUtil;
    @Lazy
    @Autowired
    private TdaOrderService tdaOrderService;
    @Autowired
    private XdaStoreUserClient xdaStoreUserClient;
    @Autowired
    private StoreMapper storeMapper;
    @Autowired
    private OrderMapper orderMapper;
    @Autowired
    private XdaFrequentPurchaseV4Service xdaFrequentPurchaseV4Service;
    @Autowired
    private ToBService toBService;
    @Autowired
    private ProductPriceModelClient productPriceModelClient;
    @Autowired
    private CommoditySaleDayStatisticsV4Service commoditySaleDayStatisticsV4Service;
    @Autowired
    private CommodityMapper commodityMapper;
    @Autowired
    private CommodityWarehouseClient commodityWarehouseClient;
    @Autowired
    private FreightCalculationService freightCalculationService;
    @Autowired
    private StoreService storeService;

    /**
     * 鲜达预订单、非预订单保存订单统一校验
     * 返回 要保存的商品详细信息
     */
    public Order xdaCreateOrderCheckAndSaveOrder(XdaCreatePrePayOrderV4IDTO xdaCreatePrePayOrderV4IDTO,
                                                             Boolean isPreOrder, Boolean isSaveOrder,
                                                             ShoppingCartV4ODTO shoppingCartV4ODTO){
        Order order = new Order();
        Long storeId = xdaCreatePrePayOrderV4IDTO.getStoreId();
        Date orderTime = xdaCreatePrePayOrderV4IDTO.getOrderTime();

        // 0.校验鲜达订单备注是否有特殊表情符号
        checkOrderRemarkSpecialSymbols(xdaCreatePrePayOrderV4IDTO.getRemark());

        // 1.通达校验截单时间
        TdaDeliveryTimeRangeODTO tdaDeliveryTimeRangeODTO = tdaOrderService.checkDeliveryTimeRange(xdaCreatePrePayOrderV4IDTO.getStoreId(),
                xdaCreatePrePayOrderV4IDTO.getDeliverytimerange(), xdaCreatePrePayOrderV4IDTO.getDeliverybatch(), true);

        // 是否通达客户
        Boolean isTdaStore = tdaOrderService.isTdaStore(xdaCreatePrePayOrderV4IDTO.getStoreId());

        // 2.校验客户是否停用
        Integer storeStatus = storeCompanyClient.selectStoreCompanyByStoreId(storeId);
        if(null == storeStatus || storeStatus != 1){
            QYAssert.isTrue(false, "客户已停用!");
        }

        // 3.购物车空判断和是否可结算
        if(null == shoppingCartV4ODTO || !shoppingCartV4ODTO.getCanSettlement()){
            order.setErrorMsg(ApiErrorCodeEnum.XDA_PRE_ORDER_V4_WARN.getRemark());
            return order;
        }

        //4.校验旧版本APP（小于V1.6.2） 超出总限量或客户限购的情况
        if (StringUtils.isNotBlank(xdaCreatePrePayOrderV4IDTO.getAppVersion())
                && xdaCreatePrePayOrderV4IDTO.getAppVersion().compareToIgnoreCase(XdaAppVersionConstant.TDA_VERSION_162) < 0) {
            shoppingCartV4ODTO.getNormalGroup().getCommodities()
                    .stream()
                    .filter(x -> Objects.equals(x.getIsExceedLimit(), Boolean.TRUE))
                    .findAny()
                    .ifPresent(y -> QYAssert.isTrue(false, "超出特价商品限值，请下载最新版本APP后再继续下单"));
        }

        // 计算配送费
        BigDecimal freightAmount = calculateFreightAmount(storeId, orderTime, shoppingCartV4ODTO);

        // 5.校验数据是否合法
        String errorMsg = bizValidateAndMakeup(shoppingCartV4ODTO, xdaCreatePrePayOrderV4IDTO, orderTime, freightAmount, isPreOrder);
        if(StringUtils.isNotBlank(errorMsg)){
            order.setErrorMsg(errorMsg);
            return order;
        }

        Date maxBeginDeliveryTime = new Date();
        // 6.拆单 按配送模式+仓库+供应商
        List<CreOrderItemV4DTO> creOrderItemDTOList = assembleProductData(shoppingCartV4ODTO, storeId, orderTime);

        // 7.获取商品最大 最早可订货时间
        maxBeginDeliveryTime = getMaxBeginDeliveryTime(shoppingCartV4ODTO, maxBeginDeliveryTime);

        // 8.计算订单截至时间
        Date orderDurationTime = xdaShoppingCartToolUtil.calculateOrderDeadline(orderTime, maxBeginDeliveryTime, isTdaStore ? tdaDeliveryTimeRangeODTO.getStoreEndTime() : xdaCreatePrePayOrderV4IDTO.getStoreEndTime());

        // 确保预订单id和正式t_order的id和order_code都一样
        // 9.保存订单
        Order freezeOrder = new Order();
        order = xdaOrderV4Service.saveOrder(xdaCreatePrePayOrderV4IDTO, creOrderItemDTOList, shoppingCartV4ODTO.getSummation(), freightAmount, orderDurationTime, freezeOrder, tdaDeliveryTimeRangeODTO, isTdaStore, isSaveOrder);

        order.setId(freezeOrder.getId());
        order.setOrderCode(freezeOrder.getOrderCode());

        return order;
    }

    private void checkOrderRemarkSpecialSymbols(String remark) {
        if(StringUtils.isNotBlank(remark) && EmojiFilter.containsEmoji(remark)){
            QYAssert.isTrue(false, "订单备注含有特殊符号，无法保存");
        }
    }


    /**
     * 获取最大 起始送货时间
     * @param shopCart
     * @param maxBeginDeliveryTime
     * @return
     */
    public Date getMaxBeginDeliveryTime(ShoppingCartV4ODTO shopCart,Date maxBeginDeliveryTime){
        List<ShoppingCartCommodityV4ODTO> normalCommList = shopCart.getNormalGroup().getCommodities();
        List<ShoppingCartCommodityV4ODTO> thCommList = shopCart.getThGroups().getCommodities();
        if(SpringUtil.isNotEmpty(thCommList)){
            normalCommList.addAll(thCommList);
        }
        shopCart.getPromotionGroup().forEach(item->{
            List<ShoppingCartCommodityV4ODTO> commodities = item.getCommodities();
            normalCommList.addAll(commodities);

        });
        for (ShoppingCartCommodityV4ODTO odto : normalCommList){
            // 获取最大 起始送货时间
            boolean timeChange = maxBeginDeliveryTime.compareTo(odto.getBeginDeliveryTime()) < 0;
            maxBeginDeliveryTime = timeChange ? odto.getBeginDeliveryTime() : maxBeginDeliveryTime;
        }
        return maxBeginDeliveryTime;
    }

    /**
     * 1、校验是否可结算
     * 2、校验订货范围
     * 3、校验支付相关
     *
     * @param shoppingCartV4ODTO         购物车数据
     * @param xdaCreatePrePayOrderV4IDTO 订单请求条件
     * @param orderTime                  送货日期
     * @param freightAmount
     */
    public String bizValidateAndMakeup(ShoppingCartV4ODTO shoppingCartV4ODTO, XdaCreatePrePayOrderV4IDTO xdaCreatePrePayOrderV4IDTO,
                                       Date orderTime, BigDecimal freightAmount, Boolean isPreOrder){
        // 1.检查是否复合结算条件
        if(!shoppingCartV4ODTO.getCanSettlement()){
            //xdaSaveOrderODTO.setErrorMsg(ApiErrorCodeEnum.XDA_PRE_ORDER_V4_WARN.getRemark());
            return ApiErrorCodeEnum.XDA_PRE_ORDER_V4_WARN.getRemark();
        }
        Long storeId = xdaCreatePrePayOrderV4IDTO.getStoreId();

        //2.检查客户是否在订货时间内
        xdaCreatePrePayOrderV4IDTO.setStoreEndTime(xdaShoppingCartToolUtil.getStoreDurationEndTime(storeId));
        BigDecimal summation = shoppingCartV4ODTO.getSummation().setScale(2, BigDecimal.ROUND_HALF_UP);

        //实付金额=订单总金额+运费
        BigDecimal actualPaid = summation.add(freightAmount);
        BigDecimal orderAmount = xdaCreatePrePayOrderV4IDTO.getOrderAmount();

        // 3.校验商品金额，品种数量以及商品数量
        if(null == shoppingCartV4ODTO.getVarietySum() || !shoppingCartV4ODTO.getVarietySum().equals(xdaCreatePrePayOrderV4IDTO.getVarietySum()) || actualPaid.compareTo(orderAmount) != 0
                || shoppingCartV4ODTO.getCommodityNum().compareTo(xdaCreatePrePayOrderV4IDTO.getCommodityNum())!=0){
            // xdaSaveOrderODTO.setErrorMsg(ApiErrorCodeEnum.XDA_PRE_ORDER_V4_WARN.getRemark());
            return ApiErrorCodeEnum.XDA_PRE_ORDER_V4_WARN.getRemark();
        }

        //XdaUserAccountDTO xdaUserAccountDTO = userAccountInfo(storeId, xdaCreatePrePayOrderV4IDTO.getPayPassword());
        // 查询鲜达账户信息和客户余额
        PaymentParentChildStoreSettlementODTO xdaUserAccountDTO = storeService.queryStoreCollectPriceByStoreId(storeId, true);

        // 4.判断订单金额
        if(isPreOrder){
            // 预付单基本上都会支付一定的金额，账户有钱就扣钱。没有全部预订单扣
            Boolean amountFlag = (summation.subtract(xdaCreatePrePayOrderV4IDTO.getPayAmount())).compareTo(xdaUserAccountDTO.getCollectPrice()) <= 0;
            QYAssert.isTrue(amountFlag, "账户金额不足，无法下单");
        }else {
            // 支付密码   支付回调创建订单不需要验证支付密码
            if(xdaCreatePrePayOrderV4IDTO.getIsPayPassword()){
                // 客户账户类型
                QYAssert.isTrue(xdaUserAccountDTO.getHasPayPassword(), "请设置支付密码");
                QYAssert.hasLength(xdaCreatePrePayOrderV4IDTO.getPayPassword(), "密码为空！");
                xdaPay(xdaCreatePrePayOrderV4IDTO.getPayPassword(), xdaCreatePrePayOrderV4IDTO.getUserId(), xdaCreatePrePayOrderV4IDTO.getTypeEnum());
            }
            Boolean amountFlag = summation.compareTo(xdaUserAccountDTO.getCollectPrice()) <= 0;
            QYAssert.isTrue(amountFlag, "账户金额不足，无法下单");
        }

        xdaCreatePrePayOrderV4IDTO.setStoreType(XdaStoreTypeEnum.PRE_PAY.getCode() == xdaUserAccountDTO.getCollectStatus() ? XdaStoreTypeEnum.PRE_PAY : XdaStoreTypeEnum.POST_PAY);
        // 5.计算下单次数
        Integer storeLimit = storeMapper.findOrderNumberLimit(storeId);
        if(storeLimit != null){
            List<Order> orderList = orderMapper.queryOrderByOrderTime(storeId, orderTime);
            QYAssert.isTrue(storeLimit > orderList.size(), "每天限制"+storeLimit+"单，建议取消1单。");
        }
        return null;
    }


    /**
     * 组装商品信息
     * 商品分组按照物流模式+仓库id+采购商id
     * 保存商品常购清单
     * @param shoppingCartV4ODTO
     * @param storeId
     * @param orderTime
     * @return
     */
    public List<CreOrderItemV4DTO> assembleProductData(ShoppingCartV4ODTO shoppingCartV4ODTO, Long storeId, Date orderTime){
        List<CreOrderItemV4DTO> orderCommodityList = new ArrayList<>();
        /* 普通商品分组处理 */
        List<ShoppingCartCommodityV4ODTO> normalGroupCommodityList;
        if(SpringUtil.isNotEmpty(normalGroupCommodityList = shoppingCartV4ODTO.getNormalGroup().getCommodities())){
            normalGroupCommodityList.forEach(item->{
                if(YesOrNoEnums.YES.getCode().equals(item.getIsSpecialPrice())){
                    // 同一个商品特价和普通价格拆行
                    if (item.getSpecialQuantity() != null && item.getSpecialQuantity().compareTo(BigDecimal.ZERO) > 0){
                        CreOrderItemV4DTO itemDTO = BeanCloneUtils.copyTo(item, CreOrderItemV4DTO.class);
                        itemDTO.setType(ProductTypeEnums.PRODUCT);
                        itemDTO.setPricePromotionId(item.getPricePromotionId());
                        itemDTO.setQuantity(item.getSpecialQuantity());
                        itemDTO.setCouponDiscountAmount(item.getSpecialCouponDiscountAmount());
                        itemDTO.setCouponId(item.getCouponId());
                        itemDTO.setCouponUserId(item.getCouponUserId());
                        orderCommodityList.add(itemDTO);

                    }

                    if(item.getNormalQuantity() != null && item.getNormalQuantity().compareTo(BigDecimal.ZERO) > 0){
                        CreOrderItemV4DTO itemDTO = BeanCloneUtils.copyTo(item, CreOrderItemV4DTO.class);
                        itemDTO.setType(ProductTypeEnums.PRODUCT);
                        itemDTO.setQuantity(item.getNormalQuantity());
                        itemDTO.setIsSpecialPrice(YesOrNoEnums.NO.getCode());
                        itemDTO.setSpecialPrice(null);
                        itemDTO.setCouponDiscountAmount(item.getCouponDiscountAmount());
                        itemDTO.setCouponId(item.getCouponId());
                        itemDTO.setCouponUserId(item.getCouponUserId());
                        orderCommodityList.add(itemDTO);

                    }

                }else {
                    CreOrderItemV4DTO itemDTO = BeanCloneUtils.copyTo(item, CreOrderItemV4DTO.class);
                    itemDTO.setType(ProductTypeEnums.PRODUCT);
                    itemDTO.setCouponDiscountAmount(item.getCouponDiscountAmount());
                    itemDTO.setCouponId(item.getCouponId());
                    itemDTO.setCouponUserId(item.getCouponUserId());
                    orderCommodityList.add(itemDTO);
                }

            });
        }

        /*促销商品分组处理*/
        List<ShoppingCartGroupV4ODTO> shoppingCartGroupV3ODTOList;
        if(SpringUtil.isNotEmpty(shoppingCartGroupV3ODTOList = shoppingCartV4ODTO.getPromotionGroup())){
            shoppingCartGroupV3ODTOList.forEach(item->{
                /* 普通商品分组*/
                item.getCommodities().forEach(normalCommodityGroup->{
                    CreOrderItemV4DTO itemDTO = BeanCloneUtils.copyTo(normalCommodityGroup, CreOrderItemV4DTO.class);
                    itemDTO.setType(ProductTypeEnums.PRODUCT);
                    if(null != item.getFullStatus() && item.getFullStatus()==1){
                        itemDTO.setConditionId(Long.valueOf(item.getPromotionId()));
                    }
                    itemDTO.setCouponDiscountAmount(normalCommodityGroup.getCouponDiscountAmount());
                    itemDTO.setCouponId(normalCommodityGroup.getCouponId());
                    itemDTO.setCouponUserId(normalCommodityGroup.getCouponUserId());
                    if (normalCommodityGroup.getIsGift()) {
                        itemDTO.setType(ProductTypeEnums.GIFT);
                        itemDTO.setCouponDiscountAmount(null);
                        itemDTO.setCouponId(null);
                        itemDTO.setCouponUserId(null);
                    }
                    if(normalCommodityGroup.getCommodityUnitName().trim().equals("kg")){
                        itemDTO.setIsWeight(1);
                    }
                    orderCommodityList.add(itemDTO);
                });
            });
        }

        /* 特惠商品分组处理 */
        List<ShoppingCartCommodityV4ODTO> thGroupsCommodityList;
        if(SpringUtil.isNotEmpty(thGroupsCommodityList = shoppingCartV4ODTO.getThGroups().getCommodities())){
            thGroupsCommodityList.forEach(item->{
                CreOrderItemV4DTO itemDTO = BeanCloneUtils.copyTo(item, CreOrderItemV4DTO.class);
                itemDTO.setType(ProductTypeEnums.TH);
                Boolean isThInvalidate = !item.getIsInvalidate();
                itemDTO.setIsThInvalidate(isThInvalidate);
                orderCommodityList.add(itemDTO);
            });
        }

        // 保存客户常购商品  --经确认不需要保存特惠商品
        List<CreOrderItemV4DTO> normalCommodityList = orderCommodityList.stream().filter(item -> item.getType().equals(ProductTypeEnums.PRODUCT)).collect(Collectors.toList());
        if(CollectionUtils.isNotEmpty(normalCommodityList)){
            List<CreOrderItemV4DTO> saveSaleDayStatisticsList = BeanCloneUtils.copyTo(normalCommodityList, CreOrderItemV4DTO.class);
            xdaFrequentPurchaseV4Service.saveSaleDayStatistics(storeId, saveSaleDayStatisticsList);
        }


        List<CreOrderItemV4DTO> toBCommodityList = orderCommodityList.stream().filter(item -> item.getType().equals(ProductTypeEnums.PRODUCT)
                || item.getType().equals(ProductTypeEnums.TH)).collect(Collectors.toList());
        // toB订单销量日统计 -- 保存普通商品组、特惠商品组、特价商品组。 赠品不需要保存
        commoditySaleDayStatisticsV4Service.saveSaleDayStatistics(orderTime, toBCommodityList);

        List<Long> commodityIds = orderCommodityList.stream().map(CreOrderItemV4DTO :: getCommodityId).collect(Collectors.toList());
        List<CommodityInfoEntry> commodityInfoEntryList = commodityMapper.findCommodityInfoIds(commodityIds);
        Map<Long,CommodityInfoEntry> commodityInfoEntryMap = commodityInfoEntryList.stream().collect(Collectors.toMap(CommodityInfoEntry::getId, Function.identity()));
        Map<Long, CommodityDefaultDcODto> dcODtoMap = commodityWarehouseClient.queryCommodityDefaultWarehouseAndSupplier(commodityIds);

        for(CreOrderItemV4DTO dto : orderCommodityList){
            CommodityInfoEntry entry = commodityInfoEntryMap.get(dto.getCommodityId());
            dto.setLogisticsModel(entry.getLogisticsModel());
            dto.setCommodityPackageSpec(entry.getCommodityPackageSpec());
            dto.setIsWeight(entry.getIsWeight());
            // 获取商品 仓库id和供应商id
            CommodityDefaultDcODto oDto = dcODtoMap.get(dto.getCommodityId());
            QYAssert.notNull(oDto, "商品没有默认供应商或默认仓库");

            dto.setWarehouseId(oDto.getWarehouseId());
            dto.setSupplierId(oDto.getSupplierId());
        }
        //找到订单中的组合商品，拆分成子商品订单
        this.splitCombCommodityOrder(orderCommodityList,storeId);
        return orderCommodityList;
    }


    /**
     * 客户类型
     * @param storeId
     * @param payPassword
     * @return
     */
    public XdaUserAccountDTO userAccountInfo(Long storeId,String payPassword){
        // 客户账户类型
        QueryXdaUserAccountDTO queryXdaUserAccountDTO = new QueryXdaUserAccountDTO();
        queryXdaUserAccountDTO.setStoreId(storeId);
        XdaUserAccountDTO xdaUserAccountDTO = xdaStoreUserClient.queryUserAccountInfo(queryXdaUserAccountDTO);
        QYAssert.notNull(xdaUserAccountDTO, "客户账户不存在");

        return xdaUserAccountDTO;
    }

    /**
     * 支付密码校验
     * @param payPassword
     * @param userId
     * @param typeEnum
     */
    public void xdaPay(String payPassword, Long userId, TerminalSourceTypeEnum typeEnum){
        XdaPayPasswordDTO xdaPayPasswordDTO = new XdaPayPasswordDTO();
        xdaPayPasswordDTO.setPayPassword(payPassword);
        xdaPayPasswordDTO.setUserId(userId);
        xdaPayPasswordDTO.setTerminalSourceTypeEnum(typeEnum);
        XdaPayResultDTO payResultODTO = xdaStoreUserClient.xdaPay(xdaPayPasswordDTO);
        if(!payResultODTO.getPaySuccess()){
            throw new BizLogicException("密码错误。", ApiErrorCodeEnum.ACCOUNT_OR_PASSWORD_FAILURE);
        }
    }

    /**
     * 找到订单中的组合商品，拆分成子商品订单
     */
    private void splitCombCommodityOrder(List<CreOrderItemV4DTO> orderCommodityList, Long storeId) {
        List<Long> combCommodityIds = orderCommodityList.stream().filter(x -> Objects.equals(x.getProductType(), 2)).map(CreOrderItemV4DTO::getCommodityId).collect(Collectors.toList());
        List<CommodityItemEntryList> commodityItemList = toBService.getCommodityItemListByCommodityIdList(combCommodityIds);
        Map<Long, List<CommodityItemEntry>> map = commodityItemList.stream().collect(Collectors.toMap(CommodityItemEntryList::getCommodityId, CommodityItemEntryList::getCommodityItemEntry));

        //根据子商品列表查询产品价格方案中的原价
        List<CommodityResultODTO> commodityPriceList = queryCommodityPrice(storeId, commodityItemList);
        Map<String, CommodityResultODTO> priceMap = commodityPriceList.stream().collect(Collectors.toMap(CommodityResultODTO::getCommodityId, Function.identity()));
        List<CreOrderItemV4DTO> combChildItemList = new ArrayList<>();
        for (CreOrderItemV4DTO itemV4DTO : orderCommodityList) {
            BigDecimal quantity = itemV4DTO.getQuantity();
            List<CommodityItemEntry> commodityItemEntries = map.get(itemV4DTO.getCommodityId());
            if (CollectionUtils.isNotEmpty(commodityItemEntries)) {
                //有子商品，标记为组合商品
                itemV4DTO.setCombType(CombTypeEnum.COMB.getCode());
                BigDecimal totalPricesOfAllItems = commodityItemEntries.stream()
                        .map(entry -> entry.getCommodityNum().multiply(priceMap.get(entry.getCommodityItemId()).getCommodityPrice()))
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
                for (CommodityItemEntry commodityItemEntry : commodityItemEntries) {
                    CreOrderItemV4DTO itemDTO = BeanCloneUtils.copyTo(itemV4DTO, CreOrderItemV4DTO.class);
                    itemDTO.setQuantity(quantity.multiply(commodityItemEntry.getCommodityNum()));
                    itemDTO.setCommodityId(Long.valueOf(commodityItemEntry.getCommodityItemId()));
                    //标记为子商品
                    itemDTO.setCombType(CombTypeEnum.COMB_CHILD.getCode());
                    itemDTO.setCombCommodityId(itemV4DTO.getCommodityId());
                    // 设置其他商品信息
                    itemDTO.setSpecialPrice(null);
                    itemDTO.setThPrice(null);
                    itemDTO.setCouponUserId(null);
                    itemDTO.setCouponId(null);
                    itemDTO.setCouponDiscountAmount(null);
                    CommodityResultODTO commodityResultODTO = priceMap.get(commodityItemEntry.getCommodityItemId());
                    QYAssert.isTrue(Objects.nonNull(commodityResultODTO), "商品信息有误!");
                    //原价
                    itemDTO.setCommodityPrice(commodityResultODTO.getCommodityPrice());
                    itemDTO.setThPrice(commodityResultODTO.getCommodityPrice());
                    itemDTO.setSpecialPrice(commodityResultODTO.getCommodityPrice());
                    //金额占比
                    BigDecimal totalPrice = commodityResultODTO.getCommodityPrice().multiply(commodityItemEntry.getCommodityNum());
                    BigDecimal proportion = totalPrice.divide(totalPricesOfAllItems, 6, RoundingMode.HALF_UP);
                    itemDTO.setProportion(proportion);
                    combChildItemList.add(itemDTO);
                }
            }
        }
        orderCommodityList.addAll(combChildItemList);
    }

    /**
     * 根据子商品列表查询产品价格方案中的原价
     */
    private List<CommodityResultODTO> queryCommodityPrice(Long storeId, List<CommodityItemEntryList> commodityItemList) {
        if (CollectionUtils.isEmpty(commodityItemList)) {
            return Collections.emptyList();
        }
        //有子商品列表
        List<Long> commodityItemIdList = commodityItemList.stream()
                .flatMap(x -> x.getCommodityItemEntry().stream())
                .map(CommodityItemEntry::getCommodityItemId)
                .distinct()
                .map(Long::parseLong)
                .collect(Collectors.toList());
        //查询子商品价格
        CommodityListRequestIDTO idto = new CommodityListRequestIDTO();
        idto.setStoreId(String.valueOf(storeId));
        idto.setCommodityIdListAll(commodityItemIdList);
        return productPriceModelClient.findStoreCommodityList(idto);
    }

    /**
     * 计算配送费
     *
     * @param storeId   客户ID
     * @param orderTime 下单时间
     * @param shopCart  购物车
     * @return 配送费
     */
    public BigDecimal calculateFreightAmount(Long storeId, Date orderTime, ShoppingCartV4ODTO shopCart) {
        return freightCalculationService.calculateFreight(storeId, orderTime, shopCart).getAmount();
    }
}
