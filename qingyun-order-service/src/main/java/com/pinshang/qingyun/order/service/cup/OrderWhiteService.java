package com.pinshang.qingyun.order.service.cup;

import com.pinshang.qingyun.order.mapper.cup.ConsumableLimitMapper;
import com.pinshang.qingyun.order.mapper.cup.OrderWhiteMapper;
import com.pinshang.qingyun.order.model.cup.ConsumableLimit;
import com.pinshang.qingyun.order.model.cup.OrderWhite;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @Author: sk
 * @Date: 2024/7/18
 */
@Slf4j
@Service
public class OrderWhiteService {

    @Autowired
    private OrderWhiteMapper orderWhiteMapper;

    public List<OrderWhite> findListByStoreId(Long storeId){
        Example example = new Example(OrderWhite.class);
        example.createCriteria().andEqualTo("storeId",storeId);
        return orderWhiteMapper.selectByExample(example);
    }



}
