package com.pinshang.qingyun.order.controller;

import com.github.pagehelper.PageInfo;
import com.pinshang.qingyun.base.annotations.RepeatSubmitAnno;
import com.pinshang.qingyun.base.api.TokenInfo;
import com.pinshang.qingyun.base.configure.expand.QYAssert;
import com.pinshang.qingyun.base.constant.QingyunConstant;
import com.pinshang.qingyun.base.constant.ShopStockTypeConstant;
import com.pinshang.qingyun.base.enums.IogisticsModelEnums;
import com.pinshang.qingyun.base.enums.RepeatSubmitBusinessCode;
import com.pinshang.qingyun.base.util.FastThreadLocalUtil;
import com.pinshang.qingyun.common.dto.DictionaryODTO;
import com.pinshang.qingyun.common.service.DictionaryClient;
import com.pinshang.qingyun.order.dto.ReceiveOrderMessageIDTO;
import com.pinshang.qingyun.order.dto.ReceiveOrderMessageODTO;
import com.pinshang.qingyun.order.enums.MessageTypeEnums;
import com.pinshang.qingyun.order.mapper.entry.shop.*;
import com.pinshang.qingyun.order.service.ShopAutoReceiveService;
import com.pinshang.qingyun.order.service.ShopReceiveService;
import com.pinshang.qingyun.order.service.ok4distribution.DistributionOkProcessor;
import com.pinshang.qingyun.order.vo.ShopReceiveHQVo;
import com.pinshang.qingyun.order.vo.order.ShopReceiveOrderVo;
import com.pinshang.qingyun.order.vo.shop.*;
import com.pinshang.qingyun.supplier.dto.SupplierODTO;
import com.pinshang.qingyun.supplier.service.SupplierClient;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * Created by zhaoranguang on 2017/6/7.
 */
@RestController
@RequestMapping("/shopReceive")
@Api(description = "鲜道收货管理")
public class ShopReceiveController {


    @Autowired
    ShopReceiveService shopReceiveService;
    
    @Resource
    private RedissonClient redissonClient;

    @Autowired
    private SupplierClient supplierClient;

    @Autowired
    private DistributionOkProcessor processor;
    @Autowired
    private ShopAutoReceiveService shopAutoReceiveService;
    @Autowired
    private DictionaryClient dictionaryClient;

    @ApiOperation(value = "收货列表(鲜道前置仓)", notes = "收货列表(鲜道前置仓)", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @RequestMapping(value = "/list", method = RequestMethod.POST)
    public PageInfo <ShopReceiveEntry> list(@RequestBody ShopReceiveVo shopReceiveVo) {
        TokenInfo tokenInfo = FastThreadLocalUtil.getQY();
        if(null != tokenInfo){
            Long enterpriseId = tokenInfo.getEnterpriseId();
            Long storeId = tokenInfo.getStoreId();
            shopReceiveVo.setStoreId(String.valueOf(storeId));
            shopReceiveVo.setEnterpriseId(enterpriseId.toString());
            shopReceiveVo.setIsInternal(tokenInfo.getIsInternal());
            shopReceiveVo.setConsignmentId(tokenInfo.getConsignmentId());
        }
    	PageInfo <ShopReceiveEntry> pageInfo = shopReceiveService.getListByCondition(shopReceiveVo);
        DictionaryODTO dictionaryODTO = dictionaryClient.getDictionaryByCode("supplierCode");
        SupplierODTO supplier = supplierClient.getSupplierByCode(dictionaryODTO.getOptionValue());

    	List<ShopReceiveEntry> list =pageInfo.getList();
    	if(null !=list && !list.isEmpty()){
    		list.forEach( i ->{
    			if(i.getLogisticsModel().intValue()== IogisticsModelEnums.DIRECT_CONNECTION.getCode() || i.getLogisticsModel().intValue()== IogisticsModelEnums.DISPATCHING.getCode()){
					i.setSupplierName(supplier != null ? supplier.getSupplierName() : "");
				}
    		});
    	}
        return pageInfo;
    }


    @RequestMapping(value = "/showReceive", method = RequestMethod.POST)
    public ShopReceiveOrderInfoEntry showReceive(@RequestBody QuerySubOrderVo querySubOrderVo) throws Throwable {
        return  shopReceiveService.showReceive(querySubOrderVo);
    }

    @RequestMapping(value = "/autoReceive",method = RequestMethod.POST)
    @ApiOperation(value="鲜食自动收货")
    public Boolean autoReceive(@RequestParam(value = "orderTime",required = false) String orderTime)  {
        shopAutoReceiveService.autoReceive(orderTime);
        return Boolean.TRUE;
    }


    @RequestMapping(value = "/autoReceiveXsJm",method = RequestMethod.POST)
    @ApiOperation(value="鲜食加盟自动收货(钱大妈)")
    public Boolean autoReceiveXsJm(@RequestParam(value = "orderTime",required = false) String orderTime)  {
        shopAutoReceiveService.autoReceiveXsJm(orderTime);
        return Boolean.TRUE;
    }

    @RequestMapping(value = "/pushQueue",method = RequestMethod.POST)
    @ApiOperation(value="自动收货放入队列")
    public Boolean pushQueue(@RequestBody List<Long> subOrderIds)  {
        shopAutoReceiveService.pushSubOrderIdToQueue(subOrderIds);
        return Boolean.TRUE;
    }

    /**
     * 根据队列平滑收货，此方法走完，最后再走以前的收货方法
     * controller.ShopReceiveController#autoReceive
     * @return
     */
    @RequestMapping(value = "/autoReceiveByQueue",method = RequestMethod.POST)
    @ApiOperation(value="鲜食自动收货(新)")
    public Boolean autoReceiveByQueue()  {
        shopAutoReceiveService.autoReceiveByQueue();
        return Boolean.TRUE;
    }


    @RequestMapping(value = "/handleAutoReceive",method = RequestMethod.POST)
    @ApiOperation(value="手动收货:超时后自动收掉")
    public Boolean handleAutoReceive(@RequestParam(value = "orderTime",required = false) String orderTime) {
        return  shopAutoReceiveService.handleAutoReceive(orderTime);
    }

    @RequestMapping(value = "/autoReceiveRemind",method = RequestMethod.POST)
    @ApiOperation(value="自动收货:完成后提醒")
    public Boolean autoReceiveRemind(){
        return shopAutoReceiveService.autoReceiveRemind();
    }
    @RequestMapping(value = "/handleReceiveRemind",method = RequestMethod.POST)
    @ApiOperation(value="手动收货:t时间截止手动收货提醒")
    public Boolean handleReceiveRemind(){
        return shopAutoReceiveService.handleReceiveRemind();
    }


    @ApiOperation(value = "收货短信列表", notes = "收货短信列表", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @RequestMapping(value = "/receiveOrderSMSPage", method = RequestMethod.GET)
    public PageInfo<ReceiveOrderMessageODTO> receiveOrderSMSPage(ReceiveOrderMessageIDTO idto) {
        idto.setMessageType(MessageTypeEnums.SMS.getCode());
        return shopAutoReceiveService.receiveOrderMessagePage(idto);
    }
    @ApiOperation(value = "收货广播列表", notes = "收货广播列表", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @RequestMapping(value = "/receiveOrderBroadCastPage", method = RequestMethod.GET)
    public PageInfo<ReceiveOrderMessageODTO> receiveOrderBroadCastPage(ReceiveOrderMessageIDTO idto) {
        idto.setMessageType(MessageTypeEnums.BROADCAST.getCode());
        return shopAutoReceiveService.receiveOrderMessagePage(idto);
    }

    /**
     * 门店收货
     * @param receiveOrderVo
     * @return
     * @throws Throwable
     */
    @RequestMapping(value = "/addReceive",method = RequestMethod.POST)
    public String addReceive(@RequestBody ReceiveOrderVo receiveOrderVo) throws Throwable{
        String  cacheKey = receiveOrderVo.getSubOrderId()+QingyunConstant.UNDERLINE_SPLIT+ShopStockTypeConstant.SHOP_ORDER_RECIEVE;
        RLock lock = redissonClient.getLock(cacheKey);
        if(lock.tryLock()){
            try{
                 // 大店手动收货校验
                 shopReceiveService.bigShopReveiveCheck(receiveOrderVo);
                 return shopReceiveService.addReceive(receiveOrderVo);
            }finally{
                if(lock.isLocked()){ // 是否还是锁定状态
                    if(lock.isHeldByCurrentThread()){ // 时候是当前执行线程的锁
                        lock.unlock(); // 释放锁
                    }
                }
            }
        }else {
            QYAssert.isFalse("系统繁忙,请勿频繁操作!");
        }
        return "";
    }
    
    /*
     * 直送预订单收货
     */
    @RequestMapping(value = "/addPreReceive",method = RequestMethod.POST)
    public void addPreReceive(@RequestBody ReceiveOrderVo receiveOrderVo) throws Throwable{
    	String  cacheKey = receiveOrderVo.getPreOrderId()+QingyunConstant.UNDERLINE_SPLIT+ShopStockTypeConstant.SHOP_PREORDER_RECIEVE;
        RLock lock = redissonClient.getLock(cacheKey);
        if(lock.tryLock()){
            try{
                 shopReceiveService.addPreReceive(receiveOrderVo);
            }finally{
                if(lock.isLocked()){ // 是否还是锁定状态
                    if(lock.isHeldByCurrentThread()){ // 时候是当前执行线程的锁
                        lock.unlock(); // 释放锁
                    }
                }
            }
        }else {
            QYAssert.isFalse("系统繁忙,请勿频繁操作!");
        }
    }


    @RequestMapping(value= "/aduitList")
    public PageInfo<AuditInfoEntry> getAuditList(@RequestBody AuditQueryVo auditQueryVo){
        TokenInfo tokenInfo = FastThreadLocalUtil.getQY();
        Long enterpriseId = tokenInfo.getEnterpriseId();
        auditQueryVo.setEnterpriseId(String.valueOf(enterpriseId));
        return shopReceiveService.getAuditList(auditQueryVo);
    }


    @RequestMapping(value = "/aduitDetail",method = RequestMethod.POST)
    public ReceiveAuditEntry getAuditDetail(@RequestBody AuditDetailVo auditDetailVo){
        return shopReceiveService.getAuditDetail(auditDetailVo);
    }

    @RequestMapping(value = "/saveAduit",method = RequestMethod.POST)
    public void saveAduit(@RequestBody AuditSaveVo auditSaveVo) throws Throwable {
        shopReceiveService.saveAduit(auditSaveVo);
    }

    /*
     * 新增门店收货单
     */
    @RequestMapping(value = "/newReceiveOrder",method = RequestMethod.POST)
    public void newReceiveOrder(@RequestBody ShopReceiveOrderVo shopReceiveOrderVo){
        shopReceiveService.newReceiveOrder(shopReceiveOrderVo);
    }
    
    @RequestMapping(value = "/cancelReceiveOrder",method = RequestMethod.POST)
    public void cancelReceiveOrder(@RequestBody ShopReceiveOrderVo shopReceiveOrderVo){
        shopReceiveService.cancelReceiveOrder(shopReceiveOrderVo);
    }

    @RequestMapping(value = "/getShopName",method = RequestMethod.GET)
    public String getShopName(String storeId){
        return  shopReceiveService.getShopName(storeId);
    }

    @RequestMapping(value = "/preOrder/list", method = RequestMethod.POST)
    public PageInfo <ShopReceiveEntry> preOrderList(@RequestBody ShopReceiveVo shopReceiveVo) {
        PageInfo <ShopReceiveEntry> pageInfo = shopReceiveService.getPreOrderListByCondition(shopReceiveVo);
        return pageInfo;
    }

    @RequestMapping(value = "/preOrder/showReceive", method = RequestMethod.POST)
    public ShopReceiveOrderInfoEntry showPreOrderReceive(@RequestBody QuerySubOrderVo querySubOrderVo) throws Throwable {
        return  shopReceiveService.showPreOrderReceive(querySubOrderVo);
    }

    @RequestMapping(value = "/distribution/ok/{distributionCode}/{userId}")
    public void autoReceiveByDirstributionOk(@PathVariable("distributionCode") String distributionCode
                                 ,@PathVariable("userId") Long userId,@RequestBody List<Long> subOrderIds){
        processor.handle(distributionCode,userId,subOrderIds);
    }

    /**
     * 预订单收货列表(总部)
     * @param shopReceiveHQVo
     * @return
     */
    @RequestMapping(value = "/preOrderHQ/list", method = RequestMethod.POST)
    public PageInfo <ShopReceiveHQEntry> preOrderHQList(@RequestBody ShopReceiveHQVo shopReceiveHQVo) {
        PageInfo <ShopReceiveHQEntry> pageInfo = shopReceiveService.getPreOrderHQListByCondition(shopReceiveHQVo);
        return pageInfo;
    }



    @ApiOperation(value = "查询收货详情(鲜道前置仓)", notes = "查询收货详情(鲜道前置仓)", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @RequestMapping(value = "/xdShowReceive", method = RequestMethod.POST)
    public ShopReceiveOrderInfoEntry xdShowReceive(@RequestBody QuerySubOrderVo querySubOrderVo) throws Throwable {
        TokenInfo tokenInfo = FastThreadLocalUtil.getQY();
        querySubOrderVo.setStoreId(tokenInfo.getStoreId());
        querySubOrderVo.setShopId(tokenInfo.getShopId());
        return  shopReceiveService.xdShowReceive(querySubOrderVo);
    }


    @ApiOperation(value = "收货操作(鲜道前置仓)", notes = "收货操作(鲜道前置仓)", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @RequestMapping(value = "/addXdReceive",method = RequestMethod.POST)
    @RepeatSubmitAnno(value = RepeatSubmitBusinessCode.SHOP_RECEIVE_ORDER,expireTime =10 )
    public Boolean addXdReceive(@RequestBody ReceiveOrderVo receiveOrderVo) throws Throwable{
        TokenInfo tokenInfo = FastThreadLocalUtil.getQY();
        receiveOrderVo.setStoreId(tokenInfo.getStoreId()+"");
        receiveOrderVo.setShopId(tokenInfo.getShopId());
        receiveOrderVo.setCreateId(tokenInfo.getUserId());

        String  cacheKey = receiveOrderVo.getSubOrderId()+QingyunConstant.UNDERLINE_SPLIT+ShopStockTypeConstant.XD_SHOP_ORDER_RECIEVE;
        RLock lock = redissonClient.getLock(cacheKey);
        lock.lock(5L, TimeUnit.SECONDS);
        try{
             // 大店手动收货校验
             shopReceiveService.bigShopReveiveCheck(receiveOrderVo);
             shopReceiveService.addXdReceive(receiveOrderVo);
        }finally{
            if(lock.isLocked()){ // 是否还是锁定状态
                if(lock.isHeldByCurrentThread()){ // 时候是当前执行线程的锁
                    lock.unlock(); // 释放锁
                }
            }
        }
        return true;
    }

    /**
     * 商品组配送统计已生成采购单的预订单
     * @param vo
     * @return
     */
    @RequestMapping(value= "/queryTjPreOrderList")
    public List<TjPreOrderEntry> queryTjPreOrderList(@RequestBody TjPreOrderVO vo){
        return shopReceiveService.queryTjPreOrderList(vo);
    }

}
