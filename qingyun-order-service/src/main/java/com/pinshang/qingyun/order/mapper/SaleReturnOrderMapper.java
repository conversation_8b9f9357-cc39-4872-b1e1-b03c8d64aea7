package com.pinshang.qingyun.order.mapper;

import com.pinshang.qingyun.base.mybatis.MyMapper;
import com.pinshang.qingyun.order.dto.*;
import com.pinshang.qingyun.order.mapper.entry.order.SaleReturnOrderDetailEntry;
import com.pinshang.qingyun.order.mapper.entry.order.SaleReturnOrderItemEntry;
import com.pinshang.qingyun.order.mapper.entry.order.SaleReturnReportSumEntry;
import com.pinshang.qingyun.order.model.order.SaleReturnOrder;
import com.pinshang.qingyun.order.vo.order.SaleReturnOrderRespVo;
import com.pinshang.qingyun.order.vo.order.SaleReturnReportVo;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

/**
 * Created by honway on 2017/11/2 16:21.
 */
@Repository
public interface SaleReturnOrderMapper extends MyMapper<SaleReturnOrder> {

    /**
     * 查询 门店退货列表
     * @param params 查询列表的参数
     * @return 返回 门店退货单列表
     */
    List<SaleReturnOrderRespVo> list(Map<String, Object> params);

    /**
     * 查询 门店退货列表(PDA)
     */
    List<SaleReturnOrderODTO> querySaleReturnOrderList(SaleReturnOrderIDTO idto);

    /**
     * 查询退货单详情
     * @param code
     * @param enterpriseId
     * @return
     */
    SaleReturnOrderDetailEntry detail(@Param("code") String code, @Param("enterpriseId") Long enterpriseId);

    /**
     * 根据退货单code查询明细信息
     * @param code
     * @param enterpriseId
     * @return
     */
    List<SaleReturnOrderItemEntry> itemList(@Param("code") String code, @Param("enterpriseId") Long enterpriseId);


    /**
     * 分页查询退货差异报表
     * @param vo
     * @return
     */
    List<SaleReturnReportODTO> listReturnReport(@Param("vo")SaleReturnReportVo vo);

    SaleReturnReportSumEntry queryReturnReportSumEntry(@Param("vo")SaleReturnReportVo vo);

    /**
     * 查询待收货/收货中的客户
     */
    List<StoreInfoODTO> queryStoreInfo(StoreInfoIDTO storeInfoIDTO);

    List<SaleReturnOrderODTO> querySaleReturnOrderListById(@Param("saleReturnOrderId") Long saleReturnOrderId);
}
