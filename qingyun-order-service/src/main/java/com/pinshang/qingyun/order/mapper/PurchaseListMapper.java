package com.pinshang.qingyun.order.mapper;

import com.pinshang.qingyun.order.mapper.entry.purchase.ConnectionPurchaseDetailEntry;
import com.pinshang.qingyun.order.mapper.entry.purchase.ConnectionPurchaseEntry;
import com.pinshang.qingyun.order.mapper.entry.purchase.SendPurchaseDetailEntry;
import com.pinshang.qingyun.order.vo.purchase.PurchaseListVo;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface PurchaseListMapper {

    List<ConnectionPurchaseDetailEntry> findConnectionPurchaseDetail(PurchaseListVo vo);

    List<ConnectionPurchaseDetailEntry> findConnectionPurchaseDetailNew(PurchaseListVo vo);

    List<ConnectionPurchaseEntry> findSendPurchaseByPage(PurchaseListVo vo);

    List<SendPurchaseDetailEntry> findSendPurchaseDetail(PurchaseListVo vo);
}