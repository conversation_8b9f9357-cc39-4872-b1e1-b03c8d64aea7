package com.pinshang.qingyun.order.controller.xda.v2;

import com.pinshang.qingyun.base.api.ApiResponse;
import com.pinshang.qingyun.base.configure.expand.QYAssert;
import com.pinshang.qingyun.order.dto.xda.v2.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;

/**
 * <AUTHOR>
 * @date 23/6/14/014 10:04
 * 新地址: XdaAppOrderV3Controller
 */
@RestController
@RequestMapping("/xda/orderV2")
@Api(value = "鲜达APP订单相关接口",tags ="XdaAppOrderV2Controller",description ="鲜达APP订单相关接口" )
@Deprecated
public class XdaAppOrderV2Controller {


    /**
     * 去结算 预付费
     * @param xdaPreOrderV2IDTO
     * @return
     */
    @ApiOperation(value = "预览订单", notes = "预览订单")
    @ApiImplicitParam(name = "xdaPreOrderV2IDTO", value = "", required = true, paramType = "body", dataTypeClass = XdaPreOrderV2IDTO.class)
    @RequestMapping(value = "/preOrderV2",method = RequestMethod.POST)
    public XdaPreOrderV2ODTO preOrderView(@RequestBody XdaPreOrderV2IDTO xdaPreOrderV2IDTO){
        QYAssert.isFalse("请升级到最新版本");
        return null;
    }

    /**
     * 预览订单 详细信息
     */
    @ApiOperation(value = "预览订单 -商品详细信息", notes = "预览订单 -商品详细信息")
    @ApiImplicitParam(name = "xdaPreOrderV2IDTO", value = "", required = true, paramType = "body", dataTypeClass = XdaPreOrderV2IDTO.class)
    @RequestMapping(value = "/preOrderViewCommodityList",method = RequestMethod.POST)
    public XdaPreOrderItemV2ODTO preOrderViewCommodityList(@RequestBody XdaPreOrderV2IDTO xdaPreOrderV2IDTO){
        QYAssert.isFalse("请升级到最新版本");
        return null;
    }

    /**************************************订单创建后的相关接口****************************************************/
    /**
     * 创建订单
     * @param xdaCreatePrePayOrderV2IDTO
     * @return
     */
    @ApiOperation(value = "创建订单", notes = "创建订单")
    @ApiImplicitParam(name = "xdaCreatePrePayOrderV2IDTO", value = "", required = true, paramType = "body", dataTypeClass = XdaCreatePrePayOrderV2IDTO.class)
    @RequestMapping(value = "/creatOrderV2",method = RequestMethod.POST)
    public boolean creatOrder(@RequestBody XdaCreatePrePayOrderV2IDTO xdaCreatePrePayOrderV2IDTO){
        QYAssert.isFalse("请升级到最新版本");
        return false;
    }

    @PostMapping("/listV2")
    @ApiOperation(value = "订单列表")
    public ApiResponse<XdaOrderAppV2ODTO> queryOrderListByPageV2(@RequestBody XdaQueryOrderAppParamV2 param, HttpServletResponse response){
        QYAssert.isFalse("请升级到最新版本");
        return null;
    }

    /**
     * 订单明细
     * @param orderCode
     * @return
     */
    @GetMapping("/queryOrderDetailByCodeV2")
    @ApiOperation(value = "订单列表详情")
    public XdaOrderAppV2ODTO queryOrderDetailByCodeV2(@RequestParam(value = "orderCode",required = false) String orderCode){
        QYAssert.isFalse("请升级到最新版本");
        return null;
    }

    /**
     * 订单详情
     */
    @ApiOperation(value = "订单列表详情-订单商品清单", notes = "订单列表详情-订单商品清单")
    @RequestMapping(value = "/queryXdaOrderDetailV2",method = RequestMethod.POST)
    public XdaPreOrderItemV2ODTO queryXdaOrderCommodityDetailV2(@RequestParam(value = "orderCode",required = false) String orderCode){
        QYAssert.isFalse("请升级到最新版本");
        return null;
    }

    /**
     * 订单复制
     * @param orderId
     * @return
     */
    @RequestMapping(value = "/copyOrderByIdXdaAppV2",method = RequestMethod.GET)
    @ApiOperation(value = "订单复制", notes = "订单复制")
    public Integer copyOrderByIdXdaAppV2(@RequestParam(value = "orderId",required = false) Long orderId,@RequestParam(value = "orderDate",required = false) String orderDate){
        QYAssert.isFalse("请升级到最新版本");
        return null;
    }

    @PostMapping("/cancelOrderV2")
    @ApiOperation(value = "订单取消", notes = "订单取消")
    public Integer cancelOrderV2(@RequestBody OrderCancelV2IDTO idto){
        QYAssert.isFalse("请升级到最新版本");
        return null;
    }

}
