package com.pinshang.qingyun.order.service;

import com.pinshang.qingyun.base.enums.SmsMessageTypeEnums;
import com.pinshang.qingyun.common.dto.SmsMessageIDTO;
import com.pinshang.qingyun.common.service.SmsMessageClient;
import com.pinshang.qingyun.msg.dto.im.ImMessageSaveIDTO;
import com.pinshang.qingyun.msg.service.im.IMMessageClient;
import com.pinshang.qingyun.order.enums.MessageTypeEnums;
import com.pinshang.qingyun.order.enums.ReceiveOrderBusinessTypeEnums;
import com.pinshang.qingyun.order.enums.ShopReceiveOrderStatusEnums;
import com.pinshang.qingyun.order.enums.ShopStockTypeEnums;
import com.pinshang.qingyun.order.mapper.ShopReceiveOrderMapper;
import com.pinshang.qingyun.order.mapper.SubOrderItemMapper;
import com.pinshang.qingyun.order.mapper.entry.order.ReceiveOrderMessageEntry;
import com.pinshang.qingyun.order.vo.order.HandleReceiveOrderLogVo;
import com.pinshang.qingyun.order.vo.order.ShopStockCommodityVo;
import com.pinshang.qingyun.order.vo.shop.Quantity;
import com.pinshang.qingyun.order.vo.shop.ReceiveOrderVo;
import com.pinshang.qingyun.order.vo.shop.ShopStockVo;
import com.pinshang.qingyun.shop.dto.ShopEmployeeODTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * @Author: sk
 * @Date: 2021/3/16
 */
@Service
@Slf4j
public class ShopHandleAutoReceiveService {

    @Autowired
    private ShopReceiveService shopReceiveService;
    @Autowired
    private SubOrderItemMapper subOrderItemMapper;
    @Autowired
    private SmsMessageClient smsMessageClient;
    @Autowired
    private IMMessageClient iMMessageClient;
    @Autowired
    private ShopReceiveOrderMapper shopReceiveOrderMapper;


    /**
     * 手动收货，超时后自动收掉
     * @param receiveOrderVo
     * @return
     */
    @Transactional(rollbackFor = Exception.class,propagation = Propagation.REQUIRES_NEW)
    public Boolean addHandleReceive(ReceiveOrderVo receiveOrderVo) {
        ShopStockVo shopStockVo = new ShopStockVo();
        shopStockVo.setStoreId(Long.valueOf(receiveOrderVo.getStoreId()));
        shopStockVo.setUserId(Long.valueOf(receiveOrderVo.getReceiveId()));
        List<ShopStockCommodityVo> shopStockCommodityVos = new ArrayList<>();

        List<HandleReceiveOrderLogVo> handleReceiveOrderLogList = new ArrayList<>();
        for (Quantity vo : receiveOrderVo.getReceiveQuantities()) {
            String commodityId = vo.getCommodityId();
            // 更新实收数量 = 发货数量
            subOrderItemMapper.updateHandleRealReceiveQuantity(receiveOrderVo.getSubOrderId(), commodityId);

            BigDecimal newQuantity = vo.getRealReceiveQuantity() == null ? vo.getRealDeliveryQuantity() : vo.getRealDeliveryQuantity().subtract(vo.getRealReceiveQuantity());
            ShopStockCommodityVo shopStockCommodityVo = new ShopStockCommodityVo();
            shopStockCommodityVo.setModifyQuantity(newQuantity);
            shopStockCommodityVo.setCommodityId(Long.valueOf(vo.getCommodityId()));
            shopStockCommodityVo.setPrice(vo.getPrice());
            shopStockCommodityVo.setTotalPrice(vo.getPrice().multiply(newQuantity));
            shopStockCommodityVos.add(shopStockCommodityVo);

            HandleReceiveOrderLogVo logVo = new HandleReceiveOrderLogVo();
            logVo.setShopId(receiveOrderVo.getShopId());
            logVo.setOrderCode(receiveOrderVo.getOrderCode());
            logVo.setSubOrderId(Long.valueOf(receiveOrderVo.getSubOrderId()));
            logVo.setCommodityId(Long.valueOf(vo.getCommodityId()));
            logVo.setReceivedQuantity(vo.getRealReceiveQuantity());
            logVo.setQuantity(newQuantity);
            logVo.setCreateId(-1L);
            logVo.setCreateTime(new Date());
            handleReceiveOrderLogList.add(logVo);
        }

        // 批量新增日志
        subOrderItemMapper.batchInsertHandleReceiveOrderLog(handleReceiveOrderLogList);

        shopStockVo.setList(shopStockCommodityVos);
        shopStockVo.setType(ShopStockTypeEnums.SHOP_RECEIVE.getCode());
        shopStockVo.setReferCode(receiveOrderVo.getSubOrderId());

        // 更新收货单状态为完成
        shopReceiveService.updateSubOrderStatus(receiveOrderVo, ShopReceiveOrderStatusEnums.PASS,null);

        // 调用入库操作
        shopReceiveService.addStockReceipt(receiveOrderVo,shopStockVo);
        return Boolean.TRUE;
    }




    /**
     * 自动收货:完成后  发送短信和广播
     * @param shopEmployee
     */
    @Transactional(rollbackFor = Exception.class,propagation = Propagation.REQUIRES_NEW)
    public void autoReceiveRemindSendMsg(ShopEmployeeODTO shopEmployee,Integer businessType, String hour){

        // 发送短信
        ReceiveOrderMessageEntry receiveOrderMessageEntry1 = getSMSMessage(shopEmployee, businessType,  hour);
        // 发送广播
        ReceiveOrderMessageEntry receiveOrderMessageEntry2 = getBroadCastMessage(shopEmployee, businessType, hour);

        List<ReceiveOrderMessageEntry> receiveOrderMessageList = new ArrayList<>();
        receiveOrderMessageList.add(receiveOrderMessageEntry1);
        receiveOrderMessageList.add(receiveOrderMessageEntry2);
        shopReceiveOrderMapper.batchInsertReceiveOrderMessage(receiveOrderMessageList);
    }

    /**
     * 发送短信
     * @return
     */
    private ReceiveOrderMessageEntry getSMSMessage(ShopEmployeeODTO shopEmployee, Integer businessType, String hour){
        // 组装消息内容
        String msgContent = appendMessageContent(shopEmployee.getEmployeeName(), shopEmployee.getShopName(), businessType, hour);

        SmsMessageIDTO smsMessageIDTO = new SmsMessageIDTO();
        smsMessageIDTO.setPhone(shopEmployee.getEmployeePhone());
        smsMessageIDTO.setContent(msgContent);
        smsMessageIDTO.setMessageType(SmsMessageTypeEnums.ADJUST_PRICE_WARN);
        smsMessageClient.sendOneMessage(smsMessageIDTO);

        return setReceiveOrderMessageEntry(shopEmployee, msgContent, businessType, MessageTypeEnums.SMS.getCode());
    }


    /**
     * 发送广播
     * @return
     */
    public ReceiveOrderMessageEntry getBroadCastMessage(ShopEmployeeODTO shopEmployee, Integer businessType, String hour){
        // 组装消息内容
        String msgContent = appendMessageContent(shopEmployee.getEmployeeName(), shopEmployee.getShopName(), businessType, hour);

        List<ImMessageSaveIDTO.User> userList = new ArrayList<>();
        ImMessageSaveIDTO.User user = new ImMessageSaveIDTO.User();
        user.setUserId(shopEmployee.getUserId() + "");
        user.setUserName(shopEmployee.getEmployeeName());
        userList.add(user);

        ImMessageSaveIDTO imMessageSave = new ImMessageSaveIDTO();
        imMessageSave.setTitle(msgContent.substring(0,40));
        imMessageSave.setContent(msgContent);
        imMessageSave.setUserList(userList);
        imMessageSave.setCreateId(-1L);
        imMessageSave.setOrganizationName("系统");
        //消息类型：1广播、2系统通知、3部门通知
        imMessageSave.setType(1);
        iMMessageClient.saveImMessage(imMessageSave);

        return setReceiveOrderMessageEntry(shopEmployee, msgContent, businessType,MessageTypeEnums.BROADCAST.getCode());
    }

    /**
     * 组装消息内容
     */
    private String appendMessageContent(String employeeName, String shopName, Integer businessType, String hour) {
        StringBuffer sb = new StringBuffer();
        sb.append(employeeName + "：今日" + shopName + "门店的货物已送达，");
        if (businessType == ReceiveOrderBusinessTypeEnums.AUTORECEIVE.getCode()) {
            sb.append("按照您选择的收货方式，系统已按照大仓实发数量为门店自动收货。" +
                    "请及时清点货物，如有异议，请于今日在系统中完成【门店退库】。");
        }
        if (businessType == ReceiveOrderBusinessTypeEnums.HANDLERECEIVE.getCode()) {
            sb.append("请及时清点并确认收货。若今天" + hour + "点前未完成收货，系统将按大仓实发数量自动为门店收货。" +
                    "如有异议，请于今日在系统中完成【门店退库】，超时未提交，则视为门店已确认收货。");
        }
        return sb.toString();
    }

    private ReceiveOrderMessageEntry setReceiveOrderMessageEntry(ShopEmployeeODTO shopEmployee, String content, Integer businessType, Integer messageType) {
        return new ReceiveOrderMessageEntry(
                    shopEmployee.getShopId(),
                    shopEmployee.getEmployeeId(),
                    shopEmployee.getEmployeeCode(),
                    shopEmployee.getEmployeeName(),
                    shopEmployee.getEmployeePhone(),
                    businessType,
                    1,
                    content,
                    messageType,
                    -1L,
                    new Date()
            );
    }
}
