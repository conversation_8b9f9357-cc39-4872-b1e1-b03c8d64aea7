package com.pinshang.qingyun.order.service;

import com.pinshang.qingyun.order.mapper.ShoppingCartItemMapper;
import com.pinshang.qingyun.order.mapper.ShoppingCartMapper;
import com.pinshang.qingyun.order.model.order.ShoppingCart;
import com.pinshang.qingyun.order.vo.order.CreateOrderVo;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * @Author: sk
 * @Date: 2023/1/30
 */
@Service
public class DeleteLimitCartService {

    @Autowired
    private ShoppingCartMapper shoppingCartMapper;
    @Autowired
    private ShoppingCartItemMapper shoppingCartItemMapper;


    //删除限量商品
    @Transactional(rollbackFor = Exception.class)
    public void deleteLimitCommoditys(Long shoppingCartId, List<CreateOrderVo.CreateOrderItemIDTO> itemList) {
        if(CollectionUtils.isNotEmpty(itemList)){
            ShoppingCart shoppingCart = shoppingCartMapper.selectByPrimaryKey(shoppingCartId);
            if (null != shoppingCart) {
                for (CreateOrderVo.CreateOrderItemIDTO item : itemList) {
                    shoppingCartItemMapper.deleteByPrimaryKey(item.getShoppingCartItemId());
                }
                if (shoppingCart.getVarietyTotal().equals(itemList.size())) {
                    shoppingCartMapper.deleteByPrimaryKey(shoppingCart.getId());
                } else {
                    shoppingCart.setVarietyTotal(shoppingCart.getVarietyTotal().intValue() - itemList.size());
                    shoppingCartMapper.updateByPrimaryKey(shoppingCart);
                }
            }
        }
    }

}
