package com.pinshang.qingyun.order.mapper;

import com.pinshang.qingyun.base.mybatis.MyMapper;

import com.pinshang.qingyun.order.model.order.XdaCommoditySaleDayStatistics;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface CommoditySaleDayStatisticsMapper extends MyMapper<XdaCommoditySaleDayStatistics> {

    Integer batchUpdate(@Param("dayStatisticsList") List<XdaCommoditySaleDayStatistics> dayStatisticsList);

    //查询
    List<XdaCommoditySaleDayStatistics> queryDayStatisticsList();

    List<XdaCommoditySaleDayStatistics> queryDayCommodityQuantity(@Param("date") String date, @Param("ids") List<Long> commodityId);

    void deleteSaleDayStatistics(@Param("orderTime") String orderTime);
    void insertSaleDayStatistics(@Param("orderTime") String orderTime);
}
