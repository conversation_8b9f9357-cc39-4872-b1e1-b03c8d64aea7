package com.pinshang.qingyun.order.controller;

import com.github.pagehelper.PageInfo;
import com.pinshang.qingyun.base.enums.shop.ShopTypeEnums;
import com.pinshang.qingyun.box.utils.SpringUtil;
import com.pinshang.qingyun.infrastructure.components.IMqSenderComponent;
import com.pinshang.qingyun.infrastructure.components.params.mq.MqMessage;
import com.pinshang.qingyun.kafka.base.KafkaTopicConstant;
import com.pinshang.qingyun.kafka.enums.KafkaMessageOperationTypeEnum;
import com.pinshang.qingyun.kafka.enums.KafkaMessageTypeEnum;
import com.pinshang.qingyun.order.mapper.entry.purchase.ShopCommodityPurchaseStatusEntry;
import com.pinshang.qingyun.order.mapper.entry.purchase.ShoppingCartKafkaEntry;
import com.pinshang.qingyun.order.mapper.entry.shop.*;
import com.pinshang.qingyun.order.service.ShopCommodityPurchaseStatusService;
import com.pinshang.qingyun.order.vo.commodity.CommodityPurchaseStatusVo;
import com.pinshang.qingyun.order.vo.kafka.ShopCommodityPurchaseStatusKafkaVo;
import com.pinshang.qingyun.order.vo.purchase.ShopCommodityModifyPurchaseStatusVo;
import com.pinshang.qingyun.order.vo.purchase.ShopCommodityPurchaseStatusVo;
import com.pinshang.qingyun.order.vo.shop.PriceModelLogVo;
import com.pinshang.qingyun.order.vo.shop.ShopQtOrderProductVo;
import com.pinshang.qingyun.order.vo.shop.XsShopCommodityPurchaseStatusVo;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/shopCommodityPurchaseStatus")
public class ShopCommodityPurchaseStatusController {
    @Autowired
    private ShopCommodityPurchaseStatusService shopCommodityPurchaseStatusService;

    @Autowired
    private RedissonClient redissonClient;

    @Autowired
    private IMqSenderComponent mqSenderComponent;

    @Value("${application.name.switch}")
    private String applicationNameSwitch;
    /**
     * 修改门店商品是否可采状态
     * @param vos
     * @return
     */
    @RequestMapping(value = "/updateShopCommodityPurchaseStatus", method = RequestMethod.POST)
    public int updateShopCommodityPurchaseStatus(@RequestBody List<CommodityPurchaseStatusVo> vos){
        return shopCommodityPurchaseStatusService.updateShopCommodityPurchaseStatus(vos);
    }

    /**
     * 保存门店商品状态是否可采
     * @param vos
     * @return
     */
    @RequestMapping(value = "/saveShopCommodityPurchaseStatus", method = RequestMethod.POST)
    public int saveShopCommodityPurchaseStatus(@RequestBody List<CommodityPurchaseStatusVo> vos){
        return shopCommodityPurchaseStatusService.saveShopCommodityPurchaseStatus(vos);
    }

    @RequestMapping(value = "/selectShopQtOrderProduct", method = RequestMethod.POST)
    public List<ShopQtOrderProductEntry> selectShopQtOrderProduct(@RequestBody ShopQtOrderProductVo vo){
        return shopCommodityPurchaseStatusService.selectShopQtOrderProduct(vo);
    }

    @RequestMapping(value ="/selectProductPriceModelListByPriceModeClode",method = RequestMethod.GET)
    public List<CommodityPriceEntry> selectProductPriceModelListByPriceModeClode(@RequestParam(value="priceModeClode",required = false) String priceModeClode){
         return shopCommodityPurchaseStatusService.selectProductPriceModelListByPriceModeClode(priceModeClode);
    }
    @RequestMapping(value ="/selectCommodityByProductPriceModelId",method = RequestMethod.GET)
    public List<CommodityListEntry> selectCommodityByProductPriceModelId(@RequestParam(value="productPriceModelId",required = false) Long productPriceModelId){
        return shopCommodityPurchaseStatusService.selectCommodityByProductPriceModelId(productPriceModelId);

    }
    @RequestMapping(value = "/priceModelLogList", method = RequestMethod.POST)
    public PageInfo<PriceModelLogEntry> priceModelLogList(@RequestBody PriceModelLogVo vo){
        return shopCommodityPurchaseStatusService.selectProductPriceModel(vo);
    }
    @RequestMapping(value ="/selectXsShopCommodityPurchase",method = RequestMethod.POST)
    public List<XsShopCommodityPurchaseStatusEntry> selectXsShopCommodityPurchaseStatus(@RequestBody XsShopCommodityPurchaseStatusVo vo){
        return shopCommodityPurchaseStatusService.selectXsShopCommodityPurchaseStatus(vo);
    }

    @PostMapping("/selectShopCommodityPurchaseStatusList")
    public PageInfo<ShopCommodityPurchaseStatusEntry> selectShopCommodityPurchaseStatusList(@RequestBody ShopCommodityPurchaseStatusVo shopCommodityPurchaseStatusVo){
        return shopCommodityPurchaseStatusService.selectShopCommodityPurchaseStatusList(shopCommodityPurchaseStatusVo);
    }

    @PostMapping("/modifyShopCommodityPurchaseStatusList")
    public Integer modifyShopCommodityPurchaseStatusList(@RequestBody ShopCommodityModifyPurchaseStatusVo shopCommodityModifyPurchaseStatusVo){
        RLock lock = redissonClient.getLock(this.getClass().getName());
        Integer i = 0;
        if (lock.tryLock()) {
            try {
                i = shopCommodityPurchaseStatusService.modifyShopCommodityPurchaseStatusList(shopCommodityModifyPurchaseStatusVo);
                List<Long> commodityIdList = null == shopCommodityModifyPurchaseStatusVo.getCommodityCodes() ?
                        null :shopCommodityPurchaseStatusService.selectCommodityByCommodityCode(shopCommodityModifyPurchaseStatusVo.getCommodityCodes());
                if(SpringUtil.isNotEmpty(shopCommodityModifyPurchaseStatusVo.getShopId()) && null != commodityIdList && null != shopCommodityModifyPurchaseStatusVo.getPurchaseStatus()){
                    List<Long> xsShopId = shopCommodityPurchaseStatusService.selectShopByShopTypeAndShopId(ShopTypeEnums.XS.getCode(),shopCommodityModifyPurchaseStatusVo.getShopId());
                    List<Long> xdShopId = shopCommodityPurchaseStatusService.selectShopByShopTypeAndShopId(ShopTypeEnums.XD.getCode(),shopCommodityModifyPurchaseStatusVo.getShopId());
                    if(shopCommodityModifyPurchaseStatusVo.getPurchaseStatus().equals(1)){
                        if(SpringUtil.isNotEmpty(xdShopId) && SpringUtil.isNotEmpty(commodityIdList)){
                            List<ShoppingCartKafkaEntry> shoppingCartKafkaODTOS = shopCommodityPurchaseStatusService.selectShopCommodityByShopCommodityStatus(xdShopId,commodityIdList);
                            if(null != shoppingCartKafkaODTOS){
                                //KafkaMessageWrapper message = new KafkaMessageWrapper(KafkaMessageTypeEnum.XD_SHOP_COMMODITY_SHOPPING_CART, shoppingCartKafkaODTOS, KafkaMessageOperationTypeEnum.UPDATE, UUID.randomUUID().toString());
                                mqSenderComponent.send(applicationNameSwitch+ KafkaTopicConstant.XD_SHOP_COMMODITY_SHOPPING_CART_TOPIC,
                                        shoppingCartKafkaODTOS,
                                        MqMessage.MQ_KAFKA,
                                        KafkaMessageTypeEnum.XD_SHOP_COMMODITY_SHOPPING_CART.name(),
                                        KafkaMessageOperationTypeEnum.UPDATE.name());
                            }
                        }
                    }
                    ShopCommodityPurchaseStatusKafkaVo xsKafkaVo = new ShopCommodityPurchaseStatusKafkaVo( null == xsShopId ?null:xsShopId,commodityIdList,shopCommodityModifyPurchaseStatusVo.getPurchaseStatus());
                    ShopCommodityPurchaseStatusKafkaVo xdKafkaVo = new ShopCommodityPurchaseStatusKafkaVo( null == xdShopId ?null:xdShopId,commodityIdList,shopCommodityModifyPurchaseStatusVo.getPurchaseStatus());
                    if(null != xsKafkaVo){
                        // KafkaMessageWrapper message = new KafkaMessageWrapper(KafkaMessageTypeEnum.XS_SHOP_COMMODITY_PURCHASE_STATUS, xsKafkaVo, KafkaMessageOperationTypeEnum.UPDATE, UUID.randomUUID().toString());
                        mqSenderComponent.send(applicationNameSwitch+ KafkaTopicConstant.XS_SHOP_COMMODITY_PURCHASE_STATUS_TOPIC,
                                xsKafkaVo,
                                MqMessage.MQ_KAFKA,
                                KafkaMessageTypeEnum.XS_SHOP_COMMODITY_PURCHASE_STATUS.name(),
                                KafkaMessageOperationTypeEnum.UPDATE.name());
                    }
                    if(null != xdKafkaVo){
                        // KafkaMessageWrapper message = new KafkaMessageWrapper(KafkaMessageTypeEnum.XD_SHOP_COMMODITY_PURCHASE_STATUS, xdKafkaVo, KafkaMessageOperationTypeEnum.UPDATE, UUID.randomUUID().toString());
                        mqSenderComponent.send(applicationNameSwitch+ KafkaTopicConstant.XD_SHOP_COMMODITY_PURCHASE_STATUS_TOPIC,
                                xdKafkaVo,
                                MqMessage.MQ_KAFKA,
                                KafkaMessageTypeEnum.XD_SHOP_COMMODITY_PURCHASE_STATUS.name(),
                                KafkaMessageOperationTypeEnum.UPDATE.name());
                    }
                    
                }
            } finally {
                lock.unlock();
            }
        }
        return i;



    }
}
