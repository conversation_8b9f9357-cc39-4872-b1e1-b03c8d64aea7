package com.pinshang.qingyun.order.mapper.entry.shop;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2017/6/8.
 */
@Data
public class ShopReceiveOrderEntry {

    @ApiModelProperty("订单id")
    private String id;

    @ApiModelProperty("送货日期")
    private Date orderTime;

    @ApiModelProperty("订单编号")
    private String subOrderCode;

    private String supplierId;

    @ApiModelProperty("供应商")
    private String supplierName;

    private Integer status;

    private String logisticsModel;

    private Date receiveTime;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("收货人id")
    private String receiveId;

    private String receiveName;

    private String shopId;

    private String shopName;
    
    private Long storeId;

    private Long receiveUserId;

}
