package com.pinshang.qingyun.order.manage.delivery.dto;

import com.pinshang.qingyun.renderer.annotation.FieldRender;
import com.pinshang.qingyun.renderer.constant.RenderFieldHelper;
import com.pinshang.qingyun.renderer.enums.FieldTypeEnum;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.Optional;

@Data
public class DeliveryOrderDTO {

    private Long orderId;

    private Date orderTime;

    private String orderCode;

    /**
     * 多发金额
     */
    private BigDecimal overDeliveryMoney;

    /**
     * 少发金额
     */
    private BigDecimal underDeliveryMoney;

    private StoreInfo storeInfo;

    private Long storeTypeId;

    /**
     * 订单类型(1=PC下单,2=APP下单, 8=鲜达APP 9-批发app 10-门店订货下单 12-清美订单 13-mini团购订单 21-团购订单 22-门店订货PC版 23-门店订货PDA版 24-门店订货代理补货 25-大仓配货自动下单 26-门店自动订货 27-代理订货 28-直送补单 29-直送审核通过 30-提货卡自动下单 31-门店加货申请单 32-日日鲜商品自动下单 33-提货卡预约订单 34-云超团购自动下单 35-玖琅自动下单
     * @see com.pinshang.qingyun.base.enums.order.OrderTypeEnum
     */
    private Integer orderType;


}
