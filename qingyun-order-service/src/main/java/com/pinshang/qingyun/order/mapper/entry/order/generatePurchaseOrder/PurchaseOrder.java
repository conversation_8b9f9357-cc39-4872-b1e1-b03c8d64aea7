package com.pinshang.qingyun.order.mapper.entry.order.generatePurchaseOrder;

import com.pinshang.qingyun.base.enums.PurchaseOrderEnums;
import lombok.Data;

import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;

@Data
@Table(name = "t_dc_purchase_order")
@Entity
public class PurchaseOrder {
    /**
     * 主键id
     */
    @Id
    private Long id;

    /**
     * 采购单编号
     */
    private String purchaseCode;

    /**
     * 供应商id
     */
    private Long supplierId;

    /**
     * 收货仓库id
     */
    private Long warehouseId;

    /**
     * 订单日期
     */
    private Date orderTime;

    /**
     * 最晚收货日期
     */
    private Date latestReceiveTime;

    /**
     * 订单金额
     */
    private BigDecimal totalAmount;

    /**
     * 订单状态,0-收货中,1-已关闭,2-已取消
     */
    private Integer status;

    /**
     * 企业id
     */
    private Long enterpriseId;

    /**
     * 备注
     */
    private String remark;

    /**
     * 创建日期
     */
    private LocalDateTime createTime;

    /**
     * 更新日期
     */
    private LocalDateTime updateTime;

    /**
     * 采购人(因为页面上,采购人可以不是当前创建人)
     */
    private Long purchaserId;

    /**
     * 创建人
     */
    private Long createId;

    /**
     * 更新人
     */
    private Long updateId;

    //物流模式
    private Integer logisticsModel;


    private Long storeId;

    /**
     * 关闭采购单
     */
    public void close() {
        this.status = PurchaseOrderEnums.CLOSED.getCode();
    }
}