package com.pinshang.qingyun.order.controller.xda;

import com.pinshang.qingyun.base.configure.expand.QYAssert;
import com.pinshang.qingyun.order.dto.shopcart.*;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

/**
 * 鲜达购物车相关功能
 * 新地址: XdaShoppingCartV3Controller
 */
@RestController
@RequestMapping("/xda/shoppingCart")
@Deprecated
public class XdaShopcartController {

    @PostMapping("/add")
    @ApiOperation(value = "购物车--加")
    public ShoppingCartODTO addShopCart(@RequestBody ShoppingCartAddIDTO idto){
        QYAssert.isFalse("请升级到最新版本");
       return null;

    }
    @PostMapping("/minus")
    @ApiOperation(value = "购物车--减")
    public ShoppingCartODTO minusShopCart(@RequestBody ShoppingCartMinusIDTO idto){
        QYAssert.isFalse("请升级到最新版本");
        return null;
    }
    @PostMapping("/setNum")
    @ApiOperation(value = "购物车--设置数量")
    public ShoppingCartODTO setNumShopCart(@RequestBody ShoppingCartSetNumIDTO idto){
        QYAssert.isFalse("请升级到最新版本");
        return null;
    }
    @PostMapping("/clear")
    @ApiOperation(value = "购物车--清空")
    public ShoppingCartODTO clear(@RequestBody ShoppingCartClearIDTO idto){
        QYAssert.isFalse("请升级到最新版本");
        return null;
    }
    @GetMapping("/refresh")
    @ApiOperation(value = "购物车--刷新")
    public ShoppingCartODTO refresh(@RequestParam(value = "orderDate",required = false) String orderDate){
        QYAssert.isFalse("请升级到最新版本");
        return null;
    }
    @GetMapping("/clear/invalid")
    @ApiOperation(value = "购物车--清空失效商品")
    public ShoppingCartODTO clearInvalidCommodity(@RequestParam(value = "orderDate",required = false) String orderDate){
        QYAssert.isFalse("请升级到最新版本");
        return null;
    }
    @GetMapping("/category")
    @ApiOperation(value = "购物车--品类数量")
    public Integer getCategoryNum(@RequestParam(value = "orderDate",required = false) String orderDate, @RequestParam(value = "storeId",required = false) Long storeId){
        QYAssert.isFalse("请升级到最新版本");
        return null;
    }

}
