package com.pinshang.qingyun.order.mapper.entry.deliveryBill;

import cn.jiguang.common.utils.StringUtils;
import lombok.Data;

import java.text.SimpleDateFormat;
import java.util.Date;

/**
 * @description: 送货单查询对象
 * @author: hhf
 * @time: 2021/8/4 14:29
 */
@Data
public class DeliveryBillEntry {

    /**订单id**/
    private String orderId;
    /**送货日期**/
    private Date orderTime;
    /**配送单号**/
    private String DeliveryBillCode;
    /**订单编号**/
    private String orderCode;

    /**客户编码**/
    private String storeCode;
    /**客户名称**/
    private String storeName;
    /**配送线路**/
    private String LineName;
    /**送货员**/
    private String deliveryManName;

    public String getDeliveryBillCode() {
        if(null != orderTime && StringUtils.isNotEmpty(orderCode)){
            SimpleDateFormat formatter = new SimpleDateFormat("yyyyMMdd");
            String orderTimeStr = formatter.format(orderTime);
            String orderCodeStr = orderCode.substring(orderCode.length() - 6);
            return  "PS" + orderTimeStr + orderCodeStr;
        }
        return DeliveryBillCode;
    }
}
