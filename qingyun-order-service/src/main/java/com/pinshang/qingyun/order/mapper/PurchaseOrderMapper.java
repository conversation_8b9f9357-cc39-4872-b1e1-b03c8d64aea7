package com.pinshang.qingyun.order.mapper;

import com.pinshang.qingyun.base.mybatis.MyMapper;
import com.pinshang.qingyun.order.mapper.entry.order.generatePurchaseOrder.PurchaseOrder;
import com.pinshang.qingyun.order.mapper.entry.order.generatePurchaseOrder.SubOrderEntry;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2017/6/20.
 */
@Mapper
@Repository
public interface PurchaseOrderMapper extends MyMapper <PurchaseOrder> {

    List <String> queryCommodityIds(@Param("endTime") String endTime);

    List <SubOrderEntry> getSubOrderBeforeDeadline(@Param("endTime") String endTime);
    
    List <SubOrderEntry> getSubOrderBeforeDeadlineById(@Param("endTime") String endTime, @Param("supplierId") String supplierId);
    
    List <SubOrderEntry> getSubOrderByDateAndId(@Param("orderTime") String orderTime, @Param("supplierId") String supplierId);

    SubOrderEntry getSubOrderBeforeDeadlineByPreorderId(@Param("preOrderId") Long preOrderId);
}

