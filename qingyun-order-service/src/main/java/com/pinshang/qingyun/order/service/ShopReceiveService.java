package com.pinshang.qingyun.order.service;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.pinshang.qingyun.base.api.TokenInfo;
import com.pinshang.qingyun.base.configure.expand.QYAssert;
import com.pinshang.qingyun.base.enums.IogisticsModelEnums;
import com.pinshang.qingyun.base.enums.shop.ShopTypeEnums;
import com.pinshang.qingyun.base.enums.xd.StockInOutTypeEnums;
import com.pinshang.qingyun.base.enums.xd.StorageAreaEnum;
import com.pinshang.qingyun.base.util.FastThreadLocalUtil;
import com.pinshang.qingyun.box.utils.SpringUtil;
import com.pinshang.qingyun.box.utils.StringUtil;
import com.pinshang.qingyun.order.enums.PreOrderItemStatusEnums;
import com.pinshang.qingyun.order.enums.PreOrderReceiveStatusEnums;
import com.pinshang.qingyun.order.enums.ShopReceiveOrderStatusEnums;
import com.pinshang.qingyun.order.enums.ShopStockTypeEnums;
import com.pinshang.qingyun.order.mapper.*;
import com.pinshang.qingyun.order.mapper.entry.commodity.CommodityBasicEntry;
import com.pinshang.qingyun.order.mapper.entry.order.CommodityInfoEntry;
import com.pinshang.qingyun.order.mapper.entry.shop.*;
import com.pinshang.qingyun.order.model.order.*;
import com.pinshang.qingyun.order.model.shop.Shop;
import com.pinshang.qingyun.order.model.shop.ShopReceiveOrder;
import com.pinshang.qingyun.order.util.StallUtils;
import com.pinshang.qingyun.order.vo.ShopReceiveHQVo;
import com.pinshang.qingyun.order.vo.commodity.CommodityVO;
import com.pinshang.qingyun.order.vo.order.ShopReceiveOrderVo;
import com.pinshang.qingyun.order.vo.order.ShopStockCommodityVo;
import com.pinshang.qingyun.order.vo.shop.*;
import com.pinshang.qingyun.product.dto.CommodityRecommandPriceODTO;
import com.pinshang.qingyun.renderer.client.dto.DictionaryODTO;
import com.pinshang.qingyun.shop.dto.ShopDto;
import com.pinshang.qingyun.shop.service.CommodityServiceClient;
import com.pinshang.qingyun.shop.service.ShopClient;
import com.pinshang.qingyun.shop.service.ShopStockClient;
import com.pinshang.qingyun.shop.service.StorageServiceClient;
import com.pinshang.qingyun.supplier.dto.FullSupplierODTO;
import com.pinshang.qingyun.supplier.dto.QuerySupplierByIdsIDTO;
import com.pinshang.qingyun.supplier.dto.SupplierListODTO;
import com.pinshang.qingyun.supplier.dto.SupplierODTO;
import com.pinshang.qingyun.supplier.service.SupplierClient;
import com.pinshang.qingyun.xd.wms.dto.ShopShelfReqDTO;
import com.pinshang.qingyun.xd.wms.dto.ShopShelfResDTO;
import com.pinshang.qingyun.xd.wms.dto.StockReceiptIDTO;
import com.pinshang.qingyun.xd.wms.dto.StockReceiptItemDTO;
import com.pinshang.qingyun.xd.wms.dto.bigShop.DdStockInOutExtraIDTO;
import com.pinshang.qingyun.xd.wms.service.GrouponClient;
import com.pinshang.qingyun.xd.wms.service.XdStockClient;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;
import tk.mybatis.mapper.entity.Example;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static java.util.stream.Collectors.toList;

/**
 * Created by zhaoranguang on 2017/6/7.
 */
@Service
@Slf4j
public class ShopReceiveService {

    @Autowired
    ShopReceiveOrderMapper shopReceiveOrderMapper;

    @Autowired
    CommodityServiceClient commodityServiceClient;

    @Autowired
    StorageServiceClient storageServiceClient;

    @Autowired
    SubOrderItemMapper subOrderItemMapper;

    @Autowired
    ShopStockClient shopStockClient;

    @Autowired
    SubOrderMapper subOrderMapper;

    @Autowired
    ComplaintMapper complaintMapper;

    @Autowired
    ComplaintItemMapper complaintItemMapper;
    
    @Autowired
    PreOrderMapper preOrderMapper;
    
    @Autowired
    PreOrderItemMapper preOrderItemMapper;

    @Autowired
    private ShopClient shopClient;

    @Autowired
    SubOrder4DistributionMapper subOrder4DistributionMapper;

    @Autowired
    private SupplierClient supplierClient;

    @Autowired
    private XdStockClient xdStockClient;

    @Autowired
    private ShopMapper shopMapper;

    @Autowired
    private CommodityMapper commodityMapper;

    @Autowired
    private OrderMapper orderMapper;

    @Autowired
    private GrouponClient grouponClient;
    @Lazy
    @Autowired
    private OrderService orderService;
    @Autowired
    private UserMapper userMapper;
    @Autowired
    private ShopService shopService;
    @Autowired
    private CommonService commonService;
    @Autowired
    private ConsignmentSupplierService consignmentSupplierService;

    public PageInfo <ShopReceiveEntry> getListByCondition(ShopReceiveVo shopReceiveVo) {
        PageInfo <ShopReceiveEntry> pageDate = null ;
        if(StringUtils.isEmpty(shopReceiveVo.getBeginDate()) || StringUtils.isEmpty(shopReceiveVo.getEndDate()) ){
            QYAssert.isTrue(false, "送货日期不能为空");
        }
        if (!StringUtil.isBlank(shopReceiveVo.getBeginDate()) && !StringUtil.isBlank(shopReceiveVo.getEndDate())){
            shopReceiveVo.setBeginDate(shopReceiveVo.getBeginDate()+ " 00:00:00");
            shopReceiveVo.setEndDate(shopReceiveVo.getEndDate()+ " 23:59:59");
        }
        //根据供应商名称，code，aid查询
        if(!StringUtils.isEmpty(shopReceiveVo.getSupplierStr())){
            List<SupplierListODTO> supplyList=supplierClient.searchSupplierByParam(shopReceiveVo.getSupplierStr());
            if(CollectionUtils.isNotEmpty(supplyList)){
                List<Long> supplyIdList = supplyList.stream().map(p -> p.getId())
                        .collect(Collectors.toList());
                shopReceiveVo.setSupplyIdList(supplyIdList);
            }
        }

        TokenInfo tokenInfo = FastThreadLocalUtil.getQY();
        if(StallUtils.isStallSubcontractor(tokenInfo.getManagementMode())){
            List<Long> stallIdList = consignmentSupplierService.selectUserStallIdList(tokenInfo.getShopId());
            if(CollectionUtils.isEmpty(stallIdList)){
                return new PageInfo<>();
            }else {
                shopReceiveVo.setStallIdList(stallIdList);
            }
        }

        pageDate = PageHelper.startPage(shopReceiveVo.getPageNo(), shopReceiveVo.getPageSize()).doSelectPageInfo(() -> {
            shopReceiveOrderMapper.getListByCondition(shopReceiveVo);
        });
        List<ShopReceiveEntry>  list = pageDate.getList();
        if(CollectionUtils.isNotEmpty(list)){
            List<Long> createIdList = list.stream().map(item -> item.getCreateId()).collect(Collectors.toList());
            List<Long> receiveIdList = list.stream().map(item -> item.getReceiveUserId()).collect(Collectors.toList());
            createIdList.addAll(CollectionUtils.isNotEmpty(receiveIdList) ? receiveIdList : new ArrayList<>());
            List<User> userList = userMapper.getEmployeeUserByUserId(createIdList);
            Map<Long, User> userMap = userList.stream().collect(Collectors.toMap(User::getUserId, Function.identity()));

            Long storeId = list.get(0).getStoreId();
            Shop shop = shopService.getShopByStoreId(storeId);

            List<Long> stallIdList = list.stream().filter(p -> p.getStallId() != null && p.getStallId() > 0).collect(Collectors.toList())
                    .stream().map(item -> item.getStallId()).collect(Collectors.toList());
            Map<Long, String> stallIdAndNameMap = consignmentSupplierService.queryStallMapByIds(stallIdList);

            for(ShopReceiveEntry entry : list){
                entry.setShopType(shop.getShopType());

                User user1 = userMap.get(entry.getCreateId());
                if(user1 != null){
                    entry.setCreateName(user1.getEmployeeCode() + "_" + user1.getEmployeeName());
                }
                User user2 = userMap.get(entry.getReceiveUserId());
                if(user2 != null){
                    entry.setRealName(user2.getEmployeeCode() + "_" + user2.getEmployeeName());
                }
                entry.setStallName(stallIdAndNameMap.get(entry.getStallId()));
            }


        }
        setSupplyName(pageDate.getList());
        return pageDate;
    }

    /**
     * 收货页面数据展示
     * @param querySubOrderVo
     * @return
     * @throws Throwable
     */
    public ShopReceiveOrderInfoEntry showReceive(QuerySubOrderVo querySubOrderVo) throws Throwable {
        Integer logisticsModel = querySubOrderVo.getLogisticsModel();
        //String subOrderCode = querySubOrderVo.getSubOrderCode();
        Long subOrderId = querySubOrderVo.getSubOrderId();
        Integer shopType = querySubOrderVo.getShopType();
        if (logisticsModel == IogisticsModelEnums.DIRECT_CONNECTION.getCode() && !shopType.equals(ShopTypeEnums.XS.getCode()) && !shopType.equals(ShopTypeEnums.XJ.getCode())) {
            return processDirectConnection(subOrderId);
        } else if (logisticsModel == IogisticsModelEnums.DIRECT_SENDING.getCode()) {
            return processDirectSending(subOrderId);
        } else if (logisticsModel == IogisticsModelEnums.DISPATCHING.getCode()
                || (logisticsModel == IogisticsModelEnums.DIRECT_CONNECTION.getCode() && (shopType.equals(ShopTypeEnums.XS.getCode()) || shopType.equals(ShopTypeEnums.XJ.getCode())))) {
            return processDispatching(subOrderId);
        }
        return null;
    }

    /**
     * 根据 SubOrderId 获取订单
     * @param subOrderId
     * @return
     */
    private ShopReceiveOrderEntry getShopReceiveOrderEntryBySubOrderId(Long subOrderId){
        ShopReceiveOrderEntry shopReceiveOrderEntry = shopReceiveOrderMapper.getShopReceiveOrderEntryBySubOrderId(subOrderId);
        if(null != shopReceiveOrderEntry ){
            Long receiveUserId = shopReceiveOrderEntry.getReceiveUserId();
            List<Long> userIdList = new ArrayList<>();
            userIdList.add(receiveUserId);
            List<User> userList = userMapper.getEmployeeUserByUserId(userIdList);
            Map<Long, User> userMap = userList.stream().collect(Collectors.toMap(User::getUserId, Function.identity()));

            Long storeId = shopReceiveOrderEntry.getStoreId();
            Shop shop = shopService.getShopByStoreId(storeId);
            shopReceiveOrderEntry.setShopId(shop.getId() + "");
            shopReceiveOrderEntry.setShopName(shop.getShopName());

            User user = userMap.get(receiveUserId);
            if (user != null) {
                shopReceiveOrderEntry.setReceiveName(user.getEmployeeCode() + "_" + user.getEmployeeName());
            }
        }
        return shopReceiveOrderEntry;
    }

    /**
     * 根据 SubOrderId 获取订单明细
     * @param subOrderId
     * @return
     */
    private List<ShopReceiveOrderItemEntry> getShopReceiveItemListBySubOrderId(String subOrderId) {
        List<ShopReceiveOrderItemEntry> shopReceiveOrderItemEntries = shopReceiveOrderMapper.getShopReceiveOrderItemEntryBySubOrderId(subOrderId);
        if (CollectionUtils.isEmpty(shopReceiveOrderItemEntries)) {
            return shopReceiveOrderItemEntries;
        }

        List<Long> commodityIdList = shopReceiveOrderItemEntries.stream()
                .map(item -> Long.valueOf(item.getCommodityId()))
                .collect(Collectors.toList());

        CommodityVO vo = new CommodityVO();
        vo.setCommodityIdList(commodityIdList);
        List<CommodityBasicEntry> commodityBasicList = commodityMapper.findCommodityBasicListByParam(vo);
        Map<String, CommodityBasicEntry> commMap = commodityBasicList.stream().collect(Collectors.toMap(CommodityBasicEntry::getCommodityId, Function.identity()));

        Set<Long> dictionaryIdSet = new HashSet<>();
        for (CommodityBasicEntry commodityBasicEntry : commodityBasicList) {
            dictionaryIdSet.add(commodityBasicEntry.getCommodityUnitId());
        }
        Map<String, DictionaryODTO> dictionaryMap = commonService.listDictionarysByIdSet(dictionaryIdSet);

        List<CommodityBasicEntry> barCodeList = commodityMapper.findCommodityBarCodeByIds(commodityIdList);
        Map<String, String> barCodeMap = barCodeList.stream().collect(Collectors.toMap(CommodityBasicEntry::getCommodityId, CommodityBasicEntry::getBarCodes, (key1, key2) -> key2));

        for (ShopReceiveOrderItemEntry itemEntry : shopReceiveOrderItemEntries) {
            CommodityBasicEntry basicEntry = commMap.get(itemEntry.getCommodityId());
            if (basicEntry != null) {
                BeanUtils.copyProperties(basicEntry, itemEntry);
            }
            itemEntry.setBarCodes(barCodeMap.get(itemEntry.getCommodityId()));
            itemEntry.setUnit(null != dictionaryMap.get(itemEntry.getCommodityUnitId() + "") ? dictionaryMap.get(itemEntry.getCommodityUnitId() + "").getOptionName() : "");
        }

        return shopReceiveOrderItemEntries;
    }

    private ShopReceiveOrderInfoEntry processDirectConnection(Long subOrderId) throws Throwable {
    	ShopReceiveOrderInfoEntry shopReceiveOrderInfoEntry = new ShopReceiveOrderInfoEntry();
        ShopReceiveOrderEntry shopReceiveOrderEntry = getShopReceiveOrderEntryBySubOrderId(subOrderId);
        if(null != shopReceiveOrderEntry && !StringUtils.isEmpty(shopReceiveOrderEntry.getSupplierId())){
            SupplierODTO supplierODTO = supplierClient.getSupplierById(Long.valueOf(shopReceiveOrderEntry.getSupplierId()));
            shopReceiveOrderEntry.setSupplierName(supplierODTO != null ? supplierODTO.getSupplierName():"");
        }
        String id = shopReceiveOrderEntry.getId();

//        String storeId = orderServiceClient.findStoreIdBySubOrderCode(subOrderCode);
//        List <String> commodityIds = prepareCommodityIds(subOrderCode);
//        String orderTime = getOrderTimeBySubOrderCode(subOrderCode);
//        List <CommodityRecommandPriceODTO> commodityRecommandPriceODTOS = getCommodityRecommandPriceODTOS(storeId, commodityIds, orderTime);
        List <ShopReceiveOrderItemEntry> shopReceiveOrderItemEntries = getShopReceiveItemListBySubOrderId(id);

        if(shopReceiveOrderItemEntries !=null && !shopReceiveOrderItemEntries.isEmpty()){
	        for (ShopReceiveOrderItemEntry item : shopReceiveOrderItemEntries) {
	        	item.setRealReceiveQuantity(item.getRealDeliveryQuantity());
                if (null != item.getRealReceiveQuantity()) {
                    Integer number = item.getRealReceiveQuantity().divide(item.getCommodityPackageSpec(),0,BigDecimal.ROUND_UP).intValue();
                    item.setRealReceiveNumber(number);
                }
//	        	BigDecimal recommandPrice = getRecommandPrice(commodityRecommandPriceODTOS, item.getCommodityCode());
//	        	item.setRecommandPrice(recommandPrice.multiply(item.getPrice()));
	        }
        }
        shopReceiveOrderInfoEntry.setShopReceiveOrderEntry(shopReceiveOrderEntry);
        shopReceiveOrderInfoEntry.setShopReceiveOrderItemEntrys(shopReceiveOrderItemEntries);
        return shopReceiveOrderInfoEntry;

    }

    private ShopReceiveOrderInfoEntry processDirectSending(Long subOrderId) throws Throwable {
//        List <CommodityRecommandPriceODTO> commodityRecommandPriceODTOS = prepareData(subOrderCode);
        return processData(subOrderId, null);
    }


    private ShopReceiveOrderInfoEntry processDispatching(Long subOrderId) throws Throwable {
        //---开始---
        ShopReceiveOrderEntry shopReceiveOrderEntry = getShopReceiveOrderEntryBySubOrderId(subOrderId);
        if(null != shopReceiveOrderEntry && !StringUtils.isEmpty(shopReceiveOrderEntry.getSupplierId())){
            SupplierODTO supplierODTO = supplierClient.getSupplierById(Long.valueOf(shopReceiveOrderEntry.getSupplierId()));
            shopReceiveOrderEntry.setSupplierName(supplierODTO != null ? supplierODTO.getSupplierName():"");
        }
        String id = shopReceiveOrderEntry.getId();
        List <ShopReceiveOrderItemEntry> shopReceiveOrderItemEntrys = getShopReceiveItemListBySubOrderId(id);
//        List <String> commodityIds = shopReceiveOrderItemEntrys.stream().map(ShopReceiveOrderItemEntry::getCommodityId).collect(toList());
//        List <CommodityStockOutQuantityEntry> commodityStockOutQuantityEntries = getStockOutQuantity(subOrderCode, commodityIds);
        List <CommodityStockOutQuantityEntry> commodityStockOutQuantityEntries = null;
        ShopReceiveOrderInfoEntry shopReceiveOrderInfoEntry = new ShopReceiveOrderInfoEntry();
        List<ShopReceiveItemVo> updates =new ArrayList<ShopReceiveItemVo>();
        for (ShopReceiveOrderItemEntry item : shopReceiveOrderItemEntrys) {
            if (item.getRealDeliveryQuantity() == null) {
                if(commodityStockOutQuantityEntries == null) {
                    commodityStockOutQuantityEntries = getStockOutQuantity(subOrderId, null);
                }
            	ShopReceiveItemVo vo =new ShopReceiveItemVo();
                String commodityId = item.getCommodityId();
                BigDecimal stockOutQuantity = getStockOutQuantity(commodityStockOutQuantityEntries, commodityId);
                if(stockOutQuantity!=null&&item.getPrice()!=null){
                    item.setRealDeliveryQuantity(stockOutQuantity);
                    item.setRealReceiveQuantity(stockOutQuantity);
                    item.setTotalPrice(stockOutQuantity.multiply(item.getPrice()));
                    
                    vo.setRealDeliveryQuantity(stockOutQuantity);
                    vo.setSubOrderId(Long.valueOf(id));
                    vo.setCommodityId(Long.valueOf(item.getCommodityId()));
                    updates.add(vo);
                }
//                subOrderItemMapper.updateRealDeliveryQuantity(stockOutQuantity, id, item.getCommodityId());
            } else {
                item.setTotalPrice(item.getRealDeliveryQuantity().multiply(item.getPrice()));
            }
            //配送 配送的等于实收的
            if (null != item.getRealDeliveryQuantity()) {
                Integer number = item.getRealDeliveryQuantity().divide(item.getCommodityPackageSpec(),0, BigDecimal.ROUND_UP).intValue();
                item.setRealReceiveNumber(number);
            }
        }
        if(null !=updates && !updates.isEmpty()){
        	subOrderItemMapper.batchUpdate(updates);
    	}
        shopReceiveOrderInfoEntry.setShopReceiveOrderEntry(shopReceiveOrderEntry);
        shopReceiveOrderInfoEntry.setShopReceiveOrderItemEntrys(shopReceiveOrderItemEntrys);
        return shopReceiveOrderInfoEntry;
    }


    /**
     * 处理页面展示的时候直送和直通的数据
     * @param subOrderId
     * @param commodityRecommandPriceODTOS
     * @return
     */
    private ShopReceiveOrderInfoEntry processData(Long subOrderId, List <CommodityRecommandPriceODTO> commodityRecommandPriceODTOS) {
        ShopReceiveOrderInfoEntry shopReceiveOrderInfoEntry = new ShopReceiveOrderInfoEntry();
        ShopReceiveOrderEntry shopReceiveOrderEntry = getShopReceiveOrderEntryBySubOrderId(subOrderId);
        if(null != shopReceiveOrderEntry && !StringUtils.isEmpty(shopReceiveOrderEntry.getSupplierId())){
            SupplierODTO supplierODTO = supplierClient.getSupplierById(Long.valueOf(shopReceiveOrderEntry.getSupplierId()));
            shopReceiveOrderEntry.setSupplierName(supplierODTO != null ? supplierODTO.getSupplierName():"");
        }
        String id = shopReceiveOrderEntry.getId();
        List <ShopReceiveOrderItemEntry> shopReceiveOrderItemEntries = getShopReceiveItemListBySubOrderId(id);
//        for (ShopReceiveOrderItemEntry item : shopReceiveOrderItemEntries) {
//            BigDecimal recommandPrice = getRecommandPrice(commodityRecommandPriceODTOS, item.getCommodityCode());
////            initialData(item);
//            item.setRecommandPrice(recommandPrice.multiply(item.getPrice()));
//        }
        //sort shopReceiveOrderItemEntries
        List <ShopReceiveOrderItemEntry> entries = sortData(shopReceiveOrderItemEntries, shopReceiveOrderEntry.getStatus() == 2);

        shopReceiveOrderInfoEntry.setShopReceiveOrderEntry(shopReceiveOrderEntry);

        shopReceiveOrderInfoEntry.setShopReceiveOrderItemEntrys(entries);

        return shopReceiveOrderInfoEntry;

    }


    private List <ShopReceiveOrderItemEntry> sortData(List <ShopReceiveOrderItemEntry> shopReceiveOrderItemEntries, Boolean needSort) {
        if (needSort) {
            List <ShopReceiveOrderItemEntry> sorted =
                    shopReceiveOrderItemEntries.stream().map(e->{
                        if (null != e.getRealReceiveQuantity()) {
                            Integer number = e.getRealReceiveQuantity().divide(e.getCommodityPackageSpec(),0, BigDecimal.ROUND_UP).intValue();
                            e.setRealReceiveNumber(number);
                        }
                        return e;
                    }).sorted((o1, o2) -> {
                        return o1.getStatus() - o2.getStatus();
                    }).collect(toList());
            return sorted;
        } else {
            return shopReceiveOrderItemEntries.stream().map(e->{
                if (null != e.getRealReceiveQuantity()) {
                    Integer number = e.getRealReceiveQuantity().divide(e.getCommodityPackageSpec(),0, BigDecimal.ROUND_UP).intValue();
                    e.setRealReceiveNumber(number);
                }
                return e;
            }).collect(toList());
          //  return shopReceiveOrderItemEntries;
        }

    }


    /**
     * 查不到的就返回null
     *
     * @param commodityStockOutQuantityEntries
     * @param commodityId
     * @return
     */
    private BigDecimal getStockOutQuantity(List <CommodityStockOutQuantityEntry> commodityStockOutQuantityEntries, String commodityId) {
        return commodityStockOutQuantityEntries.stream().filter(c -> c.getCommodityId().equals(commodityId))
                .findFirst().map(CommodityStockOutQuantityEntry::getStockOutQuantity).orElse(null);
    }


    private List <CommodityStockOutQuantityEntry> getStockOutQuantity(Long subOrderId, List <String> commodityIds) {
        StockOutQuantityVo stockOutQuantityVo = new StockOutQuantityVo();
        //stockOutQuantityVo.setSubOrderCode(subOrderCode);
        stockOutQuantityVo.setSubOrderId(subOrderId);
        stockOutQuantityVo.setCommodityIds(commodityIds);
        return shopReceiveOrderMapper.getStockOutQuantityByCommodityIds(stockOutQuantityVo);
    }

    /**
     * 大店手动收货校验
     * @param receiveOrderVo
     */
    public void bigShopReveiveCheck(ReceiveOrderVo receiveOrderVo){
        Integer logisticsModel = receiveOrderVo.getLogisticsModel();
        if(logisticsModel == IogisticsModelEnums.DISPATCHING.getCode() || logisticsModel == IogisticsModelEnums.DIRECT_CONNECTION.getCode()){
            Long subOrderId = Long.valueOf(receiveOrderVo.getSubOrderId());

            Example subOrderExample =new Example(SubOrder.class);
            subOrderExample.createCriteria().andEqualTo("id", subOrderId);
            SubOrder subOrder = subOrderMapper.selectOneByExample(subOrderExample);


            Example orderExample =new Example(Order.class);
            orderExample.createCriteria().andEqualTo("id", subOrder.getOrderId());
            Order order = orderMapper.selectOneByExample(orderExample);

            if(order.getStallId() != null && order.getStallId() > 0){
                QYAssert.isFalse("大店不允许订单单独收货，请用大店收货单功能收货");
            }
        }
    }

    /**
     * 收货
     * @param receiveOrderVo
     * @return
     * @throws Throwable 
     * @throws NumberFormatException 
     */
    @Transactional(rollbackFor = Exception.class,propagation = Propagation.REQUIRES_NEW)
    public String addReceive(ReceiveOrderVo receiveOrderVo) throws NumberFormatException, Throwable {
        Integer logisticsModel = receiveOrderVo.getLogisticsModel();
//        boolean overStorage = false; //是否走大仓
        ShopStockVo shopStockVo =null;
        if (logisticsModel == IogisticsModelEnums.DIRECT_SENDING.getCode()) {
        	shopStockVo =  modifyStock(Long.valueOf(receiveOrderVo.getStoreId()), Long.valueOf(receiveOrderVo.getReceiveId()), receiveOrderVo.getSubOrderId(), receiveOrderVo.getReceiveQuantities());
            BigDecimal receiveTotalPrice =getTotalPriceBySubOrderId(logisticsModel, receiveOrderVo.getSubOrderId());
            updateSubOrderStatus(receiveOrderVo, ShopReceiveOrderStatusEnums.UNCHECKED,receiveTotalPrice);
        } else if (logisticsModel == IogisticsModelEnums.DISPATCHING.getCode()) {
        	checkReceiveOrderStatus(Long.valueOf(receiveOrderVo.getSubOrderId()));
//            overStorage = true;
            checkQuantity(receiveOrderVo.getReceiveQuantities());
            shopStockVo = modifyStock(Long.valueOf(receiveOrderVo.getStoreId()), Long.valueOf(receiveOrderVo.getReceiveId()), receiveOrderVo.getSubOrderId(),null);
//            BigDecimal deliveryTotalPrice =getTotalPriceBySubOrderId(logisticsModel, receiveOrderVo.getSubOrderId());
            updateSubOrderStatus(receiveOrderVo, ShopReceiveOrderStatusEnums.PASS,null);

        }else if (logisticsModel == IogisticsModelEnums.DIRECT_CONNECTION.getCode()) {
        	checkReceiveOrderStatus(Long.valueOf(receiveOrderVo.getSubOrderId()));
            ShopDto shop=shopClient.findShopById(receiveOrderVo.getShopId());
            //鲜食店的直通单 ，走大仓，需要发货
            if(shop.getShopType().equals(ShopTypeEnums.XS.getCode()) || shop.getShopType().equals(ShopTypeEnums.XJ.getCode())){
                checkQuantity(receiveOrderVo.getReceiveQuantities());
//                overStorage = true;
                shopStockVo =  modifyStock(Long.valueOf(receiveOrderVo.getStoreId()), Long.valueOf(receiveOrderVo.getReceiveId()), receiveOrderVo.getSubOrderId(), null);
            }else{
                shopStockVo =  modifyStock(Long.valueOf(receiveOrderVo.getStoreId()), Long.valueOf(receiveOrderVo.getReceiveId()), receiveOrderVo.getSubOrderId(), receiveOrderVo.getReceiveQuantities());
            }
//            BigDecimal receiveTotalPrice =getTotalPriceBySubOrderId(logisticsModel, receiveOrderVo.getSubOrderId());
            updateSubOrderStatus(receiveOrderVo, ShopReceiveOrderStatusEnums.PASS,null);
        } 
        if(null !=shopStockVo){
            // 如果所有实发数量为空就不调用入库操作
            List<ShopStockCommodityVo> filterList = shopStockVo.getList().stream().filter(p -> p.getModifyQuantity() == null || p.getModifyQuantity().compareTo(BigDecimal.ZERO) == 0).collect(Collectors.toList());
            if(filterList.size() != shopStockVo.getList().size()){
                // 调用鲜到入库操作(团购的走团购client)
                addStockReceipt(receiveOrderVo, shopStockVo);
            }
        }
        return "";

    }

    /**
     * 调用鲜到入库操作(团购的走团购client)
     * @param receiveOrderVo
     * @param shopStockVo
     */
    @Transactional(rollbackFor = Exception.class)
    public void addStockReceipt(ReceiveOrderVo receiveOrderVo, ShopStockVo shopStockVo) {
        Example shopEx = new Example(Shop.class);
        shopEx.createCriteria().andEqualTo("storeId", receiveOrderVo.getStoreId());
        Shop shop = shopMapper.selectByExample(shopEx).get(0);
        SubOrder subOrder = subOrderMapper.selectByPrimaryKey(Long.valueOf(receiveOrderVo.getSubOrderId()));
        StockReceiptIDTO stockReceiptIDTO = new StockReceiptIDTO();
        stockReceiptIDTO.setUserId(StringUtils.isEmpty(receiveOrderVo.getReceiveId()) ? 0L : Long.valueOf(receiveOrderVo.getReceiveId()));
        stockReceiptIDTO.setReferId(Long.valueOf(receiveOrderVo.getSubOrderId()));
        stockReceiptIDTO.setReferCode(null != subOrder ? subOrder.getSubOrderCode() : null);
        stockReceiptIDTO.setWarehouseId(shop.getId());
        // 查询商品包装规格
        List<String> commodityIdList = shopStockVo.getList().stream().map(item -> String.valueOf(item.getCommodityId())).collect(Collectors.toList());
        Map<Long, BigDecimal> commodityPackageSpecMap = getCommodityPackageSpecMap(commodityIdList);
        List<StockReceiptItemDTO> itemList = new ArrayList<>();
        for(ShopStockCommodityVo vo:shopStockVo.getList()){
            StockReceiptItemDTO receiptItem = new StockReceiptItemDTO();
            receiptItem.setCommodityId(vo.getCommodityId());
            receiptItem.setNormalQuantity(vo.getModifyQuantity());
            receiptItem.setAbnormalQuantity(BigDecimal.ZERO);
            receiptItem.setQuantity(receiptItem.getNormalQuantity().add(receiptItem.getAbnormalQuantity()));
            if (null != vo.getModifyNumber()) {
                receiptItem.setNormalNumber(vo.getModifyNumber());
            } else {
                BigDecimal commodityPackageSpec = commodityPackageSpecMap.get(vo.getCommodityId());
                receiptItem.setNormalNumber(vo.getModifyQuantity().divide(commodityPackageSpec,0,BigDecimal.ROUND_UP).intValue());
            }
            receiptItem.setAbnormalNumber(0);
            receiptItem.setNumber(receiptItem.getNormalNumber()+receiptItem.getAbnormalNumber());
            receiptItem.setPrice(vo.getPrice());
            receiptItem.setTotalPrice(receiptItem.getPrice().multiply(receiptItem.getQuantity()));
            itemList.add(receiptItem);
        }
        stockReceiptIDTO.setCommodityList(itemList);
        Order order = orderMapper.selectByPrimaryKey(subOrder.getOrderId());
        if("团购".equals(order.getOrderRemark())){
            grouponClient.groupStockReceipt(stockReceiptIDTO);
        }else {
            xdStockClient.stockReceipt(stockReceiptIDTO);
        }
    }

    /**
     * 获取商品包装规格
     * @param commodityIds
     * @return
     */
    public Map<Long,BigDecimal> getCommodityPackageSpecMap(List<String> commodityIds){
        List<CommodityInfoEntry> commodityInfoEntryList = commodityMapper.findCommodityInfo(commodityIds);
        return commodityInfoEntryList.stream().collect(Collectors.toMap(CommodityInfoEntry::getId,CommodityInfoEntry::getCommodityPackageSpec,(key1 , key2)-> key2));
    }
    public void checkReceiveOrderStatus(Long subOrderId){
    	Example ex =new Example(ShopReceiveOrder.class);
    	ex.createCriteria().andEqualTo("subOrderId", subOrderId);
    	List<ShopReceiveOrder> list =shopReceiveOrderMapper.selectByExample(ex);
    	QYAssert.isTrue(null !=list && !list.isEmpty(), "不存在该收货单");
    	QYAssert.isTrue(list.get(0).getStatus().intValue() ==ShopReceiveOrderStatusEnums.UNRECEIVED.getCode(), "该收货单状态不对");
    }

    /**
     * 直送预订单收货
     * @param receiveOrderVo
     * @return
     * @throws Throwable 
     */
    @Transactional(rollbackFor = Exception.class)
    public void addPreReceive(ReceiveOrderVo receiveOrderVo) throws Throwable {
        QYAssert.isTrue(SpringUtil.isNotEmpty(receiveOrderVo.getReceiveQuantities()), "收货列表不能为空");
        receiveOrderVo.getReceiveQuantities().forEach(item -> {
            QYAssert.isTrue(item.getRealReceiveQuantity() != null, "实收数量不能为空");
            QYAssert.isTrue(item.getRealReceiveNumber() != null, "实收份数不能为空");
        });
        Integer logisticsModel = receiveOrderVo.getLogisticsModel();
        if(null ==receiveOrderVo || null ==receiveOrderVo.getReceiveQuantities() || null ==receiveOrderVo.getShopId()){
            QYAssert.isTrue(false, "参数异常！");
        }
        if (logisticsModel == IogisticsModelEnums.DIRECT_SENDING.getCode()) {
//        	1)修改预订单状态及明细,预订单总金额
        	String preOrderCode = updatePreOrderInfo(receiveOrderVo);
//        	2)收货完成修改门店库存，审核时才生成出入库单;
        	updatePreOrderStock(receiveOrderVo,preOrderCode);
        }
    }
    
    /*
     * 预订单收货，修改门店商品库存
     */
    @Transactional(rollbackFor = Exception.class)
    public void updatePreOrderStock(ReceiveOrderVo receiveOrderVo,String preOrderCode) throws Throwable{
    	//Long shopId =receiveOrderVo.getShopId();
        PreOrder preOrder = preOrderMapper.selectByPrimaryKey(Long.valueOf(receiveOrderVo.getPreOrderId()));
        Boolean isBigShop = (preOrder.getStallId() != null && preOrder.getStallId() > 0);
    	List <Quantity> quantitys =receiveOrderVo.getReceiveQuantities();
        // 查询商品包装规格
        List<String> commodityIdList = quantitys.stream().map(item -> item.getCommodityId()).collect(Collectors.toList());
        Map<Long, BigDecimal> commodityPackageSpecMap = getCommodityPackageSpecMap(commodityIdList);

        List<StockReceiptItemDTO> commodityList = new ArrayList<>();
        for (Quantity vo : quantitys) {
            BigDecimal commodityPackageSpec = commodityPackageSpecMap.get(Long.valueOf(vo.getCommodityId()));
            StockReceiptItemDTO stockReceiptItemDTO = new StockReceiptItemDTO();
            stockReceiptItemDTO.setCommodityId(Long.valueOf(vo.getCommodityId()));
            stockReceiptItemDTO.setNumber(vo.getRealReceiveQuantity().divide(commodityPackageSpec,0,BigDecimal.ROUND_UP).intValue());
            stockReceiptItemDTO.setNormalNumber(stockReceiptItemDTO.getNumber());
            stockReceiptItemDTO.setAbnormalNumber(0);
            stockReceiptItemDTO.setQuantity(vo.getRealReceiveQuantity());
            stockReceiptItemDTO.setNormalQuantity(stockReceiptItemDTO.getQuantity());
            stockReceiptItemDTO.setAbnormalQuantity(new BigDecimal(0));
            stockReceiptItemDTO.setPrice(vo.getPrice());
            stockReceiptItemDTO.setTotalPrice(vo.getPrice().multiply(vo.getRealReceiveQuantity()));
            commodityList.add(stockReceiptItemDTO);

            if(isBigShop){
                DdStockInOutExtraIDTO ddStockInOutExtraIDTO = new DdStockInOutExtraIDTO();
                ddStockInOutExtraIDTO.setCommodityId(Long.valueOf(vo.getCommodityId()));
                ddStockInOutExtraIDTO.setStallId(preOrder.getStallId());
                ddStockInOutExtraIDTO.setStorageArea(StorageAreaEnum.SHELF_AREA.getCode());
                stockReceiptItemDTO.setDdStockInOutExtraVO(ddStockInOutExtraIDTO);
            }
        }

        //预订单收货走wms
        StockReceiptIDTO stockReceiptIDTO = new StockReceiptIDTO();
        stockReceiptIDTO.setWarehouseId(receiveOrderVo.getShopId());
        stockReceiptIDTO.setReferId(Long.valueOf(preOrderCode));
        stockReceiptIDTO.setReferCode(preOrderCode);
        stockReceiptIDTO.setUserId(Long.valueOf(receiveOrderVo.getReceiveId()));
        stockReceiptIDTO.setStockEnums(StockInOutTypeEnums.IN_PREORDER_NORMAL);
        stockReceiptIDTO.setCommodityList(commodityList);

        xdStockClient.stockReceipt(stockReceiptIDTO);
    }

    @Transactional(rollbackFor = Exception.class)
    public String updatePreOrderInfo(ReceiveOrderVo receiveOrderVo){
		String preOrderId =receiveOrderVo.getPreOrderId();
		PreOrder preOrder =preOrderMapper.selectByPrimaryKey(Long.valueOf(preOrderId));

		QYAssert.isTrue(null !=preOrder, "不存在该预订单");
		
		QYAssert.isTrue(preOrder.getReceiveStatus().intValue()==0 || preOrder.getReceiveStatus().intValue()==2, "该预订单状态不对");
		Example ex =new Example(PreOrderItem.class);
		ex.createCriteria().andEqualTo("preorderId", preOrderId);
		List<PreOrderItem> items =preOrderItemMapper.selectByExample(ex);
		Date now =new Date();
		//修改明细数据
		BigDecimal totalPrice =BigDecimal.ZERO;
		if(null !=items && !items.isEmpty() && !receiveOrderVo.getReceiveQuantities().isEmpty()){
            Long stallId = preOrder.getStallId();
            Boolean isBigShop = (stallId != null && stallId > 0);
            List<Long> stallCommodityIdList = new ArrayList<>();
            if(isBigShop){
                Long storeId = preOrder.getStoreId();
                Example shopEx =new Example(Shop.class);
                shopEx.createCriteria().andEqualTo("storeId", storeId);
                Shop shop = shopMapper.selectOneByExample(shopEx);
                stallCommodityIdList = consignmentSupplierService.getCommodityIdListByStallIds(shop.getId(), stallId);
            }

			List <Quantity> quantitys =receiveOrderVo.getReceiveQuantities();
            List<String> commodityIdList = quantitys.stream().map(item -> item.getCommodityId()).collect(Collectors.toList());
            Map<Long,BigDecimal> commMap = orderService.getCommodityPackageSpecMap(commodityIdList);
            List<PreOrderItem> adds =new ArrayList<PreOrderItem>();
			for(Quantity q: quantitys){
			    BigDecimal commodityPackageSpec = commMap.get(Long.valueOf(q.getCommodityId()));
				if(q.getIsAdd()){
					//新增商品
					PreOrderItem p =new PreOrderItem();
					p.setCommodityId(Long.valueOf(q.getCommodityId()));
					p.setCreateId(receiveOrderVo.getCreateId());
					p.setIsAdd(true);
					p.setPreorderId(Long.valueOf(preOrderId));
					p.setPrice(q.getPrice());
					p.setRealReceiveQuantity(q.getRealReceiveQuantity());
					//p.setRealReceiveNumber(q.getRealReceiveNumber());
                    p.setRealReceiveNumber(q.getRealReceiveQuantity().divide(commodityPackageSpec,0,BigDecimal.ROUND_UP).intValue());
					p.setRequireQuantity(q.getRealReceiveQuantity());
					p.setStatus(PreOrderItemStatusEnums.UNCHECK.getCode());
					p.setUpdateTime(now);
					p.setTotalPrice(q.getPrice().multiply(q.getRealReceiveQuantity()).setScale(2,BigDecimal.ROUND_HALF_UP));
					totalPrice =totalPrice.add(p.getTotalPrice());
					p.setUpdateId(receiveOrderVo.getCreateId());
					p.setType(1);
                    p.setReceiveQuantity(q.getRealReceiveQuantity());
					adds.add(p);

                    if(isBigShop && !stallCommodityIdList.contains(p.getCommodityId())){
                        QYAssert.isFalse("该档口下，不允许订此商品 " + q.getCommodityCode());
                    }
				}else{
					for(PreOrderItem item: items){
//						if(q.getCommodityId().equals(item.getCommodityId().toString())){
						if(q.getItemId().equals(item.getId().toString())){
							item.setRealReceiveQuantity(q.getRealReceiveQuantity());
                            //item.setRealReceiveNumber(q.getRealReceiveNumber());
                            item.setRealReceiveNumber(q.getRealReceiveQuantity().divide(commodityPackageSpec,0,BigDecimal.ROUND_UP).intValue());
							BigDecimal t =item.getPrice().multiply(item.getRealReceiveQuantity()).setScale(2,BigDecimal.ROUND_HALF_UP);
							item.setTotalPrice(t);
							//赠品不算总金额
							if(item.getType().intValue() !=2){
								totalPrice=totalPrice.add(t);
							}
							item.setUpdateId(receiveOrderVo.getCreateId());
							item.setUpdateTime(now);
                            item.setReceiveQuantity(q.getRealReceiveQuantity());
							preOrderItemMapper.updateByPrimaryKey(item);
						}
					}
				}
			}
			if(null !=adds && !adds.isEmpty()){
				preOrderItemMapper.insertList(adds);
			}
		}
		//修改预订单表
		preOrder.setReceiverId(null ==receiveOrderVo.getReceiveId()?0:Long.valueOf(receiveOrderVo.getReceiveId()));
		preOrder.setReceiverTime(receiveOrderVo.getReceiveTime()==null?now:receiveOrderVo.getReceiveTime());
		preOrder.setReceiveStatus(PreOrderReceiveStatusEnums.UNCHECKED.getCode());
		preOrder.setUpdateId(receiveOrderVo.getCreateId());
		preOrder.setTotalPrice(totalPrice);
        preOrder.setRemark(receiveOrderVo.getRemark());
        preOrder.setUpdateTime(now);
		preOrderMapper.updateByPrimaryKey(preOrder);
		return preOrder.getOrderCode();
    }
    
    private void checkQuantity(List<Quantity> receiveQuantities) {
       Boolean check =  receiveQuantities.stream().allMatch(quantity -> quantity.getRealDeliveryQuantity()!=null);
       QYAssert.isTrue(check,"有商品还未发货,不能收货。");
    }

    @Transactional(rollbackFor = Exception.class)
    public void updateSubOrderStatus(ReceiveOrderVo receiveOrderVo, ShopReceiveOrderStatusEnums enums,BigDecimal totalPrice) {
        String receiveId = receiveOrderVo.getReceiveId();
        Integer status = enums.getCode();
        Date receiveTime = receiveOrderVo.getReceiveTime();
        String remark = receiveOrderVo.getRemark();
        //配送 直通 不需要处理
        if (totalPrice != null){
            subOrderMapper.updateTotalPriceById(totalPrice, receiveOrderVo.getSubOrderId());
        }
        Integer i = shopReceiveOrderMapper.updateReceiveOrder(status, receiveTime, receiveId, remark ,receiveOrderVo.getSubOrderId());
        QYAssert.isTrue(i.intValue()>0, "更新收货单状态失败");
    }


    private ShopStockVo modifyStock(Long storeId, Long receiveId, String subOrderId, List <Quantity> quantities) throws Throwable {
        //不走大仓
        if(quantities != null){
//            Integer  logisticsModel = 0;
            ShopStockVo shopStockVo = new ShopStockVo();
            shopStockVo.setStoreId(storeId);
            shopStockVo.setUserId(receiveId);
            //List <ShopReceiveOrderItemEntry> list = shopReceiveOrderMapper.getShopReceiveOrderItemEntryBySubOrderCode(subOrderId);
            List <ShopStockCommodityVo> shopStockCommodityVos = new ArrayList <>();
            List <Quantity> receiveQuantities = quantities;
            for (Quantity vo : receiveQuantities) {
                String commodityId = vo.getCommodityId();
                BigDecimal newQuantity = vo.getRealReceiveQuantity() == null ? vo.getRealDeliveryQuantity() : vo.getRealReceiveQuantity();
                ShopStockCommodityVo shopStockCommodityVo = new ShopStockCommodityVo();

                // itemId不为空，根据主键更新
                if(!StringUtil.isNullOrEmpty(vo.getItemId())) {
                    SubOrderItem subOrderItem = new SubOrderItem();
                    subOrderItem.setId(Long.valueOf(vo.getItemId()));
                    subOrderItem.setRealReceiveQuantity(newQuantity);
                    subOrderItemMapper.updateByPrimaryKeySelective(subOrderItem);
                }else {
                    subOrderItemMapper.updateRealReceiveQuantity(newQuantity, subOrderId, commodityId);
                }
                //BigDecimal oldQuantity = getOldQuantity(list, vo.getCommodityId());
//                if (oldQuantity != null) {
//                    shopStockCommodityVo.setModifyQuantity(newQuantity.subtract(oldQuantity));
//                } else {
                    shopStockCommodityVo.setModifyQuantity(newQuantity);
//                }
                shopStockCommodityVo.setCommodityId(Long.valueOf(vo.getCommodityId()));
                //走大仓处理在途，不走大仓处理已定
//                if(overStorage){
//                    shopStockCommodityVo.setOnlineQuantity(BigDecimal.ZERO.subtract(newQuantity));
//                }else{
//                    shopStockCommodityVo.setOrderedQuantity(BigDecimal.ZERO.subtract(vo.getRequireQuantity()));
//                }
//                shopStockCommodityVo.setOrderedQuantity(BigDecimal.ZERO.subtract(vo.getRequireQuantity()));
                //加权价
                shopStockCommodityVo.setPrice(vo.getPrice());
                QYAssert.isTrue(newQuantity != null , "请刷新重试");
                shopStockCommodityVo.setTotalPrice(vo.getPrice().multiply(newQuantity));
                // ∆ quantity
                shopStockCommodityVos.add(shopStockCommodityVo);
            }

            shopStockVo.setList(shopStockCommodityVos);
            shopStockVo.setType(ShopStockTypeEnums.SHOP_RECEIVE.getCode());
            shopStockVo.setReferCode(subOrderId);
//            shopStockClient.modifyStock(shopStockVo);
            //修改suborder订单金额
//            return getTotalPriceBySubOrderId(logisticsModel,subOrderId);
            return shopStockVo;
        }else {
            //走大仓
//            Integer  logisticsModel = 1;
            ShopStockVo shopStockVo = new ShopStockVo();
            shopStockVo.setStoreId(storeId);
            shopStockVo.setUserId(receiveId);
            List <ShopStockCommodityVo> shopStockCommodityVos = new ArrayList <>();
            List <Quantity> deliveryQuantity = shopReceiveOrderMapper.getDeliveryQuantity(subOrderId);
            for (Quantity vo : deliveryQuantity) {
                BigDecimal newQuantity = vo.getRealDeliveryQuantity();
                ShopStockCommodityVo shopStockCommodityVo = new ShopStockCommodityVo();
                shopStockCommodityVo.setModifyQuantity(newQuantity);
                shopStockCommodityVo.setCommodityId(Long.valueOf(vo.getCommodityId()));
//                shopStockCommodityVo.setOnlineQuantity(BigDecimal.ZERO.subtract(newQuantity));
                //加权价
                shopStockCommodityVo.setPrice(vo.getPrice());
                shopStockCommodityVo.setTotalPrice(vo.getPrice().multiply(newQuantity));
                // ∆ quantity
                shopStockCommodityVos.add(shopStockCommodityVo);
            }
            shopStockVo.setList(shopStockCommodityVos);
            shopStockVo.setType(ShopStockTypeEnums.SHOP_RECEIVE.getCode());
            shopStockVo.setReferCode(subOrderId);
            return shopStockVo;
//            shopStockClient.modifyStock(shopStockVo);
//            return getTotalPriceBySubOrderId(logisticsModel,subOrderId);
        }
    }

    public BigDecimal getTotalPriceBySubOrderId(Integer logisticsModel ,String subOrderId) {

        return subOrderMapper.getTotalPriceBySubOrderId(logisticsModel,subOrderId);

    }


    public PageInfo <AuditInfoEntry> getAuditList(AuditQueryVo auditQueryVo) {
        PageInfo <AuditInfoEntry> pageDate = null ;
        if (!StringUtil.isBlank(auditQueryVo.getBeginDate()) && !StringUtil.isBlank(auditQueryVo.getEndDate())){
            auditQueryVo.setBeginDate(auditQueryVo.getBeginDate()+ " 00:00:00");
            auditQueryVo.setEndDate(auditQueryVo.getEndDate()+ " 23:59:59");
        }
        //根据供应商名称，code，aid查询
        if(!StringUtils.isEmpty(auditQueryVo.getSupplierStr())){
            List<SupplierListODTO> supplyList=supplierClient.searchSupplierByParam(auditQueryVo.getSupplierStr());
            if(CollectionUtils.isNotEmpty(supplyList)){
                List<Long> supplyIdList = supplyList.stream().map(p -> p.getId())
                        .collect(Collectors.toList());
                auditQueryVo.setSupplyIdList(supplyIdList);
            }
        }

        TokenInfo tokenInfo = FastThreadLocalUtil.getQY();
        if(StallUtils.isStallSubcontractor(tokenInfo.getManagementMode())){
            List<Long> stallIdList = consignmentSupplierService.selectUserStallIdList(tokenInfo.getShopId());
            if(CollectionUtils.isEmpty(stallIdList)){
                return new PageInfo<>();
            }else {
                auditQueryVo.setStallIdList(stallIdList);
            }
        }

        pageDate = PageHelper.startPage(auditQueryVo.getPageNo(), auditQueryVo.getPageSize()).doSelectPageInfo(() -> {
            shopReceiveOrderMapper.getAuditList(auditQueryVo);
        });
        List<AuditInfoEntry> list = pageDate.getList();
        if(CollectionUtils.isNotEmpty(list)){
            List<Long> stallIdList = list.stream().filter(p -> p.getStallId() != null && p.getStallId() > 0).collect(Collectors.toList())
                    .stream().map(item -> item.getStallId()).collect(Collectors.toList());;
            Map<Long, String> stallIdAndNameMap = consignmentSupplierService.queryStallMapByIds(stallIdList);

            List<Long> supplyIdList = new ArrayList<>();
            for(AuditInfoEntry entry:list){
                if(!StringUtils.isEmpty(entry.getSupplierId())){
                    supplyIdList.add(Long.valueOf(entry.getSupplierId()));
                }
                entry.setStallName(stallIdAndNameMap.get(entry.getStallId()));
            }
            if(CollectionUtils.isNotEmpty(supplyIdList)){
                QuerySupplierByIdsIDTO idto = new QuerySupplierByIdsIDTO();
                idto.setSupplierIds(supplyIdList);
                Map<Long,FullSupplierODTO> map = supplierClient.querySupplierByIds(idto);
                for(AuditInfoEntry entry:list){
                    if(!StringUtils.isEmpty(entry.getSupplierId())){
                        FullSupplierODTO supply = map.get(Long.valueOf(entry.getSupplierId()));
                        if(null != supply){
                            entry.setSupplierName(supply.getSupplierName());
                        }
                    }
                }
            }
        }
        return pageDate;
    }

    public ReceiveAuditEntry getAuditDetail(AuditDetailVo auditDetailVo) {
        ReceiveAuditEntry receiveAuditEntry = new ReceiveAuditEntry();
        ReceiveAuditHeaderEntry receiveAuditHeaderEntry = new ReceiveAuditHeaderEntry();
        ShopReceiveOrderEntry shopReceiveOrderEntry = shopReceiveOrderMapper.getShopReceiveOrderEntryBySubOrderCode(auditDetailVo.getSubOrderCode());
        if(null != shopReceiveOrderEntry && !StringUtils.isEmpty(shopReceiveOrderEntry.getSupplierId())){
            Long receiveUserId = shopReceiveOrderEntry.getReceiveUserId();
            List<Long> userIdList = new ArrayList<>();
            userIdList.add(receiveUserId);
            List<User> userList = userMapper.getEmployeeUserByUserId(userIdList);
            Map<Long, User> userMap = userList.stream().collect(Collectors.toMap(User::getUserId, Function.identity()));

            Long storeId = shopReceiveOrderEntry.getStoreId();
            Shop shop = shopService.getShopByStoreId(storeId);
            shopReceiveOrderEntry.setShopId(shop.getId() + "");
            shopReceiveOrderEntry.setShopName(shop.getShopName());

            User user = userMap.get(receiveUserId);
            if (user != null) {
                shopReceiveOrderEntry.setReceiveName(user.getEmployeeName());
            }

            SupplierODTO supplierODTO = supplierClient.getSupplierById(Long.valueOf(shopReceiveOrderEntry.getSupplierId()));
            shopReceiveOrderEntry.setSupplierName(supplierODTO != null ? supplierODTO.getSupplierName():"");
        }
        BeanUtils.copyProperties(shopReceiveOrderEntry, receiveAuditHeaderEntry);
        String id = shopReceiveOrderEntry.getId();
        List <ShopReceiveOrderItemEntry> shopReceiveOrderItemEntries =
                getShopReceiveItemListBySubOrderId(id);
        List <ReceiveAuditBodyEntry> receiveAuditBodyEntries = null;
        if (receiveAuditHeaderEntry.getStatus() == ShopReceiveOrderStatusEnums.REJECT.getCode()) {
            receiveAuditBodyEntries =
                    shopReceiveOrderItemEntries.stream().map(
                            e -> {
                                ReceiveAuditBodyEntry receiveAuditBodyEntry = new ReceiveAuditBodyEntry();
                                BeanUtils.copyProperties(e, receiveAuditBodyEntry);
                                return receiveAuditBodyEntry;
                            }
                    ).sorted((e1, e2) -> {
                        return e1.getStatus() - e2.getStatus();
                    }).collect(toList());
        } else {
            receiveAuditBodyEntries =
                    shopReceiveOrderItemEntries.stream().map(
                            e -> {
                                ReceiveAuditBodyEntry receiveAuditBodyEntry = new ReceiveAuditBodyEntry();
                                BeanUtils.copyProperties(e, receiveAuditBodyEntry);
                                return receiveAuditBodyEntry;
                            }
                    ).collect(toList());
        }

        receiveAuditEntry.setReceiveAuditHeaderEntry(receiveAuditHeaderEntry);
        receiveAuditEntry.setReceiveAuditBodyrEntryList(receiveAuditBodyEntries);
        return receiveAuditEntry;
    }

    @Transactional(rollbackFor = Exception.class)
    public void saveAduit(AuditSaveVo auditSaveVo) throws Throwable {
        List <AuditItem> auditItems = auditSaveVo.getAuditItems();
        String subOrderId = auditSaveVo.getSubOrderId();
        Boolean pass = auditItems.parallelStream().map(AuditItem::getIsPass).allMatch((e) -> e == 1);
        if (pass) {
            subOrderItemMapper.updateStatus(subOrderId, ShopReceiveOrderStatusEnums.PASS.getCode());
            //只有直送生成入库 和 出库单
            if (auditSaveVo.getLogisticsModel() == IogisticsModelEnums.DIRECT_SENDING.getCode()) {
                generateStockIn(subOrderId);
                generateStockOut(subOrderId);
            }

            //直送自动生成投诉单
//            if (auditSaveVo.getLogisticsModel() == IogisticsModelEnums.DIRECT_SENDING.getCode()) {
//            	autoCreateComplaint(subOrderId);
//            }
        } else {
            subOrderItemMapper.updateStatus(subOrderId, ShopReceiveOrderStatusEnums.REJECT.getCode());
        }
        for (AuditItem auditItem : auditItems) {
            subOrderItemMapper.updateAuditStatus(auditItem);
        }


    }

    public static String getUUID(){
        UUID uuid=UUID.randomUUID();
        String str = uuid.toString();
        String uuidStr ="md" +str.replace("-", "");
        return uuidStr;
      }

    private void generateStockOut(String subOrderId) throws Throwable {
        storageServiceClient.addStockOutBySubOrderId(subOrderId);
    }

    private void generateStockIn(String subOrderId) throws Throwable {

        storageServiceClient.addStockInBySubOrderId(subOrderId);
    }


    @Transactional(rollbackFor = Exception.class)
    public void newReceiveOrder(ShopReceiveOrderVo shopReceiveOrderVo) {
        ShopReceiveOrder s = new ShopReceiveOrder();
        SpringUtil.copyProperties(shopReceiveOrderVo, s);
        Calendar c = Calendar.getInstance();
        s.setCreateTime(c.getTime());
        s.setReceiveId(shopReceiveOrderVo.getCreateId());
        c.add(Calendar.DAY_OF_YEAR, 2);
        s.setReceiveTime(c.getTime());
        s.setStatus(ShopReceiveOrderStatusEnums.UNRECEIVED.getCode());
        s.setUpdateId(shopReceiveOrderVo.getCreateId());
        shopReceiveOrderMapper.insert(s);
    }


    public String getShopName(String storeId) {
        return  subOrderItemMapper.getShopName(storeId);
    }

    @Transactional(rollbackFor = Exception.class)
    public void cancelReceiveOrder(ShopReceiveOrderVo shopReceiveOrderVo){
    	if(null !=shopReceiveOrderVo && null !=shopReceiveOrderVo.getSubOrderIds() &&  !shopReceiveOrderVo.getSubOrderIds().isEmpty()){
    		ShopReceiveOrder o =new ShopReceiveOrder();
    		o.setStatus(4);
    		o.setUpdateId(-1L);
    		for(Long subOrderId :shopReceiveOrderVo.getSubOrderIds()){
    			Example ex =new Example(ShopReceiveOrder.class);
    			ex.createCriteria().andEqualTo("subOrderId", subOrderId);
    			shopReceiveOrderMapper.updateByExampleSelective(o, ex);
    		}
    	}
    }

    public PageInfo <ShopReceiveEntry> getPreOrderListByCondition(ShopReceiveVo shopReceiveVo) {
        if (!StringUtil.isBlank(shopReceiveVo.getBeginDate()) && !StringUtil.isBlank(shopReceiveVo.getEndDate())){
            shopReceiveVo.setBeginDate(shopReceiveVo.getBeginDate()+ " 00:00:00");
            shopReceiveVo.setEndDate(shopReceiveVo.getEndDate()+ " 23:59:59");
        }

        TokenInfo tokenInfo = FastThreadLocalUtil.getQY();
        if(StallUtils.isStallSubcontractor(tokenInfo.getManagementMode())){
            List<Long> stallIdList = consignmentSupplierService.selectUserStallIdList(tokenInfo.getShopId());
            if(CollectionUtils.isEmpty(stallIdList)){
                return new PageInfo<>();
            }else {
                shopReceiveVo.setStallIdList(stallIdList);
            }
        }

        PageInfo <ShopReceiveEntry> pageDate = null ;
        pageDate = PageHelper.startPage(shopReceiveVo.getPageNo(), shopReceiveVo.getPageSize()).doSelectPageInfo(() -> {
            shopReceiveOrderMapper.getPreOrderListByCondition(shopReceiveVo);
        });

        List<ShopReceiveEntry> list = pageDate.getList();
        if(CollectionUtils.isNotEmpty(list)){
            List<Long> createIdList = list.stream().map(item -> item.getCreateId()).collect(Collectors.toList());
            List<Long> receiveIdList = list.stream().map(item -> item.getReceiveUserId()).collect(Collectors.toList());
            createIdList.addAll(CollectionUtils.isNotEmpty(receiveIdList) ? receiveIdList : new ArrayList<>());
            List<User> userList = userMapper.getEmployeeUserByUserId(createIdList);
            Map<Long, User> userMap = userList.stream().collect(Collectors.toMap(User::getUserId, Function.identity()));

            List<Long> stallIdList = list.stream().filter(p -> p.getStallId() != null && p.getStallId() > 0).collect(Collectors.toList())
                    .stream().map(item -> item.getStallId()).collect(Collectors.toList());
            Map<Long, String> stallIdAndNameMap = consignmentSupplierService.queryStallMapByIds(stallIdList);

            for(ShopReceiveEntry entry : list){
                User user1 = userMap.get(entry.getCreateId());
                if(user1 != null){
                    entry.setCreateName(user1.getEmployeeCode() + "_" + user1.getEmployeeName());
                }
                User user2 = userMap.get(entry.getReceiveUserId());
                if(user2 != null){
                    entry.setRealName(user2.getEmployeeCode() + "_" + user2.getEmployeeName());
                }
                entry.setStallName(stallIdAndNameMap.get(entry.getStallId()));
            }

        }
        setSupplyName(pageDate.getList());
        return pageDate;
    }
    public void setSupplyName(List<ShopReceiveEntry> list){
        if(CollectionUtils.isNotEmpty(list)){
            List<Long> supplyIdList = new ArrayList<>();
            for(ShopReceiveEntry entry:list){
                if(!StringUtils.isEmpty(entry.getSupplierId())){
                    supplyIdList.add(Long.valueOf(entry.getSupplierId()));
                }
            }
            if(CollectionUtils.isNotEmpty(supplyIdList)){
                QuerySupplierByIdsIDTO idto = new QuerySupplierByIdsIDTO();
                idto.setSupplierIds(supplyIdList);
                Map<Long,FullSupplierODTO> map = supplierClient.querySupplierByIds(idto);
                for(ShopReceiveEntry entry:list){
                    if(!StringUtils.isEmpty(entry.getSupplierId())){
                        FullSupplierODTO supply = map.get(Long.valueOf(entry.getSupplierId()));
                        if(null != supply){
                            entry.setSupplierName(supply.getSupplierName());
                        }
                    }
                }
            }
        }
    }
    /**
     * 预订单收货列表(总部)
     * @param shopReceiveHQVo
     * @return
     */
    public PageInfo <ShopReceiveHQEntry> getPreOrderHQListByCondition(ShopReceiveHQVo shopReceiveHQVo) {
        if (!StringUtil.isBlank(shopReceiveHQVo.getBeginDate()) && !StringUtil.isBlank(shopReceiveHQVo.getEndDate())){
            shopReceiveHQVo.setBeginDate(shopReceiveHQVo.getBeginDate()+ " 00:00:00");
            shopReceiveHQVo.setEndDate(shopReceiveHQVo.getEndDate()+ " 23:59:59");
        }
        PageInfo <ShopReceiveHQEntry> pageDate = null ;
        pageDate = PageHelper.startPage(shopReceiveHQVo.getPageNo(), shopReceiveHQVo.getPageSize()).doSelectPageInfo(() -> {
            shopReceiveOrderMapper.getPreOrderHQListByCondition(shopReceiveHQVo);
        });
        List<ShopReceiveHQEntry> list = pageDate.getList();
        if(CollectionUtils.isNotEmpty(list)){
            List<Long> supplyIdList = new ArrayList<>();
            for(ShopReceiveHQEntry entry:list){
                if(!StringUtils.isEmpty(entry.getSupplierId())){
                    supplyIdList.add(Long.valueOf(entry.getSupplierId()));
                }
            }
            if(CollectionUtils.isNotEmpty(supplyIdList)){
                QuerySupplierByIdsIDTO idto = new QuerySupplierByIdsIDTO();
                idto.setSupplierIds(supplyIdList);
                Map<Long,FullSupplierODTO> map = supplierClient.querySupplierByIds(idto);
                for(ShopReceiveHQEntry entry:list){
                    if(!StringUtils.isEmpty(entry.getSupplierId())){
                        FullSupplierODTO supply = map.get(Long.valueOf(entry.getSupplierId()));
                        if(null != supply){
                            entry.setSupplierName(supply.getSupplierName());
                        }
                    }
                }
            }
        }
        return pageDate;
    }
    /**
     * 收货页面数据展示
     * @param querySubOrderVo
     * @return
     * @throws Throwable
     */
    public ShopReceiveOrderInfoEntry showPreOrderReceive(QuerySubOrderVo querySubOrderVo) throws Throwable {
        Integer logisticsModel = querySubOrderVo.getLogisticsModel();
        String orderCode = querySubOrderVo.getSubOrderCode();
        if (logisticsModel == IogisticsModelEnums.DIRECT_CONNECTION.getCode()) {
            //
        } else if (logisticsModel == IogisticsModelEnums.DIRECT_SENDING.getCode()) {
            //return processDirectSending(subOrderCode);
            //主预订单
            ShopReceiveOrderEntry shopReceiveOrder = shopReceiveOrderMapper.getPreOrderByCode(orderCode);
            if(null != shopReceiveOrder && !StringUtils.isEmpty(shopReceiveOrder.getSupplierId())){
                SupplierODTO supplierODTO = supplierClient.getSupplierById(Long.valueOf(shopReceiveOrder.getSupplierId()));
                shopReceiveOrder.setSupplierName(supplierODTO != null ? supplierODTO.getSupplierName():"");
            }
            //预订单明细
            List<ShopReceiveOrderItemEntry> shopReceiveOrderItems = shopReceiveOrderMapper.getPreOrderItemByCode(shopReceiveOrder.getId());
            //零售价系数
//            List<String> commodityIds = shopReceiveOrderItems.stream().map(ShopReceiveOrderItemEntry::getCommodityId).collect(toList());
//            CommodityRatioQueryIDTO commodityRatioQueryIDTO = new CommodityRatioQueryIDTO();
//            commodityRatioQueryIDTO.setStoreId(String.valueOf(shopReceiveOrder.getStoreId()));
//            commodityRatioQueryIDTO.setCommodityIds(commodityIds);
//            List <RatioInfoEntryODTO> ratioList = commodityServiceClient.getRatioByIds(commodityRatioQueryIDTO);
//            if(null !=ratioList && !ratioList.isEmpty()){
              if(null !=shopReceiveOrderItems && !shopReceiveOrderItems.isEmpty()){
            	for (ShopReceiveOrderItemEntry orderItem : shopReceiveOrderItems) {
                    orderItem.setRecommandPrice(orderItem.getPrice());
//                    if (null != orderItem.getRealReceiveQuantity()) {
//                        Integer number = orderItem.getRealReceiveQuantity().divide(orderItem.getCommodityPackageSpec()).setScale(0, BigDecimal.ROUND_UP).intValue();
//                        orderItem.setRealReceiveNumber(number);
//                    }
            	}
              }
//            }

            //组装
            ShopReceiveOrderInfoEntry order = new ShopReceiveOrderInfoEntry();
            order.setShopReceiveOrderEntry(shopReceiveOrder);
            order.setShopReceiveOrderItemEntrys(shopReceiveOrderItems);
            return order;
        }
        return null;
    }


    public List<SubOrder4DistributionEntry> autoReceiveByDirstributionOk(List<Long> subOrderIds){
        return subOrder4DistributionMapper.find4DistributionOk(subOrderIds);
    }


    /**
     * 查询收货页面(鲜道前置仓)
     * @param querySubOrderVo
     * @return
     * @throws Throwable
     */
    public ShopReceiveOrderInfoEntry xdShowReceive(QuerySubOrderVo querySubOrderVo) throws Throwable {
        Long subOrderId = querySubOrderVo.getSubOrderId();

        ShopReceiveOrderEntry shopReceiveOrderEntry = getShopReceiveOrderEntryBySubOrderId(subOrderId);
        if(null != shopReceiveOrderEntry && !StringUtils.isEmpty(shopReceiveOrderEntry.getSupplierId())){
            SupplierODTO supplierODTO = supplierClient.getSupplierById(Long.valueOf(shopReceiveOrderEntry.getSupplierId()));
            shopReceiveOrderEntry.setSupplierName(supplierODTO != null ? supplierODTO.getSupplierName():"");
        }
        String id = shopReceiveOrderEntry.getId();
        List <ShopReceiveOrderItemEntry> shopReceiveOrderItemEntrys = getShopReceiveItemListBySubOrderId(id);

        ShopReceiveOrderInfoEntry shopReceiveOrderInfoEntry = new ShopReceiveOrderInfoEntry();

        // 调用鲜道接口查询商品的货位
        List<Long> commodityIdList = shopReceiveOrderItemEntrys.stream().map(item -> Long.valueOf(item.getCommodityId())).collect(Collectors.toList());
        // 获取商品货位
        Map<Long,String> shelfMap = getShopShelf(querySubOrderVo.getShopId(),commodityIdList);
        for (ShopReceiveOrderItemEntry item : shopReceiveOrderItemEntrys) {
            if(!StringUtils.isEmpty(item.getBarCodes())){
                String[] barCode = item.getBarCodes().trim().split(",");
                item.setBarCode(barCode[0]);
                List barCodeList = java.util.Arrays.asList(barCode);
                item.setBarCodeList(barCodeList);
            }
            // 数量转换份数展示给前端
            item.setQuantity(item.getQuantity().divide(item.getCommodityPackageSpec(),0, RoundingMode.UP));
            if(item.getRealDeliveryQuantity() != null){
                item.setNormalQuantity(item.getRealDeliveryQuantity());
                item.setRealDeliveryQuantity(item.getRealDeliveryQuantity().divide(item.getCommodityPackageSpec(),0, RoundingMode.UP));
                item.setNormalShares(item.getRealDeliveryQuantity());
            }
            // 设置货位号
            item.setShelfNo(shelfMap.get(Long.valueOf(item.getCommodityId())));

        }
        shopReceiveOrderInfoEntry.setShopReceiveOrderEntry(shopReceiveOrderEntry);
        shopReceiveOrderInfoEntry.setShopReceiveOrderItemEntrys(shopReceiveOrderItemEntrys);
        return shopReceiveOrderInfoEntry;
    }



    /**
     * 获取仓库商品货位
     * @return
     */
    public Map<Long,String> getShopShelf(Long shopId,List<Long> commodityList){
        Map<Long,String> shelfMap = new HashMap<>();
        ShopShelfReqDTO req = new ShopShelfReqDTO();
        req.setShopId(shopId);
        req.setCommdityIdList(commodityList);
        List<ShopShelfResDTO> list = xdStockClient.queryShopShelf(req);
        if(CollectionUtils.isNotEmpty(list)){
            shelfMap = list.stream().collect(Collectors.toMap(ShopShelfResDTO::getCommodityId,ShopShelfResDTO::getShelfNo,(key1 , key2)-> key2));
        }
        return shelfMap;
    }

    /**
     * 收货操作(鲜道前置仓)
     * @param receiveOrderVo
     * @return
     * @throws Throwable
     */
    @Transactional(rollbackFor = Exception.class)
    public void addXdReceive(ReceiveOrderVo receiveOrderVo) throws Throwable {
        // 检查订单状态，必须为待收货状态
        checkReceiveOrderStatus(Long.valueOf(receiveOrderVo.getSubOrderId()));

        // 检查realDeliveryQuantity  为空表示没有发货
        String id = receiveOrderVo.getSubOrderId();
        List <ShopReceiveOrderItemEntry> shopReceiveOrderItemEntrys = getShopReceiveItemListBySubOrderId(id);
        Boolean check =  shopReceiveOrderItemEntrys.stream().allMatch(quantity -> quantity.getRealDeliveryQuantity()!=null);
        QYAssert.isTrue(check,"有商品还未发货,不能收货。");


        // 检查商品是否绑定货位
        // 调用鲜道接口查询商品的货位
        List<Long> commodityIdList = receiveOrderVo.getReceiveQuantities().stream().map(item -> Long.valueOf(item.getCommodityId())).collect(Collectors.toList());
        Map<Long,String> shelfMap = getShopShelf(receiveOrderVo.getShopId(),commodityIdList);
        for(Quantity vo : receiveOrderVo.getReceiveQuantities()){
            if(shelfMap.get(Long.valueOf(vo.getCommodityId())) == null){
                QYAssert.isTrue(false,"商品"+vo.getCommodityCode()+"-"+vo.getCommodityName()+"未绑定货位不能完成收货");
            }
        }
        // 更新收货单状态
        updateSubOrderStatus(receiveOrderVo, ShopReceiveOrderStatusEnums.PASS,null);

        //调xd的收货入库操作
        StockReceiptIDTO stockReceiptIDTO = new StockReceiptIDTO();
        stockReceiptIDTO.setUserId(receiveOrderVo.getCreateId());
        stockReceiptIDTO.setReferId(Long.valueOf(receiveOrderVo.getSubOrderId()));
        stockReceiptIDTO.setReferCode(receiveOrderVo.getSubOrderCode());
        stockReceiptIDTO.setWarehouseId(receiveOrderVo.getShopId());
        List<StockReceiptItemDTO> itemList = new ArrayList<>();
        for(Quantity vo : receiveOrderVo.getReceiveQuantities()){
            StockReceiptItemDTO receiptItem = new StockReceiptItemDTO();
            receiptItem.setCommodityId(Long.valueOf(vo.getCommodityId()));
            receiptItem.setNormalQuantity(vo.getNormalQuantity());
            receiptItem.setAbnormalQuantity(vo.getAbnormalQuantity() == null ? BigDecimal.ZERO : vo.getAbnormalQuantity());
            receiptItem.setQuantity(receiptItem.getNormalQuantity().add(receiptItem.getAbnormalQuantity()));

            receiptItem.setNormalNumber(vo.getNormalShares().intValue());
            receiptItem.setAbnormalNumber(vo.getAbnormalShares() == null ? 0 : vo.getAbnormalShares().intValue());
            receiptItem.setNumber(receiptItem.getNormalNumber()+receiptItem.getAbnormalNumber());

            receiptItem.setPrice(vo.getPrice());
            receiptItem.setTotalPrice(receiptItem.getPrice().multiply(receiptItem.getQuantity()));

            // 鲜道直接更新实际收货数量
            subOrderItemMapper.updateRealReceiveQuantity(receiptItem.getQuantity(), receiveOrderVo.getSubOrderId(), vo.getCommodityId());

            if(receiptItem.getQuantity().compareTo(BigDecimal.ZERO) == 0){
                continue;
            }
            itemList.add(receiptItem);

        }
        stockReceiptIDTO.setCommodityList(itemList);
        if(SpringUtil.isNotEmpty(itemList)){
            xdStockClient.stockReceipt(stockReceiptIDTO);
        }
    }

    /**
     * 商品组配送统计已生成采购单的预订单
     * @param vo
     * @return
     */
    public List<TjPreOrderEntry> queryTjPreOrderList(TjPreOrderVO vo){
        return shopReceiveOrderMapper.queryTjPreOrderList(vo);
    }


    /**
     * 根据 subOrderIdList 查询收货订单列表
     * @param subOrderIdList
     */
    @Transactional
    public List<ReceiveOrderVo> queryAutoReceiveOrderList(List<Long> subOrderIdList, List<Long> xsjmStoreIdList){
        return shopReceiveOrderMapper.getAutoReceiveOrderBySubOrderIdList(subOrderIdList, xsjmStoreIdList);
    }
}
