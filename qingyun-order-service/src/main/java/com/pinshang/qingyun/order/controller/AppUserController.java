package com.pinshang.qingyun.order.controller;

import com.pinshang.qingyun.order.service.AppUserService;
import com.pinshang.qingyun.order.vo.user.AppUserInfo;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import com.pinshang.qingyun.base.configure.expand.QYAssert;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @Date 2018/10/10 10:08
 */
@RestController
@RequestMapping("/storeAccount")
public class AppUserController {

    @Autowired
    private AppUserService appUserService;

    @PostMapping("/openAccount")
    public Boolean openAccount(@RequestBody AppUserInfo info){
        check4OpenAccount(info);
        return appUserService.openAccount(info);
    }

    @PostMapping("/editPassword")
    public Boolean editPassword(@RequestBody AppUserInfo info){
        check4EditPassword(info);
        return appUserService.editUser(info,false);
    }

    private void check4OpenAccount(AppUserInfo info){
        QYAssert.isTrue(info !=null ,"开户操作参数为空异常!");
        QYAssert.isTrue(StringUtils.isNotBlank(info.getStoreCode()),"开户操作商户码为空异常!");
        QYAssert.isTrue(StringUtils.isNotBlank(info.getPassword()),"开户操作商户密码为空异常!");
        QYAssert.isTrue(info.getCreatorId()!=null && info.getCreatorId()>0,"开户操作操作者id为空/异常!");
        QYAssert.isTrue(info.getStoreId()!=null && info.getStoreId()>0,"开户操作商户id为空/异常!");
    }

    private void check4EditPassword(AppUserInfo info){
        QYAssert.isTrue(StringUtils.isNotBlank(info.getStoreCode()),"修改密码操作商户码为空异常!");
        QYAssert.isTrue(StringUtils.isNotBlank(info.getPassword()),"修改密码操作商户密码为空异常!");
    }

}
