package com.pinshang.qingyun.order.service.syncOrderJob;

import com.pinshang.qingyun.base.api.QYApplicationContext;
import com.pinshang.qingyun.base.enums.DeliveryOrderTypeEnums;
import com.pinshang.qingyun.base.enums.SubOrderStatusEnums;
import com.pinshang.qingyun.box.utils.DateUtil;
import com.pinshang.qingyun.box.utils.SpringUtil;
import com.pinshang.qingyun.order.constant.DictionaryCodeConstant;
import com.pinshang.qingyun.order.constant.LockKeyConstant;
import com.pinshang.qingyun.order.mapper.SubOrderConfirmMapper;
import com.pinshang.qingyun.order.mapper.SubOrderMapper;
import com.pinshang.qingyun.order.mapper.entry.order.OrderTODeliveryOrderEntry;
import com.pinshang.qingyun.order.mapper.entry.splitOrder.SubOrder2DeliveryOrderItemEntry;
import com.pinshang.qingyun.order.mapper.entry.splitOrder.SubOrder2DeliveryOrderListEntry;
import com.pinshang.qingyun.order.service.DictionaryService;
import com.pinshang.qingyun.order.service.SubOrderService;
import com.pinshang.qingyun.storage.dto.order.CreatDeliveryOrderIDTO;
import com.pinshang.qingyun.storage.service.DeliveryOrderClient;
import com.pinshang.qingyun.storage.service.order.DCOrderClient;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.redisson.api.RBucket;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static java.util.stream.Collectors.toList;


@Service
@Slf4j
public class SubOrderConfirmService {

    @Autowired
    private SubOrderMapper subOrderMapper;
    @Autowired
    private DeliveryOrderClient deliveryOrderClient;
    @Autowired
    private SubOrderConfirmMapper subOrderConfirmMapper;
    @Autowired
    private SubOrderService subOrderService;
    @Autowired
    private DictionaryService dictionaryService;
    @Autowired
    private RedissonClient redissonClient;
    @Autowired
    private DCOrderClient dcOrderClient;


    public void confirmSO2DO() {
        List<OrderTODeliveryOrderEntry> unConfirmEntrys = subOrderConfirmMapper.queryUnConfirmSubOrderIdList();
        if (CollectionUtils.isEmpty(unConfirmEntrys)) {
            return;
        }
        List<Long> subOrderIds = unConfirmEntrys.stream().map(OrderTODeliveryOrderEntry::getSubOrderId).collect(Collectors.toList());
        List<Long> finishedSoIds = deliveryOrderClient.confirmSO2DO(subOrderIds);
        if (CollectionUtils.isNotEmpty(finishedSoIds)) {
            subOrderService.updateSubOrderStatus(finishedSoIds, SubOrderStatusEnums.SUB_ORDER_DELIVERY.getCode());
            subOrderConfirmMapper.batchDelete(finishedSoIds);
            subOrderIds.removeIf(finishedSoIds::contains);
        }
        if (CollectionUtils.isNotEmpty(subOrderIds)) {
            RBucket<Boolean> bucket = redissonClient.getBucket(QYApplicationContext.redisKeyProfile + "DC_TOB_CREATE_DELIVERY");
            Boolean createByDC = bucket.get();
            Map<Integer,List<CreatDeliveryOrderIDTO>> unConfirmMap = new HashMap<>();
            for (OrderTODeliveryOrderEntry unConfirm : unConfirmEntrys) {
                if(subOrderIds.contains(unConfirm.getSubOrderId())){
                    CreatDeliveryOrderIDTO idto = new CreatDeliveryOrderIDTO();
                    BeanUtils.copyProperties(unConfirm, idto);
                    idto.setUserId(1L);
                    Integer businessType = unConfirm.getBusinessType() == null ? DeliveryOrderTypeEnums.SALE.getCode() : unConfirm.getBusinessType();
                    unConfirmMap.computeIfAbsent(businessType, k -> new ArrayList<>()).add(idto);
                }
            }

            for (Map.Entry<Integer, List<CreatDeliveryOrderIDTO>> entry : unConfirmMap.entrySet()) {
                if (createByDC != null && createByDC) {
                    Map<String, List<CreatDeliveryOrderIDTO>> map = entry.getValue().stream().collect(Collectors.groupingBy(i -> Optional.ofNullable(i.getWarehouseId()).orElse(-1L) + Optional.ofNullable(i.getOrderTime()).orElse("unknown")));
                    map.forEach((k, v) -> {
                        dcOrderClient.creatDeliveryOrderByOrderIds(v);
                    });
                } else {
                    List subOrderIdList = entry.getValue().stream().map(CreatDeliveryOrderIDTO::getSubOrderId).collect(toList());
                    List<SubOrder2DeliveryOrderListEntry> entryList = this.getDeliveryOrderEntryConfirm(subOrderIdList,entry.getKey());
                    if (CollectionUtils.isNotEmpty(entryList)) {
                        Map<String, List<SubOrder2DeliveryOrderListEntry>> resultMap = new HashMap<>();
                        for (SubOrder2DeliveryOrderListEntry item : entryList) {
                            String resultKey = item.getWarehouseId() + "_" + DateUtil.getDateFormate(item.getOrderTime(),"yyyy-MM-dd");
                            if (resultMap.containsKey(resultKey)) {
                                resultMap.get(resultKey).add(item);
                            } else {
                                resultMap.put(resultKey, new ArrayList<SubOrder2DeliveryOrderListEntry>() {{ add(item); }});
                            }
                        }
                        for (Map.Entry<String, List<SubOrder2DeliveryOrderListEntry>> subEntry : resultMap.entrySet()) {
                            String[] keys = subEntry.getKey().split("_");
                            RLock lock = redissonClient.getLock(LockKeyConstant.createDeliveryOrderLock(Long.valueOf(keys[0]),keys[1]));
                            try {
                                if (lock.tryLock(30L, TimeUnit.SECONDS)) {
                                    try {
                                        subOrderService.createDeliveryOrderByCommon(subEntry.getValue(), Boolean.FALSE);
                                    } catch (Exception e) {
                                        log.error("定时检测已生成发货单的子单是否在大仓系统产生相应的发货单:{}", entry.getKey(), e);
                                    } finally {
                                        lock.unlock();
                                    }
                                }
                            } catch (Exception e) {
                                log.error("定时检测已生成发货单的子单是否在大仓系统产生相应的发货单,锁异常", e);
                            }
                        }
                    }
                }
            }
        }

    }

    /**
     * 查询SO转DO单的数据
     *
     * @param subOrderIdList
     * @return
     * @throws Throwable
     */
    public List<SubOrder2DeliveryOrderListEntry> getDeliveryOrderEntryConfirm(List<Long> subOrderIdList,Integer businessType) {
        List<SubOrder2DeliveryOrderListEntry> subOrderList = subOrderMapper.listSubOrder2DeliveryOrderConfirm(subOrderIdList,businessType);
        if (CollectionUtils.isNotEmpty(subOrderList)) {
            List<SubOrder2DeliveryOrderListEntry> resultList = new ArrayList<>();
            //SQL已过滤 物流模式为 配送 或鲜食客户的直通
//            Map<String, List<Long>> dictionaryMap = new HashMap<>();
//            if(DeliveryOrderTypeEnums.SALE.getCode().equals(businessType)){
//                dictionaryMap = dictionaryService.queryDirectDcConfig();
//            }
            //获取所有子单明细
            List<SubOrder2DeliveryOrderItemEntry> subOrderItemList = subOrderMapper.listSubOrder2DeliveryOrderItem(subOrderIdList, SubOrderStatusEnums.SUB_ORDER_DELIVERY.getCode(),businessType);
            //根据子单进行分组，一个子单对应该子单下的所有子单明细
            Map<Long, List<SubOrder2DeliveryOrderItemEntry>> subOrderItemMap = new HashMap<>();
            if (SpringUtil.isNotEmpty(subOrderItemList)) {
                subOrderItemMap = subOrderItemList.stream().collect(Collectors.groupingBy(SubOrder2DeliveryOrderItemEntry::getSubOrderId));
            }
            //设置子单与子单明细关系
            for (SubOrder2DeliveryOrderListEntry subOrder : subOrderList) {
                List<SubOrder2DeliveryOrderItemEntry> subOrderItems = subOrderItemMap.get(subOrder.getSubOrderId());
                if (SpringUtil.isNotEmpty(subOrderItems)) {
                    subOrder.setItemList(subOrderItems);
                    resultList.add(subOrder);
                }
            }
            return resultList;
        }
        return Collections.EMPTY_LIST;
    }
}
