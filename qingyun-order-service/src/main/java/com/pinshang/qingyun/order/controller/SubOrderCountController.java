package com.pinshang.qingyun.order.controller;

import com.pinshang.qingyun.order.service.SubOrderCountService;
import com.pinshang.qingyun.order.vo.order.CountCommodityInfoVo;
import com.pinshang.qingyun.order.vo.order.CountCommoditySubmitVo;
import com.pinshang.qingyun.order.vo.order.ShopOrderNumVo;
import com.pinshang.qingyun.renderer.annotation.MethodRender;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/subOrderCount/")
public class SubOrderCountController {

    @Autowired
    private SubOrderCountService subOrderCountService;

    @ApiOperation(value = "该门店送货点数")
    @GetMapping("shopOrderNum")
    public ShopOrderNumVo shopOrderNum() {
        return subOrderCountService.shopOrderNum();
    }

    @ApiOperation(value = "清点商品的信息")
    @GetMapping("countCommodityInfo")
    @MethodRender
    public CountCommodityInfoVo countCommodityInfo(@RequestParam(value = "barCode", required = false) String barCode) {
        return subOrderCountService.countCommodityInfo(barCode);
    }

    @ApiOperation(value = "提交清点的商品,扫码录入，单个")
    @PostMapping("submitCountCommodity")
    public Boolean submitCountCommodity(@RequestBody CountCommoditySubmitVo vo) {
        return subOrderCountService.submitCountCommodity(vo);
    }

    @ApiOperation(value = "提交清点的商品,逐个扫码，多个")
    @PostMapping("submitCountMultiCommodity")
    public Boolean submitCountMultiCommodity(@RequestBody List<CountCommoditySubmitVo> list) {
        return subOrderCountService.submitCountMultiCommodity(list);
    }

    @ApiOperation(value = "查看已点商品")
    @GetMapping("countCommodityList")
    @MethodRender
    public List<CountCommodityInfoVo> countCommodityList() {
        return subOrderCountService.countCommodityList();
    }

    @ApiOperation(value = "查看单个已点商品")
    @GetMapping("commodityInfo")
    public CountCommodityInfoVo commodityInfo(@RequestParam(value = "barCode", required = false) String barCode) {
        return subOrderCountService.commodityInfo(barCode);
    }

    @ApiOperation(value = "删除商品的锁定")
    @PostMapping("redisDeleteCommodity")
    public Boolean redisDeleteCommodity(@RequestBody List<Long> commodityIds) {
        return subOrderCountService.redisDeleteCommodity(commodityIds);
    }




}
