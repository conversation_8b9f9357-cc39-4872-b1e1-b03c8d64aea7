package com.pinshang.qingyun.order.mapper.tms;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import com.pinshang.qingyun.order.dto.tms.RefOrderForWaybillODTO;
import com.pinshang.qingyun.order.dto.tms.RefOrderItemForWaybillODTO;
import com.pinshang.qingyun.order.dto.tms.SelectRefOrderForWaybillIDTO;
import com.pinshang.qingyun.order.dto.tms.WarehouseConfirmInfoODTO;

/**
 * 【订单系统 <-> 物流系统】
 */
@Mapper
@Repository
public interface OrderTmsMapper {

	/**
	 * 查询【源单-订单】
	 * 
	 * @param idto
	 * @return
	 */
	public RefOrderForWaybillODTO selectOrder(SelectRefOrderForWaybillIDTO idto);

	/**
	 * 查询【源单明细-订单】列表
	 * 
	 * @param idto
	 * @return
	 */
	public List<RefOrderItemForWaybillODTO> selectOrderItemList(SelectRefOrderForWaybillIDTO idto);

	/**
	 * 查询【源单-退货单】
	 * 
	 * @param idto
	 * @return
	 */
	public RefOrderForWaybillODTO selectReturnOrder(SelectRefOrderForWaybillIDTO idto);

	/**
	 * 查询【源单明细-退货单】列表
	 * 
	 * @param idto
	 * @return
	 */
	public List<RefOrderItemForWaybillODTO> selectReturnOrderItemList(SelectRefOrderForWaybillIDTO idto);
	
	/**
	 * 查询【大仓确认信息】列表
	 * 
	 * @param refOrderId
	 * @return
	 */
	public List<WarehouseConfirmInfoODTO> selectWarehouseConfirmInfoList(@Param("refOrderId")Long refOrderId);

}
