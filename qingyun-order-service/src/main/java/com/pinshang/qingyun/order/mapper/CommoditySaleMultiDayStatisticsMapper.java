package com.pinshang.qingyun.order.mapper;

import com.pinshang.qingyun.base.mybatis.MyMapper;
import com.pinshang.qingyun.order.model.order.XdaCommoditySaleDayStatistics;
import com.pinshang.qingyun.order.model.order.XdaCommoditySaleMultiDayStatistics;
import org.apache.ibatis.annotations.Delete;
import org.springframework.stereotype.Repository;

import java.util.List;


@Repository
public interface CommoditySaleMultiDayStatisticsMapper extends MyMapper<XdaCommoditySaleMultiDayStatistics> {
    @Delete("delete from t_xda_commodity_sale_multi_day_statistics")
    void truncateSaleStatistics();
    /**
     * 更新销售统计数据，先删除数据，再重新统计
     */
    void insertSaleStatistics(List<XdaCommoditySaleDayStatistics> entryList);
}
