package com.pinshang.qingyun.order.mapper;

import com.pinshang.qingyun.base.mybatis.MyMapper;
import com.pinshang.qingyun.order.model.store.StorePlaceOrderLog;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

/**
 * @author: ch<PERSON><PERSON><PERSON>
 * @time: 2021/5/13 14:42
 */
@Mapper
@Repository
public interface StorePlaceOrderLogMapper extends MyMapper<StorePlaceOrderLog> {

    /**
     * 根据客户id 查询日志相关信息
     * @param storeId
     * @return
     */
    StorePlaceOrderLog selectStorePlaceOrderLogByStoreId(@Param("storeId")Long storeId);
}
