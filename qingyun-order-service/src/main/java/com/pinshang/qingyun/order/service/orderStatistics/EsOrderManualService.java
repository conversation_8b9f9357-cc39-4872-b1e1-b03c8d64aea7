package com.pinshang.qingyun.order.service.orderStatistics;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.pinshang.qingyun.base.configure.expand.QYAssert;
import com.pinshang.qingyun.base.constant.QingyunConstant;
import com.pinshang.qingyun.box.utils.BeanCloneUtils;
import com.pinshang.qingyun.box.utils.DateUtil;
import com.pinshang.qingyun.box.utils.IDGenerator;
import com.pinshang.qingyun.box.utils.SpringUtil;
import com.pinshang.qingyun.order.enums.OrderPrintTypeEnum;
import com.pinshang.qingyun.order.mapper.OrderListGiftMapper;
import com.pinshang.qingyun.order.mapper.OrderMapper;
import com.pinshang.qingyun.order.mapper.entry.orderStatistics.OrderMirrorSyncEntry;
import com.pinshang.qingyun.order.mapper.entry.orderStatistics.OrderStatisticsEntry;
import com.pinshang.qingyun.order.mapper.entry.orderStatistics.OrderSyncEntry;
import com.pinshang.qingyun.order.mapper.orderStatistics.OrderStatisticsMonitorMapper;
import com.pinshang.qingyun.order.model.order.Order;
import com.pinshang.qingyun.order.model.order.OrderList;
import com.pinshang.qingyun.order.model.order.OrderListGift;
import com.pinshang.qingyun.order.service.OrderAsyncKafkaService;
import com.pinshang.qingyun.order.vo.orderMonitor.EsOrderManualVo;
import com.pinshang.qingyun.order.vo.orderStatistics.OrderStatisticsMonitorVo;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RequestBody;
import tk.mybatis.mapper.entity.Example;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static java.util.stream.Collectors.groupingBy;

/**
 * 订单统计查询--订单监控
 */
@Slf4j
@Service
public class EsOrderManualService {
    @Autowired
    private OrderMapper orderMapper;
    @Autowired
    private OrderListGiftMapper orderItemMapper;
    @Autowired
    private KafkaTemplate<String, String> kafkaTemplate;
    @Value("${application.name.switch}")
    private String applicationNameSwitch;
    @Autowired
    private OrderAsyncKafkaService orderAsyncKafkaService;

    private int corePoolSize = 20;
    private int maxPoolSize = 200;
    private int capacity = 10000;
    // 睡眠时间，单位毫秒
    private long sleepTime = 5000;
    // 任务执行数
    private long taskCount = 1000000;
    // 任务执行最大时间，单位毫秒
    private long taskTime = 10 * 60 * 1000;



    public void createAssignOrder(EsOrderManualVo vo){
        long orderNum = vo.getOrderNum()==null? taskCount: vo.getOrderNum();
        long continumTime = vo.getContinueTime()==null?taskTime: (vo.getContinueTime() * 1000);

        ThreadPoolExecutor threadPool = new ThreadPoolExecutor(corePoolSize, maxPoolSize, 3,
                TimeUnit.MINUTES, new ArrayBlockingQueue<Runnable>(capacity));
        int i = 0;
        long startTime = System.currentTimeMillis();
        while(i < orderNum && (System.currentTimeMillis() - startTime < continumTime)) {
            {
                long beginTime = System.currentTimeMillis();
                try {
                    threadPool.execute(()-> {
                        assignOrder();
                    });
                    System.out.println("====本次执行时间："+(System.currentTimeMillis()-beginTime)/1000+"  秒");
                    if(vo.getTimeInterval()!=null){
                        Thread.sleep(vo.getTimeInterval()*1000);
                    }
                } catch (Exception e) {
                    log.error("异常: ",e);
                }
                i++;
            }
        }
        // 等待所有线程执行完毕当前任务。
        threadPool.shutdown();
    }


    public void assignOrder(){
        try{
            Order order = new Order();
            order.setOrderCode(IDGenerator.newOrderCode());
            order.setStoreId(999872035591520201L);
            order.setOrderTime(DateUtil.getNowDate());
            order.setFinalAmount(BigDecimal.valueOf(136.92));
            order.setOrderAmount(BigDecimal.valueOf(136.92));
            order.setOrderStatus(0);
            order.setModeType(0);
            order.setOrderType(1);
            order.setPrintNum(1);
            order.setPrintType(OrderPrintTypeEnum.NOPRInteger);
            order.setSettleStatus(0);
            order.setCreateId(372L);
            order.setCreateTime(new Date());
            order.setUpdateId(372L);
            order.setUpdateTime(new Date());
            orderMapper.insertSelective(order);
            Long orderId = order.getId();
            List<OrderList> orderList = new ArrayList<>();
            OrderListGift orderItem1 = new OrderListGift();
            orderItem1.setCommodityId(999965182752695664L);
            orderItem1.setCommodityNum(BigDecimal.valueOf(4));
            orderItem1.setCommodityPrice(BigDecimal.valueOf(4.64));
            orderItem1.setOrderId(orderId);
            orderItem1.setTotalPrice(BigDecimal.valueOf(23.2));
            orderItem1.setType(1);
            orderItemMapper.insertSelective(orderItem1);
            orderList.add(BeanCloneUtils.copyTo(orderItem1,OrderList.class));
            OrderListGift orderItem2 = new OrderListGift();
            orderItem2.setCommodityId(999965182752695666L);
            orderItem2.setCommodityNum(BigDecimal.valueOf(1));
            orderItem2.setCommodityPrice(BigDecimal.valueOf(11.65));
            orderItem2.setOrderId(orderId);
            orderItem2.setTotalPrice(BigDecimal.valueOf(11.65));
            orderItem2.setType(1);
            orderItemMapper.insertSelective(orderItem2);
            orderList.add(BeanCloneUtils.copyTo(orderItem2,OrderList.class));
            order.setOrderList(orderList);

            orderAsyncKafkaService.sendKafkaSaveOrderMessage(order);
        }catch(Exception e){
            log.error("生成订单异常",e);
        }

    }

}
