package com.pinshang.qingyun.order.listener;

import com.pinshang.qingyun.order.service.XDShoppingCartService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @Author: sk
 * @Date: 2019/11/29
 */
@Component
@Slf4j
public class NewAddXdShoppingCart {

    @Autowired
    private XDShoppingCartService xDShoppingCartService;

    /**
     * 新品加入鲜道购物车
     * @param message
     */
   /* @KafkaListener(id="${application.name.switch}" + KafkaTopicConstant.COMMODITY_START_SALE_INFORM_XD_ORDER,topics = {"${application.name.switch}" + KafkaTopicConstant.COMMODITY_START_SALE_INFORM_XD_ORDER })
    public void addXdShoppingCart(String message) {
        log.info("新品加入鲜道购物车====================message==" + message);
        try {
            KafkaMessageWrapper messageWrapper = JSON.parseObject(message, KafkaMessageWrapper.class);
            List<Long> idList = JSON.parseObject(messageWrapper.getData().toString(), List.class);
            xDShoppingCartService.newCommodityAddShoppingCart(idList);
        } catch (Exception e) {
            log.error("新品加入鲜道购物车-----消息消费异常,异常:{}", e);
        }
    }*/

    /**
     * 可采加入鲜道购物车
     * @param message
     */
    /*@KafkaListener(id="${application.name.switch}" + KafkaTopicConstant.XD_SHOP_COMMODITY_SHOPPING_CART_TOPIC,topics = {"${application.name.switch}" + KafkaTopicConstant.XD_SHOP_COMMODITY_SHOPPING_CART_TOPIC })
    public void addXdShopShoppingCart(String message) {
        log.info("可采加入鲜道购物车====================message==" + message);
        try {
            KafkaMessageWrapper messageWrapper = JSON.parseObject(message, KafkaMessageWrapper.class);
            List<ShoppingCartKafkaEntry> list = JSON.parseObject(messageWrapper.getData().toString(), new TypeReference<List<ShoppingCartKafkaEntry>>(){});

            xDShoppingCartService.canPurchaseAddShoppingCart(list);
        } catch (Exception e) {
            log.error("可采加入鲜道购物车-----消息消费异常,异常:{}", e);
        }
    }*/
}
