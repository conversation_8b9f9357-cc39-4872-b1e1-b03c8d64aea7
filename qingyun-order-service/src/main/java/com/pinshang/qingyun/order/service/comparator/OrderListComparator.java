package com.pinshang.qingyun.order.service.comparator;

import com.pinshang.qingyun.order.enums.CombTypeEnum;
import com.pinshang.qingyun.order.enums.ProductTypeEnums;
import com.pinshang.qingyun.order.model.order.OrderList;
import com.pinshang.qingyun.order.model.order.OrderListGift;
import com.pinshang.qingyun.order.model.order.SubOrderItem;

import java.util.Comparator;

public class OrderListComparator {

    public static <T> Comparator<T> orderListCombComparator() {
        return Comparator.<T, Integer>comparing(o -> {
                    Long pricePromotionId = 0L;
                    Integer type = 0;
                    if (o instanceof OrderList) {
                        if(((OrderList) o).getPricePromotionId() != null){
                            pricePromotionId = ((OrderList) o).getPricePromotionId();
                        }
                        type = ((OrderList) o).getType();
                    } else if (o instanceof OrderListGift) {
                        if(((OrderListGift) o).getPricePromotionId() != null){
                            pricePromotionId = ((OrderListGift) o).getPricePromotionId();
                        }
                        type = ((OrderListGift) o).getType();
                    }else if (o instanceof SubOrderItem) {
                        if(((SubOrderItem) o).getPricePromotionId() != null){
                            pricePromotionId = ((SubOrderItem) o).getPricePromotionId();
                        }
                        type = ((SubOrderItem) o).getType();
                    }
                    if(pricePromotionId > 0){
                        return 0;
                    }else if(ProductTypeEnums.PRODUCT.getCode().equals(type)){
                        return 1;
                    } else if (ProductTypeEnums.TH.getCode().equals(type)) {
                        return 2;
                    } else if (ProductTypeEnums.RATION.getCode().equals(type) || ProductTypeEnums.ratio.getCode().equals(type)) {
                        return 3;
                    }else if (ProductTypeEnums.GIFT.getCode().equals(type)) {
                        return 4;
                    }else {
                        return Integer.MAX_VALUE;
                    }
                })
                .thenComparingInt(o -> {
                    Integer combType = 0;
                    if (o instanceof OrderList) {
                        combType = ((OrderList) o).getCombType();
                    } else if (o instanceof OrderListGift) {
                        combType = ((OrderListGift) o).getCombType();
                    }else if (o instanceof SubOrderItem) {
                        combType = ((SubOrderItem) o).getCombType();
                    }

                    if(CombTypeEnum.COMB.getCode().equals(combType) || CombTypeEnum.COMB_CHILD.getCode().equals(combType)){
                        return 0;
                    }else{
                        return 1;
                    }

                })
                .thenComparingLong(o -> {
                    Long commodityId = 0L;
                    if (o instanceof OrderList) {
                        commodityId = ((OrderList) o).getCommodityId();
                    } else if (o instanceof OrderListGift) {
                        commodityId = ((OrderListGift) o).getCommodityId();
                    }else if (o instanceof SubOrderItem) {
                        commodityId = ((SubOrderItem) o).getCommodityId();
                    }
                    return commodityId;
             });
    }
}
