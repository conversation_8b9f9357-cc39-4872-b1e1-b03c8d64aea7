package com.pinshang.qingyun.order.mapper;

import com.pinshang.qingyun.base.mybatis.MyMapper;
import com.pinshang.qingyun.order.dto.ReceiveOrderMessageIDTO;
import com.pinshang.qingyun.order.dto.ReceiveOrderMessageODTO;
import com.pinshang.qingyun.order.mapper.entry.order.ReceiveOrderMessageEntry;
import com.pinshang.qingyun.order.mapper.entry.shop.*;
import com.pinshang.qingyun.order.model.shop.ShopReceiveOrder;
import com.pinshang.qingyun.order.vo.ShopReceiveHQVo;
import com.pinshang.qingyun.order.vo.shop.*;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;

@Mapper
@Repository
public interface ShopReceiveOrderMapper extends MyMapper <ShopReceiveOrder> {

    List <ShopReceiveEntry> getListByCondition(@Param("shopReceiveVo") ShopReceiveVo shopReceiveVo);

    ShopReceiveOrderEntry getShopReceiveOrderEntryBySubOrderCode(@Param("subOrderCode") String subOrderCode);

    ShopReceiveOrderEntry getShopReceiveOrderEntryBySubOrderId(@Param("subOrderId") Long subOrderId);

    List <ShopReceiveOrderItemEntry> getShopReceiveOrderItemEntryBySubOrderId(@Param("subOrderId") String subOrderId);
    
    List <CommodityStockOutQuantityEntry> getStockOutQuantityByCommodityIds(StockOutQuantityVo stockOutQuantityVo);


    Integer updateReceiveOrder(@Param("status") Integer status,
                               @Param("receiveTime") Date receiveTime,
                               @Param("receiveId") String receiveId,
                               @Param("remark") String remark,
                               @Param("subOrderId") String subOrderId);



    List<AuditInfoEntry> getAuditList(@Param("auditQueryVo") AuditQueryVo auditQueryVo);


    @Select("select commodity_id,price,real_delivery_quantity from t_sub_order_item where sub_order_id = #{subOrderId}")
    List<Quantity> getDeliveryQuantity(@Param("subOrderId") String subOrderId);

    List <ShopReceiveEntry> getPreOrderListByCondition(@Param("shopReceiveVo") ShopReceiveVo shopReceiveVo);

    List <ShopReceiveHQEntry> getPreOrderHQListByCondition(@Param("shopReceiveHQVo") ShopReceiveHQVo shopReceiveHQVo);


    ShopReceiveOrderEntry getPreOrderByCode(@Param("orderCode") String orderCode);

    List<ShopReceiveOrderItemEntry> getPreOrderItemByCode(@Param("preorderId") String preorderId);

    List<ReceiveOrderVo> getAutoReceiveOrder(@Param("logisticsModels") List<Integer> logisticsModels, @Param("date") Date date,@Param("shopIdList") List<Long> shopIdList);

    List<ReceiveOrderVo> getAutoReceiveOrderNew(@Param("logisticsModels") List<Integer> logisticsModels, @Param("beginTime") String beginTime, @Param("orderTime") String orderTime,@Param("storeIdList") List<Long> storeIdList, @Param("xsjmStoreIdList") List<Long> xsjmStoreIdList);

    List<ReceiveOrderVo> getAutoReceiveOrderBySubOrderIdList(@Param("subOrderIdList") List<Long> subOrderIdList, @Param("xsjmStoreIdList") List<Long> xsjmStoreIdList);

    List<ReceiveOrderVo> getAutoReceiveXsjmOrder(@Param("logisticsModels") List<Integer> logisticsModels, @Param("orderTime") String orderTime,@Param("storeIdList") List<Long> storeIdList);

    List<Quantity> getAutoDeliveryQuantity(@Param("subOrderId") String subOrderId);


    List<ReceiveOrderVo> getHandleAutoReceiveOrder(@Param("logisticsModels") List<Integer> logisticsModels, @Param("orderTime") String orderTime,@Param("storeIdList") List<Long> storeIdList);
    List<Quantity> getHandleAutoDeliveryQuantity(@Param("subOrderId") String subOrderId);

    int batchInsertReceiveOrderMessage(List<ReceiveOrderMessageEntry> receiveOrderMessageList);

    List<ReceiveOrderMessageODTO> getReceiveOrderMessagePage(ReceiveOrderMessageIDTO idto);

    List<TjPreOrderEntry> queryTjPreOrderList(TjPreOrderVO vo);
}