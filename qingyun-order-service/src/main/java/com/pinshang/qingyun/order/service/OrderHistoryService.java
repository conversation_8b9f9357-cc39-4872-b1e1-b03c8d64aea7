package com.pinshang.qingyun.order.service;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.pinshang.qingyun.base.configure.expand.QYAssert;
import com.pinshang.qingyun.box.utils.DateUtil;
import com.pinshang.qingyun.order.dto.cup.OrderLogPageIDTO;
import com.pinshang.qingyun.order.listener.OrderHistoryHelper;
import com.pinshang.qingyun.order.mapper.OrderHistoryMapper;
import com.pinshang.qingyun.order.mapper.StoreMapper;
import com.pinshang.qingyun.order.mapper.entry.order.OrderLogListEntry;
import com.pinshang.qingyun.order.model.order.Order;
import com.pinshang.qingyun.order.model.order.OrderHistory;
import com.pinshang.qingyun.order.model.order.OrderList;
import com.pinshang.qingyun.order.model.order.User;
import com.pinshang.qingyun.order.model.store.Store;
import com.pinshang.qingyun.order.vo.order.OrderHistoryAddTypeEnums;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;

/**
 * 订单日志Service
 *
 * <AUTHOR>
 *
 * @date 2021年9月8日
 */
@Component
public class OrderHistoryService {
	
	@Autowired
    private StoreMapper storeMapper;
	
	@Autowired
    private OrderHistoryMapper orderHistoryMapper;

	@Autowired
	private CommonService commonService;

	/**
	 * 插入  （创建订单）订单日志
	 * 
	 * @param order
	 */
    @Async
    public void insertOrderHistoryOnCreateOrder(Order order) {
    	Store store = storeMapper.selectByPrimaryKey(order.getStoreId());
   	 	if (null != store) {
   	 		orderHistoryMapper.insert(OrderHistory.order_create(order.getId(), order.getOrderCode(), DateUtil.get4yMd(order.getOrderTime()), store.getId(), store.getStoreName(), order.getCreateTime()));
   	 	}
    }
    
    /**
     * 插入  （取消订单）订单日志
     * 
     * @param order
     */
    public void insertOrderHistoryOnCancelOrder(Order order) {
    	 Store store = storeMapper.selectByPrimaryKey(order.getStoreId());
    	 if (null != store) {
    		 orderHistoryMapper.insert(OrderHistory.order_cancel(order.getId(), order.getOrderCode(), store.getId(), store.getStoreName(), new Date()));
    	 }
    }

	/**
	 * 通达0出库，记录为系统取消
	 * @param order
	 */
	public void insertSystemOrderHistoryOnCancelOrder(Order order) {
		orderHistoryMapper.insert(OrderHistory.order_cancel_zero_out(order.getId(), order.getOrderCode(), -1L, "系统", new Date()));
	}

	/**
	 * 插入  （取消订单）订单日志
	 *
	 * @param order
	 */
	public void insertOrderHistoryOnCancelOrderV2(Order order, User user) {
		orderHistoryMapper.insert(OrderHistory.order_cancel(order.getId(), order.getOrderCode(), user.getId(), user.getRealName(), new Date()));
	}

	/**
	 * 订单日志列表
	 *
	 * @param idto
	 * @return
	 */
	public PageInfo<OrderLogListEntry> orderLogList(OrderLogPageIDTO idto) {
		QYAssert.isTrue(idto.getOrderId() != null , "订单ID不能为空");

		PageInfo<OrderLogListEntry> pageDate = null ;
		pageDate = PageHelper.startPage(idto.getPageNo(), idto.getPageSize()).doSelectPageInfo(() -> {
			orderHistoryMapper.getOrderLogList(idto.getOrderId());
		});

		return pageDate;
	}

	public void logOrderHistory(List<OrderList> oldOrderLists, List<OrderList> newOrderLists, Long userId) {
		User user = commonService.getUserById(userId);
		user.setEmployeeNumber(user.getEmployeeCode());
		user.setRealName(user.getEmployeeName());
		List<OrderHistory> histories = OrderHistoryHelper.orderHistoryList(oldOrderLists, newOrderLists, OrderHistoryAddTypeEnums.COMMON, user);
		if (CollectionUtils.isEmpty(histories)) {
			return;
		}
		orderHistoryMapper.insertList(histories);
	}
}
