package com.pinshang.qingyun.order.service;

import com.pinshang.qingyun.base.configure.expand.QYAssert;
import com.pinshang.qingyun.box.utils.SpringUtil;
import com.pinshang.qingyun.order.mapper.StoreInvoiceMapper;
import com.pinshang.qingyun.order.mapper.entry.store.StoreInviceEntry;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
@Slf4j
public class StoreInvoiceService {
    @Autowired
    private StoreInvoiceMapper storeMapper;

    public List<StoreInviceEntry> findListByStoreIds(List<Long> ids) {
        QYAssert.isTrue(SpringUtil.isNotEmpty(ids), "客户id集合为空");
        return storeMapper.findListByStoreIds(ids);
    }

    public StoreInviceEntry findInvoiceById(String uid) {
        final List<StoreInviceEntry> listByStoreIds = storeMapper.findListByIds(uid);
        return listByStoreIds.isEmpty() ? null:listByStoreIds.get(0);
    }

}
