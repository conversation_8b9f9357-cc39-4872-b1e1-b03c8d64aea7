package com.pinshang.qingyun.order.service.tob.impl;

import com.pinshang.qingyun.base.configure.expand.QYAssert;
import com.pinshang.qingyun.base.enums.BusinessTypeEnums;
import com.pinshang.qingyun.base.enums.storage.StockTypeEnum;
import com.pinshang.qingyun.box.utils.DateUtil;
import com.pinshang.qingyun.box.utils.SpringUtil;
import com.pinshang.qingyun.order.dto.tob.EsXdaCommoditySoldOutIDTO;
import com.pinshang.qingyun.order.dto.tob.EsXdaCommoditySoldOutODTO;
import com.pinshang.qingyun.order.mapper.ProcessOrderCommodityStatisticsMapper;
import com.pinshang.qingyun.order.mapper.TobCommodityStockMapper;
import com.pinshang.qingyun.order.model.tob.ToBProcessOrderCommodityStatistics;
import com.pinshang.qingyun.order.model.tob.TobCommodityStock;
import com.pinshang.qingyun.order.service.tob.ITobCommodityStockForEsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.text.ParseException;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2024/04/11
 * @Version 1.0
 */
@Service
public class TobCommodityStockForEsServiceImpl implements ITobCommodityStockForEsService {
    @Autowired
    private TobCommodityStockMapper tobCommodityStockMapper;

    @Autowired
    private ProcessOrderCommodityStatisticsMapper processOrderCommodityStatisticsMapper;

    @Override
    public List<Long> selectCommodityIdByStockType(Integer stockType){
        return tobCommodityStockMapper.selectCommodityIdByStockType(stockType);
    }

    @Override
    public List<EsXdaCommoditySoldOutODTO> queryCommodityInventory(EsXdaCommoditySoldOutIDTO idto){
        QYAssert.isTrue(SpringUtil.isNotEmpty(idto.getCommodityIdList()), "商品id不能为空");
        List<TobCommodityStock> stockList = tobCommodityStockMapper.selectStockByCommodityIdList(idto.getCommodityIdList());
        Map<Long, TobCommodityStock> stockMap = stockList.stream().collect(Collectors.toMap(TobCommodityStock::getCommodityId, Function.identity()));
        String beginDate = DateUtil.get4yMd(idto.getDate());
        String endDate = null;
        if(2 == idto.getType()){
            endDate = DateUtil.get4yMd(DateUtil.addDay(idto.getDate(), 7));
        }
        List<ToBProcessOrderCommodityStatistics> staticsList = processOrderCommodityStatisticsMapper.selectByCommodityIdAndDate(idto.getCommodityIdList(), beginDate, endDate);
        Map<Long, List<ToBProcessOrderCommodityStatistics>> staticsMap = new HashMap<>();
        if(SpringUtil.isNotEmpty(staticsList)) {
            staticsMap = staticsList.stream().collect(Collectors.groupingBy(ToBProcessOrderCommodityStatistics::getCommodityId));
        }
        List<EsXdaCommoditySoldOutODTO> resultList = new ArrayList<>(stockList.size());
        for (Long commodityId : idto.getCommodityIdList()) {
            EsXdaCommoditySoldOutODTO item = new EsXdaCommoditySoldOutODTO();
            item.setCommodityId(commodityId);
            TobCommodityStock it = stockMap.get(commodityId);
            if(null == it){
                // 未设置默认仓库
                item.setTotal(0);
            }else {
                // 不限量
                if (Objects.equals(it.getStockType(), StockTypeEnum.UN_LIMIT.getCode())) {
                    if (idto.getType() == 2) {
                        item.setUnLimit();
                    } else {
                        item.setSoldOut(1);
                    }
                }
                // 依据大仓户这话限总量
                else if (Objects.equals(it.getStockType(), StockTypeEnum.WAREHOUSE.getCode())
                        || (Objects.equals(it.getStockType(), StockTypeEnum.LIMIT.getCode()) && 1 == it.getEffectType())) {
                    // 如果tdaStockStatus为空，则取stockStatus
                    it.setTdaStockStatus(it.getTdaStockStatus() != null ? it.getTdaStockStatus() : it.getStockStatus());
                    if (idto.getType() == 2) {
                        item.setTotal(BusinessTypeEnums.TD_SALE.getCode()==idto.getBusinessType()?
                                it.getTdaStockStatus():it.getStockStatus());
                    } else {
                        item.setSoldOut(BusinessTypeEnums.TD_SALE.getCode()==idto.getBusinessType()?
                                it.getTdaStockStatus():it.getStockStatus());
                    }
                }
                // 每日循环限量
                else if (Objects.equals(it.getStockType(), StockTypeEnum.LIMIT.getCode()) && 2 == it.getEffectType()) {
                    List<ToBProcessOrderCommodityStatistics> statics = staticsMap.get(it.getCommodityId());
                    if (SpringUtil.isNotEmpty(statics)) {
                        if (idto.getType() == 2) {
                            item.setTotal(1);
                            statics.forEach(staticsItem -> this.handleStockStatusList(staticsItem, new Date(), item, it));
                        } else {
                            item.setSoldOut(it.getLimitNumber() - statics.get(0).getOrderNumber() > 0 ? 1 : 0);
                        }
                    } else {
                        item.setTotal(it.getLimitNumber() > 0 ? 1 : 0);
                    }
                } else {
                    // 未设置库存依据的统一设置为0
                    item.setTotal(0);
                }
            }
            resultList.add(item);
        }
        return resultList;
    }


    private int compareDate(Date orderTime, Date now){
        try {
            return DateUtil.getDayDif(DateUtil.getDatePart(orderTime), DateUtil.getDatePart(now));
        } catch (ParseException e) {
            throw new RuntimeException(e);
        }
    }

    private boolean handleStockStatusList(ToBProcessOrderCommodityStatistics statistics, Date now, EsXdaCommoditySoldOutODTO item, TobCommodityStock stock ){
        int dayDiff = this.compareDate(statistics.getOrderTime(), now);
        boolean matchDate = true;
        int hasStock = stock.getLimitNumber() - statistics.getOrderNumber() > 0 ? 1 : 0;
        switch (dayDiff){
            case 0:
                item.setSoldOut(hasStock);
                break;
            case 1:
                item.setSoldOut1(hasStock);
                break;
            case 2:
                item.setSoldOut2(hasStock);
                break;
            case 3:
                item.setSoldOut3(hasStock);
                break;
            case 4:
                item.setSoldOut4(hasStock);
                break;
            case 5:
                item.setSoldOut5(hasStock);
                break;
            case 6:
                item.setSoldOut6(hasStock);
                break;
            case 7:
                item.setSoldOut7(hasStock);
                break;
            default:
                matchDate = false;
        }
        return matchDate;
    }
}
