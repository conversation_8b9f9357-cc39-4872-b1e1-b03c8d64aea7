package com.pinshang.qingyun.order.service;

import com.pinshang.qingyun.base.enums.order.OrderTypeEnum;
import com.pinshang.qingyun.box.utils.DateUtil;
import com.pinshang.qingyun.order.mapper.OrderMapper;
import com.pinshang.qingyun.order.util.ThreadLocalUtils;
import com.pinshang.qingyun.order.vo.order.OrderDto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

/**
 * 日日鲜商品订单
 * @Author: sk
 * @Date: 2023/3/3
 */
@Slf4j
@Service
public class CommodityFreshOrderSaveService {

    @Autowired
    private OrderMapper orderMapper;
    @Autowired
    private OrderService orderService;
    @Autowired
    private GrouponOrderSaveService grouponOrderSaveService;


    @Transactional(rollbackFor = Exception.class)
    public Boolean saveCommodityFreshOrder(List<OrderDto> orderDtoList, Long shopId, List<Long> updateCommodityIdList){

        // 更新日日鲜商品下单记录为已经下单
        orderMapper.updateCommodityFreshOrdered(DateUtil.getDateFormate(new Date(), "yyyy-MM-dd"), shopId, updateCommodityIdList);

        ThreadLocalUtils.setOrderType(OrderTypeEnum.DAY_FRESH_COMMODITY_ORDER.getCode());
        // 循环创建订单
        for(OrderDto orderDto : orderDtoList){
            // 更新一下最新的配置: 商品价格、商品限量、商品是否可变价、商品名称
            orderService.buildProductPrice(orderDto);
            // 保存日日鲜订单
            grouponOrderSaveService.saveGroupOrder(orderDto, null);
        }

        ThreadLocalUtils.remove();

        return Boolean.TRUE;
    }
}
