package com.pinshang.qingyun.order.mapper.orderStatistics;

import com.pinshang.qingyun.base.mybatis.MyMapper;
import com.pinshang.qingyun.order.mapper.entry.orderStatistics.OrderCompanyAndStoreTypeEntry;
import com.pinshang.qingyun.order.mapper.entry.orderStatistics.OrderMirrorSyncEntry;
import com.pinshang.qingyun.order.mapper.entry.orderStatistics.OrderStatisticsEntry;
import com.pinshang.qingyun.order.mapper.entry.orderStatistics.OrderSyncEntry;
import com.pinshang.qingyun.order.model.order.Order;
import com.pinshang.qingyun.order.vo.orderStatistics.OrderStatisticsMonitorVo;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface OrderStatisticsMonitorMapper extends MyMapper<Order>{

	//查询订单统计信息，订单比较
    OrderStatisticsEntry queryOrderStatisticsInfo(@Param("list") List<String> orderCodeList, @Param("vo") OrderStatisticsMonitorVo vo);

    //查询订单编号，用于计算差异订单
    List<String> queryOrderDiffList(@Param("list") List<String> orderCodeList, @Param("vo") OrderStatisticsMonitorVo vo);

    //查询订单主表，用于订单同步
    List<OrderSyncEntry> queryOrderList(@Param("list") List<String> orderCodeList, @Param("vo") OrderStatisticsMonitorVo vo);


    List<OrderMirrorSyncEntry> queryOrderMirrorSyncList(@Param("list") List<String> orderCodeList, @Param("vo") OrderStatisticsMonitorVo vo);

    List<OrderCompanyAndStoreTypeEntry> orderByCompanyAndStoreTypeReport(@Param("startOrderTime") String startOrderTime,@Param("endOrderTime") String endOrderTime);

}

