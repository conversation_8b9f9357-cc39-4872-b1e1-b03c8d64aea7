package com.pinshang.qingyun.order.service.pay;

import com.pinshang.qingyun.base.api.XdaTokenInfo;
import com.pinshang.qingyun.base.configure.expand.QYAssert;
import com.pinshang.qingyun.base.constant.XdaAppVersionConstant;
import com.pinshang.qingyun.base.enums.pay.AppCodeEnum;
import com.pinshang.qingyun.base.enums.pay.PayTypeIntEnum;
import com.pinshang.qingyun.base.util.FastThreadLocalUtil;
import com.pinshang.qingyun.box.utils.DateUtil;
import com.pinshang.qingyun.order.constant.PreOrderTipConstant;
import com.pinshang.qingyun.order.service.DictionaryService;
import com.pinshang.qingyun.order.service.pay.model.*;
import com.pinshang.qingyun.order.service.pay.model.log.ThirdPartyPayLogEvent;
import com.pinshang.qingyun.order.util.PayTypeConvertUtil;
import com.pinshang.qingyun.pay.dto.*;
import com.pinshang.qingyun.pay.service.AppPayClient;
import com.pinshang.qingyun.store.dto.storeCompany.StoreCompanyNameODTO;
import com.pinshang.qingyun.store.service.StoreCompanyClient;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.function.Consumer;

/**
 * 对接的三方支付很少没做泛化处理或策略模式设计
 * <AUTHOR>
 * @Date 2019/11/24 13:43
 */
@Component
@Slf4j
public class ThirdPartyPayService {


    @Autowired
    private AppPayClient payClient;
    @Autowired
    private StoreCompanyClient storeCompanyClient;
    @Autowired
    private DictionaryService dictionaryService;

    public Long getCompanyIdByStoreId(Long storeId){
        List<StoreCompanyNameODTO> storeCompanyNameODTOS= storeCompanyClient.selectComapnyNameByStoreIds(Arrays.asList(storeId));
        QYAssert.isTrue(CollectionUtils.isNotEmpty(storeCompanyNameODTOS),"公司信息不存在");
        return storeCompanyNameODTOS.get(0).getCompanyId();
    }
    /**
     * 生成三方待支付单
     * @param reqParam
     * @param logEventConsumer 同步执行线程,若需要异步自行扩展
     * @return
     */
    public PrePayResponse createPrePayBill(PrePayReqParam reqParam, Consumer<ThirdPartyPayLogEvent> logEventConsumer){
        PrePayIDTO prePayIDTO = new PrePayIDTO();
        BeanUtils.copyProperties(reqParam, prePayIDTO);
        PayTypeIntEnum payTypeIntEnum = PayTypeConvertUtil.xdPayTypeEnumToPayTypeIntEnum(reqParam.getPayType());


        prePayIDTO.setPayType(payTypeIntEnum.getCode());
        prePayIDTO.setAppCode(AppCodeEnum.XDA.getCode());
        prePayIDTO.setBillCode(reqParam.getOrderCode());
        prePayIDTO.setJsCode(reqParam.getJsCode());
        prePayIDTO.setShopId(getCompanyIdByStoreId(reqParam.getStoreId()));

        // 鲜达充值、创建现付单、预订单去继续支付设置订单过期时间
        XdaTokenInfo xdaTokenInfo = FastThreadLocalUtil.getXDA();
        if(xdaTokenInfo != null && StringUtils.isNotBlank(xdaTokenInfo.getAppVersion())){
            QYAssert.isTrue(!(PayTypeIntEnum.UNIONPAY_UNION.equals(payTypeIntEnum)
                    &&xdaTokenInfo.getAppVersion().compareToIgnoreCase(XdaAppVersionConstant.XDA_VERSION_164)<0), PreOrderTipConstant.OLD_UNION_MINI_TIPS);
            Boolean oldVersion = xdaTokenInfo.getAppVersion().compareToIgnoreCase(XdaAppVersionConstant.TDA_VERSION) < 0;
            Integer xdaPreOrderDelaySeconds = dictionaryService.queryXdaPreOrderDelaySeconds();
            Integer seconds = (oldVersion ? 180 : (xdaPreOrderDelaySeconds - 7 ));
            prePayIDTO.setBizTime(DateUtil.addSecond(new Date(), seconds));
        }else{
            QYAssert.isTrue(!PayTypeIntEnum.UNIONPAY_UNION.equals(payTypeIntEnum), PreOrderTipConstant.OLD_UNION_MINI_TIPS);
        }

        PrePayODTO prePayODTO = payClient.createPrePayOrder(prePayIDTO);
        PrePayResponse response = new PrePayResponse();
        BeanUtils.copyProperties(prePayODTO, response);
        return response;
    }

    /**
     * 支付查询
     * @param reqParam
     * @return
     */
    public QueryODTO tradeQuery(TradeQueryParam reqParam, AppCodeEnum appCode, Consumer<ThirdPartyPayLogEvent> logEventConsumer){
        QueryIDTO queryIDTO = new QueryIDTO();
        BeanUtils.copyProperties(reqParam, queryIDTO);
        queryIDTO.setBillCode(reqParam.getBillCode());
        queryIDTO.setAppCode(appCode.getCode());
        PayTypeIntEnum payTypeIntEnum = PayTypeConvertUtil.xdPayTypeEnumToPayTypeIntEnum(reqParam.getPayType());
        queryIDTO.setPayType(payTypeIntEnum.getCode());
        queryIDTO.setShopId(getCompanyIdByStoreId(reqParam.getStoreId()));
        return payClient.orderQuery(queryIDTO);
    }

    /**
     * 退款查询
     *
     * @param reqParam
     * @param logEventConsumer
     * @return
     */
    public QueryODTO refundQuery(TradeQueryParam reqParam, AppCodeEnum appCode, Consumer<ThirdPartyPayLogEvent> logEventConsumer) {
        QueryIDTO queryIDTO = new QueryIDTO();
        BeanUtils.copyProperties(reqParam, queryIDTO);
        queryIDTO.setBillCode(reqParam.getBillCode());
        queryIDTO.setAppCode(appCode.getCode());
        PayTypeIntEnum payTypeIntEnum = PayTypeConvertUtil.xdPayTypeEnumToPayTypeIntEnum(reqParam.getPayType());
        queryIDTO.setPayType(payTypeIntEnum.getCode());
        return payClient.refundQuery(queryIDTO);
    }

    /**
     * 执行退款
     * @param refundReqParam
     * @param logEventConsumer 同步执行线程,若需要异步自行扩展
     * @return
     */
    public RefundODTO refund(RefundReqParam refundReqParam, Consumer<ThirdPartyPayLogEvent> logEventConsumer){
        RefundIDTO refundIDTO = new RefundIDTO();
        BeanUtils.copyProperties(refundReqParam, refundIDTO);
        refundIDTO.setAppCode(AppCodeEnum.XDA.getCode());
        PayTypeIntEnum payTypeIntEnum = PayTypeConvertUtil.xdPayTypeEnumToPayTypeIntEnum(refundReqParam.getPayType());
        refundIDTO.setPayType(payTypeIntEnum.getCode());
        refundIDTO.setBillCode(refundReqParam.getOrderCode());
        refundIDTO.setShopId(getCompanyIdByStoreId(refundReqParam.getStoreId()));
        return payClient.refund(refundIDTO);
    }



    /**
     * 生成批发三方待支付单
     * @param reqParam
     * @param logEventConsumer
     * @return
     */
    public PfPrePayResponse createPfPrePayBill(PfPrePayReqParam reqParam, Consumer<ThirdPartyPayLogEvent> logEventConsumer) {
        PrePayIDTO prePayIDTO = new PrePayIDTO();
        BeanUtils.copyProperties(reqParam, prePayIDTO);
        //PayTypeIntEnum payTypeIntEnum = PayTypeConvertUtil.xdPayTypeEnumToPayTypeIntEnum(reqParam.getPayType());

        prePayIDTO.setPayType(reqParam.getPayType());
        prePayIDTO.setAppCode(AppCodeEnum.PF.getCode());
        prePayIDTO.setBillCode(reqParam.getOrderCode());
        PrePayODTO prePayODTO = payClient.createPrePayOrder(prePayIDTO);
        PfPrePayResponse response = new PfPrePayResponse();
        BeanUtils.copyProperties(prePayODTO, response);
        response.setPayBillId(prePayODTO.getBillId()+"");
        return response;
    }

    /**
     * 批发支付查询
     * @param reqParam
     * @return
     */
    public QueryODTO tradePfQuery(PfTradeQueryParam reqParam, AppCodeEnum appCode, Consumer<ThirdPartyPayLogEvent> logEventConsumer){
        QueryIDTO queryIDTO = new QueryIDTO();
        BeanUtils.copyProperties(reqParam, queryIDTO);
        queryIDTO.setBillCode(reqParam.getBillCode());
        queryIDTO.setAppCode(appCode.getCode());
        queryIDTO.setPayType(reqParam.getPayType());
        return payClient.orderQuery(queryIDTO);
    }
}
