package com.pinshang.qingyun.order.mapper.bigShop;

import com.pinshang.qingyun.base.mybatis.MyMapper;
import com.pinshang.qingyun.order.dto.bigShop.*;
import com.pinshang.qingyun.order.model.bigShop.DdReceiveDoc;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.util.List;

@Repository
public interface DdReceiveDocMapper extends MyMapper<DdReceiveDoc> {


    List<DdReceiveDocODTO> getReceiveDocList(@Param("idto") DdReceiveDocPageIDTO idto);

    // 获取关联订单号count(未收货)
    List<DdReceiveOrderCountODTO> getOrderCount(@Param("shopId") Long shopId, @Param("orderTime") String orderTime);

    // 获取关联订单号count(已经收货)
    List<DdReceiveOrderCountODTO> getReceiveOrderCount(@Param("docIdList") List<Long> docIdList);

    // 收获记录表
    List<DdReceiveDocCommodityODTO> queryReceiveCommodityLogList(@Param("docId") Long docId);

    // 实时查询待收货订单（sum）
    List<DdReceiveDocCommodityODTO> querySumReceiveOrderCommodityList(@Param("shopId") Long shopId,
                                                                      @Param("stallId") Long stallId,
                                                                      @Param("orderTime") String orderTime,
                                                                      @Param("deliveryBatchList") List<Integer> deliveryBatchList);

    // 关联订单记录表list
    List<DdReceiveDocOrderODTO> queryRelationOrderListLog(@Param("docId") Long docId);

    // 未收货实时查询关联的订单号list
    List<DdReceiveDocOrderODTO> queryWaitReceiveRelationOrderList(@Param("shopId") Long shopId,
                                                                  @Param("stallId") Long stallId,
                                                                  @Param("orderTime") String orderTime,
                                                                  @Param("deliveryBatchList") List<Integer> deliveryBatchList);

    // 收货订单商品明细
    List<DdReceiveOrderCommodityODTO> getOrderCommodityInfo(@Param("shopId") Long shopId,
                                                            @Param("stallId") Long stallId,
                                                            @Param("orderTime") String orderTime,
                                                            @Param("deliveryBatchList") List<Integer> deliveryBatchList,
                                                            @Param("commodityId") Long commodityId);

    // 根据 大店收货单查询收货订单商品明细
    List<DdReceiveOrderCommodityODTO> getOrderListByReceiveDoc(@Param("shopId") Long shopId,
                                                            @Param("stallId") Long stallId,
                                                            @Param("orderTime") String orderTime,
                                                            @Param("deliveryBatchList") List<Integer> deliveryBatchList);

    Integer updateRealReceiveQuantity(@Param("receiveQuantityitem") BigDecimal receiveQuantityitem, @Param("subOrderId") Long subOrderId, @Param("commodityId") Long commodityId);


    Integer batchUpdateRealReceiveQuantity(@Param("list") List<DdReceiveUpdateSubOrderCommodityQuantityDTO> list);


}
