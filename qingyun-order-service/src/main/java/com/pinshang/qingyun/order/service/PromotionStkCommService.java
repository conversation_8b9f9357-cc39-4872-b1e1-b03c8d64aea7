package com.pinshang.qingyun.order.service;

import com.pinshang.qingyun.box.utils.SpringUtil;
import com.pinshang.qingyun.box.utils.VelocityUtils;
import com.pinshang.qingyun.order.dto.cup.ProductPriceDto;
import com.pinshang.qingyun.order.manage.price.CommodityPricePromotionCalculator;
import com.pinshang.qingyun.order.mapper.PromotionStkCommMapper;
import com.pinshang.qingyun.order.mapper.PromotionStkMapper;
import com.pinshang.qingyun.order.model.order.ProductPriceModelList;
import com.pinshang.qingyun.order.model.promotionSale.PromotionSaleComm;
import com.pinshang.qingyun.order.model.promotionStk.PromotionStk;
import com.pinshang.qingyun.order.model.promotionStk.PromotionStkComm;
import com.pinshang.qingyun.order.util.list.ListExtractor;
import com.pinshang.qingyun.order.util.list.ListToMapConverter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Author: sk
 * @Date: 2024/7/18
 */
@Slf4j
@Service
public class PromotionStkCommService {

    @Autowired
    PromotionStkCommMapper promotionStkCommMapper;

    @Autowired
    CommodityService commodityService;

    @Autowired
    ProductPriceModelListService productPriceModelListService;

    @Autowired
    CommodityPricePromotionCalculator commodityPricePromotionCalculator;

    public List<PromotionStkComm> findListByStkCodeId(Long stkCodeId){

        Example example = new Example(PromotionStkComm.class);
        example.createCriteria().andEqualTo("stkCodeId",stkCodeId);
        return promotionStkCommMapper.selectByExample(example);
    }

    public List<PromotionStkComm> listByStkCodeIdList(List<Long> stkCodeIdList){
        if(SpringUtil.isEmpty(stkCodeIdList)){
            return Collections.emptyList();
        }

        Example example = new Example(PromotionStkComm.class);
        example.createCriteria().andIn("stkCodeId",stkCodeIdList);
        return promotionStkCommMapper.selectByExample(example);
    }

    //配货方案 没有价格-> 移除
    public List<PromotionStkComm> processPromotionStkComm(List<PromotionStkComm> list) {
        if (SpringUtil.isEmpty(list)) {
            return null;
        }
        return list.stream().filter(p ->
            null != p.getCommodityPrice() && p.getCommodityPrice().compareTo(BigDecimal.ZERO) > 0
        ).collect(Collectors.toList());
    }


    public void findStkCommodityPrice(String storeId, List<PromotionStkComm> list, String orderTime) {

        if (SpringUtil.isNotEmpty(list)) {
            Long longStoreId = Long.valueOf(storeId);
            List<Long> commdityIdList = ListExtractor.extractToList(list, PromotionStkComm::getCommId);
            Map<Long, BigDecimal> priceModelCommodityPriceMap =productPriceModelListService.getProductPriceModelMap(Long.valueOf(storeId),commdityIdList);


            list.forEach(p -> {
                p.setCommodityPrice(priceModelCommodityPriceMap.get(p.getCommId()));
                p.setOriginalPrice(p.getCommodityPrice());
            });
            //去除查询特价代码，等配货商品添加完成后统一处理
//            Map<String, BigDecimal> pricePromotionCommodityPriceMap = commodityPricePromotionCalculator.calculatePricePromotionCommodityPrice(longStoreId,commdityIdList,orderTime);
//            list.forEach(p -> {
//                BigDecimal pricePromotionCommodityPrice = pricePromotionCommodityPriceMap.get(String.valueOf(p.getCommId()));
//                if(pricePromotionCommodityPrice != null){
//                    p.setCommodityPrice(pricePromotionCommodityPrice);
//                }
//            });

        }
    }




}
