package com.pinshang.qingyun.order.service;

import com.pinshang.qingyun.box.utils.SpringUtil;
import com.pinshang.qingyun.order.enums.CombTypeEnum;
import com.pinshang.qingyun.order.enums.ToBStockTypeEnum;
import com.pinshang.qingyun.order.mapper.OrderMonitorMapper;
import com.pinshang.qingyun.order.mapper.entry.order.OrderItemMonitorEntry;
import com.pinshang.qingyun.order.mapper.entry.order.OrderMonitorEntry;
import com.pinshang.qingyun.order.mapper.entry.order.SubOrderItemMonitorEntry;
import com.pinshang.qingyun.order.mapper.entry.order.SubOrderMonitorEntry;
import com.pinshang.qingyun.storage.dto.CommodityReferInfoODTO;
import com.pinshang.qingyun.storage.service.CommodityReferInfoClient;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2018/10/12 10:59
 */
@Slf4j
@Service
public class OrderMonitorService {
    @Autowired
    private CommodityReferInfoClient commodityReferInfoClient;
    @Autowired
    private OrderMonitorMapper orderMonitorMapper;

    public OrderMonitorEntry queryOrderInfo(String orderCode){
        OrderMonitorEntry orderMonitorEntry = orderMonitorMapper.queryOrderInfo(orderCode);
        if(orderMonitorEntry != null){
            List<OrderItemMonitorEntry> items = orderMonitorEntry.getOrderItem();
            if(SpringUtil.isNotEmpty(items)){
                List<Long> commodityIds = items.stream().map(OrderItemMonitorEntry::getCommodityId).distinct().collect(Collectors.toList());
                List<CommodityReferInfoODTO> referInfoList = commodityReferInfoClient.getCommodityReferInfoList(commodityIds);
                if(SpringUtil.isNotEmpty(referInfoList)){
                    Map<Long,CommodityReferInfoODTO> map = referInfoList.stream().collect(Collectors.toMap(CommodityReferInfoODTO::getId, Function.identity()));
                    items.forEach(x->{
                        CommodityReferInfoODTO referInfoODTO = map.get(x.getCommodityId());
                        x.setWarehouseName(referInfoODTO.getWarehouseName());
                        x.setSupplierName(referInfoODTO.getSupplierName());

                        CombTypeEnum explain = CombTypeEnum.explain(x.getCombType());
                        x.setCombTypeName(null != explain ? explain.getDesc() : "");
                    });
                }
            }
        }
        return orderMonitorEntry;
    }

    public List<SubOrderMonitorEntry> querySubOrderInfo(String orderCode){
        List<SubOrderMonitorEntry> subOrderMonitorEntries = orderMonitorMapper.querySubOrderInfo(orderCode);
        if(subOrderMonitorEntries == null || subOrderMonitorEntries.isEmpty()){
            return null;
        }
        List<Long> commodityIds = new ArrayList<>(subOrderMonitorEntries.size());
        subOrderMonitorEntries.forEach(x->{
            ToBStockTypeEnum stockTypeEnum = ToBStockTypeEnum.getByCode(x.getStockType());
            x.setStockTypeDesc(null != stockTypeEnum ? stockTypeEnum.getValue() : "");

            List<SubOrderItemMonitorEntry> items = x.getSubOrderItem();
            if(items!=null && !items.isEmpty()){
                Long commodityId = items.get(0).getCommodityId();
                commodityIds.add(commodityId);
                x.setCommodityId(commodityId);

                items.forEach(item -> {
                    CombTypeEnum explain = CombTypeEnum.explain(item.getCombType());
                    item.setCombTypeName(null != explain ? explain.getDesc() : "");
                });
            }
        });
//        List<CommodityReferInfoODTO> referInfoList = commodityReferInfoClient.getCommodityReferInfoList(commodityIds
//                                                                    .stream().distinct().collect(Collectors.toList()));
//        Map<Long,CommodityReferInfoODTO> map = referInfoList.stream()
//                                .collect(Collectors.toMap(CommodityReferInfoODTO::getId, Function.identity()));
//        subOrderMonitorEntries.forEach(x->{
//            CommodityReferInfoODTO referInfoODTO = map.get(x.getCommodityId());
//            x.setWarehouseName(referInfoODTO.getWarehouseName());
//            x.setSupplierName(referInfoODTO.getSupplierName());
//        });
        return subOrderMonitorEntries;
    }
}
