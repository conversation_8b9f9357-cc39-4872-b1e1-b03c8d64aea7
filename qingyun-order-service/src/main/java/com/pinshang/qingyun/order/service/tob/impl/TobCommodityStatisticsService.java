package com.pinshang.qingyun.order.service.tob.impl;

import com.pinshang.qingyun.box.utils.DateUtil;
import com.pinshang.qingyun.order.mapper.ProcessOrderCommodityStatisticsMapper;
import com.pinshang.qingyun.order.model.tob.ToBProcessOrderCommodityStatistics;
import com.pinshang.qingyun.order.service.tob.ITobCommodityStatisticsService;
import com.pinshang.qingyun.order.vo.tob.ToBOrderStatisticsReqVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.entity.Example;

import java.util.List;

@Slf4j
@Service
public class TobCommodityStatisticsService implements ITobCommodityStatisticsService {
    @Autowired
    private ProcessOrderCommodityStatisticsMapper orderStatisticsMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateTobOrderStatistics(ToBOrderStatisticsReqVo reqVo) {
        if (reqVo.getType() == 1) {
           addToBOrderStatistics(reqVo);
        }else if (reqVo.getType() == 2){
            subToBOrderStatistics(reqVo);
        }
    }

    private void addToBOrderStatistics(ToBOrderStatisticsReqVo reqVo) {
        ToBProcessOrderCommodityStatistics statistics = ToBProcessOrderCommodityStatistics.initAdd(reqVo);
        Example example = new Example(ToBProcessOrderCommodityStatistics.class);
        example.createCriteria().andEqualTo("orderTime", DateUtil.get4yMd(reqVo.getOrderTime())).andEqualTo("commodityId", reqVo.getCommodityId());
        List<ToBProcessOrderCommodityStatistics> dbList = orderStatisticsMapper.selectByExample(example);
        if(CollectionUtils.isEmpty(dbList)){
            orderStatisticsMapper.insertSelective(statistics);
            return;
        }
        statistics.setId(dbList.get(0).getId());
        orderStatisticsMapper.updateStatistics(statistics);
    }

    private void subToBOrderStatistics(ToBOrderStatisticsReqVo reqVo) {
        Example example = new Example(ToBProcessOrderCommodityStatistics.class);
        example.createCriteria().andEqualTo("orderTime", DateUtil.get4yMd(reqVo.getOrderTime())).andEqualTo("commodityId", reqVo.getCommodityId());
        List<ToBProcessOrderCommodityStatistics> dbList = orderStatisticsMapper.selectByExample(example);
        if(CollectionUtils.isEmpty(dbList)){
            return;
        }
        ToBProcessOrderCommodityStatistics statistics = ToBProcessOrderCommodityStatistics.initSub(reqVo);
        statistics.setId(dbList.get(0).getId());
        int count = orderStatisticsMapper.updateStatistics(statistics);
        if(count == 0){
            log.error("更新B端订单商品统计异常：错误原因-不能扣负。reqVo={},当时的数据库记录Statistics={}",reqVo,dbList.get(0));
        }
    }


}
