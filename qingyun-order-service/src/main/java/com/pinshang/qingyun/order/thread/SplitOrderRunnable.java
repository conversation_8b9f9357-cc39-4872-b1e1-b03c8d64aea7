/*
package com.pinshang.qingyun.order.thread;

import com.alibaba.fastjson.JSON;
import com.pinshang.qingyun.kafka.enums.KafkaMessageOperationTypeEnum;
import com.pinshang.qingyun.order.mapper.entry.splitOrder.SubOrder2DeliveryOrderListEntry;
import com.pinshang.qingyun.order.service.SplitOrderService;
import com.pinshang.qingyun.order.service.WeChatSendMessageService;
import com.pinshang.qingyun.order.vo.splitOrder.SplitOrderKafkaVo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.List;


public class SplitOrderRunnable implements Runnable{
	private Logger logger = LoggerFactory.getLogger(SplitOrderRunnable.class);
	private SplitOrderKafkaVo vo;

	private SplitOrderService splitOrderService;
	private WeChatSendMessageService weChatSendMessageService;

	public SplitOrderRunnable(SplitOrderKafkaVo vo, SplitOrderService splitOrderService, WeChatSendMessageService weChatSendMessageService){
		this.vo =vo;
		this.splitOrderService =splitOrderService;
		this.weChatSendMessageService = weChatSendMessageService;
	}
	
	public SplitOrderRunnable(SplitOrderKafkaVo vo){
		this.vo =vo;
	}
	@Override
	public void run() {
//		 SplitOrderService splitOrderService =(SplitOrderService) SpringBeanFinder.getBean("splitOrderService");
		 if(null ==vo.getOrderId() || null ==vo.getType()){
			 logger.error("接收到拆单消息,数据异常不能处理, {}:", JSON.toJSONString(vo));
			 return;
		 }

		 // 鲜达、生鲜、批发、一期.如果storeTypeId、settleOrderTime为空，重新赋值
		 try{
			 splitOrderService.updateOrderStoreTypeId(vo.getOrderId());
		 }catch (Exception e){
			 logger.error("拆单维护storeTypeId异常, orderId {} 错误信息 {}", vo.getOrderId(), e);
		 }

		 try {
             List<SubOrder2DeliveryOrderListEntry> so2DoList = new ArrayList<>();
			 if (vo.getType().equals(KafkaMessageOperationTypeEnum.INSERT) || vo.getType().equals(KafkaMessageOperationTypeEnum.REPLENISHMENT)) {
                //新增订单或者补货
             	so2DoList = splitOrderService.processInsert(vo);
	         } else if (vo.getType().equals(KafkaMessageOperationTypeEnum.UPDATE)) {
			 	//修改订单
				so2DoList = splitOrderService.processUpdate(vo);
			 } else if (vo.getType().equals(KafkaMessageOperationTypeEnum.CANCEL)) {
			 	//取消订单
                splitOrderService.processCancel(vo);
	         }
			 //2023-09-22，B端订单自动进大仓。这里过了覆盖时间不再处理订单进大仓
//	         if(SpringUtil.isNotEmpty(so2DoList)){
//                 //发消息，处理子单生成发货单
//                 splitOrderService.sendSo2DoKafkaMessage(so2DoList);
//             }
         } catch (Throwable e) {
			 weChatSendMessageService.sendWeChatMessage("处理拆单消息异常,订单id " + vo.getOrderId() + "消息类型 " + vo.getType());
			logger.error("处理拆单消息异常:" + JSON.toJSONString(vo),e);
		}
	}
	
	public SplitOrderKafkaVo getVo() {
		return vo;
	}
	public void setVo(SplitOrderKafkaVo vo) {
		this.vo = vo;
	}
}
*/
