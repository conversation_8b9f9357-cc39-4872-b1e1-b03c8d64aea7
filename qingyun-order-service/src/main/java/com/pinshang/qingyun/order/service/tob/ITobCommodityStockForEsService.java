package com.pinshang.qingyun.order.service.tob;

import com.pinshang.qingyun.order.dto.tob.EsXdaCommoditySoldOutIDTO;
import com.pinshang.qingyun.order.dto.tob.EsXdaCommoditySoldOutODTO;

import java.util.List;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2024/04/24
 * @Version 1.0
 */
public interface ITobCommodityStockForEsService {

    List<Long> selectCommodityIdByStockType(Integer stockType);

    List<EsXdaCommoditySoldOutODTO> queryCommodityInventory(EsXdaCommoditySoldOutIDTO idto);
}
