package com.pinshang.qingyun.order.controller;

import com.pinshang.qingyun.order.mapper.entry.settlement.SettleOrderEntry;
import com.pinshang.qingyun.order.mapper.entry.settlement.SettleOrderItemEntry;
import com.pinshang.qingyun.order.service.SettleOrderService;
import com.pinshang.qingyun.order.vo.settle.OrderReqVo;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/settleOrder")
@Api(value = "宽表订单相关接口", tags = "settleOrder", description = "宽表订单相关接口")
@Slf4j
public class SettleOrderController {

    @Autowired
    private SettleOrderService settleOrderService;

    @RequestMapping(value = "/findOrderByParams", method = RequestMethod.POST)
    public List<SettleOrderEntry> findOrderByParams(@RequestBody OrderReqVo orderReqVo){
        return  settleOrderService.findOrderByParams(orderReqVo);
    }

    @RequestMapping(value = "/findXsZsOrderByParams", method = RequestMethod.POST)
    public List<SettleOrderEntry> findXsZsOrderByParams(@RequestBody OrderReqVo orderReqVo){
        return  settleOrderService.findXsZsOrderByParams(orderReqVo);
    }


    @RequestMapping(value = "/findXsOrderItemByOrderIds", method = RequestMethod.POST)
    public List<SettleOrderItemEntry> findXsOrderItemByOrderIds(@RequestParam(value = "orderIds",required = false) List<Long> orderIds,@RequestParam(value = "logisticsModel",required = false) int logisticsModel){
        return  settleOrderService.findXsOrderItemByOrderIds(orderIds,logisticsModel);
    }


    @RequestMapping(value = "/findTOrderByParams", method = RequestMethod.POST)
    public List<SettleOrderEntry> findTOrderByParams(@RequestBody OrderReqVo orderReqVo){
        return  settleOrderService.findTOrderByParams(orderReqVo);
    }

    @RequestMapping(value = "/findTOrderItemByOrderIds", method = RequestMethod.POST)
    public List<SettleOrderItemEntry> findTOrderItemByOrderIds(@RequestParam(value = "orderIds",required = false) List<Long> orderIds){
        return  settleOrderService.findTOrderItemByOrderIds(orderIds);
    }


}
