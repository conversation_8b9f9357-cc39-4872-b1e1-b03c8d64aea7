package com.pinshang.qingyun.order.controller;

import com.pinshang.qingyun.base.api.TokenInfo;
import com.pinshang.qingyun.base.configure.expand.QYAssert;
import com.pinshang.qingyun.base.page.TablePageInfo;
import com.pinshang.qingyun.base.util.FastThreadLocalUtil;
import com.pinshang.qingyun.box.utils.SpringUtil;
import com.pinshang.qingyun.order.mapper.entry.order.DeliveryBatchEntry;
import com.pinshang.qingyun.order.mapper.entry.order.ProductPriceEntry;
import com.pinshang.qingyun.order.model.order.ShoppingCart;
import com.pinshang.qingyun.order.service.*;
import com.pinshang.qingyun.order.util.ThreadLocalUtils;
import com.pinshang.qingyun.order.vo.commodity.HandCartRspVO;
import com.pinshang.qingyun.order.vo.order.*;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/shoppingcart")
public class ShoppingCartController {
	
	@Autowired
	private ShoppingCartService shoppingCartService;

	@Autowired
	private StoreService  storeService;
	@Autowired
	private ShoppingCartAsyncService shoppingCartAsyncService;
	@Autowired
    private RedissonClient redissonClient;
    @Autowired
    private ConsignmentSupplierService consignmentSupplierService;

	/**
	  * 添加到购物车(pda添加购物车、web端门店添加购物车)
     * @return
     */
    @RequestMapping(value = "/add", method = RequestMethod.POST)
    public List<BStockShortResponseVO> addShoppingCart(@RequestBody ShoppingCartVo vo){
        TokenInfo ti = FastThreadLocalUtil.getQY();
        vo.setCreateId(ti.getUserId());
        vo.setStoreId(ti.getStoreId());
        vo.setEnterpriseId(ti.getEnterpriseId());
        vo.setInternal(ti.getIsInternal());
        consignmentSupplierService.checkStallId(vo);

        List<BStockShortResponseVO> responseVOList = new ArrayList<>();
    	QYAssert.isTrue(null !=vo && null !=vo.getStoreId(), "客户id参数为null");
    	QYAssert.isTrue(null !=vo && null !=vo.getCommodityId(), "商品id参数为null");
    	QYAssert.isTrue(null !=vo && null !=vo.getEnterpriseId(), "企业id参数为null");
        //QYAssert.isTrue(shopClient.ifShopEmployeeCheck(vo.getStoreId()), "请联系门店对应负责人进行完善联系人信息，否则会影响门店订货");

        RLock lock = redissonClient.getLock("ORDER:ADDSHOPPINGCART:" + vo.getStoreId());
        if(lock.tryLock()){
            try {

                Boolean isXd = storeService.getIsXd(ti.getStoreId());
                if(isXd){
                    ThreadLocalUtils.setShoppingCartSource(4);
                }else {
                    ThreadLocalUtils.setShoppingCartSource(5);
                }
                // 门店非代理，校验不能添加代销商商品
                //shoppingCartService.checkAddConsignmentCart(Collections.singletonList(vo.getCommodityId()));

                // 大店校验商品必须是档口下面的商品
                consignmentSupplierService.checkStallCommodity(ti.getShopId(), vo.getStallId(), Collections.singletonList(vo.getCommodityId()));
                shoppingCartService.addShoppingCart(vo);
                ThreadLocalUtils.remove();
            } finally {
                lock.unlock();
            }
        }else {
            QYAssert.isFalse("系统繁忙,请勿频繁操作!");
        }
        return responseVOList;
    }

    /**
     * 批量添加到购物车(web端门店批量添加购物车)
     * @return
     */
    @RequestMapping(value = "/batchAdd", method = RequestMethod.POST)
    public List<BStockShortResponseVO> batchAddShoppingCart(@RequestBody ShoppingCartBatchVo vo){
        TokenInfo ti = FastThreadLocalUtil.getQY();
        vo.setCreateId(ti.getUserId());
        vo.setStoreId(ti.getStoreId());
        vo.setEnterpriseId(ti.getEnterpriseId());
        vo.setInternal(ti.getIsInternal());
        consignmentSupplierService.checkStallId(vo);

        List<BStockShortResponseVO> responseVOList = new ArrayList<>();
        QYAssert.isTrue(null !=vo && null !=vo.getStoreId(), "客户id参数为null");
        QYAssert.isTrue(null !=vo && null !=vo.getList(), "商品参数为null");
        QYAssert.isTrue(null !=vo && null !=vo.getEnterpriseId(), "企业id参数为null");
        QYAssert.isTrue(null !=vo && SpringUtil.isNotEmpty(vo.getList()), "商品列表为空");
        //QYAssert.isTrue(shopClient.ifShopEmployeeCheck(vo.getStoreId()), "请联系门店对应负责人进行完善联系人信息，否则会影响门店订货");

        RLock lock = redissonClient.getLock("ORDER:BATCHADDSHOPPINGCART:" + vo.getStoreId());
        if(lock.tryLock()){
            try {
                Boolean isXd = storeService.getIsXd(vo.getStoreId());
                if(isXd){
                    ThreadLocalUtils.setShoppingCartSource(4);
                }else {
                    ThreadLocalUtils.setShoppingCartSource(5);
                }

                // 门店非代理，校验不能添加代销商商品
                //List<Long> commodityList = vo.getList().stream().map(item -> item.getCommodityId()).collect(Collectors.toList());
                //shoppingCartService.checkAddConsignmentCart(commodityList);

                // 大店校验商品必须是档口下面的商品
                List<Long> commodityIdList = vo.getList().stream().map(item -> item.getCommodityId()).collect(Collectors.toList());
                consignmentSupplierService.checkStallCommodity(ti.getShopId(), vo.getStallId(), commodityIdList);
                shoppingCartService.batchAddShoppingCart(vo);
                ThreadLocalUtils.remove();
            } finally {
                lock.unlock();
            }
        }else {
            QYAssert.isFalse("系统繁忙,请勿频繁操作!");
        }
        return responseVOList;
    }
    
    @RequestMapping(value = "/test", method = RequestMethod.GET)
    public Boolean test(){
        return true;
    }
    
    /**
	  * 删除购物车
    * @return
    */
   @RequestMapping(value = "/remove", method = RequestMethod.POST)
   public Boolean removeShoppingCart(@RequestBody ShoppingCartVo vo){

        return shoppingCartService.removeShoppingCart(vo);
   }

    /**
     * 总部登录、代理购物车列表
     */
    @RequestMapping(value="/detailAdmin", method =RequestMethod.POST)
    public TablePageInfo<ShoppingCartDetailVo> shoppingCartDetailAdmin(@RequestBody ShoppingCartAdminPageVo shoppingCartAdminPageVo){
        ThreadLocalUtils.setAdmin(true);
        TablePageInfo<ShoppingCartDetailVo> shoppingCartPageInfo = shoppingCartService.queryShoppingCartDetailAdmin(shoppingCartAdminPageVo);
        ThreadLocalUtils.remove();
        return shoppingCartPageInfo;
    }
   
   @RequestMapping(value="/detail/{storeId}/{isInternal}/{userId}", method =RequestMethod.GET)
   public ShoppingCartDetailVo shoppingCartDetail(@PathVariable(value="storeId") Long storeId, @PathVariable("isInternal") boolean isInternal, @PathVariable("userId") Long userId){
	   QYAssert.isTrue(null !=storeId, "storeId不能为null");
	   return shoppingCartService.shoppingCartDetail(storeId, isInternal,null,false,userId, null);
   }

    /**
     * 管理员版本 cartdetail
     */
    @RequestMapping(value="/cartdetailAdmin", method =RequestMethod.GET)
    public ShoppingCartDetailVo cartdetailAdmin(@RequestParam(value = "shoppingCartId",required = false) Long shoppingCartId){
        QYAssert.isTrue(null !=shoppingCartId, "购物车ID不能为null");
        ThreadLocalUtils.setAdmin(true);
        ShoppingCartDetailVo vo = shoppingCartService.shoppingCartDetailAdmin(false,shoppingCartId,true,null);
        ThreadLocalUtils.remove();
        return vo;
    }

    /**
     * 手持购物车展示详情(单个)
     * @param storeId
     * @param shoppingCartId
     * handle:true  手持，单个购物车信息
     * @return
     */
    @RequestMapping(value="/cartdetail", method =RequestMethod.GET)
    public ShoppingCartDetailVo cartdetail(@RequestParam(value="storeId",required = false) Long storeId,
                                           @RequestParam(value = "shoppingCartId", required = false) Long shoppingCartId,
                                           @RequestParam(value = "isInternal", required = false) Boolean isInternal,
                                           @RequestParam(value = "orderTime", required = false) String orderTime){
        QYAssert.isTrue(null !=storeId, "storeId不能为null");
        QYAssert.isTrue(null !=shoppingCartId, "购物车ID不能为null");
        return shoppingCartService.shoppingCartDetail(storeId, isInternal, shoppingCartId, true, null, orderTime);
    }

   /*
    * 修改购物车商品数量;
    */
   @RequestMapping(value = "/updateItemQuantity", method = RequestMethod.POST)
   public Boolean updateItemQuantity(@RequestBody ShoppingCartVo vo){
	QYAssert.isTrue(null !=vo && null !=vo.getShoppingCartItemId(), "购物车itemId参数为null");
   	QYAssert.isTrue(null !=vo && null !=vo.getQuantity(), "商品数量参数为null");
   	 return shoppingCartService.updateItemQuantity(vo);
   }


   /*
    * 拆单分组信息返回
    */
   @RequestMapping(value = "/getSplitOrderInfo", method = RequestMethod.POST)
   public Map<Integer, Map<String, List<String>>> getSplitOrderInfo(@RequestBody SplitOrderVo vo){
	QYAssert.isTrue(null !=vo && null !=vo.getCommodityIds(), "商品ids参数不能为null");
   	QYAssert.isTrue(null !=vo && null !=vo.getEnterpriseId(), "企业id参数为null");
   	 return shoppingCartService.getSplitOrderInfo(vo.getCommodityIds(), vo.getEnterpriseId());
   }

    /**
     * 获取价格
     * @return
     */
    @RequestMapping(value = "/getCommodityPrices", method = RequestMethod.POST)
    public Map<String, ProductPriceEntry> getCommodityPrices(@RequestBody RecommandPriceVo recommandPriceVo){
        List<Long> commodityIds =recommandPriceVo.getCommodityIds();
        String orderTime =recommandPriceVo.getOrderTime();
        Long storeId = recommandPriceVo.getStoreId();
        return shoppingCartService.getCommodityPrices(commodityIds,storeId,orderTime);
    }

    /**
     * 根据送货日期，控制可选择的配送批次
     * @param date 送货日期
     * @return
     */
    @GetMapping("/findDeliveryBatch")
	public List<DeliveryBatchEntry> findDeliveryBatch(@RequestParam(value = "storeId",required = false)Long storeId, @RequestParam(value = "date", required = false)String date){
		return shoppingCartService.findDeliveryBatch(storeId, date);
	}

    @GetMapping("/findDeliveryBatchByCartId")
    public List<DeliveryBatchEntry> findDeliveryBatchByCartId(@RequestParam(value = "cartId",required = false)Long cartId, @RequestParam(value = "date",required = false)String date){
        ShoppingCart shoppingCart = shoppingCartService.queryShoppingCartById(cartId);
        QYAssert.isTrue(shoppingCart != null , "购物车不存在,请刷新!");
        return shoppingCartService.findDeliveryBatch(shoppingCart.getStoreId(), date);
    }


    /*
   * (手持)删除购物车
   */
    @RequestMapping(value = "/handleRemoveShoppingCart", method = RequestMethod.POST)
    public HandCartRspVO handleRemoveShoppingCart(@RequestBody ShoppingCartVo vo){
        return shoppingCartService.handleRemoveShoppingCart(vo);
    }

    /**
     * (手持)修改购物车商品数量
     * @return
     */
    @RequestMapping(value = "/handleUpdateItemQuantity", method = RequestMethod.POST)
    public HandCartRspVO handleUpdateItemQuantity(@RequestBody ShoppingCartVo vo){
        return shoppingCartService.handleUpdateItemQuantity(vo);
    }


    /**
     * 处理自动订货购物车、代销商户购物车
     * @return
     */
    @RequestMapping(value = "/dealAutoShoppingCart", method = RequestMethod.POST)
    public Boolean dealAutoShoppingCart(){
        return shoppingCartAsyncService.dealAutoShoppingCart();
    }
}
