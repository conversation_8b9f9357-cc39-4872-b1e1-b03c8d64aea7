package com.pinshang.qingyun.order.service;

import com.pinshang.qingyun.order.mapper.CommodityMonitorMapper;
import com.pinshang.qingyun.order.mapper.entry.commodity.monitor.CommodityDeliveryOrderMonitorEntry;
import com.pinshang.qingyun.order.mapper.entry.commodity.monitor.CommodityOrderMonitorEntry;
import com.pinshang.qingyun.order.mapper.entry.commodity.monitor.CommodityPickOrderMonitorEntry;
import com.pinshang.qingyun.order.mapper.entry.commodity.monitor.CommoditySubOrderMonitorEntry;
import com.pinshang.qingyun.order.vo.commodity.CommodityMonitorRequestVo;
import com.pinshang.qingyun.order.vo.commodity.CommodityMonitorRespVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2018/10/11 10:54
 */
@Service
@Slf4j
public class CommodityMonitorService {
    @Autowired
    private CommodityMonitorMapper commodityMonitorMapper;


    public CommodityMonitorRespVo orderInfo(CommodityMonitorRequestVo vo){
        CommodityMonitorRespVo respVo = new CommodityMonitorRespVo();
        //查询取消数量
        BigDecimal cancelOrderQuantity = commodityMonitorMapper.queryCommodityCancelOrderQuantity(vo);
        respVo.setOrderCancelNum(cancelOrderQuantity);
        //查询订单信息
        CommodityOrderMonitorEntry orderMonitorEntry = commodityMonitorMapper.queryCommodityOrderQuantity(vo);
        if(orderMonitorEntry == null || orderMonitorEntry.getOrderNum().doubleValue() == 0){
            return respVo;
        }
        respVo.setOrderNum(orderMonitorEntry.getOrderNum());
        respVo.setLastOrderTime(orderMonitorEntry.getLastOrderTime());
        //查询子单信息
        List<CommoditySubOrderMonitorEntry> subOrderMonitorEntries = commodityMonitorMapper.queryCommoditySubOrderQuantity(vo);
        if(subOrderMonitorEntries == null || subOrderMonitorEntries.isEmpty()){
            return respVo;
        }
        List<Long> ids = new ArrayList<>(subOrderMonitorEntries.size());
        BigDecimal num = BigDecimal.ZERO;
        for (CommoditySubOrderMonitorEntry entry : subOrderMonitorEntries) {
            ids.add(entry.getId());
            num.add(entry.getQuantity());
        }
        respVo.setSubOrderNum(num);
        respVo.setLastSubOrderTime(subOrderMonitorEntries.get(0).getCreateTime());
        respVo.setSubOrderIds(ids);
        return respVo;
    }
}
