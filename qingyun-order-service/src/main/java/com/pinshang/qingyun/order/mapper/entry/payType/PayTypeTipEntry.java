package com.pinshang.qingyun.order.mapper.entry.payType;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * Created by Ruansi on 2019/08/15.
 */
@Data
@Deprecated
public class PayTypeTipEntry {

    private Long id;
    @ApiModelProperty("支付类型ID")
    private Long paytypeId;
    @ApiModelProperty("促销标语")
    private String tip;
    @ApiModelProperty("标语开启状态，0=停用，1=启用")
    private Integer status;
    @ApiModelProperty("开始时间")
    private Date beginTime;
    @ApiModelProperty("结束时间")
    private Date endTime;
}
