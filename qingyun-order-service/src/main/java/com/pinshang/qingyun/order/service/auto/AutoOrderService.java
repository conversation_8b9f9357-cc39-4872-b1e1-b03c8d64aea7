package com.pinshang.qingyun.order.service.auto;

import com.pinshang.qingyun.base.enums.IogisticsModelEnums;
import com.pinshang.qingyun.base.enums.order.OrderTypeEnum;
import com.pinshang.qingyun.common.dto.DictionaryODTO;
import com.pinshang.qingyun.common.service.DictionaryClient;
import com.pinshang.qingyun.order.dto.AutoSaveOrderIDTO;
import com.pinshang.qingyun.order.dto.ShopAutoOrderODTO;
import com.pinshang.qingyun.order.enums.OrderLaunchTypeEnum;
import com.pinshang.qingyun.order.enums.ProductTypeEnums;
import com.pinshang.qingyun.order.mapper.StoreMapper;
import com.pinshang.qingyun.order.mapper.auto.ShopAutoOrderMapper;
import com.pinshang.qingyun.order.model.auto.AutoOrderLog;
import com.pinshang.qingyun.order.model.store.Store;
import com.pinshang.qingyun.order.service.GrouponOrderSaveService;
import com.pinshang.qingyun.order.service.OrderService;
import com.pinshang.qingyun.order.service.OrderSplitService;
import com.pinshang.qingyun.order.util.ThreadLocalUtils;
import com.pinshang.qingyun.order.vo.order.OrderDto;
import com.pinshang.qingyun.order.vo.order.OrderItemDto;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @Author: sk
 * @Date: 2024/12/4
 */

@Slf4j
@Service
public class AutoOrderService {

    @Autowired
    private OrderSplitService orderSplitService;
    @Autowired
    private OrderService orderService;
    @Autowired
    private StoreMapper storeMapper;
    @Autowired
    private DictionaryClient dictionaryClient;
    @Autowired
    private GrouponOrderSaveService grouponOrderSaveService;
    @Autowired
    private ShopAutoOrderMapper shopAutoOrderMapper;
    private static String SHOP_AUTO_ORDER = "门店自动订货提交订单: ";



    /**
     * 保存自动下单
     * @param autoOrderList
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public Boolean saveAutoOrder(AutoSaveOrderIDTO saveOrderIDTO) {

        List<ShopAutoOrderODTO> autoOrderList = saveOrderIDTO.getAutoOrderList();
        // 转成OrderDto对象
        List<OrderDto> orderDtoList = orderSplitService.convertToOrderDto(autoOrderList, "自动推送", true);

        ThreadLocalUtils.setOrderType(OrderTypeEnum.SHOP_AUTO_ORDER.getCode());
        String autoStr = saveOrderIDTO.getShopId() + "," + saveOrderIDTO.getStoreId() + "," + saveOrderIDTO.getOrderTime();

        try {
            // 循环创建订单
            for(OrderDto orderDto : orderDtoList){
                // 更新一下最新的配置: 商品价格、商品限量、商品是否可变价、商品名称
                orderService.buildProductPrice(orderDto);
                this.createOrder(orderDto, autoStr);
            }
        }finally {
            ThreadLocalUtils.remove();
        }

        return true;
    }


    /**
     * 创建订单
     * @param orderDto
     */
    public Boolean createOrder(OrderDto orderDto, String autoStr)  {

        // 直送订单应不能参与配货/赠品方案
        if (IogisticsModelEnums.DIRECT_SENDING.getCode() != orderDto.getLogisticsModel()) {
            Store store = storeMapper.selectByPrimaryKey(orderDto.getStoreId());
            List<DictionaryODTO> dictionaryList = dictionaryClient.listDictionaryByOptionName("订单配货白名单");
            List<String> storeCodeList = dictionaryList.stream().map(item -> item.getOptionCode()).collect(Collectors.toList());
            // 如果当前客户在配货白名单里面，则不配货
            if(CollectionUtils.isEmpty(storeCodeList) || !storeCodeList.contains(store.getStoreCode())){
                // 3. 配货
                orderService.unifyProcessDistribution(orderDto.getStoreId() + "", orderDto, OrderLaunchTypeEnum.SHOP);
                // 过滤系统商品(赠送、配货商品)
                orderService.filterSystemCommodity(orderDto.getStoreId() + "", orderDto);

                // 配货处理，过滤掉依据大仓和限量的.(此处处理是因为下面的赠品条件是订单商品和配货商品和)
                List<OrderItemDto> rationItemList = orderDto.getItems().stream().filter(item -> ProductTypeEnums.RATION.getCode().equals(item.getType())).collect(Collectors.toList());
                if(CollectionUtils.isNotEmpty(rationItemList)){
                    orderService.giftAndRationDeal(rationItemList, orderDto);
                }

            }
            // 4. 赠送
            orderService.unifyProcessGifts(orderDto.getStoreId() + "", orderDto, OrderTypeEnum.STORE_ORDER);
            // 过滤系统商品(赠送、配货商品)
            orderService.filterSystemCommodity(orderDto.getStoreId() + "", orderDto);

            // 赠品(数量不够，补全当时可用库存)
            List<OrderItemDto> giftItemList = orderDto.getItems().stream().filter(item -> ProductTypeEnums.GIFT.getCode().equals(item.getType()) ).collect(Collectors.toList());
            if(CollectionUtils.isNotEmpty(giftItemList)){
                // 不包含配货，只对订单商品和赠品处理(加上订单商品就是避免订单商品和赠送商品一样。判断赠品库存不准确问题)
                List<OrderItemDto> noRationList = orderDto.getItems().stream().filter(item -> !ProductTypeEnums.RATION.getCode().equals(item.getType())).collect(Collectors.toList());
                orderService.giftAndRationDeal(noRationList, orderDto);
            }
        }

        if(CollectionUtils.isEmpty(orderDto.getItems())){
            log.warn(SHOP_AUTO_ORDER + "有效商品为空，客户id " + orderDto.getStoreId());
            return true;
        }

        grouponOrderSaveService.saveGroupOrder(orderDto, autoStr);

        // 记录日志
        List<AutoOrderLog> autoOrderLogList = new ArrayList<>();
        orderDto.getItems().forEach(i->{
            AutoOrderLog autoOrderLog = new AutoOrderLog();
            autoOrderLog.setStoreId(orderDto.getStoreId());
            autoOrderLog.setCommodityId(Long.valueOf(i.getProductId()));
            autoOrderLog.setQuantity(i.getProductNum());
            autoOrderLog.setCreateId(1L);
            autoOrderLog.setCreateTime(new Date());
            autoOrderLogList.add(autoOrderLog);
        });
        shopAutoOrderMapper.batchInsertAutoOrderLog(autoOrderLogList);
        return Boolean.TRUE;
    }
}
