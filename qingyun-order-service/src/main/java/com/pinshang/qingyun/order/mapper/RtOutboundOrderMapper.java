package com.pinshang.qingyun.order.mapper;

import com.pinshang.qingyun.order.mapper.entry.externalDocking.RtOutboundOrderEntry;
import com.pinshang.qingyun.order.vo.externalDocking.RtOutboundOrderVo;
import org.apache.ibatis.annotations.Mapper;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 大润发出库单
 * <AUTHOR>
 * @date 2023年2月15日10:00:09
 */
@Mapper
@Repository
public interface RtOutboundOrderMapper {

    /**
     * 大润发出库单列表分页查询
     * @param vo
     * @return
     */
    List<RtOutboundOrderEntry> selectRtOutboundOrderList(RtOutboundOrderVo vo);
}
