package com.pinshang.qingyun.order.mapper;

import com.pinshang.qingyun.base.mybatis.MyMapper;
import com.pinshang.qingyun.order.dto.AutoPreOrderGiftItemODTO;
import com.pinshang.qingyun.order.dto.pf.PfCommodityLimit4AppODTO;
import com.pinshang.qingyun.order.dto.pf.PfOrderItem4AppODTO;
import com.pinshang.qingyun.order.dto.xda.XdaCommodityLimit4AppODTO;
import com.pinshang.qingyun.order.dto.xda.XdaOrderItem4AppODTO;
import com.pinshang.qingyun.order.dto.xda.v2.XdaOrderItemAppV2ODTO;
import com.pinshang.qingyun.order.dto.xda.v3.XdaOrderItemAppV3ODTO;
import com.pinshang.qingyun.order.dto.xda.v4.XdaOrderItemAppV4ODTO;
import com.pinshang.qingyun.order.mapper.entry.order.OrderListInfoEntry;
import com.pinshang.qingyun.order.model.order.OrderList;
import com.pinshang.qingyun.order.vo.PfCommodityLimit4AppIDTO;
import com.pinshang.qingyun.order.vo.PfCommodityLimitAppIDTO;
import com.pinshang.qingyun.order.vo.XdaCommodityLimit4AppIDTO;
import com.pinshang.qingyun.order.vo.order.OrderListOrderIdAndCommVO;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.util.List;

@Repository
public interface OrderListMapper extends MyMapper<OrderList> {
    List<OrderList> getGiftedOrStkedNumForShop(@Param("storeId") String storeId, @Param("orderTime")String orderTime,
                                               @Param("commodityIdList")List<Long> commodityIdList, @Param("type")Integer type,
                                               @Param("giftModelOrPromotionId") Long giftModelOrPromotionId);

    List<XdaCommodityLimit4AppODTO> queryCommodityLimit(@Param("orderTime") String orderTime, @Param("commodityIdList")List<Long> commodityIdList,@Param("promotionId")Long promotionId);

    List<XdaCommodityLimit4AppODTO> queryCommodityLimitByStoreId(@Param("orderTime") String orderTime,
                                                                 @Param("storeId") Long storeId,
                                                                 @Param("type") Integer type,
                                                                 @Param("commodityIdList")List<Long> commodityIdList,
                                                                 @Param("promotionId")Long promotionId,
                                                                 @Param("orderId")Long orderId);


    List<XdaCommodityLimit4AppODTO> queryXdaCommodityLimit(@Param("idtoList")  List<XdaCommodityLimit4AppIDTO> idtoList);

    /**
     * 鲜达查询订单明细
     * @param orderIdList
     * @return
     */
    List<XdaOrderItem4AppODTO> queryOrderItem4App(@Param("orderIds") List<Long> orderIdList);

    List<XdaOrderItemAppV2ODTO>  queryOrderItemXdaApp(@Param("orderIds") List<Long> orderIdList);

    List<XdaOrderItemAppV3ODTO>  queryOrderItemXdaAppV3(@Param("orderIds") List<Long> orderIdList);
    List<XdaOrderItemAppV4ODTO>  queryOrderItemXdaAppV4(@Param("orderIds") List<Long> orderIdList);

    /**
     * 批发查询订单明细
     * @param orderIdList
     * @return
     */
    List<PfOrderItem4AppODTO> queryOrderItem4PfApp(@Param("orderIds") List<Long> orderIdList);

    Integer batchUpdateOrderList(@Param("orderUpdateList") List<OrderList> orderUpdateList);

    Integer updateBatchOrderList(@Param("orderUpdateList") List<OrderList> orderUpdateList);

    List<AutoPreOrderGiftItemODTO> findAllByOrderCode(@Param("orderCode") String orderCode);

    /**
     * 查询批发商品限制
     * @param idtoList
     * @return
     */
	List<PfCommodityLimit4AppODTO> queryPfCommodityLimit(@Param("idtoList") List<PfCommodityLimit4AppIDTO> idtoList);
    List<PfCommodityLimit4AppODTO> queryPfCommodityV2Limit(@Param("conditionId") Long conditionId,@Param("idtoList") List<PfCommodityLimitAppIDTO> idtoList);

    List<XdaOrderItemAppV2ODTO> queryOrderCommodityDetailById(@Param("orderId") Long orderId);

    List<XdaOrderItemAppV3ODTO> queryOrderCommodityDetailV3ById(@Param("orderId") Long orderId);

    List<XdaOrderItemAppV3ODTO> queryOrderCommodityDetailV3ByIds(@Param("orderIdList") List<Long> orderIdList);


    List<OrderList> selectOrderListByOrderIdList(@Param("orderIdList") List<Long> orderIdList);

    List<OrderList> queryOrderListByOrderId(@Param("orderId") Long orderId);

    BigDecimal queryRealTotalPrice(@Param("orderId") Long orderId);

    List<OrderListInfoEntry> selectOrderListGiftByOrderIdAndCommodityIdList(@Param("vo")OrderListOrderIdAndCommVO vo,@Param("isGift") boolean isGift);

    List<OrderList> findByOrderIdAndProductType(@Param("orderId")Long orderId,@Param("productType")Integer productType);

}
