package com.pinshang.qingyun.order.controller;

import com.alibaba.excel.EasyExcel;
import com.github.pagehelper.PageInfo;
import com.pinshang.qingyun.base.util.ExcelUtil;
import com.pinshang.qingyun.box.utils.DateUtil;
import com.pinshang.qingyun.box.utils.SpringUtil;
import com.pinshang.qingyun.order.dto.CommodityFreezeGroupIDTO;
import com.pinshang.qingyun.order.dto.CommodityFreezeGroupInsertIDTO;
import com.pinshang.qingyun.order.dto.CommodityFreezeGroupInsertODTO;
import com.pinshang.qingyun.order.dto.CommodityFreezeGroupODTO;
import com.pinshang.qingyun.order.service.CommodityFreezeGroupService;
import com.pinshang.qingyun.renderer.annotation.MethodRender;
import com.pinshang.qingyun.renderer.service.IRenderService;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * @ClassName CommodityController
 * <AUTHOR>
 * @Date 2022/9/29 10:20
 * @Description CommodityController
 * @Version 1.0
 *
 * 迁移至order-manage
 */
@Deprecated
@RestController
@RequestMapping("/commodityFreezeGroup")
public class CommodityFreezeGroupController {

    @Autowired
    private CommodityFreezeGroupService commodityFreezeGroupService;

    @Autowired
    private IRenderService renderService;

    @ApiOperation(value = "分页查询", notes = "分页查询")
    @PostMapping("page")
    @MethodRender
    public PageInfo<CommodityFreezeGroupODTO> page(@RequestBody CommodityFreezeGroupIDTO idto){
        return commodityFreezeGroupService.page(idto);
    }

    @ApiOperation(value = "批量插入", notes = "批量插入")
    @PostMapping("insert")
    public CommodityFreezeGroupInsertODTO insert(@RequestBody CommodityFreezeGroupInsertIDTO idto){
        return commodityFreezeGroupService.insert(idto);
    }

    @ApiOperation(value = "删除", notes = "逐项删除")
    @PostMapping("delete")
    public boolean delete(@RequestParam("id") Long id){
        return commodityFreezeGroupService.delete(id);
    }

    @ApiOperation(value = "导出", notes = "导出")
    @GetMapping("export")
    public void export(CommodityFreezeGroupIDTO idto, HttpServletResponse httpServletResponse){
        idto.setPageNo(1);
        idto.setPageSize(Integer.MAX_VALUE);
        PageInfo<CommodityFreezeGroupODTO> pageInfo = commodityFreezeGroupService.page(idto);
        List<CommodityFreezeGroupODTO> list = pageInfo.getList();
        if(SpringUtil.isEmpty(list)){
            list = new ArrayList<>();
        }else{
            renderService.render(list,"/export");
        }
        try {
            ExcelUtil.setFileNameAndHead(httpServletResponse,"凑整商品设置" + DateUtil.getDateFormate(new Date(), "yyyyMMddHHmmss"));
            EasyExcel.write(httpServletResponse.getOutputStream(),CommodityFreezeGroupODTO.class)
                    .autoCloseStream(Boolean.FALSE).sheet("凑整商品设置").doWrite(pageInfo.getList());;
        } catch (Exception e) {
            e.printStackTrace();
        }

    }
}
