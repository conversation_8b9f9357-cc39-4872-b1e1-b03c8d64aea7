package com.pinshang.qingyun.order.controller;

import com.pinshang.qingyun.order.mapper.entry.store.StoreSettlementEntry;
import com.pinshang.qingyun.order.service.SettlementDetailsReportService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2019/3/11 16:13
 */
@RestController
@RequestMapping("/store/settlement")
public class StoreSettlementController {
    @Autowired
    private SettlementDetailsReportService settlementDetailsReportService;

    @GetMapping("/balance/{storeCode}")
    public StoreSettlementEntry queryBalance(@PathVariable("storeCode") String storeCode){
        return settlementDetailsReportService.queryBalance(storeCode);
    }

    @GetMapping("/findStoreSettleByStoreIds")
    public List<StoreSettlementEntry> findStoreSettleByStoreIds(@RequestBody List<Long> storeIds){
        return settlementDetailsReportService.findStoreSettleByStoreIds(storeIds);
    }
}
