package com.pinshang.qingyun.order.service;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.pinshang.qingyun.base.api.QYApplicationContext;
import com.pinshang.qingyun.base.configure.expand.QYAssert;
import com.pinshang.qingyun.base.enums.*;
import com.pinshang.qingyun.base.enums.order.OrderTypeEnum;
import com.pinshang.qingyun.base.enums.settlement.SettleOrderSourceTypeEnum;
import com.pinshang.qingyun.base.enums.settlement.SettleOrderSourceTypeLogisticsEnum;
import com.pinshang.qingyun.base.enums.xda.TdaOrderProcessStatusEnum;
import com.pinshang.qingyun.base.enums.xda.XdaOrderProcessStatusEunm;
import com.pinshang.qingyun.box.utils.DateUtil;
import com.pinshang.qingyun.box.utils.IDGenerator;
import com.pinshang.qingyun.box.utils.SpringUtil;
import com.pinshang.qingyun.common.dto.DictionaryODTO;
import com.pinshang.qingyun.common.service.DictionaryClient;
import com.pinshang.qingyun.infrastructure.components.IMqSenderComponent;
import com.pinshang.qingyun.infrastructure.components.params.mq.MqMessage;
import com.pinshang.qingyun.kafka.KafkaConstant;
import com.pinshang.qingyun.kafka.MessageOperationType;
import com.pinshang.qingyun.kafka.MessageType;
import com.pinshang.qingyun.kafka.base.KafkaTopicConstant;
import com.pinshang.qingyun.kafka.enums.KafkaMessageOperationTypeEnum;
import com.pinshang.qingyun.kafka.enums.KafkaMessageTypeEnum;
import com.pinshang.qingyun.order.constant.DictionaryCodeConstant;
import com.pinshang.qingyun.order.constant.LockKeyConstant;
import com.pinshang.qingyun.order.constant.RedissKeyConstant;
import com.pinshang.qingyun.order.dto.OrderInfoODTO;
import com.pinshang.qingyun.order.dto.OrderItemMockDTO;
import com.pinshang.qingyun.order.dto.OrderMockDTO;
import com.pinshang.qingyun.order.dto.order.AssociationOrderODTO;
import com.pinshang.qingyun.order.dto.xda.tda.LogisticsCommodityItem;
import com.pinshang.qingyun.order.dto.xda.tda.LogisticsDeliveryMessage;
import com.pinshang.qingyun.order.enums.ProductTypeEnums;
import com.pinshang.qingyun.order.enums.XdaPayTypeEnum;
import com.pinshang.qingyun.order.manage.delivery.context.DeliveryContext;
import com.pinshang.qingyun.order.manage.delivery.dto.DeliveryOrderDTO;
import com.pinshang.qingyun.order.manage.delivery.dto.OrderDeliveryFinishDTO;
import com.pinshang.qingyun.order.manage.factory.StorePriceManageFactory;
import com.pinshang.qingyun.order.mapper.*;
import com.pinshang.qingyun.order.mapper.entry.MdShopOrderSettingEntry;
import com.pinshang.qingyun.order.mapper.entry.order.OrderRealDeliveryFinishEntry;
import com.pinshang.qingyun.order.mapper.entry.order.OrderTODeliveryOrderEntry;
import com.pinshang.qingyun.order.mapper.entry.order.SubOrderItemListEntry;
import com.pinshang.qingyun.order.mapper.entry.order.SubOrderListEntry;
import com.pinshang.qingyun.order.mapper.entry.splitOrder.SubOrder2DeliveryOrderItemEntry;
import com.pinshang.qingyun.order.mapper.entry.splitOrder.SubOrder2DeliveryOrderListEntry;
import com.pinshang.qingyun.order.model.commodity.Commodity;
import com.pinshang.qingyun.order.model.order.*;
import com.pinshang.qingyun.order.model.store.Store;
import com.pinshang.qingyun.order.service.xda.v4.XdaOrderV4Service;
import com.pinshang.qingyun.order.util.OrderUtil;
import com.pinshang.qingyun.order.util.ThreadLocalUtils;
import com.pinshang.qingyun.order.util.list.ListExtractor;
import com.pinshang.qingyun.order.util.list.ListToMapConverter;
import com.pinshang.qingyun.order.vo.OrderMockVO;
import com.pinshang.qingyun.order.vo.kafka.SubOrderRealQtyWrittenBackMessage;
import com.pinshang.qingyun.order.vo.order.*;
import com.pinshang.qingyun.order.vo.subOrder.StockOutJobSubOrderReqVo;
import com.pinshang.qingyun.order.vo.subOrder.StockOutJobSubOrderRespVo;
import com.pinshang.qingyun.order.vo.subOrder.UnDeliverySubOrderJobReqVo;
import com.pinshang.qingyun.product.dto.commodity.CommodityDetailODTO;
import com.pinshang.qingyun.product.service.CommodityClient;
import com.pinshang.qingyun.storage.dto.SubOrder2DeliveryOrderListEntryIDTO;
import com.pinshang.qingyun.storage.dto.order.CreatDeliveryOrderIDTO;
import com.pinshang.qingyun.storage.dto.tob.*;
import com.pinshang.qingyun.storage.service.DeliveryOrderClient;
import com.pinshang.qingyun.storage.service.WarehouseClient;
import com.pinshang.qingyun.storage.service.order.DCOrderClient;
import com.pinshang.qingyun.storage.service.tob.ToBClient;
import com.pinshang.qingyun.store.dto.storeSettlement.StoreSettlementCollectODTO;
import com.pinshang.qingyun.store.service.StoreSettlementNewClient;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.redisson.api.RBucket;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.aop.framework.AopContext;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionSynchronizationAdapter;
import org.springframework.transaction.support.TransactionSynchronizationManager;
import tk.mybatis.mapper.entity.Example;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static java.util.stream.Collectors.groupingBy;
import static java.util.stream.Collectors.toMap;

@SuppressWarnings("deprecation")
@Slf4j
@Service
public class SubOrderService {
    @Autowired
    private OrderMapper orderMapper;
    @Autowired
    private SubOrderMapper subOrderMapper;
    @Autowired
    private DeliveryOrderClient deliveryOrderClient;
    @Autowired
    private WarehouseClient warehouseClient;
    @Autowired
    private SubOrderItemMapper subOrderItemMapper;
    @Lazy
    @Autowired
    private MdShopOrderSettingService mdShopOrderSettingService;
    @Autowired
    private CommonService commonService;
    @Autowired
    private SubOrderConfirmMapper subOrderConfirmMapper;

    @Autowired
    private CommodityMapper commodityMapper;

    @Autowired
    private DictionaryClient dictionaryClient;

    @Autowired
    private StockOutSettleMsgMapper stockOutSettleMsgMapper;

    @Autowired
    private IMqSenderComponent mqSenderComponent;

    @Value("${application.name.switch}")
    private String applicationNameSwitch;
    @Autowired
    private CommodityClient commodityClient;
    @Autowired
    private UserMapper userMapper;
    @Autowired
    private RedissonClient redissonClient;
    @Autowired
    private SubOrderQuantityService subOrderQuantityService;
    @Autowired
    private DictionaryService dictionaryService;
    @Lazy
    @Autowired
    private ShopAutoReceiveService shopAutoReceiveService;
    @Autowired
    private DCOrderClient dcOrderClient;

    @Autowired
    private StoreService storeService;

    @Autowired
    XdaOrderV4Service xdaOrderV4Service;

    @Autowired
    ShopService shopService;

    @Autowired
    OrderListMapper orderListMapper;

    @Autowired
    OrderListGiftMapper orderListGiftMapper;

    @Autowired
    OrderListService orderListService;

    @Autowired
    StorePriceManageFactory storePriceManageFactory;

    @Autowired
    StoreSettlementService storeSettlementService;

    @Autowired
    StoreTypeDescService storeTypeDescService;

    @Autowired
    DeliveryContext deliveryContext;

    @Autowired
    StoreSettlementNewClient storeSettlementNewClient;

    @Autowired
    private StoreRechargeService storeRechargeService;

    @Autowired
    private ToBClient toBClient;
    @Autowired
    private SettleKafkaService settleKafkaService;

    private ThreadPoolExecutor threadPoolExecutor;

    public SubOrderService() {
        this.threadPoolExecutor = new ThreadPoolExecutor(5, 20, 5,TimeUnit.MINUTES, new ArrayBlockingQueue<Runnable>(2000));
    }

    /**
     * 子订单详情
     *
     * @param vo
     * @return
     */
    public List<SubOrderItemListEntry> querySubOrderItemList(SubOrderSearchVo vo) {
        return subOrderMapper.querySubOrderItemList(vo);
    }

    /**
     * 手动生成DO单
     *
     * @param param
     */

    public void creatDeliveryOrder(SubOrderSearchVo param) {
        QYAssert.isTrue(CollectionUtils.isNotEmpty(param.getOrderIds()), "请至少选择一个订单");
        List<Long> orderIdList = param.getOrderIds().stream().distinct().collect(Collectors.toList());
        //业务类型为"B端销售"时，SQL需要过滤物流模式为”配送“或”鲜食客户的直通“
        Map<String, List<Long>> dictionaryMap = new HashMap<>();
//        if (param.getBusinessType() == null || DeliveryOrderTypeEnums.SALE.getCode().equals(param.getBusinessType())) {
//            dictionaryMap = dictionaryService.queryDirectDcConfig();
//        }
        RBucket<Boolean> bucket = redissonClient.getBucket(QYApplicationContext.redisKeyProfile + "DC_TOB_CREATE_DELIVERY");
        Boolean createByDC = bucket.get();
        if (createByDC != null && createByDC) {
            SubOrderService proxy = (SubOrderService) AopContext.currentProxy();
            List<OrderTODeliveryOrderEntry> list = subOrderMapper.findNoDeliveryByOrderIds(orderIdList, param.getWarehouseId(), param.getBusinessType());
            if (SpringUtil.isNotEmpty(list)) {
                proxy.createDeliveryOrderToDC(param.getUserId(), param.getWarehouseId(), DateUtil.getDateFormate(param.getOrderTime(), "yyyy-MM-dd"), list);
            }
        } else {
            try {
                //构建子单及子单明细数据
                List<SubOrder2DeliveryOrderListEntry> subOrderList = this.buildCreateDeliveryOrderData(null, orderIdList, dictionaryMap, param.getWarehouseId(), param.getBusinessType(), param.getUserId());
                if (SpringUtil.isNotEmpty(subOrderList)) {
                    this.createDeliveryOrderByCommon(subOrderList,Boolean.TRUE);
                }
            } catch (Exception e) {
                log.error("手动生成DO单异常", e);
            }
        }
    }

    public Boolean createDeliveryOrderByCommon(List<SubOrder2DeliveryOrderListEntry> list, Boolean isSaveConfirm) {
        Map<Long, List<SubOrder2DeliveryOrderListEntry>> subOrderMap = list.stream().collect(Collectors.groupingBy(SubOrder2DeliveryOrderListEntry::getReferOrderId));
        List<Future<List<Long>>> futureList = new ArrayList<>();
        List<Long> failSubOrderIds = new ArrayList<>();
        for (Map.Entry<Long, List<SubOrder2DeliveryOrderListEntry>> subEntry : subOrderMap.entrySet()) {
            List<SubOrder2DeliveryOrderListEntry> subOrderList = subEntry.getValue();
            Future<List<Long>> future = threadPoolExecutor.submit(new Callable<List<Long>>(){
                @Override
                public List<Long> call() {
                    List<SubOrder2DeliveryOrderListEntryIDTO> entryIDTOList = subOrderList.stream().map(SubOrder2DeliveryOrderListEntry::convert).collect(Collectors.toList());
                    //生成发货单并返回执行失败的子单集合
                    return deliveryOrderClient.subOrder2DeliveryOrder(entryIDTOList);
                }
            });
            futureList.add(future);
        }
        //是否有失败的子单
        Boolean failFlag = Boolean.FALSE;
        for (Future<List<Long>> future : futureList) {
            try {
                List<Long> subOrderIds = future.get();
                if(SpringUtil.isNotEmpty(subOrderIds)){
                    failSubOrderIds.addAll(subOrderIds);
                    failFlag = Boolean.TRUE;
                }
            } catch (Exception e) {
                log.error("获取执行失败的子单ID异常",e);
                failFlag = Boolean.TRUE;
            }
        }
        if(SpringUtil.isNotEmpty(failSubOrderIds) && isSaveConfirm){
            saveSubOrderConfirmList(SubOrderConfirm.initList(failSubOrderIds));
        }
        return failFlag;
    }

    @Transactional(rollbackFor = Exception.class)
    public void createDeliveryOrderToDC(Long userId, Long warehouseId, String orderTime, List<OrderTODeliveryOrderEntry> list) {
        if (SpringUtil.isNotEmpty(list)) {
            try {
                List<CreatDeliveryOrderIDTO> idtos = list.stream().map(l -> {
                    CreatDeliveryOrderIDTO idto = new CreatDeliveryOrderIDTO();
                    BeanUtils.copyProperties(l, idto);
                    idto.setOrderTime(orderTime);
                    idto.setWarehouseId(warehouseId);
                    idto.setUserId(userId);
                    return idto;
                }).collect(Collectors.toList());
                dcOrderClient.creatDeliveryOrderByOrderIds(idtos);
            } catch (Exception e) {
                log.error("生成发货单异常", e);
                List<Long> subOrderIds = list.stream().map(OrderTODeliveryOrderEntry::getSubOrderId).collect(Collectors.toList());
                saveSubOrderConfirmList(SubOrderConfirm.initList(subOrderIds));
            }
        }
    }

    /**
     * 构建子单及子单明细数据，用于生成发货单
     * */
    public List<SubOrder2DeliveryOrderListEntry> buildCreateDeliveryOrderData(List<SubOrder2DeliveryOrderListEntry> subOrderList, List<Long> orderIdList,
                                                                              Map<String, List<Long>> dictionaryMap,Long warehouseId,Integer businessType,Long userId){
        List<SubOrder2DeliveryOrderListEntry> resultList = new ArrayList<>();
        if(SpringUtil.isEmpty(subOrderList) && SpringUtil.isNotEmpty(orderIdList)){
            //获取订单下的所有子单
            subOrderList = subOrderMapper.listSubOrder2DeliveryOrder(orderIdList, warehouseId, businessType);
        }
        if (SpringUtil.isNotEmpty(subOrderList)) {
            //获取所有子单明细
            List<Long> subOrderIdList = subOrderList.stream().map(SubOrder2DeliveryOrderListEntry::getSubOrderId).distinct().collect(Collectors.toList());
            List<SubOrder2DeliveryOrderItemEntry> subOrderItemList = subOrderMapper.listSubOrder2DeliveryOrderItem(subOrderIdList, SubOrderStatusEnums.SUB_ORDER_UNDELIVERY.getCode(), businessType);
            //根据子单进行分组，一个子单对应该子单下的所有子单明细
            Map<Long, List<SubOrder2DeliveryOrderItemEntry>> subOrderItemMap = new HashMap<>();
            if (SpringUtil.isNotEmpty(subOrderItemList)) {
                subOrderItemMap = subOrderItemList.stream().collect(Collectors.groupingBy(SubOrder2DeliveryOrderItemEntry::getSubOrderId));
            }
            //设置子单与子单明细关系
            for (SubOrder2DeliveryOrderListEntry subOrder : subOrderList) {
                List<SubOrder2DeliveryOrderItemEntry> subOrderItems = subOrderItemMap.get(subOrder.getSubOrderId());
                if (SpringUtil.isNotEmpty(subOrderItems)) {
                    subOrder.setItemList(subOrderItems);
                    subOrder.setUserId(userId);
                    resultList.add(subOrder);
                }
            }
        }
        return resultList;
    }

    /**
     * 修改子单状态
     *
     * @param subOrderIdList
     * @param subOrderStatus
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public Integer updateSubOrderStatus(List<Long> subOrderIdList, Integer subOrderStatus) {
        if (CollectionUtils.isEmpty(subOrderIdList) || subOrderStatus == null) {
            return 0;
        }
        SubOrder subOrder = new SubOrder();
        subOrder.setStatus(subOrderStatus);
        Example example = new Example(SubOrder.class);
        example.createCriteria().andIn("id", subOrderIdList);
        if (subOrderStatus != 1) {
            return subOrderMapper.updateByExampleSelective(subOrder, example);
        }
        List<SubOrder> subOrderList = subOrderMapper.selectByExample(example);
        List<Long> unConfirmedIds = new ArrayList<>(subOrderList.size());
        subOrderList.forEach(so -> {
            if (so.getStatus() == 0) {
                unConfirmedIds.add(so.getId());
            }
        });
        if (CollectionUtils.isNotEmpty(unConfirmedIds)) {
            Example updateEx = new Example(SubOrder.class);
            updateEx.createCriteria().andIn("id", unConfirmedIds);
            subOrderMapper.updateByExampleSelective(subOrder, updateEx);
            subOrderMapper.updateOrderProcessStatus(unConfirmedIds, XdaOrderProcessStatusEunm.OUT_STOCKING.convert());
        }
        return 1;
    }


    public List<PickSubOrderItemRespVo> findItemsBySubOrderId(Long subOrderId) {
        return subOrderMapper.findItemsBySubOrderId(subOrderId);
    }

    public int batchUpdateDeliveryQuantity(List<PickSubOrderItemRespVo> deliveryItemList) {
        return subOrderMapper.batchUpdateDeliveryQuantity(deliveryItemList);
    }


    /*
     * 门店调用新增子单
     */
    @Transactional(rollbackFor = Exception.class)
    public Long createSubOrderAndItem(SubOrderAndItemVo subOrderAndItemVo, Boolean isXsJmShop, Map<Long, List<AssociationOrderODTO>> associationOrderMap) {
        SubOrder subOrder = new SubOrder();
        SpringUtil.copyProperties(subOrderAndItemVo, subOrder);
        subOrder.setVarietyTotal(subOrderAndItemVo.getVarietyTotal().intValue());
        Date date = new Date();
        subOrder.setStatus(SubOrderStatusEnums.SUB_ORDER_UNDELIVERY.getCode());
        //由于一个门店订单只生成一个子单,因此直接用orderCode覆盖subOrderCode;
        subOrder.setSubOrderCode(subOrderAndItemVo.getOrderCode());
        subOrder.setUpdateTime(date);
        subOrder.setCreateTime(date);
        subOrderMapper.insert(subOrder);


        List<SubOrderAndItemVo.SubOrderItemVo> items = subOrderAndItemVo.getItems();
        if (null != items && !items.isEmpty()) {

            // 根据商品id和供应商id查询配置
            List<MdShopOrderSettingEntry> mdShopOrderSettingODTOS = this.listMdShopOrderSettingODTOS(items, subOrderAndItemVo);

            List<SubOrderItem> subOrderItems = new ArrayList<SubOrderItem>();
            items.forEach(itemVo -> {
                SubOrderItem subOrderItem = new SubOrderItem();
                subOrderItem.setCommodityId(itemVo.getCommodityId());
                subOrderItem.setCreateId(subOrderAndItemVo.getCreateId());
                subOrderItem.setCreateTime(date);
                subOrderItem.setUpdateTime(date);
                subOrderItem.setPrice(itemVo.getPrice());
                subOrderItem.setQuantity(itemVo.getQuantity());
                subOrderItem.setStatus(SubOrderStatusEnums.SUB_ORDER_UNDELIVERY.getCode());
                subOrderItem.setTotalPrice(itemVo.getTotalPrice());
                subOrderItem.setSubOrderId(subOrder.getId());

                // 团购订单默认不可变价
                // 鲜食加盟默认不可变价(钱大妈)
                boolean isGroupOrder = ThreadLocalUtils.getGroupOrder();
                if (isGroupOrder) {
                    subOrderItem.setChangePriceStatus(YesOrNoEnums.NO.getCode());
                } else {
                    if (itemVo.getChangePriceStatus() == null) {
                        // 查找该商品对应的设置，并设置该订单子项是否可变价
                        List<MdShopOrderSettingEntry> settings = mdShopOrderSettingODTOS.stream()
                                .filter(item -> item.getCommodityId() == itemVo.getCommodityId().longValue())
                                .collect(Collectors.toList());
                        if (CollectionUtils.isEmpty(settings)) {
                            // 默认供应商发生变化后，更新门店订货通用设置默认供应商
                            mdShopOrderSettingService.updateShopOrderSettingSupplier(itemVo.getCommodityId().longValue(), subOrderAndItemVo.getStoreId());
                            Commodity commodity = commodityMapper.selectByPrimaryKey(itemVo.getCommodityId().longValue());
                            QYAssert.isTrue(false, "没有查询到相应的店铺订单设置:" + commodity.getCommodityCode());
                        }
                        //QYAssert.isTrue(settings != null && !settings.isEmpty(), "没有查询到相应的店铺订单设置!"+itemVo.getCommodityId()+"-"+subOrderAndItemVo.getStoreId());
                        Integer changePriceStatus = settings.get(0).getChangePriceStatus();
                        subOrderItem.setChangePriceStatus(changePriceStatus);
                    } else {
                        subOrderItem.setChangePriceStatus(YesOrNoEnums.NO.getCode());
                    }
                }

                subOrderItem.setSourceRatio(itemVo.getSourceRatio());
                subOrderItem.setTargetRatio(itemVo.getTargetRatio());
                subOrderItem.setTargetCommodityId(itemVo.getTargetCommodityId());
                subOrderItem.setTargetQuantity(itemVo.getTargetQuantity());
                subOrderItem.setConvertStatus(itemVo.getConvertStatus());
                subOrderItem.setType(itemVo.getType() == null ? ProductTypeEnums.PRODUCT.getCode() : itemVo.getType());

                // 比较commodityId、type、commodityPrice
                if (associationOrderMap != null && associationOrderMap.containsKey(subOrderItem.getCommodityId())) {
                    List<AssociationOrderODTO> associationOrderList = associationOrderMap.get(subOrderItem.getCommodityId());
                    Optional<AssociationOrderODTO> vo = associationOrderList.stream().filter(p ->
                            p.getType().equals(subOrderItem.getType())
                                    && p.getCommodityPrice().compareTo(subOrderItem.getPrice()) == 0
                                    && !p.getOrderDone()).findFirst();
                    if (vo.isPresent()) {
                        AssociationOrderODTO p = vo.get();
                        subOrderItem.setCombOrderListId(p.getOrderListId());
                        p.setOrderDone(true);
                    }
                }
                subOrderItems.add(subOrderItem);
            });
            subOrderItemMapper.insertList(subOrderItems);
        }
        return subOrder.getId();
    }

    private List<MdShopOrderSettingEntry> listMdShopOrderSettingODTOS(List<SubOrderAndItemVo.SubOrderItemVo> items, SubOrderAndItemVo subOrderAndItemVo) {
        // 查询所有商品是否可变价的设置
        Set<String> setIds = new HashSet<>();
        items.forEach(itemVo -> {
            setIds.add(itemVo.getCommodityId().toString());
        });

        List<String> commodityIds = new ArrayList<>(setIds);
        List<MdShopOrderSettingEntry> mdShopOrderSettingODTOS = mdShopOrderSettingService.queryMdShopOrderSettingListByCommodityIds(commodityIds, subOrderAndItemVo.getStoreId());
        if (CollectionUtils.isEmpty(mdShopOrderSettingODTOS) || mdShopOrderSettingODTOS.size() < commodityIds.size()) {
            List<Long> idList = getCommodityIdList(commodityIds, mdShopOrderSettingODTOS);
            Example ex = new Example(Commodity.class);
            ex.createCriteria().andIn("id", idList);
            List<Commodity> commList = commodityMapper.selectByExample(ex);
            StringBuffer sb = new StringBuffer();
            commList.forEach(comm -> {
                sb.append(comm.getCommodityCode() + " ");
                // 默认供应商发生变化后，更新门店订货通用设置默认供应商
                mdShopOrderSettingService.updateShopOrderSettingSupplier(comm.getId(), subOrderAndItemVo.getStoreId());
            });
            QYAssert.isTrue(false, "没有查询到相应的店铺订单设置~" + sb.toString());
        }
        //QYAssert.isTrue(mdShopOrderSettingODTOS != null && !mdShopOrderSettingODTOS.isEmpty(), "没有查询到相应的店铺订单设置~" + commodityIds + "-" + subOrderAndItemVo.getStoreId());

        return mdShopOrderSettingODTOS;
    }

    /**
     * 获取订货通用设置里面没有的商品idList
     */
    public List<Long> getCommodityIdList(List<String> commodityIds, List<MdShopOrderSettingEntry> mdShopOrderSettingODTOS) {
        List<Long> idList = new ArrayList<>();
        if (CollectionUtils.isEmpty(mdShopOrderSettingODTOS)) {
            commodityIds.forEach(commodityId -> {
                idList.add(Long.valueOf(commodityId));
            });
        } else {
            List<Long> commodityIdList = mdShopOrderSettingODTOS.stream().map(item -> item.getCommodityId()).collect(Collectors.toList());
            commodityIds.forEach(commodityId -> {
                if (!commodityIdList.contains(Long.valueOf(commodityId))) {
                    idList.add(Long.valueOf(commodityId));
                }
            });
        }
        return idList;
    }

    /**
     * 批量更新子订单的实际出库数量和实际收货数量.
     * 实际收货数量 = 发货数量 = 实际拣货数量, 因为门店嫌收货后再次清点数量和页面上再输入实际收货数量
     * 或者我们实际发了10个,门店说只收到8个的尴尬情况. 这里直接帮门店设置了实际收货数量,就是等于实际的发货数量
     * 不考虑中间运输产生的消耗或者其它情况,产品需求如此
     * 参数是多个子订单和子订单的明细组成的list
     * 此方法只适用于非批次仓库的批量更新, 因为用户下单的时候,可能会有配货(配货的意思就是系统强加给用户的商品,比如用户只买了5个鸡蛋,
     * 满足了后台设置的配货方案,则会给用户多加几个鸡蛋, 用户在收到货的时候可能是7个,或者8个.具体看配货方案里设置的数量),
     * 配货或者赠品的商品,是出现2条的.一个订单比如有鸡蛋7个,用户买了5个,实际赠送或者配货了2个. 那么鸡蛋在用户订单明细里面,是2条
     * 在非批次拣货的时候,生成的拣货单也是2条. 如果是批次仓库,生成的拣货单, 只有一条记录, 需要去手支分配数量.
     * 比如批次仓库拣货单鸡蛋7个,只有一条, 但是子单里面,是2条. 需要手动去分,分给第一个5个,第二个2个.
     * 如果实际拣货只有5个,那么就分配第一个5个,第二个0个,这是另外的逻辑.此方法不处理这种逻辑
     *
     * @return 成功返回true
     * 注意：同一个订单可能发送多次，但同个订单里面的同个商品只会发送一次
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean batchUpdateDeliveryQuantityV2(PickSubOrderVo paramSubOrder, Long orderId) {
        // 获取子订单id
        Long subOrderId = paramSubOrder.getSubOrderId();

        // 订单回填实发数量优先级。特价、组合商品、正常品、特惠品(鲜达)、促销配货、赠品
        // 1.批量更新t_sub_order_item,t_order、t_order_list、t_order_list_gift 实发数量,实发总金额
        // 2.标记子单已回填实发数量和实发金额
        // 3.更新订单实发总金额
        // 4.如果子单全部实发，更新realSubOrderDoneStatus = 1
        subOrderQuantityService.updateOrderRealQuantity(paramSubOrder, orderId);

        //实时多发： 预付 & 【【客户类型： 鲜食加盟】 || 【批发】】
        //实时少发： 预付 & 【【【（结算类型：B端-判断订单与实发，最小值结算 || 客户类型： 鲜食加盟）】 + 结算时处理短交： 否】 || 【批发】】
        processDeliveryChange(Collections.singletonList(subOrderId));


        // 1.结算发送kafka消息(可以发消息的直接发消息。不可以发消息的先保存起来,然后job再发送)
        // 2.B订单实发补偿：如果订单日期小于发货当前日期,则表示晚于订单日发货，就要进行补偿
        // 3.事务完成将subOrderIds 放入队列，凌晨进行自动收货
        TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronizationAdapter() {
            @Override
            public void afterCommit() {
                // 结算发送kafka消息(可以发消息的直接发消息。不可以发消息的先保存起来,然后job再发送)
                sendSettleMsgOrJobSend(Collections.singletonList(paramSubOrder), Collections.singletonList(subOrderId));

                // B订单实发补偿：如果订单日期小于发货当前日期,则表示晚于订单日发货，就要进行补偿
                realDeliveryAfterNowThenFix(subOrderId);

                // 事务完成将subOrderIds 放入队列，凌晨进行自动收货
                shopAutoReceiveService.pushSubOrderIdToQueue(Collections.singletonList(subOrderId));
            }
        });
        return true;
    }

    private void orderDeliveryProcessIfNeeded(Map<Long, OrderDeliveryFinishDTO> prePayAccountCandidateOrderDeliveryFinishMap) {
        if (SpringUtil.isNotEmpty(prePayAccountCandidateOrderDeliveryFinishMap)) {
            //将所有预付客户的订单按照多发和少发金额分别计算，最终生成所有订单对应的中间类DeliveryOrderDTO，包含多发金额和少发金额
            List<DeliveryOrderDTO> orderDeliveryFinishDTOList = orderListService.calculateDeliveryOrderMoney(prePayAccountCandidateOrderDeliveryFinishMap);
            //将上一步处理获得的订单中间类（包含订单的多发、少发金额、客户、客户类型等信息）通过策略上下文处理多发短交
            deliveryContext.executeStrategy(orderDeliveryFinishDTOList);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public SubOrder querySubOrderBySubOrderId(Long subOrderId) {
        return subOrderMapper.selectByPrimaryKey(subOrderId);
    }

    /**
     * 通达销售的订单，需要经过物流系统进行揽收，物流揽收完成将实发数量反写到订单
     *
     * @param dto
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean updateTdaDeliveryQuantity(LogisticsDeliveryMessage dto) {
        //Order order = orderMapper.selectByPrimaryKey(dto.getOrderId());
        Example subOrderExample = new Example(SubOrder.class);
        subOrderExample.createCriteria().andEqualTo("orderId", dto.getOrderId());
        List<SubOrder> subOrderList = subOrderMapper.selectByExample(subOrderExample);
        if (CollectionUtils.isEmpty(subOrderList)) {
            log.warn("subOrderList为空, orderId {}", dto.getOrderId());
            return false;
        }
        List<SubOrder> filterList = subOrderList.stream().filter(p -> p.getWritebackRealQtyFlag() != null && p.getWritebackRealQtyFlag()).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(filterList)) {
            log.warn("通达销售的订单已回填实发数量，无需再次回填 orderId {}", dto.getOrderId());
            return false;
        }

        //方法参数赋值
        List<SubOrderItem> subOrderItemList = new ArrayList<>();
        List<PickSubOrderVo> paramSubOrderList = new ArrayList<>();
        if (Objects.equals(dto.getProcessStatus(), TdaOrderProcessStatusEnum.REAL_DELIVERY_EMPTY.getCode())) {
            //处理大仓实发全是0的消息
            processDeliveryEmptyMessage(dto, subOrderItemList);
        } else {
            //适配成和非通达一样的参数，共用更新实发方法
            paramSubOrderList = tdaBusinessAdapterOrginalParams(dto, subOrderItemList);
            batchUpdateOrderRealQuantity(paramSubOrderList);
        }

        // 结算发送kafka消息
        sendTdaSettleMsgOrJobSend(dto);
        return true;
    }

    private List<PickSubOrderVo> tdaBusinessAdapterOrginalParams(LogisticsDeliveryMessage dto, List<SubOrderItem> subOrderItems) {

        //适配非通达入参 LogisticsDeliveryMessage->List<PickSubOrderVo>
        List<PickSubOrderVo> paramSubOrderList = Lists.newArrayList();
        Long orderId = dto.getOrderId();
        List<LogisticsCommodityItem> complaintItemList = dto.getComplaintItemList();
        List<SubOrderItem> dbSubOrderItems = subOrderItemMapper.queryByOrderId(orderId);
        subOrderItems.addAll(dbSubOrderItems);
        //Map<Long(SubOrderId),List<SubOrderItem>>
        Map<Long, List<SubOrderItem>> subOrderItemMap = subOrderItems.stream().collect(groupingBy(SubOrderItem::getSubOrderId));
        subOrderItemMap.forEach((key, value) -> {
            PickSubOrderVo pickSubOrderVo = new PickSubOrderVo();
            pickSubOrderVo.setSubOrderId(key);
            pickSubOrderVo.setStoreId(dto.getStoreId());
            pickSubOrderVo.setStockOutTime(dto.getDeliveryDate());
            pickSubOrderVo.setStockOutOrderCode(dto.getWaybillCode());
            pickSubOrderVo.setIsSendSettle(true);
            pickSubOrderVo.setIsTda(true);
            List<PickSubOrderItemRespVo> items = Lists.newArrayList();

            List<SubOrderItem> subOrderItemList = value;
            List<Long> commodityIdList = subOrderItemList.stream().map(SubOrderItem::getCommodityId).distinct().collect(Collectors.toList());
            complaintItemList.forEach(
                    complaintItem -> {
                        if (commodityIdList.contains(complaintItem.getCommodityId())) {
                            PickSubOrderItemRespVo vo = new PickSubOrderItemRespVo();
                            vo.setCommodityId(complaintItem.getCommodityId());
                            vo.setRealDeliveryQuantity(complaintItem.getRealDeliveryQuantity());
                            items.add(vo);
                        }
                    }
            );
            pickSubOrderVo.setItemList(items);
            paramSubOrderList.add(pickSubOrderVo);
        });

        return paramSubOrderList;
    }

    private void processDeliveryEmptyMessage(LogisticsDeliveryMessage dto, List<SubOrderItem> subOrderItemList) {

        Long orderId = dto.getOrderId();
        List<SubOrderItem> dbSubOrderItems = subOrderItemMapper.queryByOrderId(orderId);
        subOrderItemList.addAll(dbSubOrderItems);
        subOrderItemList.forEach(
                subOrderItem -> {
                    subOrderItem.setRealDeliveryQuantity(BigDecimal.ZERO);
                }
        );
        if (CollectionUtils.isNotEmpty(subOrderItemList)) {
            subOrderMapper.batchUpdateCombDeliveryQuantity(subOrderItemList);
        }

        List<OrderList> orderLists = orderListMapper.queryOrderListByOrderId(orderId);
        orderLists.forEach(
                orderList -> {
                    orderList.setRealQuantity(BigDecimal.ZERO);
                    orderList.setRealTotalPrice(BigDecimal.ZERO);
                }
        );
        if (CollectionUtils.isNotEmpty(orderLists)) {
            orderListMapper.batchUpdateOrderList(orderLists);
        }

        List<OrderListGift> orderListGifts = orderListGiftMapper.queryOrderGiftListByOrderId(orderId);
        orderListGifts.forEach(
                orderListGift -> {
                    orderListGift.setRealQuantity(BigDecimal.ZERO);
                    orderListGift.setRealTotalPrice(BigDecimal.ZERO);
                }
        );
        if (CollectionUtils.isNotEmpty(orderListGifts)) {
            orderListGiftMapper.batchUpdateOrderGiftList(orderListGifts);
        }

        List<Long> subOrderIds = subOrderItemList.stream().map(SubOrderItem::getSubOrderId).distinct().collect(Collectors.toList());
        //markWriteBackRealQtyFlag(subOrderIds);

        PickSubOrderVo pickSubOrderVo = new PickSubOrderVo();
        pickSubOrderVo.setIsTda(Boolean.TRUE);
        pickSubOrderVo.setStockOutTime(dto.getDeliveryDate());
        pickSubOrderVo.setSubOrderIds(subOrderIds);
        // 1.标记子单已回填实发数量和实发金额
        // 2.更新订单实发总金额
        // 3.如果子单全部实发，更新realSubOrderDoneStatus = 1 . 订单所有子单是否出库完成（不包括直送类型） 等于1-完成
        subOrderQuantityService.updateOrderInfo(pickSubOrderVo, dto.getOrderId());
    }


    /**
     * 结算发送kafka消息(可以发消息的直接发消息。不可以发消息的先保存起来,然后job再发送)
     *
     * @param paramSubOrderList
     * @param subOrderIds
     */
    private void sendSettleMsgOrJobSend(List<PickSubOrderVo> paramSubOrderList, List<Long> subOrderIds) {
        // 结算发送kafka消息(可以发消息的直接发消息，不可以发消息的先保存起来)
        DictionaryODTO changePriceFinalHour = dictionaryClient.getDictionaryByCode("ChangePriceFinalHour");
        // 根据subOrder获取是否可变价
        List<PickSubOrderVo> allowSendMessageList = new ArrayList<>();
        List<OrderChangePriceVo> orderList = orderMapper.findOrderBySubOrderId(subOrderIds);
        Map<Long, OrderChangePriceVo> subOrderIdAndObjMap = orderList.stream().collect(toMap(OrderChangePriceVo::getSubOrderId, Function.identity()));
        for (PickSubOrderVo pickSubOrderVo : paramSubOrderList) {
            OrderChangePriceVo orderChangePriceVo = subOrderIdAndObjMap.get(pickSubOrderVo.getSubOrderId());
            Date stockOutTime = pickSubOrderVo.getStockOutTime();
            boolean flag = OrderUtil.compareHHmmss(stockOutTime, orderChangePriceVo.getOrderTime(), Integer.parseInt(changePriceFinalHour.getOptionValue()));
            if (YesOrNoEnums.YES.getCode().equals(orderChangePriceVo.getChangePriceStatus()) && flag
                    && paramSubOrderList.get(0).getIsSendSettle()) {
                // 可变订单 && 出库时间 <= 今天的22:00:00 (出库时间yyyy-MM-dd < 下单时间yyyy-MM-dd，出库时间 HH:mm:ss <= 22:00:00 比较)
                // 今天的22:00:00 后 触发 stockOutSettleMsgJobHandler 发送结算消息
                StockOutSettleMsg stockOutMsgLog = new StockOutSettleMsg();
                stockOutMsgLog.setSubOrderId(pickSubOrderVo.getSubOrderId());
                stockOutMsgLog.setStockOutOrderCode(pickSubOrderVo.getStockOutOrderCode());
                stockOutMsgLog.setStockOutTime(pickSubOrderVo.getStockOutTime());
                stockOutMsgLog.setOptionType(pickSubOrderVo.getOptionType());
                stockOutMsgLog.setStatus(0);
                stockOutMsgLog.setCreateTime(new Date());
                stockOutSettleMsgMapper.insert(stockOutMsgLog);
            } else {
                allowSendMessageList.add(pickSubOrderVo);
            }
        }

        if (SpringUtil.isNotEmpty(paramSubOrderList) && paramSubOrderList.get(0).getIsSendSettle()) {
            // 给结算发消息，需要排除的订单类型
            List<Long> excludeOrderTypeList =  dictionaryService.getExcludeOrderTypeList();

            sendSettleKafkaMsg(allowSendMessageList, excludeOrderTypeList);

            // 子单全部实发再发送消息
            settleKafkaService.sendSettleKafkaMsg(allowSendMessageList, excludeOrderTypeList);
        }
    }

    /**
     * 通达结算发送kafka消息(可以发消息的直接发消息。不可以发消息的先保存起来,然后job再发送)
     *
     * @param dto
     */
    private void sendTdaSettleMsgOrJobSend(LogisticsDeliveryMessage dto) {
        // 结算发送kafka消息
        List<Long> subOrderIdsByOrderId = subOrderMapper.findSubOrderIdbyOrderId(dto.getOrderId());

        // 给结算发消息，需要排除的订单类型
        List<Long> excludeOrderTypeList =  dictionaryService.getExcludeOrderTypeList();

        sendTdaSettleKafkaMsg(subOrderIdsByOrderId, dto, excludeOrderTypeList);

        // 子单全部实发再发送消息
        settleKafkaService.sendTdaSettleKafkaMsg(subOrderIdsByOrderId, dto, excludeOrderTypeList);

    }


    /**
     * B订单实发补偿：如果订单日期小于发货当前日期,则表示晚于订单日发货，就要进行补偿
     *
     * @param dbItems
     */
    private void realDeliveryAfterNowThenFix(Long subOrderId) {
        // 找到这些子订单对应的子订单明细, 主要是子单明细的id, 商品id
        List<PickSubOrderItemRespVo> dbItems = subOrderMapper.findItemsBySubOrderIds(Collections.singletonList(subOrderId));
        if (CollectionUtils.isNotEmpty(dbItems)) {
            List<Long> subOrderIdList = new ArrayList<>();
            for (PickSubOrderItemRespVo vo : dbItems) {
                if (vo.getOrderTime().getTime() < DateUtil.getNowDate().getTime()) {
                    subOrderIdList.add(vo.getSubOrderId());
                }
            }
            if (CollectionUtils.isNotEmpty(subOrderIdList)) {
                // 发送消息，qingyun-report 接收消息处理
                // 发送kafka消息
                TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronizationAdapter() {
                    @Override
                    public void afterCommit() {
                        List<OrderInfoODTO> orderInfoList = subOrderMapper.getOrderInfoList(subOrderIdList);
                        if (CollectionUtils.isNotEmpty(orderInfoList)) {
                            // 设置商品车间生产组
                            setCommodityInfo(orderInfoList);

                            // KafkaMessageWrapper message = new KafkaMessageWrapper(KafkaMessageTypeEnum.B_ORDER_FIX, orderInfoList, KafkaMessageOperationTypeEnum.UPDATE );
                            mqSenderComponent.send(applicationNameSwitch + KafkaTopicConstant.B_ORDER_FIX_TOPIC,
                                    orderInfoList,
                                    MqMessage.MQ_KAFKA,
                                    KafkaMessageTypeEnum.B_ORDER_FIX.name(),
                                    KafkaMessageOperationTypeEnum.UPDATE.name());
                        }
                    }
                });
            }
        }
    }

    /***
     * 1：(2023-06-25)此功能作用 子单出库完成 标识t_order表 real_sub_order_done_status=1
     *
     *   需求 @see http://192.168.0.213/zentao/story-view-8527.html
     *   获取 子单是否都发货完成, 子单发货完成业务需填充对应订单的（子单发货完成标识t_order表real_sub_order_done_status字段：值等于1-所有子单发货完 不包括直送类型）
     *   查询订单拆单数量（不包括直送类型：t_sub_order表logistics_model=0，直送类型大仓不发送发货消息）及实际大仓发货数
     *   实际大仓发货数=根据t_sub_order表字段writeback_real_qty_flag该字段等于1 时从而知道子订单在大仓发货并发送发货消息：topic=PICK_UPDATE_SUB_ORDER_QUANTITY_TOPIC 来判断拆分的子单是否都发货
     *
     * 2：(2024-03-19)此功能作用 子单出库完成 短交还款
     *   需求 @see http://192.168.0.213/zentao/story-view-10047.html
     *
     * 3:(2024-07-31)新增短交退款多发扣款
     */
    @Transactional(rollbackFor = Exception.class)
    public Map<Long, OrderRealDeliveryFinishEntry> markWriteBackRealSubOrderDoneStatus(List<Long> subOrderIds) {

        /*** 根据拆单子单 查出订单 */
        List<Long> orderIdList = subOrderMapper.findOrderIdListBySubOrderIds(subOrderIds);
        if (SpringUtil.isEmpty(orderIdList)) {
            return null;
        }

        /*** 查出订单的出库是否完成 */
        List<OrderRealDeliveryFinishEntry> deliveryFinishEntryList = subOrderMapper.findOrderRealDeliveryFinishList(orderIdList);
        if (SpringUtil.isEmpty(deliveryFinishEntryList)) {
            return null;
        }

        deliveryFinishEntryList.forEach(
                deliveryFinishEntry -> {
                    if (deliveryFinishEntry.getOrderStoreTypeId() != null) {
                        deliveryFinishEntry.setStoreTypeId(deliveryFinishEntry.getOrderStoreTypeId());
                    }
                }
        );

        Map<Long, OrderRealDeliveryFinishEntry> orderMap = new HashMap<>();
        deliveryFinishEntryList.stream().forEach(sub -> {
            if (sub.getRealSubOrderDoneStatus() != null && sub.getRealSubOrderDoneStatus().intValue() == 1) {
                return;
            }

            if (sub.getSubNum() != null && sub.getRealFinishNum() != null && sub.getSubNum().compareTo(sub.getRealFinishNum()) == 0) {
                Example orderExample = new Example(Order.class);
                orderExample.createCriteria().andEqualTo("id", sub.getOrderId()).andIsNull("realSubOrderDoneStatus");
                Order order = new Order();
                order.setRealSubOrderDoneStatus(1);
                int num = orderMapper.updateByExampleSelective(order, orderExample);
                if (num > 0) {
                    orderMap.put(sub.getOrderId(), sub);
                }
            }
        });

        if (SpringUtil.isEmpty(orderMap)) {
            return null;
        }

        return orderMap;
    }

    @Transactional(rollbackFor = Exception.class)
    public void handleShotDelivery(LogisticsDeliveryMessage dto) {
        Long orderId = dto.getOrderId();
        List<Long> subOrderIds = subOrderMapper.findSubOrderIdbyOrderId(orderId);
        processDeliveryChange(subOrderIds);
    }

    /**
     *实时多发： 预付 & 【【客户类型： 鲜食加盟】 || 【批发】】
     *实时少发： 预付 & 【【【（结算类型：B端-判断订单与实发，最小值结算 || 客户类型： 鲜食加盟）】 + 结算时处理短交： 否】 || 【批发】】
     * @param subOrderIds
     */
    private void processDeliveryChange(List<Long> subOrderIds) {

        //根据subOrderIdList查询出对应的订单，然后筛选出所有子单都已完成出库的正常订单，这些订单为候选订单，进行下一步的处理
        List<OrderDeliveryFinishDTO> candidateOrderDeliveryFinishList = pickCandidateOrders(subOrderIds);
        if (SpringUtil.isEmpty(candidateOrderDeliveryFinishList)) {
            return;
        }

        //将上一步的候选订单中客户为非预付的排除，得到订单中所有storeId是预付的订单进行下一步处理
        List<OrderDeliveryFinishDTO> prePayAccountCandidateOrders = filterNonePrePayAccountOrders(candidateOrderDeliveryFinishList);
        if (SpringUtil.isEmpty(prePayAccountCandidateOrders)) {
            return;
        }

        //包装订单中的storeTypeId，如果订单中不存在，则获取storeId对应的storeTypeId，用于后续短交多发的客户类型判断的依据
        wrapOrderStoreTypeId(prePayAccountCandidateOrders);

        //是否需要处理短交退款，多发扣款
        orderDeliveryProcessIfNeeded(generateOrderDeliveryMap(prePayAccountCandidateOrders));
    }

    private Map<Long, OrderDeliveryFinishDTO> generateOrderDeliveryMap(List<OrderDeliveryFinishDTO> prePayAccountCandidateOrders) {

        Map<Long, OrderDeliveryFinishDTO> ret = new HashMap<>();
        prePayAccountCandidateOrders.stream().forEach(sub -> {
            ret.put(sub.getOrderId(), sub);
        });
        return ret;
    }

    private List<OrderDeliveryFinishDTO> filterNonePrePayAccountOrders(List<OrderDeliveryFinishDTO> list) {

        List<Long> storeIds = ListExtractor.extractToList(list, OrderDeliveryFinishDTO::getStoreId);
        List<StoreSettlementCollectODTO> storeSettlementInfo = storeSettlementNewClient.listStoreSettlementByStoreIds(storeIds);
        Map<Long, Boolean> storeSettlementInfoMap = ListToMapConverter.convertListToMap(storeSettlementInfo, StoreSettlementCollectODTO::getStoreId, StoreSettlementCollectODTO::getCollectStatus);

        Iterator<OrderDeliveryFinishDTO> iterator = list.iterator();
        while (iterator.hasNext()) {
            OrderDeliveryFinishDTO orderDeliveryFinishDto = iterator.next();
            Long storeId = orderDeliveryFinishDto.getStoreId();
            Boolean prePayStoreFlag = storeSettlementInfoMap.get(storeId);
            if (prePayStoreFlag == null || !prePayStoreFlag.booleanValue()) {
                iterator.remove();
            }
        }
        return list;
    }

    private List<OrderDeliveryFinishDTO> pickCandidateOrders(List<Long> subOrderIds) {
        List<Long> orderIdList = subOrderMapper.findOrderIdListBySubOrderIds(subOrderIds);
        if (SpringUtil.isEmpty(orderIdList)) {
            return null;
        }

        List<OrderDeliveryFinishDTO> orderDeliveryFinishList = orderMapper.findOrderDeliveryFinishList(orderIdList);
        if (SpringUtil.isEmpty(orderDeliveryFinishList)) {
            return null;
        }

        return orderDeliveryFinishList;
    }

    private void wrapOrderStoreTypeId(List<OrderDeliveryFinishDTO> orderDeliveryFinishList) {
        orderDeliveryFinishList.forEach(
                deliveryFinishEntry -> {
                    if (deliveryFinishEntry.getOrderStoreTypeId() != null) {
                        deliveryFinishEntry.setStoreTypeId(deliveryFinishEntry.getOrderStoreTypeId());
                    }
                }
        );
    }

    @Transactional(rollbackFor = Exception.class)
    public void handleRefund(LogisticsDeliveryMessage dto, String remark, Integer billType) {

        Order order = orderMapper.selectByPrimaryKey(dto.getOrderId());
        if (OperateTypeEnums.删除.getCode().equals(order.getOrderStatus())
                || XdaOrderProcessStatusEunm.DELIVERY_FAIL.getCode() == order.getProcessStatus()) {
            log.warn("订单状态为已取消或配送失败，无需退款，订单号：{}", order.getOrderCode());
            return;
        }
        if (!needHandleRefund(order)) {
            return;
        }

        xdaOrderV4Service.saveOrderDeductions(order, order.getStoreId(), XdaPayTypeEnum.PAY_BACK, remark, billType);
    }

    private boolean needHandleRefund(Order order) {

        Boolean storeCollectStatus = storeSettlementService.selectCollectStatus(order.getStoreId()); //账号类型=预付费客户

        /***业务类型通达销售时，且账号类型=预付费客户*/
        boolean prePaidFlag = (storeCollectStatus != null && storeCollectStatus.booleanValue());
        boolean tdaStoreFlag = DeliveryOrderTypeEnums.TD_SALE.getCode().equals(order.getBusinessType());

        return prePaidFlag && tdaStoreFlag;

    }

    private Long wrapStoreTypeIdIfNull(Order order) {
        Long storeTypeId = order.getStoreTypeId();
        if (storeTypeId == null) {
            Store storeByStoreId = storeService.findStoreByStoreId(order.getStoreId());
            storeTypeId = storeByStoreId.getStoreTypeId();
        }
        return storeTypeId;
    }


    /**
     * 发送回填实发数量消息
     *
     * @param pickSubOrders
     */
    private void sendRealQtyWritenWrittenBack(List<PickSubOrderVo> pickSubOrders) {
        List<Long> subOrderIds = pickSubOrders.stream().map(PickSubOrderVo::getSubOrderId).collect(Collectors.toList());
        Example exp = new Example(SubOrder.class);
        exp.createCriteria()
                .andIn("id", subOrderIds);
        List<SubOrder> subOrderDtos = subOrderMapper.selectByExample(exp);
        Map<Long, Long> subOrderIdMapOrderId = subOrderDtos.stream()
                .collect(Collectors.toMap(SubOrder::getId, SubOrder::getOrderId, (s1, s2) -> s2));

        List<SubOrderRealQtyWrittenBackMessage> msgs = new ArrayList<>();
        for (PickSubOrderVo subOrder : pickSubOrders) {
            SubOrderRealQtyWrittenBackMessage msg = new SubOrderRealQtyWrittenBackMessage();
            msg.setOrderId(subOrderIdMapOrderId.get(subOrder.getSubOrderId()));
            msg.setSubOrderId(subOrder.getSubOrderId());
            List<SubOrderRealQtyWrittenBackMessage.Item> items = new ArrayList<>();
            for (PickSubOrderItemRespVo i : subOrder.getItemList()) {
                SubOrderRealQtyWrittenBackMessage.Item item = new SubOrderRealQtyWrittenBackMessage.Item();
                item.setCommodityId(i.getCommodityId());
                item.setRealDeliveryQuantity(i.getRealDeliveryQuantity());
            }
            msg.setItems(items);
            msgs.add(msg);
        }

        for (SubOrderRealQtyWrittenBackMessage data : msgs) {
            MqMessage msg = new MqMessage();
            msg.setTopic(KafkaTopicConstant.ORDER_LIST_REAL_QUANTITY_UPDATED_TOPIC);
            msg.setData(data);
            msg.setKeyId(data.getOrderId().toString());
            mqSenderComponent.send(msg);
        }
    }


    private void markWriteBackRealQtyFlag(List<Long> subOrderIds) {
        Example batchUpdateWriteBackRealQtyFlagTrueExample = new Example(SubOrder.class);
        batchUpdateWriteBackRealQtyFlagTrueExample.createCriteria()
                .andIn("id", subOrderIds);
        SubOrder batchUpdateWriteBackRealQtyFlagTrueRecord = new SubOrder();
        batchUpdateWriteBackRealQtyFlagTrueRecord.setWritebackRealQtyFlag(true);
        subOrderMapper.updateByExampleSelective(batchUpdateWriteBackRealQtyFlagTrueRecord, batchUpdateWriteBackRealQtyFlagTrueExample);
    }

    @Transactional(rollbackFor = Exception.class)
    public void markTdaWriteBackRealQtyFlag(LogisticsDeliveryMessage dto) {
        List<Long> subOrderIds = subOrderMapper.findSubOrderIdbyOrderId(dto.getOrderId());
        if (CollectionUtils.isNotEmpty(subOrderIds)) {
            Example batchUpdateWriteBackRealQtyFlagTrueExample = new Example(SubOrder.class);
            batchUpdateWriteBackRealQtyFlagTrueExample.createCriteria()
                    .andIn("id", subOrderIds);
            SubOrder batchUpdateWriteBackRealQtyFlagTrueRecord = new SubOrder();
            batchUpdateWriteBackRealQtyFlagTrueRecord.setWritebackRealQtyFlag(true);
            subOrderMapper.updateByExampleSelective(batchUpdateWriteBackRealQtyFlagTrueRecord, batchUpdateWriteBackRealQtyFlagTrueExample);
        }

    }

    /**
     * // 设置商品车间生产组
     *
     * @param orderInfoList
     */
    public void setCommodityInfo(List<OrderInfoODTO> orderInfoList) {
        if (CollectionUtils.isNotEmpty(orderInfoList)) {


            List<Long> commodityIdList = orderInfoList.stream().map(item -> item.getCommodityId()).collect(Collectors.toList());

            List<CommodityDetailODTO> commodityList = commodityClient.findCommodityInfoByIdList(commodityIdList);
            Map<String, CommodityDetailODTO> commMap = commodityList.stream().collect(Collectors.toMap(CommodityDetailODTO::getCommodityId, Function.identity()));

            Set<Long> dictionaryIdSet = new HashSet<>();
            for (CommodityDetailODTO commodityDetailODTO : commodityList) {
                dictionaryIdSet.add(Long.parseLong(commodityDetailODTO.getCommodityUnitId()));
            }
            Map<String, com.pinshang.qingyun.renderer.client.dto.DictionaryODTO> dictionaryMap = commonService.listDictionarysByIdSet(dictionaryIdSet);

            List<Long> createIdList = orderInfoList.stream().map(item -> item.getCreateId()).collect(Collectors.toList());
            List<User> userList = userMapper.getEmployeeUserByUserId(createIdList);
            Map<Long, User> userMap = userList.stream().collect(Collectors.toMap(User::getUserId, Function.identity()));

            for (OrderInfoODTO odto : orderInfoList) {
                CommodityDetailODTO commodityDetailODTO = commMap.get(odto.getCommodityId() + "");
                if (commodityDetailODTO != null) {
                    //BeanUtils.copyProperties(commodityDetailODTO,odto);
                    odto.setWorkshopCode(commodityDetailODTO.getWorkshopCode());
                    odto.setWorkshopName(commodityDetailODTO.getWorkshopName());
                    odto.setFactoryCode(commodityDetailODTO.getFactoryCode());
                    odto.setFactoryName(commodityDetailODTO.getFactoryName());
                    odto.setCommodityUnitId(commodityDetailODTO.getCommodityUnitId());
                }
                odto.setCommodityUnitName(null != dictionaryMap.get(odto.getCommodityUnitId()) ? dictionaryMap.get(odto.getCommodityUnitId() + "").getOptionName() : "");

                User user = userMap.get(odto.getCreateId());
                if (user != null) {
                    odto.setRealName(user.getEmployeeName());
                }
            }
        }
    }

    /**
     * 批量更新 t_sub_order_item,t_order、t_order_list、t_order_list_gift 实发数量,实发总金额
     *
     * @param paramSubOrderList
     */
    @Transactional(rollbackFor = Exception.class)
    public void batchUpdateOrderRealQuantity(List<PickSubOrderVo> paramSubOrderList) {
        for (PickSubOrderVo pickSubOrderVo : paramSubOrderList) {
            SubOrder subOrder = subOrderMapper.selectByPrimaryKey(pickSubOrderVo.getSubOrderId());
            if (subOrder == null) {
                continue;
            }
            subOrderQuantityService.updateOrderRealQuantity(pickSubOrderVo, subOrder.getOrderId());
        }
    }


    public void sendSettleKafkaMsg(List<PickSubOrderVo> paramSubOrderList, List<Long> excludeOrderTypeList) {
        if (CollectionUtils.isEmpty(paramSubOrderList)) {
            return;
        }
        // 鲜食店(配送、直通类型), 注意：像totalAmount和deliveryTotalAmount除了大仓发货这一种情况两个值不一样，其它都完全一样
        String sendMqTime = DateUtil.getDateFormate(new Date(), DateUtil.DEFAULT_DATE_FORMAT);
        for (PickSubOrderVo subOrderVo : paramSubOrderList) {
            Long subOrderId = subOrderVo.getSubOrderId();
            String stockOutOrderCode = subOrderVo.getStockOutOrderCode();
            List<OrderVo> orderVoList = subOrderMapper.listOrderDetail(subOrderId, excludeOrderTypeList);
            if (SpringUtil.isEmpty(orderVoList)) {
                continue;
            }

            // 计算totalAmount和deliveryTotalAmount 单价 * 数量
            for (OrderVo orderVo : orderVoList) {
                orderVo.setStockOutOrderCode(stockOutOrderCode);
                BigDecimal totalAmount = BigDecimal.ZERO;
                BigDecimal deliveryTotalAmount = BigDecimal.ZERO;
                List<ItemVo> items = orderVo.getItems();
                if (SpringUtil.isEmpty(items)) {
                    continue;
                }
                for (ItemVo itemVo : items) {
                    BigDecimal totalPrice = itemVo.getUnitPrice().multiply(itemVo.getNumber()).setScale(2, BigDecimal.ROUND_HALF_UP);
                    totalAmount = totalAmount.add(totalPrice);
                    BigDecimal deliveryTotalPrice = itemVo.getDeliveryUnitPrice().multiply(itemVo.getDeliveryNumber()).setScale(2, BigDecimal.ROUND_HALF_UP);
                    deliveryTotalAmount = deliveryTotalAmount.add(deliveryTotalPrice);
                }

                orderVo.setTotalAmount(totalAmount);
                orderVo.setDeliveryTotalAmount(deliveryTotalAmount);
                orderVo.setDeliveryTime(subOrderVo.getStockOutTime());
                orderVo.setSendMqTime(sendMqTime);

                // 如果是鲜达订单，则 sourcetype 标注为鲜达类型
                if (OrderTypeEnum.XDA_APP_ORDER.getCode().equals(orderVo.getOrderType())) {
                    orderVo.setSourceType("XDA_ORDER");
                } else if (OrderTypeEnum.GROUP_AUTO_ORDER.getCode().equals(orderVo.getOrderType())) {
                    orderVo.setSourceType(SettleOrderSourceTypeLogisticsEnum.TJ_ORDER.name());
                }

                // 是否代销订单
                if (orderVo.getConsignmentId() != null && orderVo.getConsignmentId() > 0) {
                    orderVo.setSourceType(SettleOrderSourceTypeEnum.DX_ORDER.name());
                }

                // 如果是大店
                /*if(BusinessTypeEnums.BIGSHOP_SALE.getCode().equals(orderVo.getBusinessType())) {
                    orderVo.setSourceType("DD_ORDER");
                }*/
                commonService.sendKafkaMessage(orderVoList, MessageType.SETTLE_ORDER_SYNC, KafkaConstant.SETTLE_ORDER_ALL_TOPIC, subOrderVo.getOptionType());
            }
        }
    }

    public void sendTdaSettleKafkaMsg(List<Long> subOrderIds, LogisticsDeliveryMessage dto, List<Long> excludeOrderTypeList) {
        if (CollectionUtils.isEmpty(subOrderIds)) {
            return;
        }
        // 鲜食店(配送、直通类型), 注意：像totalAmount和deliveryTotalAmount除了大仓发货这一种情况两个值不一样，其它都完全一样
        String sendMqTime = DateUtil.getDateFormate(new Date(), DateUtil.DEFAULT_DATE_FORMAT);
        String waybillCode = dto.getWaybillCode();
        for (Long subOrderId : subOrderIds) {

            List<OrderVo> orderVoList = subOrderMapper.listOrderDetail(subOrderId, excludeOrderTypeList);
            if (SpringUtil.isEmpty(orderVoList)) {
                continue;
            }

            // 计算totalAmount和deliveryTotalAmount 单价 * 数量
            for (OrderVo orderVo : orderVoList) {
                //出库单号取运单号
                orderVo.setStockOutOrderCode(waybillCode);
                BigDecimal totalAmount = BigDecimal.ZERO;
                BigDecimal deliveryTotalAmount = BigDecimal.ZERO;
                List<ItemVo> items = orderVo.getItems();
                if (SpringUtil.isEmpty(items)) {
                    continue;
                }
                for (ItemVo itemVo : items) {
                    BigDecimal totalPrice = itemVo.getUnitPrice().multiply(itemVo.getNumber()).setScale(2, BigDecimal.ROUND_HALF_UP);
                    totalAmount = totalAmount.add(totalPrice);
                    BigDecimal deliveryTotalPrice = itemVo.getDeliveryUnitPrice().multiply(itemVo.getDeliveryNumber()).setScale(2, BigDecimal.ROUND_HALF_UP);
                    deliveryTotalAmount = deliveryTotalAmount.add(deliveryTotalPrice);
                }

                orderVo.setTotalAmount(totalAmount);
                orderVo.setDeliveryTotalAmount(deliveryTotalAmount);
                //订单送货时间取送货日期
                orderVo.setDeliveryTime(dto.getDeliveryDate());
                orderVo.setSendMqTime(sendMqTime);

                // 如果是鲜达订单，则 sourcetype 标注为鲜达类型
                if (OrderTypeEnum.XDA_APP_ORDER.getCode().equals(orderVo.getOrderType())) {
                    orderVo.setSourceType("XDA_ORDER");
                } else if (OrderTypeEnum.GROUP_AUTO_ORDER.getCode().equals(orderVo.getOrderType())) {
                    orderVo.setSourceType(SettleOrderSourceTypeLogisticsEnum.TJ_ORDER.name());
                }

                // 是否代销订单
                if (orderVo.getConsignmentId() != null && orderVo.getConsignmentId() > 0) {
                    orderVo.setSourceType(SettleOrderSourceTypeEnum.DX_ORDER.name());
                }
                commonService.sendKafkaMessage(orderVoList, MessageType.SETTLE_ORDER_SYNC, KafkaConstant.SETTLE_ORDER_ALL_TOPIC, MessageOperationType.UPDATE.getCode());
            }
        }
    }


    /**
     * 出库消息记录表(是否发结算消息) job
     *
     * @param beginTime yyyy-MM-dd HH:mm:ss
     * @param endTime   yyyy-MM-dd HH:mm:ss
     */
    @Transactional(rollbackFor = Exception.class)
    public void stockOutSettleMsg(String beginTime, String endTime) {

        // 查询出库消息记录信息
        List<StockOutSettleMsg> stockOutSettleMsgList = stockOutSettleMsgMapper.selectstockOutSettleMsgByTime(beginTime, endTime);
        if (CollectionUtils.isEmpty(stockOutSettleMsgList)) {
            return;
        }

        // 给结算发消息，需要排除的订单类型
        List<Long> excludeOrderTypeList =  dictionaryService.getExcludeOrderTypeList();

        // 发送结算消息
        List<PickSubOrderVo> paramSubOrderList = new ArrayList<>();
        for (StockOutSettleMsg log : stockOutSettleMsgList) {
            PickSubOrderVo vo = new PickSubOrderVo();
            vo.setSubOrderId(log.getSubOrderId());
            vo.setStockOutOrderCode(log.getStockOutOrderCode());
            vo.setStockOutTime(log.getStockOutTime());
            vo.setOptionType(log.getOptionType());
            paramSubOrderList.add(vo);
        }

        sendSettleKafkaMsg(paramSubOrderList, excludeOrderTypeList);

        // 子单全部实发再发送消息
        settleKafkaService.sendSettleKafkaMsg(paramSubOrderList, excludeOrderTypeList);

        // 批量更新为已发送
        List<Long> idList = stockOutSettleMsgList.stream().map(item -> item.getId()).collect(Collectors.toList());
        StockOutSettleMsg stockOutSettleMsg = new StockOutSettleMsg();
        stockOutSettleMsg.setStatus(1);
        stockOutSettleMsg.setUpdateTime(new Date());
        Example updateExample = new Example(StockOutSettleMsg.class);
        updateExample.createCriteria().andIn("id", idList);
        stockOutSettleMsgMapper.updateByExampleSelective(stockOutSettleMsg, updateExample);
    }

    public List<StockOutJobSubOrderRespVo> getStockOutSubOrderList(StockOutJobSubOrderReqVo reqVo) {
        if (CollectionUtils.isEmpty(reqVo.getIds())) {
            return new ArrayList<>();
        }
        return subOrderMapper.getStockOutSubOrderList(reqVo);
    }

    public PageInfo<SubOrderListEntry> queryUnDeliverySubOrderListV2(SubOrderVo param) {
        if (param.getBusinessType() == null || DeliveryOrderTypeEnums.SALE.getCode().equals(param.getBusinessType())) {
            Map<String, List<Long>> dictionaryMap = dictionaryService.queryDirectDcConfig();
            param.init(dictionaryMap);
        }
        //        if (pageInfo != null && CollectionUtils.isNotEmpty(pageInfo.getList())) {
//            List<Long> warehouseIdList = pageInfo.getList().parallelStream().mapToLong(SubOrderListEntry::getWarehouseId).boxed().distinct().collect(Collectors.toList());
//            Map<Long, WarehouseODto> warehouseMap = warehouseClient.queryWarehouseListByIds(warehouseIdList);
//            pageInfo.getList().forEach(entry -> {
//                WarehouseODto warehouseODto = warehouseMap.get(entry.getWarehouseId());
//                if (warehouseODto != null) {
//                    entry.setWarehouseName(warehouseODto.getWarehouseName());
//                }
//                Integer deliveryOrderType = param.getBusinessType() != null && DeliveryOrderTypeEnums.TD_SALE.getCode().equals(param.getBusinessType()) ?
//                        DeliveryOrderTypeEnums.TD_SALE.getCode() : DeliveryOrderTypeEnums.SALE.getCode();
//                entry.setDeliveryOrderType(deliveryOrderType);
//            });
//        }
        return PageHelper.startPage(param.getPageNo(), param.getPageSize()).doSelectPageInfo(() -> subOrderMapper.queryUnDeliverySubOrderList(param));
    }

    public void unDeliverySubOrderJob(UnDeliverySubOrderJobReqVo param) {
//        Map<String, List<Long>> dictionaryMap = dictionaryService.queryDirectDcConfig();
        if (DeliveryOrderTypeEnums.SALE.getCode().equals(param.getBusinessType())) {
//            param.init(dictionaryMap);
            param.initCurrentTime();
        }
        Map<String, List<Long>> dictionaryMap = new HashMap<>();
        RBucket<Boolean> bucket = redissonClient.getBucket(QYApplicationContext.redisKeyProfile + "DC_TOB_CREATE_DELIVERY");
        Boolean createByDC = bucket.get();
        if (createByDC != null && createByDC) {
            //查询过截单时间未执行发货的订单id（非鲜达）
            List<OrderTODeliveryOrderEntry> orderList = subOrderMapper.queryUnDeliverySubOrderIdsJob(param);
            //查询过截单时间未执行发货的订单id（鲜达）
            List<OrderTODeliveryOrderEntry> xdaOrderList = new ArrayList<>();
            if (DeliveryOrderTypeEnums.SALE.getCode().equals(param.getBusinessType())) {
                xdaOrderList = subOrderMapper.queryUnDeliverySubOrderIdsXDAJob(param);
            }

            if (SpringUtil.isNotEmpty(orderList) || SpringUtil.isNotEmpty(xdaOrderList)) {
                if (SpringUtil.isNotEmpty(orderList) && SpringUtil.isNotEmpty(xdaOrderList)) {
                    xdaOrderList.addAll(orderList);
                } else if (SpringUtil.isNotEmpty(orderList)) {
                    xdaOrderList = orderList;
                }
                Map<String, List<OrderTODeliveryOrderEntry>> map = xdaOrderList.stream().collect(groupingBy(e -> Optional.ofNullable(e.getWarehouseId()).orElse(-1L) + Optional.ofNullable(e.getOrderTime()).orElse("unknown")));
                List<Long> shiftStoreTypeIds = new ArrayList<>();
                List<Long> delShiftStoreTypeIds = new ArrayList<>();
                SubOrderService proxy = (SubOrderService) AopContext.currentProxy();
                map.forEach((k, v) -> {
                    Long warehouseId = v.get(0).getWarehouseId();
                    String orderTime = v.get(0).getOrderTime();
                    RLock lock = redissonClient.getLock(LockKeyConstant.createDeliveryOrderLock(warehouseId,orderTime));
                    try {
                        if (lock.tryLock(30L, TimeUnit.SECONDS)) {
                            List<Long> tmpShiftStoreTypeIds = v.stream().map(OrderTODeliveryOrderEntry::getShiftStoreTypeId).filter(Objects::nonNull).distinct().collect(Collectors.toList());
                            try {
                                proxy.createDeliveryOrderToDC(1L, warehouseId, orderTime, v);
                                if (SpringUtil.isNotEmpty(tmpShiftStoreTypeIds)) {
                                    shiftStoreTypeIds.addAll(tmpShiftStoreTypeIds);
                                }
                            } catch (Exception e) {
                                log.error("自动执行生成发货单失败,orderId:{}", k, e);
                                if (SpringUtil.isNotEmpty(tmpShiftStoreTypeIds)) {
                                    delShiftStoreTypeIds.addAll(tmpShiftStoreTypeIds);
                                }
                            } finally {
                                lock.unlock();
                            }
                        }
                    } catch (Exception e) {
                        log.error("自动执行生成发货单,锁异常", e);
                    }
                    if (SpringUtil.isNotEmpty(delShiftStoreTypeIds)) {
                        shiftStoreTypeIds.removeAll(delShiftStoreTypeIds);
                    }
                    if (SpringUtil.isNotEmpty(shiftStoreTypeIds)) {
                        subOrderMapper.batchUpdateShiftStoreType(shiftStoreTypeIds, param.getCurrentTime());
                    }
                });
            }
        } else {
            //查询过截单时间未执行发货的订单信息（非鲜达）
            List<SubOrder2DeliveryOrderListEntry> orderList = subOrderMapper.queryUnDeliverySubOrderJobList(param);
            //查询过截单时间未执行发货的订单信息（鲜达）
            List<SubOrder2DeliveryOrderListEntry> xdaOrderList = new ArrayList<>();
            if (DeliveryOrderTypeEnums.SALE.getCode().equals(param.getBusinessType())) {
                xdaOrderList = subOrderMapper.queryUnDeliverySubOrderXDAJobList(param);
            }
            //数据组装后，调用生成发货单的方法
            if (SpringUtil.isNotEmpty(orderList) || SpringUtil.isNotEmpty(xdaOrderList)) {
                if (SpringUtil.isNotEmpty(orderList) && SpringUtil.isNotEmpty(xdaOrderList)) {
                    xdaOrderList.addAll(orderList);
                } else if (SpringUtil.isNotEmpty(orderList)) {
                    xdaOrderList = orderList;
                }

                Map<String, List<SubOrder2DeliveryOrderListEntry>> resultMap = new HashMap<>();
                Map<String, String> tmpMap = new HashMap<>();
                for (SubOrder2DeliveryOrderListEntry item : xdaOrderList) {
                    String key = item.getReferOrderId() + "_" + item.getSubOrderId();
                    if (!tmpMap.containsKey(key)) {
                        String resultKey = item.getWarehouseId() + "_" + DateUtil.getDateFormate(item.getOrderTime(),"yyyy-MM-dd");
                        if (resultMap.containsKey(resultKey)) {
                            resultMap.get(resultKey).add(item);
                        } else {
                            resultMap.put(resultKey, new ArrayList<SubOrder2DeliveryOrderListEntry>() {{ add(item); }});
                        }
                        tmpMap.put(key, key);
                    }
                }
                try {
                    List<Long> shiftStoreTypeIds = new ArrayList<>();
                    List<Long> delShiftStoreTypeIds = new ArrayList<>();
                    for (Map.Entry<String, List<SubOrder2DeliveryOrderListEntry>> entry : resultMap.entrySet()) {
                        String[] keys = entry.getKey().split("_");
                        Long warehouseId = Long.valueOf(keys[0]);
                        String orderTime = keys[1];
                        RLock lock = redissonClient.getLock(LockKeyConstant.createDeliveryOrderLock(warehouseId,orderTime));
                        if (lock.tryLock(30L, TimeUnit.SECONDS)) {
                            List<Long> tmpShiftStoreTypeIds = entry.getValue().stream().filter(i -> i.getShiftStoreTypeId() != null).map(o -> o.getShiftStoreTypeId()).distinct().collect(Collectors.toList());
                            try {
                                List<SubOrder2DeliveryOrderListEntry> subOrderList = this.buildCreateDeliveryOrderData(entry.getValue(),null,dictionaryMap,Long.valueOf(warehouseId),param.getBusinessType(),1L);
                                if (SpringUtil.isNotEmpty(subOrderList)) {
                                    Boolean failFlag = this.createDeliveryOrderByCommon(subOrderList,Boolean.TRUE);
                                    if (failFlag) {
                                        delShiftStoreTypeIds.addAll(tmpShiftStoreTypeIds);
                                    }else{
                                        shiftStoreTypeIds.addAll(tmpShiftStoreTypeIds);
                                    }
                                }
                            } catch (Exception e) {
                                log.error("自动执行生成发货单失败,仓库ID_送货时间:{}", entry.getKey(), e);
                                if (SpringUtil.isNotEmpty(tmpShiftStoreTypeIds)) {
                                    delShiftStoreTypeIds.addAll(tmpShiftStoreTypeIds);
                                }
                            } finally {
                                lock.unlock();
                            }
                        }
                    }

                    //因为，鲜达查询过截单时间未执行发货的订单信息的sql,使用到“t_shift_store_type_id”表中的“最新生成发货单时间（delivery_execute_time）”字段
                    //所以，切换的鲜达客户订单可能处于多个仓库中，有一个子单生成失败，“t_shift_store_type_id”表中的“最新生成发货单时间（delivery_execute_time）”都不能更新
                    if (SpringUtil.isNotEmpty(delShiftStoreTypeIds)) {
                        for (Long delShiftStoreTypeId : delShiftStoreTypeIds) {
                            if (shiftStoreTypeIds.contains(delShiftStoreTypeId)) {
                                shiftStoreTypeIds.remove(delShiftStoreTypeId);
                            }
                        }
                    }
                    if (SpringUtil.isNotEmpty(shiftStoreTypeIds)) {
                        subOrderMapper.batchUpdateShiftStoreType(shiftStoreTypeIds, param.getCurrentTime());
                    }
                } catch (Exception e) {
                    log.error("自动执行生成发货单,锁异常", e);
                }
            }
        }
    }

    private void saveSubOrderConfirmList(List<SubOrderConfirm> subOrderConfirmList) {
        if (subOrderConfirmList.isEmpty()) {
            return;
        }
        List<Long> subOrderIds = subOrderConfirmList.stream().map(SubOrderConfirm::getSubOrderId).collect(Collectors.toList());
        Example example = new Example(SubOrderConfirm.class);
        example.createCriteria().andIn("subOrderId", subOrderIds);
        List<SubOrderConfirm> existList = subOrderConfirmMapper.selectByExample(example);
        if (existList.isEmpty()) {
            subOrderConfirmMapper.insertList(subOrderConfirmList);
        } else {
            List<Long> existSubOrderIds = existList.stream().map(SubOrderConfirm::getSubOrderId).collect(Collectors.toList());
            subOrderConfirmList.removeIf(s -> existSubOrderIds.contains(s.getSubOrderId()));
            if (!subOrderConfirmList.isEmpty()) {
                subOrderConfirmMapper.insertList(subOrderConfirmList);
            }
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public OrderMockVO createMockOrder(OrderMockDTO dto) {
        Store store = storeService.findStoreByStoreId(dto.getStoreId());
        QYAssert.isTrue(store != null, "客户不存在!");
        //创建订单
        Order order = new Order(dto);
        order.setEnterpriseId(78L);
        Long userId = dto.getCreateId() == null ? 0L : dto.getCreateId();
        order.setCreateId(userId);
        order.setUpdateId(userId);
        order.setCreateTime(new Date());
        order.setUpdateTime(new Date());
        order.setOrderCode(IDGenerator.newOrderCode());
        orderMapper.insertSelective(order);
        Integer type = order.getBusinessType() == null ? DeliveryOrderTypeEnums.SALE.getCode() : order.getBusinessType();
        //查库存
        CommodityInventoryIDTO commodityInventoryIDTO=new CommodityInventoryIDTO();
        commodityInventoryIDTO.setOrderId(order.getId());
        commodityInventoryIDTO.setOrderTime(order.getOrderTime());
        commodityInventoryIDTO.setType(type);
        List<CommodityInventoryDetailIDTO> detailIDTOS=new ArrayList<>();
        for (OrderItemMockDTO item : dto.getItems()) {
            CommodityInventoryDetailIDTO detailIDTO=new CommodityInventoryDetailIDTO();
            detailIDTO.setCommodityId(item.getCommodityId());
            detailIDTO.setQuantity(item.getOrderQuantity());
            detailIDTO.setLevel(1);
            detailIDTOS.add(detailIDTO);
        }
        commodityInventoryIDTO.setOrderCommodityList(detailIDTOS);
        List<CommodityInventoryODTO> inventoryODTOS = toBClient.queryCommodityWithBomInventory(commodityInventoryIDTO);
        Map<Long, Integer> stockMap = inventoryODTOS.stream().collect(toMap(CommodityInventoryODTO::getCommodityId, CommodityInventoryODTO::getStockType, (k1, k2) -> k1));
        //冻结库存
        WarehouseFreezeInventoryIDTO idto = new WarehouseFreezeInventoryIDTO();
        idto.setOrderId(order.getId());
        idto.setOrderCode(order.getOrderCode());
        idto.setOrderTime(order.getOrderTime());
        idto.setType(type);
        idto.setSourceType(order.getOrderType());
        idto.setDeliveryBatch(order.getDeliveryBatch());
        idto.setStoreId(store.getId());
        idto.setStoreCode(store.getStoreCode());
        idto.setStoreName(store.getStoreName());
        List<ToBOrderDetailIDTO> list = new ArrayList<>();
        for (OrderItemMockDTO item : dto.getItems()) {
            ToBOrderDetailIDTO i = new ToBOrderDetailIDTO();
            BeanUtils.copyProperties(item, i);
            Integer stockType = stockMap.get(item.getCommodityId());
            i.setStockType(stockType);
            list.add(i);
        }
        idto.setOrderCommodityDetailList(list);
        List<CommodityInventoryODTO> commodityInventoryODTOS = toBClient.warehouseFreezeInventory(idto);
        Map<String, List<CommodityInventoryODTO>> map = commodityInventoryODTOS.stream().collect(groupingBy(d -> d.getWarehouseId() + "-" + d.getStockType()));
        map.forEach((k, v) -> {
            SubOrder subOrder = new SubOrder();
            subOrder.setOrderId(order.getId());
            subOrder.setLogisticsModel(0);
            subOrder.setVarietyTotal(v.size());
            subOrder.setSupplierId(0L);
            subOrder.setWarehouseId(v.get(0).getWarehouseId());
            subOrder.setStatus(0);
            subOrder.setOrderTime(order.getOrderTime());
            subOrder.setStockType(v.get(0).getStockType());
            subOrder.setPresaleStatus(0);
            subOrder.setSubOrderCode(IDGenerator.newOrderCode());
            subOrder.setCreateId(userId);
            subOrder.setCreateTime(new Date());
            subOrder.setUpdateTime(new Date());
            subOrderMapper.insertSelective(subOrder);
            for (CommodityInventoryODTO commodityInventoryODTO : v) {
                SubOrderItem subOrderItem = new SubOrderItem();
                BeanUtils.copyProperties(commodityInventoryODTO, subOrderItem);
                subOrderItem.setSubOrderId(subOrder.getId());
                subOrderItem.setQuantity(commodityInventoryODTO.getInventoryQuantity());
                subOrderItem.setPrice(BigDecimal.ZERO);
                if (commodityInventoryODTO.getCanConvert()) {
                    subOrderItem.setTargetQuantity(commodityInventoryODTO.getInventoryQuantity().multiply(new BigDecimal(commodityInventoryODTO.getTargetRatio())).divide(new BigDecimal(commodityInventoryODTO.getSourceRatio())));
                }
                subOrderItem.setCreateId(userId);
                subOrderItem.setCreateTime(new Date());
                subOrderItem.setUpdateTime(new Date());
                subOrderItemMapper.insertSelective(subOrderItem);
            }
        });

        RBucket<Boolean> bucket = redissonClient.getBucket(QYApplicationContext.redisKeyProfile + RedissKeyConstant.TOB_ORDER_SYNC_DC_KAFKA);
        boolean flag = Boolean.FALSE;
        if (bucket.get() != null) {
            flag = bucket.get();
        }
        if (flag) {
            String topic = applicationNameSwitch + KafkaTopicConstant.DC_ASYNC_ORDER;
            mqSenderComponent.sendByKey(topic,
                    order,
                    MqMessage.MQ_KAFKA,
                    KafkaMessageTypeEnum.ORDER.name(),
                    KafkaMessageOperationTypeEnum.INSERT.name(), order.getId().toString());
        }
        return new OrderMockVO(order.getId(),order.getOrderCode());
    }

    public Boolean cancelMockOrder(Long orderId) {
        Order order = orderMapper.selectByPrimaryKey(orderId);
        QYAssert.isTrue(order!=null,"订单不存在");
        order.setOrderStatus(2);
        order.setUpdateTime(new Date());
        orderMapper.updateByPrimaryKeySelective(order);
        WarehouseUnfreezeInventoryIDTO idto=new WarehouseUnfreezeInventoryIDTO();
        idto.setOrderId(order.getId());
        idto.setType(order.getBusinessType()==null?DeliveryOrderTypeEnums.SALE.getCode() : order.getBusinessType());
         toBClient.warehouseUnfreezeInventory(idto);

        RBucket<Boolean> bucket = redissonClient.getBucket(QYApplicationContext.redisKeyProfile + RedissKeyConstant.TOB_ORDER_SYNC_DC_KAFKA);
        boolean flag = Boolean.FALSE;
        if (bucket.get() != null) {
            flag = bucket.get();
        }
        if (flag) {
            String topic = applicationNameSwitch + KafkaTopicConstant.DC_ASYNC_ORDER;
            mqSenderComponent.sendByKey(topic,
                    order,
                    MqMessage.MQ_KAFKA,
                    KafkaMessageTypeEnum.ORDER.name(),
                    KafkaMessageOperationTypeEnum.CANCEL.name(), order.getId().toString());
        }
        return Boolean.TRUE;

    }

    public List<SubOrder> getSubOrdersByOrderId(Long orderId) {
        SubOrder query = new SubOrder();
        query.setOrderId(orderId);
        return subOrderMapper.select(query);
    }

    public List<SubOrderItem> getSubOrderItemsBySubOrderId(Long subOrderId) {
        Example example = new Example(SubOrderItem.class);
        example.createCriteria().andEqualTo("subOrderId", subOrderId);
        return subOrderItemMapper.selectByExample(example);
    }

    @Transactional(rollbackFor = Exception.class)
    public void updateItemByPrimaryKeySelective(SubOrderItem subOrderItem) {
        subOrderItemMapper.updateByPrimaryKeySelective(subOrderItem);
    }

    @Transactional(rollbackFor = Exception.class)
    public void updateSubOrderTotalPrice(SubOrder subOrder, BigDecimal newTotalPrice) {
        if (subOrder.getTotalPrice() != null && newTotalPrice.compareTo(subOrder.getTotalPrice()) == 0) {
            return;
        }

        SubOrder updateSubOrder = new SubOrder();
        updateSubOrder.setId(subOrder.getId());
        updateSubOrder.setTotalPrice(newTotalPrice);
        subOrderMapper.updateByPrimaryKeySelective(updateSubOrder);
    }

    /**
     * 获取子单配货明细列表。
     *
     * @param subOrderIds 子单ID列表。
     * @return 子单配合明细列表。
     */
    public List<SubOrderItem> batchSelectSubOrderRationItems(List<Long> subOrderIds) {
        Example example = new Example(SubOrderItem.class);
        example.createCriteria()
                .andIn("subOrderId", subOrderIds)
                .andIn("type", Arrays.asList(ProductTypeEnums.RATION.getCode(), ProductTypeEnums.PRODUCT.getCode()))
                .andIsNull("combOrderListId");
        return subOrderItemMapper.selectByExample(example);
    }

}
