package com.pinshang.qingyun.order.service;

import com.pinshang.qingyun.order.mapper.GiftModelMapper;
import com.pinshang.qingyun.order.mapper.GiftProductMapper;
import com.pinshang.qingyun.order.model.gift.GiftModel;
import com.pinshang.qingyun.order.model.gift.GiftProduct;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;

import java.util.List;

/**
 * @Author: sk
 * @Date: 2024/7/18
 */
@Slf4j
@Service
public class GiftProductService {

    @Autowired
    GiftProductMapper giftProductMapper;

    public List<GiftProduct> findGiftProductListByGiftModelConditionId(Long giftModelConditionId){
        Example example = new Example(GiftProduct.class);
        example.createCriteria().andEqualTo("giftModelConditionId",giftModelConditionId);
        return giftProductMapper.selectByExample(example);
    }


}
