package com.pinshang.qingyun.order.listener;

import com.google.common.util.concurrent.ThreadFactoryBuilder;
import lombok.extern.slf4j.Slf4j;

import java.util.concurrent.ScheduledThreadPoolExecutor;
import java.util.concurrent.ThreadFactory;

/**
 * <AUTHOR>
 * @Date 2018/5/11 11:31
 */
@Slf4j
public class EventProcessPool {
    private static ThreadFactory namedThreadFactory = new ThreadFactoryBuilder()
            .setNameFormat("event-Pool-%d").build();
    private static ScheduledThreadPoolExecutor POOL=
            new ScheduledThreadPoolExecutor(100,namedThreadFactory);

    public static ScheduledThreadPoolExecutor getExecutor(){
        return POOL;
    }
}
