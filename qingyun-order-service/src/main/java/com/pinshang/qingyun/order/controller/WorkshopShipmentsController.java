//package com.pinshang.qingyun.order.controller;
//
//import com.pinshang.qingyun.order.mapper.entry.order.WorkshopShipmentsEntry;
//import com.pinshang.qingyun.order.service.OrderService;
//import com.pinshang.qingyun.order.service.WorkShopService;
//import com.pinshang.qingyun.order.vo.order.ShipmentsVo;
//import org.apache.commons.lang3.StringUtils;
//import org.redisson.api.RList;
//import org.redisson.api.RLock;
//import org.redisson.api.RedissonClient;
//import org.springframework.beans.factory.annotation.Autowired;
//import com.pinshang.qingyun.base.configure.expand.QYAssert;
//import org.springframework.web.bind.annotation.RequestBody;
//import org.springframework.web.bind.annotation.RequestMapping;
//import org.springframework.web.bind.annotation.RequestMethod;
//import org.springframework.web.bind.annotation.RestController;
//
//import javax.annotation.Resource;
//import java.time.LocalDate;
//import java.util.List;
//import java.util.concurrent.TimeUnit;
//
///**
// *
// * <AUTHOR>
// * @Date 2018/4/8 18:21
// */
//@RestController
//@RequestMapping("/shipments")
//public class WorkshopShipmentsController {
//    private final String keyPattern = "directorCode:%s:%s:%s:%s:%s:%s";
//    private final long  expire = 60L;
//    @Resource
//    private RedissonClient redissonClient;
//    @Autowired
//    private WorkShopService workShopService;
//    @Autowired
//    private OrderService orderService;
//
//    @RequestMapping(value = "/list",method = RequestMethod.POST)
//    public List<WorkshopShipmentsEntry> list(@RequestBody ShipmentsVo vo){
//        QYAssert.notNull(vo,"查找生产组发货单参数为空!");
//        String key = generateCacheKey(vo.getDirectorCode(), vo.getLineGroupId(), vo.getDeliveryTime(),
//                vo.getWarehouseId(), vo.getOrderDate()==null?LocalDate.now().plusDays(1).toString():vo.getOrderDate()
//                ,vo.getDeliveryBatch());
//        RLock lock = redissonClient.getLock("key:"+key);
//        lock.lock();
//        List<WorkshopShipmentsEntry> shipmentsList;
//        try {
//            RList<WorkshopShipmentsEntry> cacheList = redissonClient.getList(key);
//            if(cacheList !=null &&!cacheList.isEmpty()){
//                return  cacheList.readAll();
//            }
//            QYAssert.isTrue(workShopService.isDirector(vo.getDirectorCode()),"您不是生产组主任.");
//            shipmentsList = orderService.findShipmentsList(vo);
//            if (shipmentsList != null && !shipmentsList.isEmpty()) {
//                cacheList.addAllAsync(shipmentsList).thenRun(() -> cacheList.expireAsync(expire,TimeUnit.SECONDS));
//            }
//        } finally {
//            lock.unlock();
//        }
//        return shipmentsList;
//    }
//
//    private String generateCacheKey(Object ... directorCode){
//        for (int i = 0; i < directorCode.length; i++) {
//            Object o = directorCode[i];
//            if(o == null || (o instanceof String && StringUtils.isBlank((String) o))){
//                directorCode[i] = "-";
//            }
//        }
//        return String.format(keyPattern,directorCode);
//    }
//
//}
