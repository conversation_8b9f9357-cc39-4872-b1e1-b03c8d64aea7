package com.pinshang.qingyun.order.mapper.auto;

import com.pinshang.qingyun.base.mybatis.MyMapper;
import com.pinshang.qingyun.order.dto.AutoPreOrderIDTO;
import com.pinshang.qingyun.order.dto.AutoPreOrderODTO;
import com.pinshang.qingyun.order.model.auto.AutoPreOrder;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * @Author: liuZhen
 * @DateTime: 2022/5/13 16:01
 */
@Repository
public interface AutoPreOrderMapper extends MyMapper<AutoPreOrder> {
    List<AutoPreOrderODTO> queryByIDTO(@Param("vo") AutoPreOrderIDTO vo);

    AutoPreOrderODTO  queryById(Long id);

}
