package com.pinshang.qingyun.order.mapper.entry.order;

import com.pinshang.qingyun.base.enums.DeliveryOrderTypeEnums;
import com.pinshang.qingyun.base.enums.order.OrderTypeEnum;
import com.pinshang.qingyun.renderer.annotation.FieldRender;
import com.pinshang.qingyun.renderer.constant.RenderFieldHelper;
import com.pinshang.qingyun.renderer.enums.FieldTypeEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@Data
public class SubOrderListEntry {
	//主键id
	private Long id;
	//子单编号
	private String subOrderCode;
	//订单id
	private Long orderId;
	//订单编号
	private String orderCode;
	//仓库id
	private Long warehouseId;
	//仓库
	@FieldRender(fieldType = FieldTypeEnum.WAREHOUSE, keyName = "warehouseId", fieldName = RenderFieldHelper.Warehouse.warehouseName)
	private String warehouseName;
	//物流模式
	private Integer logisticsModel;
	//品种类型数
	private Integer varietyTotal;
	//客户编码
	private String storeCode;
	//客户名称
	private String storeName;
	/** 订单创建时间**/
	private Date createTime;
	/** 订单修改时间**/
	private Date updateTime;
	/** 送货时间**/
	private Date orderTime;
	//配送批次
	private Integer deliveryBatch;

    @ApiModelProperty("发货时间")
    private String deliveryTime;

	@ApiModelProperty("区域（物流中心id，通达时有效）")
	private Long logisticsCenterId;

	@ApiModelProperty("区域名称")
	private String logisticsCenterName;

	@ApiModelProperty("业务类型")
	private Integer deliveryOrderType;

	@ApiModelProperty("业务类型描述")
	private String deliveryOrderTypeDesc;

	@ApiModelProperty("订单类型")
	private Integer orderType;

	@ApiModelProperty("订单类型描述（兼容前端的已有字段）")
	private String sourceTypeName;

	@ApiModelProperty("档口id")
	private Long stallId;

	@ApiModelProperty("档口名称")
	@FieldRender(fieldType = FieldTypeEnum.STALL, keyName = "stallId", fieldName = RenderFieldHelper.Stall.stallCode)
	private String stallCode;

	@ApiModelProperty("档口名称")
	@FieldRender(fieldType = FieldTypeEnum.STALL, keyName = "stallId", fieldName = RenderFieldHelper.Stall.stallName)
	private String stallName;

	public String getSourceTypeName() {
		if(this.orderType != null){
			OrderTypeEnum enums = OrderTypeEnum.fromCode(this.orderType);
			if(enums != null){
				return enums.getDesc();
			}
		}
		return sourceTypeName;
	}

	public String getDeliveryOrderTypeDesc() {
		if(this.deliveryOrderType != null){
			DeliveryOrderTypeEnums enums = DeliveryOrderTypeEnums.get(this.deliveryOrderType);
			if(enums != null){
				return enums.getName();
			}
		}
		return deliveryOrderTypeDesc;
	}
}
