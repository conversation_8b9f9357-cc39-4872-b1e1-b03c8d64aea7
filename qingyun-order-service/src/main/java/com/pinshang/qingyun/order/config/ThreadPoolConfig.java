package com.pinshang.qingyun.order.config;

import com.google.common.util.concurrent.ThreadFactoryBuilder;
import com.pinshang.qingyun.order.constant.ThreadPoolBeanConstants;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.concurrent.Executor;
import java.util.concurrent.ThreadFactory;
import java.util.concurrent.ThreadPoolExecutor;

/**
 * 线程池配置类
 *
 * <AUTHOR>
 */
@Configuration
public class ThreadPoolConfig {

    @Value("${threadPool.coreSize:20}")
    private Integer coreSize;
    @Value("${threadPool.maxSize:50}")
    private Integer maxSize;
    @Value("${threadPool.queueSize:2048}")
    private Integer queueSize;

    /**
     * 这是一个用于记录订单充值扣款日志的自定义线程池
     */
    @Bean(name = "orderBillLogThreadPoolExecutor")
    public Executor orderBillLogThreadPoolExecutor() {
        ThreadPoolTaskExecutor threadPoolExecutor = new ThreadPoolTaskExecutor();
        // 获取可用处理器数量
        int processNum = Runtime.getRuntime().availableProcessors();
        // 设置核心线程数和最大线程数
        int maxPoolSize = processNum * 2;
        // 核心线程数
        threadPoolExecutor.setCorePoolSize(processNum);
        // 最大线程数
        threadPoolExecutor.setMaxPoolSize(maxPoolSize);
        // 设置任务队列容量
        threadPoolExecutor.setQueueCapacity(maxPoolSize * 500);
        // 设置线程空闲时间
        threadPoolExecutor.setKeepAliveSeconds(60);
        // 设置自定义线程工厂，定义线程名前缀，便于调试和管理
        String threadNamePrefix = "order-bill-log-thread-";
        ThreadFactory threadFactory = new ThreadFactoryBuilder()
                .setNameFormat(threadNamePrefix + "%d")
                .setDaemon(true)
                .build();
        threadPoolExecutor.setThreadFactory(threadFactory);
        // 设置拒绝策略，当任务无法执行时，由调用线程来执行该任务，确保任务不会被丢弃
        threadPoolExecutor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        // 初始化线程池
        threadPoolExecutor.initialize();
        return threadPoolExecutor;
    }

    /**
     * 自动订货线程池
     * @return
     */
    @Bean(name = ThreadPoolBeanConstants.AUTO_ORDER_THREADPOOL)
    public Executor autoOrderThreadPoolExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(4);
        executor.setMaxPoolSize(20);
        executor.setQueueCapacity(2048);
        executor.setThreadNamePrefix("auto-order-Executor-");
        executor.setKeepAliveSeconds(60);
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        executor.initialize();
        return executor;
    }

    /**
     * 订单导入线程池
     * @return
     */
    @Bean(name = ThreadPoolBeanConstants.IMPORT_ORDER_THREADPOOL)
    public Executor importOrderThreadPoolExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(4);
        executor.setMaxPoolSize(50);
        executor.setQueueCapacity(1024);
        executor.setThreadNamePrefix("import-order-Executor-");
        executor.setKeepAliveSeconds(60);
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        executor.initialize();
        return executor;
    }


    /**
     * 代理快速订货导入和一键提交购物车线程池
     * @return
     */
    @Bean(name = ThreadPoolBeanConstants.ADMIN_SAVE_ORDER_THREADPOOL)
    public Executor adminSaveOrderThreadPoolExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(4);
        executor.setMaxPoolSize(20);
        executor.setQueueCapacity(1024);
        executor.setThreadNamePrefix("admin-save-order-Executor-");
        executor.setKeepAliveSeconds(60);
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        executor.initialize();
        return executor;
    }

    /**
     * 通用线程池
     * @return
     */
    @Bean(name = ThreadPoolBeanConstants.ORDER_THREADPOOL)
    public Executor orderThreadPoolExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(coreSize);
        executor.setMaxPoolSize(maxSize);
        executor.setQueueCapacity(queueSize);
        executor.setThreadNamePrefix("order-Executor-");
        executor.setKeepAliveSeconds(60);

        //自己实现拒绝策略，开新线程处理。并且发送告警消息
        executor.setRejectedExecutionHandler(new RejectedExecutionHandler());
        executor.initialize();
        return executor;
    }
}
