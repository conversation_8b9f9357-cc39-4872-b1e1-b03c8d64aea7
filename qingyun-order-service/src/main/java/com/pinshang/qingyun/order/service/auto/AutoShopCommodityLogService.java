package com.pinshang.qingyun.order.service.auto;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.pinshang.qingyun.base.configure.expand.QYAssert;
import com.pinshang.qingyun.order.mapper.auto.AutoShopCommodityLogMapper;
import com.pinshang.qingyun.order.model.auto.AutoShopCommodityLog;
import com.pinshang.qingyun.order.vo.auto.AutoSettingPageVO;
import com.pinshang.qingyun.order.vo.auto.AutoShopCommodityLogRequestVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 */
@Service
public class AutoShopCommodityLogService {

    @Autowired
    private AutoShopCommodityLogMapper autoShopCommodityLogMapper;

    /**
     * 批量添加日志
     * @param list
     * @return
     */
    public Integer batchSave(List<AutoShopCommodityLog> list) {
        if (null != list && list.size() > 0) {
            return autoShopCommodityLogMapper.batchSave(list);
        }
        return 0;
    }

    /**
     * 日志列表
     * @param vo
     * @return
     */
    public PageInfo<AutoShopCommodityLog> logPage(AutoShopCommodityLogRequestVO vo) {
        QYAssert.isTrue(null != vo.getShopId(), "门店不能为空");
        PageInfo<AutoShopCommodityLog> pageInfo = PageHelper.startPage(vo.getPageNo(), vo.getPageSize()).doSelectPageInfo(() ->
                autoShopCommodityLogMapper.logList(vo));
        return pageInfo;
    }
}
