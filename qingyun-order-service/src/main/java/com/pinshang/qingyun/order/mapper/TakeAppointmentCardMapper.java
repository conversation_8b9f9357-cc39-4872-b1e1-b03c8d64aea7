package com.pinshang.qingyun.order.mapper;


import com.pinshang.qingyun.base.mybatis.MyMapper;
import com.pinshang.qingyun.order.bo.QueryTakeBO;
import com.pinshang.qingyun.order.dto.tob.TobOrderTakeQuantityAndNumberODTO;
import com.pinshang.qingyun.order.model.take.TakeAppointmentCard;
import org.apache.ibatis.annotations.Mapper;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 提货预约-提货卡
 */
@Mapper
@Repository
public interface TakeAppointmentCardMapper extends MyMapper<TakeAppointmentCard> {

    /**
     * 查询提货卡的数量和份数
     * @param queryTakeBO
     * @return
     */
    List<TobOrderTakeQuantityAndNumberODTO> selectQuantityAndNumber(QueryTakeBO queryTakeBO);
}
