package com.pinshang.qingyun.order.service.xda;

import com.pinshang.qingyun.box.utils.BeanCloneUtils;
import com.pinshang.qingyun.box.utils.DateUtil;
import com.pinshang.qingyun.order.enums.CombTypeEnum;
import com.pinshang.qingyun.order.mapper.OrderListMapper;
import com.pinshang.qingyun.order.mapper.OrderMapper;
import com.pinshang.qingyun.order.mapper.XdaPreOrderItemMapper;
import com.pinshang.qingyun.order.mapper.XdaPreOrderMapper;
import com.pinshang.qingyun.order.model.order.Order;
import com.pinshang.qingyun.order.model.order.OrderList;
import com.pinshang.qingyun.order.model.order.XdaPreOrder;
import com.pinshang.qingyun.order.model.order.XdaPreOrderItem;
import com.pinshang.qingyun.price.dto.OrderLimitQuantityIDTO;
import com.pinshang.qingyun.price.service.StorePromotionClient;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.entity.Example;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @Author: sk
 * @Date: 2024/6/17
 */
@Slf4j
@Service
public class OrderLimitQuantityService {

    @Autowired
    private StorePromotionClient storePromotionClient;
    @Autowired
    private OrderListMapper orderListMapper;
    @Autowired
    private OrderMapper orderMapper;
    @Autowired
    private XdaPreOrderMapper xdaPreOrderMapper;
    @Autowired
    private XdaPreOrderItemMapper xdaPreOrderItemMapper;

    /**
     * 维护B端特价限购记录表
     */
    @Transactional(rollbackFor = Exception.class)
    public void saveOrderLimitQuantity(Long orderId, Integer operateType, Boolean isPreOrder){
        Long storeId;
        Date orderTime;
        Long createId;
        List<OrderList> priceLimitList = new ArrayList<>();

        if(isPreOrder){
            XdaPreOrder xdaPreOrder = xdaPreOrderMapper.selectByPrimaryKey(orderId);
            storeId = xdaPreOrder.getStoreId();
            orderTime = xdaPreOrder.getOrderTime();
            createId = xdaPreOrder.getCreateId();

            Example example = new Example(XdaPreOrderItem.class);
            example.createCriteria().andEqualTo("orderId", xdaPreOrder.getId());
            List<XdaPreOrderItem> xdaPreOrderItemList = xdaPreOrderItemMapper.selectByExample(example);
            List<XdaPreOrderItem> filterOrderList = xdaPreOrderItemList.stream().filter(p -> p.getPricePromotionId() != null
                    && (CombTypeEnum.NOT_COMB.getCode().equals(p.getCombType()) || CombTypeEnum.COMB.getCode().equals(p.getCombType()))).collect(Collectors.toList());

            if(CollectionUtils.isNotEmpty(filterOrderList)){
                priceLimitList = BeanCloneUtils.copyTo(filterOrderList, OrderList.class);
            }
        }else {

            Order order = orderMapper.selectByPrimaryKey(orderId);
            storeId = order.getStoreId();
            orderTime = order.getOrderTime();
            createId = order.getCreateId();

            Example orderListExample = new Example(OrderList.class);
            orderListExample.createCriteria().andEqualTo("orderId", orderId);
            List<OrderList> orderList = orderListMapper.selectByExample(orderListExample);
            List<OrderList> filterOrderList = orderList.stream().filter(p -> p.getPricePromotionId() != null
                    && (CombTypeEnum.NOT_COMB.getCode().equals(p.getCombType()) || CombTypeEnum.COMB.getCode().equals(p.getCombType()))).collect(Collectors.toList());

            if(CollectionUtils.isNotEmpty(filterOrderList)){
                priceLimitList = BeanCloneUtils.copyTo(filterOrderList, OrderList.class);
            }
        }

        if(CollectionUtils.isNotEmpty(priceLimitList)){
            List<OrderLimitQuantityIDTO> orderLimitQuantityList = new ArrayList<>();

            for(OrderList item : priceLimitList){
                OrderLimitQuantityIDTO limitQuantityIDTO = new OrderLimitQuantityIDTO();
                limitQuantityIDTO.setOperateType(operateType);
                limitQuantityIDTO.setPromotionId(item.getPricePromotionId());
                limitQuantityIDTO.setCommodityId(item.getCommodityId());
                limitQuantityIDTO.setStoreId(storeId);
                limitQuantityIDTO.setQuantity(item.getCommodityNum());
                limitQuantityIDTO.setOrderTime(DateUtil.getDateFormate(orderTime, "yyyy-MM-dd"));
                limitQuantityIDTO.setUserId(createId);
                orderLimitQuantityList.add(limitQuantityIDTO);
            }

            storePromotionClient.batchInsertOrUpdateOrderLimitQuantity(orderLimitQuantityList);

        }else {
            log.info("没有B端特价 orderId {}  isPreOrder {}", orderId, isPreOrder);
        }

    }
}
