package com.pinshang.qingyun.order.mapper.entry.purchase;

import com.pinshang.qingyun.renderer.annotation.FieldRender;
import com.pinshang.qingyun.renderer.constant.RenderFieldHelper;
import com.pinshang.qingyun.renderer.enums.FieldTypeEnum;

import java.io.Serializable;
import java.math.BigDecimal;

public class ConnectionPurchaseDetailEntry implements Serializable{

	/**
	 * 
	 */
	private static final long serialVersionUID = -2805439663940398470L;
	private Long commodityId;
	private String commodityCode;
	private String commodityName;
	@FieldRender(fieldType = FieldTypeEnum.COMMODITY_KIND,fieldName = RenderFieldHelper.CommodityKind.commodityThirdKindName,keyName = "commodityId")
	private String categoryName;
	private String commoditySpec;
	@FieldRender(fieldType = FieldTypeEnum.DICTIONARY,fieldName = RenderFieldHelper.Dictionary.optionName,keyName = "commodityUnitId")
	private String commodityUnit;
	private BigDecimal commodityNum;
	private String supplierName;
	private BigDecimal boxCapacity;
	private BigDecimal salesBoxCapacity;
	private BigDecimal boxNums;
	private Integer shopType;
	private BigDecimal commodityOrderNum;
	@FieldRender(fieldType = FieldTypeEnum.DICTIONARY,fieldName = RenderFieldHelper.Dictionary.optionName,keyName = "storeTypeId")
	private String storeTypeName;
	private Long commodityThirdId;
	private Long commodityUnitId;
	private Long storeTypeId;

	public Long getCommodityThirdId() {
		return commodityThirdId;
	}

	public void setCommodityThirdId(Long commodityThirdId) {
		this.commodityThirdId = commodityThirdId;
	}

	public Long getCommodityUnitId() {
		return commodityUnitId;
	}

	public void setCommodityUnitId(Long commodityUnitId) {
		this.commodityUnitId = commodityUnitId;
	}

	public Long getStoreTypeId() {
		return storeTypeId;
	}

	public void setStoreTypeId(Long storeTypeId) {
		this.storeTypeId = storeTypeId;
	}

	public String getStoreTypeName() {
		return storeTypeName;
	}
	public void setStoreTypeName(String storeTypeName) {
		this.storeTypeName = storeTypeName;
	}
	public BigDecimal getCommodityOrderNum() {
		return commodityOrderNum;
	}

	public void setCommodityOrderNum(BigDecimal commodityOrderNum) {
		this.commodityOrderNum = commodityOrderNum;
	}

	public Integer getShopType() {
		return shopType;
	}

	public void setShopType(Integer shopType) {
		this.shopType = shopType;
	}

	public BigDecimal getBoxNums() {
		return boxNums;
	}

	public void setBoxNums(BigDecimal boxNums) {
		this.boxNums = boxNums;
	}

	public BigDecimal getBoxCapacity() {
		return boxCapacity;
	}

	public void setBoxCapacity(BigDecimal boxCapacity) {
		this.boxCapacity = boxCapacity;
	}

	public BigDecimal getSalesBoxCapacity() {
		return salesBoxCapacity;
	}

	public void setSalesBoxCapacity(BigDecimal salesBoxCapacity) {
		this.salesBoxCapacity = salesBoxCapacity;
	}

	public Long getCommodityId() {
		return commodityId;
	}

	public void setCommodityId(Long commodityId) {
		this.commodityId = commodityId;
	}

	public String getCommodityCode() {
		return commodityCode;
	}

	public void setCommodityCode(String commodityCode) {
		this.commodityCode = commodityCode;
	}

	public String getCommodityName() {
		return commodityName;
	}

	public void setCommodityName(String commodityName) {
		this.commodityName = commodityName;
	}

	public String getCategoryName() {
		return categoryName;
	}

	public void setCategoryName(String categoryName) {
		this.categoryName = categoryName;
	}

	public String getCommoditySpec() {
		return commoditySpec;
	}

	public void setCommoditySpec(String commoditySpec) {
		this.commoditySpec = commoditySpec;
	}

	public String getCommodityUnit() {
		return commodityUnit;
	}

	public void setCommodityUnit(String commodityUnit) {
		this.commodityUnit = commodityUnit;
	}

	public BigDecimal getCommodityNum() {
		return commodityNum;
	}

	public void setCommodityNum(BigDecimal commodityNum) {
		this.commodityNum = commodityNum;
	}

	public String getSupplierName() {
		return supplierName;
	}

	public void setSupplierName(String supplierName) {
		this.supplierName = supplierName;
	}
}
