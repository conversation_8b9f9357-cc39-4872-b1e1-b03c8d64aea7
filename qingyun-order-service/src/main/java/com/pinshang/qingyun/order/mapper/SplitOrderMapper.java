package com.pinshang.qingyun.order.mapper;


import com.pinshang.qingyun.base.mybatis.MyMapper;
import com.pinshang.qingyun.order.mapper.entry.splitOrder.SplitOrderListEntry;
import com.pinshang.qingyun.order.model.order.SplitOrder;
import com.pinshang.qingyun.order.vo.orderMonitor.NonSplitCommodityDetail;
import com.pinshang.qingyun.order.vo.orderMonitor.NonSplitOrderQueryVo;
import com.pinshang.qingyun.order.vo.orderMonitor.NonSplitOrderVo;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface SplitOrderMapper extends MyMapper<SplitOrder> {

	/** 查询未被折单的订单数据**/
	List<NonSplitOrderVo> findNonSplitOrderList(NonSplitOrderQueryVo nonSplitOrderQueryVo);

	/** 根据订单ID，查询此订单的商品拆单相关信息(物流模式，仓库，供应商)**/
	List<NonSplitCommodityDetail> findNonSplitByOrderId(@Param("orderId") Long orderId);

	/** 查询未被折单的订单ID **/
	List<Long> findNonSplitOrder(NonSplitOrderQueryVo nonSplitOrderQueryVo);

	//查询订单商品，用于拆单生成SO单
	List<SplitOrderListEntry> querySplitOrderDetail(@Param("orderId") Long orderId);

	// 查询orderGiftList里面的配货商品
	List<SplitOrderListEntry> queryGiftOrderRationDetail(@Param("orderId") Long orderId);

	// 查询orderGiftList里面的正常商品
	List<SplitOrderListEntry> queryGiftOrderProductDetail(@Param("orderId") Long orderId);

    List<String> findUnSplitOrderList(@Param("startTime") String startTime, @Param("endTime") String endTime);
}
