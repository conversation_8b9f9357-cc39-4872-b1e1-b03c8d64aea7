package com.pinshang.qingyun.order.mapper.entry.commodity;

import com.pinshang.qingyun.order.enums.OrderPropertyEnum;
import com.pinshang.qingyun.order.mapper.entry.SupplyBaseEntry;
import com.pinshang.qingyun.renderer.annotation.FieldRender;
import com.pinshang.qingyun.renderer.constant.RenderFieldHelper;
import com.pinshang.qingyun.renderer.enums.FieldTypeEnum;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.util.Date;

public class CommodityResultEntry extends SupplyBaseEntry {

	private String commodityName;

    private String commoditySpec;
    private String commodityId;
    private String commodityCode;
	@FieldRender(fieldType = FieldTypeEnum.COMMODITY_KIND,fieldName = RenderFieldHelper.CommodityKind.commodityThirdKindName,keyName = "commodityId")
    private String categoryName;
    private boolean edit = true;
    
    private String commodityRemark;//商品备注
    
    private Integer commodityType;
    
    private Integer commodityState;
    //商品价格(实际用的->新价格)
    private BigDecimal commodityPrice;
    
    private BigDecimal commodityNumber;
    
    private BigDecimal commodityNumberLimit;
    
    private Integer frozen;//速冻
    
    private String  packedType;//包类型(01散装,02正包)
    private String newProductFlag;//新品标识
    @SuppressWarnings("unused")
	private boolean isNewProduct;//是否新品
    
    private boolean isPromotionProduct;//是否促销
    
    private String storageCondition;//存储条件
    private String commodityDesc;//描述(说明)
    
//    private String supplyStartTime; //供货开始时间
//    private String supplyEndTime;//供货截止时间
    
    private BigDecimal referencePrice;//参考零售价
    
    private BigDecimal saleRation;
    
    private Integer logisticsModel;//物流模式

	private Boolean isShow =false;//

    private BigDecimal salesBoxCapacity;

	private BigDecimal stockQuantity; //现有库存
	/** 库存数量 */
	private Integer stockNumber;

	private BigDecimal suggestedQuantity; //建议订货量
	
	private String commodityUnit;

	private BigDecimal retailPrice;//门店零售价

	private  Integer isWeight;
	private BigDecimal onlineQuantity;//在途数量
	private String inStorageDate;//第一次入库日期
	private BigDecimal cartNum;//购物车数量
	private Long shopId;

	private String barCode;

	private String barCodeList;	// 子码列表

	// 包装规格
	private BigDecimal commodityPackageSpec;
	private BigDecimal commodityShares;//份数
	@FieldRender(fieldType = FieldTypeEnum.COMMODITY_KIND,fieldName = RenderFieldHelper.CommodityKind.commodityFirstKindName,keyName = "commodityId")
	private String categoryFirstName;
	/** 供应商id */
	private Long supplierId;

	private Long commodityCycleId;
	private Long commodityThirdId;
	private Long commodityFirstId;
	private Long commodityUnitId;
	private BigDecimal xdSalesBoxCapacity;
	private BigDecimal boxCapacity;
	private Boolean autoCommodity; // 是否自动订货
	private BigDecimal orderedQuantity;//已定未收数量

	/** 订货属性  1 必售  2 一级可选  3 二级可选**/
	private Integer orderPropertyType;

	/**
	 * 1 当前时间可订货的商品——在订货时间段内，且有库存（商品库存依据且有库存的商品、不限量供应的商品、限量供应且有余量的商品）
	 * 2 已抢光的商品——商品库存依据且库存≤0或限量供应且已满限量的商品，标记“已抢光”效果。
	 * 3 当前时间不在订货时间段的商品
	 */
	private Integer orderedStatus;

	/**
	 * 促销开始时间
	 */
	private Date startTime;
	/**
	 * 促销结束时间
	 */
	private Date endTime;

	/**
	 * 近15天日均销量
	 */
	private BigDecimal avgDailySales15Days;
	/**
	 * 当日销售数量
	 */
	private BigDecimal dailySales;
	/**
	 * 近15天毛利率
	 */
	private BigDecimal grossProfitMargin15Days;

	public Boolean getIsShow() {
		return isShow;
	}

	public void setIsShow(Boolean isShow) {
		this.isShow = isShow;
	}

	public Integer getOrderedStatus() {
		return orderedStatus;
	}

	public void setOrderedStatus(Integer orderedStatus) {
		this.orderedStatus = orderedStatus;
	}

	public String getOrderPropertyTypeName() {
		return OrderPropertyEnum.getName(orderPropertyType);
	}

	public Integer getOrderPropertyType() {
		return orderPropertyType;
	}

	public void setOrderPropertyType(Integer orderPropertyType) {
		this.orderPropertyType = orderPropertyType;
	}

	public BigDecimal getOrderedQuantity() {
		return orderedQuantity;
	}

	public void setOrderedQuantity(BigDecimal orderedQuantity) {
		this.orderedQuantity = orderedQuantity;
	}

	public Boolean getAutoCommodity() {
		return autoCommodity;
	}

	public void setAutoCommodity(Boolean autoCommodity) {
		this.autoCommodity = autoCommodity;
	}

	public BigDecimal getBoxCapacity() {
		return boxCapacity;
	}

	public void setBoxCapacity(BigDecimal boxCapacity) {
		this.boxCapacity = boxCapacity;
	}

	public BigDecimal getXdSalesBoxCapacity() {
		return xdSalesBoxCapacity;
	}

	public void setXdSalesBoxCapacity(BigDecimal xdSalesBoxCapacity) {
		this.xdSalesBoxCapacity = xdSalesBoxCapacity;
	}

	public Long getCommodityCycleId() {
		return commodityCycleId;
	}

	public void setCommodityCycleId(Long commodityCycleId) {
		this.commodityCycleId = commodityCycleId;
	}

	public Long getCommodityThirdId() {
		return commodityThirdId;
	}

	public void setCommodityThirdId(Long commodityThirdId) {
		this.commodityThirdId = commodityThirdId;
	}

	public Long getCommodityFirstId() {
		return commodityFirstId;
	}

	public void setCommodityFirstId(Long commodityFirstId) {
		this.commodityFirstId = commodityFirstId;
	}

	public Long getCommodityUnitId() {
		return commodityUnitId;
	}

	public void setCommodityUnitId(Long commodityUnitId) {
		this.commodityUnitId = commodityUnitId;
	}

	//	public Long getSupplierId() {
//		return supplierId;
//	}
//
//	public void setSupplierId(Long supplierId) {
//		this.supplierId = supplierId;
//	}
	/** app状态：0-上架，1-下架 */
	private Integer appStatus;

	public String getCategoryFirstName() {
		return categoryFirstName;
	}

	public void setCategoryFirstName(String categoryFirstName) {
		this.categoryFirstName = categoryFirstName;
	}

	public Integer getAppStatus() {
		return appStatus;
	}

	public void setAppStatus(Integer appStatus) {
		this.appStatus = appStatus;
	}

	public BigDecimal getCommodityShares() {
		return commodityShares;
	}

	public void setCommodityShares(BigDecimal commodityShares) {
		this.commodityShares = commodityShares;
	}

	public String getBarCodeList() {
		return barCodeList;
	}

	public void setBarCodeList(String barCodeList) {
		this.barCodeList = barCodeList;
	}

	public BigDecimal getCommodityPackageSpec() {
		return commodityPackageSpec;
	}

	public void setCommodityPackageSpec(BigDecimal commodityPackageSpec) {
		this.commodityPackageSpec = commodityPackageSpec;
	}

	public Long getShopId() {
		return shopId;
	}

	public void setShopId(Long shopId) {
		this.shopId = shopId;
	}

	public Integer getIsWeight() {
		return isWeight;
	}

	public void setIsWeight(Integer isWeight) {
		this.isWeight = isWeight;
	}

	public BigDecimal getCartNum() {
		return cartNum;
	}

	public void setCartNum(BigDecimal cartNum) {
		this.cartNum = cartNum;
	}

	public BigDecimal getOnlineQuantity() {
		return onlineQuantity;
	}

	public void setOnlineQuantity(BigDecimal onlineQuantity) {
		this.onlineQuantity = onlineQuantity;
	}

	public String getInStorageDate() {
		return inStorageDate;
	}

	public void setInStorageDate(String inStorageDate) {
		this.inStorageDate = inStorageDate;
	}

	public String getCommodityUnit() {
		return commodityUnit;
	}

	public void setCommodityUnit(String commodityUnit) {
		this.commodityUnit = commodityUnit;
	}

	public String getCommodityName() {
        return commodityName;
    }

    public void setCommodityName(String commodityName) {
        this.commodityName = commodityName;
    }

    public String getCommoditySpec() {
        return commoditySpec;
    }

    public void setCommoditySpec(String commoditySpec) {
        this.commoditySpec = commoditySpec;
    }

	public BigDecimal getCommodityNumber() {
        return commodityNumber==null? BigDecimal.ZERO :commodityNumber;
    }

    public void setCommodityNumber(BigDecimal commodityNumber) {
        this.commodityNumber = commodityNumber;
    }
    
    public void setCommodityNumber(String commodityNumber){
    	this.commodityNumber = new BigDecimal(commodityNumber);
    }
    
    public BigDecimal getCommodityNumberLimit() {
		return commodityNumberLimit == null ? BigDecimal.ZERO: commodityNumberLimit ;
	}

	public void setCommodityNumberLimit(BigDecimal commodityNumberLimit) {
		this.commodityNumberLimit = commodityNumberLimit;
	}

    public BigDecimal getCommodityPrice() {
        return commodityPrice;
    }

    public void setCommodityPrice(BigDecimal commodityPrice) {
        this.commodityPrice = commodityPrice;
    }

    public String getCommodityId() {
        return commodityId;
    }

	public Long getCommodityIdLong() {
		if(StringUtils.isNotBlank(commodityId)){
			return Long.valueOf(commodityId);
		}
		return 0L;
	}

    public void setCommodityId(String commodityId) {
        this.commodityId = commodityId;
        this.productId = commodityId;
    }

    public String getCommodityCode() {
        return commodityCode;
    }

    public void setCommodityCode(String commodityCode) {
        this.commodityCode = commodityCode;
    }

	public Integer getCommodityType() {
		return commodityType;
	}

	public void setCommodityType(Integer commodityType) {
		this.commodityType = commodityType;
	}
	
	
    public String getCommodityRemark() {
		return commodityRemark;
	}

	public void setCommodityRemark(String commodityRemark) {
		this.commodityRemark = commodityRemark;
	}

	public String getCategoryName() {
		return categoryName;
	}

	public void setCategoryName(String categoryName) {
		this.categoryName = categoryName;
	}

	public Integer getCommodityState() {
		return commodityState;
	}

	public void setCommodityState(Integer commodityState) {
		this.commodityState = commodityState;
	}

	public boolean isEdit() {
		return edit;
	}

	public void setEdit(boolean edit) {
		this.edit = edit;
	}

	public Integer getFrozen() {
		if( null == this.frozen){
			return  0 ;
		}
		return frozen;
	}

	public void setFrozen(Integer frozen) {
		this.frozen = frozen;
	}

	public String getPackedType() {
		if(StringUtils.isEmpty(this.packedType)){
			return "01";
		}
		return packedType;
	}

	public void setPackedType(String packedType) {
		this.packedType = packedType;
	}

	public String getStorageCondition() {
		return storageCondition;
	}

	public void setStorageCondition(String storageCondition) {
		this.storageCondition = storageCondition;
	}

	public String getCommodityDesc() {
		if(StringUtils.isEmpty(this.commodityDesc)){
			return "";
		}
		return commodityDesc;
	}

	public void setCommodityDesc(String commodityDesc) {
		this.commodityDesc = commodityDesc;
	}
	 
//	public String getSupplyStartTime() {
//		return supplyStartTime;
//	}
//
//	public void setSupplyStartTime(String supplyStartTime) {
//		this.supplyStartTime = supplyStartTime;
//	}
//
//	public String getSupplyEndTime() {
//		return supplyEndTime;
//	}
//
//	public void setSupplyEndTime(String supplyEndTime) {
//		this.supplyEndTime = supplyEndTime;
//	}

	public BigDecimal getReferencePrice() {
		return referencePrice;
	}

	public void setReferencePrice(BigDecimal referencePrice) {
		this.referencePrice = referencePrice;
	}

	public String getNewProductFlag() {
		return newProductFlag;
	}

	public void setNewProductFlag(String newProductFlag) {
		this.newProductFlag = newProductFlag;
	}
	
	/*public boolean isNewProduct() {
		if(StringUtils.isEmpty(this.newProductFlag)){
			return false;
		}
		if("01".equals(this.newProductFlag)){
			return true;
		}
		return false;
	}*/

	public boolean isNewProduct() {
		return isNewProduct;
	}

	public void setNewProduct(boolean newProduct) {
		isNewProduct = newProduct;
	}

	public boolean isPromotionProduct() {
		return isPromotionProduct;
	}

	public void setPromotionProduct(boolean isPromotionProduct) {
		this.isPromotionProduct = isPromotionProduct;
	}

	public BigDecimal getSaleRation() {
		return saleRation;
	}

	public void setSaleRation(BigDecimal saleRation) {
		this.saleRation = saleRation;
	}

	@Override
	public Integer getLogisticsModel() {
		return logisticsModel;
	}

	@Override
	public void setLogisticsModel(Integer logisticsModel) {
		this.logisticsModel = logisticsModel;
	}

	public BigDecimal getSalesBoxCapacity() {
		return salesBoxCapacity;
	}

	public void setSalesBoxCapacity(BigDecimal salesBoxCapacity) {
		this.salesBoxCapacity = salesBoxCapacity;
	}

	public BigDecimal getStockQuantity() {
		return stockQuantity;
	}

	public void setStockQuantity(BigDecimal stockQuantity) {
		this.stockQuantity = stockQuantity;
	}

	public Integer getStockNumber() {
		return stockNumber;
	}

	public void setStockNumber(Integer stockNumber) {
		this.stockNumber = stockNumber;
	}

	public BigDecimal getSuggestedQuantity() {
		return suggestedQuantity;
	}

	public void setSuggestedQuantity(BigDecimal suggestedQuantity) {
		this.suggestedQuantity = suggestedQuantity;
	}

	public BigDecimal getRetailPrice() {
		return retailPrice;
	}

	public void setRetailPrice(BigDecimal retailPrice) {
		this.retailPrice = retailPrice;
	}

	public String getBarCode() {
		return barCode;
	}

	public void setBarCode(String barCode) {
		this.barCode = barCode;
	}

	public Date getEndTime() {
		return endTime;
	}

	public void setEndTime(Date endTime) {
		this.endTime = endTime;
	}

	public Date getStartTime() {
		return startTime;
	}

	public void setStartTime(Date startTime) {
		this.startTime = startTime;
	}

	public BigDecimal getGrossProfitMargin15Days() {
		return grossProfitMargin15Days;
	}

	public void setGrossProfitMargin15Days(BigDecimal grossProfitMargin15Days) {
		this.grossProfitMargin15Days = grossProfitMargin15Days;
	}

	public BigDecimal getDailySales() {
		return dailySales;
	}

	public void setDailySales(BigDecimal dailySales) {
		this.dailySales = dailySales;
	}

	public BigDecimal getAvgDailySales15Days() {
		return avgDailySales15Days;
	}

	public void setAvgDailySales15Days(BigDecimal avgDailySales15Days) {
		this.avgDailySales15Days = avgDailySales15Days;
	}
}
