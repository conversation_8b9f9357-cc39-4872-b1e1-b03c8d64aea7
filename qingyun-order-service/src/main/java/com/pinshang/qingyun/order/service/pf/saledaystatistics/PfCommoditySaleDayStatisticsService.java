package com.pinshang.qingyun.order.service.pf.saledaystatistics;

import static java.util.stream.Collectors.groupingBy;

import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.pinshang.qingyun.box.utils.SpringUtil;
import com.pinshang.qingyun.order.dto.pf.PfCreOrderItemDTO;
import com.pinshang.qingyun.order.mapper.CommodityMapper;
import com.pinshang.qingyun.order.mapper.PfCommoditySaleDayStatisticsMapper;
import com.pinshang.qingyun.order.mapper.entry.order.CommodityInfoEntry;
import com.pinshang.qingyun.order.model.order.PfCommoditySaleDayStatistics;

import lombok.extern.slf4j.Slf4j;
import tk.mybatis.mapper.entity.Example;


@Service
@Slf4j
public class PfCommoditySaleDayStatisticsService {
	
    @Autowired
    private PfCommoditySaleDayStatisticsMapper pfCommoditySaleDayStatisticsMapper;
    
    @Autowired
    private CommodityMapper commodityMapper;

    /**
     * 查询商品销量统计
     * @param queryDtos 包含商品id,统计起止日期
     * @return 商品的销量统计信息
     */
    public List<CommoditySaleStatisticsDto> queryCommoditySaleStatistics(List<CommoditySaleStatisticsQueryDto> queryDtos) {
    	if (queryDtos == null || queryDtos.isEmpty()) {
    		return new ArrayList<>();
    	}
    	List<CommoditySaleStatisticsDto> result = pfCommoditySaleDayStatisticsMapper.queryCommoditySaleStatistics(queryDtos);
    	// 附加商品名称信息
    	if (!result.isEmpty()) {
    		List<Long> commodityIds = result.stream().map(CommoditySaleStatisticsDto::getCommodityId).collect(Collectors.toList());
	    	List<CommodityInfoEntry> commodityInfoList = commodityMapper.findCommodityInfoIds(commodityIds);
	    	Map<Long, CommodityInfoEntry> commodityInfoMap = commodityInfoList.stream().collect(Collectors.toMap(CommodityInfoEntry::getId, obj->obj, (k1, k2)->k1));
	    	for (CommoditySaleStatisticsDto st : result) {
	    		Long commodityId = st.getCommodityId();
	    		CommodityInfoEntry e = commodityInfoMap.get(commodityId);
	    		if (e != null) {
	    			st.setCommodityName(e.getCommodityName());
	    			st.setCommodityCode(e.getCommodityCode());
	    		}
	    	}
    	}
    	return result;
    }
    
    /**
     * 保存线上订单销量日统计
     * @param orderTime
     * @param orderLists
     */
    public void saveSaleDayStatistics(Date orderTime, List<PfCreOrderItemDTO> orderLists){
        if(SpringUtil.isNotEmpty(orderLists)){
            List<PfCommoditySaleDayStatistics> dayStatisticsList = new ArrayList<>();
            List<Long> commodityIdList = new ArrayList<>();
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
            Date orderTimeParse = null;
            try {
                orderTimeParse = sdf.parse(sdf.format(orderTime));
                Date finalOrderTimeParse = orderTimeParse;
                orderLists.stream().collect(groupingBy(PfCreOrderItemDTO::getCommodityId)).forEach((k, v)->{
                    BigDecimal totalQuantity = v.stream().map(PfCreOrderItemDTO:: saleQuantity).reduce(BigDecimal.ZERO, BigDecimal::add);
                    BigDecimal totalAmount = v.stream().map(PfCreOrderItemDTO :: saleAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
                    PfCommoditySaleDayStatistics dayStatistics = new PfCommoditySaleDayStatistics();
                    dayStatistics.setOrderTime(finalOrderTimeParse);
                    dayStatistics.setCommodityId(k);
                    dayStatistics.setTotalQuantity(totalQuantity);
                    dayStatistics.setTotalAmount(totalAmount);
                    dayStatisticsList.add(dayStatistics);
                    commodityIdList.add(k);
                });

                // 换一种 insert on duplicated key update的写法
//                pfCommoditySaleDayStatisticsMapper.insertOnDuplicatedKeyUpdate(dayStatisticsList);
                
                // 这里的写法没有考虑并发影响，应该是不健壮的
                
                Example example = new Example(PfCommoditySaleDayStatistics.class);
                example.createCriteria().andEqualTo("orderTime",finalOrderTimeParse).andIn("commodityId",commodityIdList);
                List<PfCommoditySaleDayStatistics> dbList = pfCommoditySaleDayStatisticsMapper.selectByExample(example);
                if(SpringUtil.isNotEmpty(dbList)){
                    List<Long> dbCommodityIdList = dbList.stream().map(PfCommoditySaleDayStatistics::getCommodityId).collect(Collectors.toList());
                    List<PfCommoditySaleDayStatistics> addList = new ArrayList<>();
                    List<PfCommoditySaleDayStatistics> updateList = new ArrayList<>();
                    dayStatisticsList.forEach(entry->{
                        if(dbCommodityIdList.contains(entry.getCommodityId())){
                            updateList.add(entry);
                        }else{
                            addList.add(entry);
                        }
                    });
                    if(SpringUtil.isNotEmpty(addList)){
                        pfCommoditySaleDayStatisticsMapper.insertList(addList);
                    }
                    if(SpringUtil.isNotEmpty(updateList)){
                        pfCommoditySaleDayStatisticsMapper.batchUpdate(updateList);
                    }
                }else{
                    pfCommoditySaleDayStatisticsMapper.insertList(dayStatisticsList);
                }
            } catch (ParseException e) {
               log.error("线上订单销量日统计",e);
            }

        }
    }

}
