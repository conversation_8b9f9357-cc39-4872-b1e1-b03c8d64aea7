package com.pinshang.qingyun.order.mapper;

import com.pinshang.qingyun.base.mybatis.MyMapper;
import com.pinshang.qingyun.order.mapper.entry.order.CommodityInfoEntry;
import com.pinshang.qingyun.order.mapper.entry.order.FrozenProductEntry;
import com.pinshang.qingyun.order.mapper.entry.order.QuickGoodsEntry;
import com.pinshang.qingyun.order.model.order.QuickGoods;
import com.pinshang.qingyun.order.vo.order.QuickGoodsVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Mapper
@Repository
public interface QuickGoodsMapper extends MyMapper <QuickGoods>{
	
	List<QuickGoodsEntry> findQuickGoodsList(QuickGoodsVo vo);

	List<QuickGoodsEntry> findQuickGoodsNewList(QuickGoodsVo vo);

	List<CommodityInfoEntry> queryCommodityInfoByStoreId(@Param("commodityCodes") List<String> commodityIds, @Param("storeId") Long storeId);

	List<CommodityInfoEntry> queryCommodityPurchaseStatus(@Param("commodityIdList") List<Long> commodityIdList, @Param("shopId") Long shopId);

	FrozenProductEntry findFrozenProductListByProductCode(@Param("productCode") String productCode);

	int insertListAdmin(@Param("quickList") List<QuickGoods> quickList);

    void deleteQuickGoodsAdmin(@Param("userId") Long userId, @Param("bigShop") Boolean bigShop);
}
