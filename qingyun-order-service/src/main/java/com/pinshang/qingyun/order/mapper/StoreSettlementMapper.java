package com.pinshang.qingyun.order.mapper;

import com.pinshang.qingyun.base.mybatis.MyMapper;
import com.pinshang.qingyun.order.mapper.entry.store.StoreSettlementEntry;
import com.pinshang.qingyun.order.model.order.StoreSettlement;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

@Repository
public interface StoreSettlementMapper extends MyMapper<StoreSettlement> {

    Integer updateCollectPrice(@Param("map") Map collectPriceMap);

    Integer updateCollectPriceTwo(@Param("amountPrice") BigDecimal amountPrice, @Param("id") Long id);

    StoreSettlement getStoreSettlementByStoreId(@Param("storeId") Long storeId);

    List<StoreSettlement> listStoreSettlementByStoreIds(@Param("storeIds")List<Long> storeIds);

    StoreSettlementEntry queryBalance(@Param("storeCode")String storeCode);

    List<StoreSettlementEntry> findStoreSettleByStoreIds(@Param("storeIds")List<Long> storeIds);
}
