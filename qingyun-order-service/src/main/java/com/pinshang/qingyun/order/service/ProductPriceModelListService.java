package com.pinshang.qingyun.order.service;

import com.pinshang.qingyun.base.configure.expand.QYAssert;
import com.pinshang.qingyun.box.utils.SpringUtil;
import com.pinshang.qingyun.order.mapper.ProductPriceModelListMapper;
import com.pinshang.qingyun.order.mapper.PromotionStkMapper;
import com.pinshang.qingyun.order.model.order.ProductPriceModelList;
import com.pinshang.qingyun.order.model.promotionStk.PromotionStk;
import com.pinshang.qingyun.order.util.list.ListToMapConverter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * @Author: sk
 * @Date: 2024/7/18
 */
@Slf4j
@Service
public class ProductPriceModelListService {

    @Autowired
    ProductPriceModelListMapper productPriceModelListMapper;

    //获取商品价格
    public List<ProductPriceModelList> findCommodityPriceByStoreIdAndCommodityId(Long storeId, List<Long> commodityIdList){
        QYAssert.isTrue(storeId != null,"查询产品价格方案的客户id不能为空");
        QYAssert.isTrue(SpringUtil.isNotEmpty(commodityIdList) ,"查询产品价格方案的商品ids不能为空");
        return productPriceModelListMapper.findCommodityPriceByStoreIdAndCommodityId(storeId,commodityIdList);
    }

    public Map<Long, BigDecimal> getProductPriceModelMap(Long storeId,List<Long> commodityIdList){
        List<ProductPriceModelList> productPriceModelLists = findCommodityPriceByStoreIdAndCommodityId(storeId,commodityIdList);
        return ListToMapConverter.convertListToMap(productPriceModelLists, ProductPriceModelList::getCommodityId, ProductPriceModelList::getCommodityPrice);
    }


}
