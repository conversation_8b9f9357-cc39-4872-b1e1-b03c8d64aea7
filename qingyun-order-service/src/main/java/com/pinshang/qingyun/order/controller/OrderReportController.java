package com.pinshang.qingyun.order.controller;

import com.alibaba.excel.EasyExcel;
import com.github.pagehelper.PageInfo;
import com.pinshang.qingyun.base.api.TokenInfo;
import com.pinshang.qingyun.base.configure.expand.QYAssert;
import com.pinshang.qingyun.base.page.TablePageInfo;
import com.pinshang.qingyun.base.service.DdTokenShopIdService;
import com.pinshang.qingyun.base.util.ExcelUtil;
import com.pinshang.qingyun.base.util.FastThreadLocalUtil;
import com.pinshang.qingyun.box.utils.BeanCloneUtils;
import com.pinshang.qingyun.box.utils.DateTimeUtil;
import com.pinshang.qingyun.box.utils.DateUtil;
import com.pinshang.qingyun.box.utils.SpringUtil;
import com.pinshang.qingyun.infrastructure.exportfile.cache.starter.FileCacheQuery;
import com.pinshang.qingyun.order.dto.report.*;
import com.pinshang.qingyun.order.enums.ExcelSheetTitleEnum;
import com.pinshang.qingyun.order.mapper.entry.shop.ReturnOrderDetailsReportEntry;
import com.pinshang.qingyun.order.service.OrderReportService;
import com.pinshang.qingyun.order.util.ViewExcel;
import com.pinshang.qingyun.order.vo.shop.ReturnOrderDetailsReportVo;
import com.pinshang.qingyun.renderer.annotation.MethodRender;
import com.pinshang.qingyun.renderer.service.IRenderService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * 迁移至order-manage
 */

@Deprecated
@RestController
@RequestMapping("/report")
@Api(value = "门店报表接口", tags = "shopReport", description = "门店报表相关接口")
@Slf4j
public class OrderReportController {
    @Autowired
    private OrderReportService orderReportService;

    @Autowired
    private IRenderService renderService;

    @Autowired
    private DdTokenShopIdService ddTokenShopIdService;

    public OrderReportController() {
    }


    //退单明细表
    @MethodRender
    @PostMapping("/findReturnOrderDetailsReport")
    public PageInfo<ReturnOrderDetailsReportEntry> findReturnOrderDetailsReport(@RequestBody ReturnOrderDetailsReportVo returnOrderDetailsReportVo) throws ParseException {
        TokenInfo tokenInfo = FastThreadLocalUtil.getQY();
        ddTokenShopIdService.processReadDdTokenShopId(tokenInfo.getShopId(), returnOrderDetailsReportVo.getStallId());
        return orderReportService.findReturnOrderDetailsReport(returnOrderDetailsReportVo);
    }

    @MethodRender
    @ApiOperation(value = "门店短交报表", notes = "门店短交报表",produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @RequestMapping(value = "/shortDeliveryReport", method = RequestMethod.GET)
    public PageInfo<ShortDeliveryReportODto> shortDeliveryReport(ShortDeliveryReportIDto shortDeliveryReportIDto) {
        TokenInfo tokenInfo = FastThreadLocalUtil.getQY();
        shortDeliveryReportIDto.setEnterpriseId(tokenInfo.getEnterpriseId());
        if (shortDeliveryReportIDto.getShopId() == null) {
            shortDeliveryReportIDto.setShopId(tokenInfo.getShopId());
        }
        return orderReportService.shortDeliveryReport(shortDeliveryReportIDto);
    }
    @ApiOperation(value = "门店短交报表-导出", notes = "门店短交报表-导出", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @RequestMapping(value = "/exportInfo/shortDeliveryReport", method = RequestMethod.GET)
    @FileCacheQuery(bizCode = "SHORT_DELIVERY_REPORT")
    public void exportInfoShortDeliveryReport(ShortDeliveryReportIDto shortDeliveryReportIDto, HttpServletResponse response) throws IOException {
        TokenInfo tokenInfo = FastThreadLocalUtil.getQY();
        shortDeliveryReportIDto.setEnterpriseId(tokenInfo.getEnterpriseId());
        if (shortDeliveryReportIDto.getShopId() == null) {
            shortDeliveryReportIDto.setShopId(tokenInfo.getShopId());
        }
        QYAssert.isTrue(shortDeliveryReportIDto.getShopId() != null, "权限有问题或未选择门店");
        QYAssert.isTrue(shortDeliveryReportIDto.getBeginDate() != null, "请选择送货日期");
        QYAssert.isTrue(shortDeliveryReportIDto.getEndDate() != null, "请选择送货日期");
        QYAssert.isTrue(DateUtil.isAfter(DateUtil.addMonth(DateTimeUtil.parse(shortDeliveryReportIDto.getBeginDate(), "yyyy-MM-dd"), 1), DateTimeUtil.parse(shortDeliveryReportIDto.getEndDate(), "yyyy-MM-dd")), "送货日期范围不能超过一个月!");
        shortDeliveryReportIDto.setPageNo(1);
        shortDeliveryReportIDto.setPageSize(Integer.MAX_VALUE);

        PageInfo<ShortDeliveryReportODto> result = orderReportService.shortDeliveryReport(shortDeliveryReportIDto);
        List<ShortDeliveryReportODto> list = result.getList();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmss");
        String fileName = "短交报表" + "_" + sdf.format(new Date());

        renderService.render(list, "/exportInfo/shortDeliveryReport");
        if(shortDeliveryReportIDto.getIsHq() != null && 1 == shortDeliveryReportIDto.getIsHq()) {
            // 导出ShortDeliveryReportIsHqODto
            try {
                List<ShortDeliveryReportIsHqODto> tableData = new ArrayList<>();
                if(SpringUtil.isNotEmpty(list)){
                    tableData =  BeanCloneUtils.copyTo(list, ShortDeliveryReportIsHqODto.class);
                }
                ExcelUtil.setFileNameAndHead(response, fileName);
                EasyExcel.write(response.getOutputStream(), ShortDeliveryReportIsHqODto.class).autoCloseStream(Boolean.FALSE).sheet("短交报表")
                        .doWrite(tableData);

            }catch (Exception e){
                log.error("短交报表导出错误", e);
                ExcelUtil.setExceptionResponse( response );
            }
        }else{
            // 导出 ShortDeliveryReportNotHqODto
            try {
                ExcelUtil.setFileNameAndHead(response, fileName);
                if(SpringUtil.isNotEmpty(list)){
                    if(shortDeliveryReportIDto.getIsStall()){
                        List<ShortDeliveryReportNoHqStallODto> tableData =  BeanCloneUtils.copyTo(list, ShortDeliveryReportNoHqStallODto.class);
                        EasyExcel.write(response.getOutputStream(),ShortDeliveryReportNoHqStallODto.class ).autoCloseStream(Boolean.FALSE).sheet("短交报表")
                                .doWrite(tableData);
                    }else{
                        List<ShortDeliveryReportNoHqODto> tableData =  BeanCloneUtils.copyTo(list, ShortDeliveryReportNoHqODto.class);
                        EasyExcel.write(response.getOutputStream(),ShortDeliveryReportNoHqODto.class ).autoCloseStream(Boolean.FALSE).sheet("短交报表")
                                .doWrite(tableData);
                    }
                }

            }catch (Exception e){
                log.error("短交报表导出错误", e);
                ExcelUtil.setExceptionResponse( response );
            }
        }
    }



    @MethodRender
    @ApiOperation(value = "商品实发汇总表(当日)", notes = "商品实发汇总表(当日)",produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @RequestMapping(value = "/realDeliveryReportCurrentDay", method = RequestMethod.GET)
    public TablePageInfo<RealDeliveryReportODto> realDeliveryReportCurrentDay(RealDeliveryReportIDto realDeliveryReportIDto) {
        return orderReportService.realDeliveryReportCurrentDay(realDeliveryReportIDto);
    }
    @ApiOperation(value = "商品实发汇总表-导出(当日)", notes = "商品实发汇总表-导出(当日)", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @RequestMapping(value = "/exportInfo/realDeliveryReportCurrentDay", method = RequestMethod.GET)
    @FileCacheQuery(bizCode = "REAL_DELIVERY_REPORT_CURRENT_DAY")
    public void exportInfoRealDeliveryReportCurrentDay(RealDeliveryReportIDto realDeliveryReportIDto, HttpServletResponse response) throws IOException {
         exportInfoRealDeliveryReport(realDeliveryReportIDto, response);
    }
    public void exportInfoRealDeliveryReport(RealDeliveryReportIDto realDeliveryReportIDto, HttpServletResponse response) throws IOException {
        realDeliveryReportIDto.setPageNo(1);
        realDeliveryReportIDto.setPageSize(65536);

        TablePageInfo<RealDeliveryReportODto> result = orderReportService.realDeliveryReportCurrentDay(realDeliveryReportIDto);

        List<RealDeliveryReportODto> list = result.getList();
        try {
            List<RealDeliveryReportODto> tableData = new ArrayList<>();

            if(!list.isEmpty()){
                renderService.render(list,"/exportInfo/realDeliveryReportCurrentDays");
                BigDecimal realTotalAmount = (BigDecimal) result.getHeader();
                RealDeliveryReportODto head = new RealDeliveryReportODto();
                head.setCommodityCode("实发金额合计:");
                head.setRealDeliveryAmount(realTotalAmount);
                tableData.add(head);
                tableData.addAll(list);
            }

            ExcelUtil.setFileNameAndHead(response, "商品实发汇总报表");
            EasyExcel.write(response.getOutputStream(),RealDeliveryReportODto.class ).autoCloseStream(Boolean.FALSE).sheet("商品实发汇总报表")
                    .doWrite(tableData);

        }catch (Exception e){
            log.error("短交报表导出错误", e);
            ExcelUtil.setExceptionResponse( response );
        }
    }

    @MethodRender
    @ApiOperation(value = "商品实发汇总表(按客户类型) 当日", notes = "商品实发汇总表(按客户类型) 当日",produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @RequestMapping(value = "/realDeliveryStoreTypeReportCurrentDay", method = RequestMethod.GET)
    public TablePageInfo<RealDeliveryReportODto> realDeliveryStoreTypeReportCurrentDay(RealDeliveryReportIDto realDeliveryReportIDto) {
        return orderReportService.realDeliveryStoreTypeReportCurrentDay(realDeliveryReportIDto);
    }
    @ApiOperation(value = "商品实发汇总表-导出(按客户类型) 当日", notes = "商品实发汇总表-导出(按客户类型) 当日", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @RequestMapping(value = "/exportInfo/realDeliveryStoreTypeReportCurrentDay", method = RequestMethod.GET)
    public ModelAndView exportInfoRealDeliveryStoreTypeReportCurrentDay(RealDeliveryReportIDto realDeliveryReportIDto) {
        return exportInfoRealDeliveryStoreTypeReport(realDeliveryReportIDto);
    }
    public ModelAndView exportInfoRealDeliveryStoreTypeReport(RealDeliveryReportIDto realDeliveryReportIDto) {
        realDeliveryReportIDto.setPageNo(1);
        realDeliveryReportIDto.setPageSize(65536);

        TablePageInfo<RealDeliveryReportODto> result = orderReportService.realDeliveryStoreTypeReportCurrentDay(realDeliveryReportIDto);

        List<RealDeliveryReportODto> list = result.getList();
        Map<String, List<String>> data = new HashMap<>();
        List<String> dataLst = new ArrayList<>();
        int i = 0;
        BigDecimal realTotalAmount = BigDecimal.ZERO;
        if (null != list && !list.isEmpty()) {
            renderService.render(list, "/exportInfo/realDeliveryStoreTypeReportCurrentDay");
            realTotalAmount = (BigDecimal) result.getHeader();
            for (RealDeliveryReportODto dto : list) {
                dataLst = new ArrayList<>();
                dataLst.add(dto.getStoreTypeName());
                dataLst.add(dto.getCommodityCode());
                dataLst.add(dto.getCommodityName());
                dataLst.add(dto.getCommoditySpec());
                dataLst.add(dto.getBarCode());
                dataLst.add(dto.getCommodityFirstName());
                dataLst.add(dto.getCommodityUnit());
                dataLst.add(dto.getOrderNum() == null ? "" : dto.getOrderNum().toString());
                dataLst.add(dto.getDeliveryNum() == null ? "" : dto.getDeliveryNum().toString());
                dataLst.add(dto.getRealDeliveryAmount() == null ? "" : dto.getRealDeliveryAmount().toString());
                dataLst.add(dto.getWarehouseName());
                dataLst.add(dto.getFactoryName());
                dataLst.add(dto.getWorkshopName());
                dataLst.add(dto.getFlowshopName());
                data.put("key_" + i++, dataLst);
            }
        }
        Map<String, Object> map = new HashMap<>();
        // 文件名
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmss");
        String filename = "商品实发汇总报表(客户类型)" + "_" + sdf.format(new Date());
        // 参数设置
        map.put("filename", filename);
        map.put("sheetTitle", ExcelSheetTitleEnum.SHOP_REAL_DELIVERY_STORE_TYPE);
        map.put("data", data);
        //map.put("title", "商品实发汇总报表");
        //map.put("titleCells", (short)3);
        map.put("tableHeader", "实发金额总计:" + realTotalAmount+"元");
        ViewExcel viewExcel = new ViewExcel();
        return new ModelAndView(viewExcel, map);
    }



    @MethodRender
    @ApiOperation(value = "门店订货汇总表(当日)", notes = "门店订货汇总表(当日)",produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @RequestMapping(value = "/shopOrderGoodReportCurrentDay", method = RequestMethod.POST)
    public PageInfo<ShopOrderGoodReportODto> shopOrderGoodReportCurrentDay(@RequestBody ShopOrderGoodReportIDto idto) {
        return orderReportService.shopOrderGoodReportCurrentDay(idto);
    }


    @PostMapping("/exportShopOrderGoodReportCurrentDay")
    @ApiOperation("导出门店订货汇总表")
    @FileCacheQuery(bizCode = "SHOP_ORDER_GOOD_REPORT_DAY")
    public void exportShopOrderGoodReportCurrentDay(@RequestBody ShopOrderGoodReportIDto idto, HttpServletResponse response) throws IOException {
        idto.initExportPage();
        PageInfo<ShopOrderGoodReportODto> page = orderReportService.shopOrderGoodReportCurrentDay(idto);
        List<ShopOrderGoodReportODto> list = page.getList();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmss");

        try {
            renderService.render(list, "/exportShopOrderGoodReportCurrentDay");
            List<ShopOrderGoodReportExportDTO> exportList = BeanCloneUtils.copyTo(list, ShopOrderGoodReportExportDTO.class);
            ExcelUtil.setFileNameAndHead(response,  "门店订货汇总表"+"_"+ sdf.format(new Date()));
            EasyExcel.write(response.getOutputStream(), ShopOrderGoodReportExportDTO.class)
                    .autoCloseStream(Boolean.TRUE).sheet("门店订货汇总表").doWrite(exportList);

        } catch (Exception e) {
            log.error("导出门店订货汇总表-导出报错",e);
            ExcelUtil.setExceptionResponse( response );
        }

    }




    @ApiOperation(value = "门店实收商品分析表", notes = "门店实收商品分析表", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @RequestMapping(value = "/actualReceiptAnalysisReport", method = RequestMethod.GET)
    public PageInfo<ActualReceiptAnalysisODto> actualReceiptAnalysisReport(ActualReceiptAnalysisIDto idto) throws Exception{
        return orderReportService.actualReceiptAnalysisReport(idto);
    }
    @ApiOperation(value = "门店实收商品分析表-导出", notes = "门店实收商品分析表-导出", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @RequestMapping(value = "/exportInfo/actualReceiptAnalysisReport", method = RequestMethod.GET)
    public ModelAndView exportInfoActualReceiptAnalysisReport(ActualReceiptAnalysisIDto actualReceiptAnalysisIDto)throws Exception {
        actualReceiptAnalysisIDto.setPageNo(1);
        actualReceiptAnalysisIDto.setPageSize(Integer.MAX_VALUE);
        PageInfo<ActualReceiptAnalysisODto> result = actualReceiptAnalysisReport(actualReceiptAnalysisIDto);
        List<ActualReceiptAnalysisODto> list = result.getList();
        Map<String, List<String>> data = new HashMap<>();
        int i = 0;
        String shopName = "";
        if (null != list && !list.isEmpty()) {
            for (ActualReceiptAnalysisODto dto : list) {
                List<String> dataList = new ArrayList<>();
                if (i == 0) {
                    if (actualReceiptAnalysisIDto.getShopId() != null) {
                        shopName = dto.getShopName();
                    } else {
                        shopName = "全部";
                    }
                }
                dataList.add(dto.getShopName());
                dataList.add(dto.getCateName());
                dataList.add(dto.getBarCode());
                dataList.add(dto.getCommodityCode());
                dataList.add(dto.getCommodityName());
                dataList.add(dto.getCommoditySpec());
                dataList.add(toStr(dto.getTotalQuantity()));
                dataList.add(toStr(dto.getTotalRealDeliveryQuantity()));
                dataList.add(toStr(dto.getTotalRealReceiveQuantity()));
                dataList.add(toStr(dto.getQuantityDifference()));
                //dataList.add(toStr(dto.getStockQuantity()));
                dataList.add(toStr(dto.getSupplyPrice()));
                dataList.add(toStr(dto.getTotalSupplyPrice()));
                dataList.add(dto.getSupplierName());
                dataList.add(dto.getRealName());
                data.put("key_" + i++, dataList);
            }
        }
        Map<String, Object> map = new HashMap<>();
        // 文件名
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmss");
        String filename = "门店实收商品分析表" + "_" + sdf.format(new Date());
        // 参数设置
        map.put("filename", filename);
        map.put("sheetTitle",ExcelSheetTitleEnum.ACTUAL_RECEIPT_ANALYSIS_REPORT);
        map.put("data", data);
        map.put("title", "门店实收商品分析表[" + shopName + "]");
        map.put("titleCells", (short) 3);
        ViewExcel viewExcel = new ViewExcel();
        return new ModelAndView(viewExcel, map);
    }


    private String toStr(Object o) {
        if(o == null) {
            return "";
        }else if(o instanceof Date) {
            return DateFormatUtils.format((Date)o, "yyyy-MM-dd HH:mm:ss");
        }else {
            return o.toString();
        }
    }
}
