package com.pinshang.qingyun.order.service.syncOrderJob;

import com.pinshang.qingyun.box.utils.SpringUtil;
import com.pinshang.qingyun.order.constant.DictionaryCodeConstant;
import com.pinshang.qingyun.order.mapper.SubOrder2DeliveryOrderMapper;
import com.pinshang.qingyun.order.mapper.entry.splitOrder.SubOrder2DeliveryOrderListEntry;
import com.pinshang.qingyun.order.model.order.DeliveryTime;
import com.pinshang.qingyun.order.service.DictionaryService;
import com.pinshang.qingyun.order.service.SplitOrderService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
@Slf4j
public class SubOrder2DeliveryOrderService {
	@Autowired
	SubOrder2DeliveryOrderMapper subOrder2DeliveryOrderMapper;
	@Autowired
	SplitOrderService splitOrderService;
	@Autowired
	private DictionaryService dictionaryService;

	public void subOrder2DeliveryOrder(DeliveryTime deliveryTime, Integer businessType) {
		Map<String,List<Long>> dictionaryMap = dictionaryService.queryDirectDcConfig();
		List<Long> orderIdList = subOrder2DeliveryOrderMapper.listCurrentDayOrderIdV2(deliveryTime,businessType,
				dictionaryMap.get(DictionaryCodeConstant.STORETYPE_DIRECT_LIST),
				dictionaryMap.get(DictionaryCodeConstant.STORETYPE_ToB_LIST),
				dictionaryMap.get(DictionaryCodeConstant.WAREHOUSE_ToB_LIST));
		if(null ==orderIdList || orderIdList.isEmpty()){
			log.debug("订单覆盖：SO转DO单，线路组：{},没有查询到要执行的订单",deliveryTime.getLineGroupId());
			return;
		}
		try {
            List<SubOrder2DeliveryOrderListEntry> entryList = splitOrderService.getDeliveryOrderEntry(orderIdList,businessType);
            if(SpringUtil.isNotEmpty(entryList)){
                Map<Long,List<SubOrder2DeliveryOrderListEntry>> map = entryList.stream().collect(Collectors.groupingBy(SubOrder2DeliveryOrderListEntry::getReferOrderId));
                for (Map.Entry<Long, List<SubOrder2DeliveryOrderListEntry>> entry : map.entrySet()) {
                    splitOrderService.sendSo2DoKafkaMessage(entry.getValue());
                }
            }
		} catch (Throwable throwable) {
			log.error("SO转DO异常，subOrder2DeliveryOrder 线路组ID:{}",deliveryTime.getLineGroupId().toString());
		}
	}

}
