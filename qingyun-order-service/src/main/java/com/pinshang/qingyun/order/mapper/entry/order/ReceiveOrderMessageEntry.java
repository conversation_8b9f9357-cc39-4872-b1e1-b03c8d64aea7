package com.pinshang.qingyun.order.mapper.entry.order;

import lombok.AllArgsConstructor;
import lombok.Data;

import java.util.Date;

/**
 * @Author: sk
 * @Date: 2021/6/21
 */
@Data
@AllArgsConstructor
public class ReceiveOrderMessageEntry {
    /** 门店id */
    private Long shopId;

    /** 职员id */
    private Long employeeId;

    private String employeeCode;

    /** 职员姓名 */
    private String employeeName;

    /** 职员电话 */
    private String employeePhone;

    /** 业务类型 1 自动收货后  2 手动收货前 */
    private Integer businessType;

    /** 状态 1 成功  0 失败 */
    private Integer status;

    /** 发送内容 */
    private String content;

    /**1短信  2 广播 */
    private Integer messageType;

    private Long createId;
    private Date createTime;
}
