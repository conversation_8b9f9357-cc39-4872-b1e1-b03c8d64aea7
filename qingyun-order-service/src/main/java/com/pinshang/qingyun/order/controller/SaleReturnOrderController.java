package com.pinshang.qingyun.order.controller;

import com.alibaba.excel.EasyExcel;
import com.github.pagehelper.PageInfo;
import com.pinshang.qingyun.base.api.TokenInfo;
import com.pinshang.qingyun.base.configure.expand.QYAssert;
import com.pinshang.qingyun.base.service.DdTokenShopIdService;
import com.pinshang.qingyun.base.util.ExcelUtil;
import com.pinshang.qingyun.base.util.FastThreadLocalUtil;
import com.pinshang.qingyun.box.utils.DateUtil;
import com.pinshang.qingyun.box.utils.SpringUtil;
import com.pinshang.qingyun.infrastructure.exportfile.cache.starter.FileCacheQuery;
import com.pinshang.qingyun.order.dto.*;
import com.pinshang.qingyun.order.dto.consignment.*;
import com.pinshang.qingyun.order.enums.ResponsiblePartyEnum;
import com.pinshang.qingyun.order.mapper.entry.order.SaleReturnOrderDetailEntry;
import com.pinshang.qingyun.order.mapper.entry.order.SaleReturnReportSumEntry;
import com.pinshang.qingyun.order.model.order.SaleReturnOrder;
import com.pinshang.qingyun.order.service.SaleReturnOrderService;
import com.pinshang.qingyun.order.vo.consignment.BatchImportConsignmentSaleReturnOrderResultVO;
import com.pinshang.qingyun.order.vo.consignment.ImportConsignmentSaleReturnOrderEntry;
import com.pinshang.qingyun.order.vo.order.*;
import com.pinshang.qingyun.renderer.annotation.MethodRender;
import com.pinshang.qingyun.renderer.service.IRenderService;
import com.pinshang.qingyun.smm.dto.user.SelectUserShopIdListIDTO;
import com.pinshang.qingyun.smm.service.SMMUserClient;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.ss.usermodel.WorkbookFactory;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * Created by honway on 2017/11/2 14:56.
 * 门店退货入库单 控制器
 */
@RestController
@RequestMapping("saleReturnOrder")
@Slf4j
public class SaleReturnOrderController {

    private final SaleReturnOrderService saleReturnOrderService;
    @Autowired
    private RedissonClient redissonClient;
    @Autowired
    private IRenderService renderService;
    @Autowired
    private SMMUserClient smmUserClient;
    @Autowired
    private DdTokenShopIdService ddTokenShopIdService;

    public SaleReturnOrderController(SaleReturnOrderService saleReturnOrderService) {
        this.saleReturnOrderService = saleReturnOrderService;
    }


    /**
     * 门店退货入库列表 查询列表接口
     * @param reqVo 请求参数
     * @return 返回退货单列表
     */
    @PostMapping("/list")
    public PageInfo<SaleReturnOrderRespVo> list(@RequestBody SaleReturnOrderReqVo reqVo) {
        return saleReturnOrderService.list(reqVo);
    }

    /**
     * 门店退货入库列表 查询列表接口（PDA）
     * @return 返回退货单列表
     */
    @RequestMapping(value = "/querySaleReturnOrderList", method = RequestMethod.POST)
    public List<SaleReturnOrderODTO> querySaleReturnOrderList(@RequestBody SaleReturnOrderIDTO idto) {
        QYAssert.isTrue(StringUtils.isNotBlank(idto.getStartDate()) && StringUtils.isNotBlank(idto.getEndDate()), "开始时间或结束时间不能为null");
        QYAssert.isTrue(idto.getStoreId() != null, "客户不能为空");
        QYAssert.isTrue(idto.getWarehouseId() != null, "仓库不能为空");
        return saleReturnOrderService.querySaleReturnOrderList(idto);
    }

    /**
     * 收货页面查询退货单详情
     * @param code
     * @return
     */
    @GetMapping("/querySaleReturnDetail/{code}/{enterpriseId}")
    public SaleReturnOrderDetailEntry querySaleReturnDetail(@PathVariable("code") String code, @PathVariable("enterpriseId") Long enterpriseId) {
        return saleReturnOrderService.querySaleReturnDetail(code, enterpriseId);
    }

    /**
     * 验证退货单是否被其他人收货
     * @return
     */
    @PostMapping("/checkReceiver")
    public CheckReceiverODTO checkReceiver(@RequestBody CheckReceiverIDTO idto) {
        QYAssert.isTrue(StringUtils.isNotBlank(idto.getCode()),"退货单号不能为空");
        RLock lock = redissonClient.getLock("checkReturnOrderReceiver");
        CheckReceiverODTO checkReceiverODTO = new CheckReceiverODTO();
        if (!lock.isLocked()) {
            lock.lock();
            try {
                checkReceiverODTO = saleReturnOrderService.checkReceiver(idto);
                if(checkReceiverODTO.getFlag()){
                    SaleReturnOrderUpdateReqVo reqVo = new SaleReturnOrderUpdateReqVo();
                    reqVo.setId(idto.getId());
                    reqVo.setStatus(3);
                    reqVo.setUpdateId(idto.getReceiverId());
                  boolean flag = saleReturnOrderService.updateOrderStatus(reqVo);
                  QYAssert.isTrue(flag, "退货单状态修改失败");
                }
            } finally {
                lock.unlock();
            }
        }
        return checkReceiverODTO;
    }


    /**
     * 分页查询退货差异报表
     * @param vo
     * @return
     * 迁移 order-manage
     */
    @MethodRender
    @Deprecated
    @PostMapping("/returnDifferentReport")
    public PageInfo<SaleReturnReportODTO> listReturnReport(@RequestBody SaleReturnReportVo vo) {
        //校验档口权限
        ddTokenShopIdService.processReadDdTokenShopId(vo.getShopId(), vo.getStallId());
        List<Long> shopIdList = smmUserClient.selectUserShopIdList( SelectUserShopIdListIDTO.firstCacheThenDb(FastThreadLocalUtil.getQY().getUserId()));
        if(SpringUtil.isEmpty(shopIdList)){
            return null;
        }
        vo.setShopIdList( shopIdList );
        return saleReturnOrderService.listReturnReport(vo);
    }


    // 迁移 order-manage
    @Deprecated
    @FileCacheQuery(bizCode = "INFO_SHOP_RETURN_DIFFERENT")
    @ApiOperation(value = "门店退货差异报表导出", notes = "门店退货差异报表导出")
    @RequestMapping(value = "/exportInfo/returnDifferentReport", method = RequestMethod.GET)
    public void exportInfoShopReturnDifferent(SaleReturnReportVo saleReturnReportIDTO, HttpServletResponse httpServletResponse) throws IOException {
        SimpleDateFormat format =new SimpleDateFormat("yyyy-MM-dd");
        List<Long> shopIdList = smmUserClient.selectUserShopIdList( SelectUserShopIdListIDTO.firstCacheThenDb(FastThreadLocalUtil.getQY().getUserId()));
        PageInfo<SaleReturnReportODTO> result = null;
        if(SpringUtil.isNotEmpty(shopIdList )) {
            saleReturnReportIDTO.setShopIdList( shopIdList );
            saleReturnReportIDTO.setPageNo(1);
            saleReturnReportIDTO.setPageSize(Integer.MAX_VALUE);
            result = saleReturnOrderService.listReturnReport(saleReturnReportIDTO);
            List<SaleReturnReportODTO> list = result.getList();
            renderService.render(list,"/saleReturnOrder/exportInfo/returnDifferentReport");
        }
        List<SaleReturnReportODTO> list = new ArrayList<>();
        if(null !=result && !result.getList().isEmpty()){
            SaleReturnReportODTO odto ;
            list = new ArrayList<>(result.getSize());
            for (SaleReturnReportODTO dto : result.getList()) {
                odto = new SaleReturnReportODTO();

                if(null ==dto.getReturnQuantity()){
                    odto.setReturnQuantity("0.00");
                }
                if(null ==dto.getRealReturnQuantity()){
                    odto.setRealReturnQuantity("0.00");
                }
                odto.setDiffQuantity(new BigDecimal(dto.getReturnQuantity()).subtract(new BigDecimal(dto.getRealReturnQuantity())).toString());
                odto.setStoreCode(dto.getStoreCode());
                odto.setShopName(dto.getShopName());
                odto.setOrderCode(dto.getOrderCode());
                odto.setUpdateTime(dto.getUpdateTime());
                odto.setUpdateTimeStr(format.format(odto.getUpdateTime()));
                ResponsiblePartyEnum rpTypeEnum = ResponsiblePartyEnum.getEnumByCode(dto.getRpType());
                odto.setRpTypeStr(rpTypeEnum == null?"":rpTypeEnum.getDesc());
                odto.setRealReturnQuantity(dto.getRealReturnQuantity() ==null ?"":dto.getRealReturnQuantity().toString());
                odto.setPrice(dto.getPrice());
                BigDecimal realTotalPrice = dto.getRealReturnQuantity() != null && dto.getPrice() != null ? new BigDecimal(dto.getRealReturnQuantity()).multiply(dto.getPrice()).setScale(2, RoundingMode.HALF_UP).stripTrailingZeros() : null;
                odto.setRealTotalPrice(realTotalPrice);
                odto.setCommodityCode(dto.getCommodityCode());
                odto.setBarCode(dto.getBarCode());
                odto.setCommodityName(dto.getCommodityName());
                odto.setCommoditySpec(dto.getCommoditySpec());
                odto.setCreateTime(dto.getCreateTime());
                odto.setCreateTimeStr(format.format(odto.getCreateTime()));
                odto.setReturnQuantity(dto.getReturnQuantity() ==null ?"":dto.getReturnQuantity().toString());
                odto.setReturnReasonName(dto.getReturnReasonName());
                if(null ==dto.getReturnQuantity()){
                    dto.setReturnQuantity("0.00");
                }
                if(null ==dto.getRealReturnQuantity()){
                    dto.setRealReturnQuantity("0.00");
                }
                odto.setDiffQuantity(new BigDecimal(dto.getReturnQuantity()).subtract(new BigDecimal(dto.getRealReturnQuantity())).toString());
                odto.setStallName(dto.getStallName());
                list.add(odto);
            }
        }
        // 文件名
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmss");
        String filename = "退货差异报表导出"+"_"+ sdf.format(new Date());
        try {
            ExcelUtil.setFileNameAndHead(httpServletResponse, filename);
            EasyExcel.write(httpServletResponse.getOutputStream(),SaleReturnReportODTO.class).autoCloseStream(Boolean.FALSE).
                    sheet("退货差异报表").doWrite(list);;
        } catch (Exception e) {
            log.error("退货差异报表导出出错",e);
            ExcelUtil.setExceptionResponse(httpServletResponse);
        }

    }


    /**
     * 退货差异报表实退金额合计
     * @param vo
     * @return
     */
    @PostMapping("/queryReturnReportSumEntry")
    public SaleReturnReportSumEntry queryReturnReportSumEntry(@RequestBody SaleReturnReportVo vo) {
        return saleReturnOrderService.queryReturnReportSumEntry(vo);
    }

    /**
     * 更新退货单状态和收货人
     * @param reqVo 条件
     * @return 成功返回true
     */
    @RequestMapping(value = "/updateOrderStatus", method = RequestMethod.POST)
    boolean updateOrderStatus(@RequestBody SaleReturnOrderUpdateReqVo reqVo){
        return saleReturnOrderService.updateOrderStatus(reqVo);
    }

    @RequestMapping(value = "/selectByOrderCodeAndLogisticsModel/{returnOrderCode}", method = RequestMethod.POST)
    SaleReturnOrder selectByOrderCodeAndLogisticsModel(@PathVariable("returnOrderCode") String returnOrderCode, @RequestBody List<Integer> logistModle){
        QYAssert.isTrue(StringUtils.isNotEmpty(returnOrderCode), "退货单号不能为空.");
        QYAssert.isTrue(SpringUtil.isNotEmpty(logistModle), "物流模式不能为空");
        return saleReturnOrderService.selectByOrderCodeAndLogisticsModel(returnOrderCode, logistModle);
    }

    /**
     * 退货、少货。总部审核通过，大仓调用此方法
     * @param updateQuantityWrapper
     * @return
     */
    @RequestMapping(value = "/processReturnOrderItemQuantity", method = RequestMethod.POST)
    boolean processReturnOrderItemQuantity(@RequestBody SaleReturnOrderUpdateQuantityWrapperVo updateQuantityWrapper){
        QYAssert.notNull(updateQuantityWrapper, "更新对象不能为空");
        QYAssert.isTrue(updateQuantityWrapper.getId() != null, "退货单ID不能为空");
        QYAssert.isTrue(SpringUtil.isNotEmpty(updateQuantityWrapper.getItemList()), "退货单明细不能为空");
        return saleReturnOrderService.processReturnOrderItemQuantity(updateQuantityWrapper);
    }

    /**
     * 查询待收货/收货中的客户
     * @param idto 请求参数
     * @return 客户名称
     */
    @RequestMapping(value = "/queryStoreInfo", method = RequestMethod.POST)
    public List<StoreInfoODTO> queryStoreInfo(@RequestBody StoreInfoIDTO idto) {
        QYAssert.isTrue(StringUtils.isNotBlank(idto.getBeginTime()) && StringUtils.isNotBlank(idto.getEndTime()), "开始时间或结束时间不能为空");
        QYAssert.isTrue(idto.getWarehouseId() != null, "仓库不能为空");
        return saleReturnOrderService.queryStoreInfo(idto);
    }


    /**
     * 代销退货单列表
     * @param reqVo 请求参数
     * @return 返回代销退货单列表
     */
    @ApiOperation(value = "代销退货单列表",notes = "代销退货单列表")
    @PostMapping("/consignmentReturnOrderlist")
    public PageInfo<ConsignmentSaleReturnOrderRespVo> consignmentReturnOrderlist(@RequestBody ConsignmentSaleReturnOrderReqVo reqVo) {
        return saleReturnOrderService.consignmentReturnOrderlist(reqVo);
    }

    /**
     * 代销商品批量退货导入
     *
     * @param file
     * @return
     */
    @ApiOperation(value = "代销商品批量退货导入")
    @RequestMapping(value = "/consignmentReturnOrderImport", method = RequestMethod.POST)
    public ImportConsignmentSaleReturnOrderEntry consignmentReturnOrderImport(@RequestParam(value = "file", required = true) MultipartFile file,
                                                                              @RequestParam(value="supplierId") Long supplierId) {
        Workbook wb = null;
        try {
            InputStream in = file.getInputStream();
            wb = WorkbookFactory.create(in);
        } catch (Exception e) {
            log.error("代销商品批量退货导入",e);
        }
        BatchImportConsignmentSaleReturnOrderResultVO data =  saleReturnOrderService.consignmentReturnOrderImport(wb,supplierId);
        if(SpringUtil.isNotEmpty(data.getErrors())){
            return ImportConsignmentSaleReturnOrderEntry.fail(data.getErrors());
        }

        return ImportConsignmentSaleReturnOrderEntry.success(data.getList());

    }


    /**
     * 批量生成退货单
     *
     * @param saveConsignmentSaleReturnOrderODTOS
     * @return
     */
    @ApiOperation(value = "批量生成退货单")
    @RequestMapping(value = "/batchSaveConsignmentReturnOrder", method = RequestMethod.POST)
    public Boolean batchSaveConsignmentReturnOrder(@RequestBody List<SaveConsignmentSaleReturnOrderODTO> saveConsignmentSaleReturnOrderODTOS) {
        TokenInfo tokenInfo = FastThreadLocalUtil.getQY();
        Long createId = tokenInfo.getUserId();
        return saleReturnOrderService.batchSaveConsignmentReturnOrder(saveConsignmentSaleReturnOrderODTOS,createId);
    }

    /**
     * 代销退货明细
     */
    @ApiOperation(value = "代销退货明细")
    @GetMapping("/queryConsignmentSaleReturnOrderItemList/{consignmentSaleReturnOrderId}")
    public ConsignmentSaleReturnOrderDetailODTO queryConsignmentSaleReturnOrderItemList(@PathVariable("consignmentSaleReturnOrderId") Long saleReturnOrderId) {
        return saleReturnOrderService.queryConsignmentSaleReturnOrderItemList(saleReturnOrderId);
    }


    /***
     * 代销退货门店取消
     */
    @ApiOperation(value = "代销退货门店取消", notes = "代销退货门店取消")
    @GetMapping("/cancelConsignmentSaleReturnOrder/{consignmentSaleReturnOrderId}")
    public Boolean cancelConsignmentSaleReturnOrder(@PathVariable("consignmentSaleReturnOrderId") Long saleReturnOrderId) {
        return saleReturnOrderService.cancelSaleReturnOrder(saleReturnOrderId);
    }

    /***
     * 代销退货门店确认
     */
    @ApiOperation(value = "代销退货门店确认", notes = "代销退货门店确认")
    @PostMapping("/confirmConsignmentSaleReturnOrder")
    public Boolean confirmConsignmentSaleReturnOrder(@RequestBody ConfirmConsignmentReturnOrderIDTO dto) {
        TokenInfo tokenInfo = FastThreadLocalUtil.getQY();
        dto.setConfirmId(tokenInfo.getUserId());
        return saleReturnOrderService.confirmSaleReturnOrder(dto);
    }

    /***
     * 代销退货门店审核
     */
    @ApiOperation(value = "代销退货门店审核", notes = "代销退货门店审核")
    @PostMapping("/auditConsignmentSaleReturnOrder")
    public Boolean auditConsignmentSaleReturnOrder(@RequestBody AuditConsignmentReturnOrderIDTO dto) {
        TokenInfo tokenInfo = FastThreadLocalUtil.getQY();
        dto.setCheckUserId(tokenInfo.getUserId());
        return saleReturnOrderService.auditSaleReturnOrder(dto);
    }

    // 迁移order-manage
    @Deprecated
    @ApiOperation(value = "代销退货明细", notes = "代销退货明细")
    @PostMapping("/consignmentReturnOrderReport")
    @MethodRender
    public PageInfo<ConsignmentReturnOrderPageODTO> consignmentReturnOrderReport(@RequestBody ConsignmentReturnOrderQueryIDTO vo) {
        return saleReturnOrderService.consignmentReturnOrderReport(vo);
    }

    // 迁移order-manage
    @Deprecated
    @ApiOperation(value = "导出代销退货明细", notes = "导出代销退货明细")
    @GetMapping("exportConsignmentReturnOrderReport")
    public void exportConsignmentReturnOrderReport(ConsignmentReturnOrderQueryIDTO idto, HttpServletResponse httpServletResponse){
        idto.setPageNo(1);
        idto.setPageSize(Integer.MAX_VALUE);
        PageInfo<ConsignmentReturnOrderPageODTO> pageInfo = saleReturnOrderService.consignmentReturnOrderReport(idto);
        List<ConsignmentReturnOrderPageODTO> list = pageInfo.getList();
        if(!SpringUtil.isEmpty(list)){
            renderService.render(list,"/exportConsignmentReturnOrderReport");
        }
        try {
            ExcelUtil.setFileNameAndHead(httpServletResponse,"代销退货明细" + DateUtil.getDateFormate(new Date(), "yyyyMMddHHmmss"));
            EasyExcel.write(httpServletResponse.getOutputStream(),ConsignmentReturnOrderPageODTO.class)
                    .autoCloseStream(Boolean.FALSE).sheet("代销退货明细").doWrite(list);
        } catch (Exception e) {
            e.printStackTrace();
        }

    }
}
