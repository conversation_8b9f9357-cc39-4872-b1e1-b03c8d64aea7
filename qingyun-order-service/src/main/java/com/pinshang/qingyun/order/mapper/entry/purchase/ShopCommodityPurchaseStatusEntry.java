package com.pinshang.qingyun.order.mapper.entry.purchase;

import com.pinshang.qingyun.base.enums.CommodityPurchaseStatusEnum;
import com.pinshang.qingyun.base.enums.CommodityStatusEnum;
import com.pinshang.qingyun.base.enums.IsWeightEnums;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class ShopCommodityPurchaseStatusEntry {

    private Long shopId;

    private String shopCode;//门店编码

    private String shopName;//门店名称

    private Long commodityId;

    private String commodityCode;//商品编码

    private String barCode;//商品条码

    private String commodityName;//商品名称

    private String commoditySpec;//包装规格

    private Long commodityUnitId;
    private String commodityUnitName;//计量单位

    private Integer commodityPurchaseStatus;
    private String commodityPurchaseStatusName;//可采状态

    private Integer commodityState;

    private String commodityStateName;//状态：0-不可售,1-可售

    private BigDecimal commodityPackageSpec;//包装规格

    private BigDecimal salesBoxCapacity;//销售箱规

    private BigDecimal xdSalesBoxCapacity;//前置仓销售箱规

    private Integer isWeight;
    private String isWeightName;//是否称重

    private Integer appStatus;

    //app状态：0-上架，1-下架
    private String appStatusName;

    private String commodityAppName;

    private Long commodityPackageId;
    private String commodityPackageKind;//包装类型ID

    private String categoryName;

    public String getAppStatusName(){
        String str = "";
        if(null != this.appStatus){
            if(this.appStatus.equals(0)){
                str = "是";
            }else if(this.appStatus.equals(1)){
                str = "否";
            }
        }
        return str;
    }

    public String getCommodityPurchaseStatusName(){
        String str = "";
        if(null != this.commodityPurchaseStatus){
            if(this.commodityPurchaseStatus.equals(CommodityPurchaseStatusEnum.ENABLE.getCode())){
                str = "是";
            }else if(this.commodityPurchaseStatus.equals(CommodityPurchaseStatusEnum.DISABLE.getCode())){
                str = "否";
            }
        }
        return str;
    }
    public String getIsWeightName (){
        String str = "";
        if(this.isWeight.equals(IsWeightEnums.ENABLE.getCode())){//是否称重0-不称量,1-称重
            str = "是";
        }else if(this.isWeight.equals(IsWeightEnums.DISABLE.getCode())){
            str = "否";
        }
        return str;
    }

    public String getCommodityStateName(){
        String str = "";
        if(this.commodityState.equals(CommodityStatusEnum.START.getCode())){//是否称重0-不称量,1-称重
            str = "是";
        }else if(this.commodityState.equals(CommodityStatusEnum.STOP.getCode())){
            str = "否";
        }
        return str;
    }
}
