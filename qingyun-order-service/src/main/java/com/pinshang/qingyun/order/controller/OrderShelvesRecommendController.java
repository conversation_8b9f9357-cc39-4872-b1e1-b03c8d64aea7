package com.pinshang.qingyun.order.controller;

import com.pinshang.qingyun.order.dto.ShopOrderedQuantityODTO;
import com.pinshang.qingyun.order.dto.ShopOrderedQuantityQueryIDTO;
import com.pinshang.qingyun.order.service.CommodityFreshOrderService;
import com.pinshang.qingyun.order.service.OrderShelvesRecommendService;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * @Author: sk
 * @Date: 2021/9/1
 */
@RestController
@RequestMapping("/orderShelves")
public class OrderShelvesRecommendController {

    @Autowired
    private OrderShelvesRecommendService orderShelvesRecommendService;
    @Autowired
    private CommodityFreshOrderService commodityFreshOrderService;

    /**
     * 汇总日日鲜商品昨日下单数量(job调用)
     * @return
     */
    @RequestMapping(value = "/commodityFreshOrderSummary",method = RequestMethod.POST)
    @ApiOperation(value="汇总日日鲜商品昨日下单数量")
    public Boolean commodityFreshOrderSummary(@RequestParam(value = "orderTime",required = false) String orderTime) {
        return  commodityFreshOrderService.commodityFreshOrderSummary(orderTime);
    }


    /**
     * 统计门店在途数量
     * @return
     */
    @RequestMapping(value = "/countShopOrderedQuantity",method = RequestMethod.POST)
    @ApiOperation(value="在途数量=该门店该商品sum（T+1订单数量，T+N订单数量）")
    public Boolean countShopOrderedQuantity() {
        return  orderShelvesRecommendService.countShopOrderedQuantity();
    }


    /**
     * 启用线程池计算排面推荐数量并且加入购物车
     * @param hhmm
     * @return
     */
    @RequestMapping(value = "/shelvesRecommendAddShoppingCart",method = RequestMethod.POST)
    @ApiOperation(value="启用线程池计算推荐数量并且加入购物车")
    public Boolean shelvesRecommendAddShoppingCart(@RequestParam(value = "hhmm",required = false) String hhmm) {
        return  orderShelvesRecommendService.shelvesRecommendAddShoppingCart(hhmm);
    }

    /**
     * 根据订货日期、日日鲜商品idList查询在途数量
     * @return
     */
    @RequestMapping(value = "/queryShopOrderedQuantity",method = RequestMethod.POST)
    @ApiOperation(value="根据订货日期、日日鲜商品idList查询在途数量")
    public List<ShopOrderedQuantityODTO> queryShopOrderedQuantity(@RequestBody ShopOrderedQuantityQueryIDTO idto) {
        return  orderShelvesRecommendService.queryShopOrderedQuantity(idto);
    }

}
