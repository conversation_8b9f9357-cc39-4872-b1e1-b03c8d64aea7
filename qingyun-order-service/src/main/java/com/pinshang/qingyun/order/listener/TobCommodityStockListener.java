package com.pinshang.qingyun.order.listener;

import com.alibaba.fastjson.JSON;
import com.pinshang.qingyun.base.enums.BusinessTypeEnums;
import com.pinshang.qingyun.box.utils.JsonUtil;
import com.pinshang.qingyun.kafka.base.KafkaMessageWrapper;
import com.pinshang.qingyun.kafka.base.KafkaTopicConstant;
import com.pinshang.qingyun.order.service.TobCommodityStockService;
import com.pinshang.qingyun.order.service.WeChatSendMessageService;
import com.pinshang.qingyun.order.vo.tob.TobCommodityStockKafkaVo;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.stereotype.Component;

import java.util.concurrent.TimeUnit;

@Component
@Slf4j
public class TobCommodityStockListener {

    @Autowired
    private TobCommodityStockService tobCommodityStockService;
    @Autowired
    private RedissonClient redissonClient;
    @Autowired
    private WeChatSendMessageService weChatSendMessageService;

    @KafkaListener(
            topics = {"${application.name.switch}" + KafkaTopicConstant.TOB_COMMODITY_STOCK_CHANGE_TOPIC}
            , containerFactory = "kafkaListenerContainerFactory", errorHandler = "kafkaConsumerErrorHandler"
    )
    public void updateOrderStock(String message) {
        log.info("====================message==" + message);
        KafkaMessageWrapper messageWrapper = JSON.parseObject(message, KafkaMessageWrapper.class);
        if (messageWrapper != null && messageWrapper.getData() != null) {
            log.info("data:{}", messageWrapper.getData().toString());
            TobCommodityStockKafkaVo vo = JSON.parseObject(JsonUtil.java2json(messageWrapper.getData()), TobCommodityStockKafkaVo.class);
            Long commodityId = vo.getCommodityId();
            vo.setType(vo.getType() == null ? BusinessTypeEnums.SALE.getCode() : vo.getType());
            //tobCommodityStockService.updateTobCommodityStock(vo, commodityId);

            RLock lock = redissonClient.getLock("order:updateOrderStock:" + commodityId);
            lock.lock(3L, TimeUnit.SECONDS);
            try {
                tobCommodityStockService.updateTobCommodityStock(vo, commodityId);
            }catch (Exception e){
                weChatSendMessageService.sendWeChatMessage("更新库存失败,commodityId: " + commodityId);
                log.error("更新库存失败,入参 {}", JsonUtil.java2json(vo), e);
            }finally {
                if(lock.isLocked() && lock.isHeldByCurrentThread()){
                    lock.unlock(); // 释放锁
                }
            }
        } else {
            log.error("接收到一条不支持的类型消息:[{}]", message);
        }
    }

}
