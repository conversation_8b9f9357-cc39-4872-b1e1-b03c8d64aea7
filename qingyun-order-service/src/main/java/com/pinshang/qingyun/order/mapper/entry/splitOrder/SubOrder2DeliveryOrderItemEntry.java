package com.pinshang.qingyun.order.mapper.entry.splitOrder;

import com.pinshang.qingyun.box.utils.SpringUtil;
import com.pinshang.qingyun.storage.dto.SubOrder2DeliveryOrderItemEntryIDTO;
import lombok.Data;

import java.math.BigDecimal;

/*
 * 用于生成do明细
 */
@Data
public class SubOrder2DeliveryOrderItemEntry {
    private Long commodityId;
    private BigDecimal quantity;
    private BigDecimal price;
    private Long orderItemId;
    private Long subOrderId;

    private Long targetCommodityId;
    //组合商品转换状态：0=无转换，1=有转换
    private Integer convertStatus;
    //组合商品源转换比率
    private Integer sourceRatio;
    //组合商品目标转换比率
    private Integer targetRatio;
    //组合商品目标数量 : 向上取整 ( quantity / source_ratio * target_ratio)
    private BigDecimal targetQuantity;

    public static SubOrder2DeliveryOrderItemEntryIDTO convert(SubOrder2DeliveryOrderItemEntry itemEntry){
        SubOrder2DeliveryOrderItemEntryIDTO itemEntryIDTO = new SubOrder2DeliveryOrderItemEntryIDTO();
        SpringUtil.copyProperties(itemEntry, itemEntryIDTO);
        return itemEntryIDTO;
    }

}
