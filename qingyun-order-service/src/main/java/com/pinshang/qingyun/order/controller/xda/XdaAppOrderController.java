package com.pinshang.qingyun.order.controller.xda;

import com.pinshang.qingyun.base.api.ApiResponse;
import com.pinshang.qingyun.base.configure.expand.QYAssert;
import com.pinshang.qingyun.order.config.LockConstants;
import com.pinshang.qingyun.order.dto.file.FileODTO;
import com.pinshang.qingyun.order.dto.xda.*;
import com.pinshang.qingyun.order.service.xda.CommoditySaleMultiDayStatisticsService;
import com.pinshang.qingyun.order.service.xda.XdaOrderService;
import io.swagger.annotations.ApiOperation;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;

@RestController
@RequestMapping("/xda/order")
@Deprecated
public class XdaAppOrderController {

    @Autowired
    private XdaOrderService xdaOrderService;
    @Autowired
    private RedissonClient redissonClient;
    @Autowired
    private CommoditySaleMultiDayStatisticsService commoditySaleMultiDayStatisticsService;


    @PostMapping("/preOrder")
    @ApiOperation(value = "预览订单")
    public PreOrderDTO preOrderView(@RequestBody XdaPreOrderDTO query){
        QYAssert.isFalse("请升级到最新版本");
        return null;
    }
    @PostMapping("/creatOrder")
    @ApiOperation(value = "创建订单")
    public boolean creatOrder(@RequestBody CreatePrePayOrderIDTO idto){
        QYAssert.isFalse("请升级到最新版本");
        return false;
    }
    @PostMapping("/list")
    @ApiOperation(value = "订单列表")
    public ApiResponse<XdaOrder4AppODTO> queryOrderListByPage(@RequestBody XdaQueryOrder4AppParam param, HttpServletResponse response){
        QYAssert.isFalse("请升级到最新版本");
        return null;
    }
    @GetMapping("/detail")
    @ApiOperation(value = "订单明细")
    public XdaOrder4AppODTO queryOrderDetailByCode(@RequestParam(value = "orderCode",required = false) String orderCode){
        QYAssert.isFalse("请升级到最新版本");
        return null;
    }

    @GetMapping("/copy")
    @ApiOperation(value = "订单复制")
    public Integer copyOrderById4App(@RequestParam(value = "orderId",required = false) Long orderId){
        QYAssert.isFalse("请升级到最新版本");
        return null;
    }

    @PostMapping("/cancel")
    @ApiOperation(value = "订单取消")
    public Integer cancelOrder(@RequestBody OrderCancelIDTO idto){
        QYAssert.isFalse("请升级到最新版本");
        return null;
    }

    /**
     * 定时任务 将昨天配送中的数据改为配送完成
     * @param date
     * @return
     */
    @GetMapping("/job/complete")
    public Integer autoCompleteXdaOrder(@RequestParam(value = "date",required = false) String date){
        return xdaOrderService.autoCompleteXdaOrder(date);
    }

    @GetMapping("/job/updateSaleStatistics")
    public Boolean updateSaleStatistics() {
       return commoditySaleMultiDayStatisticsService.updateSaleStatistics();
    }
    
    
    @ApiOperation(value = "查询  订单PDF文件")
    @GetMapping("/queryOrderPdfFile")
    public FileODTO queryOrderPdfFile(@RequestParam(value = "orderId",required = false) Long orderId){
        //return xdaOrderService.queryOrderPdfFile(orderId);
        String lockKey = LockConstants.generateXdaQueryPdfLockKey(orderId);
        RLock lock = redissonClient.getLock(lockKey);
        if(lock.tryLock()){
            try {
                return xdaOrderService.queryOrderPdfFile(orderId);
            } finally {
                lock.unlock();
            }
        }else {
            QYAssert.isFalse("系统繁忙,请勿频繁操作!");
        }
        return null;
    }
    
}
