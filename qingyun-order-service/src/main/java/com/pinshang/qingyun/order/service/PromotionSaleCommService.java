package com.pinshang.qingyun.order.service;

import com.pinshang.qingyun.box.utils.SpringUtil;
import com.pinshang.qingyun.order.mapper.PromotionSaleCommMapper;
import com.pinshang.qingyun.order.mapper.PromotionSaleMapper;
import com.pinshang.qingyun.order.model.order.ProductPriceModelList;
import com.pinshang.qingyun.order.model.promotionSale.PromotionSale;
import com.pinshang.qingyun.order.model.promotionSale.PromotionSaleComm;
import com.pinshang.qingyun.order.util.list.ListExtractor;
import com.pinshang.qingyun.order.util.list.ListToMapConverter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * @Author: sk
 * @Date: 2024/7/18
 */
@Slf4j
@Service
public class PromotionSaleCommService {

    @Autowired
    PromotionSaleCommMapper promotionSaleCommMapper;

    @Autowired
    ProductPriceModelListService productPriceModelListService;

    public List<PromotionSaleComm> findListByPromotionId(Long promotionId){
        Example example = new Example(PromotionSaleComm.class);
        example.createCriteria().andEqualTo("promotionId",promotionId);
        return promotionSaleCommMapper.selectByExample(example);
    }

    //获取配比商品价格
    public void findSaleCommodityPrice(String storeId, List<PromotionSaleComm> list) {
        if (SpringUtil.isNotEmpty(list)) {

            List<Long> commodityIdList = ListExtractor.extractToList(list, PromotionSaleComm::getCommId);

            Map<Long, BigDecimal> productPriceModelMap =productPriceModelListService.getProductPriceModelMap(Long.valueOf(storeId),commodityIdList);

            list.forEach(p -> {
                p.setCommodityPrice(productPriceModelMap.get(p.getCommId()));
            });
        }
    }


}
