package com.pinshang.qingyun.order.controller;

import com.github.pagehelper.PageInfo;
import com.pinshang.qingyun.order.model.auto.AutoShopCommodityLog;
import com.pinshang.qingyun.order.service.auto.AutoSettingService;
import com.pinshang.qingyun.order.service.auto.AutoShopCommodityLogService;
import com.pinshang.qingyun.order.service.auto.AutoShopCommodityService;
import com.pinshang.qingyun.order.vo.auto.*;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.ss.usermodel.WorkbookFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.InputStream;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * 迁移至order-manage, pda操作暂不迁移
 */
@Deprecated
@Slf4j
@RequestMapping("/autoShopCommodity")
@RestController
public class AutoShopCommodityController {

    @Autowired
    private AutoShopCommodityService autoShopCommodityService;

    @Autowired
    private AutoSettingService autoSettingService;

    @Autowired
    private AutoShopCommodityLogService autoShopCommodityLogService;


    @ApiOperation(value = "查询门店自动订货商品列表")
    @PostMapping("/commodityPageByShopId")
    public PageInfo<AutoShopCommodityListVO> commodityPageByShopId(@RequestBody AutoShopCommodityRequestVO vo) {
        return autoShopCommodityService.commodityPageByShopId(vo);
    }

    /**
     * 导出门店商品列表
     * @param vo
     * @return
     */
    @ApiOperation(value = "导出门店自动订货商品列表")
    @PostMapping("/exportCommodityByShopId")
    public List<AutoShopCommodityListVO> exportCommodityByShopId(@RequestBody AutoShopCommodityRequestVO vo) {
        vo.initExportPage();
        PageInfo<AutoShopCommodityListVO> pageDate = autoShopCommodityService.commodityPageByShopId(vo);
        if (pageDate.getSize() > 0) {
            return pageDate.getList();
        }
        return new ArrayList<>();
    }

    @ApiOperation(value = "删除门店自动订货商品")
    @GetMapping("deleteAutoShopCommodity")
    public Integer  deleteAutoShopCommodity(@RequestParam(value = "shopCommodityId",required = false) Long shopCommodityId) {
        return autoShopCommodityService.deleteAutoShopCommodity(shopCommodityId);
    }

    @ApiOperation(value = "删除门店下所有自动订货商品")
    @GetMapping("deleteAllAutoShopCommodity")
    public Integer  deleteAllAutoShopCommodity(@RequestParam(value = "shopId",required = false) Long shopId) {
        return autoShopCommodityService.deleteAllAutoShopCommodity(shopId);
    }

    @ApiOperation(value = "导入门店自动订货安全库存")
    @PostMapping("importStockQuantity")
    public List<String> importStockQuantity(@RequestParam(value = "file", required = true) MultipartFile file,
                                            @RequestParam(value = "shopId", required = true) Long shopId) {
        Workbook wb = null;
        try {
            InputStream in = file.getInputStream();
            wb = WorkbookFactory.create(in);
        } catch (Exception e) {
            log.error("导入门店自动订货安全库存",e);
        }
        return autoShopCommodityService.importStockQuantity(wb, shopId);
    }

    @ApiOperation(value = "门店自动订货开启或关闭")
    @PostMapping("updateStatus")
    public Boolean updateStatus(@RequestParam(value = "shopId",required = false) Long shopId, @RequestParam(value = "status",required = false) Integer status) {
        return autoSettingService.updateStatus(shopId, status);
    }

    @ApiOperation(value = "自定订货设置列表")
    @PostMapping("autoSettingPage")
    public PageInfo<AutoSettingPageVO> autoSettingPage(@RequestBody AutoSettingRequestVO resVO){
        return autoSettingService.autoSettingPage(resVO);
    }

    @ApiOperation(value = "自定订货设置列表导出")
    @PostMapping("exportAutoSettingPage")
    public List<AutoSettingPageVO> exportAutoSettingPage(@RequestBody AutoSettingRequestVO resVO){
        resVO.initExportPage();
        return autoSettingService.autoSettingPage(resVO).getList();
    }

    /**
     * 获取所有门店的
     * @return
     */
    @ApiOperation(value = "上个月销售品项")
    @GetMapping("lastMonthSale")
    public boolean lastMonthSale() {
        return autoSettingService.lastMonthSale();
    }

    @ApiOperation(value = "上个月销售品项列表")
    @PostMapping("lastMonthSalePage")
    public PageInfo<AutoShopCommodityListVO> lastMonthSalePage(@RequestBody AutoShopCommodityRequestVO vo) {
        return autoSettingService.lastMonthSalePage(vo);
    }

    @ApiOperation(value = "导出上个月销售品项列表")
    @PostMapping("exportLastMonthSalePage")
    public List<AutoShopCommodityListVO> exportLastMonthSalePage(@RequestBody AutoShopCommodityRequestVO vo) {
        vo.initExportPage();
        return autoSettingService.lastMonthSalePage(vo).getList();
    }

    @ApiOperation(value = "要求单店品项列表")
    @PostMapping("requireShopItemsPage")
    public PageInfo<AutoShopCommodityListVO> requireShopItemsPage(@RequestBody AutoShopCommodityRequestVO vo) {
        return autoSettingService.requireShopItemsPage(vo);
    }

    @ApiOperation(value = "导出要求单店品项列表")
    @PostMapping("exportRequireShopItemsPage")
    public List<AutoShopCommodityListVO> exportRequireShopItemsPage(@RequestBody AutoShopCommodityRequestVO vo) {
        vo.initExportPage();
        return autoSettingService.requireShopItemsPage(vo).getList();
    }

    @ApiOperation(value = "完成品项列表")
    @PostMapping("finishItemsPage")
    public PageInfo<AutoShopCommodityListVO> finishItemsPage(@RequestBody AutoShopCommodityRequestVO vo) {
        return autoSettingService.finishItemsPage(vo);
    }

    @ApiOperation(value = "导出完成品项列表")
    @PostMapping("exportFinishItemsPage")
    public List<AutoShopCommodityListVO> exportFinishItemsPage(@RequestBody AutoShopCommodityRequestVO vo) {
        vo.initExportPage();
        return autoSettingService.finishItemsPage(vo).getList();
    }

    @ApiOperation(value = "未完成品项列表")
    @PostMapping("notFinishedItemsPage")
    public PageInfo<AutoShopCommodityListVO> notFinishedItemsPage(@RequestBody AutoShopCommodityRequestVO vo) {
        return autoSettingService.notFinishedItemsPage(vo);
    }

    @ApiOperation(value = "导出未完成品项列表")
    @PostMapping("exportNotFinishedItemsPage")
    public List<AutoShopCommodityListVO> exportNotFinishedItemsPage(@RequestBody AutoShopCommodityRequestVO vo) {
        vo.initExportPage();
        return autoSettingService.notFinishedItemsPage(vo).getList();
    }

    @ApiOperation(value = "日志列表")
    @PostMapping("logPage")
    public PageInfo<AutoShopCommodityLog> logPage(@RequestBody AutoShopCommodityLogRequestVO vo){
        return autoShopCommodityLogService.logPage(vo);
    }

    @ApiOperation(value = "自动清理直送商品")
    @GetMapping("deleteDirectSendingCommodity")
    public Boolean deleteDirectSendingCommodity(){
        return autoShopCommodityService.deleteDirectSendingCommodity();
    }


    @ApiOperation(value = "pda自动订货设置查询")
    @GetMapping("/commodityInfo")
    public AutoCommodityVO commodityInfo(@RequestParam(value = "barCode", required = false) String barCode) {
        return autoShopCommodityService.commodityInfo(barCode);
    }

    @ApiOperation(value = "pda自动订货设置添加")
    @GetMapping("/insertAutoOrderCommodity")
    public Boolean insertAutoOrderCommodity(@RequestParam(value = "commodityId", required = false) Long commodityId, @RequestParam(value = "stockQuantity", required = false) BigDecimal stockQuantity) {
        return autoShopCommodityService.insertAutoOrderCommodity(commodityId, stockQuantity);
    }
}
