package com.pinshang.qingyun.order.service;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.pinshang.qingyun.base.api.TokenInfo;
import com.pinshang.qingyun.base.configure.expand.QYAssert;
import com.pinshang.qingyun.base.util.FastThreadLocalUtil;
import com.pinshang.qingyun.common.dto.DictionaryODTO;
import com.pinshang.qingyun.common.service.DictionaryClient;
import com.pinshang.qingyun.order.dto.ShopReceiveSettingIDTO;
import com.pinshang.qingyun.order.dto.ShopReceiveSettingODTO;
import com.pinshang.qingyun.order.enums.ShopReceiveTypeEnums;
import com.pinshang.qingyun.order.mapper.ShopReceiveSettingLogMapper;
import com.pinshang.qingyun.order.mapper.ShopReceiveSettingMapper;
import com.pinshang.qingyun.order.model.shop.ShopReceiveSetting;
import com.pinshang.qingyun.order.model.shop.ShopReceiveSettingLog;
import com.pinshang.qingyun.smm.dto.user.SelectUserShopIdListIDTO;
import com.pinshang.qingyun.smm.service.SMMUserClient;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Created by zhaoranguang on 2017/6/7.
 */
@Service
@Slf4j
public class ShopReceiveSettingService {

    @Autowired
    private ShopReceiveSettingMapper shopReceiveSettingMapper;

    @Autowired
    private ShopReceiveSettingLogMapper shopReceiveSettingLogMapper;

    @Autowired
    private DictionaryClient dictionaryClient;

    @Autowired
    private SMMUserClient smmUserClient;

    /**
     * 查看门店收货设置(分页)
     * @param shopReceiveSettingIDTO
     * @return
     */
    public PageInfo<ShopReceiveSettingODTO> getShopReceiveSettingPage(ShopReceiveSettingIDTO shopReceiveSettingIDTO) {

        PageInfo <ShopReceiveSettingODTO> pageDate = null ;
        pageDate = PageHelper.startPage(shopReceiveSettingIDTO.getPageNo(), shopReceiveSettingIDTO.getPageSize()).doSelectPageInfo(() -> {
            shopReceiveSettingMapper.getShopReceiveSettingPage(shopReceiveSettingIDTO);
        });

        return pageDate;
    }


    /**
     * 查询收货设置
     * @param shopId
     * @return
     */
    public ShopReceiveSettingODTO getShopReceiveSettingByShopId(Long shopId) {
        List<Long> shopIdList = smmUserClient.selectUserShopIdList( SelectUserShopIdListIDTO.onlyDb(FastThreadLocalUtil.getQY().getUserId()));
        QYAssert.isTrue( null != shopId,"门店id不能为空");
        QYAssert.isTrue(shopIdList.contains(shopId),"您没有该门店的操作权限");

        ShopReceiveSettingIDTO shopReceiveSettingIDTO = new ShopReceiveSettingIDTO();
        shopReceiveSettingIDTO.setShopId(shopId);
        List<ShopReceiveSettingODTO> list = shopReceiveSettingMapper.getShopReceiveSettingPage(shopReceiveSettingIDTO);
        ShopReceiveSettingODTO settODTO = list.get(0);
        settODTO.setReceiveType(null == settODTO.getReceiveType() ? ShopReceiveTypeEnums.AUTOMATIC.getCode() : settODTO.getReceiveType());
        return settODTO;
    }

    public void  checkTime(){
        // 判断操作时间
        DictionaryODTO dictionaryODTO = dictionaryClient.getDictionaryByCode("shopReceiveTimeSet");
        String timeStr [] = dictionaryODTO.getOptionValue().split("-");

        Calendar c1 = Calendar.getInstance();
        c1.set(Calendar.HOUR_OF_DAY, Integer.parseInt(timeStr[0]));
        c1.set(Calendar.MINUTE, 0);
        c1.set(Calendar.SECOND, 0);


        Calendar c2 = Calendar.getInstance();
        c2.set(Calendar.HOUR_OF_DAY,Integer.parseInt(timeStr[1]));
        c2.set(Calendar.MINUTE, 0);
        c2.set(Calendar.SECOND, 0);

        Boolean isOk = new Date().after(c1.getTime()) && new Date().before(c2.getTime());
        QYAssert.isTrue(isOk,"非工作时间不允许切换收货方式");
    }
    /**
     * 切换模式
     * @param shopId
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public Boolean changeReceiveType(Long shopId,Integer receiveType) {
        // 判断操作时间
        checkTime();

        TokenInfo tokenInfo = FastThreadLocalUtil.getQY();
        QYAssert.isTrue( null != shopId,"门店id不能为空");
        QYAssert.isTrue( null != receiveType,"收货方式不能为空");

        ShopReceiveSettingIDTO shopReceiveSettingIDTO = new ShopReceiveSettingIDTO();
        shopReceiveSettingIDTO.setShopId(shopId);
        List<ShopReceiveSettingODTO> list = shopReceiveSettingMapper.getShopReceiveSettingPage(shopReceiveSettingIDTO);
        ShopReceiveSettingODTO settODTO = list.get(0);

        if(null == settODTO.getSettingId()){
            ShopReceiveSetting  shopReceiveSetting = new ShopReceiveSetting();
            shopReceiveSetting.setShopId(settODTO.getShopId());
            shopReceiveSetting.setReceiveType(receiveType);
            shopReceiveSetting.setCreateId(tokenInfo.getUserId());
            shopReceiveSetting.setCreateTime(new Date());
            shopReceiveSetting.setUpdateId(tokenInfo.getUserId());
            shopReceiveSetting.setUpdateTime(new Date());
            shopReceiveSettingMapper.insert(shopReceiveSetting);

            // 记录日志
            addShopReceiveSettingLog(tokenInfo.getUserId(),tokenInfo.getRealName(), settODTO.getShopId(), receiveType);
        }else {
            ShopReceiveSetting  shopReceiveSetting = new ShopReceiveSetting();
            shopReceiveSetting.setId(settODTO.getSettingId());
            shopReceiveSetting.setReceiveType(receiveType);
            shopReceiveSetting.setUpdateId(tokenInfo.getUserId());
            shopReceiveSetting.setUpdateTime(new Date());
            shopReceiveSettingMapper.updateByPrimaryKeySelective(shopReceiveSetting);

            // 记录日志
            addShopReceiveSettingLog(tokenInfo.getUserId(),tokenInfo.getRealName(),settODTO.getShopId(), receiveType);
        }
        return true;
    }

    @Transactional(rollbackFor = Exception.class)
    public void addShopReceiveSettingLog(Long userId,String userName, Long shopId, Integer code) {
        ShopReceiveSettingLog log = new ShopReceiveSettingLog();
        log.setShopId(shopId);
        log.setReceiveType(code);
        log.setCreateId(userId);
        log.setCreateName(userName);
        log.setCreateTime(new Date());
        shopReceiveSettingLogMapper.insert(log);
    }


    /**
     * 查看门店收货设置log(分页)
     * @param shopReceiveSettingIDTO
     * @return
     */
    public PageInfo<ShopReceiveSettingODTO> getShopReceiveSettingLog(ShopReceiveSettingIDTO shopReceiveSettingIDTO) {

        PageInfo <ShopReceiveSettingODTO> pageDate = null ;
        pageDate = PageHelper.startPage(shopReceiveSettingIDTO.getPageNo(), shopReceiveSettingIDTO.getPageSize()).doSelectPageInfo(() -> {
            shopReceiveSettingMapper.getShopReceiveSettingLogPage(shopReceiveSettingIDTO);
        });

        return pageDate;
    }


    /**
     * 根据收货方式查询其下面的所有门店
     * @param receiveType
     * @return
     */
    public List<Long> getShopIdList(Integer receiveType){
        List<ShopReceiveSettingODTO> list = shopReceiveSettingMapper.getShopReceiveSettingPage(new ShopReceiveSettingIDTO());
        if(receiveType.equals(ShopReceiveTypeEnums.AUTOMATIC.getCode())){
            List<ShopReceiveSettingODTO> filterList = list.stream().filter(p -> p.getReceiveType() == null || p.getReceiveType().equals(ShopReceiveTypeEnums.AUTOMATIC.getCode())).collect(Collectors.toList());
            return filterList.stream().map(item -> item.getShopId()).collect(Collectors.toList());
        }else if(receiveType.equals(ShopReceiveTypeEnums.HANDLE.getCode())){
            List<ShopReceiveSettingODTO> filterList = list.stream().filter(p -> p.getReceiveType() != null && p.getReceiveType().equals(ShopReceiveTypeEnums.HANDLE.getCode())).collect(Collectors.toList());
            return filterList.stream().map(item -> item.getShopId()).collect(Collectors.toList());
        }else {
            return new ArrayList<>();
        }
    }
}
