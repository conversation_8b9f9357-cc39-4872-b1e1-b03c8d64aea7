package com.pinshang.qingyun.order.listener;/**
 * @Author: sk
 * @Date: 2025/8/1
 */

import com.pinshang.qingyun.order.mapper.entry.commodity.CommodityBasicEntry;
import com.pinshang.qingyun.order.service.ConsigneeCommonService;
import com.pinshang.qingyun.order.vo.commodity.CommodityVO;
import com.pinshang.qinyun.cache.utils.RedisDelayQueueHandle;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025年08月01日 上午10:05
 */
@Component
@Slf4j
public class DdShortReturnDelayFreed implements RedisDelayQueueHandle<String> {

    @Autowired
    private ConsigneeCommonService consigneeCommonService;

    @Override
    public void execute(String occupyCode) {
        log.info("大店手动收货生成少货单：{}", occupyCode);
        String str[] = occupyCode.split(",");
        Long docId = Long.parseLong((StringUtils.isNotBlank(str[0]) ? str[0] : "-1"));
        Long enterpriseId = Long.parseLong((StringUtils.isNotBlank(str[1]) ? str[1] : "78"));
        Long userId = Long.parseLong((StringUtils.isNotBlank(str[2]) ? str[2] : "-1"));

        consigneeCommonService.addBigShopShortReturnDelay(docId, enterpriseId, userId);

    }
}
