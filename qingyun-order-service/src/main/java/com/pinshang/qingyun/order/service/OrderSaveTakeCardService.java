package com.pinshang.qingyun.order.service;

import com.pinshang.qingyun.base.enums.StoreBillTypeEnums;
import com.pinshang.qingyun.base.enums.YesOrNoEnums;
import com.pinshang.qingyun.base.enums.order.OrderTypeEnum;
import com.pinshang.qingyun.base.enums.xda.XdaOrderProcessStatusEunm;
import com.pinshang.qingyun.box.utils.DateTimeUtil;
import com.pinshang.qingyun.box.utils.IDGenerator;
import com.pinshang.qingyun.box.utils.SpringUtil;
import com.pinshang.qingyun.order.bo.StoreDeductionBO;
import com.pinshang.qingyun.order.enums.OrderModeType;
import com.pinshang.qingyun.order.enums.OrderPrintTypeEnum;
import com.pinshang.qingyun.order.mapper.*;
import com.pinshang.qingyun.order.model.order.*;
import com.pinshang.qingyun.order.vo.store.StoreCompanyVo;
import com.pinshang.qingyun.price.dto.commodity.CommodityListRequestIDTO;
import com.pinshang.qingyun.price.dto.commodity.CommodityResultODTO;
import com.pinshang.qingyun.price.service.ProductPriceModelClient;
import com.pinshang.qingyun.shop.admin.dto.TakeAppointmentCardODTO;
import com.pinshang.qingyun.shop.admin.service.TakeAppointmentClient;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.entity.Example;

import java.math.BigDecimal;
import java.util.*;

/**
 * @Author: xx
 */
@Slf4j
@Service
public class OrderSaveTakeCardService {

    @Autowired
    private ShoppingCartMapper shoppingCartMapper;

    @Autowired
    private OrderListGiftMapper orderListGiftMapper;
    @Autowired
    private OrderBillMapper orderBillMapper;
    @Autowired
    private OrderMapper orderMapper;
    @Autowired
    private OrderListMapper orderListMapper;
    @Autowired
    private StoreSettlementMapper storeSettlementMapper;
    @Autowired
    private StoreMapper storeMapper;
    @Lazy
    @Autowired
    private OrderService orderService;

    @Autowired
    private ProductPriceModelClient productPriceModelClient;
    @Autowired
    TakeCardOrderBillMapper takeCardOrderBillMapper;

    @Autowired
    private TakeAppointmentClient takeAppointmentClient;
    @Autowired
    private StoreRechargeService storeRechargeService;

    @Autowired
    private OrderSaveTakeCardService orderSaveTakeCardService;

    public String pullTakeBill(String appointDate){
        List<TakeAppointmentCardODTO> takeAppointmentCardODTOS = takeAppointmentClient.querytakeAppointmentCardList(appointDate);
        if(!takeAppointmentCardODTOS.isEmpty()){
            HashMap<Long,Set<Long>> storeCommodityMaps = new HashMap<>();
            for (TakeAppointmentCardODTO takeAppointmentCardODTO : takeAppointmentCardODTOS) {
                if(storeCommodityMaps.containsKey(takeAppointmentCardODTO.getStoreId())){
                    storeCommodityMaps.get(takeAppointmentCardODTO.getStoreId()).add(takeAppointmentCardODTO.getCommodityId());
                }else{
                    storeCommodityMaps.put(takeAppointmentCardODTO.getStoreId(),new HashSet<Long>(){{add(takeAppointmentCardODTO.getCommodityId());}});
                }
            }
            /**
             * 封装 客户&商品 -》价格
             */
            HashMap<Long, HashMap<Long,CommodityResultODTO>> storePriceMaps = new HashMap<>();
            for(Map.Entry<Long,Set<Long>> entry : storeCommodityMaps.entrySet()){
                CommodityListRequestIDTO idto = new CommodityListRequestIDTO();
                idto.setStoreId(entry.getKey().toString());
                idto.setCommodityIdListAll(new ArrayList<>(entry.getValue()));
                List<CommodityResultODTO> commodityList = productPriceModelClient.findStoreCommodityList(idto);
                // 客户价格方案商品存在
                if(!commodityList.isEmpty()){
                    for (CommodityResultODTO commodityResultODTO : commodityList) {
                        Long resultStoreId = Long.valueOf(commodityResultODTO.getStoreId());
                        Long resultCommodityId = Long.valueOf(commodityResultODTO.getCommodityId());
                        HashMap<Long, CommodityResultODTO> resultODTOHashMap = new HashMap<>();
                        resultODTOHashMap.put(resultCommodityId,commodityResultODTO);
                        //客户存在，进入set商品价格
                        if(storePriceMaps.containsKey(resultStoreId)){
                            //客户存在 赋予当前客户的商品金额
                            storePriceMaps.get(resultStoreId).putAll(resultODTOHashMap);
                        }else{
                            //客户不存在
                            storePriceMaps.put(resultStoreId,resultODTOHashMap);
                        }
                    }
                }
            }

            List<TakeCardOrderBill> inserts = new ArrayList<>();
            for (TakeAppointmentCardODTO takeAppointmentCardODTO : takeAppointmentCardODTOS) {
                BigDecimal price = BigDecimal.ZERO;
                HashMap<Long, CommodityResultODTO> longCommodityResultODTOHashMap = storePriceMaps.get(takeAppointmentCardODTO.getStoreId());
                if(longCommodityResultODTOHashMap!=null){
                    CommodityResultODTO commodityResultODTO = longCommodityResultODTOHashMap.get(takeAppointmentCardODTO.getCommodityId());
                    if(commodityResultODTO!=null){
                        price=commodityResultODTO.getCommodityPrice();
                    }
                }
                TakeCardOrderBill bill = new TakeCardOrderBill();
                BeanUtils.copyProperties(takeAppointmentCardODTO,bill);
                bill.setCommodityPrice(price);
                bill.setRealTotalPrice(bill.getCommodityPrice());
                bill.setRealQuantity(BigDecimal.ONE);
                bill.setCreateId(-1L);
                bill.setUpdateId(-1L);
                inserts.add(bill);
            }

            takeCardOrderBillMapper.insertTakeCardOrderBills(inserts);
        }
        return null;
    }

    public void generateCardOrder(){
        List<TakeCardOrderBill> takeCardOrderBillByStatus = takeCardOrderBillMapper.findTakeCardOrderBillByStatus();
       for (TakeCardOrderBill cardOrderBillByStatus : takeCardOrderBillByStatus) {
               try {
                   orderSaveTakeCardService.saveOrder(cardOrderBillByStatus);
               }catch (Exception e){
                   log.error("========",cardOrderBillByStatus.toString());
                   log.error("生成提货卡订单失败：",e);
                   throw e;
               }
           }
    }



    @Transactional(rollbackFor = Exception.class)
    public Order saveOrder(TakeCardOrderBill cardOrderBillByStatus){
        Order order = new Order();
        Date date = new Date();
        order.setEnterpriseId(78L);
        order.setCreateId(cardOrderBillByStatus.getCreateId());
        order.setUpdateId(cardOrderBillByStatus.getCreateId());
        order.setCreateTime(date);
        order.setUpdateTime(date);
        order.setOrderAmount(cardOrderBillByStatus.getRealTotalPrice());
        order.setFinalAmount(cardOrderBillByStatus.getRealTotalPrice());
        order.setStoreId(Long.valueOf(cardOrderBillByStatus.getStoreId()));
        order.setOrderTime(DateTimeUtil.parse(cardOrderBillByStatus.getAppointDate(), "yyyy-MM-dd"));
        order.setOrderCode(IDGenerator.newOrderCode());
        order.setEnterpriseId(78L);
        order.setPrintNum(3);
        order.setOrderType(OrderTypeEnum.TAKE_STORE_B_AUTO_ORDER.getCode());
        // 配送费默认为0
        order.setFreightAmount(BigDecimal.ZERO);
        order.setModeType(OrderModeType.ORDER.getCode());
        order.setPrintType(OrderPrintTypeEnum.NOPRInteger);
        order.setOrderRemark(cardOrderBillByStatus.getShopName()+" "+cardOrderBillByStatus.getCardNo());
        order.setOrderStatus(0);
        order.setChangePriceStatus(YesOrNoEnums.NO.getCode());
        order.setSyncStatus(YesOrNoEnums.NO.getCode());
        order.setSettleStatus(0);
        order.setProcessStatus(XdaOrderProcessStatusEunm.DELIVERY_COMPLETED.getCode());

        // 根据storeId获取所属公司ID
        StoreCompanyVo storeCompanyVo = storeMapper.getStoreCompanyByStoreId(order.getStoreId());
        order.setCompanyId(storeCompanyVo.getCompanyId());
        order.setStoreTypeId(storeCompanyVo.getStoreTypeId());
        order.setSettleOrderTime(order.getOrderTime());
        this.orderMapper.insert(order);
        // 提货卡流水标记已处理
        cardOrderBillByStatus.setStatus(YesOrNoEnums.YES.getCode());
        cardOrderBillByStatus.setRemark(order.getId().toString());
        takeCardOrderBillMapper.upStatusById(cardOrderBillByStatus);

        // 订单 mirror
        orderService.crateOrderMirror(order);
        // 订单明细
        OrderList orderList = new OrderList();
        orderList.setCommodityId(cardOrderBillByStatus.getCommodityId());
        orderList.setCommodityNum(cardOrderBillByStatus.getRealQuantity());
        orderList.setType(1);
        orderList.setRemark("提货卡商品");
        orderList.setCommodityPrice(cardOrderBillByStatus.getCommodityPrice());
        orderList.setTotalPrice(cardOrderBillByStatus.getRealTotalPrice());
        orderList.setOrderId(order.getId());
        orderList.setRealTotalPrice(cardOrderBillByStatus.getCommodityPrice());
        orderList.setRealQuantity(cardOrderBillByStatus.getRealQuantity());
        this.orderListMapper.insert(orderList);

        // 订单实发明细
        OrderListGift gift = new OrderListGift();
        gift.setCommodityId(cardOrderBillByStatus.getCommodityId());
        gift.setCommodityNum(cardOrderBillByStatus.getRealQuantity());
        gift.setType(orderList.getType());
        gift.setRemark(orderList.getRemark());
        gift.setCommodityPrice(orderList.getCommodityPrice());
        gift.setOrderId(order.getId());
        gift.setTotalPrice(orderList.getTotalPrice());
        gift.setRealTotalPrice(orderList.getRealTotalPrice());
        gift.setRealQuantity(orderList.getRealQuantity());
        this.orderListGiftMapper.insert(gift);

        // 更新预付款余额，订单扣款记录对账明细
        takeCardDeductPayment(order);

        return order;
    }

    /**
     * 提货卡下单扣款
     *
     * @param order 订单信息
     */
    public void takeCardDeductPayment(Order order) {
        // 更新预付款余额，订单扣款记录对账明细
        Boolean preStore = storeRechargeService.isPreStore(order.getStoreId());
        //预付费客户
        if (Objects.equals(preStore, Boolean.TRUE)) {
            String remark = "<--提货卡扣款:" + order.getOrderCode() + " -->";
            int billType = StoreBillTypeEnums.TIHUOKA_DEDUCTION.getCode();
            //扣款
            StoreDeductionBO deductionBO = StoreDeductionBO.builder()
                    .orderCode(order.getOrderCode())
                    .orderId(order.getId())
                    .orderAmount(order.getOrderAmount())
                    .storeId(order.getStoreId())
                    .tradeCode(order.getOrderCode())
                    .tradeTime(new Date())
                    .orderTime(order.getOrderTime())
                    .billType(billType)
                    .remark(remark)
                    .userId(order.getStoreId())
                    .build();
            storeRechargeService.storeDeduction(deductionBO);
        }
    }

    /**
     * 订单扣款 对账
     * @param order
     */
    @Transactional(rollbackFor = Exception.class)
    public void saveOrderDeductions(Order order,Long storeId){
        if(null != storeId){
            Example ex = new Example(StoreSettlement.class);
            ex.createCriteria().andEqualTo("storeId", storeId);

            List<StoreSettlement> ssList = this.storeSettlementMapper.selectByExample(ex);
            StoreSettlement ss = null;
            if(SpringUtil.isNotEmpty(ssList)){
                ss = ssList.get(0);
            }
            if(null != ss){
                if (ss.getCollectStatus()){
                    if(ss.getCollectPrice()!=null){
                        OrderBill ob = new OrderBill();
                        ob.setStoreId(order.getStoreId());
                        ob.setOrderId(order.getId());
                        ob.setArAmount(order.getOrderAmount());
                        ob.setPaAmount(BigDecimal.ZERO);
                        ob.setOrderTime(order.getOrderTime());
                        ob.setStoreBalance(BigDecimal.valueOf(ss.getCollectPrice()));
                        ob.setBillRemark("<--提货卡扣款:"+order.getOrderCode()+" -->");
                        ob.setCreateTime(new Date());
                        ob.setCrateId(storeId);
                        this.orderBillMapper.insert(ob);
                    }
                }
            }
        }
    }
}
