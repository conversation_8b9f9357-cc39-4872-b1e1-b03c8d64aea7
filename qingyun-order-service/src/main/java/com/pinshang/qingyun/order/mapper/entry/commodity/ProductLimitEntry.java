package com.pinshang.qingyun.order.mapper.entry.commodity;

import java.math.BigDecimal;

public class ProductLimitEntry {
	
	private String commodityId;
	private String commodityName;
	private BigDecimal limitNumber;
	
	public String getCommodityId() {
		return commodityId;
	}

	public void setCommodityId(String commodityId) {
		this.commodityId = commodityId;
	}

	public BigDecimal getLimitNumber() {
		return limitNumber;
	}

	public void setLimitNumber(BigDecimal limitNumber) {
		this.limitNumber = limitNumber;
	}

	public String getCommodityName() {
		return commodityName;
	}

	public void setCommodityName(String commodityName) {
		this.commodityName = commodityName;
	}
}
