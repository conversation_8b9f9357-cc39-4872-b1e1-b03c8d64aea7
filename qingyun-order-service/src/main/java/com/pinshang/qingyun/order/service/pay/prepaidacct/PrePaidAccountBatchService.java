package com.pinshang.qingyun.order.service.pay.prepaidacct;

import java.math.BigDecimal;
import java.util.List;
import java.util.concurrent.TimeUnit;

import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.pinshang.qingyun.order.config.LockConstants;
import com.pinshang.qingyun.order.enums.PrePaidAccountReqStatusEnum;
import com.pinshang.qingyun.order.enums.PrePaidAccountReqTypeEnum;
import com.pinshang.qingyun.order.mapper.PrePaidAccountReqMapper;
import com.pinshang.qingyun.order.mapper.StoreSettlementMapper;
import com.pinshang.qingyun.order.model.order.PrePaidAccountReq;
import com.pinshang.qingyun.order.model.order.StoreSettlement;
import com.pinshang.qingyun.order.vo.prepayacct.PrePaidAccountReqVo;

import lombok.extern.slf4j.Slf4j;
import tk.mybatis.mapper.entity.Example;

@Service
@Slf4j
public class PrePaidAccountBatchService {
	
    private RedissonClient redissonClient;

	private Long prePaidAccountRecordLockWaitTimeSec;

	private StoreSettlementMapper storeSettlementMapper;

	private PrePaidAccountReqMapper prePaidAccountReqMapper;

	private List<PrePaidAccountBatchAsyncRecordListener> postReqRecordedHandlers;

	@Autowired
	public void setRedissonClient(RedissonClient redissonClient) {
		this.redissonClient = redissonClient;
	}

	@Value("${prePaidAccountRecordLockWaitTimeSec:10}")
	public void setPrePaidAccountRecordLockWaitTimeSec(Long prePaidAccountRecordLockWaitTimeSec) {
		this.prePaidAccountRecordLockWaitTimeSec = prePaidAccountRecordLockWaitTimeSec;
	}

	@Autowired(required = false)
	public void setPostReqRecordedHandlers(List<PrePaidAccountBatchAsyncRecordListener> postReqRecordedHandlers) {
		this.postReqRecordedHandlers = postReqRecordedHandlers;
	}

	@Autowired
	public void setStoreSettlementMapper(StoreSettlementMapper storeSettlementMapper) {
		this.storeSettlementMapper = storeSettlementMapper;
	}

	@Autowired
	public void setPrePaidAccountReqMapper(PrePaidAccountReqMapper prePaidAccountReqMapper) {
		this.prePaidAccountReqMapper = prePaidAccountReqMapper;
	}

	/**
	 * 该方法需要事务，所以独立出一个Service以支持spring注解事务机制
	 * @param storeId 客户
	 * @param reqs 请求
	 * @throws InterruptedException 锁请求被中断
	 */
	@Transactional
	public void batchRecord(Long storeId, List<PrePaidAccountReqVo> reqs) throws InterruptedException {
		String lockKey = LockConstants.generatePrePaidAccountLock(storeId);
		RLock lock = redissonClient.getLock(lockKey);
		try {
			// 在操作账户前需要获取锁
			if (lock.tryLock(prePaidAccountRecordLockWaitTimeSec, TimeUnit.SECONDS)) {
				
				Example ex = new Example(StoreSettlement.class);
		        ex.createCriteria().andEqualTo("storeId", storeId);
		        List<StoreSettlement> ssList = this.storeSettlementMapper.selectByExample(ex);
		        StoreSettlement ss;
		        try {
			        if(ssList.isEmpty()){
			            throw new PrePaidAccountReqException(String.format("预付费账户扣款异常，未找到预付款账户[storeId=%s]", storeId));
			        }
			        
			        ss = ssList.get(0);
			        if (!ss.getCollectStatus()) { // 非预付费账户
			        	throw new PrePaidAccountReqException(String.format("预付费账户扣款异常，当前账户[storeId=%s]不是一个预付费账户", storeId));
			        }
		        } catch (PrePaidAccountReqException e) {
		        	// 这里账户异常了，所以把所有的请求更新为异常
					log.error(String.format("异步批量入账异常[storeId=%s]", storeId), e);
					PrePaidAccountReq reqDto;
			        for (PrePaidAccountReqVo req : reqs) {
			        	req.setStatus(PrePaidAccountReqStatusEnum.EXCEPTION);
			        	reqDto = new PrePaidAccountReq();
						reqDto.setId(req.getId());
						reqDto.setReqStatus(req.getStatus().getCode());
						reqDto.setErrMsg(e.getMessage());
						prePaidAccountReqMapper.updateByPrimaryKeySelective(reqDto);
			        }
			        onBatchUpdateFailed(storeId, reqs);
			        return; // 更新完后直接结束
				}
		        
		        if(ss.getCollectPrice() == null){
		            ss.setCollectPrice(BigDecimal.ZERO.doubleValue());
		        }
		        
		        BigDecimal balance = BigDecimal.valueOf(ss.getCollectPrice());
				
				for (PrePaidAccountReqVo req : reqs) {
					if (req.getReqType() == PrePaidAccountReqTypeEnum.PAY) {
			        	balance = balance.subtract(req.getAmount());
			        } else {
			        	balance = balance.add(req.getAmount());
			        }
					req.setBalance(balance); // 回写本次扣款后的账户余额
					req.setStatus(PrePaidAccountReqStatusEnum.RECORDED);
				}
				ss.setCollectPrice(balance.doubleValue());
		        
		        storeSettlementMapper.updateByPrimaryKey(ss);
				
		        // 更新请求状态
		        PrePaidAccountReq reqDto;
		        for (PrePaidAccountReqVo req : reqs) {
		        	reqDto = new PrePaidAccountReq();
					reqDto.setId(req.getId());
					reqDto.setReqStatus(req.getStatus().getCode());
					prePaidAccountReqMapper.updateByPrimaryKeySelective(reqDto);
		        }
		        
		        
		        onBatchUpdateSuccess(storeId, reqs);
			}
			
			// 对于锁获取失败的情况，不做任何操作
		} finally {
			if (lock.isHeldByCurrentThread()) {
				lock.unlock();
			}
		}
	}

	private void onBatchUpdateSuccess(Long storeId, List<PrePaidAccountReqVo> reqs) {
		if (postReqRecordedHandlers != null && !postReqRecordedHandlers.isEmpty()) {
			for (PrePaidAccountBatchAsyncRecordListener ls : postReqRecordedHandlers) {
				ls.onBatchRecordSuccess(storeId, reqs);
			}
		}
		
	}

	private void onBatchUpdateFailed(Long storeId, List<PrePaidAccountReqVo> reqs) {
		if (postReqRecordedHandlers != null && !postReqRecordedHandlers.isEmpty()) {
			for (PrePaidAccountBatchAsyncRecordListener ls : postReqRecordedHandlers) {
				ls.onBatchRecordFailed(storeId, reqs);
			}
		}
	}
}
