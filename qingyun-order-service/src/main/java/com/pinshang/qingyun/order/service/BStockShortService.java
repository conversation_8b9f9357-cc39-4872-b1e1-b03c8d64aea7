package com.pinshang.qingyun.order.service;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.pinshang.qingyun.base.configure.expand.QYAssert;
import com.pinshang.qingyun.box.utils.BeanCloneUtils;
import com.pinshang.qingyun.box.utils.DateUtil;
import com.pinshang.qingyun.box.utils.StringUtil;
import com.pinshang.qingyun.order.dto.BStockShortPageIDTO;
import com.pinshang.qingyun.order.dto.BStockShortPageODTO;
import com.pinshang.qingyun.order.dto.XDCommodityODTO;
import com.pinshang.qingyun.order.mapper.BStockShortMapper;
import com.pinshang.qingyun.order.mapper.CommodityMapper;
import com.pinshang.qingyun.order.mapper.StoreMapper;
import com.pinshang.qingyun.order.model.commodity.Commodity;
import com.pinshang.qingyun.order.model.stock.BStockShort;
import com.pinshang.qingyun.order.model.store.Store;
import com.pinshang.qingyun.order.vo.BStockLackVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.entity.Example;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2024/03/13
 * @Version 1.0
 */
@Slf4j
@Service
public class BStockShortService {

    @Autowired
    private BStockShortMapper bStockShortMapper;

    @Autowired
    private StoreMapper storeMapper;

    @Autowired
    private CommodityMapper commodityMapper;

    @Transactional(rollbackFor = Exception.class)
    public void stockShort(List<BStockLackVO> voList){
        List<BStockLackVO> orderTypeEmptyList = voList.stream().filter(p ->
                p.getOrderType() == null || p.getStockType() == null || null == p.getStockQuantity())
                .collect(Collectors.toList());
        if(CollectionUtils.isNotEmpty(orderTypeEmptyList)){
            log.error("保存库存不足日志，orderType 为空 {}", orderTypeEmptyList);
            return;
        }

        List<Long> storeIdList = voList.stream().map(BStockLackVO::getStoreId).distinct().collect(Collectors.toList());
        List<Long> commodityIdList = voList.stream().map(BStockLackVO::getCommodityId).distinct().collect(Collectors.toList());
        List<XDCommodityODTO> commodityList = commodityMapper.getXDCommodityODTOByIdList(commodityIdList);
        Map<Long, XDCommodityODTO> commodityMap = commodityList.stream().collect(Collectors.toMap(it -> Long.parseLong(it.getCommodityId()), Function.identity()));

        Example storeExample = new Example(Commodity.class);
        storeExample.createCriteria().andIn("id",storeIdList);
        List<Store> storeList = storeMapper.selectByExample(storeExample);
        Map<Long, Store> storeMap = storeList.stream().collect(Collectors.toMap(Store::getId, Function.identity()));

        Date now = new Date();
        List<BStockShort> insertList = voList.stream().map(it -> {
            BStockShort bStockShort = BeanCloneUtils.copyTo(it, BStockShort.class);
            Store storeItem = storeMap.get(it.getStoreId());
            XDCommodityODTO commodityItem = commodityMap.get(it.getCommodityId());
            bStockShort.setStoreCode(storeItem.getStoreCode());
            bStockShort.setStoreName(storeItem.getStoreName());
            bStockShort.setStoreTypeId(storeItem.getStoreTypeId());
            bStockShort.setCommodityCode(commodityItem.getCommodityCode());
            bStockShort.setCommodityName(commodityItem.getCommodityName());
            bStockShort.setCommoditySpec(commodityItem.getCommoditySpec());
            bStockShort.setCommodityUnitName(commodityItem.getCommodityUnit());
            bStockShort.setCommodityPackageSpec(commodityItem.getCommodityPackageSpec());
            bStockShort.setCreateId(it.getCreateId());
            bStockShort.setSalesBoxCapacity(commodityItem.getSalesBoxCapacity());
            bStockShort.setCreateTime(now);
            bStockShort.setNeedQuantity(it.getNeedQuantity());
            if (bStockShort.getNeedQuantity()==null){
                bStockShort.setNeedQuantity(BigDecimal.ZERO);
            }
            return bStockShort;
        }).collect(Collectors.toList());

        bStockShortMapper.insertList(insertList);
    }

    @Deprecated
    public PageInfo<BStockShortPageODTO> page(BStockShortPageIDTO idto){
        if(!StringUtil.isBlank(idto.getBeginTime()) && !StringUtil.isBlank(idto.getEndTime())){
            idto.setBeginTime(idto.getBeginTime()+ " 00:00:00");
            idto.setEndTime(idto.getEndTime()+ " 23:59:59");
            int diff = DateUtil.getDayDif(DateUtil.parseDate(idto.getEndTime(), DateUtil.DEFAULT_DATE_FORMAT), DateUtil.parseDate(idto.getBeginTime(), DateUtil.DEFAULT_DATE_FORMAT));
            QYAssert.isTrue(diff <= 7, "查询日期的跨度不能超过7天");
        }
        return PageHelper.startPage(idto.getPageNo(), idto.getPageSize()).doSelectPageInfo(() -> {
            bStockShortMapper.page(idto);
        });
    }
}
