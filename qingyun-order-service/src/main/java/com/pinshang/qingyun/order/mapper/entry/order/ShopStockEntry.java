package com.pinshang.qingyun.order.mapper.entry.order;

import java.math.BigDecimal;
import java.util.Date;

public class ShopStockEntry {

	private String shopName;
	private String commodityCode;
	private String commodityName;
    private String commoditySpec;
    private String commodityId;
    private String categoryName;
	private String commodityUnitName;
	private BigDecimal quantity;
	private String barCode;
	private BigDecimal totalSales;//总销售量
	private Date inStorageDate;//首次入库时间
	private BigDecimal averageSales;//日均销量
	private BigDecimal onlinequantity;//在途库存
	private String barCodes;	// 子码列表

	public String getShopName() {
		return shopName;
	}

	public void setShopName(String shopName) {
		this.shopName = shopName;
	}

	public String getCommodityCode() {
		return commodityCode;
	}

	public void setCommodityCode(String commodityCode) {
		this.commodityCode = commodityCode;
	}

	public String getCommodityName() {
		return commodityName;
	}

	public void setCommodityName(String commodityName) {
		this.commodityName = commodityName;
	}

	public String getCommoditySpec() {
		return commoditySpec;
	}

	public void setCommoditySpec(String commoditySpec) {
		this.commoditySpec = commoditySpec;
	}

	public String getCommodityId() {
		return commodityId;
	}

	public void setCommodityId(String commodityId) {
		this.commodityId = commodityId;
	}

	public String getCategoryName() {
		return categoryName;
	}

	public void setCategoryName(String categoryName) {
		this.categoryName = categoryName;
	}

	public String getCommodityUnitName() {
		return commodityUnitName;
	}

	public void setCommodityUnitName(String commodityUnitName) {
		this.commodityUnitName = commodityUnitName;
	}

	public BigDecimal getQuantity() {
		return quantity;
	}

	public void setQuantity(BigDecimal quantity) {
		this.quantity = quantity;
	}

	public String getBarCode() {
		return barCode;
	}

	public void setBarCode(String barCode) {
		this.barCode = barCode;
	}

	public BigDecimal getTotalSales() {
		return totalSales;
	}

	public void setTotalSales(BigDecimal totalSales) {
		this.totalSales = totalSales;
	}

	public Date getInStorageDate() {
		return inStorageDate;
	}

	public void setInStorageDate(Date inStorageDate) {
		this.inStorageDate = inStorageDate;
	}

	public BigDecimal getAverageSales() {
		return averageSales;
	}

	public void setAverageSales(BigDecimal averageSales) {
		this.averageSales = averageSales;
	}

	public BigDecimal getOnlinequantity() {
		return onlinequantity;
	}

	public void setOnlinequantity(BigDecimal onlinequantity) {
		this.onlinequantity = onlinequantity;
	}

	public String getBarCodes() {
		return barCodes;
	}

	public void setBarCodes(String barCodes) {
		this.barCodes = barCodes;
	}
}
