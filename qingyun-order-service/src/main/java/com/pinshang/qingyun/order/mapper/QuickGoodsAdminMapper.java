package com.pinshang.qingyun.order.mapper;

import com.pinshang.qingyun.base.mybatis.MyMapper;
import com.pinshang.qingyun.order.model.order.QuickGoodsAdmin;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Mapper
@Repository
public interface QuickGoodsAdminMapper extends MyMapper <QuickGoodsAdmin>{

    List<QuickGoodsAdmin> listGroup(@Param("pass") Integer pass, @Param("userId") Long userId, @Param("bigShop") Boolean bigShop);
}
