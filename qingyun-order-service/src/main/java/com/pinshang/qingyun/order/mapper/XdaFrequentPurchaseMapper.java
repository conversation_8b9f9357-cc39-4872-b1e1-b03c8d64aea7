package com.pinshang.qingyun.order.mapper;

import com.pinshang.qingyun.base.mybatis.MyMapper;
import com.pinshang.qingyun.order.model.order.XdaFrequentPurchase;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Set;


@Repository
public interface XdaFrequentPurchaseMapper extends MyMapper<XdaFrequentPurchase>{

   List<XdaFrequentPurchase> queryFrequentPurchase(@Param("storeId") Long storeId ,@Param("idList") Set<Long> idList);

   int batchUpdate(@Param("purchaseList") List<XdaFrequentPurchase> frequentPurchaseList);
}