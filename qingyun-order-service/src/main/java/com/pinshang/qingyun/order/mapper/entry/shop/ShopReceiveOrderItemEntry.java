package com.pinshang.qingyun.order.mapper.entry.shop;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2017/6/8.
 */
@Data
public class ShopReceiveOrderItemEntry {

    @ApiModelProperty("明细id")
    private String itemId;

    private String commodityId;

    @ApiModelProperty("商品编码")
    private String commodityCode;

    @ApiModelProperty("商品名称")
    private String commodityName;

    @ApiModelProperty("规格")
    private String commoditySpec;

    @ApiModelProperty("单位")
    private String unit;

    @ApiModelProperty("价格")
    private BigDecimal price;

    private Integer status;

    private BigDecimal recommandPrice;

    @ApiModelProperty("前置仓订货份数")
    private BigDecimal quantity;

    @ApiModelProperty("前置仓发货份数")
    private BigDecimal realDeliveryQuantity;

    private BigDecimal realReceiveQuantity;

    @ApiModelProperty("实收份数")
    private Integer realReceiveNumber;

    private BigDecimal totalPrice;

    private String rejectReason;

    private String remark;

    @ApiModelProperty("主条形码")
    private String barCode;

    private String barCodes;

    @ApiModelProperty("条形码list")
    private List<String> barCodeList;

    @ApiModelProperty("货位")
    private String shelfNo;

    @ApiModelProperty("包装规格")
    private BigDecimal commodityPackageSpec;


    @ApiModelProperty("实收正常品份数")
    private BigDecimal normalShares;

    @ApiModelProperty("实收正常品数量")
    private BigDecimal normalQuantity;

    @ApiModelProperty("01 散装  02 整包")
    private String commodityPackageKind;

    @ApiModelProperty("是否称重0-不称量,1-称重")
    private Integer isWeight;

    private Long commodityUnitId; // 单位id
}
