package com.pinshang.qingyun.order.service.order;/**
 * @Author: sk
 * @Date: 2025/7/9
 */

import com.pinshang.qingyun.base.configure.expand.QYAssert;
import com.pinshang.qingyun.base.enums.YesOrNoEnums;
import com.pinshang.qingyun.base.enums.order.OrderTypeEnum;
import com.pinshang.qingyun.base.enums.storage.StockTypeEnum;
import com.pinshang.qingyun.base.enums.storage.ToBTypeEnums;
import com.pinshang.qingyun.box.utils.DateTimeUtil;
import com.pinshang.qingyun.box.utils.DateUtil;
import com.pinshang.qingyun.box.utils.SpringUtil;
import com.pinshang.qingyun.order.dto.order.XiaoeTongPushOrderIDTO;
import com.pinshang.qingyun.order.enums.DeliveryBatchTypeEnum;
import com.pinshang.qingyun.order.mapper.entry.MdShopOrderSettingEntry;
import com.pinshang.qingyun.order.model.order.Order;
import com.pinshang.qingyun.order.model.shop.Shop;
import com.pinshang.qingyun.order.service.*;
import com.pinshang.qingyun.order.util.ThreadLocalUtils;
import com.pinshang.qingyun.order.vo.order.OrderDto;
import com.pinshang.qingyun.storage.dto.tob.CommodityInventoryODTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025年07月09日 上午9:11
 */
@Slf4j
@Service
public class XiaoetongOrderService {

    @Autowired
    private BStockService bStockService;
    @Autowired
    private ShopService shopService;
    @Autowired
    private OrderSplitService orderSplitService;
    @Autowired
    private MdShopOrderSettingService mdShopOrderSettingService;
    @Autowired
    private GrouponOrderSaveService grouponOrderSaveService;

    /**
     * 小鹅通订单推送参数校验
     */
    private void paramCheck(List<XiaoeTongPushOrderIDTO> xiaoeTongPushOrderList) {
        QYAssert.isTrue(xiaoeTongPushOrderList.size() > 0, "参数异常!");
        xiaoeTongPushOrderList.stream().forEach(dto ->{
            QYAssert.notNull(dto.getShopId(), "门店id不能为空!");
            QYAssert.notNull(dto.getOrderId(), "订单id不能为空!");
            QYAssert.notNull(dto.getOrderTime(), "订单时间不能为空!");
            QYAssert.notNull(dto.getCommodityId(), "商品id不能为空!");
            QYAssert.notNull(dto.getOrderQuantity(), "下单数量不能为空!");
            QYAssert.notNull(dto.getPrice(), "价格不能为空!");
            QYAssert.notNull(dto.getOrderCreateTime(), "订单下单时间不能为空!");
        });
    }

    /**
     * 小鹅通推送订单保存
     * @param xiaoeTongPushOrderList
     */
    @Transactional(rollbackFor = Exception.class)
    public List<XiaoeTongPushOrderIDTO> createXiaoeTongOrder(List<XiaoeTongPushOrderIDTO> xiaoeTongPushOrderList){
        // 参数校验
        paramCheck(xiaoeTongPushOrderList);

        String orderTime = xiaoeTongPushOrderList.get(0).getOrderTime();
        Long commodityId = xiaoeTongPushOrderList.get(0).getCommodityId();
        Set<Long> shopIdList = xiaoeTongPushOrderList.stream().map(XiaoeTongPushOrderIDTO::getShopId).collect(Collectors.toSet());

        // 商品合计总数量
        BigDecimal totalQuantity = xiaoeTongPushOrderList.stream().collect(Collectors.reducing(BigDecimal.ZERO, XiaoeTongPushOrderIDTO::getOrderQuantity, BigDecimal::add));
        List<Shop> shopList = shopService.getShopByIdList(new ArrayList<>(shopIdList));
        Map<Long,  Shop> shopMap = shopList.stream().collect(Collectors.toMap(Shop::getId, v -> v));

        Map<Long, BigDecimal> orderQuantityMap = new HashMap<>();
        orderQuantityMap.put(commodityId, totalQuantity);
        Date orderDate = DateUtil.parseDate(orderTime, "yyyy-MM-dd");

        // 查询商品库存和库存依据
        Map<Long, CommodityInventoryODTO> toBStockMap = bStockService.getbStockMap(orderDate, orderQuantityMap);
        QYAssert.isTrue(toBStockMap.size() > 0, "商品无库存依据!");

        CommodityInventoryODTO commodityInventoryODTO = toBStockMap.get(commodityId);

        Boolean hasStock = commodityInventoryODTO.getInventoryQuantity().compareTo(BigDecimal.ZERO) > 0;
        QYAssert.isTrue(hasStock, "商品库存不足!");


        // 按小鹅通订单下单时间从早到晚顺序计算最优可满足订单数x和可冻结商品数量
        xiaoeTongPushOrderList.stream().sorted(Comparator.comparing(XiaoeTongPushOrderIDTO::getOrderCreateTime)).collect(Collectors.toList());

        // 设置下单数量，根据实际库存判断
        setOrderQuantity(xiaoeTongPushOrderList, shopMap, commodityInventoryODTO.getInventoryQuantity());

        // 过滤出可下单数量大于0的
        xiaoeTongPushOrderList = xiaoeTongPushOrderList.stream().filter(p -> p.getQuantity().compareTo(BigDecimal.ZERO) > 0).collect(Collectors.toList());
        QYAssert.isTrue(xiaoeTongPushOrderList.size() > 0, "商品可用库存不足!");

        // 设置转换比例
        setTargetRatio(xiaoeTongPushOrderList, commodityInventoryODTO);

        // 物流模式、供应商、仓库、送货日期
        setGroupOrderSetting(xiaoeTongPushOrderList, orderTime);

        // 转成OrderDto对象
        List<OrderDto> orderDtoList = orderSplitService.convertToOrderDto(xiaoeTongPushOrderList, "小鹅通自动下单" , true);

        // 不可变价
        ThreadLocalUtils.setGroupOrder(true);
        ThreadLocalUtils.setOrderType(OrderTypeEnum.XIAO_E_TONG_AUTO_ORDER.getCode());
        try {
            List<Long> orderIdList = new ArrayList<>();
            // 循环创建订单
            for(OrderDto orderDto : orderDtoList){
                try {
                    // 保存订单
                    Order order =  grouponOrderSaveService.saveXiaoetongOrder(orderDto, null);
                    orderIdList.add(order.getId());
                }catch (Exception e){

                    // 如果其中一个订单创建失败，则全部回滚。之前成功冻结库存的再解冻
                    batchUnFreezeStock(orderIdList);
                    throw e;
                }
            }
        }finally {
            ThreadLocalUtils.remove();
        }

        return xiaoeTongPushOrderList;
    }

    /**
     * 批量解冻库存
     */
    private void batchUnFreezeStock(List<Long> orderIdList) {
        if (orderIdList.size() > 0) {
            orderIdList.forEach(orderId -> bStockService.warehouseUnfreezeInventory(orderId, ToBTypeEnums.SALE.getCode()));
        }
    }


    /**
     * 设置转换比例
     */
    private void setTargetRatio(List<XiaoeTongPushOrderIDTO> xiaoeTongPushOrderList, CommodityInventoryODTO commodityInventoryODTO) {
        xiaoeTongPushOrderList.forEach(dto ->{
            dto.setPresaleStatus(YesOrNoEnums.NO.getCode());
            dto.setStockType(commodityInventoryODTO != null ? commodityInventoryODTO.getStockType() : StockTypeEnum.UN_LIMIT.getCode());
            if (Objects.nonNull(commodityInventoryODTO)) {
                Integer sourceRatio = commodityInventoryODTO.getSourceRatio();
                dto.setSourceRatio(sourceRatio);
                Integer targetRatio = commodityInventoryODTO.getTargetRatio();
                dto.setTargetRatio(targetRatio);
                Long targetCommodityId = commodityInventoryODTO.getTargetCommodityId();
                dto.setTargetCommodityId(targetCommodityId);
                Integer convertStatus = commodityInventoryODTO.getCanConvert() ? 1 : 0;
                dto.setConvertStatus(convertStatus);
                BigDecimal quantity = dto.getQuantity();
                // 组合商品目标数量 : 向上取整 ( quantity / source_ratio * target_ratio)
                if (commodityInventoryODTO.getCanConvert()) {
                    BigDecimal targetQuantity = quantity.divide(new BigDecimal(sourceRatio), 0, RoundingMode.UP).multiply(new BigDecimal(targetRatio));
                    dto.setTargetQuantity(targetQuantity);
                }
            }
        });
    }

    /**
     * 设置下单数量，根据实际库存判断
     * 例如：第一个5、第二个8、第三个5
     * 库存只剩10，则只提交第一个5
     */
    private void setOrderQuantity(List<XiaoeTongPushOrderIDTO> xiaoeTongPushOrderList, Map<Long, Shop> shopMap, BigDecimal inventoryQuantity) {
        BigDecimal remainQuantity = inventoryQuantity;
        for(XiaoeTongPushOrderIDTO item : xiaoeTongPushOrderList) {
            if(shopMap.containsKey(item.getShopId())) {
                Shop shop = shopMap.get(item.getShopId());
                item.setStoreId(shop.getStoreId());
            }else {
                QYAssert.isFalse("无此门店：" + item.getShopId());
            }

            if(remainQuantity.compareTo(item.getOrderQuantity()) >= 0) {
                item.setQuantity(item.getOrderQuantity());
                // 下单数量=推送数量，标记为推送成功
                item.setOrderStauts(YesOrNoEnums.YES.getCode());
            }else{
                break;
            }
            remainQuantity = remainQuantity.subtract(item.getQuantity());
        }
    }

    /**
     * 物流模式、供应商、仓库、送货日期
     * @param xiaoeTongPushOrderList
     */
    public void setGroupOrderSetting(List<XiaoeTongPushOrderIDTO> xiaoeTongPushOrderList, String orderTime){
        for(XiaoeTongPushOrderIDTO odto : xiaoeTongPushOrderList){
            List<String> commodityIdList = new ArrayList<>();
            commodityIdList.add(odto.getCommodityId() + "");
            List<MdShopOrderSettingEntry> settingList = mdShopOrderSettingService.queryMdShopOrderSettingListByIds(odto.getStoreId(), commodityIdList);
            QYAssert.isTrue(SpringUtil.isNotEmpty(settingList), "门店订货配置信息有误");
            MdShopOrderSettingEntry settingEntry = settingList.get(0);

            odto.setLogisticsModel(settingEntry.getLogisticsModel().intValue());
            odto.setSupplierId(Long.valueOf(settingEntry.getSupplierId()));
            odto.setWarehouseId(Long.valueOf(settingEntry.getWarehouseId()));
            odto.setDeleveryTimeRange(settingEntry.getDeleveryTimeRange());


            odto.setEnterpriseId(78L);
            odto.setUserId(-1L);
            odto.setCreateName("系统");
            odto.setDeliveryBatch(DeliveryBatchTypeEnum.ONE_BATCH.getCode().toString());
            // 送货日期默认 T+1
            odto.setOrderTime(orderTime);
        }
    }
}
