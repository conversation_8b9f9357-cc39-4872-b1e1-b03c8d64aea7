package com.pinshang.qingyun.order.controller.client;

import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.pinshang.qingyun.order.service.fresh.AppOrderService;

/**
 * 清美生鲜订单
 */
@Slf4j
@RestController
@RequestMapping("/app/order/client")
public class FreshAppOrderClientController {

	@Autowired
	public AppOrderService appOrderService;

	/**
	 * 发送生鲜订单创建消息				—— 供【II期-清美生鲜App，tramy-api-starter.QingyunOrderServiceClient】调用
	 */
	@PostMapping("/sendAppOrderSaveMsg")
	public void sendAppOrderSaveMsg(@RequestParam(value = "orderId", required = false) Long orderId) {
		log.info("\n发送生鲜订单创建消息.订单信息为! orderId={}", orderId);
		appOrderService.sendAppOrderSaveMsg(orderId);
	}

	/**
	 * 发送生鲜订单变更消息				—— 供【II期-清美生鲜App，tramy-api-starter.QingyunOrderServiceClient】调用
	 */
	@PostMapping("/sendAppOrderUpdateMsg")
	public void sendAppOrderUpdateMsg(@RequestParam(value = "orderId", required = false) Long orderId) {
		log.info("\n发送生鲜订单变更消息.订单信息为! orderId={}", orderId);
		appOrderService.sendAppOrderUpdateMsg(orderId);
	}

	/**
	 * 补偿生鲜订单消息Job
	 */
	@PostMapping("/compensateAppOrderMsgJob")
	public String compensateAppOrderMsgJob() {
		int quantity = appOrderService.compensateAppOrderMsgJob();
		log.info("\n补偿生鲜订单消息Job.quantity={}", quantity);
		return "本次执行" + quantity + "条!";
	}

}
