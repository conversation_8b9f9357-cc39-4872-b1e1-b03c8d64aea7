package com.pinshang.qingyun.order.service;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.pinshang.qingyun.base.configure.expand.QYAssert;
import com.pinshang.qingyun.box.utils.SpringUtil;
import com.pinshang.qingyun.order.mapper.SettleStoreMapper;
import com.pinshang.qingyun.order.mapper.entry.SettlementStoreEntry;
import com.pinshang.qingyun.order.mapper.entry.StoreEntry;
import com.pinshang.qingyun.order.vo.SettlementStoreVo;
import com.pinshang.qingyun.order.vo.StoreVo;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

@Service
public class SettleStoreService {

    @Autowired
    private SettleStoreMapper settleStoreMapper;


    /**
     * 条件查询客户信息
     * @param vo
     * @return
     */
    public PageInfo<StoreEntry> selectStore(StoreVo vo){
        PageInfo<StoreEntry> pageDate = PageHelper.startPage(vo.getPageNo(), vo.getPageSize()).doSelectPageInfo(() -> {
            settleStoreMapper.selectStore(vo);
        });
        return pageDate;
    }

    /**
     * 条件查询结账客户
     * @param vo
     * @return
     */
    public PageInfo<SettlementStoreEntry> selectSettleStore(SettlementStoreVo vo){
        PageInfo<SettlementStoreEntry> pageDate = PageHelper.startPage(vo.getPageNo(), vo.getPageSize()).doSelectPageInfo(() -> {
            settleStoreMapper.selectSettleStore(vo);
        });
        return pageDate;
    }

    /**
     * 批量添加客户
     * @param storeCodes
     * @return
     */
    public List<StoreEntry> selectStoreByStores(String storeCodes){
        QYAssert.isTrue(StringUtils.isNotBlank(storeCodes), "客户编码不能为空!");
        List<String> codesList =  Arrays.asList(storeCodes.split("\n"));
        QYAssert.isTrue(SpringUtil.isNotEmpty(codesList), "请以回车分割商品编码!");
        List<StoreEntry> storeEntryList = new ArrayList<>();
        codesList.forEach(p->{
            StoreEntry storeEntry = settleStoreMapper.selectStoreByStores(p);
            QYAssert.isTrue(null != storeEntry, p+"客户编码不存在!");
            storeEntryList.add(storeEntry);
        });
        return  storeEntryList;
    }
}
