package com.pinshang.qingyun.order.mapper.entry.shop;

import lombok.Data;

import java.math.BigDecimal;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2017/6/15.
 */
@Data
public class ReceiveAuditBodyEntry {

    private String itemId;

    private String commodityId;

    private String commodityCode;

    private String commodityName;

    private String commoditySpec;

    private String unit;

    private BigDecimal price;

    private Integer status;

    private BigDecimal recommandPrice;

    private BigDecimal quantity;

    private BigDecimal realReceiveQuantity;

    private BigDecimal realDeliveryQuantity;

    private BigDecimal totalPrice;
}
