package com.pinshang.qingyun.order.mapper.entry.order;

import org.apache.commons.lang3.StringUtils;

public class ProductPackedTypeEntry {
	
	private String productId;
	private String packedType;// 包类型(01散装,02正包)
	private String productName;
	
	public String getProductId() {
		return productId;
	}
	public void setProductId(String productId) {
		this.productId = productId;
	}
	
	public String getPackedType() {
		if(StringUtils.isBlank(this.packedType)){
			return "01";
		}
		return packedType;
	}
	public void setPackedType(String packedType) {
		this.packedType = packedType;
	}
	public String getProductName() {
		return productName;
	}
	public void setProductName(String productName) {
		this.productName = productName;
	}
}
