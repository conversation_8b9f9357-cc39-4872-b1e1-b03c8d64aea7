package com.pinshang.qingyun.order.service.orderSync;

import com.github.pagehelper.PageInfo;
import com.pinshang.qingyun.order.vo.order.OrderClientVO;
import com.pinshang.qingyun.order.vo.order.OrderSyncToDcReqVo;
import com.pinshang.qingyun.order.vo.order.OrderSyncToDcRespVo;
import java.util.List;

public interface IOrderSyncToDcService {
    List<OrderSyncToDcRespVo> queryOrderSyncToDcList(OrderSyncToDcReqVo vo);

    List<OrderClientVO> getOrderListByIds(List<Long> orderIds);

    List<OrderClientVO> queryOrderListBySubOrderIds(List<Long> subOrderIds);

    PageInfo<OrderSyncToDcRespVo> queryOrderPage(OrderSyncToDcReqVo vo);
}
