package com.pinshang.qingyun.order.service.tob;

import com.github.pagehelper.PageInfo;
import com.pinshang.qingyun.order.dto.tob.ToBStockRepairIDTO;
import com.pinshang.qingyun.order.dto.tob.ToBStockRepairODTO;

import java.util.List;

/**
 * @Description：
 * @Author：<PERSON><PERSON>ui
 * @Package：com.pinshang.qingyun.order.service.tob
 * @Date: 2024/4/10
 */
public interface ToBCommodityStockService {

    /**
     * 修复数据
     * @param repairIDTOList
     * @return
     */
    void stockRepair(List<ToBStockRepairIDTO> repairIDTOList);

    /**
     * 查询库存数量
     * @return
     */
    Integer queryStockCount();

    /**
     * 查询数据
     * @param pageNo
     * @param pageSize
     * @return
     */
    PageInfo<ToBStockRepairODTO> queryStockList(Integer pageNo, Integer pageSize);
}
