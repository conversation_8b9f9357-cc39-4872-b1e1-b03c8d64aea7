package com.pinshang.qingyun.order.mapper.entry.order;

import com.pinshang.qingyun.box.utils.DateUtil;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.util.Date;

public class ShoppingCartItemEntry {
	//shoppingCartItemId
	private Long shoppingCartItemId;
	//商品id
	private Long commodityId;
	//商品编码
	private String commodityCode;
	//商品名称
	private String commodityName;
	//规格型号
	private String commoditySpec;

	// 条形码
	private String barCode;

	//单价
	private BigDecimal price;
	//数量
	private BigDecimal quantity;
	//商品限量
	private BigDecimal commodityNumberLimit;
    //速冻
    private Integer frozen;//速冻
    //包装类型
    private String  packedType;//包类型
	//箱规
	private BigDecimal salesBoxCapacity;
	//单位
	private String commodityUnit;
	private String newProductFlag;//是否新品标识

	// 包装规格
	private BigDecimal commodityPackageSpec;

	//("01 散装  02 整包")
	private String commodityPackageKind;

	//("是否称重0-不称量,1-称重")
	private Integer isWeight;
	private Long commodityUnitId; // 单位id
	private Long commodityCycleId; //新品标识 id

	private Long createId;
	private Date createTime;
	private BigDecimal boxCapacity;

	private Boolean shelvesRecommend; //  是否系统推荐商品
	private BigDecimal shelvesRecommendQuantity; // 系统推荐订货数
	private String shelvesRecommendTime; // 系统推荐时间


	/**
	 * 近15天日均销量
	 */
	private BigDecimal avgDailySales15Days;
	/**
	 * 当日销售数量
	 */
	private BigDecimal dailySales;
	/**
	 * 近15天毛利率
	 */
	private BigDecimal grossProfitMargin15Days;

	public BigDecimal getAvgDailySales15Days() {
		return avgDailySales15Days;
	}

	public void setAvgDailySales15Days(BigDecimal avgDailySales15Days) {
		this.avgDailySales15Days = avgDailySales15Days;
	}

	public BigDecimal getDailySales() {
		return dailySales;
	}

	public void setDailySales(BigDecimal dailySales) {
		this.dailySales = dailySales;
	}

	public BigDecimal getGrossProfitMargin15Days() {
		return grossProfitMargin15Days;
	}

	public void setGrossProfitMargin15Days(BigDecimal grossProfitMargin15Days) {
		this.grossProfitMargin15Days = grossProfitMargin15Days;
	}

	public BigDecimal getBoxCapacity() {
		return boxCapacity;
	}

	public void setBoxCapacity(BigDecimal boxCapacity) {
		this.boxCapacity = boxCapacity;
	}

	public Boolean getShelvesRecommend() {
		return createId != null && createId.equals(-1L);
	}

	public BigDecimal getShelvesRecommendQuantity() {
		return quantity;
	}

	public String getShelvesRecommendTime() {
		return DateUtil.getDateFormate(createTime,"yyyy-MM-dd HH:mm:ss");
	}

	public Long getCreateId() {
		return createId;
	}
	public void setCreateId(Long createId) {
		this.createId = createId;
	}
	public Date getCreateTime() {
		return createTime;
	}
	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}
	public Long getCommodityUnitId() {
		return commodityUnitId;
	}

	public void setCommodityUnitId(Long commodityUnitId) {
		this.commodityUnitId = commodityUnitId;
	}

	public Long getCommodityCycleId() {
		return commodityCycleId;
	}

	public void setCommodityCycleId(Long commodityCycleId) {
		this.commodityCycleId = commodityCycleId;
	}
	public String getBarCode() {
		return barCode;
	}

	public void setBarCode(String barCode) {
		this.barCode = barCode;
	}

	public String getCommodityPackageKind() {
		return commodityPackageKind;
	}

	public void setCommodityPackageKind(String commodityPackageKind) {
		this.commodityPackageKind = commodityPackageKind;
	}

	public Integer getIsWeight() {
		return isWeight;
	}

	public void setIsWeight(Integer isWeight) {
		this.isWeight = isWeight;
	}

	public BigDecimal getCommodityPackageSpec() {
		return commodityPackageSpec;
	}

	public void setCommodityPackageSpec(BigDecimal commodityPackageSpec) {
		this.commodityPackageSpec = commodityPackageSpec;
	}

	public String getCommodityCode() {
		return commodityCode;
	}

	public void setCommodityCode(String commodityCode) {
		this.commodityCode = commodityCode;
	}

	public String getNewProductFlag() {
		return newProductFlag;
	}

	public void setNewProductFlag(String newProductFlag) {
		this.newProductFlag = newProductFlag;
	}

	public String getCommodityUnit() {
		return commodityUnit;
	}
	public void setCommodityUnit(String commodityUnit) {
		this.commodityUnit = commodityUnit;
	}
	public Long getCommodityId() {
		return commodityId;
	}
	public void setCommodityId(Long commodityId) {
		this.commodityId = commodityId;
	}
	public String getCommodityName() {
		return commodityName;
	}
	public void setCommodityName(String commodityName) {
		this.commodityName = commodityName;
	}
	public String getCommoditySpec() {
		return commoditySpec;
	}
	public void setCommoditySpec(String commoditySpec) {
		this.commoditySpec = commoditySpec;
	}
	public BigDecimal getPrice() {
		return price;
	}
	public void setPrice(BigDecimal price) {
		this.price = price;
	}
	public BigDecimal getQuantity() {
		return quantity;
	}
	public void setQuantity(BigDecimal quantity) {
		this.quantity = quantity;
	}
	public BigDecimal getCommodityNumberLimit() {
		return commodityNumberLimit;
	}
	public void setCommodityNumberLimit(BigDecimal commodityNumberLimit) {
		this.commodityNumberLimit = commodityNumberLimit;
	}
	public Integer getFrozen() {
		return frozen;
	}
	public void setFrozen(Integer frozen) {
		this.frozen = frozen;
	}
	public String getPackedType() {
		if(StringUtils.isEmpty(this.packedType)){
			return "01";
		}
		return packedType;
	}
	public void setPackedType(String packedType) {
		this.packedType = packedType;
	}
	public Long getShoppingCartItemId() {
		return shoppingCartItemId;
	}
	public void setShoppingCartItemId(Long shoppingCartItemId) {
		this.shoppingCartItemId = shoppingCartItemId;
	}
	public BigDecimal getSalesBoxCapacity() {
		return salesBoxCapacity;
	}
	public void setSalesBoxCapacity(BigDecimal salesBoxCapacity) {
		this.salesBoxCapacity = salesBoxCapacity;
	}
}
