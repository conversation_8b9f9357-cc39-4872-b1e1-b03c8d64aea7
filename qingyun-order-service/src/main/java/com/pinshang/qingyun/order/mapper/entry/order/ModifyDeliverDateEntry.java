package com.pinshang.qingyun.order.mapper.entry.order;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * summary.
 * <p>
 * detailed description
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2018/7/25
 */
@Data
public class ModifyDeliverDateEntry {

    /** 送货日期 */
    private String orderTime;

     /** 门店编码 */
    private String shopCode;

     /** 门店名称 */
    private String shopName;

     /** 订单金额 */
    private BigDecimal orderAmount;

     /** 配送批次 */
    private String deliveryBatch;

     /** 创建人 */
    private String realName;

     /** 发货状态 */
    private String deliveryStatus;

     /** 订单号 */
    private String orderCode;

    private Long referOrderId;

    /** 订单号 */
    private Long orderId;
}
