package com.pinshang.qingyun.order.mapper;

import com.pinshang.qingyun.base.mybatis.MyMapper;
import com.pinshang.qingyun.order.mapper.entry.store.StoreAccountEntry;
import com.pinshang.qingyun.order.mapper.entry.store.StoreEntry;
import com.pinshang.qingyun.order.model.distribution.DistributionLine;
import com.pinshang.qingyun.order.model.store.Store;
import com.pinshang.qingyun.order.vo.shop.DistributionStoreAccountVo;
import com.pinshang.qingyun.order.vo.StoreCompanyRespVo;
import com.pinshang.qingyun.order.vo.store.StoreCompanyVo;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface StoreMapper extends MyMapper<Store>{

    /**
     * 查询客户账号管理列表
     * @param employeeId 查询者权限
     * @param storeCode 可空
     * @return
     */
    List<StoreAccountEntry> getStoreAccountList(@Param("employeeId") Long employeeId,
                                                @Param("storeCode") String storeCode);

    /**
     * 根据storeCode查询
     * @param storeCode
     * @return
     */
    Store  getByStoreCode(@Param("storeCode")String storeCode);

    Store  getShopByStoreCode(@Param("storeCode")String storeCode);

    List<Store> getStoreByCodeList(@Param("storeCodeList") List<String> storeCodeList);
    /**
     * 新增配送排序客户列表
     * @param storeTypeId
     * @param vagueStr
     * @return
     */
    List<DistributionStoreAccountVo> getDistributionStoreAccountList(@Param("storeTypeId") Long storeTypeId
            ,@Param("vagueStr") String vagueStr, @Param("excludeIds") List<Long> storeIds);

    /**
     * 根据codes集合查询
     * @param codes
     * @return 仅包含field: id, code, store_name
     */
    List<Store> findInStoreCodes(@Param("codes") List<String> codes);

    List<DistributionLine> queryStoreLineByStoreIds(@Param("ids") List<Long> storeIds);

    /**
     * 条件查询客户编码
     * @param storeName
     * @return
     */
    List<StoreEntry> findStoreListByStoreName(@Param("storeName") String storeName);

    /**
     * 查询所有绑定门店的客户
     * @return
     */
    List<StoreEntry> findAllShopStoreList();

    /**
     * 鲜达查询客户限单数
     * @param storeId
     * @return
     */
    Integer findOrderNumberLimit(@Param("storeId") Long storeId);

    /**
     * 根据storeId 获取 companyId
     * @param storeId
     * @return
     */
    StoreCompanyVo getStoreCompanyByStoreId(@Param("storeId") Long storeId);
    
    /**
     * 查询  客户线路组名称
     * 
     * @param storeId
     * @return
     */
    public String selectStoreLineGroupName(@Param("storeId")Long storeId);

    List<StoreCompanyRespVo> getCompanyIdByShopIdList(@Param("shopIdList") List<Long> shopIdList);

    /**
     * 结算类型：
     * 0：不结算，
     * 1：鲜达模式=取最小（数量：实发小取实发，下单小去下单，日期：实发晚取实发日期，送货日期晚取送货日期），
     * 2：批发模式，
     * 3：门店结算（取实发），
     * 4：取下单
     * @param id
     * @return
     */
    Integer findSettleBillCalcTypeById(@Param("id") Long id);

    List<StoreEntry> queryStoreByStoreCodeList(@Param("storeCodeList") List<String> storeCodeList);

    List<Long> findStoreCompanyIdByStoreId(@Param("storeId") Long storeId);

    List<Long> queryStoreIdsByStoreNameOrCode(@Param("storeCode") String storeCode, @Param("storeName") String storeName);
}
