package com.pinshang.qingyun.order.mapper;

import com.pinshang.qingyun.base.mybatis.MyMapper;
import com.pinshang.qingyun.order.model.store.StoreDuration;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

/**
 * @description:
 * @author: hhf
 * @time: 2020/5/28 16:32
 */
@Repository
public interface StoreDurationMapper extends MyMapper<StoreDuration>{
   /**
        * 根据客户编码查询客户下单截止时间，开通账号时使用
     * @param storeCode
     * @return
             */
   StoreDuration findLineDurationByStoreCode(@Param("storeCode")String storeCode);

}
