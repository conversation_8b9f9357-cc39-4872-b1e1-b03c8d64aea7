package com.pinshang.qingyun.order.service.xda;

import com.pinshang.qingyun.base.enums.StoreBillTypeEnums;
import com.pinshang.qingyun.base.enums.order.OrderTypeEnum;
import com.pinshang.qingyun.base.enums.pay.AppCodeEnum;
import com.pinshang.qingyun.box.utils.JsonUtil;
import com.pinshang.qingyun.order.mapper.*;
import com.pinshang.qingyun.order.mapper.entry.recharge.XdaPayBillJobEntry;
import com.pinshang.qingyun.order.model.order.Order;
import com.pinshang.qingyun.order.model.order.OrderBill;
import com.pinshang.qingyun.order.model.order.XdaPreOrder;
import com.pinshang.qingyun.order.model.order.XfOrder;
import com.pinshang.qingyun.order.model.recharge.XDAPayBill;
import com.pinshang.qingyun.order.service.WeChatSendMessageService;
import com.pinshang.qingyun.order.service.pay.ThirdPartyPayService;
import com.pinshang.qingyun.order.service.pay.model.TradeQueryParam;
import com.pinshang.qingyun.order.service.recharge.RechargeService;
import com.pinshang.qingyun.pay.dto.QueryODTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Author: sk
 * @Date: 2025/1/2
 */
@Slf4j
@Service
public class XdaCancelOrderAmountCompareService {

    @Autowired
    private OrderMapper orderMapper;
    @Autowired
    private OrderBillMapper orderBillMapper;
    @Autowired
    private XdaPreOrderMapper xdaPreOrderMapper;
    @Autowired
    private XdaPayBillMapper xdaPayBillMapper;
    @Autowired
    private XfOrderMapper xfOrderMapper;
    @Autowired
    private ThirdPartyPayService thirdPartyPayService;
    @Autowired
    private RechargeService  rechargeService;
    @Autowired
    private WeChatSendMessageService weChatSendMessageService;


    /**
     * 鲜达取消订单金额对比
     * @param timeStamp
     * @return
     */
    @Async
    public Boolean compareCancelOrderAmount(String timeStamp) {
        String beginTime = timeStamp + " 00:00:00";
        String endTime = timeStamp + " 23:59:59";

        long queryBeginTimeMillis = System.currentTimeMillis();

        // 1.查询日期内取消的鲜达订单
        List<Order> orderList = getOrderList(beginTime, endTime);
        if(CollectionUtils.isEmpty(orderList)){
            log.warn("查询日期内取消的鲜达订单为空, timeStamp {}", timeStamp);
            return false;
        }

        Set<Long> storeIdList = orderList.stream().map(item -> item.getStoreId()).collect(Collectors.toSet());
        Set<String> orderCodeList = orderList.stream().map(item -> item.getOrderCode()).collect(Collectors.toSet());

        // 2.根据订单号、客户查询预订单表
        List<XdaPreOrder> xdaPreOrderList = getXdaPreOrders(storeIdList, beginTime, endTime, orderCodeList);
        if(CollectionUtils.isEmpty(xdaPreOrderList)) {
            log.warn("查询日期内取消的鲜达预订单为空, timeStamp {}", timeStamp);
            return false;
        }

        storeIdList = xdaPreOrderList.stream().map(item -> item.getStoreId()).collect(Collectors.toSet());
        Set<String> billCodeList = xdaPreOrderList.stream().map(item -> item.getBillCode()).collect(Collectors.toSet());

        // 3.查询现付单，获取每单用户实际付了多少钱
        List<XfOrder> xfOrderList = getXfOrderList(storeIdList, beginTime, endTime, billCodeList);
        if(CollectionUtils.isEmpty(xfOrderList)) {
            log.warn("查询日期内取消的鲜达现付订单为空, timeStamp {}", timeStamp);
            return false;
        }

        storeIdList = xfOrderList.stream().map(item -> item.getStoreId()).collect(Collectors.toSet());
        billCodeList = xfOrderList.stream().map(item -> item.getBillCode()).collect(Collectors.toSet());
        Map<String, BigDecimal> xfAmountMap = xfOrderList.stream().collect(Collectors.toMap(XfOrder::getBillCode,XfOrder::getPayAmount,(key1 , key2)-> key2));

        // 4.查询订单账款表
        Map<Long, List<OrderBill>> orderBillMap = getOrderBillMap(storeIdList, beginTime, endTime);

        // 支付成功未退款,充值失败 addChargeMsgList
        Set<String> addChargeMsgList = new HashSet<>();
        // 支付成功未退款,账户已被扣除 exceptionMsgList
        Set<String> exceptionMsgList = new HashSet<>();

        // 5.查询鲜达payBill
        List<XDAPayBill> payBillList = getXdaPayBillList(storeIdList, beginTime, endTime, billCodeList);

        long queryEndTimeMillis = System.currentTimeMillis();
        log.warn("鲜达取消订单金额对比查询订单耗时1：{}" ,(queryEndTimeMillis - queryBeginTimeMillis) + " 毫秒");

        if(CollectionUtils.isNotEmpty(payBillList)) {
            Map<String, List<XDAPayBill>> payBillMap = payBillList.stream().collect(Collectors.groupingBy(XDAPayBill::getBillCode));
            payBillMap.forEach((key, value) -> {
               /* try {
                    Thread.sleep(100L);
                }catch (Exception e){
                    log.error("鲜达取消订单金额对比Thread异常", e);
                }*/

                String billCode = key;
                BigDecimal payAmount = xfAmountMap.get(billCode) != null ? xfAmountMap.get(billCode) : BigDecimal.ZERO;

                List<OrderBill> orderBills = orderBillMap.get(value.get(0).getStoreId());
                if(orderBills == null) {
                    orderBills = new ArrayList<>();
                }

                // 6.当前billCode支付成功
                // 同一个billCode可能有多个支付方式，其中一个支付成功了。就当做成功
                if(isPayOk(value)) {

                    // 7.退款成功
                    if(isReturnOk(value)) {
                        // 用户支付成功了，也退款成功了。就忽略
                    }else {
                        // 支付成功了，但是没有退款。则查看钱有没有充值到客户余额
                        // 如果没有充值到客户余额，则告警
                        List<OrderBill> addChargeBillList = orderBills.stream().filter(p ->
                                StoreBillTypeEnums.XD_DEPOSIT.getCode() == p.getBillType()
                                && payAmount.compareTo(p.getPaAmount()) == 0
                                && billCode.equals(p.getTradeCode())).collect(Collectors.toList());
                        if(CollectionUtils.isEmpty(addChargeBillList)) {
                            addChargeMsgList.add(billCode);
                        }else {
                            // 如果充值到客户余额了，则查看预订单转订单异常导致账户余额又被扣了。则告警
                            List<OrderBill> exceptionBillList = orderBills.stream().filter(p ->
                                    StoreBillTypeEnums.XD_ORDER_ERROR_CANCEL_DEDUCTION.getCode() == p.getBillType()
                                    && (billCode + "999").equals(p.getTradeCode())).collect(Collectors.toList());
                            if(CollectionUtils.isNotEmpty(exceptionBillList)) {
                                exceptionMsgList.add(billCode);
                            }
                        }
                    }
                }
            });
        }

        log.warn("鲜达取消订单金额对比查询pay耗时2：{}" ,(System.currentTimeMillis() - queryEndTimeMillis) + " 毫秒");

        // 8.发送微信消息
        if(CollectionUtils.isNotEmpty(addChargeMsgList) || CollectionUtils.isNotEmpty(exceptionMsgList)) {
            List<String> errorMsgList = new ArrayList<>();
            errorMsgList.addAll(addChargeMsgList);
            errorMsgList.addAll(exceptionMsgList);
            log.warn("日期内取消的鲜达订单金额对比异常, timeStamp {}, errorMsgList {}", timeStamp, errorMsgList);

            if(CollectionUtils.isNotEmpty(addChargeMsgList)) {
                // 告警
                weChatSendMessageService.sendWeChatMessage("支付成功未退款,充值失败 " + String.join(",", addChargeMsgList));
            }

            if(CollectionUtils.isNotEmpty(exceptionMsgList)) {
                // 告警
                weChatSendMessageService.sendWeChatMessage("支付成功未退款,充值成功。账户已被扣除 " + String.join(",", exceptionMsgList));
            }
        }

        log.warn("鲜达取消订单金额对比共耗时3：{}" ,(System.currentTimeMillis() - queryBeginTimeMillis) + " 毫秒");
        return true;
    }

    /**
     * 调用pay 判断 XDAPayBill是否退款成功
     * @param xdaPayBills
     * @return
     */
    private Boolean isReturnOk(List<XDAPayBill> xdaPayBills){
        Boolean isReturnOk = false;
        for(XDAPayBill payBill : xdaPayBills) {
            TradeQueryParam tradeQueryParam = new TradeQueryParam(payBill);
            // 退款后面都加了999.所以查询是否退款成功也加999
            // com.pinshang.qingyun.order.service.recharge.RechargeService.reversePay
            tradeQueryParam.setBillCode(payBill.getBillCode() + "999");
            QueryODTO queryODTO = thirdPartyPayService.refundQuery(tradeQueryParam, AppCodeEnum.XDA, prepayCreationLogEvent -> rechargeService.handleLogEvent(prepayCreationLogEvent));
            if(queryODTO != null && queryODTO.getException() == null){
                if(queryODTO.getBizOk()){
                    isReturnOk = true;
                    break;
                }
            }
        }
        return isReturnOk;
    }

    /**
     * 调用pay 判断 XDAPayBill是否支付成功
     * @param xdaPayBills
     * @return
     */
    private Boolean isPayOk(List<XDAPayBill> xdaPayBills){
        Boolean isPayOk = false;
        for(XDAPayBill payBill : xdaPayBills) {
            TradeQueryParam tradeQueryParam = new TradeQueryParam(payBill);
            QueryODTO queryODTO = thirdPartyPayService.tradeQuery(tradeQueryParam, AppCodeEnum.XDA, prepayCreationLogEvent -> rechargeService.handleLogEvent(prepayCreationLogEvent));
            if (queryODTO.getException() == null && queryODTO.getBizOk()) {
                isPayOk = true;
                break;
            }
        }
        return isPayOk;
    }

    private List<XDAPayBill> getXdaPayBillList(Set<Long> storeIdList, String beginTime, String endTime, Set<String> billCodeList) {
        Example payBillEx = new Example(XDAPayBill.class);
        payBillEx.createCriteria()
                .andIn("storeId", storeIdList)
                .andGreaterThan("createTime", beginTime)
                .andLessThan("createTime", endTime)
                .andIn("billCode", billCodeList);
        payBillEx.orderBy("updateTime").desc();
        List<XDAPayBill> payBillList = xdaPayBillMapper.selectByExample(payBillEx);
        return payBillList;
    }


    private Map<Long, List<OrderBill>> getOrderBillMap(Set<Long> storeIdList, String beginTime, String endTime){
        Map<Long, List<OrderBill>> orderBillMap = new HashMap<>(storeIdList.size());

        Example orderBillEx = new Example(OrderBill.class);
        orderBillEx.createCriteria()
                .andIn("storeId", storeIdList)
                .andGreaterThan("createTime", beginTime)
                .andLessThan("createTime", endTime);
        List<OrderBill> orderBillList = orderBillMapper.selectByExample(orderBillEx);
        if(CollectionUtils.isNotEmpty(orderBillList)) {
            orderBillMap = orderBillList.stream().collect(Collectors.groupingBy(OrderBill::getStoreId));
        }

        return orderBillMap;
    }

    private List<XfOrder> getXfOrderList(Set<Long> storeIdList, String beginTime, String endTime, Set<String> billorderCodeList) {
        Example xfOrderEx = new Example(XfOrder.class);
        xfOrderEx.createCriteria()
                .andIn("storeId", storeIdList)
                .andGreaterThan("createTime", beginTime)
                .andLessThan("createTime", endTime)
                .andIn("billCode", billorderCodeList);
        List<XfOrder> xfOrderList = xfOrderMapper.selectByExample(xfOrderEx);
        return xfOrderList;
    }

    private List<XdaPreOrder> getXdaPreOrders(Set<Long> storeIdList, String beginTime, String endTime, Set<String> orderCodeList) {
        Example xdaPreOrderExample = new Example(XdaPreOrder.class);
        xdaPreOrderExample.createCriteria()
                .andIn("storeId", storeIdList)
                .andGreaterThan("createTime", beginTime)
                .andLessThan("createTime", endTime)
                .andIn("orderCode", orderCodeList);
        List<XdaPreOrder> xdaPreOrderList = xdaPreOrderMapper.selectByExample(xdaPreOrderExample);
        return xdaPreOrderList;
    }

    private List<Order> getOrderList(String beginTime, String endTime) {
        Example queryOrderByExample = new Example(Order.class);
        queryOrderByExample.createCriteria()
            .andEqualTo("orderType", OrderTypeEnum.XDA_APP_ORDER.getCode())
            .andGreaterThan("createTime", beginTime)
            .andLessThan("createTime", endTime)
            .andEqualTo("orderStatus", 2);
        List<Order> orderList = orderMapper.selectByExample(queryOrderByExample);
        return orderList;
    }
}
