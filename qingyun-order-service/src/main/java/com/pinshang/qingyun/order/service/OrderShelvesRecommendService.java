package com.pinshang.qingyun.order.service;

import com.github.pagehelper.PageInfo;
import com.pinshang.qingyun.base.configure.expand.QYAssert;
import com.pinshang.qingyun.base.enums.ShopShopStatusEnums;
import com.pinshang.qingyun.base.enums.order.OrderTypeEnum;
import com.pinshang.qingyun.base.spring.SpringBeanFinder;
import com.pinshang.qingyun.box.utils.DateUtil;
import com.pinshang.qingyun.order.constant.ThreadPoolBeanConstants;
import com.pinshang.qingyun.order.dto.ShelvesShoppingCartODTO;
import com.pinshang.qingyun.order.dto.ShopOrderedQuantityODTO;
import com.pinshang.qingyun.order.dto.ShopOrderedQuantityQueryIDTO;
import com.pinshang.qingyun.order.mapper.CommodityMapper;
import com.pinshang.qingyun.order.mapper.OrderMapper;
import com.pinshang.qingyun.order.mapper.ShopMapper;
import com.pinshang.qingyun.order.mapper.ShoppingCartMapper;
import com.pinshang.qingyun.order.mapper.entry.commodity.CommodityBasicEntry;
import com.pinshang.qingyun.order.model.shop.Shop;
import com.pinshang.qingyun.order.util.StallUtils;
import com.pinshang.qingyun.order.vo.commodity.CommodityListRequestVO;
import com.pinshang.qingyun.order.vo.commodity.CommodityVO;
import com.pinshang.qingyun.order.vo.order.ShoppingCartVo;
import com.pinshang.qingyun.price.dto.commodity.CommodityListRequestIDTO;
import com.pinshang.qingyun.price.dto.commodity.CommodityResultODTO;
import com.pinshang.qingyun.price.service.ProductPriceModelClient;
import com.pinshang.qingyun.shop.admin.dto.ShopShelvesRecommendODTO;
import com.pinshang.qingyun.shop.admin.service.ShelvesSettingClient;
import com.pinshang.qingyun.shop.dto.shopCommodity.ShopCommodityInfoODTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.entity.Example;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * @Author: sk
 * @Date: 2021/9/1
 */
@Slf4j
@Service
public class OrderShelvesRecommendService {

    @Autowired
    private ShopMapper shopMapper;
    @Autowired
    private OrderMapper orderMapper;
    @Autowired
    private ProductService productService;
    @Autowired
    private ProductPriceModelClient productPriceModelClient;
    @Autowired
    private CommodityMapper commodityMapper;
    @Autowired
    private ShoppingCartService shoppingCartService;
    @Autowired
    private ShoppingCartMapper shoppingCartMapper;
    @Autowired
    private ShelvesSettingClient shelvesSettingClient;
    @Autowired
    private StoreService storeService;
    @Autowired
    private WeChatSendMessageService weChatSendMessageService;
    @Autowired
    private OrderedQuantityService orderedQuantityService;
    @Autowired
    private RedissonClient redissonClient;
    @Autowired
    private ShopService shopService;

    /**
     * 统计门店在途数量
     * @return
     */
    @Async
    public Boolean countShopOrderedQuantity() {
        Example shopEx = new Example(Shop.class);
        shopEx.createCriteria().andIn("shopStatus",Arrays.asList(ShopShopStatusEnums.BEFORE_OPEN.getCode(), ShopShopStatusEnums.OPEN.getCode()));
        List<Shop> shopList = shopMapper.selectByExample(shopEx);
        List<Long> shopIdList = shopList.stream().map(item -> item.getId()).collect(Collectors.toList());

        // 获取t-1  止 t-8 的日期list
        List<String> orderTimeList = new ArrayList<>();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        for(int i = 1 ;i <= 8 ;i ++){
            Calendar now = Calendar.getInstance();
            now.set(Calendar.DATE,now.get(Calendar.DATE) + i);
            orderTimeList.add(sdf.format(now.getTime()));
        }


        // 获取线程池
        ThreadPoolTaskExecutor threadPool = (ThreadPoolTaskExecutor) SpringBeanFinder.getBean(ThreadPoolBeanConstants.ORDER_THREADPOOL);

        for(Long shopId :shopIdList){
            for(String orderTime : orderTimeList){
                // 创建任务并提交到线程池中
                threadPool.execute(new Runnable() {
                    @Override
                    public void run() {
                        try {
                            orderedQuantityService.countShopOrderedQuantity(orderTime, shopId);
                        } catch (Exception e) {
                            log.error("统计在途数量异常: ",e);
                        }
                    }
                });
            }
        }

        return Boolean.TRUE;
    }

    /**
     * 维护在途数量(t_md_shop_ordered_quantity)
     * @param orderCode
     * @param operateType
     * @return
     */
    @Async
    @Transactional(rollbackFor = Exception.class) // 加事务为了查询主库
    public Boolean insertOrUpdateShopOrderedQuantity(String orderCode, Integer operateType){
        log.info("维护在途数量 订单号：{}  操作类型：{}", orderCode, operateType);
        List<Integer> filterOrderTypeList = Arrays.asList(OrderTypeEnum.GROUP_AUTO_ORDER.getCode(),
                OrderTypeEnum.DIRECT_REPLENISH_ORDER.getCode(),
                OrderTypeEnum.DIRECT_AUDIT_ORDER.getCode(),
                OrderTypeEnum.TAKE_AUTO_ORDER.getCode());

        // 根据订单号查询订单信息
        List<ShopOrderedQuantityODTO> orderList = orderMapper.queryOrderDetailByOrderCode(orderCode, filterOrderTypeList);
        if(CollectionUtils.isEmpty(orderList)){
            log.warn("维护在途数量,订单信息不存在.或者该订单不需要统计在途数量 订单号:{}", orderCode);
            return Boolean.FALSE;
        }

        ShopOrderedQuantityODTO order = orderList.get(0);
        String orderTime = order.getOrderTime();
        Long shopId = order.getShopId();

        RLock lock = redissonClient.getLock("order:orderedQuantity:" + orderTime + shopId);
        lock.lock(5L, TimeUnit.SECONDS);
        try {
            orderedQuantityService.insertOrUpdateOrderedQuantity(orderList, operateType);
        }catch (Exception e){
            log.error("维护在途数量异常: ", e);
        }finally {
            lock.unlock(); // 释放锁
        }
        return Boolean.TRUE;
    }
    /**
     * 启用线程池计算推荐数量并且加入购物车
     * @param hhmm 时分
     * @return
     */
    public Boolean shelvesRecommendAddShoppingCart(String hhmm){
        // 根据时分 反查所有门店的牌面方案,牌面量
        List<ShopShelvesRecommendODTO> shopRecommendList = shelvesSettingClient.findShopShelvesRecommendCommodity(hhmm);
        if(CollectionUtils.isEmpty(shopRecommendList)){
            return false;
        }

        // 排面加购物车过滤大店
        Set<Long> shopIdList = shopRecommendList.stream().map(item -> item.getShopId()).collect(Collectors.toSet());
        List<Shop> shopList = shopService.getShopByIdList(new ArrayList<>(shopIdList));
        Map<Long, Long> bigShopIdMap = new HashMap<>(shopList.size());
        shopList.forEach(item -> {
            if(StallUtils.isStallSubcontractor(item.getManagementMode())) {
                bigShopIdMap.put(item.getId(), item.getId());
            }
        });

        shopRecommendList = shopRecommendList.stream().filter(p -> !bigShopIdMap.containsKey(p.getShopId())).collect(Collectors.toList());
        if(CollectionUtils.isEmpty(shopRecommendList)){
            return false;
        }

        Map<Long, List<ShopShelvesRecommendODTO>> recommendMap = shopRecommendList.stream().collect(Collectors.groupingBy(ShopShelvesRecommendODTO::getShopId));
        Map<Long, Long> idMap = shopRecommendList.stream().collect(Collectors.toMap(ShopShelvesRecommendODTO::getShopId, ShopShelvesRecommendODTO::getStoreId,(key1 , key2)-> key2));

        // 获取线程池
        ThreadPoolTaskExecutor threadPool = (ThreadPoolTaskExecutor) SpringBeanFinder.getBean(ThreadPoolBeanConstants.ORDER_THREADPOOL);

        for(Map.Entry<Long, List<ShopShelvesRecommendODTO>> entry : recommendMap.entrySet()){
            // 创建任务并提交到线程池中
            threadPool.execute(new Runnable() {
                @Override
                public void run() {
                    try {
                        Long shopId = entry.getKey();
                        String storeId = idMap.get(shopId) + "";
                        List<ShopShelvesRecommendODTO> recommendCommodityList = entry.getValue();
                        recommendAddShoppingCart(shopId, storeId,recommendCommodityList);
                    } catch (Exception e) {
                        log.error("计算推荐数量并且加入购物车: ",e);
                    }
                }
            });
        }

        return Boolean.TRUE;
    }


    /**
     * 计算数量
     * @param shopId
     * @param storeId
     * @param recommendCommodityList
     */
    public void recommendAddShoppingCart(Long shopId, String storeId,List<ShopShelvesRecommendODTO> recommendCommodityList){
        List<Long> commodityIdList = recommendCommodityList.stream().map(item -> item.getCommodityId()).collect(Collectors.toList());
        Map<Long, BigDecimal> recommendCommodityMap = recommendCommodityList.stream().collect(Collectors.toMap(ShopShelvesRecommendODTO::getCommodityId, ShopShelvesRecommendODTO::getRecommendQuantity,(key1 , key2)-> key2));

        // 调用client 去 qingyun-price 查询
        CommodityListRequestIDTO idto = new CommodityListRequestIDTO();

        CommodityListRequestVO commodityListRequestVO = new CommodityListRequestVO();
        commodityListRequestVO.setShopId(shopId);
        List<Long> commodityPurchaseList = commodityMapper.findShopCommodityPurchaseList(commodityListRequestVO);
        if(CollectionUtils.isNotEmpty(commodityPurchaseList)){
            idto.setCommodityPurchaseIdList(commodityPurchaseList);
        }else {
            return;
        }

        idto.setStoreId(storeId);
        idto.setCommodityIdListAll(commodityIdList);
        idto.setPageNo(1);
        idto.setPageSize(Integer.MAX_VALUE);
        PageInfo<CommodityResultODTO> resultPageData = productPriceModelClient.findStoreCommodityListByPage(idto);
        List<CommodityResultODTO> pageList = resultPageData.getList();

        if(CollectionUtils.isNotEmpty(pageList)){
            Boolean isXd =  storeService.getIsXd(Long.valueOf(storeId));
            // 获取鲜到库存
            Map<Long, ShopCommodityInfoODTO> xdStockMap = productService.getXdShopCommodityStock(shopId,commodityIdList);

            // 查询门店商品在途数量
            String orderTime = DateUtil.getDateFormate(new Date(),"yyyy-MM-dd");
            List<ShopOrderedQuantityODTO> orderedQuantityList = orderMapper.selectShopOrderQuantity(shopId,commodityIdList,orderTime);
            Map<Long, BigDecimal> orderedQuantityMap = orderedQuantityList.stream().collect(Collectors.toMap(ShopOrderedQuantityODTO::getCommodityId,ShopOrderedQuantityODTO::getQuantity,(key1 , key2)-> key2));

            // 查询商品基本信息
            CommodityVO vo = new CommodityVO();
            vo.setCommodityIdList(commodityIdList);
            List<CommodityBasicEntry> commodityBasicList = commodityMapper.findCommodityBasicListByParam(vo);
            Map<String, BigDecimal> commMap = new HashMap<>();
            if(isXd){
                commMap = commodityBasicList.stream().collect(Collectors.toMap(CommodityBasicEntry::getCommodityId,CommodityBasicEntry::getXdSalesBoxCapacity,(key1 , key2)-> key2));
            }else {
                commMap = commodityBasicList.stream().collect(Collectors.toMap(CommodityBasicEntry::getCommodityId,CommodityBasicEntry::getSalesBoxCapacity,(key1 , key2)-> key2));
            }

            // 根据storeId查询门店购物车
            List<ShelvesShoppingCartODTO>  shelvesShoppingCartList = shoppingCartMapper.findShoppingCartCreateId(Long.valueOf(storeId));
            Map<Long, List<Long>> shelvesShoppingCartMap = shelvesShoppingCartList.stream().collect(Collectors.groupingBy(ShelvesShoppingCartODTO::getCommodityId, Collectors.mapping(ShelvesShoppingCartODTO::getCreateId, Collectors.toList())));


            for(CommodityResultODTO result : pageList){
                Long commodityId = Long.valueOf(result.getCommodityId());

                ShopCommodityInfoODTO xdShopCommodity = xdStockMap.get(commodityId);
                BigDecimal stockQuantity = BigDecimal.ZERO;
                if (xdShopCommodity != null) {
                    stockQuantity = xdShopCommodity.getStockQuantity();
                }

                BigDecimal orderedQuantity = orderedQuantityMap.get(commodityId) != null ? orderedQuantityMap.get(commodityId) : BigDecimal.ZERO;
                BigDecimal recommendQuantity = recommendCommodityMap.get(commodityId);

                BigDecimal salesBoxCapacity = commMap.get(commodityId + ""); // 销售箱规

                log.info("排面订货 shopId:" + shopId + " commodityId:" + commodityId
                        + " stockQuantity:" + stockQuantity
                        + " orderedQuantity:" + orderedQuantity
                        + " recommendQuantity:" + recommendQuantity
                        + " salesBoxCapacity:" + salesBoxCapacity);
                // 如果库存数量 + 已定未收 < 排面基本量
                if(stockQuantity.add(orderedQuantity).compareTo(recommendQuantity) < 0){

                    // 如果 排面基本量 - (库存数量 + 已定未收) 的差值 >= 订货的销售箱规
                    BigDecimal diff = recommendQuantity.subtract(stockQuantity.add(orderedQuantity));
                    if(diff.compareTo(salesBoxCapacity) >= 0){

                        // (差值 / 订货的销售箱规) 向下取整后的值 * 订货的销售箱规
                        BigDecimal addQuantity = diff.divide(salesBoxCapacity,0,BigDecimal.ROUND_DOWN).multiply(salesBoxCapacity);

                        //添加购物车, 判断购物车是否有此商品
                        List<Long> createIdList = shelvesShoppingCartMap.get(commodityId);
                        if(CollectionUtils.isEmpty(createIdList)){
                            log.info("购物车无商品 storeId:" + storeId + " commodityId:" + commodityId);
                            //调用加入购物车
                            addShoppingCart(Long.valueOf(storeId),commodityId,addQuantity);

                        }else {
                            log.info("购物车有商品 storeId:" + storeId + " commodityId:" + commodityId + " createIdList:" + createIdList);
                            // 购物车商品是否系统添加
                            if(createIdList.contains(-1L)){
                                // 覆盖购物车
                                addShoppingCart(Long.valueOf(storeId),commodityId,addQuantity);
                            }
                        }
                    }
                }
            }
        }

    }

    /**
     * 加入购物车
     * @param storeId
     * @param commodityId
     * @param addQuantity
     */
    public void addShoppingCart(Long storeId,Long commodityId,BigDecimal addQuantity){
        ShoppingCartVo shoppingCartVo = new ShoppingCartVo();
        shoppingCartVo.setCreateId(-1L);
        shoppingCartVo.setInternal(false);
        shoppingCartVo.setStoreId(storeId);
        shoppingCartVo.setEnterpriseId(78L);
        shoppingCartVo.setCommodityId(commodityId);
        shoppingCartVo.setQuantity(addQuantity);
        shoppingCartVo.setStallId(-1L);
        try {
            shoppingCartService.addShoppingCart(shoppingCartVo);
        }catch (Exception e){
            log.error("推荐订货加购物车异常 storeId {} commodityId {}", storeId, commodityId, e);
            weChatSendMessageService.sendWeChatMessage("推荐订货加购物车异常");
        }

    }

    /**
     * 根据订货日期、日日鲜商品idList查询在途数量
     * @param idto
     * @return
     */
    public List<ShopOrderedQuantityODTO> queryShopOrderedQuantity(ShopOrderedQuantityQueryIDTO idto) {
        QYAssert.isTrue(CollectionUtils.isNotEmpty(idto.getCommodityIdList()), "商品idList不能为空");
        QYAssert.isTrue(StringUtils.isNotBlank(idto.getOrderTime()), "订单日期不能为空");
        return orderMapper.queryShopOrderedQuantity(idto.getCommodityIdList(), idto.getOrderTime());
    }
}
