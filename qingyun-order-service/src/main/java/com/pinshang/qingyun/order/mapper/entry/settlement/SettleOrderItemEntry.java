package com.pinshang.qingyun.order.mapper.entry.settlement;

import lombok.Data;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Date;
import java.util.List;

@Data
public class SettleOrderItemEntry {

    private String orderSettleUid; //
    private String kid;
    private long commodityId; // 商品ID
    private String commodityName; // 商品名称
    private String commodityCode; // 商品Code
    private String commoditySpec; // 商品规格
    private Long commodityUnitId;//单位ID
    private Long taxRateId;//税率 ID
    private BigDecimal quantity; // 下单数量
    private BigDecimal unitPrice; // 下单单价
    private BigDecimal totalPrice; // 下单总金额

    private BigDecimal deliveryUnitPrice; // 实际发货单价
    private BigDecimal deliveryQuantity; // 实际发货数量
    private BigDecimal deliveryTotalPrice; // 实际发货总金额

    private long categoryFirstId; // 一级分类ID
    private long categorySecondId; // 二级分类ID
    private long commodityThirdId;
    private String itemId; // 订单明细ID
    private BigDecimal rate; // 税率
    private Date createTime; //
    private String commodityUnit; // 商品单位
    private String categoryFirstName; // 一级分类名称
    private String categorySecondName; // 二级分类名称
    private String commodityThirdName;

    private int status; // 0:无效，1：有效
    private Date updateTime; //

}
