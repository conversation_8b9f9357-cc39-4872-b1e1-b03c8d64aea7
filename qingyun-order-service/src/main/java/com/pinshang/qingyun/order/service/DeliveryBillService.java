package com.pinshang.qingyun.order.service;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.pinshang.qingyun.common.dto.PrinterTaskINDTO;
import com.pinshang.qingyun.common.service.PrintClient;
import com.pinshang.qingyun.order.mapper.OrderMapper;
import com.pinshang.qingyun.order.mapper.entry.deliveryBill.DeliveryBillEntry;
import com.pinshang.qingyun.order.mapper.entry.deliveryBill.DeliveryOrderStkPrintEntry;
import com.pinshang.qingyun.order.util.CreateOrderStkA4;
import com.pinshang.qingyun.order.vo.deliveryBill.DeliveryBillSearchVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;

/**
 * @description: 送货单查询
 * @author: hhf
 * @time: 2021/8/4 14:25
 */
@Slf4j
@Service
public class DeliveryBillService {

    @Autowired
    private OrderMapper orderMapper;
    @Autowired
    private CreateOrderStkA4 createOrderStkA4;
    @Autowired
    private PrintClient printClient;

    /**
     * 送货单
     * @param vo
     * @return
     */
    public PageInfo<DeliveryBillEntry> findDeliveryBillPageInfoByParams(DeliveryBillSearchVo vo){
        PageInfo<DeliveryBillEntry> pageInfo = PageHelper.startPage(vo.getPageNo(), vo.getPageSize()).doSelectPageInfo(() ->
                orderMapper.findDeliveryBillPageInfoByParams(vo)
        );
        return pageInfo;
    }


    public String exportData(Long orderId){
        DeliveryOrderStkPrintEntry deliveryOrderStkPrintEntry = orderMapper.selectPrintViewById(orderId);
        deliveryOrderStkPrintEntry.setOrderPrintItems(orderMapper.selectItemPrintViewByOrderId(orderId));
        return createOrderStkA4.execCreatePdfByOrder(deliveryOrderStkPrintEntry,57);
    }

    @Transactional(rollbackFor = Exception.class)
    public String printer(Long orderId,Long userId){
        String s = exportData(orderId);
        PrinterTaskINDTO printTask = new PrinterTaskINDTO();
        printTask.setUserId(userId);
        printTask.setAvg(0);
        printTask.setCreatedAt(new Date());
        printTask.setDataType(5);
        printTask.setNumber(1);
        printTask.setFilePath(s);
        return printClient.savePrintTask(printTask);
    }



}
