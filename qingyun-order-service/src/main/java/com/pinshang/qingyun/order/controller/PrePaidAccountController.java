package com.pinshang.qingyun.order.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.pinshang.qingyun.order.service.pay.prepaidacct.PrePaidAccountService;

/**
 * 预付账户
 * <AUTHOR>
 *
 */
@RestController
@RequestMapping("/prePaidAccount")
public class PrePaidAccountController {

	@Autowired
	private PrePaidAccountService prePaidAccountService;
	
	@PostMapping("/asyncRecord")
	public void asyncRecord() {
		prePaidAccountService.asyncRecord();
	}
	
}
