package com.pinshang.qingyun.order.service.tda;

import com.alibaba.fastjson.JSON;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.pinshang.qingyun.base.configure.expand.BizLogicException;
import com.pinshang.qingyun.base.configure.expand.QYAssert;
import com.pinshang.qingyun.base.enums.DeliveryOrderTypeEnums;
import com.pinshang.qingyun.base.enums.YesOrNoEnums;
import com.pinshang.qingyun.box.utils.BeanCloneUtils;
import com.pinshang.qingyun.box.utils.DateUtil;
import com.pinshang.qingyun.box.utils.StringUtil;
import com.pinshang.qingyun.common.service.DictionaryClient;
import com.pinshang.qingyun.order.dto.xda.TdaOrderSyncTmsIDTO;
import com.pinshang.qingyun.order.dto.xda.tda.*;
import com.pinshang.qingyun.order.dto.xda.v4.TdaDeliveryTimeRangeODTO;
import com.pinshang.qingyun.order.enums.tda.*;
import com.pinshang.qingyun.order.mapper.XdaReturnOrderItemMapper;
import com.pinshang.qingyun.order.mapper.XdaReturnOrderMapper;
import com.pinshang.qingyun.order.mapper.entry.commodity.CommodityItemEntry;
import com.pinshang.qingyun.order.mapper.entry.commodity.CommodityItemEntryList;
import com.pinshang.qingyun.order.model.commodity.Commodity;
import com.pinshang.qingyun.order.model.xda.XdaReturnOrder;
import com.pinshang.qingyun.order.model.xda.XdaReturnOrderItem;
import com.pinshang.qingyun.order.service.CommodityService;
import com.pinshang.qingyun.order.service.xda.v4.TdaOrderService;
import com.pinshang.qingyun.order.service.xda.v4.ToBService;
import com.pinshang.qingyun.storage.dto.td.QueryTdDefaultWarehouseReqIDTO;
import com.pinshang.qingyun.storage.dto.thirdParty.CreateReturnApplyOrderItemReqDto;
import com.pinshang.qingyun.storage.dto.thirdParty.CreateReturnApplyOrderReqDto;
import com.pinshang.qingyun.storage.dto.thirdParty.CreateReturnApplyOrderTransferOrderDto;
import com.pinshang.qingyun.storage.dto.warehouse.WarehouseODTO;
import com.pinshang.qingyun.storage.service.td.TdClient;
import com.pinshang.qingyun.storage.service.thirdParty.ThirdPartyClient;
import com.pinshang.qingyun.store.sh.dto.*;
import com.pinshang.qingyun.store.sh.service.ComplaintNewClient;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.entity.Example;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.LocalTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <p>
 * 退货单--后台管理
 * </p>
 *
 * <AUTHOR> shenyang
 * @version : 1.0.0
 * @history : modify history
 * <author>              <time>              <version>              <desc>
 * @since : 2024/05/10 11:11
 */
@Slf4j
@Service
public class XdaReturnOrderServiceForAdmin {
    @Autowired
    private TdClient tdClient;
    @Value("${pinshang.img-server-url}")
    private String imgServerUrl;
    @Autowired
    private ToBService tobService;
    @Autowired
    private TdaOrderService tdaOrderService;
    @Autowired
    private CommodityService commodityService;
    @Autowired
    private ThirdPartyClient thirdPartyClient;
    @Autowired
    private DictionaryClient dictionaryClient;
    @Autowired
    private ComplaintNewClient complaintNewClient;
    @Autowired
    private XdaReturnOrderMapper xdaReturnOrderMapper;
    @Autowired
    private XdaReturnOrderItemMapper xdaReturnOrderItemMapper;


    /***
     * 退货单列表
     */
    public PageInfo<ReturnOrderODTO> queryReturnOrderList(ReturnOrderSearchIDTO vo) {
        QYAssert.isTrue(Objects.nonNull(vo.getCreateBeginTime()) && Objects.nonNull(vo.getCreateEndTime()), " 创建时间不能为空! ");
        PageInfo<ReturnOrderODTO> pageInfo = PageHelper.startPage(vo.getPageNo(), vo.getPageSize())
                .doSelectPageInfo(() -> xdaReturnOrderMapper.queryReturnOrderList(vo));
        for (ReturnOrderODTO odto : pageInfo.getList()) {
            odto.setStatusName(TDaReturnStatusEnum.getByCode(odto.getStatus()).getName());
            odto.setReturnTypeName(TDaReturnTypeEnum.getByCode(odto.getReturnType()).getName());
            String deliveryBatch = odto.getDeliveryBatch();
            if (StringUtils.isNotBlank(deliveryBatch)) {
                dictionaryClient.querySubDictionaryByParentOptionCode("DeliveryBatch")
                        .stream()
                        .filter(x -> x.getOptionCode().equals(deliveryBatch))
                        .findAny()
                        //设置批次名称
                        .ifPresent(y -> odto.setDeliveryBatchName(y.getOptionName()));
            }
        }
        return pageInfo;
    }

    /***
     * 退货单明细
     */
    public ReturnOrderDetailODTO queryReturnOrderItemList(Long returnOrderId) {
        QYAssert.isTrue(Objects.nonNull(returnOrderId), " 退货单详情-退货单ID不能为空! ");
        ReturnOrderODTO xdaReturnOrder = xdaReturnOrderMapper.selectById(returnOrderId);
        QYAssert.isTrue(null != xdaReturnOrder, " 退货单详情-退货单不存在! ");
        String deliveryBatch = xdaReturnOrder.getDeliveryBatch();
        ReturnOrderDetailODTO odto = BeanCloneUtils.copyTo(xdaReturnOrder, ReturnOrderDetailODTO.class);
        if (StringUtils.isNotBlank(deliveryBatch)) {
            dictionaryClient.querySubDictionaryByParentOptionCode("DeliveryBatch")
                    .stream()
                    .filter(x -> x.getOptionCode().equals(deliveryBatch))
                    .findAny()
                    //设置批次名称
                    .ifPresent(y -> odto.setDeliveryBatchName(y.getOptionName()));
        }
        odto.setStatusName(TDaReturnStatusEnum.getByCode(odto.getStatus()).getName());
        odto.setReturnTypeName(TDaReturnTypeEnum.getByCode(odto.getReturnType()).getName());
        odto.setBusinessTypeName(DeliveryOrderTypeEnums.getName(xdaReturnOrder.getBusinessType()));
        List<ReturnOrderItemODTO> returnOrderItemList = xdaReturnOrderItemMapper.queryByReturnOrderId(returnOrderId);
        for (ReturnOrderItemODTO returnOrderItem : returnOrderItemList) {
            returnOrderItem.setReturnOrderTypeName(TDaReturnOrderTypeEnum.getByCode(returnOrderItem.getReturnOrderType()).getName());
            for (ReturnOrderItemPicODTO item : returnOrderItem.getReturnOrderItemPicList()) {
                if (!StringUtil.isNullOrEmpty(item.getImgPicUrl()) && !item.getImgPicUrl().contains("http")) {
                    item.setImgPicUrl(imgServerUrl + item.getImgPicUrl());
                }
            }
        }
        if (CollectionUtils.isNotEmpty(returnOrderItemList)) {
            odto.setReturnOrderItemList(returnOrderItemList);
        }
        //计算取货时间
        List<TdaDeliveryTimeRangeODTO> pickUpTimeRangeList = calcPickUpTime(xdaReturnOrder);
        TdaDeliveryTimeRangeODTO tdaDeliveryTimeRangeODTO = pickUpTimeRangeList.stream()
                .filter(x -> Objects.equals(String.valueOf(x.getDeliveryBatch()), xdaReturnOrder.getDeliveryBatch())
                        && Objects.equals(x.getLogisticsCenterId(), xdaReturnOrder.getLogisticsCenterId())
                        && Objects.equals(x.getDeliveryTimeRange(), xdaReturnOrder.getPickUpTimeRange()))
                .findFirst().orElse(null);
        if (Objects.nonNull(tdaDeliveryTimeRangeODTO) && CollectionUtils.isNotEmpty(tdaDeliveryTimeRangeODTO.getPickupTimeList())) {
            String pickUpTimeStr = tdaDeliveryTimeRangeODTO.getPickupTimeList().get(0);
            Date pickUpTime = xdaReturnOrder.getPickUpTime();
            if (Objects.isNull(pickUpTime) && StringUtils.isNotEmpty(pickUpTimeStr)) {
                odto.setPickUpTime(DateUtil.parseDate(pickUpTimeStr, "yyyy-MM-dd"));
            }
        }
        odto.setPickUpTimeRangeList(pickUpTimeRangeList);
        return odto;
    }

    /***
     * 退货单审核
     */
    @Transactional(rollbackFor = Exception.class)
    public Boolean auditReturnOrder(AuditReturnOrderIDTO dto) {
        XdaReturnOrder xdaReturnOrder = xdaReturnOrderMapper.selectByPrimaryKey(dto.getId());
        List<ReturnOrderItemODTO> returnOrderItemList = xdaReturnOrderItemMapper.queryByReturnOrderId(xdaReturnOrder.getId());
        //校验参数
        checkParam(dto, xdaReturnOrder, returnOrderItemList);
        if (dto.getAuditResult() == TDaAuditResultEnum.FAIL.getCode()) {
            //审核不通过
            xdaReturnOrder.setStatus(TDaReturnStatusEnum.AUDIT_FAILED.getCode());
        } else if (dto.getAuditResult() == TDaAuditResultEnum.SUCCESS.getCode()) {
            if (xdaReturnOrder.getReturnOrderType() == 1) {
                //退货单 审核通过-》待取货
                xdaReturnOrder.setStatus(TDaReturnStatusEnum.PENDING_PICKUP.getCode());
            } else if (xdaReturnOrder.getReturnOrderType() == 2) {
                //差异单 审核通过=》已完成
                xdaReturnOrder.setStatus(TDaReturnStatusEnum.COMPLETED.getCode());
            }
            Map<Long, ReturnOrderItemODTO> map = returnOrderItemList.stream().collect(Collectors.toMap(ReturnOrderItemODTO::getId, Function.identity()));
            BigDecimal totalCheckMoney = BigDecimal.ZERO;
            for (AuditReturnOrdeItemIDTO auditReturnOrderItem : dto.getAuditReturnOrderItems()) {
                ReturnOrderItemODTO returnOrderItem = map.get(auditReturnOrderItem.getId());
                if (Objects.nonNull(returnOrderItem)) {
                    Integer isWeight = returnOrderItem.getIsWeight();
                    if (Objects.equals(auditReturnOrderItem.getCheckNumber(), 0)) {
                        auditReturnOrderItem.setCheckNumber(0);
                        auditReturnOrderItem.setCheckMoney(BigDecimal.ZERO);
                        auditReturnOrderItem.setCheckQuantity(BigDecimal.ZERO);
                    } else {
                        if (Objects.equals(isWeight, YesOrNoEnums.YES.getCode())) {
                            auditReturnOrderItem.setCheckNumber(returnOrderItem.getApplyNumber());
                            auditReturnOrderItem.setCheckMoney(returnOrderItem.getApplyMoney());
                            auditReturnOrderItem.setCheckQuantity(returnOrderItem.getApplyQuantity());
                        } else {
                            BigDecimal price;
                            BigDecimal commodityPriceInput = auditReturnOrderItem.getCommodityPrice();
                            BigDecimal commodityPrice = returnOrderItem.getCommodityPrice();
                            if (Objects.isNull(commodityPrice) && Objects.isNull(commodityPriceInput)) {
                                QYAssert.isTrue(false, " 退货单审核-单价不能为空! ");
                            }
                            if (Objects.nonNull(commodityPrice)) {
                                price = commodityPrice;
                            } else {
                                // 输入的时候 单价也允许等于0，范围是 0<=单价<99999.99
                                QYAssert.isTrue(commodityPriceInput.compareTo(BigDecimal.ZERO) >= 0 && commodityPriceInput.compareTo(new BigDecimal("99999.99")) < 0, "退货单审核-输入单价必须在0到99999.99之间!");
                                price = commodityPriceInput;
                            }
                            BigDecimal commodityPackageSpec = returnOrderItem.getCommodityPackageSpec();
                            Integer checkNumber = auditReturnOrderItem.getCheckNumber();
                            //审核数量不能大于申请数量
                            BigDecimal checkQuantity = commodityPackageSpec.multiply(BigDecimal.valueOf(checkNumber));
                            BigDecimal finalCheckQuantity = returnOrderItem.getApplyQuantity().compareTo(checkQuantity) < 0 ? returnOrderItem.getApplyQuantity() : checkQuantity;
                            auditReturnOrderItem.setCheckQuantity(finalCheckQuantity);
                            auditReturnOrderItem.setCheckMoney(finalCheckQuantity.multiply(price));
                        }
                    }
                    BigDecimal checkMoney = auditReturnOrderItem.getCheckMoney();
                    if (returnOrderItem.getReturnOrderType() == 3) {
                        totalCheckMoney = totalCheckMoney.subtract(checkMoney);
                    } else {
                        totalCheckMoney = totalCheckMoney.add(checkMoney);
                    }
                }
            }
            //更新明细表
            xdaReturnOrderItemMapper.batchUpdateByAudit(dto.getAuditReturnOrderItems());
            //合计申请金额和数量，份数
            BigDecimal totalCheckQuantity = dto.getAuditReturnOrderItems().stream().map(AuditReturnOrdeItemIDTO::getCheckQuantity).reduce(BigDecimal.ZERO, BigDecimal::add);
            Integer totalCheckNumber = dto.getAuditReturnOrderItems().stream().map(AuditReturnOrdeItemIDTO::getCheckNumber).reduce(0, Integer::sum);
            xdaReturnOrder.setTotalCheckMoney(totalCheckMoney);
            xdaReturnOrder.setTotalCheckNumber(totalCheckNumber);
            xdaReturnOrder.setTotalCheckQuantity(totalCheckQuantity);
        }
        //更新退单主表
        xdaReturnOrder.setLogisticsCenterId(dto.getLogisticsCenterId());
        xdaReturnOrder.setDeliveryBatch(Objects.toString(dto.getDeliveryBatch(), null));
        xdaReturnOrder.setPickUpTimeRange(dto.getPickUpTimeRange());
        xdaReturnOrder.setPickUpTime(dto.getPickUpTime());
        xdaReturnOrder.setCheckUserId(dto.getCheckUserId());
        xdaReturnOrder.setCheckTime(new Date());
        int flag = xdaReturnOrderMapper.updateByPrimaryKeySelective(xdaReturnOrder);

        if (dto.getAuditResult() == TDaAuditResultEnum.SUCCESS.getCode()) {
            // 少货 同步给 投诉表
            if (xdaReturnOrder.getReturnOrderType() == 2) {
                ReturnOrderDetailODTO returnOrderDetail = queryReturnOrderItemList(dto.getId());
                syncReturnOrder2Complaint(BeanCloneUtils.copyTo(returnOrderDetail, SyncReturnOrderIDTO.class));
            }
            if (xdaReturnOrder.getReturnOrderType() == 1) {
                //退货单审核通过 给物流发消息
                TdaOrderSyncTmsIDTO idto = TdaOrderSyncTmsIDTO.forReturnOrder(xdaReturnOrder.getId(), xdaReturnOrder.getStatus());
                tdaOrderService.tdaOrderSyncToTms(idto);
            }

        }
        return flag > 0;
    }

    /***
     * 大仓待确认退货单列表
     */
    public PageInfo<WarehousePendingODTO> queryWarehousePendingList(WarehousePendingSearchIDTO vo) {
        if (vo.getStatus() == 6) {
            QYAssert.isTrue(Objects.nonNull(vo.getConfirmBeginTime()) && Objects.nonNull(vo.getConfirmEndTime()), " 大仓确认时间不能为空! ");
        }
        if (vo.getStatus() == 4) {
            QYAssert.isTrue(Objects.nonNull(vo.getPickBeginTime()) && Objects.nonNull(vo.getPickEndTime()), " 取货完成时间不能为空! ");
        }
        vo.setReturnOrderType(TDaReturnOrderTypeEnum.RETURN.getCode());
        PageInfo<WarehousePendingODTO> pageInfo = PageHelper.startPage(vo.getPageNo(), vo.getPageSize())
                .doSelectPageInfo(() -> xdaReturnOrderMapper.queryWarehousePendingList(vo));
        for (WarehousePendingODTO odto : pageInfo.getList()) {
            odto.setStatusName(TDaReturnStatusEnum.getByCode(odto.getStatus()).getName());
            odto.setReturnTypeName(TDaReturnTypeEnum.getByCode(odto.getReturnType()).getName());
        }
        return pageInfo;
    }

    /***
     * 大仓待确认退货单明细
     */
    public WarehousePendingDetailODTO queryWarehousePendingItemList(Long returnOrderId) {
        QYAssert.isTrue(Objects.nonNull(returnOrderId), " 退货单详情-退货单ID不能为空! ");
        ReturnOrderODTO xdaReturnOrder = xdaReturnOrderMapper.selectById(returnOrderId);
        QYAssert.isTrue(null != xdaReturnOrder, " 退货单详情-退货单不存在! ");
        WarehousePendingDetailODTO odto = BeanCloneUtils.copyTo(xdaReturnOrder, WarehousePendingDetailODTO.class);
        odto.setStatusName(TDaReturnStatusEnum.getByCode(odto.getStatus()).getName());
        odto.setReturnTypeName(TDaReturnTypeEnum.getByCode(odto.getReturnType()).getName());
        List<ReturnOrderItemODTO> returnOrderItemList = xdaReturnOrderItemMapper.queryWarehousePendingItemList(returnOrderId);
        for (ReturnOrderItemODTO returnOrderItem : returnOrderItemList) {
            for (ReturnOrderItemPicODTO item : returnOrderItem.getReturnOrderItemPicList()) {
                if (!StringUtil.isNullOrEmpty(item.getImgPicUrl()) && !item.getImgPicUrl().contains("http")) {
                    item.setImgPicUrl(imgServerUrl + item.getImgPicUrl());
                }
            }
        }
        if (CollectionUtils.isNotEmpty(returnOrderItemList)) {
            odto.setReturnOrderItemList(returnOrderItemList);
        }
        return odto;
    }

    /***
     * 大仓确认退货单
     */
    @Transactional(rollbackFor = Exception.class)
    public Boolean confirmReturnOrder(ConfirmReturnOrderIDTO dto) {
        //查询主表
        ReturnOrderODTO xdaReturnOrder = xdaReturnOrderMapper.selectById(dto.getId());
        //查询明细
        Example example = new Example(XdaReturnOrderItem.class);
        example.createCriteria().andEqualTo("returnOrderId", dto.getId());
        List<XdaReturnOrderItem> xdaReturnOrderItems = xdaReturnOrderItemMapper.selectByExample(example);
        //校验参数
        checkParam(dto, xdaReturnOrder, xdaReturnOrderItems);

        List<ReturnOrderItemODTO> returnOrderItemList = xdaReturnOrderItemMapper.queryByReturnOrderId(xdaReturnOrder.getId());
        Map<Long, ReturnOrderItemODTO> map = returnOrderItemList.stream().collect(Collectors.toMap(ReturnOrderItemODTO::getId, Function.identity()));
        for (ConfirmReturnOrdeItemIDTO confirmReturnOrderItem : dto.getConfirmReturnOrderItems()) {
            ReturnOrderItemODTO returnOrderItem = map.get(confirmReturnOrderItem.getId());
            if (Objects.nonNull(returnOrderItem)) {
                Integer isWeight = returnOrderItem.getIsWeight();
                if (Objects.equals(confirmReturnOrderItem.getConfirmNumber(), 0)) {
                    confirmReturnOrderItem.setConfirmNumber(0);
                    confirmReturnOrderItem.setConfirmMoney(BigDecimal.ZERO);
                    confirmReturnOrderItem.setConfirmQuantity(BigDecimal.ZERO);
                } else {
                    if (Objects.equals(isWeight, YesOrNoEnums.YES.getCode())) {
                        // 验证称重品的审核 数量，金额
                        confirmReturnOrderItem.setConfirmNumber(returnOrderItem.getCheckNumber());
                        confirmReturnOrderItem.setConfirmMoney(returnOrderItem.getCheckMoney());
                        confirmReturnOrderItem.setConfirmQuantity(returnOrderItem.getCheckQuantity());
                    } else {
                        BigDecimal commodityPrice = returnOrderItem.getCommodityPrice();
                        BigDecimal commodityPackageSpec = returnOrderItem.getCommodityPackageSpec();
                        Integer confirmNumber = confirmReturnOrderItem.getConfirmNumber();
                        BigDecimal confirmQuantity = commodityPackageSpec.multiply(BigDecimal.valueOf(confirmNumber));
                        //确认数量不能超过审核数量
                        BigDecimal finalConfirmQuantity = confirmQuantity.compareTo(returnOrderItem.getCheckQuantity()) > 0 ? returnOrderItem.getCheckQuantity() : confirmQuantity;
                        confirmReturnOrderItem.setConfirmNumber(confirmNumber);
                        confirmReturnOrderItem.setConfirmQuantity(finalConfirmQuantity);
                        confirmReturnOrderItem.setConfirmMoney(finalConfirmQuantity.multiply(commodityPrice));
                    }
                }
            }
        }

        //更新明细表
        int flag = xdaReturnOrderItemMapper.batchUpdateByConfirm(dto.getConfirmReturnOrderItems());

        XdaReturnOrder updateOrder = new XdaReturnOrder();
        updateOrder.setId(dto.getId());
        //已完成
        updateOrder.setStatus(TDaReturnStatusEnum.COMPLETED.getCode());
        updateOrder.setConfirmUserId(dto.getConfirmUserId());
        updateOrder.setConfirmTime(new Date());
        //更新退单主表
        xdaReturnOrderMapper.updateByPrimaryKeySelective(updateOrder);

        // 同步给大仓
        syncToWarehouse(dto, xdaReturnOrder, xdaReturnOrderItems);

        if (xdaReturnOrder.getReturnOrderType() == 1) {
            //退货单确认完成 给物流发消息
            TdaOrderSyncTmsIDTO idto = TdaOrderSyncTmsIDTO.forReturnOrder(dto.getId(), TDaReturnStatusEnum.COMPLETED.getCode());
            tdaOrderService.tdaOrderSyncToTms(idto);
        }

        return flag > 0;
    }

    /**
     * 确认结果同步给大仓
     */
    private void syncToWarehouse(ConfirmReturnOrderIDTO dto, ReturnOrderODTO xdaReturnOrder, List<XdaReturnOrderItem> xdaReturnOrderItems) {
        CreateReturnApplyOrderReqDto req = new CreateReturnApplyOrderReqDto();
        req.setApplyDate(xdaReturnOrder.getCreateTime());
        req.setConfirmDate(new Date());
        CreateReturnApplyOrderTransferOrderDto transferOrderDto = new CreateReturnApplyOrderTransferOrderDto();
        transferOrderDto.setTransferOrderCode(xdaReturnOrder.getWaybillCode());
        transferOrderDto.setTransferUserName(xdaReturnOrder.getDriverName());
        List<CreateReturnApplyOrderTransferOrderDto> transferOrderList = new ArrayList<>();
        transferOrderList.add(transferOrderDto);
        req.setTransferOrderList(transferOrderList);
        req.setStoreId(xdaReturnOrder.getStoreId());
        req.setType(10);
        req.setSourceType(8);
        req.setOrderTime(xdaReturnOrder.getDeliveryTime());
        req.setDeliveryBatch(Integer.valueOf(xdaReturnOrder.getDeliveryBatch()));
        int returnSourceType = 1;
        if (TDaReturnSourceEnum.APP_RETURN.getCode() == xdaReturnOrder.getReturnSource()) {
            returnSourceType = 1;
        } else if (TDaReturnSourceEnum.PX_RETURN.getCode() == xdaReturnOrder.getReturnSource()) {
            returnSourceType = 1;
        } else if (TDaReturnSourceEnum.ORDER_CANCEL.getCode() == xdaReturnOrder.getReturnSource()) {
            returnSourceType = 2;
        } else if (TDaReturnSourceEnum.DELIVERY_FAILURE.getCode() == xdaReturnOrder.getReturnSource()) {
            returnSourceType = 3;
        }
        req.setReturnSourceType(returnSourceType);
        if (TDaReturnSourceEnum.APP_RETURN.getCode() == xdaReturnOrder.getReturnSource()
                || TDaReturnSourceEnum.PX_RETURN.getCode() == xdaReturnOrder.getReturnSource()
                || TDaReturnSourceEnum.DELIVERY_FAILURE.getCode() == xdaReturnOrder.getReturnSource()) {
            req.setReturnWay(3);
        } else if (TDaReturnSourceEnum.ORDER_CANCEL.getCode() == xdaReturnOrder.getReturnSource()) {
            req.setReturnWay(2);
        }
        req.setReferCode(xdaReturnOrder.getReturnOrderSeq());
        req.setUserId(dto.getConfirmUserId());

        List<Long> commodityIds = xdaReturnOrderItems.stream().mapToLong(XdaReturnOrderItem::getCommodityId).boxed().distinct().collect(Collectors.toList());
        //批量查询组合商品的子商品列表
        List<CommodityItemEntryList> commodityItemList = tobService.getCommodityItemListByCommodityIdList(commodityIds);
        Map<Long, List<CommodityItemEntry>> combChildCommodityItemMap = commodityItemList.stream().collect(Collectors.toMap(CommodityItemEntryList::getCommodityId, CommodityItemEntryList::getCommodityItemEntry));
        if (MapUtils.isNotEmpty(combChildCommodityItemMap)) {
            Map<Boolean, List<Long>> partitionedIds = commodityIds.stream()
                    .collect(Collectors.partitioningBy(combChildCommodityItemMap::containsKey));

            // 组合品ID
            List<Long> combIds = partitionedIds.get(true);
            // 非组合品ID
            List<Long> nonCombIds = partitionedIds.get(false);

            // 将子品ID添加到 nonCombIds 列表中
            List<Long> subItemIds = combIds.stream()
                    .flatMap(combId -> combChildCommodityItemMap.get(combId).stream())
                    .map(subItem -> Long.valueOf(subItem.getCommodityItemId()))
                    .distinct()
                    .collect(Collectors.toList());

            // 合并非组合品ID和子品ID
            commodityIds = new ArrayList<>(nonCombIds);
            commodityIds.addAll(subItemIds);
        }

        QueryTdDefaultWarehouseReqIDTO reqVo = new QueryTdDefaultWarehouseReqIDTO();
        reqVo.setLogisticsCenterId(xdaReturnOrder.getLogisticsCenterId());
        reqVo.setCommodityIds(commodityIds);
        // 查询通达的默认仓库
        Map<Long, WarehouseODTO> tdDefaultWarehouseMap = tdClient.getTdDefaultWarehouse(reqVo);
        //查询商品信息（包装规格）
        Map<Long, Commodity> commodityInfoByIdMap = commodityService.findCommodityInfoByIdMap(commodityIds);

        Map<Long, ConfirmReturnOrdeItemIDTO> confirmReturnOrderItemMap = dto.getConfirmReturnOrderItems().stream()
                .collect(Collectors.toMap(ConfirmReturnOrdeItemIDTO::getId, Function.identity()));

        List<CreateReturnApplyOrderItemReqDto> itemReqVoList = new ArrayList<>();
        for (XdaReturnOrderItem xdaReturnOrderItem : xdaReturnOrderItems) {
            Long commodityId = xdaReturnOrderItem.getCommodityId();
            List<CommodityItemEntry> combChildItemList = combChildCommodityItemMap.getOrDefault(commodityId, Collections.emptyList());
            if (MapUtils.isNotEmpty(combChildCommodityItemMap) && !combChildItemList.isEmpty()) {
                //组合品，给大仓传子品
                addCombChildItemsToList(commodityInfoByIdMap, combChildItemList, xdaReturnOrderItem, confirmReturnOrderItemMap, tdDefaultWarehouseMap, itemReqVoList);
            } else {
                //非组合品
                addMainItemToList(xdaReturnOrderItem, confirmReturnOrderItemMap, tdDefaultWarehouseMap, itemReqVoList);
            }
        }
        req.setItemReqVoList(itemReqVoList);
        log.info("请求createReturnApplyOrder,req:{}", JSON.toJSONString(req));
        thirdPartyClient.createReturnApplyOrder(req);

    }


    /**
     * 取消退货单
     * 页面取消，或者hhf那边的投诉单取消
     */
    public Boolean cancelReturnOrder(CancelReturnOrderIDTO dto) {
        int flag = 0;
        if (Objects.equals(dto.getCancelSource(), 2)) {
            QYAssert.isTrue(StringUtils.isNotBlank(dto.getReturnOrderSeq()), " 取消退货单-退货单编号不能为空! ");
            XdaReturnOrder xdaReturnOrder = xdaReturnOrderMapper.selectByReturnOrderSeq(dto.getReturnOrderSeq());
            QYAssert.isTrue(null != xdaReturnOrder, " 取消退货单-退货单不存在! ");
            QYAssert.isTrue(!Objects.equals(xdaReturnOrder.getStatus(), TDaReturnStatusEnum.CANCELLED.getCode()), " 取消退货单-退货单已取消,不能重复取消! ");
            //hhf那边取消了投诉单，退货单也要更新为已取消
            XdaReturnOrder updateOrder = new XdaReturnOrder();
            updateOrder.setId(xdaReturnOrder.getId());
            updateOrder.setStatus(TDaReturnStatusEnum.CANCELLED.getCode());
            flag = xdaReturnOrderMapper.updateByPrimaryKeySelective(updateOrder);
            deleteReturnOrderItemByReturnOrderId(xdaReturnOrder.getId());
        } else if (Objects.equals(dto.getCancelSource(), 1)) {
            XdaReturnOrder xdaReturnOrder = xdaReturnOrderMapper.selectByPrimaryKey(dto.getId());
            QYAssert.isTrue(null != xdaReturnOrder, " 取消退货单-退货单不存在! ");
            QYAssert.isTrue(Objects.equals(xdaReturnOrder.getStatus(), TDaReturnStatusEnum.PENDING_AUDIT.getCode()), " 取消退货单-待审核状态的通达退货单可以进行取消! ");
            if (Objects.equals(xdaReturnOrder.getReturnSource(), TDaReturnSourceEnum.APP_RETURN.getCode())) {
                //页面上点的取消，如果是待审核并且是app发起的，更新退单主表 状态为已取消
                XdaReturnOrder updateOrder = new XdaReturnOrder();
                updateOrder.setId(xdaReturnOrder.getId());
                updateOrder.setStatus(TDaReturnStatusEnum.CANCELLED.getCode());
                flag = xdaReturnOrderMapper.updateByPrimaryKeySelective(updateOrder);
                deleteReturnOrderItemByReturnOrderId(xdaReturnOrder.getId());
            } else if (Objects.equals(xdaReturnOrder.getReturnSource(), TDaReturnSourceEnum.PX_RETURN.getCode())) {
                throw new BizLogicException("客服新增退货单，请至投诉单列表取消！");
            }
        }
        return flag > 0;
    }

    /**
     * 删除全部取消的退货单明细
     */
    private void deleteReturnOrderItemByReturnOrderId(Long returnOrderId) {
        Example example = new Example(XdaReturnOrderItem.class);
        example.createCriteria().andEqualTo("returnOrderId", returnOrderId);
        XdaReturnOrderItem updateOrderItem = new XdaReturnOrderItem();
        updateOrderItem.setStatus(1);
        xdaReturnOrderItemMapper.updateByExampleSelective(updateOrderItem, example);
    }

    /**
     * 修改退货单状态
     */
    public void updateReturnOrder(XdaReturnOrder xdaReturnOrder) {
        if (Objects.nonNull(xdaReturnOrder.getId())) {
            xdaReturnOrderMapper.updateByPrimaryKeySelective(xdaReturnOrder);
        }
    }

    /**
     * 有组合子品，组装子品明细同步给大仓
     */
    private void addCombChildItemsToList(Map<Long, Commodity> commodityInfoByIdMap,
                                         List<CommodityItemEntry> subItems,
                                         XdaReturnOrderItem xdaReturnOrderItem,
                                         Map<Long, ConfirmReturnOrdeItemIDTO> confirmReturnOrderItemMap,
                                         Map<Long, WarehouseODTO> tdDefaultWarehouseMap,
                                         List<CreateReturnApplyOrderItemReqDto> itemReqVoList) {
        for (CommodityItemEntry subItem : subItems) {
            CreateReturnApplyOrderItemReqDto reqDto = new CreateReturnApplyOrderItemReqDto();
            Long commodityItemId = Long.valueOf(subItem.getCommodityItemId());
            reqDto.setCommodityId(commodityItemId);

            WarehouseODTO odto = tdDefaultWarehouseMap.get(commodityItemId);
            if (Objects.nonNull(odto)) {
                reqDto.setWarehouseId(odto.getWarehouseId());
            }

            Commodity commodity = commodityInfoByIdMap.get(commodityItemId);
            if (Objects.isNull(commodity)) {
                //未查到商品信息
                continue;
            }

            BigDecimal commodityNum = subItem.getCommodityNum();
            BigDecimal quantity = xdaReturnOrderItem.getApplyQuantity().multiply(commodityNum);
            BigDecimal xdQuantity = xdaReturnOrderItem.getCheckQuantity().multiply(commodityNum);

            //子品只能根据组合品的数量确定子品的数量，份数需要根据包装规格计算
            int number = quantity.divide(commodity.getCommodityPackageSpec(), 0, RoundingMode.CEILING).intValue();
            //审核份数=审核数量/包装规格 向上取整
            int xdNumber = xdQuantity.divide(commodity.getCommodityPackageSpec(), 0, RoundingMode.CEILING).intValue();
            reqDto.setQuantity(quantity);
            reqDto.setNumber(number);
            reqDto.setXdQuantity(xdQuantity);
            reqDto.setXdNumber(xdNumber);

            ConfirmReturnOrdeItemIDTO idto = confirmReturnOrderItemMap.get(xdaReturnOrderItem.getId());
            if (Objects.nonNull(idto)) {
                BigDecimal confirmQuantity = idto.getConfirmQuantity().multiply(commodityNum);
                reqDto.setConfirmQuantity(confirmQuantity);
                //确认份数=确认数量/包装规格 向上取整
                int confirmNumber = confirmQuantity.divide(commodity.getCommodityPackageSpec(), 0, RoundingMode.CEILING).intValue();
                reqDto.setConfirmNumber(confirmNumber);
            }

            itemReqVoList.add(reqDto);
        }
    }

    /**
     * 非组合品，组装同步明细给大仓
     */
    private void addMainItemToList(XdaReturnOrderItem xdaReturnOrderItem,
                                   Map<Long, ConfirmReturnOrdeItemIDTO> confirmReturnOrderItemMap,
                                   Map<Long, WarehouseODTO> tdDefaultWarehouseMap,
                                   List<CreateReturnApplyOrderItemReqDto> itemReqVoList) {
        CreateReturnApplyOrderItemReqDto reqDto = new CreateReturnApplyOrderItemReqDto();
        Long commodityId = xdaReturnOrderItem.getCommodityId();
        WarehouseODTO odto = tdDefaultWarehouseMap.get(commodityId);

        if (Objects.nonNull(odto)) {
            reqDto.setWarehouseId(odto.getWarehouseId());
        }

        reqDto.setCommodityId(commodityId);
        reqDto.setQuantity(xdaReturnOrderItem.getApplyQuantity());
        reqDto.setNumber(xdaReturnOrderItem.getApplyNumber());
        reqDto.setXdQuantity(xdaReturnOrderItem.getCheckQuantity());
        reqDto.setXdNumber(xdaReturnOrderItem.getCheckNumber());

        ConfirmReturnOrdeItemIDTO idto = confirmReturnOrderItemMap.get(xdaReturnOrderItem.getId());
        if (Objects.nonNull(idto)) {
            reqDto.setConfirmQuantity(idto.getConfirmQuantity());
            reqDto.setConfirmNumber(idto.getConfirmNumber());
        }

        itemReqVoList.add(reqDto);
    }

    /***
     * 同步到投诉单列表 供结算使用
     */
    public void syncReturnOrder2Complaint(SyncReturnOrderIDTO dto) {
        if (TDaReturnSourceEnum.APP_RETURN.getCode() == dto.getReturnSource()) {
            ComplaintSaveByReturnOrderCheckIDTO idto = new ComplaintSaveByReturnOrderCheckIDTO();
            idto.setComplaintCode(dto.getReturnOrderSeq());
            if (dto.getReturnOrderType() == 1) {
                idto.setComplaintType(1);
                idto.setCheckDate(dto.getDeliveryEndTime());
            } else if (dto.getReturnOrderType() == 2) {
                idto.setComplaintType(0);
                idto.setCheckDate(dto.getCheckTime());
            }
            idto.setComplaintUser(dto.getStoreName());
            idto.setUserId(dto.getStoreId());
            idto.setDeliveryDate(dto.getDeliveryTime());
            idto.setStoreId(dto.getStoreId());
            idto.setStoreCode(dto.getStoreCode());
            idto.setComplaintTotalMoney(dto.getTotalApplyMoney());
            idto.setComplaintRemark(dto.getRemark());
            idto.setLinkmanMobile(dto.getPhone());
            idto.setDeliverymanId(dto.getDriverId());
            idto.setBusinessType(dto.getBusinessType());
            idto.setDeliveryTimeValue(dto.getPickUpTimeRange());
            if (dto.getStatus() == 1) {
                idto.setComplaintHandleStatus(1);
            } else if (dto.getStatus() == 2 || dto.getStatus() == 7) {
                idto.setComplaintHandleStatus(2);
            } else if (dto.getStatus() == 5) {
                idto.setComplaintHandleStatus(3);
            }
            idto.setCheckTotalMoney(Objects.nonNull(dto.getTotalCheckMoney()) ? dto.getTotalCheckMoney().negate() : null);
            idto.setCheckId(dto.getCheckUserId());
            List<ComplaintCommodityListSaveByReturnOrderCheckIDTO> commodityList = new ArrayList<>();
            for (ReturnOrderItemODTO item : dto.getReturnOrderItemList()) {
                ComplaintCommodityListSaveByReturnOrderCheckIDTO req = new ComplaintCommodityListSaveByReturnOrderCheckIDTO();
                if (item.getReturnOrderType() == 1) {
                    req.setComplaintType(2);
                } else if (item.getReturnOrderType() == 2) {
                    req.setComplaintType(1);
                } else if (item.getReturnOrderType() == 3) {
                    req.setComplaintType(3);
                }
                req.setCommodityId(Long.valueOf(item.getCommodityId()));
                req.setCommodityCode(item.getCommodityCode());
                req.setCommodityPrice(item.getCommodityPrice());
                req.setCommodityOrderNumber(item.getCommodityOrderQuantity());
                req.setComplaintNumber(item.getApplyQuantity());
                req.setComplaintMoney(item.getApplyMoney());
                req.setQuestionType(item.getReturnReasonType());
                req.setComplaintContent(item.getComplaintContent());
                req.setUid(String.valueOf(item.getCheckUserId()));
                req.setCheckNumber(item.getCheckQuantity());
                req.setCheckMoney(item.getCheckMoney());
                List<ComplaintCommodityListPicIDTO> picList = new ArrayList<>();
                for (ReturnOrderItemPicODTO orderItemPic : item.getReturnOrderItemPicList()) {
                    ComplaintCommodityListPicIDTO pic = new ComplaintCommodityListPicIDTO();
                    pic.setImgPicUrl(orderItemPic.getImgPicUrl());
                    picList.add(pic);
                }
                req.setPicList(picList);
                commodityList.add(req);
            }
            idto.setCommodityList(commodityList);
            log.info("请求returnOrderCheckSaveComplaint,req:{}", JSON.toJSONString(idto));
            complaintNewClient.returnOrderCheckSaveComplaint(idto);
        } else if (TDaReturnSourceEnum.PX_RETURN.getCode() == dto.getReturnSource()) {
            ComplaintCheckByReturnOrderCheckIDTO idto = new ComplaintCheckByReturnOrderCheckIDTO();
            idto.setComplaintCode(dto.getReturnOrderSeq());
            idto.setCheckTotalMoney(Objects.nonNull(dto.getTotalCheckMoney()) ? dto.getTotalCheckMoney().negate() : null);
            idto.setCheckDate(dto.getCheckTime());
            idto.setCheckRemark(dto.getRemark());
            idto.setCheckId(dto.getCheckUserId());

            List<ComplaintCommodityListCheckByReturnOrderCheckIDTO> commodityList = new ArrayList<>();
            for (ReturnOrderItemODTO item : dto.getReturnOrderItemList()) {
                ComplaintCommodityListCheckByReturnOrderCheckIDTO req = new ComplaintCommodityListCheckByReturnOrderCheckIDTO();
                req.setCommodityId(Long.valueOf(item.getCommodityId()));
                req.setCommodityPrice(item.getCommodityPrice());
                req.setCheckNumber(item.getCheckQuantity());
                req.setCheckMoney(item.getCheckMoney());
                commodityList.add(req);
            }
            idto.setCommodityList(commodityList);
            log.info("请求returnOrderCheckEditComplaint,req:{}", JSON.toJSONString(idto));
            complaintNewClient.returnOrderCheckEditComplaint(idto);
        }
    }


    //=============================================================================================================================================================================================================================================================================================================================================================================================================================================================================================================================================================================================================================================================================================================================================================

    /**
     * 校验审核参数
     */
    private void checkParam(AuditReturnOrderIDTO dto, XdaReturnOrder xdaReturnOrder, List<ReturnOrderItemODTO> returnOrderItemList) {
        QYAssert.isTrue(null != xdaReturnOrder, " 退货单审核-退货单不存在! ");
        if (dto.getAuditResult() == TDaAuditResultEnum.SUCCESS.getCode()) {
            QYAssert.isTrue(CollectionUtils.isNotEmpty(dto.getAuditReturnOrderItems()), " 退货单审核-审核参数不能为空! ");
            //待审核状态的可以进行审核
            QYAssert.isTrue(1 == xdaReturnOrder.getStatus(), " 退货单审核-退货单状态不为待审核! ");
            if (TDaReturnOrderTypeEnum.RETURN.getCode() == xdaReturnOrder.getReturnOrderType()) {
                // 判断pickupTime是否在T+1到T+7范围内
                LocalDate pickupLocalDate = dto.getPickUpTime().toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
                LocalDate tPlusOne = LocalDate.now().plusDays(1);
                LocalDate tPlusSeven = LocalDate.now().plusDays(7);
                //送货日期最多可以选择7天（T+1—T+7）
                QYAssert.isTrue(!pickupLocalDate.isBefore(tPlusOne) && !pickupLocalDate.isAfter(tPlusSeven), " 退货单审核-送货日期最多可以选择7天! ");
                // 判断取货日期 是否正确
                List<TdaDeliveryTimeRangeODTO> timeRangeList = calcPickUpTime(BeanCloneUtils.copyTo(xdaReturnOrder, ReturnOrderODTO.class));
                boolean isValidPickUpTime = timeRangeList.stream()
                        .filter(x -> Objects.equals(x.getDeliveryBatch(), dto.getDeliveryBatch())
                                && Objects.equals(x.getLogisticsCenterId(), dto.getLogisticsCenterId())
                                && Objects.equals(x.getDeliveryTimeRange(), dto.getPickUpTimeRange()))
                        .findAny()
                        .map(y -> y.getPickupTimeList().contains(DateUtil.getDateFormate(dto.getPickUpTime(), "yyyy-MM-dd")))
                        .orElse(false);
                QYAssert.isTrue(isValidPickUpTime, "退货单审核-请重新选择取货日期!");
            }
            QYAssert.isTrue(CollectionUtils.isNotEmpty(returnOrderItemList), " 退货单审核-退货单明细不存在! ");
            Map<Long, ReturnOrderItemODTO> map = returnOrderItemList.stream().collect(Collectors.toMap(ReturnOrderItemODTO::getId, Function.identity()));
            for (AuditReturnOrdeItemIDTO auditReturnOrderItem : dto.getAuditReturnOrderItems()) {
                Integer checkNumber = auditReturnOrderItem.getCheckNumber();
                ReturnOrderItemODTO returnOrderItem = map.get(auditReturnOrderItem.getId());
                QYAssert.isTrue(Objects.nonNull(checkNumber) && checkNumber >= 0 && checkNumber <= returnOrderItem.getApplyNumber(), " 退货单审核-审核份数需小于等于申请份数大于等于0，只能输入整数! ");
            }
        }
        // 校验px投诉单是否为待审核
        if (Objects.equals(xdaReturnOrder.getReturnSource(), TDaReturnSourceEnum.PX_RETURN.getCode())) {
            ComplaintODTO complaint = complaintNewClient.findComplaintInfoByComplaintCode(xdaReturnOrder.getReturnOrderSeq());
            QYAssert.isTrue(null != complaint, " 退货单审核-PC投诉单不存在! ");
            QYAssert.isTrue(Objects.equals(complaint.getComplaintHandleStatus(), 1), " 退货单审核-PC投诉单状态不为待审核! ");
        }
    }

    /**
     * 校验确认参数
     */
    private void checkParam(ConfirmReturnOrderIDTO dto, ReturnOrderODTO xdaReturnOrder, List<XdaReturnOrderItem> xdaReturnOrderItems) {
        QYAssert.isTrue(Objects.nonNull(xdaReturnOrder), " 确认退货单-退货单不能为空! ");
        QYAssert.isTrue(CollectionUtils.isNotEmpty(dto.getConfirmReturnOrderItems()), " 确认退货单-确认明细不能为空! ");
        QYAssert.isTrue(CollectionUtils.isNotEmpty(xdaReturnOrderItems), " 确认退货单-确认明细不能为空! ");
        Map<Long, ConfirmReturnOrdeItemIDTO> idtoMap = dto.getConfirmReturnOrderItems().stream().collect(Collectors.toMap(ConfirmReturnOrdeItemIDTO::getId, Function.identity()));
        xdaReturnOrderItems.forEach(item -> {
            ConfirmReturnOrdeItemIDTO returnOrderItem = idtoMap.get(item.getId());
            QYAssert.isTrue(Objects.nonNull(returnOrderItem), " 确认退货单-确认明细不能为空! ");
            //确认份数 大于等于0小于等于审核份数的整数
            QYAssert.isTrue(returnOrderItem.getConfirmNumber() >= 0 && returnOrderItem.getConfirmNumber() <= item.getApplyNumber(), " 确认份数不能小于0或者大于审核份数! ");
        });
        QYAssert.isTrue(xdaReturnOrder.getStatus() == 4, " 确认退货单-退货单状态不为待确认! ");
    }

    /**
     * 根据客户可下单的最晚时间判断取货时间
     */
    private static LocalDate getPickupTime(String storeEndTime) {
        Date date = DateUtil.parseDate(storeEndTime, "HH:mm");
        //app发起退货
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        int hours = calendar.get(Calendar.HOUR_OF_DAY);
        int minutes = calendar.get(Calendar.MINUTE);
        LocalTime endTime = LocalTime.of(hours, minutes);
        // 获取当前时间
        LocalTime currentTime = LocalTime.now();
        // 判断当前时间是T+1还是T+2
        LocalDate pickupLocalDate;
        if (currentTime.isBefore(endTime)) {
            // T+1
            pickupLocalDate = LocalDate.now().plusDays(1);
        } else {
            // T+2
            pickupLocalDate = LocalDate.now().plusDays(2);
        }
        return pickupLocalDate;
    }

    /**
     * 计算取货时间
     */
    private List<TdaDeliveryTimeRangeODTO> calcPickUpTime(ReturnOrderODTO xdaReturnOrder) {
        // 如果是未审核的，需要计算并展示取货时间段和取货日期
        if (xdaReturnOrder.getStatus() == TDaReturnStatusEnum.PENDING_AUDIT.getCode() &&
                //退货才需要，少货不需要
                TDaReturnOrderTypeEnum.RETURN.getCode() == xdaReturnOrder.getReturnOrderType()) {
            // 判断客户最近的可退货时间
            List<TdaDeliveryTimeRangeODTO> list = tdaOrderService.queryDeliveryTimeRangeListByStoreId(xdaReturnOrder.getStoreId());
            updatePickUpTimeRangeList(list);
            return list;
        }
        return Collections.emptyList();
    }

    private void updatePickUpTimeRangeList(List<TdaDeliveryTimeRangeODTO> list) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        if (CollectionUtils.isNotEmpty(list)) {
            for (int j = 0; j < list.size(); j++) {
                TdaDeliveryTimeRangeODTO dto = list.get(j);
                List<String> pickupTimeList = new ArrayList<>();
                LocalDate pickupTime = getPickupTime(dto.getStoreEndTime());
                for (int i = 0; i < 7; i++) {
                    LocalDate plusDays = LocalDate.now().plusDays(i + 1);
                    if (plusDays.isBefore(pickupTime)) {
                        continue;
                    }
                    pickupTimeList.add(plusDays.format(formatter));
                }
                dto.setPickupTimeList(pickupTimeList);
                dto.setId((long) j + 1);
            }
//            odto.setPickUpTimeRangeList(list);
        }
    }

    public ReturnOrderODTO queryReturnOrderByReturnOrderSeq(String returnOrderSeq) {
        XdaReturnOrder returnOrder = xdaReturnOrderMapper.selectByReturnOrderSeq(returnOrderSeq);
        return Objects.isNull(returnOrder) ? null : BeanCloneUtils.copyTo(returnOrder, ReturnOrderODTO.class);
    }
}