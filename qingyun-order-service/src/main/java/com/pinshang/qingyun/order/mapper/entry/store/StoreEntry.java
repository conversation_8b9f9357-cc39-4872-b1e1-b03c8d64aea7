package com.pinshang.qingyun.order.mapper.entry.store;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Data
public class StoreEntry{
	private Long id;
    /**所属企业ID **/
    private Long enterpriseId;
    /**店铺编号 **/
    private String storeCode;
    /**店铺名称 **/
    private String storeName;
    private String storeShortName; // '店铺短名称',
    /**店铺助记码 **/
    private String storeAid;
    /**旧代码 **/
    private String storeOldCode;
    /**描述 **/
    private String storeDestribe;
    /**店铺类型_id **/
    private Long storeTypeId;
    /**店铺等级_id **/
    private Long storeLevelId;
    /**商铺类别_id **/
    private Long storeCategoryId;
    /**状态
     (0启用,1停用) **/
    private Integer storeStatus;
    /**开店时间 **/
    private Date storeOpenTime;
    /**关店时间 **/
    private Date storeCloseTime;
    /**所属公司ID **/
    private Long storeCompanyId;
    /**所属区域ID **/
    private Long storeDistrictId;
    /**结账客户ID **/
    private Long settlementCustomerId;

    /**渠道ID **/
    private Long storeChannelId;
    /**线路ID **/
    private Long storeLineId;
    
    /**下浮率（折扣率） **/
    private BigDecimal lowerRate;
    /**是否预收 **/
    private Integer storeIsPrepay;
    /**是否开票 **/
    private Integer storeIsBilling;
    /**送货员 **/
    private Long deliverymanId;
 
    /**销售员 **/
    private Long salesmanId;
 
    /**督导ID **/
    private Long supervisorId;
 
    /**办事处主任ID **/
    private Long officeDirectorId;
 
    /**押金 **/
    private BigDecimal deposit;
    /**大区经理ID **/
    private Long regionManagerId;
 
    /**是否提醒 **/
    private Integer storeIsAlert;
    /**订货结束是否短信提示 **/
    private Integer orderCloseIsAlert;
    /**订货方式 **/
    private String orderMethod;
    /**第一次提醒司机 **/
    private String firstAlertTime;
    /**提醒时间间隔 **/
    private String alertInterval;
    /**提醒次数 **/
    private Integer alertTimes;
    /**停止手机订货时间
     (控制手机app的停止订货时间) **/
    private String stopMonileTime;
    /**网上订货时间 **/
    private String netOrderTime;
    /**电话停止订货
     时间 **/
    private String telStopOrder;
    /**未订货备注 **/
    private String notOrderRemark;
    /**打印份数
     (客户要求的小单子份数) **/
    private BigDecimal printCopies;
    /**打印备注 **/
    private String printBackup;
    /**打印送货单标题 **/
    private String printOrderTitle;
    /**是否显示单价 **/
    private Integer showPriceRetail;
    /**是否显示金额 **/
    private Integer moneyIsShow;
    /**是否送货单加上签字 **/
    private Integer orderSignIn;
    /**当日手机最大订单量 **/
    private BigDecimal maxMobileOrder;
    /**当日订单最大订单量 **/
    private BigDecimal maxOrder;
    /**国家ID **/
    private Long countryId;
    /**省ID **/
    private Long provinceId;
    /**市ID **/
    private Long cityId;
    /**区ID **/
    private Long areaId;
    /**详细地址**/
    private String detailAddress;
    /**商圈ID **/
    private Long businessCirclesId;
    /**门店地址 **/
    private String storeAddress;
    /**送货地址 **/
    private String deliveryAddress;
    /**联系人 **/
    private String storeLinkman;
    /**电话 **/
    private String linkmanTel;
    /**手机 **/
    private String linkmanMobile;
    /**传真 **/
    private String linkmanFax;
    /**邮箱 **/
    private String linkmanEmail;
    /**收货时间 **/
    private String receiveTime;
    /**检验报告 **/
    private Integer isTestReport;
    /**检验报告备注 **/
    private String testReportRemark;
    /**是否领用耗材 **/
    private Integer isReceiveConsumables;
    /**金蝶接口 **/
    private String kingdeeInterface;
    /**是否速冻 **/
    private Integer isQuickFreeze;
    /**速冻数量 **/
    private BigDecimal quickFreezeAmount;
    /**打印批次_id **/
    private Long printDeliveryBatch;
 
    /**结账备注 **/
    private String settlementRemark;
    /**顺序 **/
    private Integer printDeliveryQueue;

    /** 创建该客户的用户ID **/
    private Long createUserId;
    /** 是否已开启结账方式 默认为启用-1 其余0 */
    private Long settlementStatus;
    

    private String storeTypeName;
    private String storeDistrictName;
    /** 结账客户名称 **/
    private String settlementCustomerName;
    /**线路名称 **/
    private String storeLineCode;
    private String storeLineName;
    private String deliverymanName;

    private String supervisorName;
    private String officeDirector;
    private String regionManager;
    
    /**公司**/
    private String storeCompanyCode;
    
    //销售员名字
    private String storeSalesName;
    //大区经理名字
    private String regionmanagerName;
    //主任名字
    private String officeDirectorName;
    /**区域**/
    private String storeSortCode;

    /**客户编码-名称**/
    private String storeCodeAndName;
    private String idStr;

    /**客户关联门店信息**/
    private Long shopId;
    private String shopCode;

    private Integer collectStatus;// 是否预收（0：为非预收，1：为预收）
    private Integer storeType;//客户类型1-内部，2-外部',
    private Integer businessType;//业务类型：10-通达销售
    private String optionCode;//store_type_id对应字典表(id)中的option_code
}