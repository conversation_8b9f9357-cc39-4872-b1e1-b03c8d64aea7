package com.pinshang.qingyun.order.mapper;

import com.pinshang.qingyun.base.mybatis.MyMapper;
import com.pinshang.qingyun.order.model.cup.OrderWhite;
import com.pinshang.qingyun.order.model.promotionStk.PromotionStk;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * @Author: sk
 * @Date: 2024/7/18
 */
@Repository
public interface PromotionStkMapper extends MyMapper<PromotionStk> {

    List<PromotionStk> findDistributionByStoreId(@Param("storeId")Long storeId, @Param("orderTime")String orderTime);
}
