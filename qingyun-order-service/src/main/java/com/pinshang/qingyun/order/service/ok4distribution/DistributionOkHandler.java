package com.pinshang.qingyun.order.service.ok4distribution;

import com.google.common.util.concurrent.ThreadFactoryBuilder;
import com.pinshang.qingyun.base.constant.QingyunConstant;
import com.pinshang.qingyun.base.constant.ShopStockTypeConstant;
import com.pinshang.qingyun.order.mapper.entry.shop.SubOrder4DistributionEntry;
import com.pinshang.qingyun.order.mapper.entry.shop.SubOrderItem4DistributionEntry;
import com.pinshang.qingyun.order.service.ShopReceiveService;
import com.pinshang.qingyun.order.vo.shop.Quantity;
import com.pinshang.qingyun.order.vo.shop.ReceiveOrderVo;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.ScheduledThreadPoolExecutor;
import java.util.concurrent.ThreadFactory;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.locks.Lock;
import java.util.concurrent.locks.ReentrantLock;

/**
 *
 * <AUTHOR>
 * @Date 2018/4/16 18:25
 */
@Component
@Slf4j
public class DistributionOkHandler implements DistributionOkProcessor{
    @Resource
    private RedissonClient redissonClient;
    @Autowired
    private ShopReceiveService shopReceiveService;

    private ScheduledThreadPoolExecutor THREAD_POOL;

    private String cacheKey =  QingyunConstant.UNDERLINE_SPLIT+ShopStockTypeConstant.SHOP_ORDER_RECIEVE;
    private Map<String,Job> jobs = new LinkedHashMap<>(8,0.75f,true);
    private final Lock jobHelper = new ReentrantLock();
    private static final int  MAX_RETRY = 3;
    private static final long POOL_INTERVAL_MILLISECONDS = 3000;
    private static final int PER_MAX_EXEC_JOBS = 100;

    @PostConstruct
    public void init(){
        ThreadFactory namedThreadFactory = new ThreadFactoryBuilder()
                    .setNameFormat("distributionOk-Pool").build();
        THREAD_POOL = new ScheduledThreadPoolExecutor(Runtime.getRuntime().availableProcessors(),namedThreadFactory);
        THREAD_POOL.scheduleWithFixedDelay(()->{
            if(jobs.size() == 0){
                return;
            }
            Iterator<Map.Entry<String, Job>> iterator = jobs.entrySet().iterator();
            int jobCount = 0;
            while (iterator.hasNext() && jobCount <= PER_MAX_EXEC_JOBS){
                Job job = iterator.next().getValue();
                if(!job.ableRun()){
                    doGet(job);
                    continue;
                }
                ReceiveOrderVo v = job.getV();
                try {
                    go(v);
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    log.error(throwable.getMessage(),throwable.getStackTrace());
                    log.error("==========>自动辅助收货重试失败subOrderId:"+v.getSubOrderId()+"排入队尾等待下一轮重试.");
                    doGet(job.refresh());
                }
                remove(v);
                jobCount++;
            }
        },10000,POOL_INTERVAL_MILLISECONDS,TimeUnit.MILLISECONDS);
    }



    @Override
    public void handle(String distributionCode,Long distributionUserId,List<Long> subOrderIds) {
        log.info("==========>开始处理自动辅助收货,distributionCode:"+distributionCode);
        List<SubOrder4DistributionEntry> preData = shopReceiveService.autoReceiveByDirstributionOk(subOrderIds);
        if(preData == null || preData.isEmpty()){
            return;
        }
        List<ReceiveOrderVo> params = buildParams(preData,distributionUserId);
        for (ReceiveOrderVo v : params) {
            THREAD_POOL.submit(()->{
                try {
                    go(v);
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    log.error(throwable.getMessage(),throwable);
                    log.error("==========>自动辅助收货失败subOrderId:"+v.getSubOrderId()+" 加入重试队列.");
                    putRetry(v);
                }
            });
        }
    }

    private void go(ReceiveOrderVo v) throws Throwable {
        final RLock lock = redissonClient.getLock(v.getSubOrderId()+cacheKey);
        lock.lock(ShopStockTypeConstant.EXPIRE_TIME, TimeUnit.SECONDS);
        try {
            shopReceiveService.addReceive(v);
        } finally {
            lock.unlock();
        }
    }

    private List<ReceiveOrderVo> buildParams(List<SubOrder4DistributionEntry> preData,Long userId){
        List<ReceiveOrderVo> params = new ArrayList<>(preData.size());
        Date now = new Date();
        String remark = "配送完成辅助自动收货";
        ReceiveOrderVo vo;
        for (SubOrder4DistributionEntry sb : preData) {
            vo = new ReceiveOrderVo();
            vo.setSubOrderId(String.valueOf(sb.getId()));
            vo.setLogisticsModel(sb.getLogisticsModel());
            vo.setStoreId(String.valueOf(sb.getStoreId()));
            vo.setReceiveId(String.valueOf(userId));
            vo.setReceiveTime(now);
            vo.setRemark(remark);
            vo.setShopId(sb.getShopId());
            List<SubOrderItem4DistributionEntry> itemList = sb.getItemList();
            if(itemList !=null && !itemList.isEmpty()){
                Quantity q;
                List<Quantity> qs = new ArrayList<>(itemList.size());
                for (SubOrderItem4DistributionEntry item : itemList) {
                    q = new Quantity();
                    q.setCommodityId(String.valueOf(item.getCommodityId()));
                    q.setRealDeliveryQuantity(item.getQuantity());
                    q.setRealReceiveQuantity(item.getQuantity());
                    q.setRequireQuantity(item.getRequireQuantity());
                    q.setPrice(item.getPrice());
                    qs.add(q);
                }
                vo.setReceiveQuantities(qs);
            }
            params.add(vo);
        }
        return params;
    }

    private void putRetry(ReceiveOrderVo vo){
        final Lock l = jobHelper;
        jobHelper.lock();
        try {
            jobs.put(vo.getSubOrderId(),build(vo));
        } finally {
            l.unlock();
        }
    }

    private void remove(ReceiveOrderVo vo){
        final Lock l = jobHelper;
        jobHelper.lock();
        try {
            jobs.remove(vo.getSubOrderId());
        } finally {
            l.unlock();
        }
    }

    private void doGet(Job j){
        if(j.getTime() >=MAX_RETRY){
            log.error("=========>job beyond retry max times:"+MAX_RETRY+", sink! suborderId:"+j.getV().getSubOrderId());
            remove(j.getV());
            return;
        }
        final Lock l = jobHelper;
        jobHelper.lock();
        try {
            j.setTime(j.getTime()+1);
            jobs.get(j.getV().getSubOrderId());
        } finally {
            l.unlock();
        }
    }

    private Job build(ReceiveOrderVo v){
        Job j = new Job();
        j.setV(v);
        j.setTime(1);
        j.setTimestamp(System.currentTimeMillis());
        j.setEpoch(1);
        return j;
    }

    class Job{
        private ReceiveOrderVo v;
        private int time;
        private long timestamp;
        private int epoch;

        public ReceiveOrderVo getV() {
            return v;
        }

        public void setV(ReceiveOrderVo v) {
            this.v = v;
        }

        public int getTime() {
            return time;
        }

        public void setTime(int time) {
            this.time = time;
        }

        public long getTimestamp() {
            return timestamp;
        }

        public void setTimestamp(long timestamp) {
            this.timestamp = timestamp;
        }

        public int getEpoch() {
            return epoch;
        }

        public void setEpoch(int epoch) {
            this.epoch = epoch;
        }

        public boolean ableRun(){
            return this.timestamp -System.currentTimeMillis() <=0;
        }

        public Job refresh(){
            this.epoch = epoch <<1;
            this.timestamp = this.timestamp + epoch * POOL_INTERVAL_MILLISECONDS;
            return this;
        }
    }

}
