package com.pinshang.qingyun.order.mapper;

import com.pinshang.qingyun.base.mybatis.MyMapper;
import com.pinshang.qingyun.order.model.cup.OrderWhite;
import com.pinshang.qingyun.order.model.gift.GiftModel;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * @Author: sk
 * @Date: 2024/7/18
 */
@Repository
public interface GiftModelMapper extends MyMapper<GiftModel> {

    List<GiftModel> findGiftModelByStoreId(@Param("storeId")Long storeId, @Param("orderTime")String orderTime);
}
