package com.pinshang.qingyun.order.listener;

import com.ctrip.framework.apollo.model.ConfigChangeEvent;
import com.ctrip.framework.apollo.spring.annotation.ApolloConfigChangeListener;
import com.pinshang.qingyun.base.spring.SpringBeanFinder;
import com.pinshang.qingyun.order.constant.ThreadPoolBeanConstants;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;

/**
 * @ClassName ApolloConfigChangeListener
 */
@Component
@Slf4j
public class ApolloConfigListener {

    @ApolloConfigChangeListener
    public void onChange(ConfigChangeEvent changeEvent) {
        log.info("线程池参数配置发生变化,namespace:{}", changeEvent.getNamespace());

        boolean corePoolSizeChanged = changeEvent.isChanged("threadPool.coreSize");
        boolean maxPoolSizeChanged = changeEvent.isChanged("threadPool.maxSize");

        ThreadPoolTaskExecutor executor = (ThreadPoolTaskExecutor)SpringBeanFinder.getBean(ThreadPoolBeanConstants.ORDER_THREADPOOL);
        if(null == executor){
            return;
        }

        if (corePoolSizeChanged) {
            executor.setCorePoolSize(Integer.valueOf(changeEvent.getChange("threadPool.coreSize").getNewValue()));
            log.info("线程池核心线程修改:{}", executor.getCorePoolSize());
        }

        if(maxPoolSizeChanged){
            executor.setMaxPoolSize(Integer.valueOf(changeEvent.getChange("threadPool.maxSize").getNewValue()));
            log.info("线程池最大线程修改:{}", executor.getMaxPoolSize());
        }

    }
}
