package com.pinshang.qingyun.order.mapper;

import com.pinshang.qingyun.order.model.order.DeliveryTime;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Mapper
@Repository
public interface SubOrder2DeliveryOrderMapper {

	@Deprecated
	List<Long> listCurrentDayOrderId(@Param("deliveryTime") DeliveryTime deliveryTime);

	List<Long> listCurrentDayOrderIdV2(@Param("deliveryTime") DeliveryTime deliveryTime,
									   @Param("businessType") Integer businessType,
									   @Param("directStoreTypeList") List<Long> directStoreTypeList,
									   @Param("tobStoreTypeList") List<Long> tobStoreTypeList,
									   @Param("tobWarehouseList") List<Long> tobWarehouseList);

}
