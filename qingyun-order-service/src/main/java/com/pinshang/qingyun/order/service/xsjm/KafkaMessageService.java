package com.pinshang.qingyun.order.service.xsjm;

import com.pinshang.qingyun.base.api.QYApplicationContext;
import com.pinshang.qingyun.box.utils.ListUtil;
import com.pinshang.qingyun.box.utils.SpringUtil;
import com.pinshang.qingyun.infrastructure.components.IMqSenderComponent;
import com.pinshang.qingyun.infrastructure.components.params.mq.MqMessage;
import com.pinshang.qingyun.kafka.enums.KafkaMessageOperationTypeEnum;
import com.pinshang.qingyun.kafka.enums.KafkaMessageTypeEnum;
import com.pinshang.qingyun.kafka.enums.KafkaTopicEnum;
import com.pinshang.qingyun.order.vo.kafka.XjShopCommodityStockKafkaVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
@Slf4j
public class KafkaMessageService {


	@Autowired
	private IMqSenderComponent mqSenderComponent;


	public void sendUpdateXjStock(XjShopCommodityStockKafkaVo kafkaVo){
    	if(kafkaVo==null || SpringUtil.isEmpty(kafkaVo.getCommodityList())){
    		return;
		}

		List<List<XjShopCommodityStockKafkaVo.XjShopCommodityStockItemVo>> subList = ListUtil.splitSubList(kafkaVo.getCommodityList(),500);
    	subList.forEach(itemList->{
			XjShopCommodityStockKafkaVo subKafkaVo = new XjShopCommodityStockKafkaVo();
			SpringUtil.copyProperties(kafkaVo,subKafkaVo);
			subKafkaVo.setCommodityList(itemList);
			mqSenderComponent.send(QYApplicationContext.applicationNameSwitch+ KafkaTopicEnum.SHOP_STOCK_CHANGE_TOPIC.getTopic(), subKafkaVo,
					MqMessage.MQ_KAFKA, KafkaMessageTypeEnum.SHOP_STOCK_CHANGE.name(),
					KafkaMessageOperationTypeEnum.COPY.name());
    	});
	}

}
