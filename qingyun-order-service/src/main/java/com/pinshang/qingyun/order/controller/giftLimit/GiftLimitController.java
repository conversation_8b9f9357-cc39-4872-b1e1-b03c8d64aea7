package com.pinshang.qingyun.order.controller.giftLimit;/**
 * @Author: sk
 * @Date: 2025/4/17
 */

import com.pinshang.qingyun.order.dto.giftLimit.GiftLeftQuantityODTO;
import com.pinshang.qingyun.order.dto.giftLimit.GiftLimitQuantityQueryIDTO;
import com.pinshang.qingyun.order.dto.giftLimit.GiftLimitQuantitySaveIDTO;
import com.pinshang.qingyun.order.service.giftLimit.GiftLimitService;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025年04月17日 下午12:44
 */
@RestController
@RequestMapping("/giftLimit")
public class GiftLimitController {

    @Autowired
    private GiftLimitService giftLimitService;


    @ApiOperation(value = "查询赠品总限量剩余数量")
    @PostMapping("/queryGiftLeftQuantityList")
    public List<GiftLeftQuantityODTO> queryGiftLeftQuantityList(@RequestBody GiftLimitQuantityQueryIDTO queryIdto){
        return giftLimitService.queryGiftLeftQuantityList(queryIdto);
    }


    @ApiOperation(value = "新增修改订单维护已赠数量")
    @PostMapping("/saveGiftLimitQuantity")
    public Boolean saveGiftLimitQuantity(@RequestBody List<GiftLimitQuantitySaveIDTO> saveList){
        return giftLimitService.saveGiftLimitQuantity(saveList);
    }


    @ApiOperation(value = "订单取消回退已经赠送数量")
    @PostMapping("/deleteGiftLimitQuantity")
    public Boolean deleteGiftLimitQuantity(@RequestBody List<Long> orderIdList){
        return giftLimitService.deleteGiftLimitQuantity(orderIdList);
    }
}
