package com.pinshang.qingyun.order.mapper.entry;

import lombok.Data;

import java.util.Date;

/**
 * Created by chenjx on 2017/8/18.
 */
@Data
public class ShopEntry {

    private Long id;
    private String shopName;
    private String shopShortName;
    private String shopCode;
    private String shopAid;
    private Byte status;
    private String statusName;
    private Byte shopStatus;
    private String shopStatusName;
    private Long enterpriseId;
    /**门店类型:1-门店，2-鲜食店**/
    private Integer shopType;
    private String shopTypeName;
    /**关联客户id**/
    private Long storeId;
    /**关联客户**/
    private String storeName;
    private String storeCode;
    /**门店活动访问URL**/
    private String shopActitityUrl;
    /**创建时间**/
    private Date  createTime;
    private Long h5TemplateId;

}
