package com.pinshang.qingyun.order.service.impl;

import com.pinshang.qingyun.order.bo.ExportLastMonthLineGroupBO;
import com.pinshang.qingyun.order.mapper.OrderMapper;
import com.pinshang.qingyun.order.service.CompanyProductOrderReportService;
import com.pinshang.qingyun.order.util.TimeUtil;
import com.pinshang.qingyun.order.vo.order.ExportLastMonthLineGroupReqVO;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.Month;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoField;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/05/18 10:39
 */
@Service
public class CompanyProductOrderReportServiceImpl implements CompanyProductOrderReportService {

    @Autowired
    private OrderMapper orderMapper;

    /**
     * 查询上月线路组汇总报表
     *
     * @param vo vo
     * @return list
     */
    @Override
    public List<ExportLastMonthLineGroupBO> getList(ExportLastMonthLineGroupReqVO vo) {
        int monthCount;
        if (StringUtils.isBlank(vo.getStartOrderTime()) && StringUtils.isBlank(vo.getEndOrderTime())){
            LocalDateTime date = LocalDateTime.now();
            Month month = date.getMonth();
            int lastMonth = month.get(ChronoField.MONTH_OF_YEAR) - 1;
            monthCount = lastMonth < 1 ? 12 : lastMonth;
            date = date.minusMonths(1);
            vo.setStartOrderTime(TimeUtil.getFirstDayOfMonthStr(date, TimeUtil.DATE_PATTERN));
            vo.setEndOrderTime(TimeUtil.getLastDayOfMonthStr(date, TimeUtil.DATE_PATTERN));
        }else {
            LocalDateTime date = TimeUtil.parseLocalDateTime(vo.getStartOrderTime() + " 00:00:00", TimeUtil.DATETIME_PATTERN);
            Month month = date.getMonth();
            monthCount = month.get(ChronoField.MONTH_OF_YEAR);
            vo.setStartOrderTime(TimeUtil.getFirstDayOfMonthStr(date, TimeUtil.DATE_PATTERN));
            vo.setEndOrderTime(TimeUtil.getLastDayOfMonthStr(date, TimeUtil.DATE_PATTERN));
        }
        return orderMapper.getExportLastMonthLineGroupList(vo.getStartOrderTime(), vo.getEndOrderTime(), monthCount + "月");
    }

}
