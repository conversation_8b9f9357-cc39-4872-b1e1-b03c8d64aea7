package com.pinshang.qingyun.order.mapper.entry.order;

import java.math.BigDecimal;

/*
 * 拆单明细列表
 */
public class SplitOrderListEntry {
	//商品id
	private Long commodityId;
	//物流模式
	private Integer logisticsModel;
	//仓库id
	private Long warehouseId;
	//供应商id
	private Long supplierId;
	// 配送日期范围
	private String deleveryTimeRange;
	//数量
	private BigDecimal quantity;
	//单价
	private BigDecimal price;
	private BigDecimal totalPrice;
	public Long getCommodityId() {
		return commodityId;
	}
	public void setCommodityId(Long commodityId) {
		this.commodityId = commodityId;
	}
	public Integer getLogisticsModel() {
		return logisticsModel;
	}
	public void setLogisticsModel(Integer logisticsModel) {
		this.logisticsModel = logisticsModel;
	}
	public Long getWarehouseId() {
		return warehouseId;
	}
	public void setWarehouseId(Long warehouseId) {
		this.warehouseId = warehouseId;
	}
	public Long getSupplierId() {
		return supplierId;
	}
	public void setSupplierId(Long supplierId) {
		this.supplierId = supplierId;
	}
	public BigDecimal getQuantity() {
		return quantity;
	}
	public void setQuantity(BigDecimal quantity) {
		this.quantity = quantity;
	}
	public BigDecimal getPrice() {
		return price;
	}
	public void setPrice(BigDecimal price) {
		this.price = price;
	}
	public BigDecimal getTotalPrice() {
		return totalPrice;
	}
	public void setTotalPrice(BigDecimal totalPrice) {
		this.totalPrice = totalPrice;
	}

	public String getDeleveryTimeRange() {
		return deleveryTimeRange;
	}

	public void setDeleveryTimeRange(String deleveryTimeRange) {
		this.deleveryTimeRange = deleveryTimeRange;
	}
}
