package com.pinshang.qingyun.order.service;

import com.pinshang.qingyun.order.mapper.ShoppingCartMapper;
import com.pinshang.qingyun.order.mapper.XdShoppingCartTempMapper;
import com.pinshang.qingyun.order.model.order.XdShoppingCartTemp;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import tk.mybatis.mapper.entity.Example;

import java.util.List;

/**
 * @Author: sk
 * @Date: 2019/11/28
 */

@Slf4j
@Service
public class XDShoppingCartDeleteService {

    @Autowired
    private XdShoppingCartTempMapper xdShoppingCartTempMapper;

    @Autowired
    private ShoppingCartMapper shoppingCartMapper;

    /**
     * 删除临时购物车
     */
    @Transactional(rollbackFor = Exception.class)
    public void deleteShoppingCartTemp(){
        // 先删除临时购物车
        Example cartTempEx = new Example(XdShoppingCartTemp.class);
        xdShoppingCartTempMapper.deleteByExample(cartTempEx);
    }

    /**
     * 删除购物车
     */
    @Transactional(rollbackFor = Exception.class)
    public void deleteShoppingCart(){
        // 先删除鲜道所有购物车
        //1,查询要删除的购物车ids
        List<Long> shoppingCartIdList = shoppingCartMapper.selectShpppingCartIdList();
        if(!CollectionUtils.isEmpty(shoppingCartIdList)){
            shoppingCartMapper.deleteXDShoppingCartItem(shoppingCartIdList);
            shoppingCartMapper.deleteXdShoppingCart(shoppingCartIdList);
        }
    }
}
