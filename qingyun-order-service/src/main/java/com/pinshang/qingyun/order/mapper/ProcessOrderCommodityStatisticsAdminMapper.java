package com.pinshang.qingyun.order.mapper;

import com.pinshang.qingyun.base.mybatis.MyMapper;
import com.pinshang.qingyun.order.dto.tob.ToBOrderStatisticsRepairIDTO;
import com.pinshang.qingyun.order.dto.tob.ToBQueryOrderStatisticsIDTO;
import com.pinshang.qingyun.order.dto.tob.ToBQueryOrderStatisticsODTO;
import com.pinshang.qingyun.order.model.tob.ToBProcessOrderCommodityStatistics;
import com.pinshang.qingyun.order.vo.tob.AdminToBOrderStatisticReqVo;
import com.pinshang.qingyun.order.vo.tob.AdminToBOrderStatisticRespVo;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * @Description：
 * @Author：ZhangHui
 * @Package：com.pinshang.qingyun.order.mapper
 * @Date: 2024/4/7
 */
@Repository
public interface ProcessOrderCommodityStatisticsAdminMapper extends MyMapper<ToBProcessOrderCommodityStatistics> {


    /**
     * b端下单统计分页查询
     * @param reqVo
     */
    List<AdminToBOrderStatisticRespVo> queryToBOrderStatisticPage(AdminToBOrderStatisticReqVo reqVo);

    /**
     * 批量更新
     * @param updateList
     */
    void batchUpdate(@Param("tobOrderStatisticsList") List<ToBOrderStatisticsRepairIDTO> updateList);

    /**
     * 批量新增
     * @param insertList
     */
    void batchInsert(List<ToBOrderStatisticsRepairIDTO> insertList);

    /**
     * 查询数据
     * @param statisticsIDTO
     * @return
     */
    List<ToBQueryOrderStatisticsODTO> queryOrderStatisticsList(ToBQueryOrderStatisticsIDTO statisticsIDTO);
}
