package com.pinshang.qingyun.order.controller.xda;

import com.pinshang.qingyun.base.api.XdaTokenInfo;
import com.pinshang.qingyun.base.configure.expand.QYAssert;
import com.pinshang.qingyun.base.util.FastThreadLocalUtil;
import com.pinshang.qingyun.order.config.LockConstants;
import com.pinshang.qingyun.order.dto.xda.QueryXdaComplaintDTO;
import com.pinshang.qingyun.order.dto.xda.XdaComplaintCommodityDTO;
import com.pinshang.qingyun.order.dto.xda.XdaComplaintOrderCancelDTO;
import com.pinshang.qingyun.order.dto.xda.XdaComplaintOrderODTO;
import com.pinshang.qingyun.order.dto.xda.v4.TdaDeliveryTimeRangeODTO;
import com.pinshang.qingyun.order.enums.ComplaintTypeEnum;
import com.pinshang.qingyun.order.service.xda.XdaComplaintOrderService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.text.ParseException;
import java.util.List;

/**
 * <AUTHOR>
 * @Version 0.1.0
 * @Date 2020/12/30 14:24
 * @Copyright © 2019-2020 qm
 * @Description -- 类说明 鲜达投诉单
 **/
@RestController
@RequestMapping("/xda/complaint")
@Api(value = "鲜达投诉单相关api", tags = "xda-complaint", description = "鲜达投诉单相关接口")
public class XdaComplaintOrderController {

    @Autowired
    private XdaComplaintOrderService xdaComplaintOrderService;
    @Autowired
    private RedissonClient redissonClient;

    /***
     * 客户投诉单列表
     * @return
     */
    @ApiOperation(value = "客户投诉单列表", notes = "客户投诉单列表")
    @PostMapping("/queryComplaintOrderList")
    public List<XdaComplaintOrderODTO> queryComplaintOrderList(@RequestBody QueryXdaComplaintDTO dto){
        XdaTokenInfo xdaTokenInfo = FastThreadLocalUtil.getXDA();
        QYAssert.isTrue(!(xdaTokenInfo == null || xdaTokenInfo.getStoreId() == null), "客户未登录！");
        dto.setStoreId(xdaTokenInfo.getStoreId());
        return xdaComplaintOrderService.queryComplaintOrderList(dto);
    }



    /***
     * 客户投诉单取消
     * @return
     */
    @ApiOperation(value = "取消投诉单", notes = "取消修改投诉单-操作类型，true-取消投诉单下的所有投诉商品，false-取消指定的投诉商品")
    @PostMapping("/cancelComplaintOrder")
    public Boolean cancelComplaintOrder(@RequestBody XdaComplaintOrderCancelDTO dto){
        XdaTokenInfo xdaTokenInfo = FastThreadLocalUtil.getXDA();
        QYAssert.isTrue(!(xdaTokenInfo == null || xdaTokenInfo.getStoreId() == null), "客户未登录！");
        dto.setStoreId(xdaTokenInfo.getStoreId());

        String lockKey = LockConstants.generateXdaComplaintLockKey(dto.getStoreId());
        RLock lock = redissonClient.getLock(lockKey);
        if(lock.tryLock()){
            try {
                return xdaComplaintOrderService.cancelComplaintOrder(dto);
            } finally {
                lock.unlock();
            }
        }else {
            QYAssert.isFalse("系统繁忙,请勿频繁操作!");
        }
        return Boolean.FALSE;
    }


    /***
     * 客户投诉单修改取消
     * @return
     */
    @ApiOperation(value = "投诉单取消修改", notes = "取消修改投诉单-操作类型，0-取消投诉单下的所有投诉商品，1-取消指定的投诉商品，2-修改指定的投诉商品")
    @PostMapping("/operateComplaintOrder")
    public Boolean operateComplaintOrder(@RequestBody XdaComplaintOrderODTO dto){
        XdaTokenInfo xdaTokenInfo = FastThreadLocalUtil.getXDA();
        QYAssert.isTrue(!(xdaTokenInfo == null || xdaTokenInfo.getStoreId() == null), "客户未登录！");
        dto.setStoreId(xdaTokenInfo.getStoreId());

        String lockKey = LockConstants.generateXdaComplaintLockKey(dto.getStoreId());
        RLock lock = redissonClient.getLock(lockKey);
        if(lock.tryLock()){
            try {
                return xdaComplaintOrderService.operateComplaintOrder(dto);
            } finally {
                lock.unlock();
            }
        }else {
            QYAssert.isFalse("系统繁忙,请勿频繁操作!");
        }
        return Boolean.FALSE;
    }

    /***
     * 客户当天可投诉的商品列表
     * @return
     */
    @ApiOperation(value = "客户可投诉的商品列表, 投诉类型 0-差异投诉，1-退货投诉", notes = "可投诉的商品列表")
    @GetMapping("/queryComplaintCommodityList/{complaintType}")
    public XdaComplaintCommodityDTO queryComplaintCommodityList(@PathVariable("complaintType") Integer complaintType,
                                                                @RequestParam(value = "deliveryDate", required = false) String deliveryDate) {
        XdaTokenInfo xdaTokenInfo = FastThreadLocalUtil.getXDA();
        QYAssert.isTrue(!(xdaTokenInfo == null || xdaTokenInfo.getStoreId() == null), "客户未登录！");
        QYAssert.notNull(complaintType, "投诉类型不能为空！");
        QYAssert.isTrue(complaintType == 0 || complaintType == 1, "不支持此种投诉类型！");
        XdaComplaintCommodityDTO xdaComplaintCommodityDTO = new XdaComplaintCommodityDTO();
        xdaComplaintCommodityDTO.setStoreId(xdaTokenInfo.getStoreId());
        xdaComplaintCommodityDTO.setComplaintType(ComplaintTypeEnum.getByCode(complaintType));
        xdaComplaintCommodityDTO.setDeliveryDate(deliveryDate);
        return xdaComplaintOrderService.queryComplaintCommodityList(xdaComplaintCommodityDTO);
    }

    /***
     * 客户投诉单提交
     * @return
     */
    @ApiOperation(value = "提交投诉单", notes = "投诉单提交")
    @PostMapping("/saveComplaintCommodity")
    public Boolean saveComplaintCommodity(@RequestBody XdaComplaintCommodityDTO xdaComplaintCommodityDTO) {
        XdaTokenInfo xdaTokenInfo = FastThreadLocalUtil.getXDA();
        QYAssert.isTrue(!(xdaTokenInfo == null || xdaTokenInfo.getStoreId() == null), "客户未登录！");
        xdaComplaintCommodityDTO.setStoreId(xdaTokenInfo.getStoreId());
        xdaComplaintCommodityDTO.setStoreCode(xdaTokenInfo.getStoreCode());

        String lockKey = LockConstants.generateXdaComplaintLockKey(xdaComplaintCommodityDTO.getStoreId());
        RLock lock = redissonClient.getLock(lockKey);
        if (lock.tryLock()) {
            try {
                return xdaComplaintOrderService.saveComplaintCommodity(xdaComplaintCommodityDTO);
            } finally {
                lock.unlock();
            }
        }else {
            QYAssert.isFalse("系统繁忙,请勿频繁操作!");
        }
        return Boolean.FALSE;
    }

    @ApiOperation(value = "投诉选择送货日期", notes = "投诉选择送货日期")
    @RequestMapping(value = "/selectComplaintDeliveryDate",method = RequestMethod.GET)
    public List<String> selectComplaintDeliveryDate(){
        return xdaComplaintOrderService.selectComplaintDeliveryDate();
    }

    @ApiOperation(value = "投诉选择送货时间段", notes = "投诉选择送货时间段")
    @RequestMapping(value = "/selectComplaintDeliveryDateRange",method = RequestMethod.GET)
    public List<TdaDeliveryTimeRangeODTO> selectComplaintDeliveryDateRange(){
        XdaTokenInfo ti = FastThreadLocalUtil.getXDA();
        return xdaComplaintOrderService.selectComplaintDeliveryDateRange(ti.getStoreId());
    }

}
