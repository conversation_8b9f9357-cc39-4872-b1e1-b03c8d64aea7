package com.pinshang.qingyun.order.service;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.pinshang.qingyun.base.configure.expand.QYAssert;
import com.pinshang.qingyun.base.util.FastThreadLocalUtil;
import com.pinshang.qingyun.box.utils.DateUtil;
import com.pinshang.qingyun.box.utils.ListToPageInfoUtil;
import com.pinshang.qingyun.box.utils.SpringUtil;
import com.pinshang.qingyun.box.utils.StringUtil;
import com.pinshang.qingyun.order.dto.*;
import com.pinshang.qingyun.order.mapper.CommodityMapper;
import com.pinshang.qingyun.order.mapper.OrderMapper;
import com.pinshang.qingyun.order.mapper.SubOrderCountMapper;
import com.pinshang.qingyun.order.model.commodity.Commodity;
import com.pinshang.qingyun.shop.dto.ShopDto;
import com.pinshang.qingyun.shop.service.ShopClient;
import com.pinshang.qingyun.smm.dto.user.SelectUserShopIdListIDTO;
import com.pinshang.qingyun.smm.service.SMMUserClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @ClassName ShopCountStockService
 * <AUTHOR>
 * @Date 2022/12/8 18:14
 * @Description ShopCountStockService
 * @Version 1.0
 */
@Service
public class ShopCountStockService {
    @Autowired
    private OrderMapper orderMapper;
    @Autowired
    private SubOrderCountMapper subOrderCountMapper;
    @Autowired
    private SMMUserClient smmUserClient;
    @Autowired
    private ShopClient shopClient;
    @Autowired
    private CommodityMapper commodityMapper;

    public PageInfo<ShopCountStockPageODTO> page(ShopCountStockPageIDTO idto){
        if(!StringUtil.isBlank(idto.getBeginTime()) && !StringUtil.isBlank(idto.getEndTime())){
            idto.setBeginTime(idto.getBeginTime()+ " 00:00:00");
            idto.setEndTime(idto.getEndTime()+ " 23:59:59");
            int diff = DateUtil.getDayDif(DateUtil.parseDate(idto.getEndTime(), DateUtil.DEFAULT_DATE_FORMAT), DateUtil.parseDate(idto.getBeginTime(), DateUtil.DEFAULT_DATE_FORMAT));
            QYAssert.isTrue(diff <= 30, "查询日期的跨度不能超过31天");
        }
        List<Long> shopIdList = this.handleShopIdList(idto.getOrgCode());
        // 单门店查询时候需要判断门店是否在权限或者部门范围内
        if(SpringUtil.isEmpty(shopIdList) || (null != idto.getShopId() && !shopIdList.contains(idto.getShopId()))){
            return new PageInfo<>();
        }
        idto.setShopIdList(shopIdList);
        PageInfo<ShopCountStockPageODTO> pageInfo = PageHelper.startPage(idto.getPageNo(), idto.getPageSize()).doSelectPageInfo(() -> {
            orderMapper.selectShopCommodityVarietyTotal(idto);
        });
        List<ShopCountStockPageODTO> list = pageInfo.getList();
        if(SpringUtil.isEmpty(list)){
            return pageInfo;
        }
        // 查询各门店商品的点货数
        if(null != idto.getShopId()){
            shopIdList.add(idto.getShopId());
        }else{
            shopIdList = list.stream().map(ShopCountStockPageODTO::getShopId).collect(Collectors.toList());
        }
        List<ShopCountStockPageODTO> countList = subOrderCountMapper.countCommodityListForPage(shopIdList,
                idto.getBeginTime(), idto.getEndTime());
        Map<String, Integer> countMap = SpringUtil.isNotEmpty(countList) ?
                countList.stream().filter(it -> null != it.getCountNum())
                        .collect(Collectors.toMap(this::handleKey, ShopCountStockPageODTO::getCountNum)) : new HashMap<>();
        list.forEach(it -> {
            String key = this.handleKey(it);
            Integer countNum = countMap.get(key);
            if(null == countNum){
                it.setCountNum(0);
            }else{
                it.setCountNum(countNum);
            }
        });
        return pageInfo;
    }

    public ShopCountStockDetailODTO detail(ShopCountStockDetailIDTO idto){
        ShopCountStockDetailODTO odto = new ShopCountStockDetailODTO();
        odto.setShopId(idto.getShopId());
        odto.setOrderTime(idto.getOrderTime());
        List<ShopCountStockDetailItemODTO> varietyList = orderMapper.selectShopCommodityVarietyDetail(idto);
        List<ShopCountStockDetailItemODTO> countList = subOrderCountMapper.countCommodityListForDetail(idto);
        Map<Long, ShopCountStockDetailItemODTO> varietyMap = varietyList.stream().collect(Collectors.toMap(ShopCountStockDetailItemODTO::getCommodityId, Function.identity()));
        if(SpringUtil.isEmpty(countList)){
            return odto;
        }
        // 填充点货数并且过滤操作人
        countList = countList.stream().peek(it ->{
                    ShopCountStockDetailItemODTO varietyInfo = varietyMap.get(it.getCommodityId());
                    if(null != varietyInfo){
                        it.setVarietyTotal(varietyInfo.getVarietyTotal());
                    }else{
                        it.setVarietyTotal(BigDecimal.ZERO);
                    }
                    // 计算差异
                    if(null == it.getCountNum()){
                        it.setCountNum(BigDecimal.ZERO);
                    }
                    if(null == it.getVarietyTotal()){
                        it.setVarietyTotal(BigDecimal.ZERO);
                    }
                    BigDecimal diffQuantity = it.getCountNum().subtract(it.getVarietyTotal());
                    it.setDiffQuantity(diffQuantity);
                })
                .filter(it ->
                        null == idto.getUpdateId() ||
                                (null != idto.getUpdateId() && null != it.getUpdateId() && it.getUpdateId().equals(idto.getUpdateId()))
                )
                .collect(Collectors.toList());
        if(SpringUtil.isEmpty(countList)){
            return odto;
        }
        odto.setCountNum(countList.size());
        odto.setVarietyTotal(varietyList.size());
        // 内存分页
        PageInfo<ShopCountStockDetailItemODTO> pageInfo = ListToPageInfoUtil.convert(countList,  idto.getPageSize(), idto.getPageNo());
        List<ShopCountStockDetailItemODTO> pageList = pageInfo.getList();
        if(SpringUtil.isEmpty(pageList)){
            // 包装返回结果
            odto.setItemList(pageInfo);
            return odto;
        }
        // 拼接商品信息
        List<Long> commodityIdList = pageList.stream().map(ShopCountStockDetailItemODTO::getCommodityId).collect(Collectors.toList());
        Example example = new Example(Commodity.class);
        example.selectProperties("id", "commodityCode", "barCode", "commodityName", "commoditySpec", "commodityUnitId", "commodityPackageSpec", "isWeight");
        example.createCriteria().andIn("id",commodityIdList);
        List<Commodity> commodityInfoList = commodityMapper.selectByExample(example);
        Map<Long, Commodity> commodityInfoMap = commodityInfoList.stream().collect(Collectors.toMap(Commodity::getId, Function.identity()));
        pageList.forEach(it -> {
            Commodity commodityInfo = commodityInfoMap.get(it.getCommodityId());
            it.setCommodityCode(commodityInfo.getCommodityCode());
            it.setBarCode(commodityInfo.getBarCode());
            it.setCommodityName(commodityInfo.getCommodityName());
            it.setCommoditySpec(commodityInfo.getCommoditySpec());
            it.setCommodityUnitId(commodityInfo.getCommodityUnitId().longValue());
            it.setCommodityPackageSpec(commodityInfo.getCommodityPackageSpec());
            it.setIsWeight(commodityInfo.getIsWeight() == 1 ? "是" : "否");
        });
        // 包装返回结果
        odto.setItemList(pageInfo);
        return odto;
    }

    /**
     * 查询有权限且属于orgCode部门的门店
     * @param orgCode
     * @return
     */
    public List<Long> handleShopIdList(String orgCode){
        // 查询有权限的门店
        List<Long> shopIdList = smmUserClient.selectUserShopIdList( SelectUserShopIdListIDTO.firstCacheThenDb(FastThreadLocalUtil.getQY().getUserId()));
        if(SpringUtil.isEmpty(shopIdList)){
            return null;
        }
        if(StringUtil.isBlank(orgCode)){
            return shopIdList;
        }
        // 查询有权限且属于orgCode部门的门店
        List<ShopDto> shopDtos = shopClient.selectShopListByParentOrgCode(orgCode);
        if (SpringUtil.isNotEmpty(shopDtos)) {
             return shopDtos.stream().map(ShopDto::getId).filter(shopIdList::contains).collect(Collectors.toList());
        }else{
            return null;
        }
    }

    private String handleKey(ShopCountStockPageODTO odto){
        return odto.getShopId() + "-" + odto.getOrderTime();
    }
}
