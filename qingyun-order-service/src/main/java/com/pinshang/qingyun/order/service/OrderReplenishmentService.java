package com.pinshang.qingyun.order.service;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.pinshang.qingyun.base.exception.ParamException;
import com.pinshang.qingyun.box.utils.DateTimeUtil;
import com.pinshang.qingyun.box.utils.StringUtil;
import com.pinshang.qingyun.order.mapper.OrderMapper;
import com.pinshang.qingyun.order.mapper.entry.order.OrderReplenishmentListEntry;
import com.pinshang.qingyun.order.vo.order.OrderReplenishmentCommodityOVO;
import com.pinshang.qingyun.order.vo.order.OrderReplenishmentIVO;
import com.pinshang.qingyun.order.vo.order.OrderReplenishmentListVo;
import com.pinshang.qingyun.order.vo.order.OrderReplenishmentOVO;
import com.pinshang.qingyun.shop.dto.ShopDto;
import com.pinshang.qingyun.shop.dto.shopCommodity.ShopCommodityIDTO;
import com.pinshang.qingyun.shop.dto.shopCommodity.ShopCommodityODTO;
import com.pinshang.qingyun.shop.service.ShopClient;
import com.pinshang.qingyun.shop.service.ShopCommodityClient;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Service
public class OrderReplenishmentService {

    @Autowired
    private ShopCommodityClient shopCommodityClient;

    @Autowired
    private ShopClient shopClient;

    @Autowired
    private OrderMapper orderMapper;

    /**
     *门店补货列表
     * @param
     * @return
     */
    public PageInfo <OrderReplenishmentListEntry> orderReplenishmentList(OrderReplenishmentListVo orderReplenishmentListVo) {
        if (!StringUtil.isBlank(orderReplenishmentListVo.getBeginDate()) && !StringUtil.isBlank(orderReplenishmentListVo.getEndDate())){
            orderReplenishmentListVo.setBeginDate(orderReplenishmentListVo.getBeginDate()+ " 00:00:00");
            orderReplenishmentListVo.setEndDate(orderReplenishmentListVo.getEndDate()+ " 23:59:59");
        }
        return PageHelper.startPage(orderReplenishmentListVo.getPageNo(), orderReplenishmentListVo.getPageSize()).doSelectPageInfo(() -> {
            orderMapper.orderReplenishmentList(orderReplenishmentListVo);
        });
    }

    public Object importShopReplenishment(List<OrderReplenishmentIVO> list)throws ParamException {
        Map<String,Object> dataMap=new HashMap<>();
        //定义返回错误信息list
        List<String> errorList=new ArrayList<>();
        errorList.add("错误提示：");
        boolean isError=false;
        //定义返回对象信息
        OrderReplenishmentOVO ovo=new OrderReplenishmentOVO();
        ovo.setDeliveryDate(DateTimeUtil.formatDate(new Date(), "yyyy-MM-dd"));

        ShopDto shop=shopClient.findShopByShopCode(list.get(0).getShopCode());
        if(null ==shop || ( null != shop && null==shop.getId())){
            errorList.add("第2行,门店编码不存在");
            dataMap.put("success",0);
            dataMap.put("data",errorList);
            return dataMap;
        }
        ovo.setShopId(shop.getId()+"");
        ovo.setShopCode(shop.getShopCode());
        ovo.setShopName(shop.getShopName());
        ovo.setStoreId(shop.getStoreId()+"");
        Map<String,ShopCommodityODTO> shopCommodityMap=getShopCommodityMap(shop.getId());

        List<OrderReplenishmentCommodityOVO> commodityList=new ArrayList<>();
        BigDecimal totalAmount=BigDecimal.ZERO;
        Map<String,String> codeMap=new HashMap<>();
        for(int i=0;i<list.size();i++){
            OrderReplenishmentIVO vo=list.get(i);
            OrderReplenishmentCommodityOVO sc=new OrderReplenishmentCommodityOVO();

            if(codeMap.get(vo.getCommodityCode())!=null){
                isError=true;
                errorList.add("第"+(i+2)+"行,商品编码重复,一个商品只能一行");
            }
            codeMap.put(vo.getCommodityCode(),vo.getCommodityCode());

            if(i>0){
                if (!shop.getShopCode().equals(vo.getShopCode())) {
                    isError=true;
                    errorList.add("第"+(i+2)+"行,每次只能导入一家门店的直送补单信息");
                }
            }

            ShopCommodityODTO entry=shopCommodityMap.get(vo.getCommodityCode());
            if(null ==entry){
                isError=true;
                errorList.add("第"+(i+2)+"行,产品价格方案中未找到该商品编码"+vo.getCommodityCode());
            }

            if(!isNumber(vo.getPrice())){
                //throw new ParamException("导入失败,第"+(i+2)+"行单价信息不对");
                isError=true;
                errorList.add("第"+(i+2)+"行,单价信息不对");
            }else{
                sc.setPrice(new BigDecimal(vo.getPrice()));
            }

            if(!isNumber(vo.getProductNum())){
                isError=true;
                errorList.add("第"+(i+2)+"行,数量信息不对");
            }else{
                sc.setProductNum(new BigDecimal(vo.getProductNum()));
            }

            if(!isError){
                sc.setProductId(entry.getCommodityId()+"");
                sc.setProductCode(entry.getCommodityCode());
                sc.setProductName(entry.getCommodityName());
                sc.setProductSpec(entry.getCommoditySpec());
                sc.setBarcode(entry.getBarCode());
                sc.setBarcodes(entry.getBarCodes());
                sc.setProductAmount(sc.getPrice().multiply(sc.getProductNum()).setScale(2,BigDecimal.ROUND_HALF_UP));

                totalAmount=totalAmount.add(sc.getProductAmount());
                commodityList.add(sc);
            }
        }
        ovo.setTotalAmount(totalAmount.setScale(2,BigDecimal.ROUND_HALF_UP));
        ovo.setCommodityList(commodityList);
        if(!isError){
            dataMap.put("success",1);
            dataMap.put("data",ovo);
        }else{
            dataMap.put("success",0);
            dataMap.put("data",errorList);
        }
        return dataMap;
    }

    //当前门店零售价格方案里的商品
    public Map<String,ShopCommodityODTO> getShopCommodityMap(Long shopId){
        Map<String, ShopCommodityODTO> commodityMap =new HashMap<>();
        //当前门店零售价格方案里的商品
        ShopCommodityIDTO shopCommodityIDTO=new ShopCommodityIDTO();
        shopCommodityIDTO.setPageNo(1);
        shopCommodityIDTO.setPageSize(Integer.MAX_VALUE);
        PageInfo<ShopCommodityODTO> result = shopCommodityClient.queryShopCommodityListByParams(shopId,shopCommodityIDTO);
        List<ShopCommodityODTO> commodityList=result.getList();
        if(CollectionUtils.isNotEmpty(commodityList)){
            for(ShopCommodityODTO entry:commodityList){
                commodityMap.put(entry.getCommodityCode(),entry);
            }
        }
        return commodityMap;
    }
    // 判断小数点后2位的数字的正则表达式
    public  boolean isNumber(String str) {
        String regEx = "^(?!0+(?:\\.0+)?$)(?:[1-9]\\d*|0)(?:\\.\\d{1,2})?$";
        Pattern pattern = Pattern.compile(regEx);
        Matcher match = pattern.matcher(str);
        if (match.matches() == false) {
            return false;
        } else {
            return true;
        }
    }
}
