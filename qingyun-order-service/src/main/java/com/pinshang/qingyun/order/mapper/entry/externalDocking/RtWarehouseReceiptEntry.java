package com.pinshang.qingyun.order.mapper.entry.externalDocking;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
/**
 * 大润发ru库单
 * <AUTHOR>
 * @date 2023年2月15日10:00:09
 */
@Data
public class RtWarehouseReceiptEntry {

    @ApiModelProperty(value = "供应商")
    private String supplier;

    @ApiModelProperty(value = "产品SKU")
    private String productSKU;

    @ApiModelProperty(value = "产品名称")
    private String productName;

    @ApiModelProperty(value = "操作员")
    private String operator;

    @ApiModelProperty(value = "入库时间")
    private String warehousingTime;

    @ApiModelProperty(value = "生产日期")
    private String  manufactureTime;

    @ApiModelProperty(value = "数量")
    private String number;

    @ApiModelProperty(value = "单价")
    private String unitPrice;

    @ApiModelProperty(value = "检验检疫合格证")
    private String certificate;

    @ApiModelProperty(value = "交易凭证")
    private String voucher;
}
