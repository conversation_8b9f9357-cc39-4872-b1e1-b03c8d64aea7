package com.pinshang.qingyun.order.service.cup.strategy;

import com.pinshang.qingyun.base.configure.expand.QYAssert;
import com.pinshang.qingyun.box.utils.DateTimeUtil;
import com.pinshang.qingyun.box.utils.SpringUtil;
import com.pinshang.qingyun.order.dto.cup.EditOrderDto;
import com.pinshang.qingyun.order.enums.CombTypeEnum;
import com.pinshang.qingyun.order.enums.ProductTypeEnums;
import com.pinshang.qingyun.order.mapper.OrderMapper;
import com.pinshang.qingyun.order.model.commodity.Commodity;
import com.pinshang.qingyun.order.model.order.Order;
import com.pinshang.qingyun.order.service.CommodityService;
import com.pinshang.qingyun.order.service.OrderService;
import com.pinshang.qingyun.order.service.cup.OrderCupService;
import com.pinshang.qingyun.price.dto.storePromotion.StorePromotionCommodityPriceODTO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.DependsOn;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static java.util.stream.Collectors.toList;

@Component
public class EditOrderDetailStrategy extends AbstractOrderDetail implements OrderDetailStrategy{

    @Autowired
    OrderService orderService;

    @Autowired
    OrderMapper orderMapper;

    @Autowired
    CommodityService commodityService;

    @Autowired
    OrderCupService orderCupService;

    @Override
    public Order detailCheck(Long orderId) {
        List<Order> orderList = orderService.findListByOrderIdAndOrderStatus(orderId,0);
        QYAssert.isTrue(SpringUtil.isNotEmpty(orderList), "无法编辑该订单!");
        return orderList.get(0);
    }

    @Override
    public void processDetail(Order order, EditOrderDto dto) {
        List<Commodity> commoditylist = orderMapper.findCommodityByOrderId(order.getId(),DateTimeUtil.formatDate(order.getOrderTime(), "yyyy-MM-dd"), ProductTypeEnums.PRODUCT.getCode());

        List<Commodity> processedCommoditylist = commoditylist.stream().filter(commodity -> CombTypeEnum.COMB_CHILD.getCode() != commodity.getCombType()).collect(toList());
        BigDecimal orderAmount = BigDecimal.ZERO;
        if (SpringUtil.isNotEmpty(processedCommoditylist)) {

            Map<Long, StorePromotionCommodityPriceODTO> storePromotionMap = commodityService.selectCommodityPromotionToMap(
                    processedCommoditylist
                            .stream()
                            .map(Commodity::getId)
                            .distinct()
                            .collect(Collectors.toList()),order.getStoreId(),DateTimeUtil.formatDate(order.getOrderTime(), "yyyy-MM-dd"));
            orderCupService.processCommodityPromotion(processedCommoditylist, storePromotionMap);
            dto.setCommoditylist(processedCommoditylist);
            for (Commodity c : processedCommoditylist) {
                if(c.getPromotionFlag() == 1){
                    BigDecimal lineOrderAmount =
                            c.getCommodityPrice().multiply(c.getPromotionCount())
                                    .add(c.getOriginalCommodityPrice().multiply(c.getNormalCount()))
                                    .setScale(2, BigDecimal.ROUND_HALF_UP);
                    orderAmount = orderAmount.add(lineOrderAmount);
                    c.setCommodityLinePrice(lineOrderAmount);
                }else{
                    BigDecimal lineOrderAmount = c.getCommodityPrice().multiply(c.getProductNumber()).setScale(2, BigDecimal.ROUND_HALF_UP);
                    orderAmount = orderAmount.add(lineOrderAmount);
                    c.setCommodityLinePrice(lineOrderAmount);
                }
            }
        }
        dto.setOrderAmount(orderAmount);


        List<Commodity> giftCommoditylist = orderMapper.findCommodityByOrderId(order.getId(),DateTimeUtil.formatDate(order.getOrderTime(), "yyyy-MM-dd"),ProductTypeEnums.GIFT.getCode());
        dto.setGiftCommoditylist(giftCommoditylist);

        List<Commodity> rationCommoditylist =  orderMapper.findCommodityByOrderId(order.getId(),DateTimeUtil.formatDate(order.getOrderTime(), "yyyy-MM-dd"),ProductTypeEnums.RATION.getCode());
        dto.setRationCommoditylist(rationCommoditylist);

    }

    @Override
    public String getEditType() {
        return "edit";
    }
}
