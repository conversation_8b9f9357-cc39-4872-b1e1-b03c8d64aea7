package com.pinshang.qingyun.order.controller.api;

import com.pinshang.qingyun.base.api.XdaTokenInfo;
import com.pinshang.qingyun.base.configure.expand.QYAssert;
import com.pinshang.qingyun.base.util.FastThreadLocalUtil;
import com.pinshang.qingyun.box.utils.BeanCloneUtils;
import com.pinshang.qingyun.order.aspect.RequestBodyAndHeader;
import com.pinshang.qingyun.order.config.LockConstants;
import com.pinshang.qingyun.order.dto.PrePayBillODTO;
import com.pinshang.qingyun.order.dto.xda.v4.XdaCreatePrePayOrderV4IDTO;
import com.pinshang.qingyun.order.dto.xforder.CreateXfOrderIDTO;
import com.pinshang.qingyun.order.dto.xforder.SelectXfOrderStatusIDTO;
import com.pinshang.qingyun.order.dto.xforder.XfOrderStatusODTO;
import com.pinshang.qingyun.order.service.XfOrderService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/xfOrder/api")
@Api(value = "XfOrderApiController", tags = "现付订单-Api")
public class XfOrderApiController {

	@Autowired
	private XfOrderService xfOrderService;

	@Autowired
	private RedissonClient redissonClient;

	@ApiOperation(value = "新增现付单")
	@ApiImplicitParam(name = "idto", value = "请求参数", required = true, paramType = "body", dataTypeClass = CreateXfOrderIDTO.class)
	@PostMapping("/createXfOrder")
	public PrePayBillODTO createXfOrder(@RequestBodyAndHeader CreateXfOrderIDTO idto) {
		XdaTokenInfo xdaTokenInfo = FastThreadLocalUtil.getXDA();
		QYAssert.isTrue(!xdaTokenInfo.getIsTouristStore(), "游客无法下单。");
		QYAssert.isTrue(null != xdaTokenInfo.getStoreId(), "未能获取到用户信息,请重新登录!");

		idto.setStoreId(xdaTokenInfo.getStoreId());
		idto.setUserId(xdaTokenInfo.getUserId());
		idto.setAppCode(xdaTokenInfo.getAppCode());

		// idto.setStoreType(storeType);
		// idto.setStoreEndTime(storeEndTime);
		idto.setTypeEnum(xdaTokenInfo.getTerminalSourceType());

		String lockKey = LockConstants.generateOrderCommitLock(xdaTokenInfo.getStoreId());
		RLock lock = redissonClient.getLock(lockKey);
		QYAssert.isTrue(!lock.isLocked(), "系统繁忙，请勿频繁操作(1)!");
		QYAssert.isTrue(lock.tryLock(), "系统繁忙，请勿频繁操作(2)!");

		idto.setAppVersion(xdaTokenInfo.getAppVersion());
		XdaCreatePrePayOrderV4IDTO xdaCreatePrePayOrderV4IDTO = BeanCloneUtils.copyTo(idto, XdaCreatePrePayOrderV4IDTO.class);
		xdaCreatePrePayOrderV4IDTO.convert(idto.getStoreId(), idto.getAppCode(), idto.getUserId(), xdaTokenInfo.getTerminalSourceType());
		try {
			return xfOrderService.createXfOrder(idto, xdaCreatePrePayOrderV4IDTO);
		} finally {
			lock.unlock();
		}
	}


	@ApiOperation(value = "预订单去支付")
	@ApiImplicitParam(name = "idto", value = "请求参数", required = true, paramType = "body", dataTypeClass = CreateXfOrderIDTO.class)
	@PostMapping("/preOrderGoPay")
	public PrePayBillODTO preOrderGoPay(@RequestBody CreateXfOrderIDTO idto) {
		XdaTokenInfo xdaTokenInfo = FastThreadLocalUtil.getXDA();
		QYAssert.isTrue(!xdaTokenInfo.getIsTouristStore(), "游客无法下单。");
		QYAssert.isTrue(null != xdaTokenInfo.getStoreId(), "未能获取到用户信息,请重新登录!");

		idto.setStoreId(xdaTokenInfo.getStoreId());
		idto.setUserId(xdaTokenInfo.getUserId());
		idto.setAppCode(xdaTokenInfo.getAppCode());

		idto.setTypeEnum(xdaTokenInfo.getTerminalSourceType());

		String lockKey = LockConstants.generateOrderCommitLock(xdaTokenInfo.getStoreId());
		RLock lock = redissonClient.getLock(lockKey);
		QYAssert.isTrue(!lock.isLocked(), "系统繁忙，请勿频繁操作(1)!");
		QYAssert.isTrue(lock.tryLock(), "系统繁忙，请勿频繁操作(2)!");

		try {
			idto.setAppVersion(xdaTokenInfo.getAppVersion());
			return xfOrderService.preOrderGoPay(idto);
		} finally {
			lock.unlock();
		}
	}
	@ApiOperation(value = "查询  现付单状态")
	@ApiImplicitParam(name = "idto", value = "请求参数", required = true, paramType = "body", dataTypeClass = SelectXfOrderStatusIDTO.class)
	@PostMapping("/selectXfOrderStatus")
	public XfOrderStatusODTO selectXfOrderStatus(@RequestBody SelectXfOrderStatusIDTO idto) {
		XdaTokenInfo xdaTokenInfo = FastThreadLocalUtil.getXDA();
		Long storeId = xdaTokenInfo.getStoreId();
		QYAssert.isTrue(!xdaTokenInfo.getIsTouristStore(), "游客无法下单。");
		QYAssert.isTrue(storeId != null, "未能获取到用户信息,请重新登录!");

		idto.setStoreId(storeId);
		idto.setUserId(xdaTokenInfo.getUserId());
		return xfOrderService.selectXfOrderStatus(idto);
	}

}
