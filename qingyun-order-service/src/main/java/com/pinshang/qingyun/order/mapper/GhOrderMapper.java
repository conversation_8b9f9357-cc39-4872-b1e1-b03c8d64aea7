package com.pinshang.qingyun.order.mapper;

import com.pinshang.qingyun.order.mapper.entry.externalDocking.GhOrderEntry;
import com.pinshang.qingyun.order.vo.externalDocking.GhOrderVo;
import org.apache.ibatis.annotations.Mapper;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 高校订单
 * <AUTHOR>
 * @date 2023年2月15日10:00:09
 */
@Repository
@Mapper
public interface GhOrderMapper {

    /**
     * 根据条件查询高校订单列表
     * @param vo
     * @return
     */
    List<GhOrderEntry> findGhOrderPageInfo(GhOrderVo vo);
}
