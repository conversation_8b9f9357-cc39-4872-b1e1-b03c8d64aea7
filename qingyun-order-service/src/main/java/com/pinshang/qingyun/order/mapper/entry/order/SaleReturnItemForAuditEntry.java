package com.pinshang.qingyun.order.mapper.entry.order;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * Created by crell on 2017/11/6.
 */
@Data
public class SaleReturnItemForAuditEntry extends SaleReturnItemEntry{

    @ApiModelProperty("审核备注")
    private String auditRemark;

    @ApiModelProperty("责任方")
    private String rpTypeStr;

    @ApiModelProperty("索赔金额")
    private BigDecimal compensatePrice;

    @ApiModelProperty("退货原因")
    private String returnReasonStr;

}
