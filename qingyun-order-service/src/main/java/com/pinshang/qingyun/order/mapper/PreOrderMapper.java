package com.pinshang.qingyun.order.mapper;

import com.pinshang.qingyun.base.mybatis.MyMapper;
import com.pinshang.qingyun.order.dto.consignment.ConsignmentOrderPageODTO;
import com.pinshang.qingyun.order.dto.consignment.ConsignmentOrderQueryIDTO;
import com.pinshang.qingyun.order.mapper.entry.purchase.DirectSendingPurchaseItemEntry;
import com.pinshang.qingyun.order.model.order.PreOrder;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;

@Repository
public interface PreOrderMapper extends MyMapper<PreOrder>{

	Integer updatePreOrderStatus(@Param("orderId") Long orderId, @Param("userId")Long userId);
	
	Long finsShopIdByStore(@Param("storeId") Long storeId);

	List<DirectSendingPurchaseItemEntry> getMdPreOrderList(@Param("supplierId")Long supplierId, @Param("orderTime")Date orderTime);

	Integer countNotReceiveConsignmentOrder(@Param("storeId") Long storeId, @Param("beginDate") String beginDate, @Param("endDate") String endDate, @Param("commodityIdList") List<Long> commodityIdList, @Param("supplierIdList") List<Long> supplierIdList);

	List<ConsignmentOrderPageODTO> consignmentOrderReport(@Param("vo") ConsignmentOrderQueryIDTO vo);
}
