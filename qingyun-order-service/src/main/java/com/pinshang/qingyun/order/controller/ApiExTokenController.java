package com.pinshang.qingyun.order.controller;

import com.pinshang.qingyun.order.service.OrderService;
import io.swagger.annotations.Api;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RequestMapping("/apiExToken")
@Api(value = "免登陆验证", description = "免登陆验证")
@RestController
public class ApiExTokenController {

    @Autowired
    private OrderService orderService;

    /***
     * 根据订单查询订单明细
     */
    @RequestMapping( value = "/existConsignmentOrder", method = RequestMethod.POST)
    public Boolean existConsignmentOrder(@RequestParam("shopId") Long shopId){
        return orderService.existConsignmentOrder(shopId);
    }
}
