package com.pinshang.qingyun.order.mapper.entry.pda;

import com.pinshang.qingyun.renderer.annotation.FieldRender;
import com.pinshang.qingyun.renderer.constant.RenderFieldHelper;
import com.pinshang.qingyun.renderer.enums.FieldTypeEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class PdaOrderQueryEntry {

    private Long commodityId;

    private BigDecimal commodityNum;

    private BigDecimal realQuantity;

}
