package com.pinshang.qingyun.order.controller;

import com.pinshang.qingyun.box.utils.DateUtil;
import com.pinshang.qingyun.order.service.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import java.util.Calendar;

@RestController
@RequestMapping("/takeCardOrderBill")
@Slf4j
public class TakeCardOrderBillController {
	@Autowired
	private OrderSaveTakeCardService orderService;

	/**
	 * 拉取陈奇提货卡流水
	 * @param appointDate
	 * @return
	 */
	@RequestMapping(value = "/pullTakeBill", method = RequestMethod.POST)
    public String pullTakeBill(@RequestParam(value = "appointDate", required = false) String appointDate){
		if(!StringUtils.hasText(appointDate)){
			Calendar calendar = Calendar.getInstance();
			calendar.add(Calendar.DATE,-1);
			appointDate = DateUtil.getDateFormate(calendar.getTime(), "yyyy-MM-dd");
		}
		return orderService.pullTakeBill(appointDate);
	}

	/**
	 * 基于提货卡流水 生成订单
	 * @return
	 */
	@RequestMapping(value = "/generateCardOrder", method = RequestMethod.POST)
    public String generateCardOrder(){
			orderService.generateCardOrder();
		return null;
	}



}
