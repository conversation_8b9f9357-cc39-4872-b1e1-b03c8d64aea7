package com.pinshang.qingyun.order.controller.client;

import com.github.pagehelper.PageInfo;
import com.pinshang.qingyun.order.service.orderSync.IOrderSyncToDcService;
import com.pinshang.qingyun.order.vo.order.OrderClientVO;
import com.pinshang.qingyun.order.vo.order.OrderSyncToDcReqVo;
import com.pinshang.qingyun.order.vo.order.OrderSyncToDcRespVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/orderSync/client")
public class OrderSyncToDcController {

    @Autowired
    private IOrderSyncToDcService iOrderSyncToDcService;

    @PostMapping("/queryOrderSyncToDcList")
    public List<OrderSyncToDcRespVo> queryOrderSyncToDcList(@RequestBody OrderSyncToDcReqVo vo) {
        return iOrderSyncToDcService.queryOrderSyncToDcList(vo);
    }

    @PostMapping("/getOrderListByIds")
    public List<OrderClientVO> getOrderListByIds(@RequestParam("orderIds") List<Long> orderIds) {
        return iOrderSyncToDcService.getOrderListByIds(orderIds);
    }

    @PostMapping("/queryOrderListBySubOrderIds")
    public List<OrderClientVO> queryOrderListBySubOrderIds(@RequestParam("subOrderIds") List<Long> subOrderIds) {
        return iOrderSyncToDcService.queryOrderListBySubOrderIds(subOrderIds);
    }

    @PostMapping("/queryOrderPage")
    public PageInfo<OrderSyncToDcRespVo> queryOrderPage(@RequestBody OrderSyncToDcReqVo vo) {
        return iOrderSyncToDcService.queryOrderPage(vo);
    }

}
