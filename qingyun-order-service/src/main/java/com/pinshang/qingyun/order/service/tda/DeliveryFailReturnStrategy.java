/*
 * tramy.cn Inc.
 * Copyright (c) 2021-2024 All Rights Reserved.
 */
package com.pinshang.qingyun.order.service.tda;

import com.pinshang.qingyun.base.configure.expand.QYAssert;
import com.pinshang.qingyun.base.enums.tms.TmsBusinessTypeEnums;
import com.pinshang.qingyun.box.utils.DateUtil;
import com.pinshang.qingyun.common.dto.DictionaryODTO;
import com.pinshang.qingyun.common.service.DictionaryClient;
import com.pinshang.qingyun.order.dto.xda.tda.SaveReturnOrderItemDTO;
import com.pinshang.qingyun.order.dto.xda.tda.SaveReturnOrderODTO;
import com.pinshang.qingyun.order.enums.tda.TDaReturnOrderTypeEnum;
import com.pinshang.qingyun.order.enums.tda.TDaReturnSourceEnum;
import com.pinshang.qingyun.order.enums.tda.TDaReturnStatusEnum;
import com.pinshang.qingyun.order.enums.tda.TDaReturnTypeEnum;
import com.pinshang.qingyun.order.mapper.XdaReturnOrderItemMapper;
import com.pinshang.qingyun.order.mapper.XdaReturnOrderMapper;
import com.pinshang.qingyun.order.model.commodity.Commodity;
import com.pinshang.qingyun.order.model.store.Store;
import com.pinshang.qingyun.order.model.xda.XdaReturnOrder;
import com.pinshang.qingyun.order.model.xda.XdaReturnOrderItem;
import com.pinshang.qingyun.order.service.CommodityService;
import com.pinshang.qingyun.order.service.StoreService;
import com.pinshang.qingyun.order.service.tda.factory.ReturnOrderTemplate;
import com.pinshang.qingyun.renderer.service.IRenderService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.entity.Example;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

import static com.pinshang.qingyun.order.service.xda.XdaComplaintOrderService.getUUID;

/**
 * <p>
 * 配送失败
 * </p>
 *
 * <AUTHOR> shenyang
 * @version : 1.0.0
 * @history : modify history
 * <author>              <time>              <version>              <desc>
 * @since : 2024/05/17 23:09
 */
@Slf4j
@Component
public class DeliveryFailReturnStrategy extends ReturnOrderTemplate implements ReturnOrderStrategy {
    @Autowired
    private StoreService storeService;
    @Autowired
    private IRenderService renderService;
    @Autowired
    private CommodityService commodityService;
    @Autowired
    private DictionaryClient dictionaryClient;
    @Autowired
    private ApplicationContext applicationContext;
    @Autowired
    private XdaReturnOrderMapper xdaReturnOrderMapper;
    @Autowired
    private XdaReturnOrderItemMapper xdaReturnOrderItemMapper;


    @Override
    public Boolean execute(SaveReturnOrderODTO saveReturnOrderDTO) {
        DeliveryFailReturnStrategy contextBean = applicationContext.getBean(DeliveryFailReturnStrategy.class);
        //查询订单信息，组装退货单明细
        super.buildComplaintItemList(saveReturnOrderDTO);
        return contextBean.saveReturnOrder(saveReturnOrderDTO);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean saveReturnOrder(SaveReturnOrderODTO saveReturnOrderDTO) {
        Long storeId = saveReturnOrderDTO.getStoreId();
        Store store = storeService.findStoreByStoreId(storeId);
        QYAssert.isTrue(!(storeId == null || store == null), "客户不存在！");

        if (!Objects.equals(saveReturnOrderDTO.getBusinessType(), TmsBusinessTypeEnums.TD_SALE.getCode())) {
            log.warn("订单:{}非通达销售订单，不生成退货单", saveReturnOrderDTO.getOrderCode());
            return false;
        }
        //实发全部为0 不生成退货单
        BigDecimal totalRealQuantity = saveReturnOrderDTO.getComplaintItemList().stream().map(SaveReturnOrderItemDTO::getRealDeliveryQuantity).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);
        if (totalRealQuantity.compareTo(BigDecimal.ZERO) == 0) {
            log.warn("订单:{}实发全部为0，不生成退货单", saveReturnOrderDTO.getOrderCode());
            return false;
        }
        //根据运单号判断是否已经生成过，防止重复生成退货单
        Example example = new Example(XdaReturnOrder.class);
        example.createCriteria()
                .andEqualTo("returnOrderType", TDaReturnOrderTypeEnum.RETURN.getCode())
                .andEqualTo("waybillCode", saveReturnOrderDTO.getWaybillCode())
                .andIn("status", Arrays.asList(TDaReturnStatusEnum.PENDING_WAREHOUSE_CONFIRMATION.getCode(), TDaReturnStatusEnum.COMPLETED.getCode()));
        List<XdaReturnOrder> xdaReturnOrders = xdaReturnOrderMapper.selectByExample(example);
        if (CollectionUtils.isNotEmpty(xdaReturnOrders)) {
            log.warn("运单:{}已生成过退货单，不生成退货单", saveReturnOrderDTO.getWaybillCode());
            return false;
        }
        //创建退货单
        XdaReturnOrder xdaReturnOrder = new XdaReturnOrder();
        xdaReturnOrder.setStoreId(saveReturnOrderDTO.getStoreId());
        xdaReturnOrder.setReturnOrderType(TDaReturnOrderTypeEnum.RETURN.getCategoryCode());
        xdaReturnOrder.setReturnOrderCode(getUUID());
        xdaReturnOrder.setReturnOrderSeq(String.valueOf(new Date().getTime()));
        xdaReturnOrder.setBusinessType(saveReturnOrderDTO.getBusinessType());
        xdaReturnOrder.setWaybillCode(saveReturnOrderDTO.getWaybillCode());
        xdaReturnOrder.setDriverId(saveReturnOrderDTO.getDriverId());
        xdaReturnOrder.setReturnType(TDaReturnTypeEnum.DELIVERY_FAILURE.getCode());
        xdaReturnOrder.setReturnSource(TDaReturnSourceEnum.DELIVERY_FAILURE.getCode());
        xdaReturnOrder.setStatus(TDaReturnStatusEnum.PENDING_WAREHOUSE_CONFIRMATION.getCode());
        xdaReturnOrder.setDriverId(saveReturnOrderDTO.getDriverId());
        renderService.render(xdaReturnOrder, "saveReturnOrder");
        xdaReturnOrder.setSourceOrderId(saveReturnOrderDTO.getOrderId());
        xdaReturnOrder.setSourceOrderCode(saveReturnOrderDTO.getOrderCode());
        xdaReturnOrder.setLogisticsCenterId(saveReturnOrderDTO.getLogisticsCenterId());
        xdaReturnOrder.setDeliveryBatch(saveReturnOrderDTO.getDeliveryBatch());
        xdaReturnOrder.setWaybillCode(saveReturnOrderDTO.getWaybillCode());
        xdaReturnOrder.setPickUpTimeRange(saveReturnOrderDTO.getPickUpTimeRange());
        xdaReturnOrder.setDeliveryTime(DateUtil.parseDate(saveReturnOrderDTO.getDeliveryDate(), "yyyy-MM-dd"));
        xdaReturnOrder.setDeliveryEndTime(new Date());
        xdaReturnOrder.setPhone(store.getLinkmanMobile());
        xdaReturnOrder.setDeliveryAddress(store.getDeliveryAddress());
        BigDecimal complaintTotalMoney = saveReturnOrderDTO.getComplaintTotalMoney();
        if (Objects.nonNull(complaintTotalMoney)) {
            if (complaintTotalMoney.compareTo(BigDecimal.ZERO) > 0) {
                complaintTotalMoney = complaintTotalMoney.negate();
            }
            xdaReturnOrder.setTotalApplyMoney(complaintTotalMoney);
        }
        xdaReturnOrder.setBusinessType(TmsBusinessTypeEnums.TD_SALE.getCode());


        //插入退货单
        int flag = xdaReturnOrderMapper.insertSelective(xdaReturnOrder);

        //创建退货明细
        List<Long> commodityIdList = saveReturnOrderDTO.getComplaintItemList().stream().map(SaveReturnOrderItemDTO::getCommodityId).distinct().collect(Collectors.toList());

        //查询商品信息（包装规格）
        Map<Long, Commodity> commodityInfoByIdMap = commodityService.findCommodityInfoByIdMap(commodityIdList);

        //查询退货原因的子字典集合
        List<DictionaryODTO> returnResonDicts = dictionaryClient.querySubDictionaryByParentOptionCode("6789");
        List<DictionaryODTO> responsiblePartyDicts = dictionaryClient.querySubDictionaryByParentOptionCode("XdaResponsiblePartyType");

        DictionaryODTO returnResonDict = returnResonDicts.stream().filter(x -> Objects.equals(x.getOptionCode(), "DELIVERY_FAILURE")).findFirst().orElse(null);

        List<XdaReturnOrderItem> xdaReturnOrderItems = saveReturnOrderDTO.getComplaintItemList()
                .stream()
                .filter(x -> Objects.nonNull(commodityInfoByIdMap.get(x.getCommodityId()))
                        && x.getRealDeliveryQuantity().compareTo(BigDecimal.ZERO) > 0)
                .map(dto -> {
                    Commodity commodity = commodityInfoByIdMap.get(dto.getCommodityId());
                    XdaReturnOrderItem xdaReturnOrderItem = new XdaReturnOrderItem();
                    xdaReturnOrderItem.setReturnOrderId(xdaReturnOrder.getId());
                    xdaReturnOrderItem.setCommodityId(dto.getCommodityId());
                    xdaReturnOrderItem.setCommodityPrice(dto.getCommodityPrice());
                    //计算申请详情
                    BigDecimal applyQuantity = dto.getRealDeliveryQuantity().abs();
                    super.calculateApplyDetails(applyQuantity, dto, commodity, xdaReturnOrderItem);
                    xdaReturnOrderItem.setComplaintPicList(dto.getComplaintPicList());

                    if (Objects.nonNull(returnResonDict)) {
                        xdaReturnOrderItem.setReturnReasonType(Long.valueOf(returnResonDict.getId()));
                    }
                    DictionaryODTO responsiblePartyDict = responsiblePartyDicts.stream().filter(x -> Objects.equals(x.getOptionName(), "其他")).findFirst().orElse(null);
                    if (Objects.nonNull(responsiblePartyDict)) {
                        xdaReturnOrderItem.setResponsibleParty(responsiblePartyDict.getOptionValue());
                    }
                    xdaReturnOrderItem.setCheckNumber(xdaReturnOrderItem.getApplyNumber());
                    xdaReturnOrderItem.setCheckQuantity(xdaReturnOrderItem.getApplyQuantity());
                    xdaReturnOrderItem.setCheckMoney(xdaReturnOrderItem.getApplyMoney());
                    xdaReturnOrderItem.setCommodityOrderQuantity(dto.getRealDeliveryQuantity());
                    xdaReturnOrderItem.setReturnOrderType(TDaReturnOrderTypeEnum.RETURN.getCode());

                    return xdaReturnOrderItem;
                }).collect(Collectors.toList());

        if (CollectionUtils.isNotEmpty(xdaReturnOrderItems)) {
            //合计申请金额和数量，份数
            BigDecimal totalCheckQuantity = xdaReturnOrderItems.stream().map(XdaReturnOrderItem::getCheckQuantity).reduce(BigDecimal.ZERO, BigDecimal::add);
            Integer totalCheckNumber = xdaReturnOrderItems.stream().map(XdaReturnOrderItem::getCheckNumber).reduce(0, Integer::sum);
            BigDecimal totalCheckMoney = xdaReturnOrderItems.stream().map(XdaReturnOrderItem::getCheckMoney).reduce(BigDecimal.ZERO, BigDecimal::add);
            // 更新合计申请金额和数量，份数
            XdaReturnOrder returnOrder = new XdaReturnOrder();
            returnOrder.setId(xdaReturnOrder.getId());
            returnOrder.setTotalCheckMoney(totalCheckMoney);
            returnOrder.setTotalCheckNumber(totalCheckNumber);
            returnOrder.setTotalCheckQuantity(totalCheckQuantity);
            xdaReturnOrderMapper.updateByPrimaryKeySelective(returnOrder);

            // 插入 退货明细
            xdaReturnOrderItemMapper.batchInsertSelective(xdaReturnOrderItems);
        }

        return flag > 0;
    }


    @Override
    public Integer getReturnSourceType() {
        return TDaReturnSourceEnum.DELIVERY_FAILURE.getCode();
    }
}