package com.pinshang.qingyun.order.controller.xda.v2;

import com.pinshang.qingyun.base.configure.expand.QYAssert;
import com.pinshang.qingyun.order.dto.shopcart.v2.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 23/6/14/014 10:01
 * 新地址: XdaShoppingCartV3Controller
 */
@Slf4j
@RestController
@RequestMapping("/xda/shoppingCartV2")
@Api(value = "鲜达APP购物车相关接口",tags ="XdaShopcartV2Controller",description ="鲜达APP购物车相关接口" )
@Deprecated
public class XdaShopcartV2Controller {


    @PostMapping("/addV2")
    @ApiOperation(value = "购物车--加")
    public ShoppingCartV2ODTO addShopCart(@RequestBody ShoppingCartAddV2IDTO shoppingCartAddV2IDTO){
        QYAssert.isFalse("请升级到最新版本");
        return null;
    }

    @PostMapping("/minusV2")
    @ApiOperation(value = "购物车--减")
    public ShoppingCartV2ODTO minusShopCart(@RequestBody ShoppingCartMinusV2IDTO shoppingCartMinusV2IDTO){
        QYAssert.isFalse("请升级到最新版本");
        return null;
    }

    @PostMapping("/setNum")
    @ApiOperation(value = "购物车--设置数量")
    public ShoppingCartV2ODTO setNumShopCart(@RequestBody ShoppingCartSetNumV2IDTO shoppingCartSetNumV2IDTO){
        QYAssert.isFalse("请升级到最新版本");
        return null;
    }

    @GetMapping("/refreshV2")
    @ApiOperation(value = "购物车--刷新")
    public ShoppingCartV2ODTO refresh(@RequestParam(value = "orderDate",required = false) String orderDate){
        QYAssert.isFalse("请升级到最新版本");
        return null;
    }

    @PostMapping("/clear")
    @ApiOperation(value = "购物车--清空")
    public ShoppingCartV2ODTO clear(@RequestBody ShoppingCartClearV2IDTO shoppingCartClearV2IDTO){
        QYAssert.isFalse("请升级到最新版本");
        return null;
    }

    @GetMapping("/getCategoryV2")
    @ApiOperation(value = "购物车--品类数量")
    public Integer getCategoryNum(@RequestParam(value = "orderDate",required = false) String orderDate, @RequestParam(value = "storeId",required = false) Long storeId){
        QYAssert.isFalse("请升级到最新版本");
        return null;
    }

    @GetMapping("/getNormalGroupAmountV2")
    @ApiOperation(value = "购物车--普通商品组优惠后的价格")
    public BigDecimal getNormalGroupAmountV2(@RequestParam(value = "orderDate",required = false) String orderDate, @RequestParam(value = "storeId",required = false) Long storeId){
        QYAssert.isFalse("请升级到最新版本");
        return null;
    }

    @GetMapping("/clear/invalidV2")
    @ApiOperation(value = "购物车--清空失效商品")
    public ShoppingCartV2ODTO clearInvalidCommodity(@RequestParam(value = "orderDate",required = false) String orderDate){
        QYAssert.isFalse("请升级到最新版本");
        return null;
    }

}
