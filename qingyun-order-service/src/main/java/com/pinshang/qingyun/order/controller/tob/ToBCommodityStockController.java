package com.pinshang.qingyun.order.controller.tob;

import com.github.pagehelper.PageInfo;
import com.pinshang.qingyun.base.configure.expand.BizLogicException;
import com.pinshang.qingyun.order.dto.tob.*;
import com.pinshang.qingyun.order.service.TobCommodityStockService;
import com.pinshang.qingyun.order.service.tob.ITobCommodityStockForEsService;
import com.pinshang.qingyun.order.service.tob.ToBCommodityStockService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * @Description：
 * @Author：ZhangHui
 * @Package：com.pinshang.qingyun.order.controller.tob
 * @Date: 2024/4/10
 */
@Log4j2
@RestController
@RequestMapping("/tob/order/stock")
@Api(tags = "ToB库存接口服务")
public class ToBCommodityStockController {


    @Autowired
    private ToBCommodityStockService toBCommodityStockService;


    @Autowired
    private ITobCommodityStockForEsService tobCommodityStockForEsService;

    @Autowired
    private TobCommodityStockService tobCommodityStockService;

    private static final String stockRepairDataNullMsg = "需要修复的数据为空！";


    @ApiOperation(value = "库存修复")
    @PostMapping("/stockRepair")
    public void stockRepair(@RequestBody List<ToBStockRepairIDTO> repairIDTOList) {
        if (CollectionUtils.isEmpty(repairIDTOList)) {
            throw new BizLogicException(stockRepairDataNullMsg);
        }
        toBCommodityStockService.stockRepair(repairIDTOList);
    }


    @ApiOperation(value = "查询库存数量")
    @GetMapping("/queryStockCount")
    public Integer queryStockCount() {
        return toBCommodityStockService.queryStockCount();
    }


    @ApiOperation(value = "查询库存数据")
    @GetMapping("/queryStockList")
    public PageInfo<ToBStockRepairODTO> queryStockList(@RequestParam(value = "pageNo") Integer pageNo,
                                                       @RequestParam(value = "pageSize") Integer pageSize) {
        return toBCommodityStockService.queryStockList(pageNo, pageSize);
    }

    @ApiOperation("xda es查询库存类型下的商品id")
    @RequestMapping(value = "/selectCommodityIdByStockType", method = RequestMethod.GET)
    public List<Long> selectCommodityIdByStockType(@RequestParam("stockType") Integer stockType){
        return tobCommodityStockForEsService.selectCommodityIdByStockType(stockType);
    }

    @RequestMapping(value = "/queryCommodityInventory", method = RequestMethod.POST)
    @ApiOperation("xda es查询大仓库存")
    public List<EsXdaCommoditySoldOutODTO> queryCommodityInventory(@RequestBody EsXdaCommoditySoldOutIDTO idto){
        return tobCommodityStockForEsService.queryCommodityInventory(idto);
    }

    @RequestMapping(value = "/updateXdaCommodityStock", method = RequestMethod.POST)
    @ApiOperation("鲜达app详情页时候，刷新库存")
    public Boolean updateXdaCommodityStock(@RequestBody TobCommodityStockIDTO idto){
        return tobCommodityStockService.updateXdaCommodityStock(idto);
    }

}
