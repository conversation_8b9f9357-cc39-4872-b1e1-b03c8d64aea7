package com.pinshang.qingyun.order.service;

import com.pinshang.qingyun.base.configure.expand.QYAssert;
import com.pinshang.qingyun.base.enums.IogisticsModelEnums;
import com.pinshang.qingyun.base.enums.storage.ToBTypeEnums;
import com.pinshang.qingyun.box.utils.BeanCloneUtils;
import com.pinshang.qingyun.box.utils.DateUtil;
import com.pinshang.qingyun.order.dto.FreezeGiftODTO;
import com.pinshang.qingyun.order.dto.OrderInfoODTO;
import com.pinshang.qingyun.order.enums.OperationTypeEnum;
import com.pinshang.qingyun.order.enums.ProductTypeEnums;
import com.pinshang.qingyun.order.mapper.*;
import com.pinshang.qingyun.order.model.order.*;
import com.pinshang.qingyun.order.service.bigShop.BigShopReceiveService;
import com.pinshang.qingyun.order.util.ThreadLocalUtils;
import com.pinshang.qingyun.order.vo.order.*;
import com.pinshang.qingyun.storage.dto.tob.CommodityInventoryODTO;
import com.pinshang.qingyun.storage.dto.tob.ToBOrderDetailIDTO;
import com.pinshang.qingyun.storage.dto.tob.WarehouseFreezeInventoryIDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionSynchronizationAdapter;
import org.springframework.transaction.support.TransactionSynchronizationManager;
import tk.mybatis.mapper.entity.Example;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @Author: sk
 * @Date: 2022/9/21
 */
@Slf4j
@Service
public class OrderSaveService {

    @Autowired
    private ShoppingCartMapper shoppingCartMapper;
    @Autowired
    private ShoppingCartItemMapper shoppingCartItemMapper;

    @Autowired
    private OrderAsyncKafkaService orderAsyncKafkaService;
    @Lazy
    @Autowired
    private OrderService orderService;
    @Lazy
    @Autowired
    private BStockService bStockService;
    @Autowired
    private OrderListGiftMapper orderListGiftMapper;
    @Autowired
    private SubOrderMapper subOrderMapper;
    @Autowired
    private OrderListMapper orderListMapper;
    @Autowired
    private SubOrderItemMapper subOrderItemMapper;
    @Autowired
    private ShopService shopService;
    @Autowired
    private BigShopReceiveService bigShopReceiveService;


    @Transactional(rollbackFor = Exception.class)
    public CreateOrderWrapperVo saveOneOrder(OrderDto orderDto, OrderRequestVo orderRequestVo, CreateOrderVo vo){

        // 7. 创建订单
        CreateOrderWrapperVo result = new CreateOrderWrapperVo();
        // 根据物流模式生成直送或者配送直通单
        Boolean isDirectModel = orderDto.getLogisticsModel().equals(IogisticsModelEnums.DIRECT_SENDING.getCode());
        // 档口id
        orderDto.setStallId(orderRequestVo.getStallId());
        if (isDirectModel) {//直送订单-> 预置订单
            orderService.savePreOrder(orderDto);
        } else {    // 配送、直通
            Boolean isXsJmShop = shopService.isJmShop(orderDto.getStoreId());
            // 代理订货不校验批次信息
            Boolean ifAdmin = ThreadLocalUtils.getAdmin();
            if(!ifAdmin){
                // 检查批次对应的时间
                orderService.checkDeliveryBatchTime(orderDto);
            }else {
                Date orderDate = DateUtil.parseDate(orderDto.getOrderTime(),"yyyy-MM-dd");
                QYAssert.isTrue(orderDate.getTime() >= DateUtil.getNowDate().getTime(), "送货日期不能小于今日");
            }
            // 设置代销商户
            orderDto.setConsignmentId(orderRequestVo.getConsignmentId());
            Order order = orderService.saveOrder(orderDto, false, isXsJmShop);
            Long referId = order.getId();
            Order kafkaOrder = orderService.saveGiftOrder(orderDto, order.getId(), false);
            kafkaOrder.setOrderAmount(orderDto.amount());
            result.setKafkaOrder(kafkaOrder);
            // 克隆一个orderDto给冻结使用
            OrderDto cloneOrderDto = BeanCloneUtils.copyTo(orderDto, OrderDto.class);
            cloneOrderDto.setOrderType(order.getOrderType());

            // 循环创建多个子订单 t_order(1) -> t_sub_order(N)
            List<OrderDto> orderDtos = orderService.buildOrderDtos(orderDto);
            for (OrderDto orderDtoItem : orderDtos) {
                //创建subOrder
                Long subOrderId = orderService.createSubOrderAndItem(orderDtoItem, order.getId(), order.getOrderCode(), isXsJmShop, order.getAssociationOrderMap());
                //创建收货单
                ShopReceiveOrderVo receiveDto =new ShopReceiveOrderVo(orderRequestVo.getEnterpriseId(), subOrderId, orderRequestVo.getUserId());
                orderService.newReceiveOrder(receiveDto);
            }

            // 记录订单日志
            orderService.inserOrderHistory(referId, Long.valueOf(order.getOrderCode()), orderRequestVo.getUserId(), orderRequestVo.getCreateName(), OperationTypeEnum.ORDER_NEW.getCode(), order.getOrderTime(), null, null);

            // 依据大仓和限量的才进行冻结
            List<FreezeGiftODTO> freezeODTOList = getFreezeGiftList(cloneOrderDto);
            if(CollectionUtils.isNotEmpty(freezeODTOList)){
                List<FreezeGiftODTO> giftProductList = freezeODTOList.stream().filter(p -> ProductTypeEnums.GIFT.getCode().equals(p.getType())).collect(Collectors.toList());
                Map<Long, BigDecimal> giftProductMap = giftProductList.stream().collect(Collectors.toMap(FreezeGiftODTO::getCommodityId,FreezeGiftODTO::getOrderQuantity,(key1 , key2)-> key2));

                // 组装冻结明细list
                List<ToBOrderDetailIDTO> toBOrderDetailList = getToBOrderDetailList(giftProductMap, freezeODTOList);
                WarehouseFreezeInventoryIDTO freezeInventoryIDTO = WarehouseFreezeInventoryIDTO.builder()
                        .orderId(order.getId())
                        .orderCode(order.getOrderCode())
                        .orderTime(order.getOrderTime())
                        // 冻结type大店传大店类型的
                        .type((order.getStallId() != null && order.getStallId() > 0) ? ToBTypeEnums.BIGSHOP_SALE.getCode() : ToBTypeEnums.SALE.getCode())
                        .sourceType(order.getOrderType())
                        .deliveryBatch(order.getDeliveryBatch())
                        .storeId(order.getStoreId())
                        .storeCode(null)
                        .storeName(null)
                        .orderCommodityDetailList(toBOrderDetailList)
                        .build();
                // 去掉了保底数量，赠品不足也报错
                bStockService.warehouseFreezeInventory(freezeInventoryIDTO, cloneOrderDto.getUserId(), null);

               /* // 冻结赠品判断是否都冻结成功，如果赠品冻结失败。则更新订单明细里面的赠品数量
                if(!giftProductMap.isEmpty() && CollectionUtils.isNotEmpty(responseFreezeList)){
                    dealFreezeResponse(cloneOrderDto, giftProductMap, responseFreezeList, toBOrderDetailList, referId);
                }*/
            }

        }

        // 删除购物车
        if(vo != null && vo.getShoppingCartId() != null){
            //删除购物车及明细
            Example ex = new Example(ShoppingCartItem.class);
            ex.createCriteria().andEqualTo("shoppingCartId", vo.getShoppingCartId());
            this.shoppingCartItemMapper.deleteByExample(ex);
            this.shoppingCartMapper.deleteByPrimaryKey(vo.getShoppingCartId());

            // 设置商品的编码并去重复
            List<FilterTipVo> filterTipList = orderService.distinctFilterTips();

            // 将过滤商品加入购物车
            orderService.batchAddShoppingCartForFilterCommodity(vo, filterTipList, orderDto.getStallId());
        }

        //事务完成发送消息
        TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronizationAdapter() {
            @Override
            public void afterCommit() {
                orderAsyncKafkaService.sendKafkaSaveOrderMessage(result.getKafkaOrder());

                // 大店非直送维护收货单表
                if(!isDirectModel && orderDto.getStallId() != null && orderDto.getStallId() > 0 && StringUtils.isNotBlank(orderDto.getDeliveryBatch())){
                    bigShopReceiveService.createReceiveDoc(orderDto.getOrderTime(), orderDto.getStoreId(), orderDto.getStallId(), Integer.valueOf(orderDto.getDeliveryBatch()));
                }
            }
        });
        return result;
    }

    /**
     * 冻结赠品判断是否都冻结成功，如果赠品冻结失败。则更新订单明细里面的赠品数量
     * @param orderDto
     * @param giftProductMap
     * @param responseFreezeList
     * @param toBOrderDetailList
     * @param referId
     */
    public void dealFreezeResponse(OrderDto orderDto, Map<Long, BigDecimal> giftProductMap, List<CommodityInventoryODTO> responseFreezeList, List<ToBOrderDetailIDTO> toBOrderDetailList, Long referId) {
        Map<Long, BigDecimal> responseFreezeMap = responseFreezeList.stream().collect(Collectors.toMap(CommodityInventoryODTO::getCommodityId,CommodityInventoryODTO::getFreezeQuantity,(key1 , key2)-> key2));
        log.warn("包含赠品冻结，一共冻结的商品:{}", responseFreezeMap);

        // 如果赠送的商品数量，没有全部冻结成功。只冻结部分或者赠品一个都没有冻结成功。
        // 则修改订单明细赠品的数量为实际冻结的
        List<FreezeGiftODTO> updateList = new ArrayList<>(); // 赠品5，0 < 冻结数量 < 5
        List<FreezeGiftODTO> deleteList = new ArrayList<>(); // 赠品5，冻结了0
        for(ToBOrderDetailIDTO toBIdto : toBOrderDetailList){
            // responseFreezeQuantity 冻结接口返回的实际冻结的数量
            BigDecimal responseFreezeQuantity = responseFreezeMap.get(toBIdto.getCommodityId());
            // giftNeedQuantity 下单实际需要的赠品数量
            BigDecimal giftNeedQuantity = giftProductMap.get(toBIdto.getCommodityId());
            if(giftNeedQuantity != null && responseFreezeQuantity != null){ //  只处理赠品
                if(responseFreezeQuantity.compareTo(toBIdto.getOrderQuantity()) < 0){
                    // shortFreezeQuantity 缺少冻结的数量.比如订单商品A是10个，赠品A是5个。大仓成功冻结了12个。则 shortFreezeQuantity = 3个
                    BigDecimal shortFreezeQuantity = toBIdto.getOrderQuantity().subtract(responseFreezeQuantity);
                    // giftFreezeQuantity 赠品实际冻结的数量
                    BigDecimal giftFreezeQuantity = giftNeedQuantity.subtract(shortFreezeQuantity);
                    if(giftFreezeQuantity.compareTo(BigDecimal.ZERO) > 0 && giftFreezeQuantity.compareTo(giftNeedQuantity) < 0){
                        FreezeGiftODTO update = new FreezeGiftODTO();
                        update.setCommodityId(toBIdto.getCommodityId());
                        update.setQuantity(giftNeedQuantity);
                        update.setInventoryQuantity(giftFreezeQuantity);
                        update.setStockType(toBIdto.getStockType());
                        updateList.add(update);
                    }else {
                        // giftFreezeQuantity == giftNeedQuantity
                        // 赠品一个都没有冻上，删除订单明细
                        FreezeGiftODTO delete = new FreezeGiftODTO();
                        delete.setCommodityId(toBIdto.getCommodityId());
                        delete.setStockType(toBIdto.getStockType());
                        delete.setQuantity(giftNeedQuantity);
                        delete.setInventoryQuantity(BigDecimal.ZERO);
                        deleteList.add(delete);
                    }
                }
            }
        }

        if(CollectionUtils.isNotEmpty(deleteList)){
            List<Long> commodityIdList = deleteList.stream().map(item -> item.getCommodityId()).collect(Collectors.toList());
            // 删除订单明细
            // t_order_list,t_order_list_gift
            orderListGiftMapper.deleteGiftOrderList(referId, commodityIdList);
            orderListGiftMapper.deleteOrderList(referId, commodityIdList);

            // 更新 t_sub_order.variety_total
            // 删除 t_sub_order_item
            List<OrderInfoODTO> orderInfoList = orderListGiftMapper.selectGiftOrderList(referId, commodityIdList);
            if(CollectionUtils.isNotEmpty(orderInfoList)){
                List<Long> subOrderItemIdList = orderInfoList.stream().map(item -> item.getSubOrderItemId()).collect(Collectors.toList());
                orderListGiftMapper.deleteSubOrderItem(subOrderItemIdList);
                Map<Long, List<OrderInfoODTO>> orderInfoMap = orderInfoList.stream().collect(Collectors.groupingBy(OrderInfoODTO::getSubOrderId));
                orderInfoMap.forEach((item, list) -> {
                    Integer varietyTotal = list.get(0).getVarietyTotal();
                    if(varietyTotal > list.size()){ // 更新subOrder的varietyTotal
                        SubOrder subOrder = new SubOrder();
                        subOrder.setId(item);
                        subOrder.setVarietyTotal(varietyTotal - list.size());
                        subOrder.setUpdateTime(new Date());
                        subOrderMapper.updateByPrimaryKeySelective(subOrder);
                    }else {
                        //删除subOrder
                        subOrderMapper.deleteByPrimaryKey(item);
                    }
                });
            }
            // 记录缺货信息
            List<BStockShortResponseVO> responseVOList =BeanCloneUtils.copyTo(deleteList, BStockShortResponseVO.class);
            bStockService.giftShort(orderDto.getStoreId(), orderDto.getOrderType(), "赠品不足", responseVOList, orderDto.getUserId());
        }

        if(CollectionUtils.isNotEmpty(updateList)){
            // 更新订单明细
            //更新t_order_list、t_order_list_gift、t_sub_order_item的赠品数量
            List<Long> commodityIdList = new ArrayList<>();
            updateList.forEach(item -> {
                OrderList orderList = new OrderList();
                orderList.setCommodityNum(item.getInventoryQuantity());
                Example orderListUpdateExample = new Example(OrderList.class);
                orderListUpdateExample.createCriteria().andEqualTo("orderId", referId)
                        .andEqualTo("type", ProductTypeEnums.GIFT.getCode())
                        .andEqualTo("commodityId", item.getCommodityId());
                orderListMapper.updateByExampleSelective(orderList, orderListUpdateExample);

                OrderListGift orderListGift = new OrderListGift();
                orderListGift.setCommodityNum(item.getInventoryQuantity());
                Example orderListGiftUpdateExample = new Example(OrderListGift.class);
                orderListGiftUpdateExample.createCriteria().andEqualTo("orderId", referId)
                        .andEqualTo("type", ProductTypeEnums.GIFT.getCode())
                        .andEqualTo("commodityId", item.getCommodityId());
                orderListGiftMapper.updateByExampleSelective(orderListGift, orderListGiftUpdateExample);

                commodityIdList.add(item.getCommodityId());
            });

            List<OrderInfoODTO> orderInfoList = orderListGiftMapper.selectGiftOrderList(referId, commodityIdList);
            Map<Long, BigDecimal> updateMap = updateList.stream().collect(Collectors.toMap(FreezeGiftODTO::getCommodityId,FreezeGiftODTO::getInventoryQuantity,(key1 , key2)-> key2));
            if(CollectionUtils.isNotEmpty(orderInfoList)){
                orderInfoList.forEach(item -> {
                    BigDecimal inventoryQuantity = updateMap.get(item.getCommodityId());
                    if( inventoryQuantity != null){
                        SubOrderItem subOrderItem = new SubOrderItem();
                        subOrderItem.setId(item.getSubOrderItemId());
                        subOrderItem.setQuantity(inventoryQuantity);
                        subOrderItem.setUpdateTime(new Date());
                        subOrderItemMapper.updateByPrimaryKeySelective(subOrderItem);
                    }
                });
            }

            // 记录缺货信息
            List<BStockShortResponseVO> responseVOList =BeanCloneUtils.copyTo(updateList, BStockShortResponseVO.class);
            bStockService.giftShort(orderDto.getStoreId(), orderDto.getOrderType(), "赠品不足", responseVOList, orderDto.getUserId());
        }

    }

    /**
     * 组装冻结明细list
     * @param giftProductMap
     * @param freezeODTOList
     * @return
     */
    public List<ToBOrderDetailIDTO> getToBOrderDetailList(Map<Long, BigDecimal> giftProductMap, List<FreezeGiftODTO> freezeODTOList) {
        List<ToBOrderDetailIDTO> toBOrderDetailList = new ArrayList<>();
        // 冻结的商品包含赠品
        // ①：下单A10个，没赠品：orderQuantity = 10, guaranteedQuantity = null
        // ②：下单A10个，赠送A5个：orderQuantity = 15, guaranteedQuantity = 10
        // ③：下单A10个，赠B5个：orderQuantity = 10, guaranteedQuantity = null
        //                  B： orderQuantity = 5, guaranteedQuantity = 0

        if(!giftProductMap.isEmpty()){
            // 合并数量
            List<FreezeGiftODTO> mergeList = freezeODTOList.stream()
                    .collect(Collectors.toMap(FreezeGiftODTO::getCommodityId, a -> a, (o1, o2)-> {
                        o1.setOrderQuantity(o1.getOrderQuantity().add(o2.getOrderQuantity()));
                        return o1;
                    })).values().stream().collect(Collectors.toList());

            for(FreezeGiftODTO freezeODTO : mergeList){
                ToBOrderDetailIDTO detailIDTO = BeanCloneUtils.copyTo(freezeODTO, ToBOrderDetailIDTO.class);
                //如果当前商品是赠品，判断合计数量是否大于赠品的数量。
                BigDecimal giftQuantity = giftProductMap.get(freezeODTO.getCommodityId());
                if(giftQuantity != null){
                    // 如果大于赠品数量。则合计数量包含了订单商品和赠品数量.那么保底数量就是订单商品的数量
                    if(freezeODTO.getOrderQuantity().compareTo(giftQuantity) > 0){
                        detailIDTO.setGuaranteedQuantity(freezeODTO.getOrderQuantity().subtract(giftQuantity));
                    }
                    // 如果等于赠品数量。则合计数量就是当前赠品数量.那么保底数量就是0
                    if(freezeODTO.getOrderQuantity().compareTo(giftQuantity) == 0){
                        detailIDTO.setGuaranteedQuantity(BigDecimal.ZERO);
                    }
                }
                toBOrderDetailList.add(detailIDTO);
            }
           log.warn("包含赠品冻结,提交的商品 :{}", toBOrderDetailList);
        }else {
            toBOrderDetailList = BeanCloneUtils.copyTo(freezeODTOList, ToBOrderDetailIDTO.class);
        }
        return toBOrderDetailList;
    }

    @NotNull
    public List<FreezeGiftODTO> getFreezeGiftList(OrderDto orderDto) {
        List<FreezeGiftODTO> freezeODTOList = new ArrayList<>();
        orderDto.getItems().forEach(item -> {
            // 只冻结依据大仓的和限量的(限量的需要记录已经下单量)
            //if(StockTypeEnum.WAREHOUSE.getCode().equals(item.getStockType()) ||  StockTypeEnum.LIMIT.getCode().equals(item.getStockType())){
                FreezeGiftODTO freezeGiftODTO = new FreezeGiftODTO();
                freezeGiftODTO.setCommodityId(Long.valueOf(item.getProductId()));
                freezeGiftODTO.setOrderQuantity(item.getProductNum());
                freezeGiftODTO.setStockType(item.getStockType());
                freezeGiftODTO.setType(item.getType());
                freezeODTOList.add(freezeGiftODTO);
            //}
        });
        return freezeODTOList;
    }

}
