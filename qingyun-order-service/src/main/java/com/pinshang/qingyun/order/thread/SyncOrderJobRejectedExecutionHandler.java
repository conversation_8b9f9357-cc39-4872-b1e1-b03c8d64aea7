//package com.pinshang.qingyun.order.thread;
//
//import java.util.concurrent.RejectedExecutionHandler;
//import java.util.concurrent.ThreadPoolExecutor;
//
//public class SyncOrderJobRejectedExecutionHandler implements RejectedExecutionHandler{
//
//	@Override
//	public void rejectedExecution(Runnable r, ThreadPoolExecutor executor) {
//		//队列拒绝,则新增线程处理
//		if(r instanceof SyncOrderJobRunnable){
//			SyncOrderJobRunnable sor =(SyncOrderJobRunnable) r;
//			Thread t =new Thread(r);
//			t.setName("SyncOrderJobRejectedThread-" +sor.getCoverTime());
//			new Thread(r).start();
//		}
//	}
//
//}
