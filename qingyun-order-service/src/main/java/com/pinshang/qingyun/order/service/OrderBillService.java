package com.pinshang.qingyun.order.service;


import com.pinshang.qingyun.base.enums.OrderStatusEnums;
import com.pinshang.qingyun.box.utils.DateTimeUtil;
import com.pinshang.qingyun.box.utils.DateUtil;
import com.pinshang.qingyun.box.utils.SpringUtil;
import com.pinshang.qingyun.order.dto.job.OrderBillStoreBalanceDTO;
import com.pinshang.qingyun.order.dto.job.OrderBillStoreChargingDTO;
import com.pinshang.qingyun.order.mapper.OrderBillMapper;
import com.pinshang.qingyun.order.mapper.entry.job.StoreOrderAmountEntry;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Optional;

@Service
@Slf4j
@RequiredArgsConstructor
public class OrderBillService {

    private final OrderBillMapper orderBillMapper;
    private final String BILL_REMARK_LIKE = "充值";

    public BigDecimal queryOrderBillStoreBalance(String dateStr,Long storeId){

        List<OrderBillStoreBalanceDTO> orderBillStoreBalanceDTOS = this.queryOrderBillList(dateStr, storeId);
        OrderBillStoreBalanceDTO yesterdayBeforeLatestOrderBillStoreBalanceDTO = SpringUtil.isNotEmpty(orderBillStoreBalanceDTOS)? orderBillStoreBalanceDTOS.get(0):null;
        return Optional.ofNullable(yesterdayBeforeLatestOrderBillStoreBalanceDTO).map(OrderBillStoreBalanceDTO::getStoreBalance).orElse(BigDecimal.ZERO);

    }


    public List<OrderBillStoreBalanceDTO> queryOrderBillList(String dateStr,Long storeId){
        return orderBillMapper.queryOrderBillList(dateStr,storeId);
    }

    public List<OrderBillStoreChargingDTO> queryChargingOrderBill(String dateStr, List<Long> storeIdList) {
        return orderBillMapper.queryChargingOrderBill(dateStr,  BILL_REMARK_LIKE,storeIdList);
    }

    public static void main(String[] args) {
        String nowDateStr = DateTimeUtil.getNowDayDate();
        System.out.println(nowDateStr);
        Date nowDate = DateTimeUtil.parse(nowDateStr,DateTimeUtil.YYYY_MM_DD);
        System.out.println(nowDate);
        String beginTime = DateTimeUtil.addDay(nowDate, -30) + " 00:00:00";
        System.out.println(beginTime);
        String endTime = nowDateStr + " 00:00:00";
        System.out.println(endTime);

    }
}
