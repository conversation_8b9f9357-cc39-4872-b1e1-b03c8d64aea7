package com.pinshang.qingyun.order.manage.price;

import com.pinshang.qingyun.box.utils.SpringUtil;
import com.pinshang.qingyun.order.dto.order.storemoney.OrderMoneyConditionDTO;
import com.pinshang.qingyun.order.mapper.StoreSettlementMapper;
import com.pinshang.qingyun.order.mapper.entry.order.OrderRealDeliveryFinishEntry;
import com.pinshang.qingyun.order.mapper.entry.order.OrderRealTotalPriceListEntry;
import com.pinshang.qingyun.order.model.order.StoreSettlement;
import com.pinshang.qingyun.order.service.ShopService;
import com.pinshang.qingyun.order.util.list.ListExtractor;
import com.pinshang.qingyun.settlementTb.dto.StoreRechargeDTO;
import com.pinshang.qingyun.settlementTb.dto.StoreRechargeOrderIDTO;
import com.pinshang.qingyun.settlementTb.service.StoreRechargeClient;
import com.pinshang.qingyun.store.dto.storeType.StoreTypeDescIDTO;
import com.pinshang.qingyun.store.dto.storeType.StoreTypeDescODTO;
import com.pinshang.qingyun.store.service.StoreTypeClient;
import lombok.extern.slf4j.Slf4j;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionSynchronizationAdapter;
import org.springframework.transaction.support.TransactionSynchronizationManager;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
public class StorePriceManage {

    Map<Long, OrderRealDeliveryFinishEntry> deliveryFinishOrderMap;

    Map<Long, OrderMoneyConditionDTO> orderMoneyConditionDTOMap;

    Map<Long, OrderRealDeliveryFinishEntry> needOrderMoneyDealMap;

    StoreSettlementMapper storeSettlementMapper;
    StoreTypeClient storeTypeClient;
    ShopService shopService;
    StoreRechargeClient storeRechargeClient;

    public StorePriceManage(Map<Long, OrderRealDeliveryFinishEntry> deliveryFinishOrderMap
            , Map<Long, OrderMoneyConditionDTO> orderMoneyConditionDTOMap
            , StoreSettlementMapper storeSettlementMapper
            , StoreTypeClient storeTypeClient
            , ShopService shopService
            , StoreRechargeClient storeRechargeClient) {
        this.deliveryFinishOrderMap = deliveryFinishOrderMap;
        this.orderMoneyConditionDTOMap = orderMoneyConditionDTOMap;
        this.storeSettlementMapper = storeSettlementMapper;
        this.storeTypeClient = storeTypeClient;
        this.shopService = shopService;
        this.storeRechargeClient = storeRechargeClient;

    }

    @Transactional(rollbackFor = Exception.class)
    public void orderMoneyDealDiffCondition() {

        boolean needChangeFlag = checkIfNeedChange();
        if (!needChangeFlag) {
            return;
        }

        List<OrderRealDeliveryFinishEntry> deliveryFinishEntryList = new ArrayList<>(needOrderMoneyDealMap.values());

        List<Long> storeIds = ListExtractor.extractToList(deliveryFinishEntryList, OrderRealDeliveryFinishEntry::getStoreId);
        Map<Long, Boolean> jmShopMap = shopService.listJmShop(storeIds);


        Map<Long, OrderRealDeliveryFinishEntry> notJmOrderMap = new HashMap<>();

        needOrderMoneyDealMap.forEach(
                (key, value) -> {
                    OrderMoneyConditionDTO orderMoneyConditionDTO = orderMoneyConditionDTOMap.get(key);
                    boolean jmFlag = jmShopMap.get(value.getStoreId()) == null ? false : jmShopMap.get(value.getStoreId());
                    if (jmFlag) {
                        if (orderMoneyConditionDTO.getDiffAmount().compareTo(BigDecimal.ZERO) != 0) {
                            //多发扣款、少发退款
                            TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronizationAdapter() {
                                @Override
                                public void afterCommit() {
                                    BigDecimal diffAmount = orderMoneyConditionDTO.getDiffAmount();
                                    if (diffAmount.compareTo(BigDecimal.ZERO) > 0) {
                                        handleShotDeliveryRefund(orderMoneyConditionDTO.getDiffAmount(), value);
                                    } else {
                                        handleOverDeliveryDeduction(orderMoneyConditionDTO.getDiffAmount(), value);
                                    }
                                }
                            });
                        }
                    } else {
                        notJmOrderMap.put(key, value);
                    }

                }
        );

        if (SpringUtil.isNotEmpty(notJmOrderMap)) {
            List<OrderRealDeliveryFinishEntry> notJmDeliveryFinishEntryList = new ArrayList<>(notJmOrderMap.values());
            List<StoreSettlement> storeSettlements = storeSettlementMapper.listStoreSettlementByStoreIds(ListExtractor.extractToList(notJmDeliveryFinishEntryList, OrderRealDeliveryFinishEntry::getStoreId));
            Map<Long, StoreSettlement> storeSettlementMap = storeSettlements.stream().collect(Collectors.toMap(StoreSettlement::getStoreId, Function.identity()));

            List<Long> storeTypeIds = notJmDeliveryFinishEntryList.stream().map(OrderRealDeliveryFinishEntry::getStoreTypeId).collect(Collectors.toList());
            StoreTypeDescIDTO storeTypeDescIDTO = new StoreTypeDescIDTO();
            storeTypeDescIDTO.setIdList(storeTypeIds);

            List<StoreTypeDescODTO> storeTypeDescODTOS = storeTypeClient.selectStoreTypeData(storeTypeDescIDTO);
            Map<Long, StoreTypeDescODTO> storeTypeDescIdMap = storeTypeDescODTOS.stream().collect(Collectors.toMap(StoreTypeDescODTO::getId, Function.identity()));

            notJmOrderMap.forEach(
                    (key, value) -> {
                        OrderMoneyConditionDTO orderMoneyConditionDTO = orderMoneyConditionDTOMap.get(key);

                        boolean collectStatus = storeSettlementMap.get(value.getStoreId()).getCollectStatus();
                        boolean settleBillReturnAmountStateFlag = storeTypeDescIdMap.get(value.getStoreTypeId()) == null ? false : (0 == storeTypeDescIdMap.get(value.getStoreTypeId()).getSettleBillReturnAmountState());
                        boolean settleBillCalcTypeFlag = storeTypeDescIdMap.get(value.getStoreTypeId()) == null ? false : (1 == storeTypeDescIdMap.get(value.getStoreTypeId()).getSettleBillCalcType());
                        if (collectStatus && settleBillReturnAmountStateFlag && settleBillCalcTypeFlag && orderMoneyConditionDTO.getPureUnderPaymentAmount().compareTo(BigDecimal.ZERO) > 0) {
                            //处理短交
                            TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronizationAdapter() {
                                @Override
                                public void afterCommit() {
                                    handleShotDeliveryRefund(orderMoneyConditionDTO.getPureUnderPaymentAmount(), value);
                                }
                            });
                        }
                    }
            );
        }
    }

    private boolean checkIfNeedChange() {

        needOrderMoneyDealMap = new HashMap<>();
        deliveryFinishOrderMap.forEach(
                (key, value) -> {
                    OrderMoneyConditionDTO orderMoneyConditionDTO = orderMoneyConditionDTOMap.get(key);
                    if ((orderMoneyConditionDTO
                            .getDiffAmount()
                            .compareTo(BigDecimal.ZERO) != 0)
                            || (orderMoneyConditionDTO
                            .getPureUnderPaymentAmount()
                            .compareTo(BigDecimal.ZERO) != 0)) {
                        needOrderMoneyDealMap.put(key, value);
                    }
                }
        );

        if (SpringUtil.isEmpty(needOrderMoneyDealMap)) {
            return false;
        }

        return true;
    }


    private void handleShotDeliveryRefund(BigDecimal money, OrderRealDeliveryFinishEntry orderRealDeliveryFinishEntry) {
        if (money != null && money.compareTo(BigDecimal.ZERO) > 0) {
            log.warn("监听出库消息 订单：{} 子单出库完后 存在短交【money={}】 进行退款 短交退款执行开始...", orderRealDeliveryFinishEntry.getOrderCode(), money);
            StoreRechargeDTO dto = new StoreRechargeDTO();
            dto.setMoney(money.doubleValue());
            dto.setStoreId(orderRealDeliveryFinishEntry.getStoreId());
            dto.setTradeCode(orderRealDeliveryFinishEntry.getOrderCode());
            dto.setRemark("<--短交退款：" + orderRealDeliveryFinishEntry.getOrderCode() + "-->");
            try {
                storeRechargeClient.addRecharge(dto);
            } catch (Exception e) {
                log.warn("is method markWriteBackRealSubOrderDoneStatus storeRechargeClient.addRecharge(dto) fegin远程调用失败 error:{}", e);
            }
            log.warn("监听出库消息 订单：{} 子单出库完后 存在短交【money={}】 进行退款 短交退款执行结束...", orderRealDeliveryFinishEntry.getOrderCode(), money);
        }
    }

    private void handleOverDeliveryDeduction(BigDecimal money, OrderRealDeliveryFinishEntry orderRealDeliveryFinishEntry) {
        if (money != null && money.compareTo(BigDecimal.ZERO) < 0) {
            log.warn("监听出库消息 订单：{} 子单出库完后 存在多发【money={}】 进行扣款 多发扣款执行开始...", orderRealDeliveryFinishEntry.getOrderCode(), money);
            StoreRechargeOrderIDTO dto = new StoreRechargeOrderIDTO();
            dto.setOrderAmount(money.negate());
            dto.setStoreId(orderRealDeliveryFinishEntry.getStoreId());
            dto.setTradeCode(orderRealDeliveryFinishEntry.getOrderCode());
            dto.setRemark("<--多发扣款：" + orderRealDeliveryFinishEntry.getOrderCode() + "-->");
            try {
                storeRechargeClient.deductionAccount(dto);
            } catch (Exception e) {
                log.warn("is method markWriteBackRealSubOrderDoneStatus storeRechargeClient.deductionAccount(dto) fegin远程调用失败 error:{}", e);
            }
            log.warn("监听出库消息 订单：{} 子单出库完后 存在多发【money={}】 进行扣款 多发扣款执行结束...", orderRealDeliveryFinishEntry.getOrderCode(), money);
        }
    }

}
