package com.pinshang.qingyun.order.controller;

import com.github.pagehelper.PageInfo;
import com.pinshang.qingyun.base.api.TokenInfo;
import com.pinshang.qingyun.base.util.FastThreadLocalUtil;
import com.pinshang.qingyun.order.dto.ShopReceiveSettingIDTO;
import com.pinshang.qingyun.order.dto.ShopReceiveSettingODTO;
import com.pinshang.qingyun.order.service.ShopReceiveSettingService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

/**
 * @Author: sk
 * @Date: 2020/3/17
 */
@RestController
@RequestMapping("/shopReceiveSetting")
@Api("鲜道收货管理")
public class ShopReceiveSettingController {

      @Autowired
      private ShopReceiveSettingService shopReceiveSettingService;


      /**
       *查看门店收货设置(分页)
       * @return
       */
      @ApiOperation(value = "查看门店收货设置(分页)", notes = "查看门店收货设置(分页)", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
      @RequestMapping(value = "/getXdReceiveDocCommodityInfo", method = RequestMethod.POST)
      public PageInfo<ShopReceiveSettingODTO> getShopReceiveSettingPage(@RequestBody ShopReceiveSettingIDTO shopReceiveSettingIDTO) {
            return  shopReceiveSettingService.getShopReceiveSettingPage(shopReceiveSettingIDTO);
      }


      @ApiOperation(value = "查询收货设置", notes = "查询收货设置", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
      @RequestMapping(value = "/getShopReceiveSettingByShopId",method = RequestMethod.GET)
      public ShopReceiveSettingODTO getShopReceiveSettingByShopId(@RequestParam(value = "shopId",required = false) Long shopId) {
            TokenInfo tokenInfo = FastThreadLocalUtil.getQY();
            return shopReceiveSettingService.getShopReceiveSettingByShopId(shopId);
      }

      @ApiOperation(value = "切换模式", notes = "切换模式", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
      @RequestMapping(value = "/changeReceiveType",method = RequestMethod.POST)
      public Boolean changeReceiveType(@RequestParam(value = "shopId",required = false) Long shopId,@RequestParam(value = "receiveType",required = false) Integer receiveType) {
            return shopReceiveSettingService.changeReceiveType(shopId,receiveType);
      }


      @ApiOperation(value = "查看门店收货设置log(分页)", notes = "查看门店收货设置log(分页)", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
      @RequestMapping(value = "/getShopReceiveSettingLog", method = RequestMethod.POST)
      public PageInfo<ShopReceiveSettingODTO> getShopReceiveSettingLog(@RequestBody ShopReceiveSettingIDTO shopReceiveSettingIDTO) {
            return  shopReceiveSettingService.getShopReceiveSettingLog(shopReceiveSettingIDTO);
      }

}
