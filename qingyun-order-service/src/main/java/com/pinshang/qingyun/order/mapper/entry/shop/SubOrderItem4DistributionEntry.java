package com.pinshang.qingyun.order.mapper.entry.shop;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 *
 * <AUTHOR>
 * @Date 2018/4/16 17:41
 */
@Data
public class SubOrderItem4DistributionEntry implements Serializable {

    private static final long serialVersionUID = -8210721131236160701L;
    private Long commodityId;
    private BigDecimal price;
    /**
     * 实发数量(在拣货出库后会更新到 real_delivery_quantity 字段里)
     */
    private BigDecimal quantity;

    private BigDecimal requireQuantity;
}
