package com.pinshang.qingyun.order.controller;

import com.pinshang.qingyun.order.service.CommodityMonitorService;
import com.pinshang.qingyun.order.vo.commodity.CommodityMonitorRequestVo;
import com.pinshang.qingyun.order.vo.commodity.CommodityMonitorRespVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @Date 2018/10/11 10:36
 */
@RestController
@RequestMapping("/commodityMonitor")
public class CommodityMonitorController {
    @Autowired
    private CommodityMonitorService commodityMonitorService;

    @RequestMapping(value = "/orderInfo",method = RequestMethod.POST)
    public CommodityMonitorRespVo orderInfo(@RequestBody CommodityMonitorRequestVo vo){
        return commodityMonitorService.orderInfo(vo);
    }
}
