package com.pinshang.qingyun.order.service;

import com.pinshang.qingyun.order.mapper.ShoppingCartItemMapper;
import com.pinshang.qingyun.order.mapper.ShoppingCartMapper;
import com.pinshang.qingyun.order.mapper.entry.order.ShoppingCartEntry;
import com.pinshang.qingyun.order.model.order.ShoppingCart;
import com.pinshang.qingyun.order.model.order.ShoppingCartItem;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.entity.Example;

import java.util.List;

/**
 * @Author: sk
 * @Date: 2023/2/6
 */
@Service
public class ShoppingCartDeleteService {

    @Autowired
    ShoppingCartItemMapper itemMapper;
    @Autowired
    ShoppingCartMapper shoppingCartMapper;


    @Transactional(rollbackFor = Exception.class)
    public Boolean deleteShoppingCartAndItems(List<ShoppingCartEntry> entry){
        entry.forEach(e->{
            Example itemEx = new Example(ShoppingCartItem.class);
            itemEx.createCriteria().andEqualTo("shoppingCartId", e.getId());
            itemMapper.deleteByExample(itemEx);

            Example cartEx = new Example(ShoppingCart.class);
            cartEx.createCriteria().andEqualTo("id", e.getId());
            shoppingCartMapper.deleteByExample(cartEx);
        });

        return Boolean.TRUE;
    }


    @Async
    public Boolean deleteShoppingCart(Long shoppingCartId){
        Example itemEx = new Example(ShoppingCartItem.class);
        itemEx.createCriteria().andEqualTo("shoppingCartId", shoppingCartId);
        itemMapper.deleteByExample(itemEx);
        return Boolean.TRUE;
    }
}
