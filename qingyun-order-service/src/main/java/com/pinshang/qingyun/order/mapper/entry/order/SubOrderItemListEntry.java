package com.pinshang.qingyun.order.mapper.entry.order;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.pinshang.qingyun.renderer.annotation.FieldRender;
import com.pinshang.qingyun.renderer.constant.RenderFieldHelper;
import com.pinshang.qingyun.renderer.enums.FieldTypeEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class SubOrderItemListEntry {
	@ApiModelProperty("商品ID")
	private String commodityId;
	@ApiModelProperty("商品编码")
	private String commodityCode;
	@ApiModelProperty("商品名称")
	private String commodityName;
	@ApiModelProperty("商品规格")
	private String commoditySpec;
	@ApiModelProperty("计量单位名称")
	@FieldRender(fieldType = FieldTypeEnum.COMMODITY, keyName = "commodityId", fieldName = RenderFieldHelper.Commodity.commodityUnit)
	private String commodityUnitName;
	@ApiModelProperty("数量")
	private String quantity;
	@ApiModelProperty("份数")
	private Integer number;
	@ApiModelProperty("组合商品转换状态：0=无转换，1=有转换")
	private Integer convertStatus;
	@ApiModelProperty("组合商品转换的最小单位商品ID")
	@JsonSerialize(using = ToStringSerializer.class)
	private Long targetCommodityId;
	@ApiModelProperty("组合商品转换的商品编码")
	private String targetCommodityCode;
	@ApiModelProperty("组合商品转换的商品名称")
	private String targetCommodityName;
	@ApiModelProperty("组合商品转换的商品规格")
	private String targetCommoditySpec;
	@ApiModelProperty("组合商品转换的商品计量单位名称")
	@FieldRender(fieldType = FieldTypeEnum.COMMODITY, keyName = "targetCommodityId", fieldName = RenderFieldHelper.Commodity.commodityUnit)
	private String targetCommodityUnitName;
	@ApiModelProperty("组合商品转换的数量")
	private String targetQuantity;
	@ApiModelProperty("组合商品转换的份数")
	private Integer targetNumber;
}
