package com.pinshang.qingyun.order.controller.xda;

import com.github.pagehelper.PageInfo;
import com.pinshang.qingyun.base.api.TokenInfo;
import com.pinshang.qingyun.base.util.FastThreadLocalUtil;
import com.pinshang.qingyun.order.dto.xda.tda.*;
import com.pinshang.qingyun.order.service.tda.XdaReturnOrderService;
import com.pinshang.qingyun.order.service.tda.XdaReturnOrderServiceForAdmin;
import com.pinshang.qingyun.renderer.annotation.MethodRender;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * <p>
 * 退货单
 * </p>
 *
 * <AUTHOR> shenyang
 * @version : 1.0.0
 * @history : modify history
 * <author>              <time>              <version>              <desc>
 * @since : 2024/05/10 11:11
 */
@RestController
@RequestMapping("/xda/return")
@Api(value = "通达退货单相关api", tags = "xda-return", description = "通达退货单相关接口")
public class XdaReturnOrderController {
    @Autowired
    private XdaReturnOrderService xdaReturnOrderService;
    @Autowired
    private XdaReturnOrderServiceForAdmin xdaReturnOrderServiceForAdmin;

    /***
     * 生成退货/少货单
     * 来源：app发起，后台新增投诉，通达销售的订单配送失败，通达销售的取消物流拦截成功的订单
     */
    @ApiOperation(value = "生成退货/少货单", notes = "生成退货/少货单")
    @PostMapping("/generateReturnOrder")
    public Boolean generateReturnOrder(@RequestBody SaveReturnOrderODTO dto) {
        return xdaReturnOrderService.generateReturnOrder(dto);
    }

    /***
     * 退货/少货单列表
     */
    @MethodRender
    @ApiOperation(value = "退货单列表", notes = "退货单列表")
    @PostMapping("/queryReturnOrderList")
    public PageInfo<ReturnOrderODTO> queryReturnOrderList(@RequestBody ReturnOrderSearchIDTO dto) {
        return xdaReturnOrderServiceForAdmin.queryReturnOrderList(dto);
    }


    /***
     * 退货/少货单明细
     */
    @MethodRender
    @ApiOperation(value = "退货/少货单明细", notes = "退货/少货单明细")
    @GetMapping("/queryReturnOrderItemList/{returnOrderId}")
    public ReturnOrderDetailODTO queryReturnOrderItemList(@PathVariable("returnOrderId") Long returnOrderId) {
        return xdaReturnOrderServiceForAdmin.queryReturnOrderItemList(returnOrderId);
    }


    /***
     * 退货/少货单审核
     */
    @ApiOperation(value = "退货/少货单审核", notes = "退货/少货单审核")
    @PostMapping("/auditReturnOrder")
    public Boolean auditReturnOrder(@RequestBody AuditReturnOrderIDTO dto) {
        TokenInfo tokenInfo = FastThreadLocalUtil.getQY();
        dto.setCheckUserId(tokenInfo.getUserId());
        return xdaReturnOrderServiceForAdmin.auditReturnOrder(dto);
    }

    /***
     * 大仓待确认退货单列表
     */
    @MethodRender
    @ApiOperation(value = "大仓待确认退货单列表", notes = "大仓待确认退货单列表")
    @PostMapping("/queryWarehousePendingList")
    public PageInfo<WarehousePendingODTO> queryWarehousePendingList(@RequestBody WarehousePendingSearchIDTO dto) {
        return xdaReturnOrderServiceForAdmin.queryWarehousePendingList(dto);
    }

    /***
     * 大仓待确认退货单明细
     */
    @MethodRender
    @ApiOperation(value = "大仓待确认退货单明细", notes = "大仓待确认退货单明细")
    @GetMapping("/queryWarehousePendingItemList/{returnOrderId}")
    public WarehousePendingDetailODTO saveComplaintCommodity(@PathVariable Long returnOrderId) {
        return xdaReturnOrderServiceForAdmin.queryWarehousePendingItemList(returnOrderId);
    }

    /***
     * 大仓确认退货单
     */
    @ApiOperation(value = "大仓确认退货单", notes = "大仓确认退货单")
    @PostMapping("/confirmReturnOrder")
    public Boolean confirmReturnOrder(@RequestBody ConfirmReturnOrderIDTO dto) {
        TokenInfo tokenInfo = FastThreadLocalUtil.getQY();
        dto.setConfirmUserId(tokenInfo.getUserId());
        return xdaReturnOrderServiceForAdmin.confirmReturnOrder(dto);
    }

    /***
     * 取消退货/少货单
     */
    @ApiOperation(value = "取消退货/少货单", notes = "取消退货/少货单")
    @PostMapping("/cancelReturnOrder")
    public Boolean cancelReturnOrder(@RequestBody CancelReturnOrderIDTO dto) {
        return xdaReturnOrderServiceForAdmin.cancelReturnOrder(dto);
    }

    /***
     * 退货/少货单 同步
     * 同步到投诉单列表 供结算使用
     */
    @ApiOperation(value = "退货/少货单 同步", notes = "同步到投诉单列表 供结算使用")
    @PostMapping("/syncReturnOrder")
    public void syncReturnOrder(@RequestBody SyncReturnOrderIDTO dto) {
        xdaReturnOrderServiceForAdmin.syncReturnOrder2Complaint(dto);
    }

    /***
     * 查询单个退货单
     */
    @MethodRender
    @ApiOperation(value = "退货单列表", notes = "查询单个退货单")
    @PostMapping("/queryReturnOrderByReturnOrderSeq")
    public ReturnOrderODTO queryReturnOrderByReturnOrderSeq(@RequestParam("returnOrderSeq") String returnOrderSeq) {
        return xdaReturnOrderServiceForAdmin.queryReturnOrderByReturnOrderSeq(returnOrderSeq);
    }
}
