package com.pinshang.qingyun.order.listener;

import com.pinshang.qingyun.order.service.WeChatSendMessageService;
import com.pinshang.qingyun.order.service.xda.v4.XdaPreOrderService;
import com.pinshang.qinyun.cache.utils.RedisDelayQueueHandle;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @Author: sk
 * @Date: 2022/10/18
 */
@Component
@Slf4j
public class XdaPreOrderDelayFreed implements RedisDelayQueueHandle<String> {
    @Autowired
    private XdaPreOrderService xdaPreOrderService;
    @Autowired
    private WeChatSendMessageService weChatSendMessageService;

    @Override
    public void execute(String occupyCode) {
        log.warn("鲜达预订单超时自动取消,预订单号 {}", occupyCode);
        try {
            xdaPreOrderService.xdaPreOrderAutoCancel(occupyCode);
        }catch (Exception e){
            log.error("鲜达预订单超时自动取消失败,订单号 {}", occupyCode);
            weChatSendMessageService.sendWeChatMessage("鲜达预订单超时自动取消失败,订单号 " + occupyCode);
        }
    }
}
