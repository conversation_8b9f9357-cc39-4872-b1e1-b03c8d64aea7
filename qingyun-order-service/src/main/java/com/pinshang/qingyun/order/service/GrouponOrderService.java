package com.pinshang.qingyun.order.service;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.pinshang.qingyun.base.enums.order.OrderTypeEnum;
import com.pinshang.qingyun.base.page.TablePageInfo;
import com.pinshang.qingyun.box.utils.BeanCloneUtils;
import com.pinshang.qingyun.box.utils.BeanUtil;
import com.pinshang.qingyun.box.utils.DateUtil;
import com.pinshang.qingyun.box.utils.JsonUtil;
import com.pinshang.qingyun.order.dto.GroupOrderIDTO;
import com.pinshang.qingyun.order.dto.GroupOrderODTO;
import com.pinshang.qingyun.order.dto.GrouponCommodtyOrderDTO;
import com.pinshang.qingyun.order.enums.GrouponTypeEnum;
import com.pinshang.qingyun.order.mapper.CommodityMapper;
import com.pinshang.qingyun.order.mapper.ShopMapper;
import com.pinshang.qingyun.order.mapper.entry.commodity.CommodityBasicEntry;
import com.pinshang.qingyun.order.mapper.groupon.GrouponOrderCMapper;
import com.pinshang.qingyun.order.mapper.groupon.GrouponOrderLogMapper;
import com.pinshang.qingyun.order.model.groupon.GrouponOrdeCustomModel;
import com.pinshang.qingyun.order.util.ThreadLocalUtils;
import com.pinshang.qingyun.order.vo.commodity.CommodityVO;
import com.pinshang.qingyun.order.vo.order.OrderDto;
import com.pinshang.qingyun.xd.wms.dto.groupon.GrouponCommodityIDTO;
import com.pinshang.qingyun.xd.wms.dto.groupon.GrouponCommodityODTO;
import com.pinshang.qingyun.xd.wms.service.GrouponClient;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @Author: sk
 * @Date: 2020/12/18
 */
@Service
@Slf4j
public class GrouponOrderService {

    @Autowired
    private GrouponClient grouponClient;
    @Autowired
    private GrouponOrderLogMapper grouponOrderLogMapper;
    @Autowired
    private GrouponOrderCMapper grouponOrderCMapper;
    @Autowired
    private ShopMapper shopMapper;
    @Lazy
    @Autowired
    private MdShopOrderSettingService mdShopOrderSettingService;
    @Autowired
    private GrouponOrderSaveService grouponOrderSaveService;
    @Autowired
    private CommodityMapper commodityMapper;
    @Autowired
    private CommonService commonService;
    @Autowired
    private WeChatSendMessageService weChatSendMessageService;
    @Autowired
    private OrderSplitService orderSplitService;
    @Lazy
    @Autowired
    private CloudGrouponOrderService cloudGrouponOrderService;
    /**
     * 社区团购提交订单
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public Boolean createGrouponOrder(String timeStamp) {
        // 执行云超团购下单
        cloudGrouponOrderService.createCloudGrouponOrder(timeStamp);

        // 清美团购
        //createQingmeiGrouponOrder(timeStamp);
        return true;
    }


    /**
     * 清美团购
     * @param timeStamp
     * @return
     */
    public Boolean createQingmeiGrouponOrder(String timeStamp){

        String beginTime = timeStamp + " 00:00:00";
        String endTime = timeStamp + " 23:59:59";

        // 首先查询已经提交订单的 groupIdList
        List<Long> dbGroupIdList = grouponOrderLogMapper.queryGrouponOrderLog(beginTime,endTime,GrouponTypeEnum.QINGMEI.getCode());

        // 获取团购信息
        GrouponCommodityIDTO groupIDTO = new GrouponCommodityIDTO();
        groupIDTO.setGrouponIdList(dbGroupIdList);
        groupIDTO.setBeginTime(beginTime);
        groupIDTO.setEndTime(endTime);
        List<GrouponCommodityODTO> grouponCommodityODTOList = grouponClient.queryGrouponCommodityList(groupIDTO);


        if(CollectionUtils.isEmpty(grouponCommodityODTOList)){
            return false;
        }

        List<GrouponCommodtyOrderDTO> groupCommodityList = BeanCloneUtils.copyTo(grouponCommodityODTOList,GrouponCommodtyOrderDTO.class);

        // 设置storeId、物流模式、供应商、仓库
        orderSplitService.setGroupOrderSetting(groupCommodityList);

        // 转成OrderDto对象
        List<OrderDto> orderDtoList = orderSplitService.convertToOrderDto(groupCommodityList, "团购" , true);

        // 记录团购id 日志
        List<Long> insertGroupIdList = groupCommodityList.stream().map(item -> item.getGrouponId()).collect(Collectors.toList());
        orderSplitService.insertGrouponOrderLog(insertGroupIdList, GrouponTypeEnum.QINGMEI.getCode());
        // 记录C端团购信息
        insertGrouponOrderC(groupCommodityList);

        ThreadLocalUtils.setGroupOrder(true);
        ThreadLocalUtils.setOrderType(OrderTypeEnum.GROUP_AUTO_ORDER.getCode());
        // 循环创建订单
        for(OrderDto orderDto : orderDtoList){
            // 保存团购订单
            try{
                grouponOrderSaveService.saveGroupOrder(orderDto, null);
            }catch (Throwable e){
                log.error("保存团购订单异常:" + JsonUtil.java2json(orderDto) + e.getMessage());
                // 异常发送微信消息
                weChatSendMessageService.sendWeChatMessage("保存团购订单异常");
            }
        }
        ThreadLocalUtils.remove();
        return true;
    }

    /**
     * 记录C端团购信息
     * @param groupCommodityList
     */
    public void insertGrouponOrderC(List<GrouponCommodtyOrderDTO> groupCommodityList){
        List<GrouponOrdeCustomModel> list = new ArrayList<>();
        for(GrouponCommodtyOrderDTO odto:groupCommodityList){
            GrouponOrdeCustomModel cModel = new GrouponOrdeCustomModel();
            BeanUtils.copyProperties(odto,cModel);
            cModel.setPrice(odto.getGroupPrice());
            cModel.setArrivalTime(DateUtil.parseDate(odto.getOrderTime(),"yyyy-MM-dd"));
            cModel.setCreateId(-1L);
            cModel.setCreateTime(new Date());
            list.add(cModel);
        }
        grouponOrderCMapper.insertList(list);
    }

    /**
     * B 端团购订单日汇总
     * @param orderTime
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public Boolean grouponOrderDay(String orderTime) {
        // 先删除
        grouponOrderLogMapper.deleteGrouponOrderDay(orderTime);
        // 新增
        grouponOrderLogMapper.insertGrouponOrderDay(orderTime);

        return Boolean.TRUE;
    }

    /**
     * 商品、门店订货分析表(团购)
     * @param idto
     * @return
     */
    public TablePageInfo<GroupOrderODTO> shopCommodityGroupOrder(GroupOrderIDTO idto) {

        PageInfo<GroupOrderODTO> pageInfo = PageHelper.startPage(idto.getPageNo(), idto.getPageSize()).doSelectPageInfo(() ->{
            grouponOrderLogMapper.commodityGroupOrder(idto);
        });

        TablePageInfo tablePageInfo = BeanUtil.pageInfo2TablePageInfo(pageInfo, TablePageInfo.class);
        List<GroupOrderODTO> list = pageInfo.getList();

        if(CollectionUtils.isNotEmpty(list)){
            Map<String, CommodityBasicEntry> commMap = new HashMap<>();
            Map<Long,String> barCodeMap = new HashMap<>();

            if(idto.getGroupType() == 1){
                List<Long> commodityIdList = list.stream().map(item -> Long.valueOf(item.getCommodityId())).collect(Collectors.toList());
                CommodityVO vo = new CommodityVO();
                vo.setCommodityIdList(commodityIdList);
                List<CommodityBasicEntry> basicEntryList = commodityMapper.findCommodityBasicListByParam(vo);
                commMap = basicEntryList.stream().collect(Collectors.toMap(CommodityBasicEntry::getCommodityId, Function.identity()));
                barCodeMap = commonService.getCommodityBarCodeMap(commodityIdList,null);
            }

            if(idto.getGroupType() == 1) {
                for(GroupOrderODTO odto : list){
                    CommodityBasicEntry basicEntry = commMap.get(odto.getCommodityId());
                    if (basicEntry != null) {
                        BeanUtils.copyProperties(basicEntry, odto);
                    }

                    String barCodes = barCodeMap.get(Long.valueOf(odto.getCommodityId()));
                    odto.setBarCodes(barCodes);
                    odto.setBarCode(barCodes != null ? barCodes.split(",")[0] : "");
                }
            }
            GroupOrderODTO header = grouponOrderLogMapper.commodityGroupOrderSum(idto);
            tablePageInfo.setHeader(header);
        }
        return tablePageInfo;
    }
}
