package com.pinshang.qingyun.order.service;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.pinshang.qingyun.base.api.TokenInfo;
import com.pinshang.qingyun.base.configure.expand.QYAssert;
import com.pinshang.qingyun.base.enums.IogisticsModelEnums;
import com.pinshang.qingyun.base.enums.SaleReturnReasonEnums;
import com.pinshang.qingyun.base.enums.shop.ManagementModeEnums;
import com.pinshang.qingyun.base.enums.xd.StockOutTypeEnums;
import com.pinshang.qingyun.base.enums.xd.StockQualityInTypeEnums;
import com.pinshang.qingyun.base.enums.xd.StorageAreaEnum;
import com.pinshang.qingyun.base.util.FastThreadLocalUtil;
import com.pinshang.qingyun.box.utils.DateUtil;
import com.pinshang.qingyun.box.utils.SpringUtil;
import com.pinshang.qingyun.box.utils.StringUtil;
import com.pinshang.qingyun.common.dto.DictionaryODTO;
import com.pinshang.qingyun.common.service.CodeClient;
import com.pinshang.qingyun.common.service.DictionaryClient;
import com.pinshang.qingyun.order.dto.XdSaleReturnIDTO;
import com.pinshang.qingyun.order.dto.XdSaleReturnODTO;
import com.pinshang.qingyun.order.dto.order.SaleReturnDetailPageIDTO;
import com.pinshang.qingyun.order.dto.order.SaleReturnDetailPageODTO;
import com.pinshang.qingyun.order.enums.SaleReturnStatusEnums;
import com.pinshang.qingyun.order.mapper.*;
import com.pinshang.qingyun.order.mapper.entry.MdShopOrderSettingEntry;
import com.pinshang.qingyun.order.mapper.entry.commodity.CommodityBasicEntry;
import com.pinshang.qingyun.order.mapper.entry.order.*;
import com.pinshang.qingyun.order.model.order.SaleReturn;
import com.pinshang.qingyun.order.model.order.SaleReturnItem;
import com.pinshang.qingyun.order.model.order.SaleReturnOrderPic;
import com.pinshang.qingyun.order.model.order.User;
import com.pinshang.qingyun.order.model.shop.Shop;
import com.pinshang.qingyun.order.util.StallUtils;
import com.pinshang.qingyun.order.vo.commodity.CommodityVO;
import com.pinshang.qingyun.order.vo.order.*;
import com.pinshang.qingyun.shop.admin.dto.ConsignmentSupplierInfoIDTO;
import com.pinshang.qingyun.shop.admin.dto.ConsignmentSupplierInfoODTO;
import com.pinshang.qingyun.shop.admin.service.ConsignmentSupplierClient;
import com.pinshang.qingyun.shop.dto.bigShop.StallCommodityODTO;
import com.pinshang.qingyun.shop.dto.shopCommodity.ShopCommodityInfoIDTO;
import com.pinshang.qingyun.shop.dto.shopCommodity.ShopCommodityInfoODTO;
import com.pinshang.qingyun.shop.service.ShopCommodityClient;
import com.pinshang.qingyun.shop.service.ShopStockClient;
import com.pinshang.qingyun.storage.dto.CommoditySupplierODto;
import com.pinshang.qingyun.storage.dto.CommodityWarehouseODto;
import com.pinshang.qingyun.storage.dto.WarehouseODto;
import com.pinshang.qingyun.storage.service.CommoditySupplierClient;
import com.pinshang.qingyun.storage.service.CommodityWarehouseClient;
import com.pinshang.qingyun.storage.service.WarehouseClient;
import com.pinshang.qingyun.supplier.dto.FullSupplierODTO;
import com.pinshang.qingyun.supplier.dto.QuerySupplierByIdsIDTO;
import com.pinshang.qingyun.supplier.service.SupplierClient;
import com.pinshang.qingyun.xd.wms.dto.ShopCommodityStockDTO;
import com.pinshang.qingyun.xd.wms.dto.StockIDTO;
import com.pinshang.qingyun.xd.wms.dto.StockItemIDTO;
import com.pinshang.qingyun.xd.wms.dto.StockQueryIDTO;
import com.pinshang.qingyun.xd.wms.dto.bigShop.DdStockInOutExtraIDTO;
import com.pinshang.qingyun.xd.wms.service.XdStockClient;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.entity.Example;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * Created by crell on 2017/11/6.
 */
@Service
public class SaleReturnService {
    @Autowired
    private ProductService productService;
    @Autowired
    private OrderMapper orderMapper;

    @Autowired
    SaleReturnMapper saleReturnMapper;

    @Autowired
    SaleReturnItemMapper saleReturnItemMapper;

    @Autowired
    CodeClient codeClient;

    @Autowired
    ShopCommodityMapper shopCommodityMapper;

    @Autowired
    ShopStockClient shopStockClient;

    @Autowired
    private CommoditySupplierClient commoditySupplierClient;

    @Autowired
    private CommodityWarehouseClient commodityWarehouseClient;

    @Autowired
    private ShopCommodityClient shopCommodityClient;

    @Autowired
    private SupplierClient supplierClient;

    @Autowired
    private ShopMapper shopMapper;

    @Autowired
    private XdStockClient xdStockClient;

    @Lazy
    @Autowired
    private MdShopOrderSettingService mdShopOrderSettingService;

    @Autowired
    private ShopReceiveService shopReceiveService;

    @Value("${application.name.switch}")
    private String applicationNameSwitch;

    @Value("${pinshang.img-server-url}")
    private String imgServerUrl;
    @Autowired
    private CommodityMapper commodityMapper;
    @Autowired
    private SaleReturnOrderPicMapper saleReturnOrderPicMapper;
    @Autowired
    private DictionaryClient dictionaryClient;
    @Autowired
    private UserMapper userMapper;
    @Autowired
    private ShopService shopService;

    @Autowired
    private CommonService commonService;

    @Autowired
    private WarehouseClient warehouseClient;

    @Autowired
    private ConsigneeCommonService consigneeCommonService;

    @Autowired
    ConsignmentSaleReturnOrderMapper consignmentSaleReturnOrderMapper;

    @Autowired
    ConsignmentSaleReturnOrderItemMapper consignmentSaleReturnOrderItemMapper;

    @Autowired
    private ConsignmentSupplierClient consignmentSupplierClient;
    @Autowired
    private ConsignmentSupplierService consignmentSupplierService;

    public PageInfo <SaleReturnEntry> getSaleReturnListByCondition(SaleReturnVo vo) {
        vo.setIfReturnShort(vo.getIfReturnShort() == null ? Boolean.FALSE : vo.getIfReturnShort());
        vo.setReturnReason(SaleReturnReasonEnums.少货.getCode());

        QYAssert.isTrue(StringUtils.isNotBlank(vo.getBeginDate()) && StringUtils.isNotBlank(vo.getEndDate()),"退货起止日期不能为空！");
        vo.setBeginDate(vo.getBeginDate() + " 00:00:00");
        vo.setEndDate(vo.getEndDate() + " 23:59:59");

        //代销商
        vo.setConsignmentId(-1L);
        TokenInfo tokenInfo= FastThreadLocalUtil.getQY();
        if (1 == tokenInfo.getConsignmentFlag()) {
            vo.setConsignmentId(tokenInfo.getConsignmentId());
        }

        if(StallUtils.isStallSubcontractor(tokenInfo.getManagementMode())){
            List<Long> stallIdList = consignmentSupplierService.selectUserStallIdList(tokenInfo.getShopId());
            if(CollectionUtils.isEmpty(stallIdList)){
                return new PageInfo<>();
            }else {
                vo.setStallIdList(stallIdList);
            }
        }

        PageInfo<SaleReturnEntry> pageInfo = PageHelper.startPage(vo.getPageNo(), vo.getPageSize()).doSelectPageInfo(() -> {
            saleReturnMapper.getSaleReturnListByCondition(vo);
        });
        List<SaleReturnEntry> list = pageInfo.getList();
        if(CollectionUtils.isNotEmpty(list)){
            List<Long> createIdList = list.stream().map(item -> item.getCreateId()).collect(Collectors.toList());
            List<User> userList = userMapper.getEmployeeUserByUserId(createIdList);
            Map<Long, User> userMap = userList.stream().collect(Collectors.toMap(User::getUserId, Function.identity()));

            Long storeId = list.get(0).getStoreId();
            Shop shop = shopService.getShopByStoreId(storeId);

            List<Long> stallIdList = list.stream().filter(p -> p.getStallId() != null && p.getStallId() > 0).collect(Collectors.toList())
                    .stream().map(item -> item.getStallId()).collect(Collectors.toList());
            Map<Long, String> stallIdAndNameMap = consignmentSupplierService.queryStallMapByIds(stallIdList);

            for(SaleReturnEntry entry : list){
                User user = userMap.get(entry.getCreateId());
                if(user != null){
                    entry.setCreateName(user.getEmployeeName());
                }
                entry.setShopName(shop.getShopName());

                entry.setStallName(stallIdAndNameMap.get(entry.getStallId()));
            }
        }
        setSupplierName(pageInfo.getList());
        return pageInfo;
    }

    private void setSupplierName(List<SaleReturnEntry> list) {
        if (SpringUtil.isEmpty(list)) {
            return;
        }

        List<Long> supplierIdList = list.stream().map(item -> item.getSupplierId()).collect(Collectors.toList());
        QuerySupplierByIdsIDTO idsIDTO = new QuerySupplierByIdsIDTO();
        idsIDTO.setSupplierIds(supplierIdList);
        Map<Long, FullSupplierODTO> supplierIdAndODTOMap = supplierClient.querySupplierByIds(idsIDTO);
        list.forEach(item -> {
            Long supplierId = item.getSupplierId();
            if (supplierId != null) {
                FullSupplierODTO fullSupplierODTO = supplierIdAndODTOMap.get(supplierId);
                if (fullSupplierODTO != null) {
                    item.setSupplierName(fullSupplierODTO.getSupplierName());
                }
            }
        });
    }

    private void buildPicListMap(Map<Long, List<SaleReturnOrderPicVO>> picListMap, SaleReturnOrderPic saleReturnPic) {
        if (null != saleReturnPic) {
            Long commodityId = saleReturnPic.getCommodityId();
            List<SaleReturnOrderPicVO> picList = null != picListMap.get(commodityId)? picListMap.get(commodityId): new ArrayList<>();;
            picList.add(new SaleReturnOrderPicVO(saleReturnPic.getPicUrl(),imgServerUrl + saleReturnPic.getPicUrl()));
            picListMap.put(commodityId, picList);
        }
    }

    /**
     * 退货单数据展示
     * @param orderCode
     * @return
     */
    public SaleReturnInfoEntry showSaleReturn(String orderCode){
        //退货单
        SaleReturnEntry saleReturnEntry = saleReturnMapper.getSaleReturnByCode(orderCode);
        setSupplierName(Arrays.asList(saleReturnEntry));

        // 查询退货单图片信息
        Map<Long, List<SaleReturnOrderPicVO>> picListMap = new HashMap<>();
        Map<Long, String> videoUrlMap = new HashMap<>();

        Example saleReturnOrderPicEx = new Example(SaleReturnOrderPic.class);
        saleReturnOrderPicEx.createCriteria().andEqualTo("saleReturnOrderId", saleReturnEntry.getId());
        List<SaleReturnOrderPic> saleReturnPicList = saleReturnOrderPicMapper.selectByExample(saleReturnOrderPicEx);
        if(CollectionUtils.isNotEmpty(saleReturnPicList)){
            List<SaleReturnOrderPic> picList = saleReturnPicList.stream().filter(saleReturnpic -> {return SaleReturnOrderPic.PicTypeEnums.PIC.getCode().equals(saleReturnpic.getPicType());}).collect(Collectors.toList());
            List<SaleReturnOrderPic> videoList = saleReturnPicList.stream().filter(saleReturnpic -> {return SaleReturnOrderPic.PicTypeEnums.VIDEO.getCode().equals(saleReturnpic.getPicType());}).distinct().collect(Collectors.toList());
            picList.forEach(pic -> {this.buildPicListMap(picListMap, pic);});
            videoUrlMap.putAll(videoList.stream().collect(Collectors.toMap(SaleReturnOrderPic::getCommodityId, SaleReturnOrderPic::getPicUrl)));
        }

        //退货单明细
        List<SaleReturnItemEntry> saleReturnItems = saleReturnItemMapper.getSaleReturnItemById(saleReturnEntry.getId());
        if(CollectionUtils.isNotEmpty(saleReturnItems)){
            List<String> commodityIdList = saleReturnItems.stream().map(item -> item.getCommodityId()).collect(Collectors.toList());
            List<SaleReturnItemEntry> deliveryList = orderMapper.getCommodityDeliveryQuantityList(saleReturnEntry.getShopId(), commodityIdList, DateUtil.getDateFormate(saleReturnEntry.getCreateTime(),"yyyy-MM-dd"), saleReturnEntry.getStallId());
            Map<String, SaleReturnItemEntry> deliveryMap = new HashMap<>();
            if(CollectionUtils.isNotEmpty(deliveryList)){
                deliveryMap = deliveryList.stream().collect(Collectors.toMap(SaleReturnItemEntry::getCommodityId, Function.identity()));
            }
            for(SaleReturnItemEntry itemEntry:saleReturnItems){
                itemEntry.setShares(itemEntry.getReturnQuantity().divide(itemEntry.getCommodityPackageSpec(),0, RoundingMode.UP).longValue());

                itemEntry.setPicList(picListMap.get(Long.valueOf(itemEntry.getCommodityId())));
                itemEntry.setVideoUrl(StringUtil.isNullOrEmpty(videoUrlMap.get(Long.valueOf(itemEntry.getCommodityId())))? "": videoUrlMap.get(Long.valueOf(itemEntry.getCommodityId())).trim());
                itemEntry.setVisitVideoUrl(StringUtil.isNullOrEmpty(videoUrlMap.get(Long.valueOf(itemEntry.getCommodityId())))? "": imgServerUrl + videoUrlMap.get(Long.valueOf(itemEntry.getCommodityId())).trim());

                SaleReturnItemEntry deliveryItem = deliveryMap.get(itemEntry.getCommodityId());
                if(deliveryItem != null){
                    itemEntry.setDeliveryQuantity(deliveryItem.getDeliveryQuantity());
                }
            }
        }
        //组装
        SaleReturnInfoEntry info = new SaleReturnInfoEntry();
        info.setSaleReturn(saleReturnEntry);
        info.setSaleReturnItems(saleReturnItems);
        return info;
    }

    public SaleReturnInfoForAuditEntry showSaleReturnForAudit(String orderCode){
        //退货单
        SaleReturnForAuditEntry saleReturnEntry = saleReturnMapper.getSaleReturnAuditByCode(orderCode);
        setSupplierName(Collections.singletonList(saleReturnEntry));
        saleReturnEntry.setCreateTimeStr(DateUtil.get4yMdHms(saleReturnEntry.getCreateTime()));
        // 仓库名称
        Map<Long, WarehouseODto> warehouseMap = warehouseClient.queryWarehouseListByIds(Collections.singletonList(saleReturnEntry.getWarehouseId()));
        if(null!= warehouseMap && null != warehouseMap.get(saleReturnEntry.getWarehouseId())){
            saleReturnEntry.setWarehouseName(warehouseMap.get(saleReturnEntry.getWarehouseId()).getWarehouseName());
        }
        // 查询退货单图片信息
        Map<Long, List<SaleReturnOrderPicVO>> picListMap = new HashMap<>();
        Map<Long, String> videoUrlMap = new HashMap<>();

        Example saleReturnOrderPicEx = new Example(SaleReturnOrderPic.class);
        saleReturnOrderPicEx.createCriteria().andEqualTo("saleReturnOrderId", saleReturnEntry.getId());
        List<SaleReturnOrderPic> saleReturnPicList = saleReturnOrderPicMapper.selectByExample(saleReturnOrderPicEx);
        if(CollectionUtils.isNotEmpty(saleReturnPicList)){
            List<SaleReturnOrderPic> picList = saleReturnPicList.stream().filter(saleReturnpic -> {return SaleReturnOrderPic.PicTypeEnums.PIC.getCode().equals(saleReturnpic.getPicType());}).collect(Collectors.toList());
            List<SaleReturnOrderPic> videoList = saleReturnPicList.stream().filter(saleReturnpic -> {return SaleReturnOrderPic.PicTypeEnums.VIDEO.getCode().equals(saleReturnpic.getPicType());}).collect(Collectors.toList());

            picList.forEach(pic -> {this.buildPicListMap(picListMap, pic);});
            videoUrlMap.putAll(videoList.stream().collect(Collectors.toMap(SaleReturnOrderPic::getCommodityId, SaleReturnOrderPic::getPicUrl)));
        }

        //退货单明细
        List<SaleReturnItemForAuditEntry> saleReturnItems = saleReturnItemMapper.getSaleReturnItemForAuditById(saleReturnEntry.getId());
        if(CollectionUtils.isNotEmpty(saleReturnItems)){

            // 查询责任方字典
            List<DictionaryODTO> responsiblePartyTypeList = dictionaryClient.querySubDictionaryByParentOptionCode("ResponsiblePartyType");
            Map<String, String> responsiblePartyTypeMap = responsiblePartyTypeList.stream().collect(
                    Collectors.toMap(DictionaryODTO::getOptionValue, DictionaryODTO::getOptionName));

            // 查退货原因字典表
            List<DictionaryODTO> returnReasonDic = dictionaryClient.querySubDictionaryByParentOptionCode("7890");
            Map<String, String> returnReasonDicMap = returnReasonDic.stream().collect(Collectors.toMap(DictionaryODTO::getOptionCode, DictionaryODTO::getOptionName));

            // 查询送货日期当天少货商品的实发数量合计
            Shop shop = shopService.getShopByStoreId(saleReturnEntry.getStoreId());
            List<String> commodityIdList = saleReturnItems.stream().map(item -> item.getCommodityId()).collect(Collectors.toList());
            Map<String, SaleReturnItemEntry> deliveryMap = new HashMap<>();
            List<SaleReturnItemEntry> deliveryList = orderMapper.getCommodityDeliveryQuantityList(shop.getId(), commodityIdList, DateUtil.getDateFormate(saleReturnEntry.getCreateTime(),"yyyy-MM-dd"),saleReturnEntry.getStallId());
            if(CollectionUtils.isNotEmpty(deliveryList)){
                deliveryMap = deliveryList.stream().collect(Collectors.toMap(SaleReturnItemEntry::getCommodityId, Function.identity()));
            }

            for(SaleReturnItemForAuditEntry itemEntry:saleReturnItems){
                if(null != itemEntry.getRpType()) {
                    itemEntry.setRpTypeStr(responsiblePartyTypeMap.get(itemEntry.getRpType().toString()));
                }
                itemEntry.setPicList(picListMap.get(Long.valueOf(itemEntry.getCommodityId())));
                itemEntry.setVideoUrl(StringUtil.isNullOrEmpty(videoUrlMap.get(Long.valueOf(itemEntry.getCommodityId())))? "": videoUrlMap.get(Long.valueOf(itemEntry.getCommodityId())).trim());
                itemEntry.setVisitVideoUrl(StringUtil.isNullOrEmpty(videoUrlMap.get(Long.valueOf(itemEntry.getCommodityId())))? "": imgServerUrl + videoUrlMap.get(Long.valueOf(itemEntry.getCommodityId())).trim());
                if(null != returnReasonDicMap.get(itemEntry.getReturnReason()+"")){
                    itemEntry.setReturnReasonStr(returnReasonDicMap.get(itemEntry.getReturnReason()+""));
                }

                SaleReturnItemEntry deliveryItem = deliveryMap.get(itemEntry.getCommodityId());
                if(deliveryItem != null){
                    itemEntry.setDeliveryQuantity(deliveryItem.getDeliveryQuantity());
                }
            }
        }
        //组装
        SaleReturnInfoForAuditEntry info = new SaleReturnInfoForAuditEntry();
        info.setSaleReturn(saleReturnEntry);
        info.setSaleReturnItems(saleReturnItems);
        return info;
    }

    public List<SaleReturnItemEntry> copySaleReturn(String orderCode) {
        //退货单
        Example example = new Example(SaleReturn.class);
        example.createCriteria().andEqualTo("orderCode",orderCode);
        List<SaleReturn> saleReturnList = saleReturnMapper.selectByExample(example);
        QYAssert.isTrue(SpringUtil.isNotEmpty(saleReturnList),"退货单有误,未找到相应数据");
        SaleReturn saleReturn = saleReturnList.get(0);
        //退货单明细
        List<SaleReturnItemEntry> saleReturnItems = saleReturnItemMapper.copySaleReturnItemById(saleReturn.getId().toString());
        if(CollectionUtils.isNotEmpty(saleReturnItems)){
            List<Long> commodityIdList = saleReturnItems.stream().map(item -> Long.valueOf(item.getCommodityId())).collect(Collectors.toList());
            CommodityVO vo = new CommodityVO();
            vo.setCommodityIdList(commodityIdList);
            List<CommodityBasicEntry> commodityBasicList = commodityMapper.findCommodityBasicListByParam(vo);
            Map<String, CommodityBasicEntry> commodityBasicMap = commodityBasicList.stream().collect(Collectors.toMap(CommodityBasicEntry::getCommodityId, Function.identity()));

            // 设置实发数量
            Long storeId = saleReturn.getStoreId();
            Shop shop = shopService.getShopByStoreId(storeId);
            List<String> commodityIdStrList = saleReturnItems.stream().map(item -> item.getCommodityId()).collect(Collectors.toList());
            List<SaleReturnItemEntry> deliveryList = orderMapper.getCommodityDeliveryQuantityList(shop.getId(), commodityIdStrList, DateUtil.getDateFormate(saleReturn.getCreateTime(),"yyyy-MM-dd"),saleReturn.getStallId());
            Map<String, SaleReturnItemEntry> deliveryMap = new HashMap<>();
            if(CollectionUtils.isNotEmpty(deliveryList)){
                deliveryMap = deliveryList.stream().collect(Collectors.toMap(SaleReturnItemEntry::getCommodityId, Function.identity()));
            }

            for(SaleReturnItemEntry entry : saleReturnItems){
                CommodityBasicEntry basicEntry = commodityBasicMap.get(entry.getCommodityId());
                if(basicEntry != null){
                    BeanUtils.copyProperties(basicEntry,entry);
                    entry.setIfWeight(basicEntry.getIsWeight());
                }

                SaleReturnItemEntry deliveryItem = deliveryMap.get(entry.getCommodityId());
                if(deliveryItem != null){
                    entry.setDeliveryQuantity(deliveryItem.getDeliveryQuantity());
                }
                entry.setStallId(saleReturn.getStallId() + "");
            }
        }
//        for (SaleReturnItemEntry item : saleReturnItems) {
//            item.setLogisticsModel(saleReturn.getLogisticsModel());
//            item.setSupplierId(saleReturn.getSupplierId());
//            item.setWarehouseId(saleReturn.getWarehouseId());
//        }
        setSupplierIdAndWarehouseId(saleReturnItems, saleReturn.getStoreId());

        return saleReturnItems;
    }

    @Transactional(rollbackFor = Exception.class)
    public String addSaleReturn(SaleReturnAddVo vo) throws Throwable {
        checkHour(vo.getShopId());
        QYAssert.isTrue(SpringUtil.isNotEmpty(vo.getSaleReturnItems()), "商品列表不能为空");

        // 大店判断，新增的退货、少货商品必须是档口下面的
        List<Long> commodityIdList = vo.getSaleReturnItems().stream().map(item -> Long.valueOf(item.getCommodityId())).collect(Collectors.toList());
        consignmentSupplierService.checkStallCommodity(vo.getShopId(), vo.getStallId(), commodityIdList);

        for (SaleReturnItemAddVo item : vo.getSaleReturnItems()) {
            QYAssert.isTrue(item.getReturnQuantity() != null, "库存数量不能为空");
            QYAssert.isTrue(null !=item.getReturnReason(), "退货原因不能为空");
        }

        List<SaleReturnItemAddVo> saleReturnItems = vo.getSaleReturnItems();
        vo.setEnterpriseId(vo.getEnterpriseId() == null ? 78L:vo.getEnterpriseId());
        Shop shop = shopMapper.selectByPrimaryKey(vo.getShopId());
        vo.setStoreId(shop.getStoreId());

        // 判断退货单图片不能为空，按商品合并退货单图片视频
        Map<String,List<SaleReturnItemAddPicVO>> picMap = new HashMap<>();
        Map<String,List<SaleReturnItemAddPicVO>> videoMap = new HashMap<>();
        validateReturnPic(vo.getSaleReturnItems(),picMap,videoMap);

        //分组合并
        saleReturnItems = this.groupingByList(saleReturnItems);

        // 设置商品价格和供应商，仓库      并且检查库存
        String commodityName = setXdPriceSupplierWarehouse(saleReturnItems, shop, vo.getStallId());
        //如果是质检就不校验库存
        if(!vo.getXdQuality()){
            QYAssert.isTrue(StringUtil.isBlank(commodityName), "门店[" + commodityName + "]库存数量不足，无法退货");
        }
        
        List<SaleReturnItemAddVo> sendingItems = new ArrayList<>();
        //直通、配送item
        List<SaleReturnItemAddVo> companyItems = new ArrayList<>();
        for (SaleReturnItemAddVo saleReturnItem : saleReturnItems) {
            QYAssert.isTrue(null != saleReturnItem.getPrice(), "商品信息异常，价格不能为空");
            if(IogisticsModelEnums.DIRECT_SENDING.getCode() == saleReturnItem.getLogisticsModel()){
                QYAssert.isTrue(saleReturnItem.getSupplierId() != null, saleReturnItem.getCommodityCode() + " 请设置首选供应商");
                sendingItems.add(saleReturnItem);
            }else{
                QYAssert.isTrue(saleReturnItem.getWarehouseId() != null, saleReturnItem.getCommodityCode() + " 请设置首选仓库");
                companyItems.add(saleReturnItem);
            }
        }

        List<StockIDTO> stockList = new ArrayList<>();
        //处理直送
        if(sendingItems.size() > 0){
            Map<Long, List<SaleReturnItemAddVo>> sendingMap = sendingItems.stream().collect(Collectors.groupingBy(SaleReturnItemAddVo::getSupplierId));
            List<StockIDTO> stockIDTOList = doSaveSaleReturn(vo, sendingMap, IogisticsModelEnums.DIRECT_SENDING.getCode(), shop ,picMap,videoMap);
            if(CollectionUtils.isNotEmpty(stockIDTOList)){
                stockList.addAll(stockIDTOList);
            }
        }
        //处理直通、配送
        if(companyItems.size() > 0){
            Map<Long, List<SaleReturnItemAddVo>> companyMap = companyItems.stream().collect(Collectors.groupingBy(SaleReturnItemAddVo::getWarehouseId));
            List<StockIDTO> stockIDTOList = doSaveSaleReturn(vo, companyMap, IogisticsModelEnums.DISPATCHING.getCode(), shop,picMap,videoMap);
            if(CollectionUtils.isNotEmpty(stockIDTOList)){
                stockList.addAll(stockIDTOList);
            }
        }

        // 改成一次调用xd-wms
        if(CollectionUtils.isNotEmpty(stockList)){
            xdStockClient.stockShopReturnList(stockList);
        }
        return "";
    }

    /**
     * 判断退货单图片不能为空，按商品合并退货单图片视频
     * @param saleReturnItems
     * @param picMap
     * @param videoMap
     */
    public void validateReturnPic(List<SaleReturnItemAddVo> saleReturnItems,Map<String,List<SaleReturnItemAddPicVO>> picMap,Map<String,List<SaleReturnItemAddPicVO>> videoMap){
        for(SaleReturnItemAddVo addVo : saleReturnItems){
            QYAssert.isTrue(CollectionUtils.isNotEmpty(addVo.getPicList()),addVo.getCommodityName()+ "图片不能为空，请上传图片");
            QYAssert.isTrue(addVo.getPicList().size() <= 5,addVo.getCommodityName()+ "最多只能上传5张图片");
            addVo.getPicList().forEach(item ->{
                QYAssert.isTrue(item != null && StringUtils.isNotBlank(item.getPicUrl()), addVo.getCommodityName()+ "图片不能为空，请重新上传图片");
            });

            List<SaleReturnItemAddPicVO> picList = picMap.get(addVo.getCommodityId()) != null ? picMap.get(addVo.getCommodityId()) : new ArrayList<>();
            for(SaleReturnItemAddPicVO picVO : addVo.getPicList()){
                picList.add(picVO);
            }
            picMap.put(addVo.getCommodityId(),picList);


            List<SaleReturnItemAddPicVO> videoList = videoMap.get(addVo.getCommodityId()) != null ? videoMap.get(addVo.getCommodityId()) : new ArrayList<>();
            if(CollectionUtils.isNotEmpty(addVo.getVideoList())){
                for(SaleReturnItemAddPicVO videoVO : addVo.getVideoList()){
                    videoList.add(videoVO);
                }
            }
            videoMap.put(addVo.getCommodityId(),videoList);
        }
    }
    /**
     * 设置商品价格和供应商，仓库
     * 检查商品库存
     * @param saleReturnItems
     * @param shop
     */
    public String setXdPriceSupplierWarehouse(List<SaleReturnItemAddVo> saleReturnItems, Shop shop, Long stallId) {
        List<ShopStockEntry> shopCommodities = new ArrayList<>();
        List<Long> commodityIdList = saleReturnItems.stream().map(item -> Long.valueOf(item.getCommodityId())).collect(Collectors.toList());
        List<String> commodityIdStrList = saleReturnItems.stream().map(item -> item.getCommodityId()).collect(Collectors.toList());

        Map<Long, ShopCommodityInfoODTO> idAndObjMap = new HashMap<>(commodityIdList.size());
        Boolean isBigShop = (stallId != null && stallId > 0);
        if(isBigShop){
            // 获取大店的成本价
            List<StallCommodityODTO> stallCommodityList = consignmentSupplierService.queryStallCommodityInfo(shop.getId(), stallId, commodityIdList);
            if(CollectionUtils.isNotEmpty(stallCommodityList)) {
                for(StallCommodityODTO item : stallCommodityList){
                    ShopCommodityInfoODTO infoODTO = new ShopCommodityInfoODTO();
                    infoODTO.setCommodityId(item.getCommodityId());
                    infoODTO.setWeightPrice(item.getWeightPrice());
                    infoODTO.setCommodityPackageSpec(item.getCommodityPackageSpec());
                    idAndObjMap.put(item.getCommodityId(), infoODTO);
                }
            }
        }else {
            // 查询鲜食门店成本价、包装规格
            ShopCommodityInfoIDTO idto = new ShopCommodityInfoIDTO();
            idto.setShopId(shop.getId());
            idto.setCommodityIdList(commodityIdList);
            idAndObjMap = shopCommodityClient.getShopCommodityList(idto);
        }

        QYAssert.isTrue(null != idAndObjMap && idAndObjMap.size() > 0, isBigShop ? "门店档口无商品信息" : "门店无商品信息");

        // 查询门店订货通用设置
        List<MdShopOrderSettingEntry> settingList = mdShopOrderSettingService.queryMdShopOrderSettingListByIds(shop.getStoreId(), commodityIdStrList);
        QYAssert.isTrue(SpringUtil.isNotEmpty(settingList), "门店订货通用设置配置信息有误");
        Map<Long, MdShopOrderSettingEntry> settingMap = settingList.stream().collect(Collectors.toMap(MdShopOrderSettingEntry::getCommodityId, Function.identity()));

        Map<Long, CommoditySupplierODto> commodityIdAndSupplierODTOMap = commoditySupplierClient.queryCommodityDefaultSupplier(commodityIdList);
        Map<Long, CommodityWarehouseODto> commodityIdAndWarehouseODtoMap = commodityWarehouseClient.queryCommodityDefaultWarehouse(commodityIdList);

        Map<Long, ShopCommodityInfoODTO> finalIdAndObjMap = idAndObjMap;
        saleReturnItems.forEach(item->{
            Long commodityId = Long.parseLong(item.getCommodityId());
            ShopCommodityInfoODTO stockDTO = finalIdAndObjMap.get(commodityId);
            if(null != stockDTO ){
                item.setCommodityPackageSpec(stockDTO.getCommodityPackageSpec());
                /*// 鲜道进来returnQuantity 其实是份数，所以*包装规格，获取实际数量
                //if(shop.getShopType().equals(ShopTypeEnums.XD.getCode())){
                    item.setReturnQuantity(item.getReturnQuantity().multiply(item.getCommodityPackageSpec()));
                //}*/
                item.setPrice(stockDTO.getWeightPrice());
                item.setTotalPrice(null == item.getPrice() ? BigDecimal.ZERO : item.getPrice().multiply(item.getReturnQuantity()));
            }
            MdShopOrderSettingEntry mdShopOrderSettingEntry = settingMap.get(commodityId);
            QYAssert.isTrue(mdShopOrderSettingEntry != null,"商品id："+commodityId+"未设置门店订货通用设置");
            item.setLogisticsModel(mdShopOrderSettingEntry.getLogisticsModel());

            CommoditySupplierODto supplierODto = commodityIdAndSupplierODTOMap.get(commodityId);
            if (supplierODto != null) {
                item.setSupplierId(supplierODto.getSupplierId());
            }

            CommodityWarehouseODto warehouseODto = commodityIdAndWarehouseODtoMap.get(commodityId);
            if (warehouseODto != null) {
                item.setWarehouseId(warehouseODto.getWarehouseId());
            }
        });

        // 查询鲜道门店库存
        List<ShopCommodityStockDTO> stockList = new ArrayList<>();
        if(stallId != null && stallId > 0){
            StockQueryIDTO queryDTO = new StockQueryIDTO();
            queryDTO.setWarehouseId(shop.getId());
            queryDTO.setStallId(stallId);
            queryDTO.setCommodityList(commodityIdList);
            stockList = xdStockClient.queryBigShopCommodityStock(queryDTO);
        }else {
            stockList = xdStockClient.queryShopCommodityStock(shop.getId(),commodityIdList);
        }
        if(CollectionUtils.isNotEmpty(stockList)){
            for(ShopCommodityStockDTO odto : stockList){
                ShopStockEntry entry = new ShopStockEntry();
                entry.setCommodityId(odto.getCommodityId()+"");
                entry.setCommodityName(odto.getCommodityName());
                entry.setQuantity(odto.getStockQuantity());
                shopCommodities.add(entry);
            }
        }

        String commodityName = "";
        for (ShopStockEntry shopCommodity : shopCommodities) {
            for (SaleReturnItemAddVo item : saleReturnItems) {
                if(item.getCommodityId().equals(shopCommodity.getCommodityId()) && (shopCommodity.getQuantity().compareTo(item.getReturnQuantity()) == -1)){
                    commodityName = shopCommodity.getCommodityName();
                    break;
                }
            }
        }
        return commodityName;
    }


    //合并相同的商品
    private List<SaleReturnItemAddVo> groupingByList(List<SaleReturnItemAddVo> saleReturnItems){

        if(SpringUtil.isEmpty(saleReturnItems)){
            return null;
        }
        List<SaleReturnItemAddVo> resultList = new ArrayList<SaleReturnItemAddVo>();

        saleReturnItems.stream()
                .collect(Collectors.groupingBy(SaleReturnItemAddVo::getCommodityId))//分组
                .forEach((k,v)->{
                    Optional<SaleReturnItemAddVo>  item =  v.stream().reduce((v1,v2)->{//合并
                        v1.setReturnQuantity(v1.getReturnQuantity().add(v2.getReturnQuantity()));//退货数量
                        if(null != v1.getTotalPrice() && null != v2.getTotalPrice()){
                            v1.setTotalPrice(v1.getTotalPrice().add(v2.getTotalPrice()));//金额
                        }
                        return v1;
                    });
                    resultList.add(item.orElse(new SaleReturnItemAddVo()));
                });
        return resultList;
    }

    @Transactional(rollbackFor = Exception.class)
    public List<StockIDTO> doSaveSaleReturn(SaleReturnAddVo vo, Map<Long, List<SaleReturnItemAddVo>> map, Integer logisticsModel, Shop shop,Map<String,List<SaleReturnItemAddPicVO>> picMap,Map<String,List<SaleReturnItemAddPicVO>> videoMap){
        List<StockIDTO> stockIDTOList = new ArrayList<>();
        SaleReturn saleReturn = null;
        List<SaleReturnItem> saleReturnList = null;
        List<SaleReturnItemAddVo> tempItem = null;
        for (Long key : map.keySet()) {
            BigDecimal returnAmount = BigDecimal.ZERO;
            saleReturn = new SaleReturn(vo.getEnterpriseId(), vo.getStoreId(), vo.getCreateId(), SaleReturnStatusEnums.DEAL.getCode(), logisticsModel);
            if(IogisticsModelEnums.DIRECT_SENDING.getCode() == logisticsModel){
                saleReturn.setSupplierId(key);
            }else{
                saleReturn.setWarehouseId(key);
            }
            saleReturnList = new ArrayList<>();
            //明细
            tempItem = map.get(key);
            //汇总明细金额
            returnAmount = tempItem.stream().map(SaleReturnItemAddVo::getTotalPrice).reduce(BigDecimal.ZERO, BigDecimal::add);
            saleReturn.setReturnAmount(returnAmount);
            saleReturn.setTotalQuantity(tempItem.size());
            String returnCode = generateReturnCode(vo.getEnterpriseId());
            QYAssert.notNull(returnCode, " 退货单号获取异常");
            saleReturn.setOrderCode(returnCode);
            saleReturn.setCreateId(vo.getCreateId());
            saleReturn.setUpdateTime(new Date());
//            saleReturn.setUpdateId(vo.getCreateId());
            saleReturn.setConsignmentId((tempItem.get(0).getConsignmentId() != null && tempItem.get(0).getConsignmentId() > 0 ) ? tempItem.get(0).getConsignmentId() : -1);// 设置代销商
            saleReturn.setStallId(vo.getStallId() != null ? vo.getStallId() : -1L);
            saleReturn.setRemark(vo.getRemark());
            saleReturnMapper.insert(saleReturn);

            List<SaleReturnOrderPic> saleReturnOrderPicList = new ArrayList<>();
            //处理明细
            for (SaleReturnItemAddVo itemVo : tempItem) {
                SaleReturnItem saleReturnItem = new SaleReturnItem(Long.parseLong(itemVo.getCommodityId()), itemVo.getPrice()
                        , itemVo.getReturnQuantity(), itemVo.getTotalPrice(), itemVo.getReturnReason());
                saleReturnItem.setSaleReturnOrderId(saleReturn.getId());
                saleReturnItem.setRemark(itemVo.getRemark());

                saleReturnList.add(saleReturnItem);

                // 记录图片视频
                if(picMap != null){
                    List<SaleReturnItemAddPicVO> picList = picMap.get(itemVo.getCommodityId());
                    setSaleReturnOrderPicModel(vo.getCreateId(), saleReturn.getId(), saleReturnOrderPicList, itemVo.getCommodityId(), picList, SaleReturnOrderPic.PicTypeEnums.PIC);
                }
                if(videoMap != null){
                    List<SaleReturnItemAddPicVO> videoList = videoMap.get(itemVo.getCommodityId());
                    setSaleReturnOrderPicModel(vo.getCreateId(), saleReturn.getId(), saleReturnOrderPicList, itemVo.getCommodityId(), videoList, SaleReturnOrderPic.PicTypeEnums.VIDEO);
                }
            }
            if(!SpringUtil.isEmpty(saleReturnList)){
                saleReturnItemMapper.insertList(saleReturnList);
            }
            if(CollectionUtils.isNotEmpty(saleReturnOrderPicList)){
                saleReturnOrderPicMapper.insertList(saleReturnOrderPicList);
            }

            //处理库存
            StockIDTO stockIDTO = doReduceStock(tempItem,shop,vo.getCreateId(),saleReturn.getId(),saleReturn.getOrderCode(),vo.getXdQuality(),saleReturn.getStallId());
            if(stockIDTO != null){
                stockIDTOList.add(stockIDTO);
            }
        }
        return stockIDTOList;
    }

    /**
     * 设置 销售退货明细-图片视频'
     * @param createId
     * @param saleReturnId
     * @param saleReturnOrderPicList
     * @param commodityId
     * @param picList
     * @param pic
     */
    private void setSaleReturnOrderPicModel(Long createId, Long saleReturnId, List<SaleReturnOrderPic> saleReturnOrderPicList, String commodityId, List<SaleReturnItemAddPicVO> picList, SaleReturnOrderPic.PicTypeEnums pic) {
        if(CollectionUtils.isNotEmpty(picList)){
            for (SaleReturnItemAddPicVO picVO : picList) {
                SaleReturnOrderPic saleReturnOrderPic = new SaleReturnOrderPic();
                saleReturnOrderPic.setSaleReturnOrderId(saleReturnId);
                saleReturnOrderPic.setCommodityId(Long.parseLong(commodityId));
                saleReturnOrderPic.setPicType(pic.getCode());
                saleReturnOrderPic.setPicUrl(picVO.getPicUrl());
                saleReturnOrderPic.setCreateId(createId);
                saleReturnOrderPic.setCreateTime(new Date());
                saleReturnOrderPicList.add(saleReturnOrderPic);
                // 同一个商品视频只存一条
                if(SaleReturnOrderPic.PicTypeEnums.VIDEO.equals(pic)){
                    break;
                }
            }
        }
    }

    /**
     * 鲜食退货统一调用鲜到的扣库存
     * @param items
     * @param shop
     * @param userId
     * @param referId
     * @param referCode
     */
    private StockIDTO doReduceStock(List<SaleReturnItemAddVo> items, Shop shop,Long userId,Long referId,String referCode,Boolean xdQuality,Long stallId) {
        Boolean isBigShop = (stallId != null && stallId > 0);
        // (注：如果是质检进来的就不扣库存，因为wms那边已经扣过了)
        if(!xdQuality){
            StockIDTO stockIDTO = new StockIDTO();
            stockIDTO.setOutTypeEnum(StockOutTypeEnums.SHOP);
            stockIDTO.setWarehouseId(shop.getId());
            stockIDTO.setReferId(referId);
            stockIDTO.setReferCode(referCode);
            stockIDTO.setUserId(userId);
            List<StockItemIDTO> commodityList = new ArrayList<>();
            List<String> commodityIdList = items.stream().map(item -> item.getCommodityId()).collect(Collectors.toList());
            Map<Long,BigDecimal> commMap = shopReceiveService.getCommodityPackageSpecMap(commodityIdList);
            BigDecimal commodityPackageSpec = BigDecimal.ONE;
            for (SaleReturnItemAddVo vo : items) {
                StockItemIDTO stockItemIDTO = new StockItemIDTO();
                stockItemIDTO.setCommodityId(Long.valueOf(vo.getCommodityId()));
                if(null != commMap && commMap.get(Long.valueOf(vo.getCommodityId())) != null){
                    commodityPackageSpec = commMap.get(Long.valueOf(vo.getCommodityId()));
                }
                stockItemIDTO.setStockNumber(vo.getReturnQuantity().divide(commodityPackageSpec,0, RoundingMode.UP).intValue());
                stockItemIDTO.setQuantity(vo.getReturnQuantity());
                commodityList.add(stockItemIDTO);

                if(isBigShop){
                    DdStockInOutExtraIDTO ddStockInOutExtraIDTO = new DdStockInOutExtraIDTO();
                    ddStockInOutExtraIDTO.setCommodityId(Long.valueOf(vo.getCommodityId()));
                    ddStockInOutExtraIDTO.setStallId(stallId);
                    ddStockInOutExtraIDTO.setStorageArea(StorageAreaEnum.SHELF_AREA.getCode());
                    stockItemIDTO.setDdStockInOutExtraVO(ddStockInOutExtraIDTO);
                }
            }
            stockIDTO.setCommodityList(commodityList);

            //xdStockClient.stockShopReturn(stockIDTO);
            return stockIDTO;
        }
        return null;
    }



    @Transactional(rollbackFor = Exception.class)
    public void cancelSaleReturn(String orderCode,Long userId) throws Throwable{
        int i =saleReturnMapper.cancelSaleReturn(orderCode, SaleReturnStatusEnums.CANCEL.getCode(),userId,new Date());
        if(i==0){
            QYAssert.isTrue(false,"取消失败。可尝试刷新页面后再操作。");
        }

        //退货单
        Example example = new Example(SaleReturn.class);
        example.createCriteria().andEqualTo("orderCode",orderCode);
        List<SaleReturn> saleReturnList = saleReturnMapper.selectByExample(example);
        QYAssert.isTrue(SpringUtil.isNotEmpty(saleReturnList),"退货单有误,未找到相应数据");
        SaleReturn saleReturn = saleReturnList.get(0);
        //退货单明细
        List<SaleReturnItemEntry> saleReturnItems = saleReturnItemMapper.getSaleReturnItemById(saleReturn.getId().toString());

        //处理库存
        doPlusStock(saleReturnItems, saleReturn.getStoreId(), userId, saleReturn.getId(), saleReturn.getOrderCode(),saleReturn.getStallId());
    }

    @Transactional
    public void cancelSaleReturnWithReason(String orderCode,Long userId, String remark) throws Throwable{
        int i =saleReturnMapper.cancelSaleReturn(orderCode, SaleReturnStatusEnums.CANCEL.getCode(),userId,new Date());
        if(i==0){
        	 QYAssert.isTrue(false,"取消失败。可尝试刷新页面后再操作。");
        }
        //更新取消审核原因
        saleReturnItemMapper.cancelBySaleReturnId(orderCode, remark);
        //退货单
        Example example = new Example(SaleReturn.class);
        example.createCriteria().andEqualTo("orderCode",orderCode);
        List<SaleReturn> saleReturnList = saleReturnMapper.selectByExample(example);
        QYAssert.isTrue(SpringUtil.isNotEmpty(saleReturnList),"退货单有误,未找到相应数据");
        SaleReturn saleReturn = saleReturnList.get(0);
        //退货单明细
        List<SaleReturnItemEntry> saleReturnItems = saleReturnItemMapper.getSaleReturnItemById(saleReturn.getId().toString());

        //处理库存
        doPlusStock(saleReturnItems, saleReturn.getStoreId(), userId, saleReturn.getId(), saleReturn.getOrderCode(),saleReturn.getStallId());
    }

    @Transactional(rollbackFor = Exception.class)
    public void doPlusStock(List<SaleReturnItemEntry> items, Long storeId, Long userId, Long referId, String referCode, Long stallId) throws Throwable{
        Boolean isBigShop = (stallId != null && stallId > 0);
        //判断是否前置仓
        Example shopEx = new Example(Shop.class);
        shopEx.createCriteria().andEqualTo("storeId", storeId);
        List<Shop> shopList = shopMapper.selectByExample(shopEx);
        Shop shop = shopList.get(0);
        // 鲜道取消退货
        StockIDTO stockIDTO = new StockIDTO();
        stockIDTO.setInTypeEnum(StockQualityInTypeEnums.SHOP);
        stockIDTO.setWarehouseId(shop.getId());
        stockIDTO.setReferId(referId);
        stockIDTO.setReferCode(referCode);
        stockIDTO.setUserId(userId);
        List<StockItemIDTO> commodityList = new ArrayList<>();
        for (SaleReturnItemEntry vo : items) {
            StockItemIDTO stockItemIDTO = new StockItemIDTO();
            stockItemIDTO.setCommodityId(Long.valueOf(vo.getCommodityId()));
            stockItemIDTO.setStockNumber(vo.getReturnQuantity().divide(vo.getCommodityPackageSpec(),0, RoundingMode.UP).intValue());
            stockItemIDTO.setQuantity(vo.getReturnQuantity());
            commodityList.add(stockItemIDTO);

            if(isBigShop){
                DdStockInOutExtraIDTO ddStockInOutExtraIDTO = new DdStockInOutExtraIDTO();
                ddStockInOutExtraIDTO.setCommodityId(Long.valueOf(vo.getCommodityId()));
                ddStockInOutExtraIDTO.setStallId(stallId);
                ddStockInOutExtraIDTO.setStorageArea(StorageAreaEnum.PROVISIONAL_AREA.getCode());
                stockItemIDTO.setDdStockInOutExtraVO(ddStockInOutExtraIDTO);
            }
        }
        stockIDTO.setCommodityList(commodityList);

        xdStockClient.stockShopReturn(stockIDTO);
    }

    private String generateReturnCode(Long enterpriseId){
        String returnCode = "";
        try {
            returnCode = codeClient.createCode("SHOP_RETURN_CODE");
        } catch (Throwable e) {
            returnCode = null;
        }
        return returnCode;
    }

    public List<SaleReturnItemEntry> findCommodityByCode(SaleReturnCommodityVo vo) {
        List<SaleReturnItemEntry> list = new ArrayList<>();
//        list = saleReturnMapper.findCommodityByCode(vo);

        ShopCommodityInfoIDTO idto = new ShopCommodityInfoIDTO();
        idto.setShopId(vo.getShopId());
        idto.setCommodityCode(vo.getCommodityCode());
        TokenInfo ti = FastThreadLocalUtil.getQY();
        Boolean isBigShop = StallUtils.isStallSubcontractor(ti.getManagementMode());
        if(isBigShop){
            List<Long> stallCommodityIdList = consignmentSupplierService.getCommodityIdListByStallIds(ti.getShopId(), vo.getStallId());
            if(CollectionUtils.isEmpty(stallCommodityIdList)){
                return list;
            }
            idto.setCommodityIdList(stallCommodityIdList);
        }
        Map<Long, ShopCommodityInfoODTO> idAndObjMap = shopCommodityClient.getShopCommodityList(idto);
        if (SpringUtil.isNotEmpty(idAndObjMap)) {
            List<String> commodityIdList = idAndObjMap.values().stream().map(item -> item.getCommodityId() + "").collect(Collectors.toList());
            List<SaleReturnItemEntry> deliveryList = orderMapper.getCommodityDeliveryQuantityList(vo.getShopId(), commodityIdList, DateUtil.getDateFormate(new Date(),"yyyy-MM-dd"), vo.getStallId());
            Map<String, SaleReturnItemEntry> deliveryMap = new HashMap<>();
            if(CollectionUtils.isNotEmpty(deliveryList)){
                deliveryMap = deliveryList.stream().collect(Collectors.toMap(SaleReturnItemEntry::getCommodityId, Function.identity()));
            }

            Map<Long, StallCommodityODTO> stallCommMap = new HashMap<>();
            if(isBigShop){
                List<Long> idList = idAndObjMap.values().stream().map(item -> item.getCommodityId()).collect(Collectors.toList());
                // 获取大店的成本价
                List<StallCommodityODTO> stallCommodityList = consignmentSupplierService.queryStallCommodityInfo(vo.getShopId(), vo.getStallId(), idList);
                if(CollectionUtils.isNotEmpty(stallCommodityList)) {
                    stallCommMap = stallCommodityList.stream().collect(Collectors.toMap(StallCommodityODTO::getCommodityId, Function.identity()));
                }
            }

            Collection<ShopCommodityInfoODTO> values = idAndObjMap.values();
            for(ShopCommodityInfoODTO item : values) {
                SaleReturnItemEntry entry = new SaleReturnItemEntry();
                BeanUtils.copyProperties(item, entry);
                entry.setCommodityId(item.getCommodityId().toString());
                entry.setPrice(item.getWeightPrice());

                if(stallCommMap.size() > 0 && stallCommMap.containsKey(item.getCommodityId())) {
                    StallCommodityODTO stallCommodityODTO = stallCommMap.get(item.getCommodityId());
                    entry.setPrice(stallCommodityODTO.getWeightPrice());
                }

                SaleReturnItemEntry deliveryItem = deliveryMap.get(item.getCommodityId().toString());
                if (deliveryItem != null) {
                    entry.setDeliveryQuantity(deliveryItem.getDeliveryQuantity());
                }
                list.add(entry);
            }
        }

        setSupplierIdAndWarehouseId(list, null);
        return list;
    }

    public List<SaleReturnItemEntry> findCommodityByCommodityCode(SaleReturnCommodityVo vo) {
        List<SaleReturnItemEntry> list = new ArrayList<>();
//        list = saleReturnMapper.findCommodityByCode(vo);

        ShopCommodityInfoIDTO idto = new ShopCommodityInfoIDTO();
        idto.setShopId(vo.getShopId());
        idto.setCommodityCode(vo.getCommodityCode());

        TokenInfo ti = FastThreadLocalUtil.getQY();
        Boolean isBigShop = StallUtils.isStallSubcontractor(ti.getManagementMode());
        if(isBigShop){
            List<Long> stallCommodityIdList = consignmentSupplierService.getCommodityIdListByStallIds(ti.getShopId(), vo.getStallId());
            if(CollectionUtils.isEmpty(stallCommodityIdList)){
                return list;
            }
            idto.setCommodityIdList(stallCommodityIdList);
        }

        Map<Long, ShopCommodityInfoODTO> idAndObjMap = shopCommodityClient.getShopCommodityListByCommodityCode(idto);
        if (SpringUtil.isNotEmpty(idAndObjMap)) {
            List<String> commodityIdList = idAndObjMap.values().stream().map(item -> item.getCommodityId() + "").collect(Collectors.toList());
            List<SaleReturnItemEntry> deliveryList = orderMapper.getCommodityDeliveryQuantityList(vo.getShopId(), commodityIdList, DateUtil.getDateFormate(new Date(),"yyyy-MM-dd"), vo.getStallId());
            Map<String, SaleReturnItemEntry> deliveryMap = new HashMap<>();
            if(CollectionUtils.isNotEmpty(deliveryList)){
                deliveryMap = deliveryList.stream().collect(Collectors.toMap(SaleReturnItemEntry::getCommodityId, Function.identity()));
            }

            Map<Long, StallCommodityODTO> stallCommMap = new HashMap<>();
            if(isBigShop){
                List<Long> idList = idAndObjMap.values().stream().map(item -> item.getCommodityId()).collect(Collectors.toList());
                // 获取大店的成本价
                List<StallCommodityODTO> stallCommodityList = consignmentSupplierService.queryStallCommodityInfo(vo.getShopId(), vo.getStallId(), idList);
                if(CollectionUtils.isNotEmpty(stallCommodityList)) {
                    stallCommMap = stallCommodityList.stream().collect(Collectors.toMap(StallCommodityODTO::getCommodityId, Function.identity()));
                }
            }

            Collection<ShopCommodityInfoODTO> values = idAndObjMap.values();
            for(ShopCommodityInfoODTO item :values){
                SaleReturnItemEntry entry = new SaleReturnItemEntry();
                BeanUtils.copyProperties(item, entry);
                entry.setCommodityId(item.getCommodityId().toString());
                entry.setPrice(item.getWeightPrice());

                if(stallCommMap.size() > 0 && stallCommMap.containsKey(item.getCommodityId())) {
                    StallCommodityODTO stallCommodityODTO = stallCommMap.get(item.getCommodityId());
                    entry.setPrice(stallCommodityODTO.getWeightPrice());
                }

                SaleReturnItemEntry deliveryItem = deliveryMap.get(item.getCommodityId().toString());
                if (deliveryItem != null) {
                    entry.setDeliveryQuantity(deliveryItem.getDeliveryQuantity());
                }
                list.add(entry);
            }
        }

        setSupplierIdAndWarehouseId(list, null);
        return list;
    }

    private void setSupplierIdAndWarehouseId(List<SaleReturnItemEntry> list, Long storeId) {
        if (SpringUtil.isEmpty(list)) {
            return;
        }
        List<Long> commodityIdList = list.stream().map(item -> Long.parseLong(item.getCommodityId())).collect(Collectors.toList());
        Map<Long, CommoditySupplierODto> commodityIdAndSupplierODTOMap = commoditySupplierClient.queryCommodityDefaultSupplier(commodityIdList);
        Map<Long, CommodityWarehouseODto> commodityIdAndWarehouseODtoMap = commodityWarehouseClient.queryCommodityDefaultWarehouse(commodityIdList);
        Map<Long, ShopCommodityInfoODTO> commodityIdAndShopCommodityODTOMap = null;
        if (storeId != null) {
            ShopCommodityInfoIDTO idto = new ShopCommodityInfoIDTO();
            idto.setStoreId(storeId);
            idto.setCommodityIdList(commodityIdList);
            commodityIdAndShopCommodityODTOMap = shopCommodityClient.getShopCommodityList(idto);
        }

        Map<Long, ShopCommodityInfoODTO> finalCommodityIdAndShopCommodityODTOMap = commodityIdAndShopCommodityODTOMap;
        list.forEach(item -> {
            Long commodityId = Long.parseLong(item.getCommodityId());
            CommoditySupplierODto supplierODto = commodityIdAndSupplierODTOMap.get(commodityId);
            if (supplierODto != null) {
                item.setSupplierId(supplierODto.getSupplierId());
            }

            CommodityWarehouseODto warehouseODto = commodityIdAndWarehouseODtoMap.get(commodityId);
            if (warehouseODto != null) {
                item.setWarehouseId(warehouseODto.getWarehouseId());
            }

            if (SpringUtil.isNotEmpty(finalCommodityIdAndShopCommodityODTOMap)) {
                ShopCommodityInfoODTO shopCommodityInfoODTO = finalCommodityIdAndShopCommodityODTOMap.get(commodityId);
                if (shopCommodityInfoODTO != null) {
                    item.setPrice(shopCommodityInfoODTO.getWeightPrice());
                }
            }

        });
    }

    public PageInfo <SaleReturnEntry> getSaleReturnSendList(SaleReturnVo saleReturnVo) {
        if (!StringUtil.isBlank(saleReturnVo.getBeginDate()) && !StringUtil.isBlank(saleReturnVo.getEndDate())){
            saleReturnVo.setBeginDate(saleReturnVo.getBeginDate()+ " 00:00:00");
            saleReturnVo.setEndDate(saleReturnVo.getEndDate()+ " 23:59:59");
        }

        PageInfo<SaleReturnEntry> pageInfo = PageHelper.startPage(saleReturnVo.getPageNo(), saleReturnVo.getPageSize()).doSelectPageInfo(() -> {
            saleReturnMapper.getSaleReturnSendList(saleReturnVo);
        });
        setSupplierName(pageInfo.getList());
        return pageInfo;
    }

    @Transactional(rollbackFor = Exception.class)
    public void confirmSaleReturn(SaleReturnAddVo vo) {
        // 取消的订单是不允许操作的
        Long saleReturnOrderId = vo.getSaleReturnOrderId();
        SaleReturn saleReturnOrder = saleReturnMapper.selectByPrimaryKey(saleReturnOrderId);
        QYAssert.isTrue(saleReturnOrder != null && SaleReturnStatusEnums.CANCEL.getCode() != saleReturnOrder.getStatus(), "已取消, 不允许操作");

        List<SaleReturnItemAddVo> returnItems = vo.getSaleReturnItems();
        BigDecimal returnAmount = BigDecimal.ZERO;
        returnAmount = returnItems.stream().map(SaleReturnItemAddVo::getTotalPrice).reduce(BigDecimal.ZERO, BigDecimal::add);
        SaleReturn saleReturn = new SaleReturn(vo.getSaleReturnOrderId(), vo.getCreateId(), SaleReturnStatusEnums.ALREADY.getCode(), returnAmount);
        saleReturnMapper.updateByPrimaryKeySelective(saleReturn);

        //确认明细
        saleReturnItemMapper.confirmItems(returnItems);
    }

    public SaleReturn getSaleReturnOrder(Long saleReturnOrderId) {
        return saleReturnMapper.selectByPrimaryKey(saleReturnOrderId);
    }


    public PageInfo<XdSaleReturnODTO> xdReturnList(XdSaleReturnIDTO xdSaleReturnIDTO) {
        if(StringUtils.isEmpty(xdSaleReturnIDTO.getBeginDate()) || StringUtils.isEmpty(xdSaleReturnIDTO.getEndDate()) ){
            QYAssert.isTrue(false, "退货日期不能为空");
        }
        if (!StringUtil.isBlank(xdSaleReturnIDTO.getBeginDate()) && !StringUtil.isBlank(xdSaleReturnIDTO.getEndDate())){
            xdSaleReturnIDTO.setBeginDate(xdSaleReturnIDTO.getBeginDate()+ " 00:00:00");
            xdSaleReturnIDTO.setEndDate(xdSaleReturnIDTO.getEndDate()+ " 23:59:59");
            int diff = DateUtil.getDayDif(DateUtil.parseDate(xdSaleReturnIDTO.getEndDate(), DateUtil.DEFAULT_DATE_FORMAT), DateUtil.parseDate(xdSaleReturnIDTO.getBeginDate(), DateUtil.DEFAULT_DATE_FORMAT));
            QYAssert.isTrue(diff <= 30, "退货日期的跨度不能超过31天");
        }
        if(StringUtils.isNotBlank(xdSaleReturnIDTO.getCommodityKey())){
            CommodityVO vo = new CommodityVO();
            vo.setCommodityKey(xdSaleReturnIDTO.getCommodityKey());
            List<CommodityBasicEntry> commodityBasicList = commodityMapper.findCommodityBasicListByParam(vo);
            if(CollectionUtils.isNotEmpty(commodityBasicList)){
                List<Long> commodityIdList = commodityBasicList.stream().map(item -> Long.valueOf(item.getCommodityId())).collect(Collectors.toList());
                xdSaleReturnIDTO.setCommodityIdList(commodityIdList);
            }else {
                return new PageInfo();
            }
        }

        PageInfo<XdSaleReturnODTO> pageInfo = PageHelper.startPage(xdSaleReturnIDTO.getPageNo(), xdSaleReturnIDTO.getPageSize()).doSelectPageInfo(() -> {
            saleReturnMapper.xdReturnList(xdSaleReturnIDTO);
        });
        List<XdSaleReturnODTO> list = pageInfo.getList();
        if(CollectionUtils.isNotEmpty(list)){
            List<Long> commodityIdList = list.stream().map(item -> Long.valueOf(item.getCommodityId())).collect(Collectors.toList());
            CommodityVO vo = new CommodityVO();
            vo.setCommodityIdList(commodityIdList);
            List<CommodityBasicEntry> commodityBasicList = commodityMapper.findCommodityBasicListByParam(vo);
            Map<String, CommodityBasicEntry> commMap = commodityBasicList.stream().collect(Collectors.toMap(CommodityBasicEntry::getCommodityId, Function.identity()));

            for(XdSaleReturnODTO oto:list){
                CommodityBasicEntry commodityBasic = commMap.get(oto.getCommodityId());
                if(commodityBasic != null){
                   BeanUtils.copyProperties(commodityBasic,oto);
                }
                if(null != oto.getTotalPrice()){
                    oto.setTotalPrice(oto.getTotalPrice().setScale(2,BigDecimal.ROUND_HALF_UP));
                }else {
                    oto.setTotalPrice(BigDecimal.ZERO);
                }

            }
        }

        return pageInfo;
    }


    /**
     * pda退货查询
     * @param shopId
     * @param barCode
     * @return
     */
    public PdaReturnItemEntry pdaReturnByBarcode(Long shopId, String barCode,Long stallId) throws Exception{
        TokenInfo ti = FastThreadLocalUtil.getQY();
        Boolean isBigShop = StallUtils.isStallSubcontractor(ti.getManagementMode());
        List<Long> stallCommodityIdList = new ArrayList<>();
        if(isBigShop){
            QYAssert.isTrue(stallId != null, "请选择档口");

            stallCommodityIdList = consignmentSupplierService.getCommodityIdListByStallIds(shopId, stallId);
            QYAssert.isTrue(CollectionUtils.isNotEmpty(stallCommodityIdList), "档口无商品信息");

        }
        // 校验时间
        checkHour(shopId);
        // 解析条码
        barCode = analyBarcode(barCode);

        PdaReturnItemEntry pdaReturnItemEntry = new PdaReturnItemEntry();

        Long commodityId = getCommodityByCode(barCode);
        QYAssert.isTrue(commodityId != null , "商品编码/条形码有误");

        if(isBigShop && !stallCommodityIdList.contains(commodityId)){
            QYAssert.isFalse( "档口下无此商品信息");
        }

        // 判断是否门店商品并且赋值商品信息
        ShopCommodityInfoIDTO idto = new ShopCommodityInfoIDTO();
        idto.setShopId(shopId);
        List<Long> commodityIdList = new ArrayList<>();
        commodityIdList.add(commodityId);
        idto.setCommodityIdList(commodityIdList);
        Map<Long, ShopCommodityInfoODTO> shopCommodityMap = shopCommodityClient.getShopCommodityList(idto);
        QYAssert.isTrue(!(shopCommodityMap == null || shopCommodityMap.get(commodityId) == null) , "门店无此商品");

        ShopCommodityInfoODTO shopCommodity = shopCommodityMap.get(commodityId);
        BeanUtils.copyProperties(shopCommodity,pdaReturnItemEntry);
        pdaReturnItemEntry.setCommodityId(shopCommodity.getCommodityId() + "");
        pdaReturnItemEntry.setPrice(shopCommodity.getWeightPrice());

        // 设置退货原因
        List<PdaReturnItemEntry.ReturnReason> returnReasonList = new ArrayList<>();
        List<DictionaryODTO> dictionaryList = dictionaryClient.listDictionaryByOptionName("门店退货原因");
        dictionaryList.forEach(item -> {
            PdaReturnItemEntry.ReturnReason returnReason = new PdaReturnItemEntry.ReturnReason();
            returnReason.setOptionCode(item.getOptionCode());
            returnReason.setOptionName(item.getOptionName());
            if(!SaleReturnReasonEnums.少货.getName().equals(item.getOptionName())){
                returnReasonList.add(returnReason);
            }
        });
        pdaReturnItemEntry.setReturnReasonList(returnReasonList);

        // 查询库存
        if(isBigShop){
            Map<Long, ShopCommodityInfoODTO> stockMap  = productService.getBigShopCommodityStock(shopId, stallId, commodityIdList, true);
            ShopCommodityInfoODTO stockCommodity = stockMap.get(commodityId);
            if(stockCommodity != null){
                pdaReturnItemEntry.setStockQuantity(stockCommodity.getStockQuantity());
            }
        }else {
            Map<Long, ShopCommodityInfoODTO> stockMap  = productService.getXdShopCommodityStock(shopId, commodityIdList);
            ShopCommodityInfoODTO stockCommodity = stockMap.get(commodityId);
            if(stockCommodity != null){
                pdaReturnItemEntry.setStockQuantity(stockCommodity.getStockQuantity());
            }
        }

        return pdaReturnItemEntry;
    }

    /**
     * pda少货查询
     * @param shopId
     * @param barCode
     * @return
     */
    public PdaReturnItemEntry pdaShortByBarcode(Long shopId, String barCode,Long stallId) throws Exception{
        TokenInfo ti = FastThreadLocalUtil.getQY();
        Boolean isBigShop = StallUtils.isStallSubcontractor(ti.getManagementMode());
        List<Long> stallCommodityIdList = new ArrayList<>();
        if(isBigShop){
            QYAssert.isTrue(stallId != null, "请选择档口");

            stallCommodityIdList = consignmentSupplierService.getCommodityIdListByStallIds(shopId, stallId);
            QYAssert.isTrue(CollectionUtils.isNotEmpty(stallCommodityIdList), "档口无商品信息");
        }
        // 校验时间
        checkHour(shopId);
        // 解析条码
        barCode = analyBarcode(barCode);

        PdaReturnItemEntry pdaReturnItemEntry = new PdaReturnItemEntry();
        Long commodityId = getCommodityByCode(barCode);
        QYAssert.isTrue(commodityId != null , "商品编码/条形码有误");

        if(isBigShop && !stallCommodityIdList.contains(commodityId)){
            QYAssert.isFalse( "档口下无此商品信息");
        }

        String orderTime = DateUtil.getDateFormate(new Date(), "yyyy-MM-dd");

        // 判断是否门店商品并且赋值商品信息
        ShopCommodityInfoIDTO idto = new ShopCommodityInfoIDTO();
        idto.setShopId(shopId);
        List<Long> commodityIdList = new ArrayList<>();
        commodityIdList.add(commodityId);
        idto.setCommodityIdList(commodityIdList);
        Map<Long, ShopCommodityInfoODTO> shopCommodityMap = shopCommodityClient.getShopCommodityList(idto);
        QYAssert.isTrue(!(shopCommodityMap == null || shopCommodityMap.get(commodityId) == null) , "门店无此商品");

        ShopCommodityInfoODTO shopCommodity = shopCommodityMap.get(commodityId);
        BeanUtils.copyProperties(shopCommodity,pdaReturnItemEntry);
        pdaReturnItemEntry.setCommodityId(String.valueOf(shopCommodity.getCommodityId()));
        pdaReturnItemEntry.setPrice(shopCommodity.getWeightPrice());

        // 查询商品当天实发数量
        BigDecimal deliveryQuantity = orderMapper.getCommodityDeliveryQuantity(shopId, commodityId, orderTime, stallId);
        QYAssert.isTrue(deliveryQuantity != null , "只允许退今天的商品");
        pdaReturnItemEntry.setDeliveryQuantity(deliveryQuantity);

        // 判断是否直送商品
        Shop shop = shopMapper.selectByPrimaryKey(shopId);
        List<String> IdList = new ArrayList<>();
        IdList.add(commodityId + "");
        List<MdShopOrderSettingEntry> mdSettingList = mdShopOrderSettingService.queryMdShopOrderSettingListByIds(shop.getStoreId(), IdList);
        QYAssert.isTrue(CollectionUtils.isNotEmpty(mdSettingList) , "门店订货通用设置不能为空");
        MdShopOrderSettingEntry mdShopOrderSetting = mdSettingList.get(0);
        QYAssert.isTrue(mdShopOrderSetting.getLogisticsModel() != IogisticsModelEnums.DIRECT_SENDING.getCode() , "不能是直送商品");

        return pdaReturnItemEntry;
    }

    /**
     * 根据条码或者编码查询商品，优先条码查询
     * @param barCode
     * @return
     */
    public Long getCommodityByCode(String barCode){
        Long commodityId = commodityMapper.findCommodityByBarCode(barCode);
        if(commodityId == null){
            commodityId = commodityMapper.findCommodityByCode(barCode);
        }
        return commodityId;
    }

    /**
     * pda退货新增
     * @param saleReturnAddVo
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public Boolean pdaReturnAdd(SaleReturnAddVo saleReturnAddVo) throws Throwable{
        checkHour(saleReturnAddVo.getShopId());
        // 调用退货方法
        addSaleReturn(saleReturnAddVo);
        return Boolean.TRUE;
    }

    /**
     * pda少货新增
     * @param saleReturnAddVo
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public Boolean pdaShortAdd(SaleReturnAddVo saleReturnAddVo) throws Throwable{
        checkHour(saleReturnAddVo.getShopId());
        // 校验是否是代销品
        this.checkConsignmentSupplierCommodity(saleReturnAddVo);

        Long storeId = saleReturnAddVo.getStoreId();
        Long shopId = saleReturnAddVo.getShopId();

        List<String> commodityIdList = saleReturnAddVo.getSaleReturnItems().stream().map(item -> item.getCommodityId()).collect(Collectors.toList());
        String orderTime = DateUtil.getDateFormate(new Date(), "yyyy-MM-dd");

        QYAssert.isTrue(shopId != null,"门店id不能为空!");
        QYAssert.isTrue(CollectionUtils.isNotEmpty(commodityIdList),"商品idList不能为空!");
        // 判断是否当天商品实发数量是否大于0
        List<SaleReturnItemEntry> deliveryList = orderMapper.getCommodityDeliveryQuantityList(shopId, commodityIdList, orderTime, saleReturnAddVo.getStallId());
        QYAssert.isTrue(CollectionUtils.isNotEmpty(deliveryList), " 只允许退今天的商品");
        Map<String, SaleReturnItemEntry> deliveryMap = deliveryList.stream().collect(Collectors.toMap(SaleReturnItemEntry::getCommodityId, Function.identity()));

        // 判断是否直送商品
        List<MdShopOrderSettingEntry> mdSettingList = mdShopOrderSettingService.queryMdShopOrderSettingListByIds(storeId, commodityIdList);
        QYAssert.isTrue(CollectionUtils.isNotEmpty(mdSettingList) , "门店订货通用设置不存在");

        Map<Long, MdShopOrderSettingEntry> settingEntryMap = mdSettingList.stream().collect(Collectors.toMap(MdShopOrderSettingEntry::getCommodityId, Function.identity()));
        for(SaleReturnItemAddVo item : saleReturnAddVo.getSaleReturnItems()){
            item.setReturnReason(SaleReturnReasonEnums.少货.getCode());
            MdShopOrderSettingEntry setting = settingEntryMap.get(Long.valueOf(item.getCommodityId()));
            String commodityName = item.getCommodityCode() + "_"+item.getCommodityName();
            QYAssert.isTrue(setting != null,  commodityName + " 门店订货通用设置不存在");
            QYAssert.isTrue(setting.getLogisticsModel() != IogisticsModelEnums.DIRECT_SENDING.getCode() , commodityName + " 不能是直送商品");

            SaleReturnItemEntry saleReturnItem = deliveryMap.get(item.getCommodityId());
            QYAssert.isTrue(saleReturnItem != null && saleReturnItem.getDeliveryQuantity().compareTo(BigDecimal.ZERO) > 0,  commodityName + " 只允许退今天的商品");

            QYAssert.isTrue(item.getReturnQuantity().compareTo(saleReturnItem.getDeliveryQuantity()) <= 0,  commodityName + " 少货数量不能大于实发数量");

        }
        // 调用退货方法
        addSaleReturn(saleReturnAddVo);
        return Boolean.TRUE;
    }

    /**
     * 查询是否有香烟代销商品
     * @param vo
     */
    private void checkConsignmentSupplierCommodity(SaleReturnAddVo saleReturnAddVo){
        ConsignmentSupplierInfoIDTO vo =new ConsignmentSupplierInfoIDTO();
        vo.setStoreIdList(Collections.singletonList(saleReturnAddVo.getStoreId()));
        List<ConsignmentSupplierInfoODTO> consignmentCommodityList = consignmentSupplierClient.batchQueryByStoreIds(vo);
        if(SpringUtil.isNotEmpty(consignmentCommodityList)){
            List<Long> consignmentCommodityIdList = consignmentCommodityList.stream()
                    .map(ConsignmentSupplierInfoODTO::getCommodityId)
                    .distinct()
                    .collect(Collectors.toList());
            List<String> consignmentCommodityNameList = saleReturnAddVo.getSaleReturnItems().stream()
                    .filter(it -> consignmentCommodityIdList.contains(Long.parseLong(it.getCommodityId())))
                    .map(SaleReturnItemAddVo::getCommodityName)
                    .collect(Collectors.toList());
            QYAssert.isTrue(SpringUtil.isEmpty(consignmentCommodityNameList), "存在代销商品:" + String.join(",",consignmentCommodityNameList));
        }
    }


    public PageInfo<SaleReturnDetailPageODTO> saleReturnDetailPage(SaleReturnDetailPageIDTO idto) {
        if(!StringUtil.isBlank(idto.getBeginTime()) && !StringUtil.isBlank(idto.getEndTime())){
            idto.setBeginTime(idto.getBeginTime()+ " 00:00:00");
            idto.setEndTime(idto.getEndTime()+ " 23:59:59");
            int diff = DateUtil.getDayDif(DateUtil.parseDate(idto.getEndTime(), DateUtil.DEFAULT_DATE_FORMAT), DateUtil.parseDate(idto.getBeginTime(), DateUtil.DEFAULT_DATE_FORMAT));
            QYAssert.isTrue(diff <= 30, "退货日期的跨度不能超过31天");
        }

        TokenInfo tokenInfo = FastThreadLocalUtil.getQY();
        if(StallUtils.isStallSubcontractor(tokenInfo.getManagementMode())){
            List<Long> stallIdList = consignmentSupplierService.selectUserStallIdList(tokenInfo.getShopId());
            if(CollectionUtils.isEmpty(stallIdList)){
                return new PageInfo<>();
            }else {
                idto.setStallIdList(stallIdList);
            }
        }
        PageInfo<SaleReturnDetailPageODTO> pageInfo = PageHelper.startPage(idto.getPageNo(), idto.getPageSize()).doSelectPageInfo(() -> {
            saleReturnItemMapper.saleReturnDetailPage(idto);
        });

        List<SaleReturnDetailPageODTO> list = pageInfo.getList();
        if(CollectionUtils.isNotEmpty(list)){
            List<DictionaryODTO> returnReasonDic = dictionaryClient.querySubDictionaryByParentOptionCode("7890");
            Map<String, String> returnReasonDicMap = returnReasonDic.stream().collect(Collectors.toMap(DictionaryODTO::getOptionCode, DictionaryODTO::getOptionName));
//            List<Long> shopIdList = list.stream().map(SaleReturnDetailPageODTO::getShopId).collect(Collectors.toList());
//            Map<Long, OrgAndParentInfoODTO> orgMap = commonService.getOrgMap(shopIdList);
            List<Long> returnIdList = list.stream().map(SaleReturnDetailPageODTO::getReturnId).collect(Collectors.toList());
            Map<String, List<String>> saleReturnPicMap = this.getSaleReturnPicMap(returnIdList);
            // 查询责任方字典
            List<DictionaryODTO> responsiblePartyTypeList = dictionaryClient.querySubDictionaryByParentOptionCode("ResponsiblePartyType");
            Map<String, String> responsiblePartyTypeMap = responsiblePartyTypeList.stream().collect(
                    Collectors.toMap(DictionaryODTO::getOptionValue, DictionaryODTO::getOptionName));

            List<Long> stallIdList = list.stream().filter(p -> p.getStallId() != null && p.getStallId() > 0).collect(Collectors.toList())
                    .stream().map(item -> item.getStallId()).collect(Collectors.toList());
            Map<Long, String> stallIdAndNameMap = consignmentSupplierService.queryStallMapByIds(stallIdList);
            for(SaleReturnDetailPageODTO oto:list){
                if(null != oto.getReTypeId()) {
                    oto.setReType(responsiblePartyTypeMap.get(oto.getReTypeId().toString()));
                }
                if(null != oto.getTotalPrice()){
                    oto.setTotalPrice(oto.getTotalPrice().setScale(2, RoundingMode.HALF_UP));
                }else {
                    oto.setTotalPrice(BigDecimal.ZERO);
                }
                if(null != oto.getReturnReasonId() && null != returnReasonDicMap.get(oto.getReturnReasonId()+"")) {
                    oto.setReturnReason(returnReasonDicMap.get(oto.getReturnReasonId()+""));
                }
                if(null != oto.getPrice() && null != oto.getRealReturnQuantity()) {
                    oto.setReturnAmount(oto.getPrice().multiply(oto.getRealReturnQuantity()).setScale(2, RoundingMode.HALF_UP));
                }

                if(SpringUtil.isNotEmpty(saleReturnPicMap)){
                    String picKey = oto.getReturnId() + "-" + oto.getCommodityId() + "-" + SaleReturnOrderPic.PicTypeEnums.PIC.getCode();
                    if(SpringUtil.isEmpty(saleReturnPicMap.get(picKey))){
                        oto.setHasPic("无");
                    }else{
                        oto.setHasPic("有");
                        oto.setPicUrl(this.buildPicList(saleReturnPicMap.get(picKey)));
                    }
                    String videoKey = oto.getReturnId() + "-" + oto.getCommodityId() + "-" + SaleReturnOrderPic.PicTypeEnums.VIDEO.getCode();
                    List<String> videoList = saleReturnPicMap.get(videoKey);
                    if(SpringUtil.isEmpty(videoList)){
                        oto.setHasVideo("无");
                    }else{
                        oto.setHasVideo("有");
                        oto.setVideoUrl(videoList.get(videoList.size()-1));
                        oto.setVisitVideoUrl(imgServerUrl + oto.getVideoUrl());
                    }
                }

                oto.setStatusStr(null == oto.getStatus() ? "" : SaleReturnStatusEnums.getName(oto.getStatus()));
                oto.setStallName(stallIdAndNameMap.get(oto.getStallId()));
            }
        }

        return pageInfo;
    }

    private List<SaleReturnOrderPicVO> buildPicList(List<String> picUrlList) {
        return picUrlList.stream().map(it ->  new SaleReturnOrderPicVO(it, imgServerUrl + it)).collect(Collectors.toList());
    }

    /**
     * 获取照片和视频
     * @param returnIdList
     * @return key = sales_order_return.id + "-" + commodity_id + "-" + 类型
     */
    private  Map<String, List<String>> getSaleReturnPicMap(List<Long> returnIdList){
        Example saleReturnOrderPicEx = new Example(SaleReturnOrderPic.class);
        saleReturnOrderPicEx.createCriteria().andIn("saleReturnOrderId", returnIdList);
        List<SaleReturnOrderPic> saleReturnPicList = saleReturnOrderPicMapper.selectByExample(saleReturnOrderPicEx);
        if(SpringUtil.isEmpty(saleReturnPicList)){
            return new HashMap<>();
        }
        Map<String, List<String>> saleReturnPicMap = new HashMap<>(saleReturnPicList.size());

        for (SaleReturnOrderPic pic : saleReturnPicList) {
            String key = pic.getSaleReturnOrderId() + "-" + pic.getCommodityId() + "-" + pic.getPicType();
            if(null == saleReturnPicMap.get(key)){
                List<String> picItemList = new ArrayList<>();
                picItemList.add(pic.getPicUrl());
                saleReturnPicMap.put(key, picItemList);
            }else{
                List<String> picItemList = saleReturnPicMap.get(key);
                picItemList.add(pic.getPicUrl());
            }
        }

        return saleReturnPicMap;
    }

    // 校验时间
    public void checkHour(Long shopId) throws Exception{
        QYAssert.isTrue(shopId != null, "门店id不能为空!");
        Shop shop = shopMapper.selectByPrimaryKey(shopId);
        QYAssert.isTrue(shop != null, "门店不存在!");

        DictionaryODTO dictionaryODTO = null;
        if(ManagementModeEnums.档口分包.getCode().equals(shop.getManagementMode())) {
            dictionaryODTO = dictionaryClient.getDictionaryByCode("ddShopReturnHour");
        }else {
            dictionaryODTO = dictionaryClient.getDictionaryByCode("shopReturnHour");
        }
        String hour = dictionaryODTO.getOptionValue();
        String currentHour = DateUtil.getDateFormate(new Date(),"HH:mm");
        QYAssert.isTrue(DateUtil.compareHourAndMin(currentHour, hour + ":00") <= 0 , "退货少货截至当天" + hour + "点");
    }

    //解析条码
    private String analyBarcode(String barCode) {
        if(StringUtils.isNotBlank(barCode)){
            // 是否为称重码(称重码为2打头 18位)
            boolean isWeightCode = barCode.length() == 18 && barCode.startsWith("2");
            if(isWeightCode){
                // 获取条形码
                barCode = barCode.substring(1, 7);
            }
        }
        return barCode;
    }



}
