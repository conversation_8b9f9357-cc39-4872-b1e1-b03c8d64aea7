package com.pinshang.qingyun.order.service.pay.callback;

import com.pinshang.qingyun.base.enums.xd.XdPayTypeEnum;
import com.pinshang.qingyun.pay.dto.Attach;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import org.springframework.context.ApplicationEvent;

import java.util.Date;

/**
 * 三方支付成功事件
 *
 * <AUTHOR>
 * @Date 2019/12/5 17:32
 */
@Getter
@Setter
public class ThirdPartyPayCallbackEvent extends ApplicationEvent {
    /**
     * 订单code
     */
    private String orderCode;
    /**
     * 外部code
     */
    private String transactionId;
    /**
     * 校验结果是否合法
     * 当且仅当校验合法与支付成功时为true
     */
    private boolean validate;
    /**
     * 请求报文
     */
    private String json;

    /**
     * 支付状态，后面可调整为枚举
     * 阿里的回调通知状态有 TRADE_FINISHED TRADE_SUCCESS TRADE_CLOSED
     */
    private String payStatus;

    private XdPayTypeEnum payType;

    private Boolean noLog;

    private Date payTime;
    @Getter
    private Attach attach;


    public ThirdPartyPayCallbackEvent(String transactionId, String orderCode, String json, boolean validate, XdPayTypeEnum payType, Date payTime, Attach attach) {
        super(orderCode);
        this.orderCode = orderCode;
        this.transactionId = transactionId;
        this.validate = validate;
        this.json = json;
        this.payType = payType;
        this.payTime = payTime;
        this.attach = attach;
    }


    public static ThirdPartyPayCallbackEvent build(String transactionId, String orderCode, String json
            , boolean validate, XdPayTypeEnum payType, Date payTime, Attach attach) {
        return new ThirdPartyPayCallbackEvent(transactionId, orderCode, json, validate, payType, payTime, attach);
    }


    public ThirdPartyPayCallbackEvent withoutLog() {
        this.noLog = true;
        return this;
    }


    @Override
    public String toString() {
        return "ThirdPartyPayCallbackEvent{" +
                "orderCode='" + orderCode + '\'' +
                ", transactionId='" + transactionId + '\'' +
                ", validate=" + validate +
                ", json='" + json + '\'' +
                ", payType=" + payType.getRemark() +
                '}';
    }
}
