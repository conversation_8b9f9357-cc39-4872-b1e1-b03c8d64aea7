package com.pinshang.qingyun.order.service.tda.factory;

import com.pinshang.qingyun.order.dto.xda.tda.SaveReturnOrderODTO;
import com.pinshang.qingyun.order.service.tda.ReturnOrderStrategy;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

@Component
public class ReturnOrderStrategyFactory implements ApplicationContextAware {

    private final Map<Integer, ReturnOrderStrategy> map = new ConcurrentHashMap<>();

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        Map<String, ReturnOrderStrategy> tempMap = applicationContext.getBeansOfType(ReturnOrderStrategy.class);
        tempMap.values().forEach(returnOrderStrategy -> map.put(returnOrderStrategy.getReturnSourceType(), returnOrderStrategy));
    }

    public Boolean execute(SaveReturnOrderODTO dto) {
        ReturnOrderStrategy returnOrderStrategy = map.get(dto.getReturnSource());
        if (returnOrderStrategy != null) {
            return returnOrderStrategy.execute(dto);
        }
        return null;
    }
}
