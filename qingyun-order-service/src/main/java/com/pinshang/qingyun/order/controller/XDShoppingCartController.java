package com.pinshang.qingyun.order.controller;

import com.pinshang.qingyun.base.annotations.RepeatSubmitAnno;
import com.pinshang.qingyun.base.api.TokenInfo;
import com.pinshang.qingyun.base.configure.expand.QYAssert;
import com.pinshang.qingyun.base.enums.RepeatSubmitBusinessCode;
import com.pinshang.qingyun.base.enums.order.OrderTypeEnum;
import com.pinshang.qingyun.base.util.FastThreadLocalUtil;
import com.pinshang.qingyun.box.utils.DateUtil;
import com.pinshang.qingyun.order.config.LockConstants;
import com.pinshang.qingyun.order.dto.OrderReferenceDetailODTO;
import com.pinshang.qingyun.order.job.GenerateCreateTakeCardOrderTasks;
import com.pinshang.qingyun.order.model.order.ShoppingCart;
import com.pinshang.qingyun.order.service.*;
import com.pinshang.qingyun.order.util.ThreadLocalUtils;
import com.pinshang.qingyun.order.vo.order.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.collections.CollectionUtils;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @Author: sk
 * @Date: 2019/11/28
 */

@RestController
@RequestMapping("/xdShoppingcart")
@Api(description = "鲜道购物车管理")
public class XDShoppingCartController {
    @Autowired
    private OrderService orderService;
    @Autowired
    private BStockService bStockService;
    @Autowired
    private RedissonClient redissonClient;
    @Autowired
    private ShopOrderService shopOrderService;
    @Autowired
    private ShoppingCartService shoppingCartService;
    @Autowired
    private XDShoppingCartService xDShoppingCartService;
    @Autowired
    private XDShoppingCartDeleteService xDShoppingCartDeleteService;
    @Autowired
    private GenerateCreateTakeCardOrderTasks generateCreateTakeCardOrderTasks;

    /**
     * 建议订货量并且加入临时购物车(鲜道前置仓)
     */
    @ApiOperation(value = "自动算法加入临时购物车(鲜道前置仓)", notes = "自动算法加入临时购物车(鲜道前置仓)", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @RequestMapping(value = "/batchAddXdShoppingCartTemp", method = RequestMethod.POST)
    public void batchAddXdShoppingCartTemp(){
        xDShoppingCartDeleteService.deleteShoppingCartTemp();
        xDShoppingCartService.batchAddShoppingCartTemp();
    }


    /**
     * 从鲜道临时购物车加入购物车(鲜道前置仓)
     */
    @ApiOperation(value = "自动算法加入购物车(鲜道前置仓)", notes = "自动算法加入购物车(鲜道前置仓)", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @RequestMapping(value = "/batchAddXdShoppingCart", method = RequestMethod.POST)
    public void batchAddShoppingCart(){
        xDShoppingCartDeleteService.deleteShoppingCart();
        xDShoppingCartService.batchAddShoppingCart();
    }


    @ApiOperation(value = "测试提交订单", notes = "测试提交订单", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @RequestMapping(value = "/testCreateOrder", method = RequestMethod.POST)
    public void testCreateOrder(@RequestParam(value = "endTime",required = false) String endTime){
        xDShoppingCartService.autoCreateXDOrder(endTime);
    }


    /**
     * 批量修改购物车商品数量(以单个购物车为维度)
     * @param
     * @return
     */
    @ApiOperation(value = "(前置仓)批量修改购物车商品数量(以单个购物车为维度)", notes = "(前置仓)批量修改购物车商品数量(以单个购物车为维度)", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @RequestMapping(value = "/batchUpdateItemQuantity", method = RequestMethod.POST)
    public void batchUpdateItemQuantity(@RequestBody ShoppingCartUpdateVO vo){
        TokenInfo ti = FastThreadLocalUtil.getQY();
        vo.setStoreId(ti.getStoreId());
        shoppingCartService.batchUpdateItemQuantity(vo);
    }


    /**
     * 订货参考
     * @param commodityId
     */
    @ApiOperation(value = "订货参考", notes = "订货参考", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @RequestMapping(value = "/orderReference", method = RequestMethod.POST)
    public OrderReferenceDetailODTO orderReference(@RequestParam(value = "commodityId",required = false) Long commodityId,
                                                   @RequestParam(value = "shoppingCartId",required = false) Long shoppingCartId){
        TokenInfo ti = FastThreadLocalUtil.getQY();
        return xDShoppingCartService.orderReference(ti.getStoreId(),ti.getShopId(),commodityId,shoppingCartId);
    }

    /**
     * 种植任务提交购物车
     */
    @ApiOperation(value = "种植任务提交购物车", notes = "种植任务提交购物车", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @RequestMapping(value = "/generateCreateXdOrder", method = RequestMethod.POST)
    public void generateCreateXdOrder() {
        xDShoppingCartService.generateJob();
    }

    /**
     * 门店下单
     */
    @ApiOperation(value = "门店购物车提交订单", notes = "门店购物车提交订单", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @RequestMapping(value = "/createOrder", method = RequestMethod.POST)
    public CreateShopOrderFilterVo createOrder(@RequestBody List<CreateOrderVo> vos)throws Throwable{
        TokenInfo ti = FastThreadLocalUtil.getQY();
        RLock lock = redissonClient.getLock(LockConstants.createOrder + ti.getUserId());
        if (lock.tryLock()) {
            try {
                // 单门店购物车提交订单
                return shopOrderService.singleShopCreateOrder(vos);
            } finally {
                lock.unlock();
            }
        }else {
            QYAssert.isFalse("系统繁忙,请勿频繁操作!");
        }
        return new CreateShopOrderFilterVo(true,null,null,null);
    }

    /**
     * 管理员版本  createOrder
     */
    @RequestMapping(value = "/createOrderAdmin", method = RequestMethod.POST)
    public CreateOrderFilterVo createOrderAdmin(@RequestBody List<CreateOrderVo> vos)throws Throwable{
        TokenInfo ti = FastThreadLocalUtil.getQY();
        RLock lock = redissonClient.getLock(LockConstants.createOrder + ti.getUserId());
        if (lock.tryLock()) {
            try {
                if (null != vos && !vos.isEmpty()) {
                    Long cartId = vos.get(0).getShoppingCartId();
                    ShoppingCart shoppingCart = shoppingCartService.queryShoppingCartById(cartId);
                    QYAssert.isTrue(shoppingCart != null , "购物车不能为空，请刷新重试!");
                    Boolean isXd = shoppingCartService.getIsXd(shoppingCart.getStoreId());
                    vos.forEach(dto -> {
                        dto.setEnterpriseId(ti.getEnterpriseId());
                        dto.setStoreId(shoppingCart.getStoreId());
                        dto.setCreateId(ti.getUserId());
                        dto.setCreateName(ti.getRealName());
                        dto.setInternal(true);
                        for (CreateOrderVo.CreateOrderItemIDTO item : dto.getItems()) {
                            QYAssert.isTrue(null != item && null != item.getQuantity(), "数量不能为空");
                        }
                    });
                    if (isXd) {
                        ThreadLocalUtils.setXd(true);
                    }
                    ThreadLocalUtils.setAdmin(true);
                    ThreadLocalUtils.setOrderType(OrderTypeEnum.AGENT_ORDER.getCode());

                    // 校验B端库存依据
                    Map<Long, BigDecimal> orderQuantityMap = new HashMap<>();
                    vos.get(0).getItems().forEach(dto ->{
                        orderQuantityMap.put(dto.getCommodityId(), dto.getQuantity());
                    });
                    List<BStockShortResponseVO> responseVOList = bStockService.checkBStock(vos.get(0).getStoreId(), OrderTypeEnum.AGENT_ORDER.getCode(), DateUtil.parseDate(vos.get(0).getOrderTime(), "yyyy-MM-dd"), orderQuantityMap, "代理提交订单", null,ti.getUserId());
                    if(CollectionUtils.isNotEmpty(responseVOList)){
                        responseVOList.forEach(dto ->{
                            dto.setShoppingCartId(cartId);
                        });
                        return new CreateOrderFilterVo(true,null,responseVOList);
                    }

                    return orderService.createOrder(vos);
                } else {
                    return new CreateOrderFilterVo(true, null, null);
                }
            } finally {
                lock.unlock();
            }
        }else {
            QYAssert.isFalse("系统繁忙,请勿频繁操作!");
        }
        return new CreateOrderFilterVo(true, null, null);
    }

    /**
     * 管理员版本  一键提交订单
     */
    @ApiOperation(value = "一键提交订单", notes = "一键提交订单", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @RequestMapping(value = "/batchCreateOrderAdmin", method = RequestMethod.POST)
    public CreateOrderFilterVo batchCreateOrderAdmin(@RequestBody CreateOrderVo createOrderVo) throws Exception {
        TokenInfo ti = FastThreadLocalUtil.getQY();
        CreateOrderFilterVo vo = new CreateOrderFilterVo();
        RLock lock = redissonClient.getLock(LockConstants.batchCreateOrderAdmin);
        if (lock.tryLock()){
            try {
                createOrderVo.setBigShop(createOrderVo.getBigShop() == null ? false : createOrderVo.getBigShop());
                vo = xDShoppingCartService.batchCreateOrderAdmin(ti.getUserId(),ti.getRealName(),createOrderVo.getBigShop());
            }finally{
                lock.unlock();
            }
        }else {
            QYAssert.isFalse("系统繁忙,请勿频繁操作!");
        }
        return vo;
    }

    @ApiOperation(value = "代理购物车商品明细数", notes = "代理购物车商品明细数", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @RequestMapping(value = "/getAdminCommodityCount", method = RequestMethod.GET)
    public Long getAdminCommodityCount(@RequestParam(value = "bigShop",required = false) Boolean bigShop){
        TokenInfo ti = FastThreadLocalUtil.getQY();
        return xDShoppingCartService.getAdminCommodityCount(ti.getUserId(), bigShop);
    }

    @ApiOperation(value = "测试提交提货卡订单", notes = "测试提交提货卡订单", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @RequestMapping(value = "/createTakeCardOrder", method = RequestMethod.POST)
    public void createTakeCardOrder(@RequestParam(value = "endTime",required = false) String endTime)throws Throwable{
        generateCreateTakeCardOrderTasks.scheduleJobs();
    }
}
