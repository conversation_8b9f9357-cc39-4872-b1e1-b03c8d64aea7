package com.pinshang.qingyun.order.service;

import com.github.pagehelper.PageInfo;
import com.pinshang.qingyun.base.configure.expand.QYAssert;
import com.pinshang.qingyun.base.enums.YesOrNoEnums;
import com.pinshang.qingyun.base.enums.xda.XdaOrderProcessStatusEunm;
import com.pinshang.qingyun.box.utils.*;
import com.pinshang.qingyun.common.dto.DictionaryODTO;
import com.pinshang.qingyun.common.service.DictionaryClient;
import com.pinshang.qingyun.order.enums.OrderModeType;
import com.pinshang.qingyun.order.enums.OrderPrintTypeEnum;
import com.pinshang.qingyun.order.mapper.*;
import com.pinshang.qingyun.order.model.order.*;
import com.pinshang.qingyun.order.util.RestTemplateUtil;
import com.pinshang.qingyun.order.vo.store.StoreCompanyVo;
import com.pinshang.qingyun.settlement.base.dto.SettlementOrderIdto;
import com.pinshang.qingyun.settlement.base.dto.SettlementSourceItemVo;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.*;

/**
 * @Author: xx
 */
@Slf4j
@Service
public class ConsumerOrderSaveService {

    @Autowired
    private OrderListGiftMapper orderListGiftMapper;
    @Autowired
    private OrderMapper orderMapper;
    @Autowired
    private OrderListMapper orderListMapper;
    @Autowired
    private StoreMapper storeMapper;
    @Lazy
    @Autowired
    private OrderService orderService;
    @Autowired
    private RestTemplateUtil restTemplateUtil;
    @Autowired
    com.pinshang.qingyun.common.service.DictionaryClient dictionaryClient;





    public void pullConsumerOrder(String url,String date,int sourceType,
                                  List<OrderListGift> orderListGifts,
                                  List<OrderList> orderLists,Order order
                                  ){
        SettlementOrderIdto settlementOrderIdto = new SettlementOrderIdto();
        settlementOrderIdto.setSourceTime(date);
        settlementOrderIdto.setPageSize(500);
        settlementOrderIdto.setSourceType(sourceType);
        settlementOrderIdto.setSourceTime(date);
        url += "settlementInterfaceController/selectConsumerOrderListByParams";
        ParameterizedTypeReference<PageInfo<SettlementSourceItemVo>> parameterizedTypeReference
                = new ParameterizedTypeReference<PageInfo<SettlementSourceItemVo>>() {
        };
        PageInfo<SettlementSourceItemVo> listApiResponse = null;
        int index  = 1;
        BigDecimal realTotalPrice = java.math.BigDecimal.ZERO;
        BigDecimal totalPrice= java.math.BigDecimal.ZERO;
        do{
            settlementOrderIdto.setPageNo(index);
            listApiResponse = restTemplateUtil.postForList(url, settlementOrderIdto, parameterizedTypeReference);
            if(!listApiResponse.getList().isEmpty()){
                List<SettlementSourceItemVo> list = listApiResponse.getList();
                for (SettlementSourceItemVo settlementSourceItemVo : list) {
                    OrderListGift orderListGift = new OrderListGift();
                    orderListGift.setCommodityId(settlementSourceItemVo.getCommodityId());
                    orderListGift.setCommodityPrice(settlementSourceItemVo.getUnitPrice());
                    orderListGift.setCommodityNum(settlementSourceItemVo.getNumber());
                    orderListGift.setTotalPrice(settlementSourceItemVo.getTotalPrice());
                    orderListGift.setType(1);
                    orderListGift.setRealQuantity(settlementSourceItemVo.getDeliveryNumber());
                    orderListGift.setRealTotalPrice(settlementSourceItemVo.getDeliveryTotalPrice());
                    OrderList orderList = new OrderList();
                    BeanUtils.copyProperties(orderListGift,orderList);
                    orderListGifts.add(orderListGift);
                    orderLists.add(orderList);
                    realTotalPrice = realTotalPrice.add(orderListGift.getRealTotalPrice());
                    totalPrice = totalPrice.add(orderListGift.getTotalPrice());
                }
            }
            index++;
        }while (listApiResponse.isHasNextPage());
        order.setFinalAmount(totalPrice);
        order.setOrderAmount(totalPrice);
        order.setRealTotalPrice(realTotalPrice);
    }


    @SneakyThrows
    @Transactional(rollbackFor = Exception.class)
    public Long saveOrder(String str){
        QYAssert.isTrue(StringUtils.isNotBlank(str),"xxl job 备注配置错误 url");
        List<DictionaryODTO> dictionaryODTOS = dictionaryClient.querySubDictionaryByParentOptionCode("C_B_ORDER");
        if(SpringUtil.isNotEmpty(dictionaryODTOS)) {
            String[] split = str.split(",");
            String url=split[0];
            for (DictionaryODTO dictionaryODTO : dictionaryODTOS) {
                try{
                    Long storeId=Long.valueOf(dictionaryODTO.getMemo());
                    int sourceType = Integer.valueOf(dictionaryODTO.getOptionValue());
                    Calendar calendar = Calendar.getInstance();
                    calendar.add(Calendar.DATE,-1);
                    String orderDate = TimeUtil.parseSimpleDateTime(calendar.getTime());
                    if(split.length==5){
                        orderDate = split[4];
                    }
                    List<OrderListGift> orderListGifts = new ArrayList<>();
                    List<OrderList> orderLists = new ArrayList<>();
                    Order order = new Order();
                    pullConsumerOrder(url,orderDate,sourceType,
                            orderListGifts,orderLists,order);
                    if(orderLists.isEmpty()){
                        log.warn("抓取{}订单数据为空",str);
                        return 0L;
                    }
                    Date date = new Date();
                    order.setEnterpriseId(78L);
                    order.setCreateId(-1L);
                    order.setUpdateId(order.getCreateId());
                    order.setCreateTime(date);
                    order.setUpdateTime(date);
                    order.setStoreId(storeId);
                    order.setOrderTime(TimeUtil.parseSimpleDateTime(orderDate));
                    order.setRealOrderTime(TimeUtil.parseSimpleDateTime(orderDate));
                    order.setOrderCode(IDGenerator.newOrderCode());
                    order.setEnterpriseId(78L);
                    order.setPrintNum(0);
                    order.setOrderType(sourceType);
                    // 配送费默认为0
                    order.setFreightAmount(BigDecimal.ZERO);
                    order.setModeType(OrderModeType.ORDER.getCode());
                    order.setPrintType(OrderPrintTypeEnum.NOPRInteger);
                    order.setOrderStatus(0);
                    order.setChangePriceStatus(YesOrNoEnums.NO.getCode());
                    order.setSyncStatus(YesOrNoEnums.NO.getCode());
                    order.setSettleStatus(0);
                    order.setProcessStatus(XdaOrderProcessStatusEunm.DELIVERY_COMPLETED.getCode());
                    // 根据storeId获取所属公司ID
                    StoreCompanyVo storeCompanyVo = storeMapper.getStoreCompanyByStoreId(storeId);
                    order.setCompanyId(storeCompanyVo.getCompanyId());
                    order.setStoreTypeId(storeCompanyVo.getStoreTypeId());
                    order.setOrderRemark(split[3]);
                    order.setRealSubOrderDoneStatus(YesOrNoEnums.YES.getCode());
                    order.setSettleOrderTime(order.getRealOrderTime());
                    this.orderMapper.insert(order);
                    // 订单 mirror
                    orderService.crateOrderMirror(order);
                    for (int i = 0; i < orderLists.size(); i++) {
                        OrderList orderList = orderLists.get(i);
                        orderList.setOrderId(order.getId());
                        OrderListGift orderListGift = orderListGifts.get(i);
                        orderListGift.setOrderId(order.getId());
                    }
                    this.orderListMapper.insertList(orderLists);
                    this.orderListGiftMapper.insertList(orderListGifts);
                }catch (Exception e){
                    log.error("抓取C端订单生成B端订单失败：{}",e);
                }
            }
        }
        return 0L;
    }
}
