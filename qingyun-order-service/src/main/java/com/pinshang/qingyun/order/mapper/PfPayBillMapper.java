package com.pinshang.qingyun.order.mapper;


import com.pinshang.qingyun.base.mybatis.MyMapper;
import com.pinshang.qingyun.order.mapper.entry.pf.recharge.PfPayBillEntry;
import com.pinshang.qingyun.order.model.pf.recharge.PfPayBill;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
@Mapper
public interface PfPayBillMapper extends MyMapper<PfPayBill> {

	/**
	 * 查询批发支付流水
	 * @param billCodes
	 * @return
	 */
	List<PfPayBillEntry> findPfPayBillByBillCodes(@Param("billCodes")List<String> billCodes);

}

