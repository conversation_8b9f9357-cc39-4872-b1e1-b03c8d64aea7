package com.pinshang.qingyun.order.controller;

import com.github.pagehelper.PageInfo;
import com.pinshang.qingyun.base.exception.ParamException;
import com.pinshang.qingyun.order.mapper.entry.order.OrderReplenishmentListEntry;
import com.pinshang.qingyun.order.service.OrderReplenishmentService;
import com.pinshang.qingyun.order.vo.order.OrderReplenishmentIVO;
import com.pinshang.qingyun.order.vo.order.OrderReplenishmentListVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping("/orderReplenishment")
public class OrderReplenishmentController {

    @Autowired
    private OrderReplenishmentService orderReplenishmentService;

    /**
     * 门店补货列表
     * @param orderReplenishmentListVo
     * @return
     */
    @RequestMapping(value = "/list", method = RequestMethod.POST)
    public PageInfo<OrderReplenishmentListEntry> orderReplenishmentList(@RequestBody OrderReplenishmentListVo orderReplenishmentListVo) {
        PageInfo <OrderReplenishmentListEntry> pageInfo = orderReplenishmentService.orderReplenishmentList(orderReplenishmentListVo);
        return pageInfo;
    }

    /**
     * 批量导入新增门店补货信息
     * @param list
     * @return
     */
    @RequestMapping(value = {"/importOrderReplenishment"}, method = {RequestMethod.POST})
    public Object importShopReplenishment(@RequestBody List<OrderReplenishmentIVO> list)throws ParamException {
        return orderReplenishmentService.importShopReplenishment(list);
    }
}
