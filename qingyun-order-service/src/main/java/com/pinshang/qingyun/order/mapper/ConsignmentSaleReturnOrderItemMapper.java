package com.pinshang.qingyun.order.mapper;

import com.pinshang.qingyun.base.mybatis.MyMapper;
import com.pinshang.qingyun.order.dto.consignment.AuditConsignmentReturnOrdeItemIDTO;
import com.pinshang.qingyun.order.dto.consignment.ConfirmConsignmentReturnOrdeItemIDTO;
import com.pinshang.qingyun.order.dto.consignment.ConsignmentSaleReturnOrderItemODTO;
import com.pinshang.qingyun.order.model.order.ConsignmentSaleReturnOrderItem;
import com.pinshang.qingyun.order.model.order.SaleReturnOrderItem;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * Created by honway on 2017/11/2 16:21.
 */
@Repository
public interface ConsignmentSaleReturnOrderItemMapper extends MyMapper<ConsignmentSaleReturnOrderItem> {

    List<ConsignmentSaleReturnOrderItemODTO> queryBySaleReturnOrderId(Long saleReturnOrderId);

    void batchUpdateByConfirm(@Param("list") List<ConfirmConsignmentReturnOrdeItemIDTO> confirmSaleReturnOrderItems);

    void batchUpdateByAudit(@Param("list") List<AuditConsignmentReturnOrdeItemIDTO> auditSaleReturnOrderItems);
}
