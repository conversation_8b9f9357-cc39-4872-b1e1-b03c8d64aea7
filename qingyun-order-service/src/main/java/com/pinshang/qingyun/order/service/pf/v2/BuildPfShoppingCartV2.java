package com.pinshang.qingyun.order.service.pf.v2;

import com.pinshang.qingyun.base.configure.expand.QYAssert;
import com.pinshang.qingyun.base.enums.order.ProductTypeEnum;
import com.pinshang.qingyun.box.utils.BeanCloneUtils;
import com.pinshang.qingyun.box.utils.CollectorsUtil;
import com.pinshang.qingyun.box.utils.DateUtil;
import com.pinshang.qingyun.box.utils.SpringUtil;
import com.pinshang.qingyun.order.dto.pf.PfCommodityLimit4AppODTO;
import com.pinshang.qingyun.order.dto.shopcart.ShoppingCartCommodityODTO;
import com.pinshang.qingyun.order.dto.shopcart.ShoppingCartGroupODTO;
import com.pinshang.qingyun.order.dto.shopcart.ShoppingCartODTO;
import com.pinshang.qingyun.order.enums.GiftModelConditionTypeEnum;
import com.pinshang.qingyun.order.enums.GiftModelTypeEnum;
import com.pinshang.qingyun.order.mapper.OrderListMapper;
import com.pinshang.qingyun.order.mapper.OrderMapper;
import com.pinshang.qingyun.order.mapper.PfShoppingCartMapper;
import com.pinshang.qingyun.order.mapper.StoreDurationMapper;
import com.pinshang.qingyun.order.model.gift.GiftModel;
import com.pinshang.qingyun.order.model.gift.GiftModelCondition;
import com.pinshang.qingyun.order.model.gift.GiftProduct;
import com.pinshang.qingyun.order.model.pf.PfShoppingCart;
import com.pinshang.qingyun.order.model.store.Store;
import com.pinshang.qingyun.order.model.store.StoreDuration;
import com.pinshang.qingyun.order.service.StoreService;
import com.pinshang.qingyun.order.service.xda.v4.ToBService;
import com.pinshang.qingyun.order.util.BeanUtil;
import com.pinshang.qingyun.order.vo.PfCommodityLimitAppIDTO;
import com.pinshang.qingyun.pf.product.dto.front.PfCommodityAppIDTO;
import com.pinshang.qingyun.pf.product.dto.front.PfCommodityAppODTO;
import com.pinshang.qingyun.pf.product.service.PfCommodityFrontClient;
import com.pinshang.qingyun.storage.dto.tob.CommodityInventoryDetailIDTO;
import com.pinshang.qingyun.storage.dto.tob.CommodityInventoryODTO;
import lombok.extern.slf4j.Slf4j;
import tk.mybatis.mapper.entity.Example;

import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2024/3/12 16:55
 */
@Slf4j
public class BuildPfShoppingCartV2 {

    private Long storeId;

    private Date deliveryDate;

    private ShoppingCartODTO shoppingCartODTO;

    private PfShoppingCartMapper pfShoppingCartMapper;
    private ToBService toBService;
    private OrderMapper orderMapper;
    private OrderListMapper orderListMapper;

    private PfCommodityFrontClient pfCommodityFrontClient;
    private StoreService storeService;
    private StoreDurationMapper storeDurationMapper;

    public BuildPfShoppingCartV2(Long storeId, Date deliveryDate,ToBService toBService, PfShoppingCartMapper pfShoppingCartMapper,
                               OrderMapper orderMapper, OrderListMapper orderListMapper, PfCommodityFrontClient pfCommodityFrontClient,
                               StoreService storeService, StoreDurationMapper storeDurationMapper) {
        this.storeId = storeId;
        this.deliveryDate = deliveryDate;
        this.toBService = toBService;
        this.pfShoppingCartMapper = pfShoppingCartMapper;
        this.orderMapper = orderMapper;
        this.orderListMapper = orderListMapper;
        this.pfCommodityFrontClient = pfCommodityFrontClient;
        this.storeService = storeService;
        this.storeDurationMapper = storeDurationMapper;
    }

    public ShoppingCartODTO shopCartList() {
        return buildCommodityInfo(queryItems(storeId));
    }

    public List<PfShoppingCart> queryItems(Long storeId) {
        Example ex = new Example(PfShoppingCart.class);
        ex.createCriteria().andEqualTo("storeId", storeId);
        return pfShoppingCartMapper.selectByExample(ex);
    }

    public ShoppingCartODTO buildCommodityInfo(List<PfShoppingCart> shoppingCarts) {
        this.initShoppingCartODTO();
        shoppingCartODTO.setCanSettlement(true);

        Example example = new Example(StoreDuration.class);
        example.createCriteria().andEqualTo("storeId", storeId);
        StoreDuration storeDuration = storeDurationMapper.selectOneByExample(example);
        if (null != storeDuration) {
            shoppingCartODTO.setTopTips("客户订货时间段：" + storeDuration.getBeginTime() + "~" + storeDuration.getEndTime());
        }
        if (shoppingCarts.size() == 0) {
            setWarnMessage("购物车商品为空。");
            return shoppingCartODTO;
        }

        List<ShoppingCartCommodityODTO> commodityODTOS = this.getValidCommodity(shoppingCarts, deliveryDate, storeId);
        //失效商品需要显示数量
        // 商品分组，判断箱规，商品限量，计算商品金额，计算冷冻商品倍数
        Map<Long, PfShoppingCart> shopCartMap = shoppingCarts.stream().collect(Collectors.toMap(PfShoppingCart::getCommodityId, Function.identity()));
        List<ShoppingCartCommodityODTO> invalidateList = shoppingCartODTO.getInvalidateGroup().getCommodities();
        invalidateCommodity(invalidateList, shopCartMap);
        if (commodityODTOS.isEmpty()) {
            setWarnMessage("购物车内没有有效商品。");
            return shoppingCartODTO;
        }

        singleCommodityLogic(commodityODTOS, shopCartMap);

        // 赠品方案
        pfProcessGifts(commodityODTOS);
        //计算库存
        pfCommodityLimit();
        // 计算商品金额是否达到目标
        Store store = storeService.findStoreByStoreId(storeId);
        BigDecimal minDeliveryAmount = store.getMinDeliveryAmount();
        if(null != minDeliveryAmount && minDeliveryAmount.compareTo(shoppingCartODTO.getSummation()) > 0){
            BigDecimal subAmount = minDeliveryAmount.subtract(shoppingCartODTO.getSummation());
            shoppingCartODTO.setBottomTips("起送金额为"+minDeliveryAmount+"元，还差"+subAmount+"元");
            setWarnMessage("起送金额为"+minDeliveryAmount+"元，还差"+subAmount+"元，请重新核对购物车");
        }
        shoppingCartODTO.setCommodityNum(this.getValidCommodityNumSum());
        return shoppingCartODTO;
    }
    public BigDecimal getValidCommodityNumSum(){
        BigDecimal commodityNumSum = BigDecimal.ZERO;
        if(null != shoppingCartODTO.getNormalGroup()){
            if(SpringUtil.isNotEmpty(shoppingCartODTO.getNormalGroup().getCommodities())){
                commodityNumSum = commodityNumSum.add(shoppingCartODTO.getNormalGroup().getCommodities().stream().collect(CollectorsUtil.summingBigDecimalMax(ShoppingCartCommodityODTO::getQuantity)));
            }
        }
        if(null != shoppingCartODTO.getFreezeGroups()){
            if(SpringUtil.isNotEmpty(shoppingCartODTO.getFreezeGroups().getCommodities())){
                commodityNumSum = commodityNumSum.add(shoppingCartODTO.getFreezeGroups().getCommodities().stream().collect(CollectorsUtil.summingBigDecimalMax(ShoppingCartCommodityODTO::getQuantity)));
            }
        }
        if(null != shoppingCartODTO.getGiftGroup()){
            if(SpringUtil.isNotEmpty(shoppingCartODTO.getGiftGroup().getCommodities())){
                commodityNumSum = commodityNumSum.add(shoppingCartODTO.getGiftGroup().getCommodities().stream().collect(CollectorsUtil.summingBigDecimalMax(ShoppingCartCommodityODTO::getQuantity)));
            }
        }
        return commodityNumSum;
    }
    public void pfCommodityLimit(){
        List<CommodityInventoryDetailIDTO> orderCommodityList = toBService.getPfInventoryOrderCommodityList(shoppingCartODTO);
        List<CommodityInventoryODTO> commodityInventoryODTOS = toBService.queryCommodityInventory(deliveryDate, orderCommodityList, null,null,null);
        Map<Integer, Map<Long, CommodityInventoryODTO>> commodityLevelMap = commodityInventoryODTOS.stream().collect(Collectors.groupingBy(CommodityInventoryODTO::getLevel, Collectors.toMap(CommodityInventoryODTO::getCommodityId, e -> e)));
        //订单判断使用
        Map<Long, Integer> commodityStockTypeMap = commodityInventoryODTOS.stream().collect(Collectors.toMap(CommodityInventoryODTO::getCommodityId, CommodityInventoryODTO::getStockType, (v1, v2) -> v1));
        shoppingCartODTO.setCommodityStockTypeMap(commodityStockTypeMap);

        Map<Long, CommodityInventoryODTO> commodityInventoryODTOMap = commodityLevelMap.get(ProductTypeEnum.NORMAL.getCode());

        Map<Long, CommodityInventoryODTO> giftCommodityInventoryODTOMap = commodityLevelMap.get(ProductTypeEnum.GIFT.getCode());

        shoppingCartODTO.getNormalGroup().getCommodities().forEach(item->{
            extracted(item, commodityInventoryODTOMap);
        });
        shoppingCartODTO.getFreezeGroups().getCommodities().forEach(item->{
            extracted(item, commodityInventoryODTOMap);
        });
        for (ShoppingCartCommodityODTO item : shoppingCartODTO.getGiftGroup().getCommodities()) {
            Long commodityId = item.getCommodityId();
            BigDecimal commodityMaxNumber = item.getCommodityMaxNumber();
            Long conditionId = item.getConditionId();
            BigDecimal usedQuantity = getUsedQuantity(item.getLimitStartTime(),item.getLimitEndTime(), item.getCommodityId(),conditionId);
            if(null != commodityMaxNumber){
                BigDecimal subtract = commodityMaxNumber.subtract(usedQuantity);
                if(subtract.compareTo(BigDecimal.ZERO) <0){
                    item.setQuantity(BigDecimal.ZERO);
                    item.setStockWarningTips("赠完即止，余量为" + BigDecimal.ZERO);
                    continue;
                }
                if(subtract.compareTo(item.getQuantity()) < 0 ){
                    item.setQuantity(subtract);
                }
            }
            if(SpringUtil.isNotEmpty(giftCommodityInventoryODTOMap) && giftCommodityInventoryODTOMap.containsKey(commodityId)){
                CommodityInventoryODTO commodityInventoryODTO = giftCommodityInventoryODTOMap.get(commodityId);
                if(null != commodityInventoryODTO && null != commodityInventoryODTO.getInventoryQuantity()){

                    if(commodityInventoryODTO.getInventoryQuantity().compareTo(BigDecimal.ZERO) <= 0){
                        item.setQuantity(BigDecimal.ZERO);
                        item.setStockWarningTips("赠完即止，余量为" + BigDecimal.ZERO);
                        continue;
                    }
                    BigDecimal inventoryQuantity = commodityInventoryODTO.getInventoryQuantity();
                    if(item.getQuantity().compareTo(inventoryQuantity) > 0){
                        BigDecimal bigDecimal = inventoryQuantity.compareTo(BigDecimal.ZERO) > 0 ? inventoryQuantity : BigDecimal.ZERO;
                        BigDecimal bigDecimal1 = new BigDecimal(bigDecimal.stripTrailingZeros().toPlainString());
                        item.setQuantity(bigDecimal);
                        item.setStockWarningTips("赠完即止，余量为" + bigDecimal1);
                    }
                    commodityInventoryODTO.setInventoryQuantity(commodityInventoryODTO.getInventoryQuantity().subtract(item.getQuantity()));
                }
            }
        }
    }

    private void extracted(ShoppingCartCommodityODTO item, Map<Long, CommodityInventoryODTO> commodityInventoryODTOMap) {
        Long commodityId = item.getCommodityId();
        BigDecimal quantity = item.getQuantity();
        if(commodityInventoryODTOMap.containsKey(commodityId)){
            CommodityInventoryODTO commodityInventoryODTO = commodityInventoryODTOMap.get(commodityId);
            if(null == commodityInventoryODTO || null == commodityInventoryODTO.getInventoryQuantity()){
                return;
            }
            BigDecimal inventoryQuantity = commodityInventoryODTO.getInventoryQuantity();
            if(quantity.compareTo(inventoryQuantity) > 0 || commodityInventoryODTO.getInventoryQuantity().compareTo(BigDecimal.ZERO) <= 0 ){
                commodityInventoryODTO.setInventoryQuantity(BigDecimal.ZERO);
                BigDecimal bigDecimal = inventoryQuantity.compareTo(BigDecimal.ZERO) < 0 ? BigDecimal.ZERO : inventoryQuantity;
                BigDecimal bigDecimal1 = new BigDecimal(bigDecimal.stripTrailingZeros().toPlainString());
                item.setStockWarningTips("库存不足，余量为" + bigDecimal1);
                item.setAbleAdd(Boolean.FALSE);
                setWarnMessage(item.getCommodityName()+"库存不足，余量为"+bigDecimal1+"，请确认数量");
            }else {
                commodityInventoryODTO.setInventoryQuantity(inventoryQuantity.subtract(quantity));
            }
            commodityInventoryODTOMap.put(item.getCommodityId(),commodityInventoryODTO);
        }
    }

    public void pfProcessGifts(List<ShoppingCartCommodityODTO> odtos) {
        Map<String, Object> paramMap =new HashMap<>();
        paramMap.put("storeId", storeId);
        paramMap.put("orderTime",deliveryDate);

        // 1. 查询门店对应的 赠品方案列表 t_gift_model {id, giftModelType}可能是多个赠品方案，每个赠品方案的类型可能不一样)
        List<GiftModel> giftModelList = this.orderMapper.findGiftModelByStoreId(paramMap);
        if(SpringUtil.isEmpty(giftModelList)){
            return;
        }
        Map<Long, List<ShoppingCartCommodityODTO>> conditionMap = new HashMap<>();
        List<ShoppingCartCommodityODTO> giftCommodityList = new ArrayList<>();
        for(GiftModel gm : giftModelList){
            BigDecimal conditionValue = this.getConditionValue(gm, odtos);
            GiftModelCondition condition = orderMapper.queryUseGiftModelCondition(gm.getId(), conditionValue);
            if(condition == null){
                continue;
            }
            // 条件类型: 1.赠送一次 2.累计赠送
            Integer conditionType = condition.getConditionType();

            // 获取赠品方案中赠送的商品列表 t_gift_product(commodity_id、commodity_number商品数量、commodity_max_number商品最大数量)
            List<GiftProduct> giftProductList = this.orderMapper.findGiftProductByGiftModelConditionId(condition.getId());
            List<Long> commodityList = giftProductList.stream().map(GiftProduct :: getCommodityId).collect(Collectors.toList());
            PfCommodityAppIDTO build = PfCommodityAppIDTO.builder().storeId(storeId).commodityIdList(commodityList).orderTime(deliveryDate).build();
            List<PfCommodityAppODTO> pfCommodityAppODTOS = pfCommodityFrontClient.queryPfCommodityListForApp(build);

            List<Long> commodityIdList = pfCommodityAppODTOS.stream().filter(item -> item.getAppStatus().equals(0) && item.getIsCanOrder()).map(PfCommodityAppODTO::getCommodityId).collect(Collectors.toList());

            Map<Long,GiftProduct> giftProductMap = giftProductList.stream().filter(item -> commodityIdList.contains(item.getCommodityId()) ).collect(Collectors.toMap(GiftProduct::getCommodityId, Function.identity()));
            if(commodityIdList.isEmpty()){
                continue;
            }
            // 调用接口查询商品信息
            List<PfCommodityAppODTO> pfCommodityInfoAppODTOList = queryCommodityBaseInfo(commodityIdList,deliveryDate, storeId, false);
            if (pfCommodityInfoAppODTOList.isEmpty()){
                continue;
            }
            List<ShoppingCartCommodityODTO> commodityODTOS = BeanCloneUtils.copyTo(pfCommodityInfoAppODTOList, ShoppingCartCommodityODTO.class);
            commodityODTOS.forEach(p ->{
                GiftProduct giftProduct = giftProductMap.get(p.getCommodityId());
                p.setCommodityPrice(BigDecimal.ZERO);
                p.setIsSpecialPrice(0);
                //如果商品有限量,判断限量是否足够，如果不足限量数量为赠品数量
                BigDecimal number = giftProduct.getCommodityNumber();
                p.setConditionId(gm.getId());
                p.setCommodityMaxNumber(giftProduct.getCommodityMaxNumber());
                p.setQuantity(number);
                p.setLimitStartTime(gm.getBeginDate());
                p.setLimitEndTime(gm.getEndDate());
            });
            if (GiftModelConditionTypeEnum.CUMULATIVE_GIVE.getCode().equals(conditionType)){
                BigDecimal cumulative = conditionValue.divideAndRemainder(condition.getConditionValue())[0];
                commodityODTOS.forEach(p ->{
                    GiftProduct giftProduct = giftProductMap.get(p.getCommodityId());
                    BigDecimal number = p.getQuantity().multiply(cumulative).setScale(3, BigDecimal.ROUND_HALF_UP);
                    p.setCommodityMaxNumber(giftProduct.getCommodityMaxNumber());
                    //判断库存是否足够，如果不足取库存数量为赠品数量
                    p.setConditionId(gm.getId());
                    p.setQuantity(number);
                    p.setLimitStartTime(gm.getBeginDate());
                    p.setLimitEndTime(gm.getEndDate());
                });
            }
            commodityODTOS.removeIf(item -> item.getQuantity().compareTo(BigDecimal.ZERO) ==0);
            if(null != commodityODTOS && commodityODTOS.size() > 0){
                conditionMap.put(gm.getId(), commodityODTOS);
                giftCommodityList.addAll(commodityODTOS);
            }
        }
        giftCommodityList = BeanUtil.deepCopy(giftCommodityList);
        shoppingCartODTO.setConditionMap(conditionMap);
        shoppingCartODTO.getGiftGroup().setCommodities(merge(giftCommodityList));
    }
    public BigDecimal getUsedQuantity(Date limitStartTime,Date limitEndTime,Long commodityId,Long conditionId){
        String startTime = DateUtil.getDateFormate(limitStartTime, "yyyy-MM-dd");
        String endTime = DateUtil.getDateFormate(limitEndTime, "yyyy-MM-dd");

        List<PfCommodityLimitAppIDTO> pfCommodityLimit4AppIDTOS = new ArrayList<>();
        pfCommodityLimit4AppIDTOS.add(PfCommodityLimitAppIDTO.builder().startTime(startTime).endTime(endTime).commodityId(commodityId).build());
        List<PfCommodityLimit4AppODTO> pfCommodityLimit4AppODTOS = orderListMapper.queryPfCommodityV2Limit(conditionId,pfCommodityLimit4AppIDTOS);
        BigDecimal usedQuantity = BigDecimal.ZERO;
        if(!pfCommodityLimit4AppODTOS.isEmpty() && pfCommodityLimit4AppODTOS.get(0) != null){
            usedQuantity = pfCommodityLimit4AppODTOS.get(0).getTotalQuantity();
        }
        return usedQuantity;
    }

    /**
     * 单个商品逻辑
     * 商品分组，判断箱规，商品限量，计算商品金额
     * @param odtos
     * @param map
     */
    public void singleCommodityLogic(List<ShoppingCartCommodityODTO> odtos, Map<Long,PfShoppingCart> map){
        List<ShoppingCartCommodityODTO> normalComm = new ArrayList<>();
        odtos.forEach(p -> {
            // 判断箱规
            PfShoppingCart cart = map.get(p.getCommodityId());
            QYAssert.notNull(cart, "购物车商品不存在");
            p.setShoppingCartId(cart.getId());
            BigDecimal remainder = cart.getQuantity().divideAndRemainder(p.getSalesBoxCapacity())[1];
            if(remainder.compareTo(BigDecimal.ZERO) != 0){
                setWarnMessage(p.getCommodityName() + "商品数量必须是箱规的倍数，请重新核对购物车");
                p.setBoxWarningTips("商品数量必须是箱规的倍数");
            }
            p.setQuantity(cart.getQuantity());
            // 商品分组
            normalComm.add(p);
            // 计算商品总金额
            shoppingCartODTO.setOriginalAmount(shoppingCartODTO.getOriginalAmount().add(p.originAmount()));
            shoppingCartODTO.setSummation(shoppingCartODTO.getSummation().add(p.saleAmount()));
        });
        if(normalComm.isEmpty()){
            setWarnMessage("购物车内没有有效商品无法结算！");
        }
        shoppingCartODTO.getNormalGroup().setCommodities(normalComm);
        shoppingCartODTO.setDiscountTotal(shoppingCartODTO.getOriginalAmount().subtract(shoppingCartODTO.getSummation()));
    }

    /**
     *
     * @param p
     * @param usedQuantity
     * @param isSpecialPrice  ture 特价商品  false 普通商品
     */
    private void checkLimitNumber(ShoppingCartCommodityODTO p,BigDecimal usedQuantity,Boolean isSpecialPrice){

//        Long commodityId = p.getCommodityId();
//        BigDecimal quantity =p.getQuantity();
//        BigDecimal limitNumber = isSpecialPrice ? p.getSpecialPriceLimit() : p.getLimitNumber();
//        BigDecimal canUseCommNum = limitNumber.subtract(isSpecialPrice ? BigDecimal.ZERO : getUsedQuantity(p.getLimitStartTime(),p.getLimitEndTime(), commodityId));
//        canUseCommNum = canUseCommNum.compareTo(BigDecimal.ZERO) >= 0 ? canUseCommNum : BigDecimal.ZERO;
//        boolean canAddComm = quantity.compareTo(canUseCommNum) > 0;
//        boolean ableAddComm = quantity.compareTo(canUseCommNum) <= 0;
//        if(null == p.getAbleAdd() || !ableAddComm){
//            p.setAbleAdd(ableAddComm);
//        }
//        if(canAddComm){
//            String error = isSpecialPrice ? "商品特价限量"+canUseCommNum+p.getCommodityUnitName():"商品库存不足，仅剩" + canUseCommNum;
//            p.setStockWarningTips(error);
//        }
//        BigDecimal limitNumber = isSpecialPrice ? p.getSpecialPriceLimit(): p.getLimitNumber();
//        BigDecimal canUseCommNum = limitNumber.subtract(usedQuantity);
//        canUseCommNum = canUseCommNum.compareTo(BigDecimal.ZERO) >= 0 ? canUseCommNum : BigDecimal.ZERO;
//        boolean canAddComm  = p.getQuantity().compareTo(canUseCommNum) > 0;
//        boolean ableAddComm = p.getQuantity().compareTo(canUseCommNum) < 0;
//        p.setAbleAdd(ableAddComm);
//        if(canAddComm){
//            String error = isSpecialPrice ? "商品特价限量"+canUseCommNum+p.getCommodityUnitName():"商品库存不足，仅剩" + canUseCommNum;
//            p.setStockWarningTips(error);
//            String errorMessage = isSpecialPrice ? p.getCommodityName() + "商品特价限量不足，仅剩" + canUseCommNum + "，请重新核对购物车" : p.getCommodityName() + "商品库存不足，仅剩" + canUseCommNum + "，请重新核对购物车";
//            setWarnMessage(errorMessage);
//        }
    }
    public void invalidateCommodity(List<ShoppingCartCommodityODTO> odtos, Map<Long,PfShoppingCart> map){
        if (odtos.isEmpty()){
            return;
        }
        odtos.forEach(p->{
            PfShoppingCart cart = map.get(p.getCommodityId());
            if(cart == null){
                return;
            }
            p.setQuantity(cart.getQuantity());
        });
        shoppingCartODTO.getInvalidateGroup().setCommodities(odtos);
    }

    public List<ShoppingCartCommodityODTO> getValidCommodity(List<PfShoppingCart> shoppingCarts, Date orderDate,
                                                             Long storeId) {
        List<Long> commodityIds = shoppingCarts.stream().map(PfShoppingCart::getCommodityId)
                .collect(Collectors.toList());
        // 调用接口获取商品基本信息
        List<PfCommodityAppODTO> pfCommodityInfoAppODTOList = queryCommodityBaseInfo(commodityIds, orderDate, storeId,
                true);
        if (pfCommodityInfoAppODTOList.isEmpty()) {
            return new ArrayList<ShoppingCartCommodityODTO>(0);
        }
        List<ShoppingCartCommodityODTO> commodityODTOS = BeanCloneUtils.copyTo(pfCommodityInfoAppODTOList,
                ShoppingCartCommodityODTO.class);

        List<ShoppingCartCommodityODTO> invalidateComm = commodityODTOS.stream()
                .filter(com -> null != com.getIsCanOrder() && !com.getIsCanOrder()).collect(Collectors.toList());
        // 移除失效商品
        commodityODTOS.removeIf(odto -> null != odto.getIsCanOrder() && !odto.getIsCanOrder());
        if (shoppingCartODTO != null) {
            shoppingCartODTO.getInvalidateGroup().setCommodities(invalidateComm);
            shoppingCartODTO.setVarietySum(commodityODTOS.size());
        }
        return commodityODTOS;
    }

    /**
     * 查询商品基本信息
     *
     * @param commodityIds
     * @return
     */
    public List<PfCommodityAppODTO> queryCommodityBaseInfo(List<Long> commodityIds, Date orderTime, Long storeId,
                                                           boolean isShopCart) {
        PfCommodityAppIDTO appIDTO = new PfCommodityAppIDTO();
        appIDTO.setCommodityIdList(commodityIds);
        appIDTO.setNeedAppDown(true);
        appIDTO.setOrderTime(orderTime);
        appIDTO.setStoreId(storeId);
        List<PfCommodityAppODTO> pfCommodityInfoAppODTOList = pfCommodityFrontClient
                .queryPfCommodityListForApp(appIDTO);

        /**
         * 如果购物车中的商品和查询的结果不同,将不存在的商品从购物车表中删除
         */
        if (commodityIds.size() != pfCommodityInfoAppODTOList.size()) {
            Set<Long> differenceSet = new HashSet<>(commodityIds);
            differenceSet.removeAll(pfCommodityInfoAppODTOList.stream().map(PfCommodityAppODTO::getCommodityId)
                    .collect(Collectors.toSet()));
            log.error("部分商品未能找到!ids:{}", Arrays.toString(differenceSet.toArray()));
            if (isShopCart && !differenceSet.isEmpty()) {
                pfShoppingCartMapper.deleteShopCart(storeId, new ArrayList<>(differenceSet));
            }
        }
        return pfCommodityInfoAppODTOList;
    }

    /**
     * 给结算和下单用，下单和结算凭此依据判断购物车商品能否结算
     */
    private void setWarnMessage(String message) {
        shoppingCartODTO.setWarnMessage(message);
        shoppingCartODTO.setCanSettlement(false);
    }

    public void initShoppingCartODTO() {
        shoppingCartODTO = new ShoppingCartODTO();
        shoppingCartODTO.setSummation(BigDecimal.ZERO);
        shoppingCartODTO.setOriginalAmount(BigDecimal.ZERO);
        shoppingCartODTO.setDiscountTotal(BigDecimal.ZERO);
        ShoppingCartGroupODTO normalGroup = new ShoppingCartGroupODTO();
        normalGroup.setCommodities(new ArrayList<>());
        normalGroup.setFreezeWarnTips("");
        shoppingCartODTO.setNormalGroup(normalGroup);
        ShoppingCartGroupODTO freezeGroups = new ShoppingCartGroupODTO();
        freezeGroups.setCommodities(new ArrayList<>());
        freezeGroups.setFreezeWarnTips("");
        shoppingCartODTO.setFreezeGroups(freezeGroups);
        ShoppingCartGroupODTO invalidateGroup = new ShoppingCartGroupODTO();
        invalidateGroup.setCommodities(new ArrayList<>());
        invalidateGroup.setFreezeWarnTips("");
        shoppingCartODTO.setInvalidateGroup(invalidateGroup);
        ShoppingCartGroupODTO giftGroup = new ShoppingCartGroupODTO();
        giftGroup.setCommodities(new ArrayList<>());
        giftGroup.setFreezeWarnTips("");
        shoppingCartODTO.setGiftGroup(giftGroup);
        shoppingCartODTO.setConditionMap(new HashMap<>());

    }

    public BigDecimal getConditionValue(GiftModel gm, List<ShoppingCartCommodityODTO> odtos){
        GiftModelTypeEnum giftModelType = GiftModelTypeEnum.fromName(gm.getGiftModelType());

        BigDecimal conditionValue = BigDecimal.ZERO;
        switch (giftModelType){
            case ORDERAMOUT:
                conditionValue = shoppingCartODTO.getSummation();
                break;
            case COMMODITYNUMBER:
                List<String> commodityCodeList = Arrays.asList(gm.getCommmdityCodes().split(","));
                for(ShoppingCartCommodityODTO odto : odtos){
                    if(commodityCodeList.contains(odto.getCommodityCode())){
                        conditionValue = conditionValue.add(odto.getQuantity());
                    }
                }
                break;
            case COMMODITYAMOUT:
                List<String> commodityCodeAmountList = Arrays.asList(gm.getCommmdityCodes().split(","));
                for(ShoppingCartCommodityODTO odto : odtos){
                    BigDecimal applyPrice =  odto.saleAmount();
                    if(commodityCodeAmountList.contains(odto.getCommodityCode())){
                        conditionValue = conditionValue.add(applyPrice);
                    }
                }
                break;
            default:
                break;
        }
        return conditionValue;
    }

    public static List<ShoppingCartCommodityODTO> merge(List<ShoppingCartCommodityODTO> list) {
        return new ArrayList<>(list.stream()
                .collect(Collectors.toMap(ShoppingCartCommodityODTO::getCommodityId, a -> a, (o1, o2) -> {
                    o1.setQuantity(o1.getQuantity().add(o2.getQuantity()));
                    return o1;
                })).values());
    }
}
