package com.pinshang.qingyun.order.service;

import com.pinshang.qingyun.order.mapper.GiftModelMapper;
import com.pinshang.qingyun.order.mapper.PromotionStkMapper;
import com.pinshang.qingyun.order.model.gift.GiftModel;
import com.pinshang.qingyun.order.model.promotionStk.PromotionStk;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @Author: sk
 * @Date: 2024/7/18
 */
@Slf4j
@Service
public class PromotionStkService {

    @Autowired
    PromotionStkMapper promotionStkMapper;

    public List<PromotionStk> findDistributionByStoreId(Long storeId, String orderTime){
        return promotionStkMapper.findDistributionByStoreId(storeId,orderTime);
    }


}
