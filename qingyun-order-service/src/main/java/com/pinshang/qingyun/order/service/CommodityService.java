package com.pinshang.qingyun.order.service;

import com.alibaba.fastjson.JSON;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.pinshang.qingyun.box.utils.SpringUtil;
import com.pinshang.qingyun.order.dto.cup.ProductPriceDto;
import com.pinshang.qingyun.order.mapper.CommodityMapper;
import com.pinshang.qingyun.order.mapper.entry.order.OrderListEntry;
import com.pinshang.qingyun.order.model.commodity.Commodity;
import com.pinshang.qingyun.order.vo.cup.CommodityListQueryDTO;
import com.pinshang.qingyun.order.vo.cup.CommodityListQueryReqVO;
import com.pinshang.qingyun.price.dto.storePromotion.StorePromotionCommodityPriceODTO;
import com.pinshang.qingyun.price.dto.storePromotion.StorePromotionCommodityPriceSearchIDTO;
import com.pinshang.qingyun.price.service.StorePromotionClient;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.ParseException;
import java.time.LocalDate;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @Author: sk
 * @Date: 2022/8/1
 */
@Slf4j
@Service
public class CommodityService {

    @Autowired
    private RedissonClient redissonClient;
    @Autowired
    private CommodityMapper commodityMapper;
    @Autowired
    StorePromotionClient storePromotionClient;

    private static String ORDER_COMMODITY_FREEZE_GROUP = "ORDER:COMMODITY:FREEZE:GROUP:";

    /**
     * 获取冻品凑整商品组
     * @return
     */
    public List<Long> getCommodityFreezeGroup(){
       /* RBucket<List<Long>> bucket = redissonClient.getBucket(ORDER_COMMODITY_FREEZE_GROUP);
        List<Long> freezeGroupList  = bucket.get();
        if(CollectionUtils.isNotEmpty(freezeGroupList)){
            return freezeGroupList;
        }*/

        List<Long> freezeGroupList = commodityMapper.getCommodityFreezeGroup();
       if(CollectionUtils.isEmpty(freezeGroupList)){
            freezeGroupList.add(999999999L);
        }

        /* bucket.set(freezeGroupList, DateUtil.getSurplusSeconds(), TimeUnit.SECONDS);*/
        return freezeGroupList;
    }

    /**
     * 批量查询商品信息并转为Map
     */
    public Map<Long, Commodity> findCommodityInfoByIdMap(List<Long> commodityIds) {
        Example selectExample = new Example(Commodity.class);
        selectExample.createCriteria().andIn("id", commodityIds);
        List<Commodity> commodityInfoByIdList = commodityMapper.selectByExample(selectExample);
        log.info("根据商品id集合查询商品详情,commodityIds:{},返回:{}", JSON.toJSONString(commodityIds), JSON.toJSONString(commodityInfoByIdList));
        if (CollectionUtils.isEmpty(commodityInfoByIdList)) {
            return Collections.emptyMap();
        }
        return commodityInfoByIdList.stream().collect(Collectors.toMap(Commodity::getId, Function.identity()));
    }

    /**
     * 批量查询商品信息并转为Map
     */
    public List<Commodity> findCommodityByIdList(List<Long> commodityIds) {
        if(CollectionUtils.isEmpty(commodityIds)){
            return null;
        }
        Example selectExample = new Example(Commodity.class);
        selectExample.createCriteria().andIn("id", commodityIds);
        List<Commodity> commodityInfoByIdList = commodityMapper.selectByExample(selectExample);
       return commodityInfoByIdList;

    }

    /**
     * 根据商品code查询商品信息
     * @param commodityCodes
     * @param addCommodityType  1:按条码搜索  2:按商品编码搜索
     * @return
     */
    public List<Commodity> findCommodityByCodeList(List<String> commodityCodes, Integer addCommodityType) {
        if(CollectionUtils.isEmpty(commodityCodes)){
            return null;
        }
        Example selectExample = new Example(Commodity.class);
        if(addCommodityType.equals(1)) {
            selectExample.createCriteria().andIn("barCode", commodityCodes);
        }else {
            selectExample.createCriteria().andIn("commodityCode", commodityCodes);
        }

        List<Commodity> commodityInfoByIdList = commodityMapper.selectByExample(selectExample);
        return commodityInfoByIdList;

    }

    public PageInfo<Commodity> commodityList(CommodityListQueryReqVO reqVO) {

        PageInfo<Commodity> pageData = findStoreCommodityByParams(reqVO);
        if(pageData !=null){
            processCommodityPromotion(pageData.getList(),reqVO.getStoreId(), reqVO.getOrderTime());
        }
        return pageData;
    }

    public PageInfo<Commodity> findStoreCommodityByParams(CommodityListQueryReqVO reqVO) {

        if (reqVO.getStoreId() == null) {
            return null;
        }

        return PageHelper.startPage(reqVO.getPageNo(), reqVO.getPageSize()).doSelectPageInfo(() -> {
            this.commodityMapper.findStoreCommodityByParams(CommodityListQueryDTO.convert(reqVO));
        });

    }

    public List<Commodity> findStoreCommodityListByParams(CommodityListQueryReqVO reqVO) {

        if (reqVO.getStoreId() == null) {
            return null;
        }


        return   this.commodityMapper.findStoreCommodityByParams(CommodityListQueryDTO.convert(reqVO));
    }

    private void processCommodityPromotion(List<Commodity> commodityList, Long storeId,String orderTime) {

        Map<Long, StorePromotionCommodityPriceODTO> storePromotionMap = this.selectCommodityPromotionToMap(
                commodityList
                        .stream()
                        .map(Commodity::getId)
                        .distinct()
                        .collect(Collectors.toList()),storeId,orderTime);

        commodityList.forEach(
                commodity -> {
                    Long commodityId = commodity.getId();
                    StorePromotionCommodityPriceODTO storePromotionCommodityPriceODTO = storePromotionMap.get(commodityId);
                    if (storePromotionCommodityPriceODTO != null
                            && storePromotionCommodityPriceODTO.getPrice().compareTo(commodity.getCommodityPrice()) < 0) {
                        commodity.setOriginalCommodityPrice(commodity.getCommodityPrice());
                        commodity.setCommodityPrice(storePromotionCommodityPriceODTO.getPrice());
                        Integer limitNumber = storePromotionCommodityPriceODTO.getLimitNumber();
                        String tagName;
                        if (0 == limitNumber) {
                            tagName = "特价";
                        } else {
                            tagName = "特价|每天限[" + limitNumber + "]份";
                        }
                        commodity.setPromotionFlag(1);
                        commodity.setTagName(tagName);
                        commodity.setLimitNumber(storePromotionCommodityPriceODTO.getLimitNumber());
                        commodity.setAvailableStoreLimit(storePromotionCommodityPriceODTO.getAvailableStoreLimit());
                        commodity.setAvailableTotalLimit(storePromotionCommodityPriceODTO.getAvailableTotalLimit());


                        //处理特价
                        BigDecimal productNum = commodity.getProductNumber();
                        if(productNum !=null){
                            BigDecimal availableStoreLimit = commodity.getAvailableStoreLimit();
                            BigDecimal availableTotalLimit = commodity.getAvailableTotalLimit();
                            BigDecimal availableLimit = availableTotalLimit.min(availableStoreLimit);
                            BigDecimal specialQuantity;
                            BigDecimal normalQuantity;
                            BigDecimal noneLimit = new BigDecimal(999999999);

                            if( 0 == limitNumber && noneLimit.compareTo(availableTotalLimit) == 0){
                                specialQuantity = productNum;
                                normalQuantity = BigDecimal.ZERO;
                            }else{
                                BigDecimal salesBoxCapacity = BigDecimal.valueOf(commodity.getSalesBoxCapacity());
                                availableLimit = salesBoxCapacity.multiply(availableLimit.divide(salesBoxCapacity,0, RoundingMode.FLOOR));

                                BigDecimal productNumSalesBoxCapacity = salesBoxCapacity.multiply(productNum.divide(salesBoxCapacity,0,RoundingMode.FLOOR));
                                specialQuantity = availableLimit.min(productNumSalesBoxCapacity);
                                normalQuantity = productNum.subtract(specialQuantity);
                            }
                            commodity.setPromotionCount(specialQuantity);
                            commodity.setNormalCount(normalQuantity);
                        }

                    }
                }
        );

    }

    public Map<Long, StorePromotionCommodityPriceODTO> selectCommodityPromotionToMap(List<Long> commodityList, Long storeId, String orderTime) {
        if(SpringUtil.isEmpty(commodityList)){
            return new HashMap<>();
        }

        orderTime = StringUtils.isNotBlank(orderTime) ? orderTime : LocalDate.now().toString();

        StorePromotionCommodityPriceSearchIDTO idto = new StorePromotionCommodityPriceSearchIDTO();
        idto.setStoreId(storeId);
        idto.setNeedAvailableLimit(1);
        try {
            idto.setOrderTime(DateUtils.parseDate(orderTime,"yyyy-MM-dd"));
        } catch (ParseException e) {
            e.printStackTrace();
        }

        Map<Long, StorePromotionCommodityPriceODTO> resultMap = new HashMap<>();
        List<Long> idList = new ArrayList<>();
        commodityList.forEach(c->{
            if(idList.size() ==2000){
                idto.setCommodityIdList(idList);
                Map<Long,StorePromotionCommodityPriceODTO> map = storePromotionClient.getStorePromotionCommodityMapByParams(idto);
                if(SpringUtil.isNotEmpty(map)){
                    resultMap.putAll(map);
                }
                idList.clear();
            }
            idList.add(c);
        });

        if(SpringUtil.isNotEmpty(idList)){
            idto.setCommodityIdList(idList);
            Map<Long,StorePromotionCommodityPriceODTO> map = storePromotionClient.getStorePromotionCommodityMapByParams(idto);
            if(SpringUtil.isNotEmpty(map)){
                resultMap.putAll(map);
            }
            idList.clear();

        }
        return resultMap;
    }

    public List<ProductPriceDto> findStoreCommodityByStoreId(String storeId, List<Long> productIds) {
        return commodityMapper.findStoreCommodityByStoreId(storeId,productIds);
    }

    public List<Commodity> findStoreCommodityByStoreIdAndCommodityCode(String storeId, List<String> commodityCodeList, int addCommodityType) {
        return commodityMapper.findStoreCommodityByStoreIdAndCommodityCode(storeId,commodityCodeList,addCommodityType);
    }

    public Commodity findCommodityByCommodityCode(String commodityCode){

        Example example = new Example(Commodity.class);
        example.createCriteria().andEqualTo("commodityCode",commodityCode);
        return commodityMapper.selectOneByExample(example);
    }
}
