package com.pinshang.qingyun.order.controller;

import com.pinshang.qingyun.order.mapper.entry.order.OrderMonitorEntry;
import com.pinshang.qingyun.order.mapper.entry.order.SubOrderMonitorEntry;
import com.pinshang.qingyun.order.service.OrderMonitorService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2018/10/12 9:57
 */
@RestController
@RequestMapping("/orderMonitor")
public class OrderMonitorController {
    @Autowired
    private OrderMonitorService orderMonitorService;

    @RequestMapping(value = "/queryOrderInfo/{orderCode}", method = RequestMethod.GET)
    public OrderMonitorEntry queryOrderInfo(@PathVariable("orderCode") String orderCode) {
        return orderMonitorService.queryOrderInfo(orderCode);
    }

    @RequestMapping(value = "/querySubOrderInfo/{orderCode}",method = RequestMethod.GET)
    public List<SubOrderMonitorEntry> querySubOrderInfo(@PathVariable("orderCode") String orderCode){
        return orderMonitorService.querySubOrderInfo(orderCode);
    }
}
