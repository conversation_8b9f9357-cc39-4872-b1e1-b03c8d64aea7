package com.pinshang.qingyun.order.service;

import com.pinshang.qingyun.order.mapper.PromotionSaleMapper;
import com.pinshang.qingyun.order.model.promotionSale.PromotionSale;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @Author: sk
 * @Date: 2024/7/18
 */
@Slf4j
@Service
public class PromotionSaleService {

    @Autowired
    PromotionSaleMapper promotionSaleMapper;

    public List<PromotionSale> findRatioByStoreId(Long storeId, String orderTime){
        return promotionSaleMapper.findRatioByStoreId(storeId,orderTime);
    }


}
