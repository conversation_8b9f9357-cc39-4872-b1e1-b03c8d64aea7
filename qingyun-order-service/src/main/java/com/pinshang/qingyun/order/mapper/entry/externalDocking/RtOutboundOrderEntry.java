package com.pinshang.qingyun.order.mapper.entry.externalDocking;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 大润发出库单
 * <AUTHOR>
 * @date 2023年2月15日10:00:09
 */
@Data
public class RtOutboundOrderEntry {

    @ApiModelProperty(value = "收货单位")
    private String receivingUnit;

    @ApiModelProperty(value = "联系电话")
    private String phone;

    @ApiModelProperty(value = "出库时间")
    private String deliveryTime;

    @ApiModelProperty(value = "配送车辆")
    private String deliveryCar;

    @ApiModelProperty(value = "配送人员")
    private String deliveryPerson;

    @ApiModelProperty(value = "登记人")
    private String registrar;

    @ApiModelProperty(value = "产品SKU-大润发商品编码")
    private String rtCommodityCode;

    @ApiModelProperty(value = "产品名称")
    private String rtCommodityName;

    @ApiModelProperty(value = "出库数量")
    private String deliveryNumber;

    @ApiModelProperty(value = "出库单价")
    private String deliveryPrice;

    /**送货日期**/
    @ApiModelProperty(hidden = true)
    private String orderTime;

    /**商品总数量**/
    @ApiModelProperty(hidden = true)
    private BigDecimal rtCommodityNum;

    /**换算率**/
    @ApiModelProperty(hidden = true)
    private BigDecimal conversionRate;
}
