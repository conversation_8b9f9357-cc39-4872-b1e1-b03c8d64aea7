package com.pinshang.qingyun.order.mapper.entry.order;

import lombok.Data;

import java.math.BigDecimal;

/**
 * @Author: sk
 * @Date: 2019/12/6
 */
@Data
public class XdProposedOrderSettingEntry {

    /** 销售量系数 */
    private BigDecimal saleRatio;

    /** 正常库存系数 */
    private BigDecimal stockRatio;

    /** 缺品被查看次数系数 */
    private BigDecimal defectiveRatio;

    /** 损耗1（过保&临保） 系数 */
    private BigDecimal loss1Ratio;

    /** 损耗2（非过保非临保）系数 */
    private BigDecimal loss2Ratio;

    private BigDecimal day1;
    private BigDecimal day2;
    private BigDecimal day3;
    private BigDecimal day4;
    private BigDecimal day5;
    private BigDecimal day6;
    private BigDecimal day7;
}
