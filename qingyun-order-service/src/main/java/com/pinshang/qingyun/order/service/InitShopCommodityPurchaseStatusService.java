package com.pinshang.qingyun.order.service;

import com.pinshang.qingyun.box.utils.SpringUtil;
import com.pinshang.qingyun.order.mapper.CommodityPurchaseStatusMapper;
import com.pinshang.qingyun.order.mapper.ShopMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @description:
 * @author: hhf
 * @time: 2022/10/11/011 10:42
 */
@Service
@Slf4j
public class InitShopCommodityPurchaseStatusService {

    @Autowired
    private ShopMapper shopMapper;
    @Autowired
    private CommodityPurchaseStatusMapper commodityPurchaseStatusMapper;

    /**
     * 期初门店id_商品id
     */
    public void initShopCommodityPurchaseStatusShopIdAndCommodityId(){
        //查询门店id集合
        List<Long> shopIdList = shopMapper.findAllShopIdList();
        if(SpringUtil.isNotEmpty(shopIdList)){
            for (Long shopId : shopIdList) {
                //根据门店id查询门店商品可采状态id集合
                List<Long> commodityPurchaseStatusIdList = commodityPurchaseStatusMapper.findCommodityPurchaseStatusIdListByShopId(shopId);
                if(SpringUtil.isNotEmpty(commodityPurchaseStatusIdList)){
                    //更新字段:shop_commodity  = 门店id_商品id
                    for (Long id : commodityPurchaseStatusIdList) {
                        commodityPurchaseStatusMapper.initShopCommodityById(id);
                    }
                }
            }
        }
    }
}
