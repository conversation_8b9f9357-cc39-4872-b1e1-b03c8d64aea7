package com.pinshang.qingyun.order.service.tob.impl;

import com.alibaba.fastjson2.util.DateUtils;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.pinshang.qingyun.base.configure.expand.BizLogicException;
import com.pinshang.qingyun.base.enums.OrderStatusEnums;
import com.pinshang.qingyun.order.bo.QueryOrderBo;
import com.pinshang.qingyun.order.bo.QueryTakeBO;
import com.pinshang.qingyun.order.dto.tob.*;
import com.pinshang.qingyun.order.mapper.OrderMapper;
import com.pinshang.qingyun.order.mapper.ProcessOrderCommodityStatisticsAdminMapper;
import com.pinshang.qingyun.order.mapper.TakeAppointmentCardMapper;
import com.pinshang.qingyun.order.model.tob.ToBProcessOrderCommodityStatistics;
import com.pinshang.qingyun.order.service.tob.ToBOrderStatisticsService;
import com.pinshang.qingyun.order.vo.tob.AdminToBOrderStatisticReqVo;
import com.pinshang.qingyun.order.vo.tob.AdminToBOrderStatisticRespVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;

import java.util.*;

/**
 * @Description：
 * @Author：ZhangHui
 * @Package：com.pinshang.qingyun.order.service.tob.impl
 * @Date: 2024/4/7
 */
@Service
@Slf4j
public class ToBOrderStatisticsServiceImpl implements ToBOrderStatisticsService {

    @Autowired
    private ProcessOrderCommodityStatisticsAdminMapper processOrderCommodityStatisticsAdminMapper;

    @Autowired
    private OrderMapper orderMapper;

    @Autowired
    private TakeAppointmentCardMapper takeAppointmentCardMapper;

    private static final String orderTimeIsNull = "同步数据出错，请传入送货时间!";

    private static final String commodityIdIsNull = "同步数据出错，请传入商品唯一标识!";

    @Override
    public PageInfo<AdminToBOrderStatisticRespVo> queryToBOrderStatisticPage(AdminToBOrderStatisticReqVo reqVo) {

        PageInfo<AdminToBOrderStatisticRespVo> pageInfo = PageHelper.startPage(reqVo.getPageNo(),
                reqVo.getPageSize()).doSelectPageInfo(() -> processOrderCommodityStatisticsAdminMapper.queryToBOrderStatisticPage(reqVo));
        return pageInfo;
    }



    @Override
    public void repairData(List<ToBOrderStatisticsRepairIDTO> repairIDTOList) {
        Map<String, ToBOrderStatisticsRepairIDTO> repairIDTOMap = new HashMap<>();
        List<Long> commodityIdList = new ArrayList<>();
        List<String> orderTimeList = new ArrayList<>();
        for (ToBOrderStatisticsRepairIDTO toBOrderStatisticsRepairIDTO : repairIDTOList) {
            if (null == toBOrderStatisticsRepairIDTO.getOrderTime()) {
                throw new BizLogicException(orderTimeIsNull);
            }
            if (null == toBOrderStatisticsRepairIDTO.getCommodityId()) {
                throw new BizLogicException(commodityIdIsNull);
            }
            String orderTime = toBOrderStatisticsRepairIDTO.getOrderTime();
            String commodityId = String.valueOf(toBOrderStatisticsRepairIDTO.getCommodityId());
            commodityIdList.add(toBOrderStatisticsRepairIDTO.getCommodityId());
            orderTimeList.add(orderTime);
            String key = orderTime + "," + commodityId;
            if (!repairIDTOMap.containsKey(key)) {
                repairIDTOMap.put(key, toBOrderStatisticsRepairIDTO);
            }
        }


        Example example = new Example(ToBProcessOrderCommodityStatistics.class);
        example.createCriteria().andIn("commodityId", commodityIdList).andIn("orderTime", orderTimeList);
        List<ToBProcessOrderCommodityStatistics> commodityStatisticsList = processOrderCommodityStatisticsAdminMapper.selectByExample(example);

        //定义两个集合，updateList是更新的集合，insertList是需要插入的集合
        List<ToBOrderStatisticsRepairIDTO> updateList = new ArrayList<>();
        List<ToBOrderStatisticsRepairIDTO> insertList = new ArrayList<>();
        List<String> updateMapKey = new ArrayList<>();
        for (ToBProcessOrderCommodityStatistics toBProcessOrderCommodityStatistics : commodityStatisticsList) {
            String orderTime = DateUtils.format(toBProcessOrderCommodityStatistics.getOrderTime(), "yyyy-MM-dd");
            String commodityId = String.valueOf(toBProcessOrderCommodityStatistics.getCommodityId());
            String key = orderTime + "," + commodityId;
            if (repairIDTOMap.containsKey(key)) {
                updateMapKey.add(key);
                updateList.add(repairIDTOMap.get(key));
            }
        }


        List<String> insertMapKey = new ArrayList<>();
        for (ToBOrderStatisticsRepairIDTO toBOrderStatisticsRepairIDTO : repairIDTOList) {
            String orderTime = toBOrderStatisticsRepairIDTO.getOrderTime();
            String commodityId = String.valueOf(toBOrderStatisticsRepairIDTO.getCommodityId());
            String key = orderTime + "," + commodityId;
            if (!updateMapKey.contains(key) && !insertMapKey.contains(key)) {
                insertMapKey.add(key);
                insertList.add(toBOrderStatisticsRepairIDTO);
            }
        }

        if (CollectionUtils.isNotEmpty(updateList)) {
            processOrderCommodityStatisticsAdminMapper.batchUpdate(updateList);
        }
        if (CollectionUtils.isNotEmpty(insertList)) {
            processOrderCommodityStatisticsAdminMapper.batchInsert(insertList);
        }
    }


    @Override
    public ToBOrderAndTakeAppointmentDataODTO queryToBOrderAndTakeAppointmentData(ToBOrderAndTakeAppointmentDataIDTO dataIDTO) {


        ToBOrderAndTakeAppointmentDataODTO toBOrderAndTakeAppointmentDataODTO = new ToBOrderAndTakeAppointmentDataODTO();

        //统计正常和取消订单的数量
        QueryOrderBo queryOrderBo = new QueryOrderBo();
        List<Integer> orderStatus = new ArrayList<>();
        orderStatus.add(OrderStatusEnums.NORMAL.getCode());
        orderStatus.add(OrderStatusEnums.CANCELED.getCode());
        queryOrderBo.setOrderStatus(orderStatus);
        queryOrderBo.setOrderTimes(dataIDTO.getOrderTimes());
        queryOrderBo.setCommodityIds(dataIDTO.getCommodityIds());
        queryOrderBo.setStartOrderTime(dataIDTO.getStartOrderTime());
        queryOrderBo.setEndOrderTime(dataIDTO.getEndOrderTime());
        List<ToBOrderDataODTO> toBOrderDataODTOList = orderMapper.selectCountByOrderStatus(queryOrderBo);
        toBOrderAndTakeAppointmentDataODTO.setToBOrderDataODTOList(toBOrderDataODTOList);

        //查询提货卡的数量和份数
        QueryTakeBO queryTakeBO = new QueryTakeBO();
        queryTakeBO.setOrderTimes(dataIDTO.getOrderTimes());
        queryTakeBO.setCommodityIds(dataIDTO.getCommodityIds());
        queryTakeBO.setStartOrderTime(dataIDTO.getStartOrderTime());
        queryTakeBO.setEndOrderTime(dataIDTO.getEndOrderTime());
        //查询状态为未下单的
        queryTakeBO.setOrderStatus(0);
        List<TobOrderTakeQuantityAndNumberODTO> takeQuantityAndNumberODTOList = takeAppointmentCardMapper.selectQuantityAndNumber(queryTakeBO);
        toBOrderAndTakeAppointmentDataODTO.setTobOrderTakeQuantityAndNumberList(takeQuantityAndNumberODTOList);
        return toBOrderAndTakeAppointmentDataODTO;
    }

    @Override
    public Integer queryOrderStatisticsCount(ToBQueryOrderStatisticsIDTO statisticsIDTO) {
        Example example = new Example(ToBProcessOrderCommodityStatistics.class);
        Example.Criteria criteria = example.createCriteria();
        if (StringUtils.isNotBlank(statisticsIDTO.getCommodityId())) {
            criteria.andEqualTo("commodityId", statisticsIDTO.getCommodityId());
        }
        if (StringUtils.isNotBlank(statisticsIDTO.getStartOrderTime())) {
            criteria.andGreaterThanOrEqualTo("orderTime", statisticsIDTO.getStartOrderTime());
        }
        if (StringUtils.isNotBlank(statisticsIDTO.getEndOrderTime())) {
            criteria.andLessThanOrEqualTo("orderTime", statisticsIDTO.getEndOrderTime());
        }
        int count = processOrderCommodityStatisticsAdminMapper.selectCountByExample(example);
        return count;
    }

    @Override
    public PageInfo<ToBQueryOrderStatisticsODTO> queryOrderStatisticsList(ToBQueryOrderStatisticsIDTO statisticsIDTO) {
        return PageHelper.startPage(statisticsIDTO.getPageNo(),
                statisticsIDTO.getPageSize()).doSelectPageInfo(() -> processOrderCommodityStatisticsAdminMapper.queryOrderStatisticsList(statisticsIDTO));
    }
}
