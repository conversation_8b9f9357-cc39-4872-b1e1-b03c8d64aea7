package com.pinshang.qingyun.order.service.syncOrderJob;

import com.pinshang.qingyun.base.enums.StoreBillTypeEnums;
import com.pinshang.qingyun.base.enums.order.OrderTypeEnum;
import com.pinshang.qingyun.base.enums.storage.StockTypeEnum;
import com.pinshang.qingyun.box.utils.BeanCloneUtils;
import com.pinshang.qingyun.box.utils.DateTimeUtil;
import com.pinshang.qingyun.box.utils.DateUtil;
import com.pinshang.qingyun.order.bo.StoreDeductionBO;
import com.pinshang.qingyun.order.bo.StoreRechargeBO;
import com.pinshang.qingyun.order.dto.order.AssociationOrderODTO;
import com.pinshang.qingyun.order.mapper.OrderListMapper;
import com.pinshang.qingyun.order.mapper.OrderMapper;
import com.pinshang.qingyun.order.mapper.SyncOrderMapper;
import com.pinshang.qingyun.order.model.order.*;
import com.pinshang.qingyun.order.service.*;
import com.pinshang.qingyun.order.vo.order.SaleReturnItemAddVo;
import com.pinshang.qingyun.storage.dto.tob.CommodityInventoryODTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;


/**
 * 同步订单
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class SyncOrderService {
    @Autowired
    SyncOrderMapper syncOrderMapper;
    @Autowired
    private SubOrderService subOrderService;
    @Autowired
    private OrderListService orderListService;
    @Autowired
    private OrderListGiftService orderListGiftService;
    @Autowired
    private StoreRechargeService storeRechargeService;
    @Autowired
    private StoreSettlementService storeSettlementService;
    @Autowired
    private OrderMapper orderMapper;
    @Autowired
    private BStockService bStockService;
    @Autowired
    private OrderListMapper orderListMapper;
    @Autowired
    private WeChatSendMessageService weChatSendMessageService;

    /**
     * 覆盖订单数据逻辑。
     *
     * <p>
     * 1.生鲜/一期 下单后，若触发补货逻辑，会导致下单付款金额与最终金额不一致
     * 2.执行覆盖订单job
     * 3.将配货商品从t_order_list_gift 查出来，插入到t_order_list
     * 4.将t_order_list的主键更新到t_sub_order_item
     * 5.将t_order的order_amount更新成最终金额final_amount
     * 6.先回款order_amount，再重新扣款final_amount
     * 7.将覆盖后的订单的同步状态sync_status改为已同步
     * </p>
     *
     * @param deliveryTime 配送时间。
     */
    @Transactional(rollbackFor = Exception.class)
    public void rewriteOrderData(DeliveryTime deliveryTime) {
        log.info("\n=====syncOrderAndSO2DOJob 开始执行：线路组Id={}，覆盖时间={}，执行时间={}",
                deliveryTime.getLineGroupId(), deliveryTime.getCoverTime(), new Date());

        // 查询 两天内([昨天，明天])、总金额=最终金额、未同步的订单ID，修改同步状态为1
        List<Long> notSyncOrderIds = syncOrderMapper.listNotSyncOrderIds(deliveryTime);
        if (CollectionUtils.isNotEmpty(notSyncOrderIds)) {
            syncOrderMapper.updateNotSyncByOrderId(notSyncOrderIds);
        }

        // 查询并处理 两天内([昨天，明天])、未同步的订单
        List<Order> ordersToSync = syncOrderMapper.listTomorrowOrderId(deliveryTime);
        ordersToSync.forEach(this::processOrder);

        log.info("\n=====syncOrderAndSO2DOJob 执行结束=========");
    }

    /**
     * 处理单个订单。
     *
     * @param order 订单对象。
     */
    private void processOrder(Order order) {
        if (order.getFinalAmount() == null || order.getOrderAmount() == null) {
            log.debug("orderId [{}] 价格数据为空", order.getId());
            return;
        }

        if (order.getFinalAmount().compareTo(order.getOrderAmount()) == 0) {
            log.debug("orderId [{}] 价格一致，标记为已同步", order.getId());
            syncOrderMapper.modifySyncStatus(order.getId());
            return;
        }

        //将t_order_list_gift里面有的配货商品，而t_order_list没有的。同步到t_order_list
        syncOrderListGift(order.getId());

        // 处理预付费客户逻辑
        handlePreStoreRecharge(order);
    }

    /**
     * 同步订单明细。
     *
     * @param orderId 订单ID。
     */
    public void syncOrderListGift(Long orderId) {
        // 根据订单id查询orderList明细
        List<OrderList> orderLists = orderListService.getOrderListsByOrderId(orderId);
        if (CollectionUtils.isEmpty(orderLists)) {
            return;
        }

        // 目前客服下单(1期)和生鲜下单存在配比，需要同步到t_order_list
        // 配比只存在正常商品里面
        Boolean isRatio = syncRatioOrderList(orderId);

        // 对配货商品进行覆盖
        syncRationOrderList(orderId, orderLists);

        // 如果存在配比，则对比order_list和order_list_gift，对比总金额是否一致.不一致则发告警消息
        if(isRatio) {
            compareOrderListTotalPrice(orderId);
        }
    }

    /**
     * 如果存在配比，则对比order_list和order_list_gift，对比总金额是否一致.不一致则发告警消息
     * @param orderId
     */
    private void compareOrderListTotalPrice(Long orderId) {
        List<OrderList>  orderList = orderListService.getOrderListsByOrderId(orderId);
        List<OrderListGift> orderListGift = orderListGiftService.getOrderGiftByOrderId(orderId);

        BigDecimal orderListTotalPrice = orderList.stream().map(OrderList::getTotalPrice).reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal orderListGiftTotalPrice = orderListGift.stream().map(OrderListGift::getTotalPrice).reduce(BigDecimal.ZERO, BigDecimal::add);
        if(orderListTotalPrice.compareTo(orderListGiftTotalPrice) != 0) {
            weChatSendMessageService.sendWeChatMessage("处理配比后订单明细金额不一致,orderId:" + orderId);
        }
    }

    /**
     * order_list     order_gift_list
     *     原始数量    配比后数量
     *   商品 A10          A12
     *
     *   特价剩余8
     *    A8        A8
     *    A2        A4
     *
     *   特价剩余10,此种情况很少，基本上忽略
     *    A10      A10
     *             A2
     *
     *   特价剩余12
     *    A10     A12
     * @param orderId
     * @return
     */
    private Boolean syncRatioOrderList(Long orderId) {
        AtomicReference<Boolean> isRatio = new AtomicReference<>(false);
        Order order = orderMapper.selectByPrimaryKey(orderId);

        List<OrderList> insertList = new ArrayList<>();
        if(OrderTypeEnum.PC_ORDER.getCode().equals(order.getOrderType())
                 || OrderTypeEnum.APP_ORDER.getCode().equals(order.getOrderType())) {
            // 查询orderList和orderGiftList,相同commodity + price一致，但是数量不一致。说明存在配比
            List<OrderListGift> giftProductList = orderListGiftService.getOrderGiftProductByOrderId(orderId);
            List<OrderList>  productList = orderListService.getOrderListsProductByOrderId(orderId);

            // 获取库存依据
            Map<Long, BigDecimal> orderQuantityGiftMap = new HashMap<>();
            productList.forEach(dto ->{
                orderQuantityGiftMap.put(dto.getCommodityId(), dto.getCommodityNum());
            });
            Map<Long, CommodityInventoryODTO> getbStockMap = bStockService.getbStockMap(DateUtil.parseDate(DateTimeUtil.defaultDeliveryDate(), "yyyy-MM-dd"), orderQuantityGiftMap);

            Map<String, List<OrderList>> orderListMap = productList.stream().collect(Collectors.groupingBy(item -> {
                return item.getCommodityId() + "_" + item.getCommodityPrice().stripTrailingZeros().toPlainString();
            }));

            giftProductList.forEach(giftDto ->{
                CommodityInventoryODTO inventoryODTO = getbStockMap.get(giftDto.getCommodityId());
                // 只有不限量的才会配比
                if(inventoryODTO != null && StockTypeEnum.UN_LIMIT.getCode().equals(inventoryODTO.getStockType())) {
                    String key = giftDto.getCommodityId() + "_" + giftDto.getCommodityPrice().stripTrailingZeros().toPlainString();
                    if(orderListMap.containsKey(key)) {
                        OrderList orderList = orderListMap.get(key).get(0);
                        if(giftDto.getCommodityNum().compareTo(orderList.getCommodityNum()) != 0) {
                            orderList.setCommodityNum(giftDto.getCommodityNum());
                            orderList.setTotalPrice(orderList.getCommodityPrice().multiply(orderList.getCommodityNum()));

                            // 如果配比商品实发为null，就执行覆盖
                            if(orderList.getRealQuantity() == null && giftDto.getRealQuantity() != null) {
                                orderList.setRealQuantity(giftDto.getRealQuantity());
                                orderList.setRealTotalPrice(orderList.getTotalPrice().divide(orderList.getCommodityNum(),6, BigDecimal.ROUND_HALF_UP).multiply(orderList.getRealQuantity()).setScale(2, BigDecimal.ROUND_HALF_UP));
                            }
                            orderListMapper.updateByPrimaryKey(orderList);
                            isRatio.set(true);
                        }
                    }else {
                        // 此种情况很少，基本上忽略
                        OrderList orderList = BeanCloneUtils.copyTo(giftDto, OrderList.class);
                        orderList.setId(null);
                        orderListMapper.insert(orderList);
                        insertList.add(orderList);
                        isRatio.set(true);
                    }
                }
            });
        }

        if(isRatio.get() && CollectionUtils.isNotEmpty(insertList)) {
            // 根据商品ID分组
            Map<Long, List<AssociationOrderODTO>> associationOrderMap = insertList.stream()
                    .map(this::buildAssociationOrder)
                    .collect(Collectors.groupingBy(AssociationOrderODTO::getCommodityId));

            // 更新 subOrderItem表中的 对应t_order_list表的主键id 的字段
            updateSubOrderItemAssociations(orderId, associationOrderMap);
        }

        return isRatio.get();
    }

    /**
     * 对配货商品进行覆盖
     * @param orderId
     * @param orderLists
     */
    private void syncRationOrderList(Long orderId, List<OrderList> orderLists) {
        // 根据订单id查询配货orderGiftList明细
        List<OrderListGift> giftRationList = orderListGiftService.getOrderGiftRationByOrderId(orderId);
        if (CollectionUtils.isEmpty(giftRationList)) {
            return;
        }

        // 过滤出不在 t_order_list 里面的配货明细
        List<OrderList> syncOrderLists = filterNewGiftItems(orderLists, giftRationList);
        if (CollectionUtils.isEmpty(syncOrderLists)) {
            return;
        }

        // 插入不在 t_order_list 里面的配货明细
        insertNewGiftItems(syncOrderLists, orderId);
    }

    /**
     * 插入新的配货明细，并更新 subOrderItem表中的 对应t_order_list表的主键id 的字段
     *
     * @param newOrderLists 新的配货明细。
     * @param orderId       订单ID。
     */
    private void insertNewGiftItems(List<OrderList> newOrderLists, Long orderId) {
        if (CollectionUtils.isEmpty(newOrderLists)) {
            return;
        }

        // 批量插入新的配货明细
        orderListService.batchInsert(newOrderLists);

        // 根据商品ID分组
        Map<Long, List<AssociationOrderODTO>> associationOrderMap = newOrderLists.stream()
                .map(this::buildAssociationOrder)
                .collect(Collectors.groupingBy(AssociationOrderODTO::getCommodityId));

        // 更新 subOrderItem表中的 对应t_order_list表的主键id 的字段
        updateSubOrderItemAssociations(orderId, associationOrderMap);
    }

    /**
     * 更新 subOrderItem表中的 对应t_order_list表的主键id 的字段
     *
     * @param orderId             订单ID。
     * @param associationOrderMap 配货明细映射。
     */
    private void updateSubOrderItemAssociations(Long orderId, Map<Long, List<AssociationOrderODTO>> associationOrderMap) {
        List<SubOrder> subOrders = subOrderService.getSubOrdersByOrderId(orderId);
        if (CollectionUtils.isEmpty(subOrders)) {
            return;
        }

        List<Long> subOrderIds = subOrders.stream().map(SubOrder::getId).collect(Collectors.toList());

        // 查询t_sub_order_item里面的配货商品，并且comb_order_list_id is null
        List<SubOrderItem> subOrderItems = subOrderService.batchSelectSubOrderRationItems(subOrderIds);

        if (CollectionUtils.isEmpty(subOrderItems)) {
            return;
        }

        subOrderItems.forEach(item -> {
            List<AssociationOrderODTO> potentialMatches = associationOrderMap.get(item.getCommodityId());
            if (CollectionUtils.isNotEmpty(potentialMatches)) {
                potentialMatches.stream()
                        .filter(p -> p.getType().equals(item.getType())
                                && p.getCommodityPrice().compareTo(item.getPrice()) == 0
                                && !p.getOrderDone())
                        .findFirst()
                        .ifPresent(match -> {
                            item.setCombOrderListId(match.getOrderListId());
                            match.setOrderDone(true);
                            // 更新 subOrderItem表中的 对应t_order_list表的主键id 的字段
                            subOrderService.updateItemByPrimaryKeySelective(item);
                        });
            }
        });
    }

    /**
     * 处理预付客户逻辑，包括余额调整和账单记录。
     *
     * @param order 订单对象。
     */
    private void handlePreStoreRecharge(Order order) {
        StoreSettlement storeSettlement = storeSettlementService.getStoreSettlementByStoreId(order.getStoreId());
        if (Objects.isNull(storeSettlement)) {
            syncOrderMapper.modifySyncStatus(order.getId());
            return;
        }

        // 更新订单金额和同步状态
        syncOrderMapper.updateAmountPrice(order.getId(), order.getFinalAmount());

        // 判断是否为预付费客户
        boolean isPreStore = Boolean.TRUE.equals(storeRechargeService.isPreStore(order.getStoreId()));
        if (!isPreStore) {
            return;
        }
        try {
            // 处理回款和扣款逻辑。
            processRechargeAndDeduction(order);
        } catch (Exception e) {
            log.warn("订单覆盖远程调用失败，订单Id: {}, error: ", order.getId(), e);
        }


    }

    /**
     * 处理回款和扣款逻辑。
     *
     * @param order 订单对象。
     */
    private void processRechargeAndDeduction(Order order) {
        // 回款
        storeRechargeService.storeRecharge(buildRechargeBO(order));

        // 扣款
        storeRechargeService.storeDeduction(buildDeductionBO(order));
    }

    /**
     * 构建回款对象。
     *
     * @param order 订单对象。
     * @return 回款对象。
     */
    private StoreRechargeBO buildRechargeBO(Order order) {
        return StoreRechargeBO.builder()
                .orderCode(order.getOrderCode())
                .tradeCode("DDFG" + order.getOrderCode())
                .money(order.getOrderAmount().doubleValue())
                .storeId(order.getStoreId())
                .tradeTime(new Date())
                .receiptDate(new Date())
                .billType(StoreBillTypeEnums.JOB_ORDER_DEPOSIT.getCode())
                .remark("<--JOB清美订单回款: " + order.getOrderCode() + "-->")
                .userId(-1L)
                .build();
    }

    /**
     * 构建扣款对象。
     *
     * @param order 订单对象。
     * @return 扣款对象。
     */
    private StoreDeductionBO buildDeductionBO(Order order) {
        return StoreDeductionBO.builder()
                .orderCode(order.getOrderCode())
                .orderId(order.getId())
                .orderAmount(order.getFinalAmount())
                .storeId(order.getStoreId())
                .tradeCode("DDFG" + order.getOrderCode())
                .tradeTime(new Date())
                .orderTime(order.getOrderTime())
                .billType(StoreBillTypeEnums.JOB_ORDER_DEDUCTION.getCode())
                .remark("<--JOB清美订单扣款: " + order.getOrderCode() + "-->")
                .userId(-1L)
                .build();
    }


    private List<OrderList> filterNewGiftItems(List<OrderList> orderLists, List<OrderListGift> giftRationList) {
        Set<String> existingKeys = orderLists.stream()
                .map(item -> item.getCommodityId() + "_" + item.getType())
                .collect(Collectors.toSet());

        return giftRationList.stream()
                .filter(gift -> !existingKeys.contains(gift.getCommodityId() + "_" + gift.getType()))
                .map(gift -> BeanCloneUtils.copyTo(gift, OrderList.class))
                .collect(Collectors.toList());
    }

    /**
     * 构建 AssociationOrderODTO 对象。
     *
     * @param orderList 配货明细。
     * @return AssociationOrderODTO 对象。
     */
    private AssociationOrderODTO buildAssociationOrder(OrderList orderList) {
        return AssociationOrderODTO.builder()
                .commodityId(orderList.getCommodityId())
                .type(orderList.getType())
                .commodityPrice(orderList.getCommodityPrice())
                .orderListId(orderList.getId())
                .orderDone(false)
                .build();
    }

}
