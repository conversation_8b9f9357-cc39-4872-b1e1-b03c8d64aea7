package com.pinshang.qingyun.order.service.pay.callback;

import com.pinshang.qingyun.base.enums.pay.PayTypeIntEnum;
import org.springframework.context.ApplicationEvent;

import java.util.Date;

/**
 * 三方支付成功事件
 * <AUTHOR>
 * @Date 2019/12/5 17:32
 */
public class PfThirdPartyPayCallbackEvent extends ApplicationEvent {
    /**
     * 订单code
     */
    private String orderCode;
    /**
     * 外部code
     */
    private String transactionId;
    /**
     * 校验结果是否合法
     * 当且仅当校验合法与支付成功时为true
     */
    private boolean validate;
    /**
     * 请求报文
     */
    private String json;

    /**
     * 支付状态，后面可调整为枚举
     * 阿里的回调通知状态有 TRADE_FINISHED TRADE_SUCCESS TRADE_CLOSED
     */
    private String payStatus;

    private PayTypeIntEnum payType;

    private Boolean noLog;

    private Date payTime;

    public PfThirdPartyPayCallbackEvent(String transactionId, String orderCode, String json, boolean validate, PayTypeIntEnum payType) {
        super(orderCode);
        this.orderCode = orderCode;
        this.transactionId = transactionId;
        this.validate = validate;
        this.json = json;
        this.payType = payType;
    }

    public PfThirdPartyPayCallbackEvent(String transactionId, String orderCode, String json, boolean validate, PayTypeIntEnum payType, Date payTime) {
        super(orderCode);
        this.orderCode = orderCode;
        this.transactionId = transactionId;
        this.validate = validate;
        this.json = json;
        this.payType = payType;
        this.payTime = payTime;
    }

    public PfThirdPartyPayCallbackEvent(String transactionId, String orderCode, String payStatus, String json, boolean validate, PayTypeIntEnum payType) {
        super(orderCode);
        this.orderCode = orderCode;
        this.transactionId = transactionId;
        this.validate = validate;
        this.json = json;
        this.payType = payType;
        this.payStatus = payStatus;
    }


    public static PfThirdPartyPayCallbackEvent build(String transactionId, String orderCode, String json
            , boolean validate, PayTypeIntEnum payType){
        return new PfThirdPartyPayCallbackEvent(transactionId, orderCode, json, validate, payType);
    }

    public static PfThirdPartyPayCallbackEvent build(String transactionId, String orderCode, String json
            , boolean validate, PayTypeIntEnum payType, Date payTime){
        return new PfThirdPartyPayCallbackEvent(transactionId, orderCode, json, validate, payType, payTime);
    }

    public static PfThirdPartyPayCallbackEvent build(String transactionId, String orderCode, String payStatus, String json
            , boolean validate, PayTypeIntEnum payType) {
        return new PfThirdPartyPayCallbackEvent(transactionId, orderCode, payStatus, json, validate, payType);
    }

    public Date getPayTime() {
        return payTime;
    }

    public void setPayTime(Date payTime) {
        this.payTime = payTime;
    }

    public String getOrderCode() {
        return orderCode;
    }

    public String getTransactionId() {
        return transactionId;
    }

    public boolean isValidate() {
        return validate;
    }

    public String getJson() {
        return json;
    }

    public String getPayStatus() {
        return payStatus;
    }

    public void setPayStatus(String payStatus) {
        this.payStatus = payStatus;
    }

    public PayTypeIntEnum getPayType() {
        return payType;
    }

    public PfThirdPartyPayCallbackEvent withoutLog(){
        this.noLog = true;
        return this;
    }

    public boolean noLog(){
        return this.noLog != null && this.noLog;
    }

    @Override
    public String toString() {
        return "ThirdPartyPayCallbackEvent{" +
                "orderCode='" + orderCode + '\'' +
                ", transactionId='" + transactionId + '\'' +
                ", validate=" + validate +
                ", json='" + json + '\'' +
                ", payType=" + payType.getRemark() +
                '}';
    }
}
