package com.pinshang.qingyun.order.controller.web.externalDocking;

import com.github.pagehelper.PageInfo;
import com.pinshang.qingyun.order.mapper.entry.externalDocking.RtOutboundOrderEntry;
import com.pinshang.qingyun.order.service.externalDocking.RtOutboundOrderService;
import com.pinshang.qingyun.order.util.RtFileUtil;
import com.pinshang.qingyun.order.vo.externalDocking.RtOutboundOrderVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.CellType;
import org.apache.poi.xssf.usermodel.XSSFCell;
import org.apache.poi.xssf.usermodel.XSSFRow;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.text.SimpleDateFormat;
import java.util.List;


/**
 * 大润发出库单
 * <AUTHOR>
 * @date 2023年2月15日10:00:09
 */
@Slf4j
@RestController
@RequestMapping("/rtOutboundOrder")
@Api(value = "大润发出库单相关接口", tags = "rtOutboundOrder", description = "大润发出库单相关接口")
public class RtOutboundOrderController {

    private final RtOutboundOrderService rtOutboundOrderService;

    public RtOutboundOrderController(RtOutboundOrderService rtOutboundOrderService) {
        this.rtOutboundOrderService = rtOutboundOrderService;
    }

    @ApiOperation(value = "大润发出库单列表分页查询", notes = "大润发出库单列表分页查询")
    @ApiImplicitParam(name = "vo", value = "", required = true, paramType = "body", dataTypeClass = RtOutboundOrderVo.class)
    @RequestMapping(value = "/selectRtOutboundOrderPageInfo",method = RequestMethod.POST)
    public PageInfo<RtOutboundOrderEntry> selectRtOutboundOrderPageInfo(@RequestBody RtOutboundOrderVo vo){
       return rtOutboundOrderService.selectRtOutboundOrderPageInfo(vo);
    }

    @ApiOperation(value = "导出列表", notes = "导出列表")
    @RequestMapping(value = "/export", method = RequestMethod.GET)
    public void exportExcel(RtOutboundOrderVo reqVO, HttpServletResponse response) throws IOException {

        File newFile = RtFileUtil.createNewFile(2);

        // 新文件写入数据，并下载*****************************************************
        InputStream is;
        XSSFWorkbook workbook = null;
        XSSFSheet sheetCommodity = null;
        try {
            // 将excel文件转为输入流
            is = new FileInputStream(newFile);
            // 创建个workbook
            workbook = new XSSFWorkbook(is);
            // 获取第一个sheet
            sheetCommodity = workbook.getSheetAt(0);
        } catch (Exception e1) {
            e1.printStackTrace();
        }
        FileOutputStream fos = new FileOutputStream(newFile);
        // 写数据
        try {
            if (null != sheetCommodity) {
                XSSFRow row = sheetCommodity.getRow(1);
                if (row == null) {
                    row = sheetCommodity.createRow(1);
                }
                XSSFCell cell = row.getCell(0);
                if (cell == null) {
                    cell = row.createCell(0);
                }

                SimpleDateFormat sf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                reqVO.initExportPage();
                PageInfo<RtOutboundOrderEntry> pageInfo = rtOutboundOrderService.selectRtOutboundOrderPageInfo(reqVO);
                List<RtOutboundOrderEntry> content = pageInfo.getList();

                String[][] values = new String[content.size()][];
                for (int i = 0; i < content.size(); i++) {
                    values[i] = new String[10];
                    // 将对象内容转换成string
                    RtOutboundOrderEntry entry = content.get(i);
                    values[i][0] = entry.getReceivingUnit();
                    values[i][1] = entry.getPhone();
                    values[i][2] = entry.getDeliveryTime();
                    values[i][3] = entry.getDeliveryCar();
                    values[i][4] = entry.getDeliveryPerson();
                    values[i][5] = "诸杰";
                    values[i][6] = entry.getRtCommodityCode();
                    values[i][7] = entry.getRtCommodityName();
                    values[i][8] = entry.getDeliveryNumber();
                    values[i][9] = entry.getDeliveryPrice();
                }
                //插入数据
                for(int i=0;i<values.length;i++){
                    row = sheetCommodity.createRow(i + 1);
                    for(int j=0;j<values[i].length;j++){
                        cell = row.createCell(j);

                        if(StringUtils.isNotEmpty(values[i][j])){
                            cell.setCellValue(values[i][j]);

                            if(j == 8){
                                cell.setCellValue(Double.parseDouble(values[i][j]));
                                cell.setCellType(CellType.NUMERIC);
                            }
                        }
                    }
                }
            }
            workbook.write(fos);
            fos.flush();
            fos.close();

            // 下载
            RtFileUtil.exportExcel(2,response, newFile,reqVO.getStoreShortName());

        } catch (FileNotFoundException e) {
            e.printStackTrace();
        } catch (IOException e) {
            e.printStackTrace();
        }
        // 删除创建的新文件
        RtFileUtil.deleteFile(newFile);
    }
}
