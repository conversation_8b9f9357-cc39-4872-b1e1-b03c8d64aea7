package com.pinshang.qingyun.order.service.tda;

import com.pinshang.qingyun.base.configure.expand.QYAssert;
import com.pinshang.qingyun.order.dto.xda.*;
import com.pinshang.qingyun.order.dto.xda.tda.ReturnOrderItemODTO;
import com.pinshang.qingyun.order.dto.xda.tda.SaveReturnOrderODTO;
import com.pinshang.qingyun.order.enums.ComplaintTypeEnum;
import com.pinshang.qingyun.order.enums.tda.TDaReturnOrderTypeEnum;
import com.pinshang.qingyun.order.enums.tda.TDaReturnStatusEnum;
import com.pinshang.qingyun.order.mapper.XdaReturnOrderItemMapper;
import com.pinshang.qingyun.order.mapper.XdaReturnOrderMapper;
import com.pinshang.qingyun.order.model.xda.XdaReturnOrder;
import com.pinshang.qingyun.order.model.xda.XdaReturnOrderItem;
import com.pinshang.qingyun.order.service.tda.factory.ReturnOrderStrategyFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;

import java.math.BigDecimal;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <p>
 * 退货单--app端用
 * </p>
 *
 * <AUTHOR> shenyang
 * @version : 1.0.0
 * @history : modify history
 * <author>              <time>              <version>              <desc>
 * @since : 2024/05/10 11:11
 */
@Slf4j
@Service
public class XdaReturnOrderService {
    @Autowired
    private ReturnOrderStrategyFactory strategyFactory;
    @Autowired
    private XdaReturnOrderMapper xdaReturnOrderMapper;
    @Autowired
    private XdaReturnOrderItemMapper xdaReturnOrderItemMapper;

    /**
     * 生成退货单
     */
    public Boolean generateReturnOrder(SaveReturnOrderODTO saveReturnOrderDTO) {
        return strategyFactory.execute(saveReturnOrderDTO);
    }

    /**
     * 根据投诉类型+送货日期+storeId查询退货单
     */
    public List<XdaComplaintCommodityItemDTO> queryReturnOrderByDeliveryDateAndStoreId(Long storeId, String deliveryDate, Integer code) {
        Integer returnOrderType;
        if (code == null) {
            returnOrderType = null;
        } else if (ComplaintTypeEnum.COMMON.getCode() == code) {
            returnOrderType = TDaReturnOrderTypeEnum.SHORTAGE.getCategoryCode();
        } else {
            returnOrderType = TDaReturnOrderTypeEnum.RETURN.getCategoryCode();
        }
        return xdaReturnOrderMapper.queryReturnOrderByDeliveryDateAndStoreId(storeId, deliveryDate, returnOrderType);
    }

    /**
     * 通达查退货单
     */
    public List<XdaComplaintOrderODTO> queryComplaintOrderList(QueryXdaComplaintDTO dto) {
        return xdaReturnOrderMapper.queryComplaintOrderList(dto);
    }

    /**
     * 通达取消退货单
     */
    public void cancelReturnOrder(XdaComplaintOrderCancelDTO dto) {
        QYAssert.notNull(dto.getComplainId(), "取消的投诉单不存在！");
        XdaReturnOrder xdaReturnOrder = xdaReturnOrderMapper.selectByReturnOrderCode(dto.getComplainId());
        QYAssert.notNull(xdaReturnOrder, "投诉单不存在！");
        QYAssert.isTrue(dto.getStoreId().equals(xdaReturnOrder.getStoreId()), "只能管理当前客户下的投诉单！");
        QYAssert.isTrue(Objects.equals(xdaReturnOrder.getStatus(), 1) || Objects.equals(xdaReturnOrder.getStatus(), 2), "投诉状态已变更，不可取消！");
        QYAssert.isTrue(xdaReturnOrder.getReturnSource() == 1, "客服发起的投诉，取消请联系客服！");
        if (dto.getOperationType()) {
            //全部取消
            cancelAll(xdaReturnOrder);
        } else {
            //取消指定商品
            List<ReturnOrderItemODTO> list = xdaReturnOrderItemMapper.queryByReturnOrderId(xdaReturnOrder.getId());
            //过滤出正常（未取消过的）的商品
            List<ReturnOrderItemODTO> returnOrderItemList = list.stream().filter(x -> Objects.equals(x.getStatus(), 0) && Objects.equals(x.getDelFlag(), 0)).collect(Collectors.toList());
            QYAssert.isTrue(!returnOrderItemList.isEmpty(), "此投诉单已没有可取消的商品！");
            if (returnOrderItemList.size() == 1) {
                cancelAll(xdaReturnOrder);
            } else {
                //非称重品同个商品只能取消一次
                returnOrderItemList.stream()
                        .filter(x -> Objects.equals(x.getCommodityId(), String.valueOf(dto.getCommodityId()))
                                && Objects.equals(x.getStatus(), 1) || Objects.equals(x.getDelFlag(), 1)
                                && Objects.equals(x.getIsWeight(), 0))
                        .findAny()
                        .ifPresent(y -> QYAssert.isTrue(false, "此商品已被取消！"));

                //匹配 商品id+商品数量+商品单价
                QYAssert.isTrue(returnOrderItemList.stream().anyMatch(returnOrderItem -> isMatchingItem(returnOrderItem, dto)), "投诉商品不存在！");

                BigDecimal realReturnQuantity = BigDecimal.ZERO;
                Long id = null;
                for (ReturnOrderItemODTO returnOrderItem : returnOrderItemList) {
                    //退货单中存在该商品，匹配 商品id+商品数量+商品单价
                    if (isMatchingItem(returnOrderItem, dto)) {
                        //退货和少货 是减法
                        if (Objects.equals(returnOrderItem.getReturnOrderType(), TDaReturnOrderTypeEnum.RETURN.getCategoryCode()) || Objects.equals(returnOrderItem.getReturnOrderType(), TDaReturnOrderTypeEnum.SHORTAGE.getCategoryCode())) {
                            realReturnQuantity = returnOrderItem.getApplyQuantity().abs();
                        } else {
                            //退货和少货 是加法
                            realReturnQuantity = returnOrderItem.getApplyQuantity().negate();
                        }
                        id = returnOrderItem.getId();
                        break;
                    }
                }


                BigDecimal cancelTotalMoney = realReturnQuantity.multiply(dto.getCommodityPrice());
                if (Objects.nonNull(id)) {
                    //取消指定商品
                    XdaReturnOrder updateReturnOrder = new XdaReturnOrder();
                    updateReturnOrder.setId(xdaReturnOrder.getId());
                    updateReturnOrder.setTotalApplyMoney(xdaReturnOrder.getTotalApplyMoney().add(cancelTotalMoney));
                    xdaReturnOrderMapper.updateByPrimaryKeySelective(updateReturnOrder);

                    XdaReturnOrderItem updateOrderItem = new XdaReturnOrderItem();
                    updateOrderItem.setId(id);
                    updateOrderItem.setDelFlag(1);
                    xdaReturnOrderItemMapper.updateByPrimaryKeySelective(updateOrderItem);
                }
            }
        }
    }

    private boolean isMatchingItem(ReturnOrderItemODTO item, XdaComplaintOrderCancelDTO dto) {
        String itemKey = generateItemKey(item.getCommodityId(), item.getCommodityOrderQuantity(), item.getCommodityPrice());
        String dtoKey = generateItemKey(dto.getCommodityId(), dto.getRealDeliveryQuantity(), dto.getCommodityPrice());
        return Objects.equals(itemKey, dtoKey);
    }

    private String generateItemKey(Object commodityId, BigDecimal quantity, BigDecimal price) {
        return commodityId + "_" + quantity.stripTrailingZeros() + "_" + price.stripTrailingZeros();
    }

    /**
     * 全部取消-status=1（部分取消：delFlag=1）
     */
    private void cancelAll(XdaReturnOrder xdaReturnOrder) {
        XdaReturnOrder updateOrder = new XdaReturnOrder();
        updateOrder.setId(xdaReturnOrder.getId());
        updateOrder.setStatus(TDaReturnStatusEnum.CANCELLED.getCode());
        xdaReturnOrderMapper.updateByPrimaryKeySelective(updateOrder);
        Example example = new Example(XdaReturnOrderItem.class);
        example.createCriteria().andEqualTo("returnOrderId", xdaReturnOrder.getId());
        XdaReturnOrderItem updateOrderItem = new XdaReturnOrderItem();
        updateOrderItem.setStatus(1);
        xdaReturnOrderItemMapper.updateByExampleSelective(updateOrderItem, example);
    }

    /**
     * 查询app已发起的投诉 商品数量（非称重品）
     */
    public List<XdaComplaintCommodityQuantityDTO> queryCompleteCommodityQuantity(Long storeId, String deliveryDate, Integer complaintType, List<Long> commodityIdList) {
        //鲜达退货单的类型有 1-退货，2-差异（少货/多货）而app端上送的退货单类型是0-差异投诉，1-退货投诉
        //因此需要做一层转换
        Integer returnOrderType = ComplaintTypeEnum.RETURN.getCode() == complaintType ? 1 : 2;
        return xdaReturnOrderMapper.queryCompleteCommodityQuantity(storeId, deliveryDate, returnOrderType, commodityIdList);
    }

}