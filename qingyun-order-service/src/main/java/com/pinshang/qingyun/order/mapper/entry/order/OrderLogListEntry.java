package com.pinshang.qingyun.order.mapper.entry.order;

import com.pinshang.qingyun.order.vo.order.OrderHistoryAddTypeEnums;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import lombok.ToString;

@Data
@ToString
@AllArgsConstructor
@RequiredArgsConstructor
public class OrderLogListEntry {

	@ApiModelProperty("操作日期")
	private String createTime;

	@ApiModelProperty("操作人")
	private String createName;

	@ApiModelProperty("操作类型")
	private String operationType;

	@ApiModelProperty("类别")
	private String entityType;

	@ApiModelProperty("编码")
	private String code;

	@ApiModelProperty("名称")
	private String name;

	@ApiModelProperty("新值")
	private String newValue;

	@ApiModelProperty("原值")
	private String oldValue;

	@ApiModelProperty("备注")
	private Integer addType;

	public String getAddTypeName() {
		return OrderHistoryAddTypeEnums.getName(addType);
	}
}
