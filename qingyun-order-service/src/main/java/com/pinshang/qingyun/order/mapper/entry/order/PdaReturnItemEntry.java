package com.pinshang.qingyun.order.mapper.entry.order;

import lombok.Data;

import java.math.BigDecimal;
import java.util.List;


@Data
public class PdaReturnItemEntry {

    private String commodityId;
    /** 商品编码 */
    private String commodityCode;
    /** 商品名称 */
    private String commodityName;
    /** 规格 */
    private String commoditySpec;
    /** 包装规格 */
    private BigDecimal commodityPackageSpec;
    /** 单位 */
    private String unit;
    private String barCode;

    /** 库存数量 */
    private BigDecimal stockQuantity;
    /** 单价 */
    private BigDecimal price;
    /** 实发数量 */
    private BigDecimal deliveryQuantity;

    /** 退货原因 */
    private List<ReturnReason> returnReasonList;


    @Data
    public static class ReturnReason{
        private String optionCode;
        private String optionName;
    }

}
