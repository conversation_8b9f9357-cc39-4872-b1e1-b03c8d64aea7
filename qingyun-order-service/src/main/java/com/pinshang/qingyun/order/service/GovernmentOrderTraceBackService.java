package com.pinshang.qingyun.order.service;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.pinshang.qingyun.base.configure.expand.QYAssert;
import com.pinshang.qingyun.order.mapper.GovernmentOrderTraceBackMapper;
import com.pinshang.qingyun.order.mapper.entry.government.FactoryRespEntry;
import com.pinshang.qingyun.order.mapper.entry.government.GovernmentCommdityRespEntry;
import com.pinshang.qingyun.order.mapper.entry.government.GovernmentOrderTraceBackRespEntry;
import com.pinshang.qingyun.order.mapper.entry.government.GovernmentStoreRespEntry;
import com.pinshang.qingyun.order.vo.government.GovernmentOrderTraceBackRepVO;
import com.pinshang.qingyun.smm.dto.user.SelectUserFactoryIdListIDTO;
import com.pinshang.qingyun.smm.service.SMMUserClient;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.servlet.view.document.AbstractXlsxView;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.math.BigDecimal;
import java.net.URL;
import java.text.Collator;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2021/2/4 14:38
 */
@Slf4j
@Service
public class GovernmentOrderTraceBackService extends AbstractXlsxView{

    @Autowired
    private GovernmentOrderTraceBackMapper orderTraceBackMapper;

    @Autowired
    private SMMUserClient smmUserClient;

    /***
     * 查询政府追溯订单列表数据
     * @param vo
     * @return
     */
    public PageInfo<GovernmentOrderTraceBackRespEntry> selectGovernmentOrderTraceBackList(GovernmentOrderTraceBackRepVO vo){

        if(this.checkUserFactoryPermission(vo.getUserId(),vo.getFactoryId())){
            QYAssert.isFalse("该用户:"+vo.getUserId()+"没有查看此数据权限，请确认");
        }
        PageInfo<GovernmentOrderTraceBackRespEntry> pageInfo= PageHelper.startPage(vo.getPageNo(),vo.getPageSize()).doSelectPageInfo(()-> {
            orderTraceBackMapper.selectGovernmentOrderTraceBackList(vo);
        });

        List<GovernmentOrderTraceBackRespEntry> list=pageInfo.getList();
        /***
         * 封装客户信息
         */
        this.processStoreData(list);
        /***
         * storeCode排序 ASC
         */
        list=list.stream().sorted(Comparator.comparing(GovernmentOrderTraceBackRespEntry::getStoreCode)).collect(Collectors.toList());
        pageInfo.setList(list);
        return pageInfo;
    }

    /***
     * 封装客户信息
     * @param list
     */
    private void processStoreData( List<GovernmentOrderTraceBackRespEntry> list){
        if(list!=null && list.size()>0){
            List<Long> storeIds=list.stream().map(GovernmentOrderTraceBackRespEntry::getStoreId).collect(Collectors.toList());
            /***
             * 获取客户信息
             */
            List<GovernmentStoreRespEntry> storeIdList= orderTraceBackMapper.selectGovernmentStoreByStoreId(storeIds);
            if(storeIdList!=null && storeIdList.size()>0){
                Map<Long,GovernmentStoreRespEntry> map= storeIdList.stream().collect(Collectors.toMap(key -> key.getStoreId(), val-> val));
                list.forEach(g ->{
                    Long storeId=g.getStoreId();
                    if(map.containsKey(storeId)){
                        GovernmentStoreRespEntry entry= map.get(storeId);
                        g.setSku(entry.getSku());
                        g.setStoreCode(entry.getStoreCode());
                        //购货者名称
                        if(StringUtils.isBlank(entry.getOuterStoreName())){
                            g.setOuterStoreName(entry.getStoreName());
                        }else{
                            g.setOuterStoreName(entry.getOuterStoreName());
                        }

                        g.setDeliveryAddress(entry.getDeliveryAddress());
                        g.setLinkmanTel(entry.getLinkmanTel());
                       // String num=g.getCommodityCount();
                        /*if(StringUtils.isNotBlank(num)){
                           String[] numSplit= num.split(",");
                           BigDecimal sum=BigDecimal.ZERO;
                           BigDecimal numBig=null;
                           for(String s:numSplit){
                               if(StringUtils.isNotBlank(s)) {
                                   numBig = new BigDecimal(s);
                                   sum = sum.add(numBig);
                               }
                           }
                            g.setCommodityCount(sum.setScale(2,BigDecimal.ROUND_HALF_UP).toString());
                        }*/


                        String deliveryDate=g.getDeliveryDate();
                        if(StringUtils.isNotBlank(deliveryDate)){
                            /***
                             * 生产日期等于送货日子减一天
                             */
                            LocalDate localDate=LocalDate.parse(deliveryDate);
                            LocalDate localDateMinusDay=localDate.minusDays(1);
                            g.setManufactureDate(localDateMinusDay.toString());//生产日期
                            g.setManufactureBatch(localDateMinusDay.format(DateTimeFormatter.ofPattern("yyyyMMdd")));//生产批次
                        }
                    }
                });
            }
        }
    }


    /***
     * 获取用户工厂权限数据
     * @param userId
     * @return
     */
    public List<FactoryRespEntry> selectFactoryByUserId(Long userId){
        QYAssert.isTrue(userId!=null,"用户信息不能为空");
        List<Long> factoryIds= smmUserClient.selectUserFactoryIdList(SelectUserFactoryIdListIDTO.onlyDb(userId));
        if(factoryIds!=null && factoryIds.size()>0){
            List<FactoryRespEntry>  factoryRespEntries=orderTraceBackMapper.selectFactoryById(factoryIds);
            factoryRespEntries=factoryRespEntries.stream().sorted(Comparator.comparing(FactoryRespEntry::getFactoryName, Collator.getInstance(Locale.CHINA))).collect(Collectors.toList());
            return factoryRespEntries;
        }
        return null;
    }

    /****
     * 验证是否具有工厂权限
     * @param userId
     */
    public boolean checkUserFactoryPermission(Long userId,Long factoryId){
        List<FactoryRespEntry> factoryRespEntries=this.selectFactoryByUserId(userId);
        if(factoryRespEntries==null || factoryRespEntries.isEmpty()){
            return true;
        }
        Map<Long,FactoryRespEntry> factoryIds=factoryRespEntries.stream().collect(Collectors.toMap(k -> Long.valueOf(k.getId()),v -> v));
        if(!factoryIds.containsKey(factoryId)){
            return true;
        }

        return false;
    }


    /***
     * 获取导出政府追溯订单列表数据
     * @param vo
     * @return
     */
    public Map<String,Object> selectGovernmentOrderTraceBackExportList(GovernmentOrderTraceBackRepVO vo){
        Map<String,Object> map=new HashMap<String,Object>();
        if(this.checkUserFactoryPermission(vo.getUserId(),vo.getFactoryId())){
            log.error("该用户:{}没有查看此数据权限，请确认",vo.getUserId());
            return map;
        }
        List<GovernmentOrderTraceBackRespEntry> govenrnmentOrderitems=orderTraceBackMapper.selectGovernmentOrderTraceBackList(vo);
        /***
         * 封装客户信息
         */
        this.processStoreData(govenrnmentOrderitems);
        /***
         * storeCode排序 ASC
         */
        govenrnmentOrderitems=govenrnmentOrderitems.stream().sorted(Comparator.comparing(GovernmentOrderTraceBackRespEntry::getStoreCode)).collect(Collectors.toList());
        map.put("order",govenrnmentOrderitems);
        List<GovernmentCommdityRespEntry> govenrnmentCommodityems=orderTraceBackMapper.selectGovernmentCommodityList(vo.getFactoryId());
        map.put("commodity",govenrnmentCommodityems);
        return map;
    }

    /***
     * 给商品报表列表赋值
     * @param index
     * @param sxssfCell
     * @param entry
     */
    private void setCommoditySxssfCell(int index ,XSSFCell sxssfCell,GovernmentCommdityRespEntry entry){
        switch (index){
            case 0:
                sxssfCell.setCellValue(entry.getProductName());
                break;
            case 1:
                sxssfCell.setCellValue(entry.getProductCode());
                break;
            case 2:
                sxssfCell.setCellValue(entry.getProductClassification());
                break;
            case 3:
                sxssfCell.setCellValue(entry.getProductQgp());
                break;
            case 4:
                sxssfCell.setCellValue(entry.getProductSpecifications());
                break;
            case 5:
                sxssfCell.setCellValue(entry.getProductBarcode());
                break;
            case 6:
                sxssfCell.setCellValue(entry.getProductManufacturer());
                break;
            case 7:
                break;
            case 8:
                sxssfCell.setCellValue(entry.getProductescribe());
                break;
            default:
                break;
        }

    }

    /***
     * 给订单报表列表赋值
     * @param index
     * @param sxssfCell
     * @param entry
     */
    private void setOrderSxssfCell(int index ,XSSFCell sxssfCell,GovernmentOrderTraceBackRespEntry entry){
        switch (index){
            case 0:
                sxssfCell.setCellValue(entry.getCommodityName());
                break;
            case 1:
                sxssfCell.setCellValue(entry.getCommodityCode());
                break;
            case 2:
                sxssfCell.setCellValue(entry.getManufactureDate());
                break;
            case 3:
                sxssfCell.setCellValue(entry.getManufactureBatch());
                break;
            case 4:
                sxssfCell.setCellValue(entry.getInspectionCertificate());
                break;
            case 5:
                sxssfCell.setCellValue(entry.getCommodityCount());
                break;
            case 6:
                sxssfCell.setCellValue(entry.getCommodityUnit());
                break;
            case 7:
                sxssfCell.setCellValue(entry.getDeliveryDate());
                break;
            case 8:
                sxssfCell.setCellValue(entry.getOuterStoreName());
                break;
            case 9:
                sxssfCell.setCellValue(entry.getDeliveryAddress());
                break;
            case 10:
                sxssfCell.setCellValue(entry.getLinkmanTel());
                break;
            case 11:
                sxssfCell.setCellValue("上海市浦东新区");
                break;
            default:
                break;
        }

    }


    /***
     * 获取远程服务的政府追溯模板文件
     * 创建本地excel文件
     */
    public File createLocalExcelFile(String governmentTemplateUrl,String localUrl,String productManufacturer)throws IOException{
        InputStream in=null;
        FileOutputStream out=null;
        try {
            URL url=new URL(governmentTemplateUrl);
            String parent=localUrl ;
            File dir=new File(parent);
            if(!dir.exists()){
                dir.mkdirs();
            }
            String localDate=LocalDate.now().format(DateTimeFormatter.ofPattern("yyyyMMdd"));
            String filname=String.format("%s_生产_食品_%s.xlsx",productManufacturer,localDate);
            File file=new File(parent,filname);
            out=new FileOutputStream(file);
            in=url.openStream();
            XSSFWorkbook xssfWorkbook=new XSSFWorkbook(in);
            XSSFSheet sheet=xssfWorkbook.getSheetAt(1);
            int num=sheet.getLastRowNum();
            if(num>0) {
                for (int i = 1; i <= num; i++) {
                     XSSFRow row=sheet.getRow(i);
                     sheet.removeRow(row);
                }
            }
            xssfWorkbook.write(out);
            return file;
        } catch (IOException e) {
            e.printStackTrace();
            log.error("下载远程政府追溯模板文件生成本地模板文件失败");
            throw e;
        }finally {
            if(in!=null){
                in.close();
            }
            if(out!=null){
                out.flush();
                out.close();
            }
        }

    }

    /***
     * 处理excel
     * @param map
     * @param xssfWorkbook
     * @param request
     * @param response
     * @throws Exception
     */
    protected void buildExcelDocument(Map<String, Object> map, XSSFWorkbook xssfWorkbook, HttpServletRequest request, HttpServletResponse response) throws Exception {
        File file=null;
        try {
            String url=(String) map.get("url");
            String localUrl=(String) map.get("localUrl");
            String productManufacturer=(String) map.get("productManufacturer");
            file=this.createLocalExcelFile(url,localUrl,productManufacturer);
            xssfWorkbook=new XSSFWorkbook(file);
            XSSFSheet productDescription= xssfWorkbook.getSheetAt(1);
            XSSFSheet governmentOrder= xssfWorkbook.getSheetAt(7);
            List<GovernmentOrderTraceBackRespEntry> govenrnmentOrderitems=(List<GovernmentOrderTraceBackRespEntry>)map.get("order");
            /***
             * 处理订单销货数据
             */
            if(govenrnmentOrderitems!=null && govenrnmentOrderitems.size()>0){
                for(int i=1;i<=govenrnmentOrderitems.size();i++){
                    XSSFRow rowOrder= governmentOrder.getRow(i);
                    if(rowOrder==null){
                        rowOrder= governmentOrder.createRow(i);
                    }
                    GovernmentOrderTraceBackRespEntry entry=govenrnmentOrderitems.get(i-1);
                    for(int j=0;j<12;j++){
                        XSSFCell xssfCell= rowOrder.createCell(j);
                        XSSFCellStyle cellStyle = xssfCell.getCellStyle();
                        cellStyle.setLocked(false);
                        this.setOrderSxssfCell(j,xssfCell,entry);
                    }
                }

            }

            /***
             * 处理产品数据
             */
            List<GovernmentCommdityRespEntry> govenrnmentCommodityitems=(List<GovernmentCommdityRespEntry>)map.get("commodity");
            if(govenrnmentCommodityitems!=null && govenrnmentCommodityitems.size()>0){
                for(int i=1;i<=govenrnmentCommodityitems.size();i++){
                    XSSFRow rowOrder= productDescription.getRow(i);
                    if(rowOrder==null){
                        rowOrder= productDescription.createRow(i);
                    }
                    GovernmentCommdityRespEntry entry=govenrnmentCommodityitems.get(i-1);
                    for(int j=0;j<9;j++){
                        XSSFCell xssfCell= rowOrder.createCell(j);
                        this.setCommoditySxssfCell(j,xssfCell,entry);
                    }
                }

            }

            String filename=new String(file.getName().getBytes("utf-8"),"iso8859-1");//处理中文文件名
            response.setContentType("application/vnd.ms-excel");
            response.setHeader("content-disposition", "attachment;filename="+ filename);
            OutputStream outputStream = response.getOutputStream();
            xssfWorkbook.write(outputStream);
            outputStream.flush();
            outputStream.close();
        }finally {
            if(file!=null){
                file.delete();
            }
        }
    }


    @Override
    protected void buildExcelDocument(Map<String, Object> map, Workbook workbook, HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse) throws Exception {
      this.buildExcelDocument(map,(XSSFWorkbook)workbook,httpServletRequest,httpServletResponse);
    }
}
