package com.pinshang.qingyun.order.mapper.entry.order;

import java.math.BigDecimal;

public class CommodityBaseEntry {
	
	private String commodityId;
	private BigDecimal commodityPrice;
	private String commodityCode;
	private String commodityName;
	
	public String getCommodityId() {
		return commodityId;
	}
	public void setCommodityId(String commodityId) {
		this.commodityId = commodityId;
	}
	public BigDecimal getCommodityPrice() {
		return commodityPrice;
	}
	public void setCommodityPrice(BigDecimal commodityPrice) {
		this.commodityPrice = commodityPrice;
	}
	public String getCommodityCode() {
		return commodityCode;
	}
	public void setCommodityCode(String commodityCode) {
		this.commodityCode = commodityCode;
	}
	public String getCommodityName() {
		return commodityName;
	}
	public void setCommodityName(String commodityName) {
		this.commodityName = commodityName;
	}
}
