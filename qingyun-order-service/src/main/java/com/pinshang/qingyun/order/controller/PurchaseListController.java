package com.pinshang.qingyun.order.controller;

import com.alibaba.excel.EasyExcel;
import com.github.pagehelper.PageInfo;
import com.pinshang.qingyun.base.util.ExcelUtil;
import com.pinshang.qingyun.box.utils.DateUtil;
import com.pinshang.qingyun.box.utils.SpringUtil;
import com.pinshang.qingyun.order.dto.consignment.ConsignmentOrderPageODTO;
import com.pinshang.qingyun.order.dto.consignment.ConsignmentOrderQueryIDTO;
import com.pinshang.qingyun.order.mapper.entry.purchase.ConnectionPurchaseDetailEntry;
import com.pinshang.qingyun.order.mapper.entry.purchase.ConnectionPurchaseEntry;
import com.pinshang.qingyun.order.mapper.entry.purchase.SendPurchaseDetailEntry;
import com.pinshang.qingyun.order.service.PurchaseListService;
import com.pinshang.qingyun.order.service.PurchaseOrderService;
import com.pinshang.qingyun.order.vo.purchase.PurchaseListVo;
import com.pinshang.qingyun.purchase.dto.DirectSendingPurchaseItemODto;
import com.pinshang.qingyun.purchase.dto.ShopPurchaseSearchIDto;
import com.pinshang.qingyun.renderer.annotation.MethodRender;
import com.pinshang.qingyun.renderer.service.IRenderService;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.Date;
import java.util.List;

/**
 * Created by crell on 2017/6/14
 * 采购列表Controller
 */
@RestController
@RequestMapping("/order/purchase")
public class PurchaseListController {
	
	@Autowired
	private PurchaseListService purchaseListService;
	
	@Autowired
	private PurchaseOrderService purchaseOrderService;
	@Autowired
	private IRenderService renderService;

	//获取直通采购列表明细
	@MethodRender
	@PostMapping("/connectionPurchaseDetail")
	public PageInfo<ConnectionPurchaseDetailEntry> findConnectionPurchaseDetail(@RequestBody PurchaseListVo vo){
		return purchaseListService.findConnectionPurchaseDetail(vo);
	}
	
	/*
	 * 获取某天某供应商直送列表明细
	 */
	@PostMapping("/findDirectSendingPurchaseItems")
	public List<DirectSendingPurchaseItemODto> findDirectSendingPurchaseItems(@RequestBody ShopPurchaseSearchIDto iDto){
		return purchaseOrderService.findDirectSendingPurchaseItems(iDto);
	}

	//获取直送采购列表
	@PostMapping("/sendListByPage")
	public PageInfo<ConnectionPurchaseEntry> findSendPurchaseByPage(@RequestBody PurchaseListVo vo){
		return purchaseListService.findSendPurchaseByPage(vo);
	}

	//获取直送采购列表明细
	@PostMapping("/sendPurchaseDetail")
	public List<SendPurchaseDetailEntry> findSendPurchaseDetail(@RequestBody PurchaseListVo vo){
		return purchaseListService.findSendPurchaseDetail(vo);
	}

	/**
	 * 迁移order-manage
	 * @param vo
	 * @return
	 */
	@Deprecated
	@ApiOperation(value = "代销订货明细", notes = "代销订货明细")
	@PostMapping("/consignmentOrderReport")
	@MethodRender
	public PageInfo<ConsignmentOrderPageODTO> consignmentOrderReport(@RequestBody ConsignmentOrderQueryIDTO vo) {
		return purchaseOrderService.consignmentOrderReport(vo);
	}

	/**
	 * 迁移order-manage
	 * @param vo
	 * @return
	 */
	@Deprecated
	@ApiOperation(value = "导出代销订货明细", notes = "导出代销订货明细")
	@GetMapping("exportConsignmentOrderReport")
	public void exportConsignmentOrderReport(ConsignmentOrderQueryIDTO idto, HttpServletResponse httpServletResponse){
		idto.setPageNo(1);
		idto.setPageSize(Integer.MAX_VALUE);
		PageInfo<ConsignmentOrderPageODTO> pageInfo = purchaseOrderService.consignmentOrderReport(idto);
		List<ConsignmentOrderPageODTO> list = pageInfo.getList();
		if(!SpringUtil.isEmpty(list)){
			renderService.render(list,"/exportConsignmentOrderReport");
		}
		try {
			ExcelUtil.setFileNameAndHead(httpServletResponse,"代销订货明细" + DateUtil.getDateFormate(new Date(), "yyyyMMddHHmmss"));
			EasyExcel.write(httpServletResponse.getOutputStream(),ConsignmentOrderPageODTO.class)
					.autoCloseStream(Boolean.FALSE).sheet("代销订货明细").doWrite(list);
		} catch (Exception e) {
			e.printStackTrace();
		}

	}
}
