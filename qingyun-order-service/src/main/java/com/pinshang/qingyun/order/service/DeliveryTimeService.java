package com.pinshang.qingyun.order.service;

import com.pinshang.qingyun.box.utils.SpringUtil;
import com.pinshang.qingyun.order.mapper.DeliveryTimeMapper;
import com.pinshang.qingyun.order.mapper.entry.DeliveryTimeEntry;
import com.pinshang.qingyun.order.model.order.DeliveryTime;
import com.pinshang.qingyun.order.vo.DeliveryTimeLineVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;

import java.util.List;

/**
 * 发货时间
 */
@Service
@Slf4j
public class DeliveryTimeService {
    @Autowired
    private DeliveryTimeMapper deliveryTimeMapper;

    public List<DeliveryTimeEntry>  deliveryTimeList(){
        return deliveryTimeMapper.queryDeliveryTimeList();
    }

    /***
     * 根据线路组id 查询线路
     * @param deliveryTimeId
     * @return
     */
    public String selectEndTimeById(Long deliveryTimeId){
        if(deliveryTimeId == null){
            return null;
        }
        Example example = new Example(DeliveryTime.class);
        example.createCriteria().andEqualTo("lineGroupId",deliveryTimeId);
        List<DeliveryTime> deliveryTimeList = deliveryTimeMapper.selectByExample(example);
        return SpringUtil.isEmpty(deliveryTimeList) ? null : deliveryTimeList.get(0).getEndTime();
    }

    /***
     * 根据线路组id 查询线路
     * @return
     */
    public String selectDeliveryTimeEndTimeByLineIds(DeliveryTimeLineVO vo){
        if(vo == null || SpringUtil.isEmpty(vo.getLineIdList())){
            return null;
        }
        List<String> list = deliveryTimeMapper.selectDeliveryTimeEndTimeByLineIds(vo.getLineIdList());
        return SpringUtil.isEmpty(list) ? null : list.get(0);
    }
}
