package com.pinshang.qingyun.order.listener;

import com.alibaba.fastjson.JSON;
import com.pinshang.qingyun.kafka.base.KafkaMessageWrapper;
import com.pinshang.qingyun.kafka.base.KafkaTopicConstant;
import com.pinshang.qingyun.order.config.LockConstants;
import com.pinshang.qingyun.order.service.OrderChangePriceService;
import com.pinshang.qingyun.order.service.OrderService;
import com.pinshang.qingyun.order.service.WeChatSendMessageService;
import com.pinshang.qingyun.order.util.RedissonLockExecutor;
import com.pinshang.qingyun.order.vo.order.UpdateOrderPriceKafkaVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 订单改价Listener
 * <p>
 * 监听Kafka主题，处理订单改价相关的消息。
 * 主要负责解析消息内容，调用订单改价服务更新订单价格，并发送相关统计信息。
 * </p>
 *
 * <AUTHOR>
 * @since 2018年4月26日
 */
@Slf4j
@Component
public class OrderListener {
    @Autowired
    private OrderService orderService;
    @Autowired
    private RedissonLockExecutor redissonLockExecutor;
    @Autowired
    private OrderChangePriceService orderChangePriceService;
    @Autowired
    private WeChatSendMessageService weChatSendMessageService;

    /**
     * <p>
     * Kafka监听器：处理订单改价消息
     * </p>
     * 更新如下表：
     * t_order
     * t_order_list
     * t_order_list_gift
     * t_sub_order
     * t_sub_order_item
     *
     * @param message 消息内容
     */
    @KafkaListener(
            id = "${application.name.switch}" + KafkaTopicConstant.UPDATE_ORDER_PRICE_KEY,
            topics = {"${application.name.switch}" + KafkaTopicConstant.UPDATE_ORDER_PRICE_KEY}
    )
    public void handleOrderPriceUpdateMessage(String message) {
        log.info("接收到订单改价消息：{}", message);

        try {
            KafkaMessageWrapper messageWrapper = JSON.parseObject(message, KafkaMessageWrapper.class);

            if (messageWrapper == null || messageWrapper.getData() == null) {
                log.error("接收到不支持的消息类型：[{}]", message);
                return;
            }

            // 防止查询到的价格数据不是最新的
            Thread.sleep(3000);
            UpdateOrderPriceKafkaVo vo = JSON.parseObject(messageWrapper.getData().toString(), UpdateOrderPriceKafkaVo.class);
            log.info("接收到订单改价UpdateOrderPriceKafkaVo：{}", JSON.toJSONString(vo));

            String lockKey = LockConstants.generateUpdateOrderPriceLockKey(vo.getProductPriceModelId());
            List<String> updatedOrderCodes = redissonLockExecutor.exec(
                    lockKey,
                    60,
                    90,
                    // 【开始处理订单改价逻辑】
                    () -> orderChangePriceService.updateOrderPrices(vo.getProductPriceModelId(), vo.getUserId())
            );

            // 需要组装订单, 发送消息到订单统计库中的订单表 pinshang_order_statistics
            if (CollectionUtils.isNotEmpty(updatedOrderCodes)) {
                orderService.sendStatisticsKafkaMessage(updatedOrderCodes);
            }
        } catch (Exception e) {
            log.error("处理订单改价消息时发生异常：", e);
            // 发送微信模板信息
            weChatSendMessageService.sendWeChatMessage("订单改价消息消费异常！");
        }
    }

}
