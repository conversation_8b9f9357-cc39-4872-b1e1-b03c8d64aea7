package com.pinshang.qingyun.order.service;

import com.github.pagehelper.PageInfo;
import com.pinshang.qingyun.base.configure.expand.QYAssert;
import com.pinshang.qingyun.bigdata.dto.shoporderingrefmetrics.list.ShopOrderingMetricsListQuery;
import com.pinshang.qingyun.bigdata.dto.shoporderingrefmetrics.list.ShopOrderingMetricsListResult;
import com.pinshang.qingyun.bigdata.service.ShopOrderingRefMetricsClient;
import com.pinshang.qingyun.box.utils.DateUtil;
import com.pinshang.qingyun.order.dto.ShopOrderedQuantityODTO;
import com.pinshang.qingyun.order.dto.order.PosXdSaleDataODTO;
import com.pinshang.qingyun.order.mapper.OrderMapper;
import com.pinshang.qingyun.order.mapper.entry.commodity.CommodityResultEntry;
import com.pinshang.qingyun.order.mapper.entry.order.ShoppingCartEntry;
import com.pinshang.qingyun.order.mapper.entry.order.ShoppingCartItemEntry;
import com.pinshang.qingyun.order.model.commodity.Commodity;
import com.pinshang.qingyun.order.model.shop.Shop;
import com.pinshang.qingyun.order.service.order.OrderReferenceCheckService;
import com.pinshang.qingyun.order.vo.order.CreateOrderVo;
import com.pinshang.qingyun.order.vo.order.OverUnderOrderingCommodityInfoVo;
import com.pinshang.qingyun.order.vo.order.OverUnderOrderingCommodityVo;
import com.pinshang.qingyun.report.dto.pos.PosSaleWaterODTO;
import com.pinshang.qingyun.report.dto.pos.PosSaleWaterQueryIDTO;
import com.pinshang.qingyun.report.service.ReportClient;
import com.pinshang.qingyun.shop.dto.shopCommodity.ShopCommodityInfoODTO;
import com.pinshang.qingyun.xd.report.dto.XdOrderCommodityDataODTO;
import com.pinshang.qingyun.xd.report.dto.XdOrderCommodityQueryIDTO;
import com.pinshang.qingyun.xd.report.service.XdSalesReportClient;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 订货参考服务
 *
 * @Author: sk
 * @Date: 2025/2/19
 */
@Slf4j
@Service
public class OrderReferenceService {

    @Autowired
    private OrderMapper orderMapper;

    @Autowired
    private ShopService shopService;

    @Autowired
    private ProductService productService;

    @Autowired
    private CommodityService commodityService;

    @Autowired
    private ShopOrderingRefMetricsClient shopOrderingRefMetricsClient;

    @Autowired
    private ReportClient reportClient;
    @Autowired
    private XdSalesReportClient xdSalesReportClient;
    @Autowired
    private OrderReferenceCheckService orderReferenceCheckService;

    /**
     * 渲染门店订货列表部分指标：
     * 近15天日均销量
     * 当日销售数量
     * 近15天毛利率
     */
    public void renderShopOrderingMetrics(Long shopId, PageInfo<CommodityResultEntry> pageData) {
        if (pageData == null || CollectionUtils.isEmpty(pageData.getList())) {
            return;
        }

        List<CommodityResultEntry> commodityResultList = pageData.getList();
        List<Long> commodityIdList = commodityResultList.stream().map(CommodityResultEntry::getCommodityId).map(Long::new).collect(Collectors.toList());


        Map<Long, PosXdSaleDataODTO> salesDataMap = querySalesDataMap(shopId, commodityIdList);
        for (CommodityResultEntry commodityResultEntry : commodityResultList) {
            PosXdSaleDataODTO saleDataODTO = salesDataMap.get(Long.valueOf(commodityResultEntry.getCommodityId()));
            if (Objects.nonNull(saleDataODTO)) {
                // 近15天日均销量
                commodityResultEntry.setAvgDailySales15Days(saleDataODTO.getAvgDailySales15Days());
                // 当日销售数量
                commodityResultEntry.setDailySales(saleDataODTO.getQuantity());
                // 近15天毛利率
                commodityResultEntry.setGrossProfitMargin15Days(saleDataODTO.getGrossProfitMargin15Days());
            }
        }
    }


    /**
     * 渲染购物车部分指标：
     * 近15天日均销量
     * 当日销售数量
     * 近15天毛利率
     */
    public void renderShoppingCartMetrics(Long shopId, List<ShoppingCartEntry> entryList) {
        if (CollectionUtils.isEmpty(entryList)) {
            return;
        }
        List<Long> commodityIdList = new ArrayList<>();
        for (ShoppingCartEntry cartEntry : entryList) {
            List<ShoppingCartItemEntry> cartItemList = cartEntry.getItems();
            if (org.apache.commons.collections.CollectionUtils.isNotEmpty(cartItemList)) {
                for (ShoppingCartItemEntry itemEntry : cartItemList) {
                    commodityIdList.add(itemEntry.getCommodityId());
                }
            }
        }

        Map<Long, PosXdSaleDataODTO> salesDataMap = querySalesDataMap(shopId, commodityIdList);

        for (ShoppingCartEntry cartEntry : entryList) {
            List<ShoppingCartItemEntry> cartItemList = cartEntry.getItems();
            if (CollectionUtils.isNotEmpty(cartItemList)) {
                for (ShoppingCartItemEntry itemEntry : cartItemList) {
                    PosXdSaleDataODTO saleDataODTO = salesDataMap.get(itemEntry.getCommodityId());
                    if (Objects.nonNull(saleDataODTO)) {
                        // 近15天日均销量
                        itemEntry.setAvgDailySales15Days(saleDataODTO.getAvgDailySales15Days());
                        // 当日销售数量
                        itemEntry.setDailySales(saleDataODTO.getQuantity());
                        // 近15天毛利率
                        itemEntry.setGrossProfitMargin15Days(saleDataODTO.getGrossProfitMargin15Days());
                    }
                }
            }
        }
    }

    /**
     * 校验订单商品的多订少订情况
     * 多订判断规则:
     * 1. 预计售卖天数=(订货量+库存数据+已订未收)/近15天日均销量>=商品保质期天数*50%
     * 2. 订货量>=最近15天订货数量最大值*2
     * 注: 保质期为空/0时不判断规则1，近15天订货数量最大值为0则不判断规则2
     * <p>
     * 少订判断规则:
     * (订货数量+当前库存+已订未收)<=近15天最低销售数量*80%
     *
     * @param shopId  门店ID
     * @param stallId 档口ID
     * @param items   订货信息
     * @return 多订少订商品信息
     */
    public OverUnderOrderingCommodityVo checkOverUnderOrdering(Long shopId, Long stallId, List<CreateOrderVo.CreateOrderItemIDTO> items) {
        if (CollectionUtils.isEmpty(items)) {
            return OverUnderOrderingCommodityVo.init();
        }

        // 订货商品ID集合
        List<Long> commodityIds = items.stream().map(CreateOrderVo.CreateOrderItemIDTO::getCommodityId).collect(Collectors.toList());
        Map<Long, BigDecimal> orderQuantityMap = items.stream().collect(Collectors.toMap(CreateOrderVo.CreateOrderItemIDTO::getCommodityId, CreateOrderVo.CreateOrderItemIDTO::getQuantity));

        // 1. 门店订货参考指标列表
        List<ShopOrderingMetricsListResult> salesMetricsList = getSalesMetrics(shopId, commodityIds);
        if (CollectionUtils.isEmpty(salesMetricsList)) {
            return OverUnderOrderingCommodityVo.init();
        }
        Map<Long, ShopOrderingMetricsListResult> salesMetricsMap = salesMetricsList.stream()
                .collect(Collectors.toMap(ShopOrderingMetricsListResult::getCommodityId, Function.identity()));

        // 2. 获取商品基础信息
        List<Commodity> commodityList = commodityService.findCommodityByIdList(commodityIds);
        Map<Long, Commodity> commodityMap = commodityList.stream().collect(Collectors.toMap(Commodity::getId, Function.identity()));

        // 3. 获取在途订单数量
        String orderDate = DateUtil.getDateFormate(new Date(), "yyyy-MM-dd");
        List<ShopOrderedQuantityODTO> orderedQuantityList = orderMapper.selectShopOrderQuantity(shopId, commodityIds, orderDate);
        Map<Long, ShopOrderedQuantityODTO> orderedQuantityMap = orderedQuantityList.stream().collect(Collectors.toMap(ShopOrderedQuantityODTO::getCommodityId, Function.identity()));

        // 4. 获取当前库存
        Shop shop = shopService.getByShopId(shopId);
        Map<Long, ShopCommodityInfoODTO> stockMap = productService.getShopStock(shop, stallId, commodityIds);

        // 记录多订少订商品
        Set<Long> overOrderingCommodityIds = new HashSet<>();
        Set<Long> underOrderingCommodityIds = new HashSet<>();

        // 5. 遍历商品进行校验
        for (ShopOrderingMetricsListResult metrics : salesMetricsList) {
            Long commodityId = metrics.getCommodityId();
            // 订货数量
            BigDecimal orderQuantity = orderQuantityMap.get(commodityId);
            if (orderQuantity == null || orderQuantity.compareTo(BigDecimal.ZERO) <= 0) {
                continue;
            }

            // 计算商品总量(订货量+库存数量+已订未收)
            BigDecimal totalQuantity = orderQuantity
                    .add(Optional.ofNullable(stockMap.get(commodityId))
                            .map(ShopCommodityInfoODTO::getStockQuantity)
                            .orElse(BigDecimal.ZERO))
                    .add(Optional.ofNullable(orderedQuantityMap.get(commodityId))
                            .map(ShopOrderedQuantityODTO::getQuantity)
                            .orElse(BigDecimal.ZERO));

            // 校验多订
            checkOverOrdering(totalQuantity, orderQuantity, metrics.getAvgDailySales15Days(), commodityMap.get(commodityId), metrics.getMaxOrderQuantity15Days(), overOrderingCommodityIds);

            // 校验少订
            checkUnderOrdering(commodityId, totalQuantity, metrics.getMinSales15Days(), underOrderingCommodityIds);
        }

        // 6. 组装返回结果
        return buildResult(overOrderingCommodityIds, underOrderingCommodityIds, salesMetricsMap, commodityMap, orderQuantityMap, orderedQuantityMap, stockMap);
    }

    /**
     * 校验多订
     */
    private void checkOverOrdering(BigDecimal totalQuantity, BigDecimal orderQuantity, BigDecimal avgDailySales, Commodity commodity,
                                   BigDecimal maxOrderQuantity, Set<Long> overOrderingCommodityIds) {
        // 校验历史订货量规则
        if (maxOrderQuantity != null && maxOrderQuantity.compareTo(BigDecimal.ZERO) > 0 &&
                orderQuantity.compareTo(maxOrderQuantity.multiply(new BigDecimal("2"))) >= 0) {
            overOrderingCommodityIds.add(commodity.getId());
            return;
        }

        // 校验保质期规则
        Integer qualityDays = commodity.getQualityDays();
        if (qualityDays == null || qualityDays <= 0 || avgDailySales == null || avgDailySales.compareTo(BigDecimal.ZERO) <= 0) {
            return;
        }

        // 预计售卖天数
        BigDecimal expectedSellDays = totalQuantity.divide(avgDailySales, 2, RoundingMode.HALF_UP);
        BigDecimal halfQualityDays = new BigDecimal(qualityDays).multiply(new BigDecimal("0.5"));
        if (expectedSellDays.compareTo(halfQualityDays) >= 0) {
            overOrderingCommodityIds.add(commodity.getId());
        }
    }

    /**
     * 校验少订
     */
    private void checkUnderOrdering(Long commodityId, BigDecimal totalQuantity, BigDecimal minSales, Set<Long> underOrderingCommodityIds) {
        if (minSales != null && minSales.compareTo(BigDecimal.ZERO) > 0) {
            BigDecimal minRequired = minSales.multiply(new BigDecimal("0.8"));
            if (totalQuantity.compareTo(minRequired) <= 0) {
                underOrderingCommodityIds.add(commodityId);
            }
        }
    }

    /**
     * 构建多订少订返回结果
     */
    private OverUnderOrderingCommodityVo buildResult(Set<Long> overOrderingCommodityIds, Set<Long> underOrderingCommodityIds,
                                                     Map<Long, ShopOrderingMetricsListResult> salesMetricsMap, Map<Long, Commodity> commodityMap,
                                                     Map<Long, BigDecimal> orderQuantityMap, Map<Long, ShopOrderedQuantityODTO> orderedQuantityMap,
                                                     Map<Long, ShopCommodityInfoODTO> stockMap) {
        OverUnderOrderingCommodityVo result = new OverUnderOrderingCommodityVo();

        result.setOverOrderingCommodityVos(buildCommodityInfoList(overOrderingCommodityIds, salesMetricsMap, commodityMap, orderQuantityMap, orderedQuantityMap, stockMap));

        result.setUnderOrderingCommodityVos(buildCommodityInfoList(underOrderingCommodityIds, salesMetricsMap, commodityMap, orderQuantityMap, orderedQuantityMap, stockMap));

        return result;
    }

    /**
     * 构建多订少订商品信息列表
     */
    private List<OverUnderOrderingCommodityInfoVo> buildCommodityInfoList(Set<Long> commodityIds, Map<Long, ShopOrderingMetricsListResult> salesMetricsMap,
                                                                          Map<Long, Commodity> commodityMap, Map<Long, BigDecimal> orderQuantityMap,
                                                                          Map<Long, ShopOrderedQuantityODTO> orderedQuantityMap, Map<Long, ShopCommodityInfoODTO> stockMap) {
        if (CollectionUtils.isEmpty(commodityIds)) {
            return Collections.emptyList();
        }

        return commodityIds.stream()
                .map(commodityId -> {
                    ShopOrderingMetricsListResult metrics = salesMetricsMap.get(commodityId);
                    Commodity commodity = commodityMap.get(commodityId);
                    if (metrics == null || commodity == null) {
                        return null;
                    }

                    OverUnderOrderingCommodityInfoVo vo = new OverUnderOrderingCommodityInfoVo();
                    vo.setCommodityId(commodityId);
                    vo.setCommodityName(commodity.getCommodityName());
                    vo.setCommodityCode(commodity.getCommodityCode());
                    vo.setCommoditySpec(commodity.getCommoditySpec());
                    vo.setOrderQuantity(orderQuantityMap.get(commodityId));
                    ShopCommodityInfoODTO stock = stockMap.get(commodityId);
                    if (stock != null) {
                        vo.setStockQuantity(stock.getStockQuantity());
                    }
                    BigDecimal orderedQuantity = Optional.ofNullable(orderedQuantityMap.get(commodityId))
                            .map(ShopOrderedQuantityODTO::getQuantity)
                            .orElse(BigDecimal.ZERO);
                    vo.setOrderedQuantity(orderedQuantity);
                    vo.setAvgDailySales15Days(metrics.getAvgDailySales15Days());
                    vo.setQualityDays(commodity.getQualityDays());
                    vo.setSalesTrend(metrics.getSalesTrend());
                    return vo;
                })
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    /**
     * 门店订货参考指标列表
     */
    private List<ShopOrderingMetricsListResult> getSalesMetrics(Long shopId, List<Long> commodityIds) {
        ShopOrderingMetricsListQuery query = new ShopOrderingMetricsListQuery();
        query.setShopId(shopId);
        query.setCommodityIds(commodityIds);
        try {
            // 1分钟内报错2次，30分钟内不再调用
            if(orderReferenceCheckService.enableOrderReference()) {
                return shopOrderingRefMetricsClient.list(query);
            }
        }catch (Exception e){
            orderReferenceCheckService.errorCount();
            log.warn("订货参考调用大数据异常", e);
        }
        return new ArrayList<>();
    }


    /**
     * 近15天日均销量、近15天毛利率、销售数量(当日)
     *
     * @param shopId
     * @param commodityIdList
     * @return
     */
    public Map<Long, PosXdSaleDataODTO> querySalesDataMap(Long shopId, List<Long> commodityIdList) {
        List<PosXdSaleDataODTO> saleDataList = queryTodaySalesDataList(shopId, commodityIdList);

        List<ShopOrderingMetricsListResult> metricsList = getSalesMetrics(shopId, commodityIdList);
        if (CollectionUtils.isNotEmpty(metricsList)) {
            Map<Long, ShopOrderingMetricsListResult> metricsMap = metricsList.stream().collect(Collectors.toMap(ShopOrderingMetricsListResult::getCommodityId, Function.identity()));
            saleDataList.forEach(saleData -> {
                if (metricsMap.containsKey(saleData.getCommodityId())) {
                    ShopOrderingMetricsListResult metricsListResult = metricsMap.get(saleData.getCommodityId());
                    saleData.setAvgDailySales15Days(metricsListResult.getAvgDailySales15Days());
                    saleData.setGrossProfitMargin15Days(metricsListResult.getGrossProfitMargin15Days());
                }
            });
        }

        return saleDataList.stream().collect(Collectors.toMap(PosXdSaleDataODTO::getCommodityId, Function.identity()));
    }


    /**
     * 查询当日销售数量(pos销售数量和xd销售数量)
     *
     * @param shopId
     * @param commodityIdList
     * @return
     */
    public List<PosXdSaleDataODTO> queryTodaySalesDataList(Long shopId, List<Long> commodityIdList) {
        QYAssert.isTrue(shopId != null, "门店id不能为空");
        QYAssert.isTrue(CollectionUtils.isNotEmpty(commodityIdList), "商品id不能为空");

        String nowTime = DateUtil.get4yMd(new Date());

        // 初始化返回数据
        List<PosXdSaleDataODTO> saleDataList = new ArrayList<>(commodityIdList.size());
        commodityIdList.forEach(commodityId -> {
            PosXdSaleDataODTO posXdSaleDataODTO = new PosXdSaleDataODTO();
            posXdSaleDataODTO.setCommodityId(commodityId);
            saleDataList.add(posXdSaleDataODTO);
        });

        // 查询pos销售数量
        PosSaleWaterQueryIDTO posSaleWaterQueryIDTO = new PosSaleWaterQueryIDTO();
        posSaleWaterQueryIDTO.setSaleTime(nowTime);
        posSaleWaterQueryIDTO.setShopId(shopId);
        posSaleWaterQueryIDTO.setCommodityIdList(commodityIdList);
        List<PosSaleWaterODTO> posSaleWaterList = reportClient.queryPosSalesWaterData(posSaleWaterQueryIDTO);
        if (CollectionUtils.isNotEmpty(posSaleWaterList)) {
            Map<Long, BigDecimal> posSaleWaterMap = posSaleWaterList.stream().collect(Collectors.toMap(PosSaleWaterODTO::getCommodityId, PosSaleWaterODTO::getQuantity, (key1, key2) -> key2));
            setSaleDataList(saleDataList, posSaleWaterMap);
        }

        // 查询xd销售数量（剔除订单来源为玖琅、礼擎）
        XdOrderCommodityQueryIDTO xdOrderCommodityQueryIDTO = new XdOrderCommodityQueryIDTO();
        xdOrderCommodityQueryIDTO.setSaleTime(nowTime);
        xdOrderCommodityQueryIDTO.setShopId(shopId);
        xdOrderCommodityQueryIDTO.setCommodityIdList(commodityIdList);
        List<XdOrderCommodityDataODTO> xdOrderCommodityDataList = xdSalesReportClient.queryXdOrderCommoditySaleData(xdOrderCommodityQueryIDTO);
        if (CollectionUtils.isNotEmpty(xdOrderCommodityDataList)) {
            Map<Long, BigDecimal> xdOrderCommodityMap = xdOrderCommodityDataList.stream().collect(Collectors.toMap(XdOrderCommodityDataODTO::getCommodityId, XdOrderCommodityDataODTO::getQuantity, (key1, key2) -> key2));
            setSaleDataList(saleDataList, xdOrderCommodityMap);
        }

        return saleDataList;
    }

    private void setSaleDataList(List<PosXdSaleDataODTO> saleDataList, Map<Long, BigDecimal> posSaleWaterMap) {
        saleDataList.forEach(saleData -> {
            if (posSaleWaterMap.containsKey(saleData.getCommodityId())) {
                BigDecimal quantity = posSaleWaterMap.get(saleData.getCommodityId());
                saleData.setQuantity(quantity.add(saleData.getQuantity()));
            }
        });
    }

}
