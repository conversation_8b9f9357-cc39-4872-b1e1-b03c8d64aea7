package com.pinshang.qingyun.order.mapper.entry.order;

import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @Date 2018/10/12 10:01
 */
@Data
public class OrderItemMonitorEntry {
    /**
     * 订单明细ID
     */
    private Long itemId;
    /**
     * 商品id
     */
    private Long commodityId;
    /**
     * 商品编码
     */
    private String commodityCode;
    /**
     * 商品名称
     */
    private String commodityName;
    /**
     * 规格
     */
    private String commoditySpec;
    /**
     * 商品数量
     */
    private BigDecimal commodityNum;
    /**
     * 价格
     */
    private BigDecimal commodityPrice;
    /**
     * 类型：1=订单商品、2=赠品、3=配货、4=配比
     */
    private Integer type;
    /**
     * 物流模式：0=直送，1＝配送，2＝直通
     */
    private Integer logisticsModel;
    /**
     * 仓库名称
     */
    private String warehouseName;
    /**
     * 供应商
     */
    private String supplierName;
    /**
     * 商品类型-1-非组合 2-组合  3-组合子品
     */
    private Integer combType;

    /**
     * 商品类型名称
     */
    private String combTypeName;
}
