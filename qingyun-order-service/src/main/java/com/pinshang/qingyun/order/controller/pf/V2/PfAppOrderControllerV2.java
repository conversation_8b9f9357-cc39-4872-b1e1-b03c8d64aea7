package com.pinshang.qingyun.order.controller.pf.V2;

import com.pinshang.qingyun.base.api.ApiResponse;
import com.pinshang.qingyun.base.api.PfTokenInfo;
import com.pinshang.qingyun.base.configure.expand.QYAssert;
import com.pinshang.qingyun.base.constant.QingyunConstant;
import com.pinshang.qingyun.base.enums.TerminalSourceTypeEnum;
import com.pinshang.qingyun.base.util.FastThreadLocalUtil;
import com.pinshang.qingyun.order.config.LockConstants;
import com.pinshang.qingyun.order.dto.file.FileODTO;
import com.pinshang.qingyun.order.dto.pf.*;
import com.pinshang.qingyun.order.dto.shopcart.ShoppingCartODTO;
import com.pinshang.qingyun.order.mapper.OrderListMapper;
import com.pinshang.qingyun.order.mapper.OrderMapper;
import com.pinshang.qingyun.order.mapper.PfShoppingCartMapper;
import com.pinshang.qingyun.order.mapper.StoreDurationMapper;
import com.pinshang.qingyun.order.mapper.entry.order.OrderItemEntry;
import com.pinshang.qingyun.order.service.StoreService;
import com.pinshang.qingyun.order.service.pf.v2.BuildPfShoppingCartV2;
import com.pinshang.qingyun.order.service.pf.v2.PfOrderServiceV2;
import com.pinshang.qingyun.order.service.xda.v4.ToBService;
import com.pinshang.qingyun.pf.product.service.PfCommodityFrontClient;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;

/**
 * 批发订单
 *
 */
@RestController
@RequestMapping("/pf/orderV2")
@Slf4j
public class PfAppOrderControllerV2 {

	@Autowired
    private RedissonClient redissonClient;
	
	@Autowired
	private PfOrderServiceV2 pfOrderServiceV2;

	@Autowired
	private ToBService toBService;
    
	@Autowired
    private OrderMapper orderMapper;
	
	@Autowired
	private PfShoppingCartMapper pfShoppingCartMapper;
	
	@Autowired
    private OrderListMapper orderListMapper;
	
	@Autowired
    private PfCommodityFrontClient pfCommodityFrontClient;
	
	@Autowired
    private StoreService storeService;
	
	@Autowired
    private StoreDurationMapper storeDurationMapper;
	
	@GetMapping("/detailV2")
    @ApiOperation(value = "订单明细")
    public PfOrder4AppODTO queryOrderDetailByCode(@RequestParam(value = "orderCode",required = false) String orderCode){
        return pfOrderServiceV2.queryOrderDetailByCode(orderCode);
    }
	
	@GetMapping("/copyV2")
    @ApiOperation(value = "订单复制")
    public Integer copyOrderById4App(@RequestParam(value = "orderId",required = false) Long orderId){
        return pfOrderServiceV2.copyOrderById4App(orderId);
    }
	
	@SuppressWarnings("unchecked")
	@PostMapping("/listV2")
    @ApiOperation(value = "订单列表")
    public ApiResponse<PfOrder4AppODTO> queryOrderListByPage(@RequestBody PfQueryOrder4AppParam param, HttpServletResponse response){
        response.addHeader(QingyunConstant.WRAP_RESPONSE_FLAG,QingyunConstant.WRAP_RESPONSE_FLAG_VALUE);
        PfTokenInfo pfTokenInfo = FastThreadLocalUtil.getPF();
        Long storeId =  pfTokenInfo.getStoreId();
        QYAssert.isTrue(storeId != null, "未能获取到用户信息,请重新登录!");
        param.setStoreId(storeId);
        return ApiResponse.convert(pfOrderServiceV2.queryOrderListByPage(param));
    }
	
	@PostMapping("/preOrderV2")
    @ApiOperation(value = "预览订单")
    public PfPreOrderODTO preOrderView(@RequestBody PfPreOrderIDTO query){
		PfTokenInfo pfTokenInfo = FastThreadLocalUtil.getPF();
        Long storeId = pfTokenInfo.getStoreId();
        QYAssert.isTrue(!pfTokenInfo.getIsTouristStore(), "游客无法下单。");
        QYAssert.isTrue(storeId != null, "未能获取到用户信息,请重新登录!");
        query.setStoreId(storeId);
        query.setAppCode(pfTokenInfo.getAppCode());
        return pfOrderServiceV2.preOrderView(query);
    }
	
	@PostMapping("/createOrderV2")
	@ApiOperation(value = "创建订单")
	public PfSaveOrderODTO createOrder(@RequestBody PfCreatePrePayOrderIDTO idto) {
		PfTokenInfo pfTokenInfo = FastThreadLocalUtil.getPF();
		QYAssert.isTrue(!pfTokenInfo.getIsTouristStore(), "游客无法下单。");
		Long storeId = pfTokenInfo.getStoreId();
		QYAssert.isTrue(storeId != null, "未能获取到用户信息,请重新登录!");
		
		String appCode = pfTokenInfo.getAppCode();
        Long userId = pfTokenInfo.getUserId();
        TerminalSourceTypeEnum typeEnum = pfTokenInfo.getTerminalSourceType();
        idto.convert(storeId, appCode, userId, typeEnum);
        
		Date orderTime = idto.getOrderTime();
        // 购物车信息
		BuildPfShoppingCartV2 buildPfShoppingCartV2 = new BuildPfShoppingCartV2(storeId, orderTime, toBService,pfShoppingCartMapper, orderMapper, orderListMapper, pfCommodityFrontClient, storeService, storeDurationMapper);
	    ShoppingCartODTO shopCart = buildPfShoppingCartV2.shopCartList();
	    List<Long> commodityIds = shopCart.getAllCommodityIds();
	    // 构建商品库存锁（多个）
	    List<RLock> commodityInventoryLocks = getCommodityInventoryLocks(commodityIds);
	    
        String storeLockKey = LockConstants.generateOrderCommitLock(storeId);
        RLock storeLock = redissonClient.getLock(storeLockKey);
        try {
        	// 这里用了两次锁，一次是客户锁，一次是他所下单的商品库存锁（保证扣减库存时的原子性）
        	if(storeLock.tryLock() && tryLockInventory(commodityInventoryLocks)){
        		return pfOrderServiceV2.creatOrder(idto);
        	} else {
        		QYAssert.isFalse("系统繁忙,请稍后再试!");
        	}
        } finally {
        	try {
	        	if (storeLock.isHeldByCurrentThread()) {
					storeLock.unlock();
				} 
        	} catch (Exception e) {
				log.error(String.format("释放客户锁[%s]异常", storeId), e);
			}
        	unlockInventory(commodityInventoryLocks);
        }
        
        return PfSaveOrderODTO.Success();
	}
	
	private void unlockInventory(List<RLock> commodityInventoryLocks) {
		for (RLock lck : commodityInventoryLocks) {
			try {
				if (lck.isHeldByCurrentThread()) {
					lck.unlock();
				}
			} catch (Exception e) {
				log.error(String.format("释放商品库存锁[%s]异常", lck.getName()), e);
			}
		}
	}

	private boolean tryLockInventory(List<RLock> commodityInventoryLocks) {
		boolean flag = true;
		for (RLock lock : commodityInventoryLocks) {
			flag = flag && lock.tryLock();
		}
		return flag;
	}

	private List<RLock> getCommodityInventoryLocks(List<Long> commodityIds) {
		Collections.sort(commodityIds);
		List<RLock> result = new ArrayList<>();
		for (Long cid : commodityIds) {
			String lockKey = LockConstants.generatePfCommodityInventory(cid);
	        RLock lock = redissonClient.getLock(lockKey);
	        result.add(lock);
		}
		return result;
	}

	@PostMapping("/cancelV2")
    @ApiOperation(value = "订单取消")
	public Integer cancelOrder(@RequestBody PfOrderCancelIDTO idto) {
		PfTokenInfo pfTokenInfo = FastThreadLocalUtil.getPF();
		Long storeId =  pfTokenInfo.getStoreId();
		Long userId = pfTokenInfo.getUserId();
        QYAssert.isTrue(storeId != null, "未能获取到用户信息,请重新登录!");
        QYAssert.isTrue(userId != null, "未能获取到用户信息,请重新登录!");
        QYAssert.isTrue(null != idto.getOrderId() , "订单id不存在!");
        QYAssert.isTrue(null != idto.getReasonOptionId() , "取消原因必选!");
        idto.setStoreId(storeId);
        idto.setUserId(userId);
        
        List<OrderItemEntry> commList = this.orderMapper.queryOrderItem4Copy(idto.getOrderId());
        List<Long> commodityIds = new ArrayList<>();
        for (OrderItemEntry com : commList) {
        	commodityIds.add(com.getCommodityId());
        }
        // 构建商品库存锁（多个）
        List<RLock> commodityInventoryLocks = getCommodityInventoryLocks(commodityIds);
        
        String storeLockKey = LockConstants.generateOrderCommitLock(storeId);
        RLock storeLock = redissonClient.getLock(storeLockKey);
        
        try {
        	// 这里用了两次锁，一次是客户锁，一次是他所下单的商品库存锁（保证扣减库存时的原子性）
        	if (storeLock.tryLock() && tryLockInventory(commodityInventoryLocks)) {
        		return pfOrderServiceV2.cancelOrder(idto);
        	} else {
        		QYAssert.isFalse("系统繁忙,请稍后再试!");
        	}
        } finally {
        	try {
	        	if (storeLock.isHeldByCurrentThread()) {
					storeLock.unlock();
				} 
        	} catch (Exception e) {
				log.error(String.format("释放客户锁[%s]异常", storeId), e);
			}
        	unlockInventory(commodityInventoryLocks);
        }
        
        return null;
	}
	
	@ApiOperation(value = "查询  订单PDF文件")
    @GetMapping("/queryOrderPdfFileV2")
    public FileODTO queryOrderPdfFile(@RequestParam(value = "orderId",required = false) Long orderId){
		return pfOrderServiceV2.queryOrderPdfFile(orderId);
    }
}
