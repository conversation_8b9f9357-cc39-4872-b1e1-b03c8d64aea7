package com.pinshang.qingyun.order.service.pay.model;

import com.pinshang.qingyun.order.mapper.entry.pf.recharge.PfPayBillJobEntry;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @Date 2019/11/27 17:40
 */
@Data
@NoArgsConstructor
public class PfTradeQueryParam {
    /**
     * 支付单code
     */
    private String billCode;
    /**
     * 外部的交易流水号（支付宝、微信或云闪付返回的支付单号）
     */
    private String tradeBillCode;

    private Integer payType;


    public PfTradeQueryParam(PfPayBillJobEntry entry){
        this.billCode = entry.getBillCode();
        this.tradeBillCode = entry.getTradeBillCode();
        this.payType = entry.getPayType();
    }
}
