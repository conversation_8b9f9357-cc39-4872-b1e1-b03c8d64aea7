package com.pinshang.qingyun.order.service;

import com.pinshang.qingyun.box.utils.PasswordUtils;
import com.pinshang.qingyun.order.enums.AppUserModifyEnum;
import com.pinshang.qingyun.order.enums.AppUserStatusEnum;
import com.pinshang.qingyun.order.mapper.AppUserMapper;
import com.pinshang.qingyun.order.mapper.StoreDurationMapper;
import com.pinshang.qingyun.order.mapper.StorePlaceOrderLogMapper;
import com.pinshang.qingyun.order.model.store.StoreDuration;
import com.pinshang.qingyun.order.model.store.StorePlaceOrderLog;
import com.pinshang.qingyun.order.model.user.AppUser;
import com.pinshang.qingyun.order.vo.user.AppUserInfo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.pinshang.qingyun.base.configure.expand.QYAssert;

import java.util.Date;

/**
 * <AUTHOR>
 *
 * 后序需迁移至 qingyun-store-service
 * @Date 2018/10/10 10:26
 */
@Service
@Slf4j
public class AppUserService {
    @Autowired
    private AppUserMapper appUserMapper;
    @Autowired
    private StoreDurationMapper storeDurationMapper;

    @Autowired
    private StorePlaceOrderLogMapper storePlaceOrderLogMapper;

    public AppUser findByStoreCode(String storeCode){
        return appUserMapper.findByStoreCode(storeCode);
    }

    /**
     * 修改密码 重置密码
     * @param info
     * @param forOpen
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean editUser(AppUserInfo info, boolean forOpen){
        AppUser user = findByStoreCode(info.getStoreCode());
        QYAssert.notNull(user,"查无此用户:"+info.getStoreCode()+"!");
        String md5Password = PasswordUtils.generateMd5Password(info.getPassword());
        user.setName(info.getStoreCode());
        user.setPassword(md5Password);
        //重置生鲜APP密码
        Integer operateType = 3;
        if(forOpen){
            //开通生鲜APP
            operateType = 1;
            user.setModify(AppUserModifyEnum.NEED);
            user.setState(AppUserStatusEnum.VALID);
        }
        boolean b = false;
        if(b = (appUserMapper.editByName(user) > 0)){
            /**
             * 记录日志
             */
            this.insertStorePlaceOrderLog(operateType,user.getStoreId(),info.getCreatorId());
        }
        return b;
    }

    /**
     * 清美助手 开户操作
     * @param info
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean openAccount(AppUserInfo info){
        AppUser user = findByStoreCode(info.getStoreCode());
        if(user !=null){
            if(user.getCreateUserId() !=null &&user.getCreateUserId().longValue() !=info.getCreatorId()){
                log.warn(user.getName()+"正在被非创建者id:"+info.getCreatorId()+" 修改为开户状态!");
            }
            setStoreDuration( info.getStoreCode() , info.getCreatorId() );
            return editUser(info,true);
        }else{
            user = buildUserModel(info);
            setStoreDuration( info.getStoreCode() , info.getCreatorId() );
            boolean b = false;
            /**
             * 记录日志
             */
            if(b = (appUserMapper.openAccount(user) > 0)){
                this.insertStorePlaceOrderLog(1,user.getStoreId(),info.getCreatorId());
            }
            return b;
        }
    }

    private AppUser buildUserModel(AppUserInfo info) {
        AppUser user = new AppUser();
        user.setName(info.getStoreCode());
        user.setPassword(PasswordUtils.generateMd5Password(info.getPassword()));
        user.setStoreId(info.getStoreId());
        user.setStoreCode(info.getStoreCode());
        user.setModify(AppUserModifyEnum.NEED);
        user.setState(AppUserStatusEnum.VALID);
        user.setCreateUserId(info.getCreatorId());
        return user;
    }
    private  void setStoreDuration(String storeCode,Long creatorId){
        
        StoreDuration storeLineDuration =  storeDurationMapper.findLineDurationByStoreCode( storeCode );
        QYAssert.notNull(storeLineDuration,"未查询客户线路下单截止时间","客户编码:{}",storeCode);

        StoreDuration db = new StoreDuration( );
        db.setStoreId(storeLineDuration.getStoreId());
        db = storeDurationMapper.selectOne( db );
        Date now = new Date() ;
        if( db == null ){
            db = new StoreDuration(storeLineDuration.getBeginTime(),storeLineDuration.getEndTime(),storeLineDuration.getStoreId()
            ,storeLineDuration.getStoreCode(),creatorId,"",storeLineDuration.getLineGroupId()
                    ,0,null,storeLineDuration.getEndTime(),now,now);
            storeDurationMapper.insert( db );
        }else{
            db.setBeginTime( storeLineDuration.getBeginTime());
            db.setEndTime( storeLineDuration.getEndTime() );
            db.setStoreCode( storeLineDuration.getStoreCode() );
            db.setLineGroupId( storeLineDuration.getLineGroupId());
            db.setOldLineGroupTime( storeLineDuration.getEndTime() );
            db.setUpdateTime( now );
            storeDurationMapper.updateByPrimaryKeySelective( db );
        }
    }
    private int insertStorePlaceOrderLog(Integer operateType , Long storeId ,Long userId){
        QYAssert.isTrue(storeId != null, "客户不能为空");
        StorePlaceOrderLog storePlaceOrderLog = storePlaceOrderLogMapper.selectStorePlaceOrderLogByStoreId(storeId);
        storePlaceOrderLog.setOperateType(operateType);
        storePlaceOrderLog.setCreateId(userId);
        storePlaceOrderLog.setCreateTime(new Date());
        return storePlaceOrderLogMapper.insert(storePlaceOrderLog);
    }
}
