package com.pinshang.qingyun.order.controller;

import com.pinshang.qingyun.order.dto.AutoShopCommodityODTO;
import com.pinshang.qingyun.order.dto.ShopAutoOrderJobIDTO;
import com.pinshang.qingyun.order.service.JobService;
import com.pinshang.qingyun.order.service.auto.ShopAutoOrderService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2018/10/10 10:08
 */
@RestController
@RequestMapping("/orderJob")
public class JobController {

    @Autowired
    private JobService jobService;
    @Autowired
    private ShopAutoOrderService shopAutoOrderService;

    @PostMapping("/testAutoOrder")
    public Boolean testAutoOrder(@RequestParam(value = "orderTime") String orderTime, @RequestParam(value = "shopIdList") List<Long> shopIdList){
        return shopAutoOrderService.testAutoOrder(orderTime, shopIdList);
    }


    /**
     * 创建提货卡job
     * @return
     */
    @PostMapping("/createTakeCardOrderJob")
    public Boolean createTakeCardOrderJob(){
        return jobService.createTakeCardOrderJob();
    }

    /**
     * 执行提货卡job
     * @param orderTime
     * @return
     * @throws Throwable
     */
    @PostMapping("/executeTakeCardOrderJob")
    public Boolean executeTakeCardOrderJob(@RequestParam(value = "orderTime",required = false) String orderTime) throws Throwable{
        return jobService.executeTakeCardOrderJob(orderTime);
    }




    /**
     * 创建生成采购单job
     * @return
     */
    @PostMapping("/createPurchaseOrderJob")
    public Boolean createPurchaseOrderJob(){
        return jobService.createPurchaseOrderJob();
    }
    /**
     * 执行生成采购单job
     * @param orderTime
     * @return
     * @throws Throwable
     */
    @PostMapping("/executePurchaseOrderJob")
    public Boolean executePurchaseOrderJob(@RequestParam(value = "orderTime",required = false) String orderTime) throws Throwable{
        return jobService.executePurchaseOrderJob(orderTime);
    }


    /**
     * 门店自动订货job创建
     * @return
     */
    @PostMapping("/createShopAutoOrderJob")
    public Boolean createShopAutoOrderJob(){
        return jobService.createShopAutoOrderJob();
    }

    /**
     * 门店自动订货job执行
     * @param idto
     * @return
     * @throws Throwable
     */
    @PostMapping("/executeShopAutoOrderJob")
    public Boolean executeShopAutoOrderJob(@RequestBody ShopAutoOrderJobIDTO idto){
        return jobService.executeShopAutoOrderJob(idto);
    }

    @PostMapping("/subOrderItemCommodityPriceJob")
    public void subOrderItemCommodityPriceJob(@RequestParam(value = "orderTime",required = false) String orderTime){
        jobService.subOrderItemCommodityPriceJob(orderTime);
    }

    /**
     * 查询需要自动订货的门店信息
     * @param orderTime
     * @return
     * @throws Throwable
     */
    @PostMapping("/queryAutoShopList")
    public  List<AutoShopCommodityODTO> queryAutoShopList(){
        return shopAutoOrderService.queryAutoShopList();
    }
}
