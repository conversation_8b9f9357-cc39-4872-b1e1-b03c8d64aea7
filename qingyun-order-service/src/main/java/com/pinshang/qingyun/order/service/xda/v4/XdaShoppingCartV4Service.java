package com.pinshang.qingyun.order.service.xda.v4;

import com.pinshang.qingyun.base.api.ApiErrorCodeEnum;
import com.pinshang.qingyun.base.api.QYApplicationContext;
import com.pinshang.qingyun.base.api.XdaTokenInfo;
import com.pinshang.qingyun.base.configure.expand.BizLogicException;
import com.pinshang.qingyun.base.configure.expand.QYAssert;
import com.pinshang.qingyun.base.enums.marketing.MarketSourceTypeEnum;
import com.pinshang.qingyun.base.enums.order.OrderTypeEnum;
import com.pinshang.qingyun.base.util.FastThreadLocalUtil;
import com.pinshang.qingyun.marketing.dto.app.CommodityDetailIDTO;
import com.pinshang.qingyun.marketing.dto.app.CommodityDetailODTO;
import com.pinshang.qingyun.marketing.service.MtPromotionClient;
import com.pinshang.qingyun.order.dto.shopcart.v4.*;
import com.pinshang.qingyun.order.enums.StockType;
import com.pinshang.qingyun.order.mapper.OrderListMapper;
import com.pinshang.qingyun.order.mapper.XdaShoppingCartMapper;
import com.pinshang.qingyun.order.model.order.XdaShoppingCart;
import com.pinshang.qingyun.order.service.BStockShortService;
import com.pinshang.qingyun.order.service.xda.util.XdaShoppingCartToolUtil;
import com.pinshang.qingyun.order.service.xda.util.ShoppingCartUtil;
import com.pinshang.qingyun.order.vo.BStockLackVO;
import com.pinshang.qingyun.product.dto.commodity.CommodityItemODTO;
import com.pinshang.qingyun.storage.dto.tob.CommodityInventoryDetailIDTO;
import com.pinshang.qingyun.storage.dto.tob.CommodityInventoryODTO;
import com.pinshang.qingyun.xda.product.dto.shoppingCart.XdaShoppingCartV3IDTO;
import com.pinshang.qingyun.xda.product.dto.shoppingCart.XdaShoppingCartV3ODTO;
import com.pinshang.qingyun.xda.product.service.XdaShoppingCartController;
import org.apache.commons.collections.CollectionUtils;
import org.redisson.api.RBucket;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.entity.Example;

import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2024/3/4 16:24
 */
@Service
public class XdaShoppingCartV4Service {
    /**
     * 购物车可添加最大数量
     */
    public static BigDecimal ShoppingCart_Max_Value = BigDecimal.valueOf(9999.99);

    @Autowired
    private XdaShoppingCartToolUtil xdaShoppingCartToolUtil;

    @Autowired
    private OrderListMapper orderListMapper;

    @Autowired
    private XdaShoppingCartMapper xdaShoppingCartMapper;

    @Autowired
    private XdaShoppingCartController xdaShoppingCartController;

    @Autowired
    private BStockShortService bStockShortService;

    @Autowired
    private ToBService toBService;
    @Autowired
    private MtPromotionClient mtPromotionClient;
    @Autowired
    private RedissonClient redissonClient;
    @Transactional(rollbackFor = Exception.class)
    public NotShoppingCartPageV4ODTO addShopCart(ShoppingCartAddV4IDTO shoppingCartAddV4IDTO){
        QYAssert.isTrue(shoppingCartAddV4IDTO.getCommodityId() != null, "商品参数异常.");
        QYAssert.isTrue(shoppingCartAddV4IDTO.getOrderTime() != null, "订货日期不能为空.");
        QYAssert.isTrue(shoppingCartAddV4IDTO.getCommodityType() != null, "商品类型不能为空.");
        NotShoppingCartPageV4ODTO notShoppingCartPageV4ODTO = new NotShoppingCartPageV4ODTO();
        notShoppingCartPageV4ODTO.setCommodityId(shoppingCartAddV4IDTO.getCommodityId());
        notShoppingCartPageV4ODTO.setCommodityType(shoppingCartAddV4IDTO.getCommodityType());
        List<XdaShoppingCart> xdaShoppingCarts = xdaShoppingCartToolUtil.queryShoppingCart(shoppingCartAddV4IDTO.getStoreId(), shoppingCartAddV4IDTO.getCommodityId(),shoppingCartAddV4IDTO.getCommodityType());
        BigDecimal salesBoxCapacity = xdaShoppingCartToolUtil.getQuantity(shoppingCartAddV4IDTO.getCommodityId(), shoppingCartAddV4IDTO.getStoreId());
        if(xdaShoppingCarts.isEmpty()){
            ShoppingCartUtil.checkQuantity(salesBoxCapacity);
            XdaShoppingCart shoppingCart = XdaShoppingCart.builder()
                    .commodityId(shoppingCartAddV4IDTO.getCommodityId())
                    .commodityType(shoppingCartAddV4IDTO.getCommodityType())
                    .storeId(shoppingCartAddV4IDTO.getStoreId())
                    .quantity(salesBoxCapacity)
                    .build();
            shoppingCart.setQuantity(salesBoxCapacity);
            notShoppingCartPageV4ODTO.setQuantity(salesBoxCapacity);
            checkCommodityInfo(shoppingCart, shoppingCartAddV4IDTO.getCommodityType(),shoppingCartAddV4IDTO.getOrderTime(),notShoppingCartPageV4ODTO,
                    shoppingCartAddV4IDTO.getStoreId(), shoppingCartAddV4IDTO.getDeliverytimerange(),shoppingCartAddV4IDTO.getDeliverybatch());
            if(null != notShoppingCartPageV4ODTO.getAbleAdd() && !notShoppingCartPageV4ODTO.getAbleAdd()){
                notShoppingCartPageV4ODTO.setQuantity(BigDecimal.ZERO);
                return notShoppingCartPageV4ODTO;
            }
            xdaShoppingCartMapper.insert(shoppingCart);
            return notShoppingCartPageV4ODTO;
        }
        XdaShoppingCart xdaShoppingCart = xdaShoppingCarts.get(0);
        BigDecimal quantity = xdaShoppingCart.getQuantity();
        xdaShoppingCart.setQuantity(ShoppingCartUtil.checkSalesBoxCapacityUp(salesBoxCapacity.add(quantity),salesBoxCapacity));
        QYAssert.isTrue( xdaShoppingCart.getQuantity().compareTo(ShoppingCart_Max_Value) <= 0, "商品数量最大"+ShoppingCart_Max_Value);
        notShoppingCartPageV4ODTO.setQuantity(xdaShoppingCart.getQuantity());
        checkCommodityInfo(xdaShoppingCart,shoppingCartAddV4IDTO.getCommodityType(), shoppingCartAddV4IDTO.getOrderTime(),notShoppingCartPageV4ODTO,
                shoppingCartAddV4IDTO.getStoreId(), shoppingCartAddV4IDTO.getDeliverytimerange(),shoppingCartAddV4IDTO.getDeliverybatch());
        /*如果超过限量则不修改商品数量*/
        if((null != notShoppingCartPageV4ODTO.getAbleAdd() && !notShoppingCartPageV4ODTO.getAbleAdd()) || null != notShoppingCartPageV4ODTO.getStockWarningTips()){
            notShoppingCartPageV4ODTO.setQuantity(quantity);
            return notShoppingCartPageV4ODTO;
        }
        int count = xdaShoppingCartMapper.updateByPrimaryKeySelective(xdaShoppingCart);
        QYAssert.isTrue(count != 0, "购物车更新失败");
        return notShoppingCartPageV4ODTO;
    }
    @Transactional(rollbackFor = Exception.class)
    public NotShoppingCartPageV4ODTO minus(ShoppingCartMinusV4IDTO shoppingCartMinusV4IDTO){
        QYAssert.isTrue(shoppingCartMinusV4IDTO.getCommodityId() != null, "商品参数异常.");
        QYAssert.isTrue(shoppingCartMinusV4IDTO.getOrderTime() != null, "订货日期不能为空.");
        QYAssert.isTrue(shoppingCartMinusV4IDTO.getCommodityType() != null, "商品类型不能为空.");
        List<XdaShoppingCart> xdaShoppingCarts = xdaShoppingCartToolUtil.queryShoppingCart(shoppingCartMinusV4IDTO.getStoreId(), shoppingCartMinusV4IDTO.getCommodityId(),shoppingCartMinusV4IDTO.getCommodityType());
        if(xdaShoppingCarts == null || xdaShoppingCarts.isEmpty()){
            QYAssert.isFalse("购物车中未找到指定商品!");
        }
        XdaShoppingCart xdaShoppingCart = xdaShoppingCarts.get(0);
        BigDecimal quantity = xdaShoppingCart.getQuantity();
        NotShoppingCartPageV4ODTO notShoppingCartPageV4ODTO = NotShoppingCartPageV4ODTO.
                init(shoppingCartMinusV4IDTO.getCommodityId(), shoppingCartMinusV4IDTO.getCommodityType(),quantity);
        BigDecimal salesBoxCapacity = xdaShoppingCartToolUtil.getQuantity(shoppingCartMinusV4IDTO.getCommodityId(), shoppingCartMinusV4IDTO.getStoreId());

        BigDecimal minusNum = xdaShoppingCart.getQuantity().subtract(salesBoxCapacity);
        notShoppingCartPageV4ODTO.setQuantity(ShoppingCartUtil.checkSalesBoxCapacityDown(minusNum,salesBoxCapacity));
        if(minusNum.compareTo(BigDecimal.ZERO) <= 0){
            //处理特价限购提示，删除redis缓存，表示不超限
            String key = "SPECIAL_PRICE_LIMIT_WARNING:%s:%s";
            RBucket<Integer> bucket = redissonClient.getBucket(QYApplicationContext.redisKeyProfile + String.format(key, shoppingCartMinusV4IDTO.getStoreId(), shoppingCartMinusV4IDTO.getCommodityId()));
            bucket.delete();

            xdaShoppingCartMapper.deleteByPrimaryKey(xdaShoppingCart);
            return notShoppingCartPageV4ODTO;
        }
        xdaShoppingCart.setQuantity(minusNum);
        checkCommodityInfo(xdaShoppingCart,shoppingCartMinusV4IDTO.getCommodityType(), shoppingCartMinusV4IDTO.getOrderTime(), notShoppingCartPageV4ODTO,
                shoppingCartMinusV4IDTO.getStoreId(), shoppingCartMinusV4IDTO.getDeliverytimerange(), shoppingCartMinusV4IDTO.getDeliverybatch());
        int count = xdaShoppingCartMapper.updateByPrimaryKeySelective(xdaShoppingCart);
        QYAssert.isTrue(count != 0, "购物车更新失败");
        return notShoppingCartPageV4ODTO;
    }
    /**
     * 设置数量
     * @param shoppingCartSetNumV4IDTO
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public NotShoppingCartPageV4ODTO setNum(ShoppingCartSetNumV4IDTO shoppingCartSetNumV4IDTO){
        QYAssert.isTrue(shoppingCartSetNumV4IDTO.getCommodityId() != null, "商品参数异常.");
        QYAssert.isTrue(shoppingCartSetNumV4IDTO.getOrderTime() != null, "订货日期不能为空.");
        QYAssert.isTrue(shoppingCartSetNumV4IDTO.getCommodityType() != null, "商品类型不能为空.");
        List<XdaShoppingCart> xdaShoppingCarts = xdaShoppingCartToolUtil.queryShoppingCart(shoppingCartSetNumV4IDTO.getStoreId(), shoppingCartSetNumV4IDTO.getCommodityId(),shoppingCartSetNumV4IDTO.getCommodityType());
        if(xdaShoppingCarts == null || xdaShoppingCarts.isEmpty()){
            QYAssert.isFalse("购物车中未找到指定商品!");
        }
        BigDecimal salesBoxCapacity = xdaShoppingCartToolUtil.getQuantity(shoppingCartSetNumV4IDTO.getCommodityId(), shoppingCartSetNumV4IDTO.getStoreId());
        QYAssert.isTrue(shoppingCartSetNumV4IDTO.getQuantity().compareTo(ShoppingCart_Max_Value) <= 0
                , "设置数量超量异常!");
        //设置购物车数量
        BigDecimal quantity =shoppingCartSetNumV4IDTO.getQuantity().divide(salesBoxCapacity,2,BigDecimal.ROUND_UP).setScale(0,BigDecimal.ROUND_UP);
        shoppingCartSetNumV4IDTO.setQuantity(salesBoxCapacity.multiply(quantity));

        //组装返回类
        NotShoppingCartPageV4ODTO notShoppingCartPageV4ODTO = NotShoppingCartPageV4ODTO.
                init(shoppingCartSetNumV4IDTO.getCommodityId(), shoppingCartSetNumV4IDTO.getCommodityType(),shoppingCartSetNumV4IDTO.getQuantity());
        XdaShoppingCart xdaShoppingCart = xdaShoppingCarts.get(0);
        BigDecimal userQuantity = xdaShoppingCart.getQuantity();
        xdaShoppingCart.setQuantity(shoppingCartSetNumV4IDTO.getQuantity());

        QYAssert.isTrue(shoppingCartSetNumV4IDTO.getQuantity().compareTo(ShoppingCart_Max_Value) <= 0
                , "设置数量超量异常!");
        if(shoppingCartSetNumV4IDTO.getQuantity().compareTo(BigDecimal.ZERO) == 0){
            xdaShoppingCartMapper.deleteByPrimaryKey(xdaShoppingCart);
            return notShoppingCartPageV4ODTO;
        }
        checkCommodityInfo(xdaShoppingCart,shoppingCartSetNumV4IDTO.getCommodityType(), shoppingCartSetNumV4IDTO.getOrderTime(), notShoppingCartPageV4ODTO,
                shoppingCartSetNumV4IDTO.getStoreId(), shoppingCartSetNumV4IDTO.getDeliverytimerange(), shoppingCartSetNumV4IDTO.getDeliverybatch());
        /**
         * 如果超过限量则不修改商品数量
         */
        if((null != notShoppingCartPageV4ODTO.getAbleAdd() && !notShoppingCartPageV4ODTO.getAbleAdd()) || null != notShoppingCartPageV4ODTO.getStockWarningTips()){
            notShoppingCartPageV4ODTO.setQuantity(userQuantity);
            return notShoppingCartPageV4ODTO;
        }
        int count = xdaShoppingCartMapper.updateByPrimaryKeySelective(xdaShoppingCart);
        QYAssert.isTrue(count != 0, "购物车更新失败");
        return notShoppingCartPageV4ODTO;
    }

    /**
     * 删除购物车商品
     * @param storeId
     * @param shoppingCartClearV4IDTO
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public Integer clearV4(Long storeId, ShoppingCartClearV4IDTO shoppingCartClearV4IDTO){
        Example ex = new Example(XdaShoppingCart.class);
        Example.Criteria criteria = ex.createCriteria().andEqualTo("storeId", storeId);
        if (null == shoppingCartClearV4IDTO) {
            List<XdaShoppingCart> xdaShoppingCarts = xdaShoppingCartMapper.selectByExample(ex);
            if (CollectionUtils.isNotEmpty(xdaShoppingCarts)) {
                for (XdaShoppingCart cart : xdaShoppingCarts) {
                    Long commodityId = cart.getCommodityId();
                    //处理特价限购提示，删除redis缓存，表示不超限
                    String key = "SPECIAL_PRICE_LIMIT_WARNING:%s:%s";
                    RBucket<Integer> bucket = redissonClient.getBucket(QYApplicationContext.redisKeyProfile + String.format(key, storeId, commodityId));
                    bucket.delete();
                }
            }
           return xdaShoppingCartMapper.deleteByExample(ex);
        }
        if(null != shoppingCartClearV4IDTO.getCommodityIds() && shoppingCartClearV4IDTO.getCommodityIds().size() >0){
            criteria.andIn("commodityId", shoppingCartClearV4IDTO.getCommodityIds())
                    .andEqualTo("commodityType",1);
            if(null != shoppingCartClearV4IDTO.getThCommodityIds() && shoppingCartClearV4IDTO.getThCommodityIds().size() > 0){
                criteria.orIn("commodityId", shoppingCartClearV4IDTO.getThCommodityIds())
                        .andEqualTo("commodityType", 2);
            }

            for (Long commodityId : shoppingCartClearV4IDTO.getCommodityIds()) {
                //处理特价限购提示，删除redis缓存，表示不超限
                String key = "SPECIAL_PRICE_LIMIT_WARNING:%s:%s";
                RBucket<Integer> bucket = redissonClient.getBucket(QYApplicationContext.redisKeyProfile + String.format(key, storeId, commodityId));
                bucket.delete();
            }
        }else if(null != shoppingCartClearV4IDTO.getThCommodityIds() && shoppingCartClearV4IDTO.getThCommodityIds().size() > 0){
            criteria.andIn("commodityId", shoppingCartClearV4IDTO.getThCommodityIds())
                    .andEqualTo("commodityType", 2);

            for (Long commodityId : shoppingCartClearV4IDTO.getThCommodityIds()) {
                //处理特价限购提示，删除redis缓存，表示不超限
                String key = "SPECIAL_PRICE_LIMIT_WARNING:%s:%s";
                RBucket<Integer> bucket = redissonClient.getBucket(QYApplicationContext.redisKeyProfile + String.format(key, storeId, commodityId));
                bucket.delete();
            }
        }
        return xdaShoppingCartMapper.deleteByExample(ex);
    }

    /**
     * 清除失效商品
     * @param buildXdaShoppingCartV4
     */
    @Transactional(rollbackFor = Exception.class)
    public ShoppingCartV4ODTO clearInvalidCommodity(BuildXdaShoppingCartV4 buildXdaShoppingCartV4){
        XdaTokenInfo tokenInfo = FastThreadLocalUtil.getXDA();
        Long storeId =  tokenInfo.getStoreId();
        QYAssert.isTrue(storeId != null, "未能获取到用户信息,请重新登录!");

        ShoppingCartV4ODTO cartODTO = buildXdaShoppingCartV4.shopCartList();

        List<ShoppingCartCommodityV4ODTO> commodities = cartODTO.getInvalidateGroup().getCommodities();
        List<Long> shopCartList = commodities.stream().map(ShoppingCartCommodityV4ODTO :: getCommodityId).collect(Collectors.toList());
        if(shopCartList.isEmpty()){
            cartODTO.setInvalidateGroup(ShoppingCartGroupV4ODTO.initialization());
            return cartODTO;
        }
        xdaShoppingCartMapper.deleteShopCart(storeId, shopCartList);
        cartODTO.setInvalidateGroup(ShoppingCartGroupV4ODTO.initialization());
        return cartODTO;
    }

    /**
     * 判断商品限量
     * @param cart
     * @param commodityType
     * @param orderTime
     * @param pageODTO
     */
    public void checkCommodityInfo(XdaShoppingCart cart, Integer commodityType, Date orderTime, NotShoppingCartPageV4ODTO pageODTO,
                                   Long storeId, String deliveryTimeRange, Integer deliveryBatch){
        XdaShoppingCartV3IDTO build = XdaShoppingCartV3IDTO.builder().commodityId(cart.getCommodityId()).storeId(cart.getStoreId()).commodityType(commodityType).orderTime(orderTime).build();
        XdaShoppingCartV3ODTO xdaCommodityTextListIsCanOrder = xdaShoppingCartController.getXdaCommodityTextListIsCanOrder(build);
        QYAssert.isTrue(null != xdaCommodityTextListIsCanOrder, "商品已下架，请刷新当前商品页。");
        if(null != xdaCommodityTextListIsCanOrder.getIsCanOrder() && !xdaCommodityTextListIsCanOrder.getIsCanOrder()){
            throw new BizLogicException(ApiErrorCodeEnum.XDA_INVALID_COMM_WARN.getRemark(), ApiErrorCodeEnum.XDA_INVALID_COMM_WARN);
        }

        // 查询组合商品的子商品
        List<CommodityItemODTO> commodityItemList = toBService.getCommodityItemList(cart.getCommodityId());

        // 根据商品ID查询商品信息及其库存
        Map<Long, CommodityInventoryODTO> commodityInventoryMap = getCommodityInventoryMap(cart, commodityType, orderTime,commodityItemList,
                                                                    storeId, deliveryTimeRange, deliveryBatch);

        //处理特价限购
        CommodityDetailODTO specialDetail = querySpecialCommodityInfo(cart, orderTime);
        if (Objects.nonNull(specialDetail) && Objects.nonNull(specialDetail.getSpecialPrice())) {
            toBService.handleSpecialLimitWarningTips(pageODTO, storeId, cart.getCommodityId(), cart.getQuantity(),
                    specialDetail.getAvailableTotalLimit(), specialDetail.getAvailableStoreLimit(), specialDetail.getSpecialLimit());
        }

        // 处理库存不足情况
        handleStockShortage(cart, pageODTO, commodityInventoryMap,commodityItemList,storeId, deliveryTimeRange, deliveryBatch);

        /**
         * 如果库存充足，则判断特惠商品限量
         */
        if(commodityType.equals(2) && xdaCommodityTextListIsCanOrder.getIsThPrice().equals(1)){
            BigDecimal subtract = xdaCommodityTextListIsCanOrder.getThLimitNumber().subtract(cart.getQuantity());
            if(subtract.compareTo(BigDecimal.ZERO) == -1){
                pageODTO.setAbleAdd(Boolean.FALSE);
                pageODTO.setStockWarningTips("特惠商品限量，余量为" + xdaCommodityTextListIsCanOrder.getThLimitNumber());
            }
        }
    }

    /**
     * 查询商品特价信息
     */
    private CommodityDetailODTO querySpecialCommodityInfo(XdaShoppingCart cart, Date orderTime) {
        CommodityDetailIDTO req = new CommodityDetailIDTO();
        req.setChannelType(MarketSourceTypeEnum.XDA.getCode());
        req.setShopId(cart.getStoreId());
        req.setOrderTime(orderTime);
        req.setCommodityId(cart.getCommodityId());
        req.setNeedAvailableLimit(1);
        return mtPromotionClient.commodityDetail(req);
    }

    /**
     * 购物车商品 查询大仓库存
     */
    private Map<Long, CommodityInventoryODTO> getCommodityInventoryMap(XdaShoppingCart cart, Integer commodityType, Date orderTime, List<CommodityItemODTO> commodityItemList,
                                                                       Long storeId, String deliveryTimeRange, Integer deliveryBatch) {
        if (CollectionUtils.isEmpty(commodityItemList)) {
            return toBService.queryCommodityInventoryOne(orderTime, cart.getCommodityId(), cart.getQuantity(), commodityType,
                                        storeId, deliveryTimeRange, deliveryBatch);
        } else {
            List<CommodityInventoryDetailIDTO> orderCommodityList = commodityItemList.stream().map(odto -> {
                String commodityItemId = odto.getCommodityItemId();
                BigDecimal commodityNum = odto.getCommodityNum();
                CommodityInventoryDetailIDTO idto = new CommodityInventoryDetailIDTO();
                idto.setCommodityId(Long.valueOf(commodityItemId));
                idto.setQuantity(commodityNum.multiply(cart.getQuantity()));
                idto.setLevel(0);
                return idto;
            }).collect(Collectors.toList());
            List<CommodityInventoryODTO> commodityInventoryList = toBService.queryCommodityInventory(orderTime, orderCommodityList,storeId, deliveryTimeRange, deliveryBatch);
            return commodityInventoryList.stream().collect(Collectors.toMap(CommodityInventoryODTO::getCommodityId, e -> e));
        }
    }


    /**
     * 处理库存不足的情况
     */
    private void handleStockShortage(XdaShoppingCart cart, NotShoppingCartPageV4ODTO pageODTO, Map<Long, CommodityInventoryODTO> commodityInventoryODTOMap, List<CommodityItemODTO> commodityItemList,
                                     Long storeId, String deliveryTimeRange, Integer deliveryBatch) {
        if (CollectionUtils.isEmpty(commodityItemList)) {
            //处理单商品的库存不足
            handleSingleProductShortage(cart, pageODTO, commodityInventoryODTOMap);
        } else {
            //处理组合商品的库存不足
            handleCompositeProductShortage(cart, pageODTO, commodityInventoryODTOMap, commodityItemList, storeId, deliveryTimeRange, deliveryBatch);
        }
    }

    /**
     * 处理单商品的库存不足
     */
    private void handleSingleProductShortage(XdaShoppingCart cart, NotShoppingCartPageV4ODTO pageODTO, Map<Long, CommodityInventoryODTO> commodityInventoryODTOMap) {
        CommodityInventoryODTO inventory = commodityInventoryODTOMap.get(cart.getCommodityId());
        if (!StockType.UNLIMITED.getCode().equals(inventory.getStockType()) && !inventory.getHaveInventory()) {
            List<BStockLackVO> voList = createStockLackVOList(cart, pageODTO.getQuantity(), inventory.getInventoryQuantity(), inventory.getStockType());
            bStockShortService.stockShort(voList);
            pageODTO.setStockWarningTips("库存不足，余量为" + inventory.getInventoryQuantity().stripTrailingZeros().toPlainString());
        }
    }


    /**
     * 处理组合商品的库存不足
     */
    private void handleCompositeProductShortage(XdaShoppingCart cart, NotShoppingCartPageV4ODTO pageODTO, Map<Long, CommodityInventoryODTO> commodityInventoryODTOMap, List<CommodityItemODTO> commodityItemList,
                                                Long storeId, String deliveryTimeRange, Integer deliveryBatch) {
        // 计算组合商品的最小库存
        BigDecimal minStock = toBService.calculateMinStock(commodityItemList, commodityInventoryODTOMap);
        // 判断子商品列表里是否有依据大仓或者限量的
        boolean stockFlag = toBService.checkStockFlag(commodityItemList, commodityInventoryODTOMap);
        if (cart.getQuantity().compareTo(minStock) > 0 && stockFlag) {
            // 查询组合商品库存依据
            List<CommodityInventoryDetailIDTO> idtoList = new ArrayList<>();
            idtoList.add(new CommodityInventoryDetailIDTO(cart.getCommodityId(), cart.getQuantity(), 0));
            List<CommodityInventoryODTO> combCommodityInventoryList = toBService.queryCommodityInventory(new Date(), toBService.mergeOrderItemsByLevel(idtoList),storeId,deliveryTimeRange,deliveryBatch);
            // 构建组合商品库存映射
            Map<Long, CommodityInventoryODTO> combInventoryMap = combCommodityInventoryList.stream().collect(Collectors.toMap(CommodityInventoryODTO::getCommodityId, Function.identity()));
            CommodityInventoryODTO odto = combInventoryMap.get(cart.getCommodityId());
            Integer stockType = Objects.nonNull(odto) ? odto.getStockType() : null;
            // 创建库存不足VO列表
            List<BStockLackVO> voList = createStockLackVOList(cart, pageODTO.getQuantity(), minStock, stockType);
            // 处理库存不足
            bStockShortService.stockShort(voList);
            pageODTO.setStockWarningTips("库存不足");
        }
    }

    /**
     * 构建购物车库存不足消息体
     */
    private List<BStockLackVO> createStockLackVOList(XdaShoppingCart cart, BigDecimal needQuantity, BigDecimal stockQuantity, Integer stockType) {
        List<BStockLackVO> voList = new ArrayList<>();
        BStockLackVO bStockLackVO = new BStockLackVO();
        bStockLackVO.setStoreId(cart.getStoreId());
        bStockLackVO.setCommodityId(cart.getCommodityId());
        bStockLackVO.setStockQuantity(stockQuantity);
        bStockLackVO.setNeedQuantity(needQuantity);
        bStockLackVO.setCreateId(cart.getStoreId());
        bStockLackVO.setOrderType(OrderTypeEnum.XDA_APP_ORDER.getCode());
        if (stockType != null) {
            bStockLackVO.setStockType(stockType);
        }
        voList.add(bStockLackVO);
        return voList;
    }

}
