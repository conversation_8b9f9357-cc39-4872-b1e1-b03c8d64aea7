package com.pinshang.qingyun.order.controller;

import com.alibaba.excel.EasyExcel;
import com.github.pagehelper.PageInfo;
import com.pinshang.qingyun.base.configure.expand.QYAssert;
import com.pinshang.qingyun.base.util.ExcelUtil;
import com.pinshang.qingyun.order.mapper.entry.orderStatistics.OrderCompanyAndStoreTypeEntry;
import com.pinshang.qingyun.order.mapper.entry.orderStatistics.OrderMirrorSyncEntry;
import com.pinshang.qingyun.order.mapper.entry.orderStatistics.OrderStatisticsEntry;
import com.pinshang.qingyun.order.mapper.entry.orderStatistics.OrderSyncEntry;
import com.pinshang.qingyun.order.service.orderStatistics.OrderStatisticsMonitorService;
import com.pinshang.qingyun.order.vo.orderStatistics.OrderStatisticsMonitorVo;
import lombok.extern.slf4j.Slf4j;
import org.junit.rules.Stopwatch;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StopWatch;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;

/**
 * 订单统计查询--订单监控
 */
@RestController
@RequestMapping("/orderStatisticsMonitor")
@Slf4j
public class OrderStatisticsMonitorController {
    @Autowired
    private OrderStatisticsMonitorService orderStatisticsMonitorService;

    /**
     * 订单比较：查询订单统计信息
     * @param vo
     * @return
     */
    @RequestMapping(value = "/queryOrderStatisticsInfo", method = RequestMethod.POST)
    public OrderStatisticsEntry queryOrderInfo(@RequestBody OrderStatisticsMonitorVo vo) {
        return orderStatisticsMonitorService.queryOrderStatisticsInfo(vo);
    }

    /**
     * 差异订单：查询订单编号
     * @param vo
     * @return
     */
    @RequestMapping(value = "/queryOrderDiffList", method = RequestMethod.POST)
    public List<String> queryOrderDiffList(@RequestBody OrderStatisticsMonitorVo vo) {
        return orderStatisticsMonitorService.queryOrderDiffList(vo);
    }

    /**
     * 订单同步：查询订单主表和明细信息
     * @param vo
     * @return
     */
    @RequestMapping(value = "/queryOrderSyncList", method = RequestMethod.POST)
    public PageInfo<OrderSyncEntry> queryOrderSyncList(@RequestBody OrderStatisticsMonitorVo vo) {
        return orderStatisticsMonitorService.queryOrderSyncList(vo);
    }

    /**
     * 订单落地数据补偿：查询订单信息及客户信息
     * @param vo
     * @return
     */
    @RequestMapping(value = "/queryOrderMirrorSyncList", method = RequestMethod.POST)
    public PageInfo<OrderMirrorSyncEntry> queryOrderMirrorSyncList(@RequestBody OrderStatisticsMonitorVo vo) {
        return orderStatisticsMonitorService.queryOrderMirrorSyncList(vo);
    }

    /**
     *　http://192.168.0.213/zentao/story-view-4647.html
     *　按公司+客户类型维度，汇总订货金额
     * 临时功能
     */
    @RequestMapping(value = "/exportSalesStatisticsReport", method = RequestMethod.GET)
    public void orderByCompanyAndStoreTypeReport(@RequestParam(value = "startOrderTime",required = false) String startOrderTime,@RequestParam(value = "endOrderTime",required = false) String endOrderTime, HttpServletResponse httpServletResponse) throws IOException {
        QYAssert.notNull(startOrderTime,"送货日期必选");
        QYAssert.notNull(endOrderTime,"送货日期必选");
        List<OrderCompanyAndStoreTypeEntry> list = orderStatisticsMonitorService.orderByCompanyAndStoreTypeReport(startOrderTime,endOrderTime);
        StopWatch sw = new StopWatch("orderReportByCompanyAndStoreType");
        sw.start();
        String sheetName = "公司与客户类型维护订货报表";
        StringBuilder sb = new StringBuilder().append(sheetName).append("_").append(startOrderTime).append("_").append(endOrderTime);
        try {
            ExcelUtil.setFileNameAndHead(httpServletResponse,sb.toString());
            EasyExcel.write(httpServletResponse.getOutputStream(),OrderCompanyAndStoreTypeEntry.class).autoCloseStream(Boolean.FALSE).sheet(sheetName).doWrite(list);
        } catch (Exception e) {
            log.error(sb.toString(),e);
            ExcelUtil.setExceptionResponse(httpServletResponse);
        }
        sw.stop();
        log.info(sb.append( sw.prettyPrint()).toString() );
    }

}
