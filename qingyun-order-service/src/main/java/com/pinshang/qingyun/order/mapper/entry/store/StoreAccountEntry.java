package com.pinshang.qingyun.order.mapper.entry.store;

import com.pinshang.qingyun.order.enums.AppUserStatusEnum;
import com.pinshang.qingyun.order.enums.StoreOpenStatusEnum;
import lombok.Data;

import java.io.Serializable;

/**
 *
 * <AUTHOR>
 * @Date 2018/4/8 16:22
 */
@Data
public class StoreAccountEntry implements Serializable {

    private static final long serialVersionUID = 6409360255806535064L;
    /**
     * 客户ID
     */
    private Long storeId;
    /**
     * 客户Code
     */
    private String storeCode;
    /**
     * 客户名称
     */
    private String storeName;
    /**
     * 客户状态
     */
    private StoreOpenStatusEnum storeStatus;
    /**
     * 订货时间段
     */
    private String beginTime;
    /**
     * 时间段
     */
    private String endTime;
    /**
     * 订单限制数
     */
    private Integer maxOrderNum;
    /**
     * 送货员
     */
    private String deliverymanName;
    /**
     * 账号状态
     */
    private AppUserStatusEnum openStatus;

}
