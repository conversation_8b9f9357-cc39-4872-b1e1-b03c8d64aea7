package com.pinshang.qingyun.order.controller.pf;

import com.pinshang.qingyun.base.api.PfTokenInfo;
import com.pinshang.qingyun.base.configure.expand.QYAssert;
import com.pinshang.qingyun.base.enums.YesOrNoEnums;
import com.pinshang.qingyun.base.util.FastThreadLocalUtil;
import com.pinshang.qingyun.box.utils.DateUtil;
import com.pinshang.qingyun.order.config.LockConstants;
import com.pinshang.qingyun.order.dto.shopcart.*;
import com.pinshang.qingyun.order.mapper.*;
import com.pinshang.qingyun.order.service.StoreService;
import com.pinshang.qingyun.order.service.pf.BuildPfShoppingCart;
import com.pinshang.qingyun.order.service.pf.PfShoppingCartService;
import com.pinshang.qingyun.pf.product.service.PfCommodityFrontClient;
import io.swagger.annotations.ApiOperation;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/pf/shoppingCart")
public class PfShopcartController {

    @Autowired
    private PfShoppingCartService pfShoppingCartService;
    @Autowired
    private RedissonClient redissonClient;
    
    @Autowired
    private PfShoppingCartMapper pfShoppingCartMapper;
	@Autowired
    private OrderMapper orderMapper;
	@Autowired
    private OrderListMapper orderListMapper;
	@Autowired
    private PfCommodityFrontClient pfCommodityFrontClient;
	@Autowired
    private StoreService storeService;
	@Autowired
    private StoreDurationMapper storeDurationMapper;

	
    @PostMapping("/add")
    @ApiOperation(value = "购物车--加")
    public ShoppingCartODTO addShopCart(@RequestBody ShoppingCartAddIDTO idto){
        PfTokenInfo pfTokenInfo = FastThreadLocalUtil.getPF();
        Long storeId = pfTokenInfo.getStoreId();
        QYAssert.isTrue(!pfTokenInfo.getIsTouristStore(), "游客无法下单。");
        QYAssert.isTrue(storeId != null, "未能获取到用户信息,请重新登录!");
        idto.setStoreId(storeId);
        String lockKey = LockConstants.generateShoppingCartAddLockKey(storeId);
        RLock lock = redissonClient.getLock(lockKey);
        if(lock.tryLock()){
            try {
                BuildPfShoppingCart buildPfShoppingCart = new BuildPfShoppingCart(storeId, idto.getOrderTime(), pfShoppingCartMapper, orderMapper, orderListMapper, pfCommodityFrontClient, storeService, storeDurationMapper);
                NotShoppingCartPageODTO odto = pfShoppingCartService.addShopCart(idto, buildPfShoppingCart);
                if (YesOrNoEnums.YES.equals(idto.getShopCartPage()) ){
                    /**
                     * 刷新购物车
                     */
                    ShoppingCartODTO shoppingCartODTO = buildPfShoppingCart.shopCartList();
                    if(null != odto.getStockWarningTips()){
                        List<ShoppingCartCommodityODTO> commodities = shoppingCartODTO.getNormalGroup().getCommodities();
                        commodities.forEach(item->{
                            if(item.getCommodityId().equals(odto.getCommodityId())){
                                item.setStockWarningTips(odto.getStockWarningTips());
                            }
                        });
                    }
                    return shoppingCartODTO;
                }
                ShoppingCartODTO shoppingCartODTO = new ShoppingCartODTO();
                shoppingCartODTO.setVarietySum(pfShoppingCartService.getValidCommoditySum(storeId, idto.getOrderTime(), buildPfShoppingCart));
                shoppingCartODTO.setNotShoppingCartPageODTO(odto);
                return shoppingCartODTO;
            } finally {
                lock.unlock();
            }
        }else {
            QYAssert.isFalse("系统繁忙,请勿频繁操作!");
        }
       return null;

    }
    @PostMapping("/minus")
    @ApiOperation(value = "购物车--减")
    public ShoppingCartODTO minusShopCart(@RequestBody ShoppingCartMinusIDTO idto){
        PfTokenInfo pfTokenInfo = FastThreadLocalUtil.getPF();
        Long storeId = pfTokenInfo.getStoreId();
        QYAssert.isTrue(!pfTokenInfo.getIsTouristStore(), "游客无法下单。");
        QYAssert.isTrue(storeId != null, "未能获取到用户信息,请重新登录!");
        idto.setStoreId(storeId);
        String lockKey = LockConstants.generateShoppingCartAddLockKey(storeId);
        RLock lock = redissonClient.getLock(lockKey);
        if(lock.tryLock()){
            try {

            	BuildPfShoppingCart buildPfShoppingCart = new BuildPfShoppingCart(storeId, idto.getOrderTime(), pfShoppingCartMapper, orderMapper, orderListMapper, pfCommodityFrontClient, storeService, storeDurationMapper);
                NotShoppingCartPageODTO odto = pfShoppingCartService.minus(idto, buildPfShoppingCart);
                if (YesOrNoEnums.YES.equals(idto.getShopCartPage()) ){
                    /**
                     * 刷新购物车
                     */
                    ShoppingCartODTO shoppingCartODTO = buildPfShoppingCart.shopCartList();
                    if(null != odto.getStockWarningTips()){
                        List<ShoppingCartCommodityODTO> commodities = shoppingCartODTO.getNormalGroup().getCommodities();
                        commodities.forEach(item->{
                            if(item.getCommodityId().equals(odto.getCommodityId())){
                                item.setStockWarningTips(odto.getStockWarningTips());
                            }
                        });
                    }
                    return shoppingCartODTO;
                }
                ShoppingCartODTO shoppingCartODTO = new ShoppingCartODTO();
                shoppingCartODTO.setVarietySum(pfShoppingCartService.getValidCommoditySum(storeId, idto.getOrderTime(), buildPfShoppingCart));
                /**
                 * 非购物车页访问购物车接口时返回 当前商品购物车的数量
                 */
                shoppingCartODTO.setNotShoppingCartPageODTO(odto);
                return shoppingCartODTO;
            } finally {
                lock.unlock();
            }
        }else {
            QYAssert.isFalse("系统繁忙,请勿频繁操作!");
        }

        return null;
    }
    @PostMapping("/setNum")
    @ApiOperation(value = "购物车--设置数量")
    public ShoppingCartODTO setNumShopCart(@RequestBody ShoppingCartSetNumIDTO idto){
        PfTokenInfo pfTokenInfo = FastThreadLocalUtil.getPF();
        Long storeId = pfTokenInfo.getStoreId();
        QYAssert.isTrue(!pfTokenInfo.getIsTouristStore(), "游客无法下单。");
        QYAssert.isTrue(storeId != null, "未能获取到用户信息,请重新登录!");
        idto.setStoreId(storeId);
        BuildPfShoppingCart buildXdaShoppingCart = new BuildPfShoppingCart(storeId, idto.getOrderTime(), pfShoppingCartMapper, orderMapper, orderListMapper, pfCommodityFrontClient, storeService, storeDurationMapper);
        NotShoppingCartPageODTO odto = pfShoppingCartService.setNum(idto, buildXdaShoppingCart);
        if (YesOrNoEnums.YES.equals(idto.getShopCartPage()) ){
            ShoppingCartODTO shoppingCartODTO = buildXdaShoppingCart.shopCartList();
            if(null != odto.getStockWarningTips()){
                List<ShoppingCartCommodityODTO> commodities = shoppingCartODTO.getNormalGroup().getCommodities();
                commodities.forEach(item->{
                    if(item.getCommodityId().equals(odto.getCommodityId())){
                        item.setStockWarningTips(odto.getStockWarningTips());
                    }
                });
            }
            return shoppingCartODTO;
        }
        ShoppingCartODTO shoppingCartODTO = new ShoppingCartODTO();
        shoppingCartODTO.setVarietySum(pfShoppingCartService.getValidCommoditySum(storeId, idto.getOrderTime(), buildXdaShoppingCart));
        shoppingCartODTO.setNotShoppingCartPageODTO(odto);
        return shoppingCartODTO;
    }
    @PostMapping("/clear")
    @ApiOperation(value = "购物车--清空")
    public ShoppingCartODTO clear(@RequestBody ShoppingCartClearIDTO idto){
        PfTokenInfo pfTokenInfo = FastThreadLocalUtil.getPF();
        Long storeId = pfTokenInfo.getStoreId();
        QYAssert.isTrue(!pfTokenInfo.getIsTouristStore(), "游客无法下单。");
        QYAssert.isTrue(storeId != null, "未能获取到用户信息,请重新登录!");
        pfShoppingCartService.clear(storeId, idto.getCommodityIds());
        BuildPfShoppingCart buildPfShoppingCart = new BuildPfShoppingCart(storeId, idto.getOrderTime(), pfShoppingCartMapper, orderMapper, orderListMapper, pfCommodityFrontClient, storeService, storeDurationMapper);
        return buildPfShoppingCart.shopCartList();
    }
    @GetMapping("/refresh")
    @ApiOperation(value = "购物车--刷新")
    public ShoppingCartODTO refresh(@RequestParam(value = "orderDate",required = false) String orderDate){
        PfTokenInfo pfTokenInfo = FastThreadLocalUtil.getPF();
        Long storeId = pfTokenInfo.getStoreId();
        QYAssert.isTrue(!pfTokenInfo.getIsTouristStore(), "游客无法下单。");
        QYAssert.isTrue(storeId != null, "未能获取到用户信息,请重新登录!");
        String lockKey = LockConstants.generateShoppingCartAddLockKey(storeId);
        RLock lock = redissonClient.getLock(lockKey);
        if(lock.tryLock()){
            try {
            	BuildPfShoppingCart buildPfShoppingCart = new BuildPfShoppingCart(storeId, DateUtil.parseDate(orderDate, "yyyy-MM-dd"), pfShoppingCartMapper, orderMapper, orderListMapper, pfCommodityFrontClient, storeService, storeDurationMapper);
                return buildPfShoppingCart.shopCartList();
            } finally {
                lock.unlock();
            }
        }else {
            QYAssert.isFalse("系统繁忙,请勿频繁操作!");
        }
        return null;
    }
    @GetMapping("/clear/invalid")
    @ApiOperation(value = "购物车--清空失效商品")
    public ShoppingCartODTO clearInvalidCommodity(@RequestParam(value = "orderDate",required = false) String orderDate){

        return pfShoppingCartService.clearInvalidCommodity(DateUtil.parseDate(orderDate, "yyyy-MM-dd"));
    }
    @GetMapping("/category")
    @ApiOperation(value = "购物车--品类数量")
    public Integer getCategoryNum(@RequestParam(value = "orderDate",required = false) String orderDate, @RequestParam(value = "storeId",required = false) Long storeId){
        BuildPfShoppingCart buildPfShoppingCart =  new BuildPfShoppingCart(storeId, DateUtil.parseDate(orderDate, "yyyy-MM-dd"), pfShoppingCartMapper, orderMapper, orderListMapper, pfCommodityFrontClient, storeService, storeDurationMapper);
        return pfShoppingCartService.getValidCommoditySum(storeId, DateUtil.parseDate(orderDate, "yyyy-MM-dd"), buildPfShoppingCart);
    }

}
