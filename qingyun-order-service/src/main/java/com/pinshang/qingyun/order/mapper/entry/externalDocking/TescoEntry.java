package com.pinshang.qingyun.order.mapper.entry.externalDocking;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class TescoEntry {

    private Long id;

    private String commodityCode;

    @ApiModelProperty(value = "产品名称")
    private String rtCommodityName;

    @ApiModelProperty(value = "产品代码")
    private String rtCommodityCode;

    @ApiModelProperty(value = "采购数量")
    private String commodityNum;

    @ApiModelProperty(value = "数量单位")
    private String unit;

    @ApiModelProperty(value = "进货日期")
    private String orderTime;

    @ApiModelProperty(value = "供货单位名称")
    private String company;

    @ApiModelProperty(value = "供货单位地址")
    private String address;

    @ApiModelProperty(value = "供货单位联系方式")
    private String rtcAdddress;

    @ApiModelProperty(value = "摊位号")
    private String rtShopCode;

    @ApiModelProperty(value = "经营者名称")
    private String rtShopName;

    @ApiModelProperty(value = "生产厂商")
    private String madeCompany;

    @ApiModelProperty(value = "生产日期")
    private String deliveryDate;

    @ApiModelProperty(value = "生产批次")
    private String deliveryDatePc;

    @ApiModelProperty(value = "产地证明编号")
    private String a1;

    @ApiModelProperty(value = "检验检疫证书编号")
    private String a2;

    @ApiModelProperty(value = "质量安全检测")
    private String a3;

    @ApiModelProperty(value = "产地")
    private String a4;

    private String email;
}
