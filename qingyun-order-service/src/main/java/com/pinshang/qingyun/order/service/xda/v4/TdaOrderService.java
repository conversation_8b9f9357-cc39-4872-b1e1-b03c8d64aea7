package com.pinshang.qingyun.order.service.xda.v4;

import com.pinshang.qingyun.base.configure.expand.QYAssert;
import com.pinshang.qingyun.base.enums.DeliveryOrderTypeEnums;
import com.pinshang.qingyun.box.utils.BeanCloneUtils;
import com.pinshang.qingyun.infrastructure.components.IMqSenderComponent;
import com.pinshang.qingyun.infrastructure.components.params.mq.MqMessage;
import com.pinshang.qingyun.kafka.base.KafkaTopicConstant;
import com.pinshang.qingyun.kafka.enums.KafkaMessageOperationTypeEnum;
import com.pinshang.qingyun.kafka.enums.KafkaMessageTypeEnum;
import com.pinshang.qingyun.order.dto.xda.TdaOrderSyncTmsIDTO;
import com.pinshang.qingyun.order.dto.xda.v4.TdaDeliveryTimeRangeODTO;
import com.pinshang.qingyun.order.mapper.ShopMapper;
import com.pinshang.qingyun.order.mapper.entry.StoreEntry;
import com.pinshang.qingyun.tms.dto.logisticscenterbatch.LogisticsBatchStoreInfoODTO;
import com.pinshang.qingyun.tms.dto.logisticscenterbatch.LogisticsBatchStoreSearchIDTO;
import com.pinshang.qingyun.tms.service.LogisticsCenterBatchClient;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Author: sk
 * @Date: 2024/5/11
 */
@Slf4j
@Service
public class TdaOrderService {

    @Autowired
    private LogisticsCenterBatchClient logisticsCenterBatchClient;
    @Autowired
    private ShopMapper shopMapper;
    @Autowired
    private IMqSenderComponent mqSenderComponent;
    private static String T_DA_DELIVERY_TIME_RANGE = "tda:timeRange";
    @Value("${application.name.switch}")
    private String applicationNameSwitch;
    @Autowired
    private RedissonClient redissonClient;

    /**
     * 根据storeId 查询送货日期时间段列表
     * @param storeId
     * @return
     */
    public List<TdaDeliveryTimeRangeODTO> queryDeliveryTimeRangeListByStoreId(Long storeId){
        List<LogisticsBatchStoreInfoODTO> logisticsBatchStoreInfoODTOList;
        /*RBucket<List<LogisticsBatchStoreInfoODTO>> bucket = redissonClient.getBucket(T_DA_DELIVERY_TIME_RANGE + storeId);
        List<LogisticsBatchStoreInfoODTO> list = bucket.get();
        if(CollectionUtils.isNotEmpty(list)){
            logisticsBatchStoreInfoODTOList = list;
        }else {
            LogisticsBatchStoreSearchIDTO searchIDTO = new LogisticsBatchStoreSearchIDTO();
            searchIDTO.setStoreId(storeId);
            logisticsBatchStoreInfoODTOList = logisticsCenterBatchClient.findLogisticsBatchStoreInfoByParams(searchIDTO);
            bucket.set(logisticsBatchStoreInfoODTOList, 5L, TimeUnit.MINUTES);
        }*/
        LogisticsBatchStoreSearchIDTO searchIDTO = new LogisticsBatchStoreSearchIDTO();
        searchIDTO.setStoreId(storeId);
        logisticsBatchStoreInfoODTOList = logisticsCenterBatchClient.findLogisticsBatchStoreInfoByParams(searchIDTO);

        List<TdaDeliveryTimeRangeODTO> tdaDeliveryTimeRangeODTOList = new ArrayList<>();
        if(CollectionUtils.isNotEmpty(logisticsBatchStoreInfoODTOList)){
            for(LogisticsBatchStoreInfoODTO logisticsBatchStoreInfoODTO : logisticsBatchStoreInfoODTOList){
                logisticsBatchStoreInfoODTO.getDeliveryTimeMap().forEach((k,v)->{
                    TdaDeliveryTimeRangeODTO timeRangeODTO = BeanCloneUtils.copyTo(logisticsBatchStoreInfoODTO, TdaDeliveryTimeRangeODTO.class);
                    timeRangeODTO.setDeliveryTimeRange(k);
                    timeRangeODTO.setDisabled(v);
                    tdaDeliveryTimeRangeODTOList.add(timeRangeODTO);
                });
            }
        }
        return tdaDeliveryTimeRangeODTOList;
    }

    /**
     * 根据storeId 查询送货日期时间段列表
     * @param storeId
     * @return
     */
    public List<TdaDeliveryTimeRangeODTO> selectTdaKfDeliveryTimeRangeList(String orderTime,Long storeId){
        List<LogisticsBatchStoreInfoODTO> logisticsBatchStoreInfoODTOList;

        LogisticsBatchStoreSearchIDTO searchIDTO = new LogisticsBatchStoreSearchIDTO();
        searchIDTO.setStoreId(storeId);
        logisticsBatchStoreInfoODTOList = logisticsCenterBatchClient.findLogisticsBatchStoreInfoByParams(searchIDTO);

        List<TdaDeliveryTimeRangeODTO> tdaDeliveryTimeRangeODTOList = new ArrayList<>();
        if(CollectionUtils.isNotEmpty(logisticsBatchStoreInfoODTOList)){
            SimpleDateFormat dateFormat = new SimpleDateFormat("HHmm");
            Integer currentTime = Integer.valueOf(dateFormat.format(new Date()));

            logisticsBatchStoreInfoODTOList.forEach(item->{
                Integer flag;
                Integer replace = Integer.valueOf(item.getKfEndTime().replace(":",""));
                if(currentTime < replace){
                    flag = 0;
                } else {
                    flag = 1;
                }

                Map<String, Integer> deliveryTimeMap = item.getDeliveryTimeMap();
                deliveryTimeMap.forEach((k,v)->{
                    deliveryTimeMap.put(k,flag);
                });
            });

            for(LogisticsBatchStoreInfoODTO logisticsBatchStoreInfoODTO : logisticsBatchStoreInfoODTOList){
                logisticsBatchStoreInfoODTO.getDeliveryTimeMap().forEach((k,v)->{
                    TdaDeliveryTimeRangeODTO timeRangeODTO = BeanCloneUtils.copyTo(logisticsBatchStoreInfoODTO, TdaDeliveryTimeRangeODTO.class);
                    timeRangeODTO.setDeliveryTimeRange(k);
                    timeRangeODTO.setDisabled(v);
                    tdaDeliveryTimeRangeODTOList.add(timeRangeODTO);
                });
            }
        }
        return tdaDeliveryTimeRangeODTOList;
    }
    /**
     * 根据storeId 和批次 查询送货日期时间段列表
     * @return 同一配送批次配置
     */
    public List<TdaDeliveryTimeRangeODTO> queryOneDeliveryTimeRangeList(Long storeId, Integer businessType, Long logisticsCenterId, Integer deliveryBatch) {
        List<TdaDeliveryTimeRangeODTO> tdaDeliveryTimeRangeODTOList = new ArrayList<>();
        LogisticsBatchStoreSearchIDTO searchIDTO = new LogisticsBatchStoreSearchIDTO();
        searchIDTO.setStoreId(storeId);
        searchIDTO.setBusinessType(businessType);
        searchIDTO.setLogisticsCenterId(logisticsCenterId);
        searchIDTO.setDeliveryBatch(deliveryBatch);
        List<LogisticsBatchStoreInfoODTO> logisticsBatchStoreInfoODTOList = logisticsCenterBatchClient.findLogisticsBatchStoreInfoByParams(searchIDTO);
        if (CollectionUtils.isNotEmpty(logisticsBatchStoreInfoODTOList)) {
            LogisticsBatchStoreInfoODTO logisticsBatchStoreInfoODTO = logisticsBatchStoreInfoODTOList.get(0);
            logisticsBatchStoreInfoODTO.getDeliveryTimeMap().forEach((k, v) -> {
                TdaDeliveryTimeRangeODTO timeRangeODTO = BeanCloneUtils.copyTo(logisticsBatchStoreInfoODTO, TdaDeliveryTimeRangeODTO.class);
                timeRangeODTO.setDeliveryTimeRange(k);
                timeRangeODTO.setDisabled(v);
                tdaDeliveryTimeRangeODTOList.add(timeRangeODTO);
            });

        }
        return tdaDeliveryTimeRangeODTOList;
    }


    /**
     * 判断客户是否通达的业务类型
     * @param storeId
     * @return
     */
    public Boolean isTdaStore(Long storeId){
        List<StoreEntry> storeEntries = shopMapper.selectStoreList(Collections.singletonList(storeId));
        StoreEntry storeEntry = storeEntries.get(0);
        return DeliveryOrderTypeEnums.TD_SALE.getCode().equals(storeEntry.getBusinessType());
    }

    /**
     * 通达订单取消订单，判断截单时间
     * @param storeId
     * @param deliveryTimeRange
     * @param deliveryBatch
     */
    public void cancelTdaOrderCheckDeliveryTimeRange(Long storeId,String deliveryTimeRange, Integer deliveryBatch){
        // 判断用户截单时间
        List<TdaDeliveryTimeRangeODTO> tdaDeliveryTimeRangeODTOList = queryDeliveryTimeRangeListByStoreId(storeId);
        List<TdaDeliveryTimeRangeODTO> tdaDeliveryTimeRangeODTOS = tdaDeliveryTimeRangeODTOList.stream().filter(p ->
                deliveryBatch.equals(p.getDeliveryBatch())  &&  deliveryTimeRange.equals(p.getDeliveryTimeRange())
        ).collect(Collectors.toList());

        if(CollectionUtils.isEmpty(tdaDeliveryTimeRangeODTOS)){
            QYAssert.isFalse("批次信息异常");
        }

        SimpleDateFormat dateFormat= new SimpleDateFormat("HHmm");
        Integer currentTime = Integer.valueOf(dateFormat.format(new Date()));
        Integer replace = Integer.valueOf(tdaDeliveryTimeRangeODTOS.get(0).getStoreEndTime().replace(":", ""));
        if(currentTime > replace){
            QYAssert.isFalse("已过截单时间,无法取消订单");
        }

    }

    /**
     * 加减购物车，去结算、结算页面校验送货日期时间段必选
     * @param storeId
     */
    public TdaDeliveryTimeRangeODTO checkDeliveryTimeRange(Long storeId,String deliveryTimeRange, Integer deliveryBatch, Boolean check){
        if(storeId == null){
            return null;
        }

        // 搜索那块特惠商品调用，查询普通商品组的商品金额
        if(!check && StringUtils.isBlank(deliveryTimeRange)){
            return null;
        }
        // 如果业务类型是通达销售，则送货时间段必选
        if(isTdaStore(storeId)){

            QYAssert.isTrue(StringUtils.isNotBlank(deliveryTimeRange), "送货时间段不能为空");
            QYAssert.isTrue(deliveryBatch != null, "配送批次不能为空");
            // 判断用户截单时间
            List<TdaDeliveryTimeRangeODTO> tdaDeliveryTimeRangeODTOList = queryDeliveryTimeRangeListByStoreId(storeId);
            List<TdaDeliveryTimeRangeODTO> tdaDeliveryTimeRangeODTOS = tdaDeliveryTimeRangeODTOList.stream().filter(p ->
                                        deliveryBatch.equals(p.getDeliveryBatch())  &&  deliveryTimeRange.equals(p.getDeliveryTimeRange())
                                        ).collect(Collectors.toList());
            if(CollectionUtils.isEmpty(tdaDeliveryTimeRangeODTOS)){
                QYAssert.isFalse("无配送批次,无法下单");
            }

            SimpleDateFormat dateFormat= new SimpleDateFormat("HHmm");
            Integer currentTime = Integer.valueOf(dateFormat.format(new Date()));

            Integer storeEndTime = Integer.valueOf(tdaDeliveryTimeRangeODTOS.get(0).getStoreEndTime().replace(":", ""));
            Integer storeBeginTime = Integer.valueOf(tdaDeliveryTimeRangeODTOS.get(0).getStoreBeginTime().replace(":", ""));
            if(currentTime > storeEndTime){
                QYAssert.isFalse("已过下单时间，请重新选择配送时间");
            }

            if(currentTime < storeBeginTime){
                QYAssert.isFalse("未到下单时间，请重新选择配送时间");
            }

            return tdaDeliveryTimeRangeODTOS.get(0);
        }

        return null;
    }


    /**
     * 订单系统 -> 物流系统
     *
     * 退货单审核通过 【单据ID refOrderId，单据类型refType=21，状态status：2-待取货】
     * 	大仓确认		【单据ID refOrderId，单据类型refType=21，状态status：6-已完成】
     *
     * 	订单创建		【单据ID refOrderId，单据类型refType=11，状态status：创建|取消】
     * 	订单取消		【单据ID refOrderId，单据类型refType=11，状态status：创建|取消】
     * @param tdaOrderSyncTmsIDTO
     */
    public void tdaOrderSyncToTms(TdaOrderSyncTmsIDTO tdaOrderSyncTmsIDTO){
        mqSenderComponent.send(applicationNameSwitch + KafkaTopicConstant.TDA_ORDER_SYNC_LOGISTICS_TOPIC,
                tdaOrderSyncTmsIDTO,
                MqMessage.MQ_KAFKA,
                KafkaMessageTypeEnum.TDA_ORDER_SYNC_LOGISTICS_TOPIC.name(),
                KafkaMessageOperationTypeEnum.UPDATE.name());
    }


    public TdaDeliveryTimeRangeODTO checkCupDeliveryTimeRange(Long storeId,String deliveryTimeRange, Integer deliveryBatch) {
        if(storeId == null){
            return null;
        }
        // 如果业务类型是通达销售，则送货时间段必选
        if(isTdaStore(storeId)){

            QYAssert.isTrue(StringUtils.isNotBlank(deliveryTimeRange), "送货时间段不能为空");
            QYAssert.isTrue(deliveryBatch != null, "配送批次不能为空");
            // 判断用户截单时间
            List<TdaDeliveryTimeRangeODTO> tdaDeliveryTimeRangeODTOList = queryCupDeliveryTimeRangeListByStoreId(storeId);
            List<TdaDeliveryTimeRangeODTO> tdaDeliveryTimeRangeODTOS = tdaDeliveryTimeRangeODTOList.stream().filter(p ->
                    deliveryBatch.equals(p.getDeliveryBatch())  &&  deliveryTimeRange.equals(p.getDeliveryTimeRange())
            ).collect(Collectors.toList());
            if(CollectionUtils.isEmpty(tdaDeliveryTimeRangeODTOS)){
            	QYAssert.isTrue(false,"批次信息异常,请重新选择送货时间段");
            }

            String kfEndTime =  tdaDeliveryTimeRangeODTOS.get(0).getKfEndTime();
            SimpleDateFormat dateFormat= new SimpleDateFormat("HHmm");
            Integer currentTime = Integer.valueOf(dateFormat.format(new Date()));
            Integer replace = Integer.valueOf(kfEndTime.replace(":", ""));
            if(currentTime > replace){
            	QYAssert.isTrue(false,"已过客服截单时间，请重新选择配送时间");
            }

            return tdaDeliveryTimeRangeODTOS.get(0);
        }

        return null;
    }

    private List<TdaDeliveryTimeRangeODTO> queryCupDeliveryTimeRangeListByStoreId(Long storeId) {

        List<LogisticsBatchStoreInfoODTO> logisticsBatchStoreInfoODTOList;
        LogisticsBatchStoreSearchIDTO searchIDTO = new LogisticsBatchStoreSearchIDTO();
        searchIDTO.setStoreId(storeId);
        logisticsBatchStoreInfoODTOList = logisticsCenterBatchClient.findLogisticsBatchStoreInfoByParams(searchIDTO);

        List<TdaDeliveryTimeRangeODTO> tdaDeliveryTimeRangeODTOList = new ArrayList<>();
        if(CollectionUtils.isNotEmpty(logisticsBatchStoreInfoODTOList)){

            //将物流返回的根据客户id查询物流中心，配送批次，截单时间，客户可选送货时间段接口按照客服截单时间重新设置送货时间段是否可选
            //获取当前时间点
            SimpleDateFormat dateFormat= new SimpleDateFormat("HHmm");
            Integer currentTime = Integer.valueOf(dateFormat.format(new Date()));

            logisticsBatchStoreInfoODTOList.forEach(item -> {
                //判断当前时间是否在可下单时间范围
                Integer flag = 1;
                Integer replace = Integer.valueOf(item.getKfEndTime().replace(":", ""));
                if(currentTime < replace){
                    flag = 0;
                }
                Map<String, Integer> deliveryTimeMap = item.getDeliveryTimeMap();
                Integer finalFlag = flag;
                deliveryTimeMap.forEach((k, v)->{
                    deliveryTimeMap.put(k, finalFlag);
                });
            });

            for(LogisticsBatchStoreInfoODTO logisticsBatchStoreInfoODTO : logisticsBatchStoreInfoODTOList){
                logisticsBatchStoreInfoODTO.getDeliveryTimeMap().forEach((k,v)->{
                    TdaDeliveryTimeRangeODTO timeRangeODTO = BeanCloneUtils.copyTo(logisticsBatchStoreInfoODTO, TdaDeliveryTimeRangeODTO.class);
                    timeRangeODTO.setDeliveryTimeRange(k);
                    timeRangeODTO.setDisabled(v);
                    tdaDeliveryTimeRangeODTOList.add(timeRangeODTO);
                });
            }
        }


        return tdaDeliveryTimeRangeODTOList;
    }

}
