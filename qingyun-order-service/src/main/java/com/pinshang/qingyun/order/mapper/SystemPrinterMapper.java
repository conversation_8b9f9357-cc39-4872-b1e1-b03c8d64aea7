package com.pinshang.qingyun.order.mapper;

import com.pinshang.qingyun.order.mapper.entry.EmployeePrinterEntry;
import com.pinshang.qingyun.order.mapper.entry.StorePrinterEntry;
import com.pinshang.qingyun.order.mapper.entry.SystemPrinterEntry;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2017/6/22.
 */
@Mapper
@Repository
public interface SystemPrinterMapper{

   List<SystemPrinterEntry> querySystemPrinterByUserId(@Param("userId") Long userId);

   EmployeePrinterEntry queryEmployeePrinterByDeliveryManCode(@Param("employeeCode") String employeeCode);

   List<StorePrinterEntry> queryStorePrinterByStoreCode(@Param("storeCode") String storeCode);
}
