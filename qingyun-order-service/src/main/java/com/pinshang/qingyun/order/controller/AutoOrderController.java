package com.pinshang.qingyun.order.controller;

import com.pinshang.qingyun.order.dto.AutoSaveOrderIDTO;
import com.pinshang.qingyun.order.service.auto.AutoOrderService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * @Author: sk
 * @Date: 2024/12/4
 */
@Slf4j
@RequestMapping("/auto")
@RestController
public class AutoOrderController {

    @Autowired
    private AutoOrderService autoOrderService;


    /**
     * 自动订货保存订单
     * @param saveOrderIDTO
     * @return
     */
    @PostMapping("/saveAutoOrder")
    public Boolean saveAutoOrder(@RequestBody AutoSaveOrderIDTO saveOrderIDTO) {
        return autoOrderService.saveAutoOrder(saveOrderIDTO);
    }
}
