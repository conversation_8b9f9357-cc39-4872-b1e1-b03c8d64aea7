package com.pinshang.qingyun.order.service;


import com.pinshang.qingyun.base.configure.expand.QYAssert;
import com.pinshang.qingyun.box.utils.SpringUtil;
import com.pinshang.qingyun.order.mapper.StoreSettlementMapper;
import com.pinshang.qingyun.order.model.order.StoreSettlement;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.entity.Example;

import java.math.BigDecimal;
import java.util.List;
import java.util.Optional;

@Slf4j
@Service
public class StoreSettlementService {

    @Autowired
    private StoreSettlementMapper storeSettlementMapper;

    @Transactional(rollbackFor = Exception.class)
    public boolean addCollectPrice(Long storeId,BigDecimal amount) {

        StoreSettlement storeSettlement = this.findByStoreId(storeId);
        if (storeSettlement.getCollectStatus()) {
            if (null == storeSettlement.getCollectPrice()) {
                storeSettlement.setCollectPrice(0.0);
            }
            storeSettlement.setCollectPrice(BigDecimal.valueOf(storeSettlement.getCollectPrice()).add(amount).doubleValue());
            int rowNum = this.storeSettlementMapper.updateByPrimaryKeySelective(storeSettlement);
            return rowNum > 0 ? true : false;
        } else {
            return true;
        }
    }

    public Boolean selectCollectStatus(Long storeId){
        QYAssert.isTrue(storeId !=null,"客户id不能为空");
        return Optional.ofNullable(
                storeSettlementMapper.getStoreSettlementByStoreId(storeId))
                .map(StoreSettlement::getCollectStatus)
                .orElse(null);
    }


    public StoreSettlement findByStoreId(Long storeId){
        Example ex = new Example(StoreSettlement.class);
        ex.createCriteria().andEqualTo("storeId", storeId);
        List<StoreSettlement> ssList = this.storeSettlementMapper.selectByExample(ex);

        if(SpringUtil.isEmpty(ssList)){
            QYAssert.isFalse("客户不存在");
        }
        return ssList.get(0);
    }

    /**
     * 根据产品价格方案获取相关门店结算信息
     *
     * @param productPriceModelId 价格方案ID
     */
    public List<StoreSettlement> getStoreSettlementsByProductPriceModelId(Long productPriceModelId) {
        StoreSettlement query = new StoreSettlement();
        query.setProductPriceModelId(productPriceModelId);
        return storeSettlementMapper.select(query);
    }

    public StoreSettlement getStoreSettlementByStoreId(Long storeId) {
        return storeSettlementMapper.getStoreSettlementByStoreId(storeId);
    }

}
