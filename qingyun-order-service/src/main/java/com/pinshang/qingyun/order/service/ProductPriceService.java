package com.pinshang.qingyun.order.service;

import com.pinshang.qingyun.box.utils.SpringUtil;
import com.pinshang.qingyun.order.dto.cup.ProductPriceDto;
import com.pinshang.qingyun.order.mapper.ProductPriceModelListMapper;
import com.pinshang.qingyun.order.model.promotion.Promotion;
import com.pinshang.qingyun.order.model.promotion.PromotionProduct;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;

/**
 * @Author: sk
 * @Date: 2024/7/18
 */
@Slf4j
@Service
public class ProductPriceService {

    @Autowired
    PromotionService promotionService;

    @Autowired
    PromotionProductService promotionProductService;


    public void processProductPrice(List<ProductPriceDto> ppList, String storeId, String orderTime) {

        if (SpringUtil.isNotEmpty(ppList) && StringUtils.isNotBlank(storeId) && StringUtils.isNotBlank(orderTime)) {

            List<Promotion> promotionList = promotionService.findCommodityPromotionByStoreId(Long.valueOf(storeId),orderTime);

            Promotion promotion = null;
            if (SpringUtil.isNotEmpty(promotionList)) { //商品特价方案只取一个
                promotion = promotionList.get(0);
            }
            if (null != promotion) {

                List<PromotionProduct> promotionProductList = promotionProductService.findListByPromotionId(promotion.getId());
                dealPromotionProductList(promotionList, promotionProductList);

                if (SpringUtil.isNotEmpty(promotionProductList)) {

                    promotionProductList.forEach(p -> {
                        ppList.forEach(c -> {
                            if (c.getProductCode().equals(p.getProductCode())) {
                                c.setPrice(BigDecimal.valueOf(p.getPrice()));
                                c.setLimitNum(BigDecimal.valueOf(p.getLimitNumber()));
                            }
                        });
                    });
                }
            }
        }
    }

    private void dealPromotionProductList(List<Promotion> promotionList, List<PromotionProduct> promotionProductList) {
        Promotion promotion = null;
        if (promotionList.size() > 1) {
            for (int i = 1; i < promotionList.size(); i++) {
                promotion = promotionList.get(i);
                List<PromotionProduct> tempPromotionProductList = promotionProductService.findListByPromotionId(promotion.getId());
                if (SpringUtil.isNotEmpty(tempPromotionProductList)) {
                    tempPromotionProductList.removeAll(promotionProductList);
                    promotionProductList.addAll(tempPromotionProductList);
                }
            }
        }
    }


}
