package com.pinshang.qingyun.order.controller.cup;

import com.pinshang.qingyun.base.api.TokenInfo;
import com.pinshang.qingyun.base.configure.expand.QYAssert;
import com.pinshang.qingyun.base.util.FastThreadLocalUtil;
import com.pinshang.qingyun.order.config.LockConstants;
import com.pinshang.qingyun.order.dto.cup.JsonMsgBean;
import com.pinshang.qingyun.order.dto.cup.OrderRequestDto;
import com.pinshang.qingyun.order.service.cup.OrderCupImportService;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.ss.usermodel.WorkbookFactory;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.InputStream;
import java.util.Arrays;

/**
 * @Author: sk
 * @Date: 2024/7/16
 */
@RestController
@RequestMapping("/orderCupImport")
@Slf4j
public class OrderCupImportController {

    @Autowired
    private OrderCupImportService orderCupImportService;
    @Autowired
    private RedissonClient redissonClient;

    /**
     *订单导入 按条码
     * @throws Exception
     */
    @ApiOperation(value = "订单导入 按条码", notes = "订单导入 按条码")
    @RequestMapping(value = "/importOrderBarCodeExcel", method = RequestMethod.POST)
    public JsonMsgBean importOrderBarCodeExcel(@RequestParam(value = "file", required = true) MultipartFile file) throws Exception{
        InputStream in = file.getInputStream();
        Workbook wb = WorkbookFactory.create(in);
        return   orderCupImportService.importOrder(wb, 1);

    }


    /**
     *订单导入 按商品编码
     * @throws Exception
     */
    @ApiOperation(value = "订单导入 按商品编码", notes = "订单导入 按商品编码")
    @RequestMapping(value = "/importOrderCommodityCodeExcel", method = RequestMethod.POST)
    public JsonMsgBean importOrderCommodityCodeExcel(@RequestParam(value = "file", required = true) MultipartFile file) throws Exception{
        InputStream in = file.getInputStream();
        Workbook wb = WorkbookFactory.create(in);
        return orderCupImportService.importOrder(wb, 2);
    }

    @ApiOperation(value = "订单导入保存", notes = "订单导入保存")
    @PostMapping("/importSave")
    public JsonMsgBean importSave(@RequestBody OrderRequestDto vo){
        TokenInfo tokenInfo = FastThreadLocalUtil.getQY();
        //String lockKey = LockConstants.createOrder + tokenInfo.getUserId();
        String lockKey = LockConstants.createOrder;
        RLock lock = redissonClient.getLock(lockKey);
        if(lock.tryLock()){
            try {
                return orderCupImportService.importSave(vo, tokenInfo);
            } finally {
                lock.unlock();
            }
        }else {
            QYAssert.isFalse("系统繁忙,请勿频繁操作!");
        }

        return new JsonMsgBean(true);
    }
}
