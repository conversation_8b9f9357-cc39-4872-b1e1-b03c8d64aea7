package com.pinshang.qingyun.order.service;

import com.pinshang.qingyun.common.dto.DictionaryODTO;
import com.pinshang.qingyun.common.service.DictionaryClient;
import com.pinshang.qingyun.order.constant.DictionaryCodeConstant;
import com.pinshang.qingyun.order.dto.cup.LineGroupODTO;
import com.pinshang.qingyun.order.mapper.DictionaryMapper;
import com.pinshang.qingyun.order.model.dictionary.Dictionary;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;


@Service
@Slf4j
public class DictionaryService  {

    @Autowired
    private DictionaryMapper dictionaryMapper;
    @Autowired
    private DictionaryClient dictionaryClient;

    /**
     * 查询直通进大仓字典配置
     * @return
     */
    public Map<String,List<Long>> queryDirectDcConfig() {
        List<String> optionCodeList = Arrays.asList(
                DictionaryCodeConstant.STORETYPE_DIRECT_LIST,
                DictionaryCodeConstant.STORETYPE_ToB_LIST,
                DictionaryCodeConstant.WAREHOUSE_ToB_LIST);
        List<Dictionary> dictionaryList = dictionaryMapper.queryDictionaryConfigList(optionCodeList);
        Map<String,List<Long>> map = new HashMap<>();
        if(CollectionUtils.isEmpty(dictionaryList)){
            optionCodeList.forEach(k->{
                map.put(k,new ArrayList<>());
            });
            return map;
        }
        List<Long> directStoreTypeList = new ArrayList<>(dictionaryList.size());
        List<Long> tobStoreTypeList = new ArrayList<>(dictionaryList.size());
        List<Long> tobWarehouseList = new ArrayList<>(dictionaryList.size());
        dictionaryList.stream().collect(Collectors.groupingBy(Dictionary::getOptionCode)).forEach((k,v)->{
            List<Long> valueList = v.stream().map(p -> p.getOptionValue() != null ? Long.parseLong(p.getOptionValue().trim()) : 0L).collect(Collectors.toList());
            switch (k){
                case DictionaryCodeConstant.STORETYPE_DIRECT_LIST:
                    directStoreTypeList.addAll(valueList);
                    break;
                case DictionaryCodeConstant.STORETYPE_ToB_LIST:
                    tobStoreTypeList.addAll(valueList);
                    break;
                case DictionaryCodeConstant.WAREHOUSE_ToB_LIST:
                    tobWarehouseList.addAll(valueList);
                    break;
                default:
                    break;
            }
        });
        List<Long> finalTobStoreTypeList = new ArrayList<>(tobStoreTypeList.size());
        tobStoreTypeList.forEach(i->{
            //B端客户类型如果未配置直通客户类型，需验是否配置了B端仓库
            if (!directStoreTypeList.contains(i) && CollectionUtils.isNotEmpty(tobWarehouseList)){
                finalTobStoreTypeList.add(Long.valueOf(i));
            }
        });
        //直通模式，只要配置了直通客户类型，都进大仓
        map.put(DictionaryCodeConstant.STORETYPE_DIRECT_LIST,directStoreTypeList);
        //直通模式，如果未配置直通客户类型，但是配置了B端客户类型+B端仓库，也进大仓
        map.put(DictionaryCodeConstant.STORETYPE_ToB_LIST,finalTobStoreTypeList);
        map.put(DictionaryCodeConstant.WAREHOUSE_ToB_LIST,tobWarehouseList);
        return map;
    }

    /**
     * 获取线路组
     * @return
     */
    public List<LineGroupODTO> findLineGroups() {
        return dictionaryMapper.findLineGroups();
    }


    public List<DictionaryODTO> findStoreTypes() {
        return dictionaryClient.queryDictionaryListByParams("5043047439271865177", null);
    }

    /**
     * 查询xda预下单超时时间
     * @return
     */
    public Integer queryXdaPreOrderDelaySeconds() {
        // 字典配置的5分钟.返回秒
        DictionaryODTO dictionaryODTO = dictionaryClient.getDictionaryByCode("xdaPreOrderDelayNN");
        return Integer.valueOf(dictionaryODTO.getOptionValue()) * 60;
    }


    /**
     * 给结算发消息排除的订单类型
     * @return
     */
    public List<Long> getExcludeOrderTypeList(){
        List<Long> excludeOrderTypeList = new ArrayList<>();

        DictionaryODTO dictionaryODTO = dictionaryClient.getDictionaryByCode("T_ORDER_TYPE_INVALID");
        if(dictionaryODTO != null && StringUtils.isNotBlank(dictionaryODTO.getOptionValue())) {
            String [] orderTypeStr = dictionaryODTO.getOptionValue().split(",");
            if(orderTypeStr != null && orderTypeStr.length > 0) {
                excludeOrderTypeList = Arrays.stream(orderTypeStr)
                        .map(Long::parseLong)
                        .collect(Collectors.toList());
            }
        }
        return excludeOrderTypeList;
    }
}
