package com.pinshang.qingyun.order.service;

import com.pinshang.qingyun.order.mapper.StoreAreaMapper;
import com.pinshang.qingyun.order.mapper.cup.OrderWhiteMapper;
import com.pinshang.qingyun.order.model.cup.OrderWhite;
import com.pinshang.qingyun.order.model.order.Order;
import com.pinshang.qingyun.order.model.store.StoreArea;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;

import java.util.List;

/**
 * @Author: sk
 * @Date: 2024/7/18
 */
@Slf4j
@Service
public class StoreAreaService {

    @Autowired
    private StoreAreaMapper storeAreaMapper;

    public StoreArea findStoreAreaById(Long id) {
        return storeAreaMapper.selectByPrimaryKey(id);
    }



}
