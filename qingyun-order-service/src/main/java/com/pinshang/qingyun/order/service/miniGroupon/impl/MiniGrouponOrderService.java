package com.pinshang.qingyun.order.service.miniGroupon.impl;

import com.pinshang.qingyun.base.configure.expand.QYAssert;
import com.pinshang.qingyun.base.enums.YesOrNoEnums;
import com.pinshang.qingyun.box.utils.BeanCloneUtils;
import com.pinshang.qingyun.box.utils.SpringUtil;
import com.pinshang.qingyun.order.dto.miniGroupon.MiniGrouponAutoOrderIDTO;
import com.pinshang.qingyun.order.dto.miniGroupon.MiniGrouponAutoOrderItemODTO;
import com.pinshang.qingyun.order.enums.OperationTypeEnum;
import com.pinshang.qingyun.order.mapper.*;
import com.pinshang.qingyun.order.mapper.entry.MdShopOrderSettingEntry;
import com.pinshang.qingyun.order.mapper.entry.store.EmployeeEntry;
import com.pinshang.qingyun.order.mapper.entry.store.StoreEntry;
import com.pinshang.qingyun.order.model.commodity.Commodity;
import com.pinshang.qingyun.order.model.order.*;
import com.pinshang.qingyun.order.model.promotion.Promotion;
import com.pinshang.qingyun.order.model.promotion.PromotionProduct;
import com.pinshang.qingyun.order.model.shop.ShopReceiveOrder;
import com.pinshang.qingyun.order.service.CommonService;
import com.pinshang.qingyun.order.service.MdShopOrderSettingService;
import com.pinshang.qingyun.order.service.OrderAsyncKafkaService;
import com.pinshang.qingyun.order.service.OrderService;
import com.pinshang.qingyun.order.service.miniGroupon.IMiniGrouponOrderService;
import com.pinshang.qingyun.order.vo.CommodityPriceVo;
import com.pinshang.qingyun.order.vo.StoreCompanyRespVo;
import com.pinshang.qingyun.order.vo.order.OrderRequestVo;
import com.pinshang.qingyun.price.dto.commodity.CommodityListRequestIDTO;
import com.pinshang.qingyun.price.dto.commodity.CommodityResultODTO;
import com.pinshang.qingyun.price.service.ProductPriceModelClient;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionSynchronizationAdapter;
import org.springframework.transaction.support.TransactionSynchronizationManager;
import tk.mybatis.mapper.entity.Example;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@Slf4j
public class MiniGrouponOrderService implements IMiniGrouponOrderService {

    @Autowired
    private StoreMapper storeMapper;
    @Autowired
    private OrderMapper orderMapper;
    @Lazy
    @Autowired
    private MdShopOrderSettingService mdShopOrderSettingService;
    @Autowired
    private OrderMirrorMapper orderMirrorMapper;
    @Autowired
    private OrderListMapper orderListMapper;
    @Autowired
    private OrderListGiftMapper orderListGiftMapper;
    @Autowired
    private SubOrderMapper subOrderMapper;
    @Autowired
    private SubOrderItemMapper subOrderItemMapper;
    @Autowired
    private StoreSettlementMapper storeSettlementMapper;
    @Autowired
    private OrderBillMapper orderBillMapper;
    @Autowired
    private OrderAsyncKafkaService orderAsyncKafkaService;
    @Autowired
    private OrderService orderService;
    @Autowired
    private ShopReceiveOrderMapper shopReceiveOrderMapper;
    @Autowired
    private ProductPriceModelClient productPriceModelClient;
    @Autowired
    private CommodityMapper commodityMapper;
    @Autowired
    private CommonService commonService;

    @Override
    @Transactional(rollbackFor = Exception.class,propagation = Propagation.REQUIRES_NEW)
    public List<MiniGrouponAutoOrderItemODTO> miniGrouponAutoOrder(List<MiniGrouponAutoOrderIDTO> reqList) throws Exception{
        List<MiniGrouponAutoOrderItemODTO> resultList = new ArrayList<>();
        //获取门店信息
        List<Long> shopIdList = reqList.stream().map(item -> item.getShopId()).collect(Collectors.toList());
        Map<Long, StoreCompanyRespVo> shopMap = this.getShopInfo(shopIdList);
        //根据门店客户分组创建订单
        Map<Long, List<MiniGrouponAutoOrderIDTO>> orderMap = reqList.stream().collect(Collectors.groupingBy(MiniGrouponAutoOrderIDTO::getShopId));
        for (Map.Entry<Long, List<MiniGrouponAutoOrderIDTO>> map : orderMap.entrySet()) {
            StoreCompanyRespVo shopInfo = shopMap.get(map.getKey());
            QYAssert.isTrue(shopInfo != null && shopInfo.getStoreId() != null,"门店信息查询异常,shopid="+map.getKey());
            List<String> commodityIdStrs = new ArrayList<>();
            List<Long> commodityIds = new ArrayList<>();
            for (MiniGrouponAutoOrderIDTO item : map.getValue()) {
                commodityIdStrs.add(item.getCommodityId()+"");
                commodityIds.add(item.getCommodityId());
            }
            // 检查客户状态
            orderService.checkStoreStatus(shopInfo.getStoreId(), "当前客户已停用，不允许下单");
            // 校验门店时间
            OrderRequestVo orderRequestVo = new OrderRequestVo();
            orderRequestVo.setStoreId(shopInfo.getStoreId() + "");
            orderService.checkStoreOrderTime(orderRequestVo);
            //检查门店订货通用设置,获取门店物流信息、仓库、供应商
            List<MdShopOrderSettingEntry> settingList  = orderService.checkShopOrderSetting(shopInfo.getStoreId(), commodityIdStrs, false);
            Map<Long,MdShopOrderSettingEntry> settingMap = settingList.stream().collect(Collectors.toMap(MdShopOrderSettingEntry::getCommodityId, Function.identity()));
            Map<String, List<MiniGrouponAutoOrderIDTO>> orderClassifyMap = new HashMap<>();
            String orderTime = map.getValue().get(0).getOrderTime();
            // 拆分成子单：供应商、仓库、物流模式
            for (MiniGrouponAutoOrderIDTO shopOrderInfo : map.getValue()) {
                MdShopOrderSettingEntry settingEntry = settingMap.get(shopOrderInfo.getCommodityId());
                QYAssert.isTrue(settingEntry != null,"门店商品查询异常,shopid="+map.getKey()+",commodityId="+shopOrderInfo.getCommodityId());
                if(settingEntry.getLogisticsModel() != null){
                    shopOrderInfo.setLogisticsModel(settingEntry.getLogisticsModel().intValue());
                }
                if(settingEntry.getSupplierId() !=null){
                    shopOrderInfo.setSupplierId(Long.valueOf(settingEntry.getSupplierId()));
                }
                if(settingEntry.getWarehouseId() != null){
                    shopOrderInfo.setWarehouseId(Long.valueOf(settingEntry.getWarehouseId()));
                }
                String key = shopOrderInfo.getClassifyKey();
                if(orderClassifyMap.containsKey(key)){
                    orderClassifyMap.get(key).add(shopOrderInfo);
                }else {
                    orderClassifyMap.put(key, new ArrayList<MiniGrouponAutoOrderIDTO>(){{add(shopOrderInfo);}});
                }
            }

            //获取产品价格方案中的价格
            Map<Long,CommodityPriceVo> priceMap = this.processProductPrice(commodityIds,shopInfo.getStoreId().toString(),orderTime);


            //保存订单信息
            for (Map.Entry<String, List<MiniGrouponAutoOrderIDTO>> itemMap : orderClassifyMap.entrySet()){
                //5、获取订单商品总价格
                BigDecimal orderAmount = BigDecimal.ZERO;
                BigDecimal promotionAmount = BigDecimal.ZERO;
                List<String> commodityIdList = new ArrayList<>();
                for (MiniGrouponAutoOrderIDTO amount : itemMap.getValue()) {
                    CommodityPriceVo price = priceMap.get(amount.getCommodityId());
                    QYAssert.isTrue(price != null, "门店"+map.getKey()+"，"+amount.getCommodityId()+"商品价格方案不存在");
                    amount.setOriginPrice(price.getOriginPrice());
                    amount.setPromotionPrice(price.getPromotionPrice());
                    orderAmount = orderAmount.add(amount.getOrderAmount()==null?BigDecimal.ZERO:amount.getOrderAmount());
                    promotionAmount = promotionAmount.add(amount.getPromotionAmount()==null?BigDecimal.ZERO:amount.getPromotionAmount());
                    commodityIdList.add(amount.getCommodityId().toString());
                }
                //6、保存订单信息
                Order order = this.saveOrder(commodityIdList,shopInfo,orderTime,orderAmount,promotionAmount);
                if(!"classifyKey".equals(itemMap.getKey())){
                    resultList.addAll(this.saveSubOrder(itemMap.getValue(),order));
                }
                //7、扣款 预付款用户保存付款单
                this.saveOrderDeductions(order, shopInfo.getStoreId(), promotionAmount);
                //8、保存”t_order_list、t_order_list_gift“数据
                List<OrderList> orderLists = this.saveOrderList(itemMap.getValue(),order.getId());
                order.setOrderList(orderLists);
                //9、保存“t_order_mirror”用户信息
                this.saveOrderMirror(order);
                //10、记录订单日志
                orderService.inserOrderHistory(order.getId(), Long.valueOf(order.getOrderCode()), -1L, "系统", OperationTypeEnum.ORDER_NEW.getCode(), order.getOrderTime(), null, null);
                //11.发送kafka消息
                TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronizationAdapter() {
                    @Override
                    public void afterCommit() {
                        orderAsyncKafkaService.sendKafkaSaveOrderMessage(order);
                    }
                });
            }
        }
        return resultList;
    }


    /**
     * 获取产品价格方案中的价格及特价
     */
    public Map<Long,CommodityPriceVo> processProductPrice(List<Long> commodityIds, String storeId, String orderTime){
        Map<Long,CommodityPriceVo> map = new HashMap<>();
        //获取产品价格方案中的价格
        CommodityListRequestIDTO idto = new CommodityListRequestIDTO();
        idto.setStoreId(storeId);
        idto.setCommodityIdListAll(commodityIds);
        List<CommodityResultODTO> commodityResultList = productPriceModelClient.findStoreCommodityList(idto);
        if(SpringUtil.isNotEmpty(commodityResultList)){
            for (CommodityResultODTO item : commodityResultList) {
                Long commodityId = Long.valueOf(item.getCommodityId());
                if(item.getCommodityPrice() != null){
                    map.put(commodityId,new CommodityPriceVo(commodityId,item.getCommodityCode(),item.getCommodityPrice()));
                }
            }
        }
        //获取特价
        List<Promotion> promotionList = commonService.findCommodityPromotionByStoreId(storeId,orderTime);
        if(SpringUtil.isNotEmpty(promotionList)){
            for (Promotion promotion : promotionList) {
                List<PromotionProduct> promotionProductList = commonService.findPromotionProductByPromotionId(promotion.getId().toString());
                if(SpringUtil.isNotEmpty(promotionProductList)){
                    Map<String,Double> promotionMap = promotionProductList.stream().collect(Collectors.toMap(PromotionProduct::getProductCode, PromotionProduct::getPrice));
                    for (Map.Entry<Long, CommodityPriceVo> itemMap : map.entrySet()) {
                        Double promotionPrice = promotionMap.get(itemMap.getValue().getCommodityCode());
                        if(promotionPrice != null){
                            itemMap.getValue().setPromotionPrice(BigDecimal.valueOf(promotionPrice));
                        }
                    }
                }
            }
        }
        return map;
    }

    /**
     * 订单扣款/回款
     * @param order
     */
    private void saveOrderDeductions(Order order, Long storeId, BigDecimal promotionAmount){
        Example ex = new Example(StoreSettlement.class);
        ex.createCriteria().andEqualTo("storeId", storeId);
        List<StoreSettlement> ssList = this.storeSettlementMapper.selectByExample(ex);
        if(SpringUtil.isNotEmpty(ssList)){
            StoreSettlement ss = ssList.get(0);
            if(ss.getCollectStatus()){
                if(ss.getCollectPrice()==null){
                    ss.setCollectPrice(promotionAmount.doubleValue());
                }else{
                    ss.setCollectPrice((BigDecimal.valueOf(ss.getCollectPrice()).subtract(promotionAmount)).doubleValue());
                }
                storeSettlementMapper.updateByPrimaryKey(ss);
                OrderBill ob = OrderBill.initForMiniFroupon(order,storeId,promotionAmount,ss.getCollectPrice());
                this.orderBillMapper.insertSelective(ob);
            }
        }
    }


    /**
     * 保存拆单明细
     * @param order
     */
    private List<MiniGrouponAutoOrderItemODTO> saveSubOrder(List<MiniGrouponAutoOrderIDTO> itemList, Order order){
        List<MiniGrouponAutoOrderItemODTO> resultItems = new ArrayList<>();
        SubOrder subOrder = SubOrder.initForMiniFroupon(itemList,order);
        List<SubOrderItem> items = new ArrayList<>(itemList.size());
        for(MiniGrouponAutoOrderIDTO item : itemList){
            //获取是否可变价状态
            Boolean isPromotionPrice = item.getPromotionPrice() == null ? Boolean.FALSE : Boolean.TRUE;
            Integer changePriceStatus = changePriceStatus(new ArrayList<String>(){{add(item.getCommodityId().toString());}},order.getStoreId(),isPromotionPrice);
            SubOrderItem subOrderItem = SubOrderItem.initForMiniFroupon(item,changePriceStatus);
            items.add(subOrderItem);
        }
        //创建子单
        subOrderMapper.insertSelective(subOrder);
        for(SubOrderItem item : items){
            item.setSubOrderId(subOrder.getId());
        }
        //创建子单明细
        subOrderItemMapper.insertList(items);
        for(SubOrderItem item : items){
            resultItems.add(new MiniGrouponAutoOrderItemODTO(item,order.getId()));
        }
        //创建收货单
        ShopReceiveOrder s = ShopReceiveOrder.initForMiniFroupon(subOrder.getId());
        shopReceiveOrderMapper.insert(s);
        return resultItems;
    }

    /**
     * 获取门店信息
     */
    public Map<Long, StoreCompanyRespVo> getShopInfo(List<Long> shopIdList){
        List<StoreCompanyRespVo> storeCompanyList = storeMapper.getCompanyIdByShopIdList(shopIdList);
        Map<Long, StoreCompanyRespVo> shopMap = new HashMap<>(shopIdList.size());
        if(SpringUtil.isNotEmpty(storeCompanyList)){
            shopMap = storeCompanyList.stream().collect(Collectors.toMap(StoreCompanyRespVo::getShopId, Function.identity()));
        }
        return shopMap;
    }

    /**
     * 创建订单
     * */
    private Order saveOrder(List<String> commodityIdList, StoreCompanyRespVo shopInfo,String orderTime,BigDecimal orderAmount,BigDecimal promotionAmount){
        //获取是否可变价状态
        Boolean isPromotionPrice = orderAmount.compareTo(promotionAmount)==0 ? Boolean.FALSE : Boolean.TRUE;
        Integer changePriceStatus = changePriceStatus(commodityIdList,shopInfo.getStoreId(),isPromotionPrice);
        Order order = Order.initForMiniFroupon(shopInfo, orderTime,orderAmount,promotionAmount,changePriceStatus);
        this.orderMapper.insertSelective(order);
        return order;
    }

    /**
     * 保存”t_order_list、t_order_list_gift“数据
     * */
    private List<OrderList> saveOrderList(List<MiniGrouponAutoOrderIDTO> list,Long orderId){
        List<OrderList> orderLists = new ArrayList<>();
        List<OrderListGift> orderGiftList = new ArrayList<>();
        for (MiniGrouponAutoOrderIDTO item : list) {
            OrderList orderList = OrderList.initForMiniFroupon(item,orderId);
            orderLists.add(orderList);
            OrderListGift gift = BeanCloneUtils.copyTo(orderList, OrderListGift.class);
            orderGiftList.add(gift);
        }
        if(SpringUtil.isNotEmpty(orderLists)){
            orderListMapper.insertList(orderLists);
        }
        if(SpringUtil.isNotEmpty(orderGiftList)){
            orderListGiftMapper.insertList(orderGiftList);
        }
        return orderLists;
    }

    public void saveOrderMirror(Order order){
        StoreEntry store = orderMapper.findStoreById(String.valueOf(order.getStoreId()));
        OrderMirror orderMirror = new OrderMirror();
        orderMirror.setOrderId(order.getId());
        orderMirror.setOrderCode(order.getOrderCode());

        if(null != store){
            EmployeeEntry employee = null;
            if(null != store.getDeliverymanId()){
                employee = orderMapper.findEmployeeById(store.getDeliverymanId());
                orderMirror.setDeliveryManId(employee.getId());
                orderMirror.setDeliveryManName(employee.getEmployeeName());
            }
            if(null != store.getSalesmanId()){
                employee = orderMapper.findEmployeeById(store.getSalesmanId());
                orderMirror.setSalesmanId(employee.getId());
                orderMirror.setSalesmanName(employee.getEmployeeName());
            }
            if(null != store.getSupervisorId()){
                employee = orderMapper.findEmployeeById(store.getSupervisorId());
                orderMirror.setSupervisionId(employee.getId());
                orderMirror.setSupervisionName(employee.getEmployeeName());
            }
            if(null != store.getRegionManagerId()){
                employee = orderMapper.findEmployeeById(store.getRegionManagerId());
                orderMirror.setRegionalManagerId(employee.getId());
                orderMirror.setRegionalManagerName(employee.getEmployeeName());
            }
            if(null != store.getOfficeDirectorId()){
                employee = orderMapper.findEmployeeById(store.getOfficeDirectorId());
                orderMirror.setDirectorId(employee.getId());
                orderMirror.setDirectorName(employee.getEmployeeName());
            }
        }
        orderMirrorMapper.insert(orderMirror);
    }

    /**
     * 判断订单中的商品是否可变价格，只要订单中有一个商品是可变价的，那么这个订单就是可变价的
     * @return
     */
    private Integer changePriceStatus(List<String> commodityIds, Long storeId,Boolean isPromotionPrice){
        if(isPromotionPrice){
            return YesOrNoEnums.NO.getCode();
        }
        List<MdShopOrderSettingEntry> shopOrderSettingODTOs = mdShopOrderSettingService.queryMdShopOrderSettingListByCommodityIds(commodityIds, storeId);
        if(CollectionUtils.isEmpty(shopOrderSettingODTOs)){
            Example ex = new Example(Commodity.class);
            ex.createCriteria().andIn("id", commodityIds);
            List<Commodity> commList = commodityMapper.selectByExample(ex);
            StringBuffer sb = new StringBuffer("");
            commList.forEach(comm -> {
                sb.append(comm.getCommodityCode()+" ");
            });
            QYAssert.isTrue(false, "没有查询到相应的店铺订单设置~" + sb.toString());
        }
        Integer changePriceStatus = YesOrNoEnums.NO.getCode();
        for (MdShopOrderSettingEntry item : shopOrderSettingODTOs) {
            if (YesOrNoEnums.YES.getCode().equals(item.getChangePriceStatus())) {
                changePriceStatus = YesOrNoEnums.YES.getCode();
                break;
            }
        }
        return changePriceStatus;
    }

}
