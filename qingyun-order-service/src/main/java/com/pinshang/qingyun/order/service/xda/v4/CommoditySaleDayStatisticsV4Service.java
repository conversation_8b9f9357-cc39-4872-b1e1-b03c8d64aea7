package com.pinshang.qingyun.order.service.xda.v4;

import com.pinshang.qingyun.base.configure.expand.QYAssert;
import com.pinshang.qingyun.box.utils.DateUtil;
import com.pinshang.qingyun.box.utils.SpringUtil;
import com.pinshang.qingyun.order.dto.xda.v4.CreOrderItemV4DTO;
import com.pinshang.qingyun.order.mapper.CommoditySaleDayStatisticsMapper;
import com.pinshang.qingyun.order.model.order.XdaCommoditySaleDayStatistics;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.entity.Example;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static java.util.stream.Collectors.groupingBy;

/**
 * <AUTHOR>
 * @Date 2023/12/26 16:33
 */
@Service
@Slf4j
public class CommoditySaleDayStatisticsV4Service {
    @Autowired
    private CommoditySaleDayStatisticsMapper commoditySaleDayStatisticsMapper;
    @Autowired
    private RedissonClient redissonClient;
    @Autowired
    private CommoditySaleDayStatisticsSaveV4Service commoditySaleDayStatisticsSaveV4Service;


    /**
     * job每天汇总鲜达商品销量日统计
     * @param orderTime
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public Boolean xdaCommoditySaleStatisticsDayReport(String orderTime){
        commoditySaleDayStatisticsMapper.deleteSaleDayStatistics(orderTime);
        commoditySaleDayStatisticsMapper.insertSaleDayStatistics(orderTime);
        return  Boolean.TRUE;
    }

    /**
     * 保存线上订单销量日统计
     * @param orderTime
     * @param orderLists
     * 异步加锁执行，这样就会导致外面报错，这里维护的就不会回滚。
     * 所以再加个job(xdaCommoditySaleStatisticsJobHandler)，每天凌晨跑一次
     */
    @Async
    public void saveSaleDayStatistics(Date orderTime, List<CreOrderItemV4DTO> orderLists){
        if(SpringUtil.isNotEmpty(orderLists)){
            List<XdaCommoditySaleDayStatistics> dayStatisticsList = new ArrayList<>();
            List<Long> commodityIdList = new ArrayList<>();

            String orderTimeStr = DateUtil.get4yMd(orderTime);
            Date orderTimeParse = DateUtil.parseDate(orderTimeStr, "yyyy-MM-dd");

            //orderTimeParse = sdf.parse(sdf.format(orderTime));
            Date finalOrderTimeParse = orderTimeParse;
            orderLists.stream().collect(groupingBy(CreOrderItemV4DTO::getCommodityId)).forEach((k, v)->{
                BigDecimal totalQuantity = v.stream().map(CreOrderItemV4DTO:: saleQuantity).reduce(BigDecimal.ZERO, BigDecimal::add);
                BigDecimal totalAmount = v.stream().map(CreOrderItemV4DTO :: saleAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
                XdaCommoditySaleDayStatistics dayStatistics = new XdaCommoditySaleDayStatistics();
                dayStatistics.setOrderTime(finalOrderTimeParse);
                dayStatistics.setCommodityId(k);
                dayStatistics.setTotalQuantity(totalQuantity.setScale(2,BigDecimal.ROUND_UP));
                dayStatistics.setTotalAmount(totalAmount.setScale(2,BigDecimal.ROUND_UP));
                dayStatisticsList.add(dayStatistics);
                commodityIdList.add(k);
            });

            for(XdaCommoditySaleDayStatistics dayStatistics : dayStatisticsList){

                RLock lock = redissonClient.getLock( "order:saveSaleDayStatistics:" + orderTimeStr + ":" +dayStatistics.getCommodityId());
                lock.lock(30L, TimeUnit.SECONDS);
                try{
                    commoditySaleDayStatisticsSaveV4Service.saveOrUpdateSaleDayStatistics(orderTimeStr, dayStatistics);
                }finally{
                    if(lock.isLocked() && lock.isHeldByCurrentThread()){
                       lock.unlock();
                    }
                }
            }

            /*Example example = new Example(XdaCommoditySaleDayStatistics.class);
            example.createCriteria().andEqualTo("orderTime",finalOrderTimeParse).andIn("commodityId",commodityIdList);
            List<XdaCommoditySaleDayStatistics> dbList = commoditySaleDayStatisticsMapper.selectByExample(example);
            if(SpringUtil.isNotEmpty(dbList)){
                List<Long> dbCommodityIdList = dbList.stream().map(XdaCommoditySaleDayStatistics::getCommodityId).collect(Collectors.toList());
                List<XdaCommoditySaleDayStatistics> addList = new ArrayList<>();
                List<XdaCommoditySaleDayStatistics> updateList = new ArrayList<>();
                dayStatisticsList.forEach(entry->{
                    if(dbCommodityIdList.contains(entry.getCommodityId())){
                        updateList.add(entry);
                    }else{
                        addList.add(entry);
                    }
                });
                if(SpringUtil.isNotEmpty(addList)){
                    commoditySaleDayStatisticsMapper.insertList(addList);
                }
                if(SpringUtil.isNotEmpty(updateList)){
                    commoditySaleDayStatisticsMapper.batchUpdate(updateList);
                }
            }else{
                commoditySaleDayStatisticsMapper.insertList(dayStatisticsList);
            }*/

        }
    }

    public List<XdaCommoditySaleDayStatistics> queryDayCommodityQuantity(Date orderTime, List<Long> commodityIds){
        QYAssert.notNull(orderTime, "订货日期不能为空！");
        String date = DateUtil.getDateFormate(orderTime, "yyyy-MM-dd");
        return commoditySaleDayStatisticsMapper.queryDayCommodityQuantity(date, commodityIds);
    };
}
