package com.pinshang.qingyun.order.service.tob;

import com.github.pagehelper.PageInfo;
import com.pinshang.qingyun.order.dto.tob.*;
import com.pinshang.qingyun.order.vo.tob.AdminToBOrderStatisticReqVo;
import com.pinshang.qingyun.order.vo.tob.AdminToBOrderStatisticRespVo;

import java.util.List;


/**
 * @Description：统计接口
 * @Author：ZhangHui
 * @Package：com.pinshang.qingyun.order.service.tob
 * @Date: 2024/4/7
 */
public interface ToBOrderStatisticsService {
    /**
     * b端下单统计分页查询接口
     *
     * @param reqVo
     * @return
     */
    PageInfo<AdminToBOrderStatisticRespVo> queryToBOrderStatisticPage(AdminToBOrderStatisticReqVo reqVo);

    /**
     * 修复数据
     * @param repairIDTOList
     */
    void repairData(List<ToBOrderStatisticsRepairIDTO> repairIDTOList);

    /**
     * 查询B端订单数据和提货卡数据
     * @param dataIDTO
     * @return
     */
    ToBOrderAndTakeAppointmentDataODTO queryToBOrderAndTakeAppointmentData(ToBOrderAndTakeAppointmentDataIDTO dataIDTO);

    /**
     * 查询订单数量
     * @param statisticsIDTO
     * @return
     */
    Integer queryOrderStatisticsCount(ToBQueryOrderStatisticsIDTO statisticsIDTO);

    /**
     * 查询订单数据
     * @param statisticsIDTO
     * @return
     */
    PageInfo<ToBQueryOrderStatisticsODTO> queryOrderStatisticsList(ToBQueryOrderStatisticsIDTO statisticsIDTO);
}
