package com.pinshang.qingyun.order.service;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.pinshang.qingyun.base.configure.expand.QYAssert;
import com.pinshang.qingyun.box.utils.DateUtil;
import com.pinshang.qingyun.order.mapper.OrderCompanyCommodityStatisticsMapper;
import com.pinshang.qingyun.order.mapper.entry.orderStatistics.OrderCompanyCommodityStatisticsEntry;
import com.pinshang.qingyun.order.model.statistics.OrderCompanyCommodityStatistics;
import com.pinshang.qingyun.order.vo.orderStatistics.OrderCompanyCommodityStatisticsVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.entity.Example;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 客户所属公司+商品两个维度，统计商品订单数据
 */
@Slf4j
@Service
public class OrderCompanyCommodityStatisticsService {
    @Autowired
    private OrderCompanyCommodityStatisticsMapper statisticsMapper;

    /**
     * 定时保存商品订单统计数据
     * @param orderTime
     */
    @Transactional(rollbackFor = Exception.class)
    public void saveOrderCompanyCommodityStatistics(Date orderTime){
        Example example = new Example(OrderCompanyCommodityStatistics.class);
        example.createCriteria().andEqualTo("orderTime",orderTime);
        int count = statisticsMapper.selectCountByExample(example);
        if(count>0){
            statisticsMapper.deleteByExample(example);
        }
        List<OrderCompanyCommodityStatistics> saveList = statisticsMapper.queryOrderCommodityStatisticsByOrderTime(orderTime);
        if(CollectionUtils.isNotEmpty(saveList)){
            statisticsMapper.insertList(saveList);
        }
    }
    public PageInfo<OrderCompanyCommodityStatisticsEntry> findListByCondition(OrderCompanyCommodityStatisticsVo vo){
        QYAssert.notNull( vo.getStartOrderTime(),"送货日期必选");
        QYAssert.notNull( vo.getEndOrderTime(),"送货日期必选");
        QYAssert.notNull( vo.getShowType(),"显示方式必选");
        return PageHelper.startPage(vo.getPageNo(), vo.getPageSize()).doSelectPageInfo(() -> statisticsMapper.findListByCondition(vo));
    }

    private  void checkParam(OrderCompanyCommodityStatisticsVo vo){
        QYAssert.isTrue(vo!=null,"参数不能为空");
        QYAssert.isTrue(StringUtils.isNotBlank(vo.getStartOrderTime()) && StringUtils.isNotBlank(vo.getEndOrderTime()), "请选择送货日期");
        int diff = DateUtil.getDayDif(DateUtil.parseDate(vo.getStartOrderTime(), "yyyy-MM-dd"), DateUtil.parseDate(vo.getEndOrderTime(), "yyyy-MM-dd"));
        QYAssert.isTrue(diff <= 62, "送货日期的跨度不能超过62天");
    }

    /**
     * 动态创建导出表头
     * @param vo
     * @param list
     * @return
     */
    public List<List<String>> createExportHead(OrderCompanyCommodityStatisticsVo vo,List<OrderCompanyCommodityStatisticsEntry> list){
        // 表头
        List<List<String>> heads = new ArrayList<>(7);
        StringBuffer sb = new StringBuffer();
        sb.append(DateUtil.getDateFormate(DateUtil.parseDate(vo.getStartOrderTime(), "yyyy-MM-dd"),"M.d"));
        sb.append("-");
        sb.append(DateUtil.getDateFormate(DateUtil.parseDate(vo.getEndOrderTime(), "yyyy-MM-dd"),"M.d"));
        if(vo.getCompanyId()!=null){
            sb.append("属于"+list.get(0).getCompanyName()+"客户的");
        }
        sb.append("发货商品(月度汇总)");
        String title = sb.toString();
        List<String> head0 = new ArrayList<>();
        head0.add(title);
        head0.add("送货日期");
        List<String> head1 = new ArrayList<>();
        head1.add(title);
        head1.add("工厂");
        List<String> head2 = new ArrayList<>();
        head2.add(sb.toString());
        head2.add("商品编码");
        List<String> head3 = new ArrayList<>();
        head3.add(sb.toString());
        head3.add("商品名称");
        List<String> head4 = new ArrayList<>();
        head4.add(sb.toString());
        head4.add("计量单位");
        List<String> head5 = new ArrayList<>();
        head5.add(sb.toString());
        head5.add("数量小计");
        List<String> head6 = new ArrayList<>();
        head6.add(sb.toString());
        head6.add("金额小计");
        if(vo.getShowType()==null || vo.getShowType()==1){
            heads.add(head0);
        }
        heads.add(head1);
        heads.add(head2);
        heads.add(head3);
        heads.add(head4);
        heads.add(head5);
        heads.add(head6);
        return heads;
    }

    /**
     * 动态创建表格内容
     * @param vo
     * @param list
     * @return
     */
    public List<List<Object>> createExportData(OrderCompanyCommodityStatisticsVo vo,List<OrderCompanyCommodityStatisticsEntry> list){
        // 表头
        List<List<Object>> dataList = new ArrayList<>(7);
        list.forEach(item->{
            List<Object> data = new ArrayList<>();
            if(vo.getShowType()==null || vo.getShowType()==1){
                data.add(item.getShowOrderTime());
            }
            data.add(item.getFactoryName());
            data.add(item.getCommodityCode());
            data.add(item.getCommodityName());
            data.add(item.getUnitName());
            data.add(item.getOrderQuantity());
            data.add(item.getOrderTotalAmount());
            dataList.add(data);
        });
        return dataList;
    }

}
