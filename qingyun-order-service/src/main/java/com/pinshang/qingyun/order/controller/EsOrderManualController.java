package com.pinshang.qingyun.order.controller;

import com.pinshang.qingyun.base.api.ApiResult;
import com.pinshang.qingyun.order.service.orderStatistics.EsOrderManualService;
import com.pinshang.qingyun.order.vo.orderMonitor.EsOrderManualVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

/**
 * 模拟下订单同步到统计库和ES
 */
@Controller
@RequestMapping("/esOrderManual")
@Slf4j
public class EsOrderManualController {
    @Autowired
    private EsOrderManualService esOrderManualService;

    @GetMapping("/index")
    public String index() {
        return "orderIndex";
    }

    @ResponseBody
    @PostMapping("/createAssignOrder")
    public ApiResult createAssignOrder(EsOrderManualVo vo) {
        try {
            esOrderManualService.createAssignOrder(vo);
        } catch (Exception e) {
            log.error("手动创建订单异常",e);
            return new ApiResult().initFailure(e.toString());
        }
        return new ApiResult().initSuccess("操作成功！");
    }

}
