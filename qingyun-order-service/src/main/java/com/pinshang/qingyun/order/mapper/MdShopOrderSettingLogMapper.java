package com.pinshang.qingyun.order.mapper;

import com.pinshang.qingyun.base.mybatis.MyMapper;
import com.pinshang.qingyun.order.mapper.entry.MdShopOrderSettingLogEntry;
import com.pinshang.qingyun.order.model.shop.MdShopOrderSettingLog;
import com.pinshang.qingyun.order.vo.shop.MdShopOrderSettingLogVo;
import org.apache.ibatis.annotations.Mapper;
import org.springframework.stereotype.Repository;

import java.util.List;

@Mapper
@Repository
public interface MdShopOrderSettingLogMapper extends MyMapper<MdShopOrderSettingLog> {

    /**
     * 获取门店下单设置日志列表
     * @param shopOrderSettingLogVo
     * @return
     */
    List<MdShopOrderSettingLogEntry> queryMdShopOrderSettingLogListByParams(MdShopOrderSettingLogVo shopOrderSettingLogVo);
}
