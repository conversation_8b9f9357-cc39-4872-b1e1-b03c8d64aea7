package com.pinshang.qingyun.order.service;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.pinshang.qingyun.base.api.TokenInfo;
import com.pinshang.qingyun.base.configure.expand.QYAssert;
import com.pinshang.qingyun.base.enums.OperateTypeEnums;
import com.pinshang.qingyun.base.enums.YesOrNoEnums;
import com.pinshang.qingyun.base.util.FastThreadLocalUtil;
import com.pinshang.qingyun.box.utils.DateUtil;
import com.pinshang.qingyun.box.utils.SpringUtil;
import com.pinshang.qingyun.order.dto.*;
import com.pinshang.qingyun.order.mapper.CommodityFreezeGroupMapper;
import com.pinshang.qingyun.order.mapper.entry.CommodityFreezeGroupEntry;
import com.pinshang.qingyun.order.model.cup.ConsumableLimit;
import com.pinshang.qingyun.product.dto.commodity.CommodityDetailODTO;
import com.pinshang.qingyun.product.dto.commodity.CommodityODTO;
import com.pinshang.qingyun.product.service.CommodityClient;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import tk.mybatis.mapper.entity.Example;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @ClassName CommodityFreezeGroupService
 * <AUTHOR>
 * @Date 2022/9/29 10:23
 * @Description CommodityFreezeGroupService
 * @Version 1.0
 */
@Service
@Slf4j
public class CommodityFreezeGroupService {
    @Autowired
    private CommodityFreezeGroupMapper commodityFreezeGroupMapper;
    @Autowired
    private CommonService commonService;
    @Autowired
    private CommodityClient commodityClient;
    @Autowired
    private SendLogService sendLogService;

    @Autowired
    private KafkaTemplate<String, String> kafkaTemplate;

    public PageInfo<CommodityFreezeGroupODTO> page(@RequestBody CommodityFreezeGroupIDTO idto){
        PageInfo<CommodityFreezeGroupODTO> pageInfo = PageHelper.startPage(idto.getPageNo(), idto.getPageSize()).doSelectPageInfo(() -> {
            commodityFreezeGroupMapper.page(idto);
        });
        List<CommodityFreezeGroupODTO> list = pageInfo.getList();
        if(SpringUtil.isEmpty(list)){
            return pageInfo;
        }
        List<Long> commodityIdList = list.stream().map(CommodityFreezeGroupODTO::getCommodityId).collect(Collectors.toList());
        Map<Long, String> barCodeMap = commonService.getCommodityBarCodeMap(commodityIdList, null);
        List<CommodityDetailODTO> commodityList = commodityClient.findCommodityInfoByIdList(commodityIdList);
        Map<String, CommodityDetailODTO> commMap = commodityList.stream().collect(Collectors.toMap(CommodityDetailODTO::getCommodityId, Function.identity()));
        list = list.stream().peek(it -> {
            it.setBarCode(barCodeMap.get(it.getCommodityId()));
            CommodityDetailODTO commodityDetailODTO = commMap.get(it.getCommodityId() + "");
            if(commodityDetailODTO != null){
                it.setWorkShopName(commodityDetailODTO.getWorkshopName());
                it.setFactoryName(commodityDetailODTO.getFactoryName());
            }
        }).collect(Collectors.toList());
        return pageInfo;
    }

    @Transactional(rollbackFor = Exception.class)
    public CommodityFreezeGroupInsertODTO insert(@RequestBody CommodityFreezeGroupInsertIDTO idto){
        CommodityFreezeGroupInsertODTO odto = new CommodityFreezeGroupInsertODTO();
        odto.setCode(YesOrNoEnums.YES.getCode());
        List<CommodityODTO> commodityList = commodityClient.findCommodityListByCommodityCodeList(idto.getCommodityCodeList());
        List<String> commodityCodeList = commodityList.stream().map(CommodityODTO::getCommodityCode).collect(Collectors.toList());
        List<String> noExitCommodityCodeList = idto.getCommodityCodeList().stream().filter(it -> !commodityCodeList.contains(it)).collect(Collectors.toList());
        if(SpringUtil.isNotEmpty(noExitCommodityCodeList)){
            odto.setNoExitCode(noExitCommodityCodeList);
            odto.setCode(YesOrNoEnums.NO.getCode());
            return odto;
        }

        List<String> combCommodityCodes  =  checkCommodityListIfComb(commodityList);
        if(SpringUtil.isNotEmpty(combCommodityCodes)){
            odto.setNoExitCode(combCommodityCodes);
            odto.setCode(0);
            return odto;
        }

        List<Long> commodityIdList = commodityList.stream().map(CommodityODTO::getId).collect(Collectors.toList());
        List<Long> oldRecord = commodityFreezeGroupMapper.selectOldCommodity(commodityIdList);
        if(SpringUtil.isNotEmpty(oldRecord)){
            commodityIdList.removeAll(oldRecord);
        }
        if(commodityIdList.size() == 0){
            return odto;
        }
        TokenInfo qy = FastThreadLocalUtil.getQY();;
        Long userId = qy.getUserId();
        Date nowDate = new Date();
        List<CommodityFreezeGroupEntry> insertList = commodityIdList.stream().map(it -> {
            CommodityFreezeGroupEntry entry = new CommodityFreezeGroupEntry();
            entry.setCommodityId(it);
            entry.setUpdateId(userId);
            entry.setUpdateTime(nowDate);
            entry.setCreateId(userId);
            entry.setCreateTime(nowDate);
            return entry;
        }).collect(Collectors.toList());
        commodityFreezeGroupMapper.insertList(insertList);
        // 日志
        List<Long> logIdList = insertList.stream().map(CommodityFreezeGroupEntry::getId).collect(Collectors.toList());
        List<CommodityFreezeGroupEntryLog> logList = commodityFreezeGroupMapper.selectInfoForLog(logIdList);
        List<CommodityDetailODTO> commodityLogList = commodityClient.findCommodityInfoByIdList(commodityIdList);
        Map<String, CommodityDetailODTO> commMap = commodityLogList.stream().collect(Collectors.toMap(CommodityDetailODTO::getCommodityId, Function.identity()));
        logList = logList.stream().peek(it -> {
            CommodityDetailODTO commodityDetailODTO = commMap.get(it.getCommodityId() + "");
            if(commodityDetailODTO != null){
                it.setWorkShopName(commodityDetailODTO.getWorkshopName());
                it.setFactoryName(commodityDetailODTO.getFactoryName());
            }
            it.setOperaType(OperateTypeEnums.新增.getCode());
            it.setOperaTime(DateUtil.get4yMdHms(nowDate));
        }).collect(Collectors.toList());
        sendLogService.sendLog(logList, "t_log_commodity_freeze_group");

        return odto;
    }

    /**
     * 校验凑正品信息不允许为组合品
     * @param
     */
    private List<String> checkCommodityListIfComb(List<CommodityODTO> commodityList) {

        List<String> codes = new ArrayList<>();
        List<CommodityODTO> combCommodities = commodityList.stream()
                .filter(commodity -> commodity.getProductType().intValue() == 2)
                .collect(Collectors.toList());

        if(CollectionUtils.isNotEmpty(combCommodities)){
            combCommodities.forEach(combCommodity -> {
                codes.add(combCommodity.getCommodityCode());
            });
        }
        return codes;
    }

    @Transactional(rollbackFor = Exception.class)
    public boolean delete(@RequestParam("id)") Long id){
        String userName = FastThreadLocalUtil.getQY().getRealName();
        // 日志数据拼接
        List<Long> commodityFreezeGroupIdList = Collections.singletonList(id);
        List<CommodityFreezeGroupEntryLog> logList = commodityFreezeGroupMapper.selectInfoForLog(commodityFreezeGroupIdList);
        QYAssert.isTrue(SpringUtil.isNotEmpty(logList), "删除数据不能为空");
        CommodityFreezeGroupEntryLog logInfo = logList.get(0);
        List<CommodityDetailODTO> commodityList = commodityClient.findCommodityInfoByIdList( Collections.singletonList(logInfo.getCommodityId()));
        if(SpringUtil.isNotEmpty(commodityList)){
            CommodityDetailODTO commInfo = commodityList.get(0);
            logInfo.setWorkShopName(commInfo.getWorkshopName());
            logInfo.setFactoryName(commInfo.getFactoryName());
            logInfo.setOperaTime(DateUtil.get4yMdHms(new Date()));
            logInfo.setOperaUserName(userName);
        }
        logInfo.setOperaType(OperateTypeEnums.删除.getCode());
        // 删除数据
        commodityFreezeGroupMapper.deleteByPrimaryKey(id);

        //日志数据发消息
        sendLogService.sendLog(logList, "t_log_commodity_freeze_group");
        return true;
    }


    public List<Long> findCommodityFreezeGroupList(List<Long> commodityIds) {
        Example example = new Example(CommodityFreezeGroupEntry.class);
        example.createCriteria().andIn("commodityId", commodityIds);

        List<CommodityFreezeGroupEntry> commodityFreezeGroupEntries = commodityFreezeGroupMapper.selectByExample(example);
        return commodityFreezeGroupEntries.stream().map(CommodityFreezeGroupEntry::getCommodityId).collect(Collectors.toList());
    }
}
