package com.pinshang.qingyun.order.mapper;

import com.pinshang.qingyun.base.mybatis.MyMapper;
import com.pinshang.qingyun.order.model.order.XdaShoppingCart;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;


@Repository
public interface XdaShoppingCartMapper extends MyMapper<XdaShoppingCart>{

   int deleteShopCart(@Param("storeId") Long storeId, @Param("idList") List<Long> idList);

   int deleteShopCartWithCommodityType(@Param("storeId") Long storeId,
                                       @Param("idList") List<Long> idList,
                                       @Param("commodityType")Integer commodityType);

   List<Long> shopCartIdsNotInThCommoditySet(@Param("storeId") Long storeId,@Param("commodityType")Integer commodityType);
}