package com.pinshang.qingyun.order.mapper;

import com.pinshang.qingyun.order.mapper.entry.externalDocking.TescoEntry;
import com.pinshang.qingyun.order.vo.externalDocking.TescoVo;
import org.apache.ibatis.annotations.Mapper;
import org.springframework.stereotype.Repository;

import java.util.List;

@Mapper
@Repository
public interface TescoMapper {

    /**
     * 乐购台账列表查询
     * @param vo
     * @return
     */
    List<TescoEntry> findTescoList(TescoVo vo);

}
