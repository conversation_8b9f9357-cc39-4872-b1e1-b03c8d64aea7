package com.pinshang.qingyun.order.service;

import com.pinshang.qingyun.box.utils.SpringUtil;
import com.pinshang.qingyun.order.mapper.PromotionStkCodeOrderMapper;
import com.pinshang.qingyun.order.model.gift.GiftModelCondition;
import com.pinshang.qingyun.order.model.promotionStk.PromotionStkCodeOrder;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;

import java.math.BigDecimal;
import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * @Author: sk
 * @Date: 2024/7/18
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class PromotionStkCodeOrderService {


    private final PromotionStkCodeOrderMapper promotionStkCodeOrderMapper;

    static final int BINARY_SEARCH_THRESHOLD = 8;

    public List<PromotionStkCodeOrder> findListByPromotionId(Long promotionId){

        Example example = new Example(PromotionStkCodeOrder.class);
        example.createCriteria().andEqualTo("promotionId",promotionId);
        return promotionStkCodeOrderMapper.selectByExample(example);
    }

    public List<PromotionStkCodeOrder> listByPromotionIds(List<Long> promotionIdList){
        if(SpringUtil.isEmpty(promotionIdList)){
            return Collections.emptyList();
        }

        Example example = new Example(PromotionStkCodeOrder.class);
        example.createCriteria().andIn("promotionId",promotionIdList);
        return promotionStkCodeOrderMapper.selectByExample(example);
    }

    /**
     * 获取一个有效的配货条件
     *
     * @return
     */
    public PromotionStkCodeOrder getOnePromotionStkCodeOrder(List<PromotionStkCodeOrder> promotionStkCodeOrderList, BigDecimal orderTotalPrice) {
        if(promotionStkCodeOrderList.size() > BINARY_SEARCH_THRESHOLD) {

            int left = 0;
            int right = promotionStkCodeOrderList.size() - 1;
            while (left <= right) {
                int mid = left + (right - left) / 2;
                PromotionStkCodeOrder midItem = promotionStkCodeOrderList.get(mid);
                if (orderTotalPrice.compareTo(BigDecimal.valueOf(midItem.getOrderReach())) >= 0) {
                    left = mid + 1;
                } else {
                    right = mid - 1;
                }
            }
            return left > 0 ? promotionStkCodeOrderList.get(left - 1) : null;
        }else if (promotionStkCodeOrderList.size() > 1) {
            PromotionStkCodeOrder result = null;
            for (PromotionStkCodeOrder item : promotionStkCodeOrderList) {
                if (orderTotalPrice.compareTo(BigDecimal.valueOf(item.getOrderReach())) >= 0) {
                    result = item;
                }
            }
            return result;
        } else {
            PromotionStkCodeOrder promotionStkCodeOrder = promotionStkCodeOrderList.get(0);
            if (orderTotalPrice.compareTo(BigDecimal.valueOf(promotionStkCodeOrder.getOrderReach())) >= 0) {
                return promotionStkCodeOrder;
            }
        }

        return null;
    }


}
