package com.pinshang.qingyun.order.mapper;

import com.pinshang.qingyun.base.mybatis.MyMapper;
import com.pinshang.qingyun.order.model.tob.ToBProcessOrderCommodityStatistics;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;


@Repository
public interface ProcessOrderCommodityStatisticsMapper extends MyMapper<ToBProcessOrderCommodityStatistics> {
    int updateStatistics(ToBProcessOrderCommodityStatistics statistics);

    List<ToBProcessOrderCommodityStatistics> selectByCommodityIdAndDate(@Param("commodityIdList") List<Long> commodityIdList, @Param("beginDate") String beginDate, @Param("endDate") String endDate);

}