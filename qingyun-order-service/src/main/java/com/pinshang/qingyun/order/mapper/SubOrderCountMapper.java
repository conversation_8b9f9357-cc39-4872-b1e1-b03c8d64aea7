package com.pinshang.qingyun.order.mapper;

import com.pinshang.qingyun.base.mybatis.MyMapper;
import com.pinshang.qingyun.order.dto.ShopCountStockDetailIDTO;
import com.pinshang.qingyun.order.dto.ShopCountStockDetailItemODTO;
import com.pinshang.qingyun.order.dto.ShopCountStockPageODTO;
import com.pinshang.qingyun.order.model.order.SubOrderItemCount;
import com.pinshang.qingyun.order.vo.order.CountCommodityInfoVo;
import com.pinshang.qingyun.order.vo.order.OrderCountVo;
import com.pinshang.qingyun.order.vo.order.ShopOrderNumVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 */
@Mapper
@Repository
public interface SubOrderCountMapper extends MyMapper<SubOrderItemCount> {

    /**
     * 查询门店的订货数量和已点数量
     * @param storeId
     * @param date
     * @return
     */
    ShopOrderNumVo shopOrderNum(@Param("storeId") Long storeId, @Param("date") String date, @Param("shopId") Long shopId);

    /**
     * 查询商品信息
     * @param storeId
     * @param date
     * @param commodityId
     * @return
     */
    CountCommodityInfoVo countCommodityInfo(@Param("storeId") Long storeId, @Param("date") String date, @Param("commodityId") Long commodityId);

    /**
     * 查询商品的订货信息
     * @param storeId
     * @param date
     * @param commodityIds
     * @return
     */
    List<OrderCountVo> subOrderItemInfo(@Param("storeId") Long storeId, @Param("date") String date, @Param("commodityIds") List<Long> commodityIds);

    /**
     * 查询商品已经清点的数量
     * @param shopId
     * @param date
     * @param commodityIds
     * @return
     */
    List<SubOrderItemCount> countCommodityList(@Param("shopId") Long shopId, @Param("date") String date, @Param("commodityIds") List<Long> commodityIds);

    /**
     * 查看已点商品列表
     * @param shopId
     * @param date
     * @return
     */
    List<CountCommodityInfoVo> todayCountCommodityList(@Param("shopId") Long shopId, @Param("date") String date, @Param("commodityId") Long commodityId);

    /**
     * 分页查询送货点数
     * @param shopIdList
     * @param commodityIdList
     * @param beginTime
     * @param endTime
     * @return
     */
    List<ShopCountStockPageODTO> countCommodityListForPage(@Param("shopIdList") List<Long> shopIdList,
                                                           @Param("beginTime") String beginTime, @Param("endTime") String endTime);

    /**
     * 分页查询门店下商品送货点数
     * @param shopIdList
     * @param commodityIdList
     * @param beginTime
     * @param endTime
     * @return
     */
    List<ShopCountStockDetailItemODTO> countCommodityListForDetail(ShopCountStockDetailIDTO idto);
}
