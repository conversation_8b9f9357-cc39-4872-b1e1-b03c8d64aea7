package com.pinshang.qingyun.order.service.xda.v4;

import com.alibaba.fastjson.JSON;
import com.github.pagehelper.PageInfo;
import com.pinshang.qingyun.base.configure.expand.QYAssert;
import com.pinshang.qingyun.base.enums.MtBusinessModelEnum;
import com.pinshang.qingyun.base.enums.XSCouponUserStatusEnums;
import com.pinshang.qingyun.box.utils.BeanCloneUtils;
import com.pinshang.qingyun.marketing.dto.mtCoupon.*;
import com.pinshang.qingyun.marketing.service.mtCoupon.MtCouponTobClient;
import com.pinshang.qingyun.order.dto.shopcart.v4.ShoppingCartCommodityV4ODTO;
import com.pinshang.qingyun.order.dto.shopcart.v4.ShoppingCartGroupV4ODTO;
import com.pinshang.qingyun.order.dto.shopcart.v4.ShoppingCartV4ODTO;
import com.pinshang.qingyun.order.mapper.OrderListMapper;
import com.pinshang.qingyun.order.mapper.OrderMapper;
import com.pinshang.qingyun.order.model.order.Order;
import com.pinshang.qingyun.order.model.order.OrderList;
import com.pinshang.qingyun.xda.product.dto.commodityText.SelectXdaCommodityInfoListIDTO;
import com.pinshang.qingyun.xda.product.dto.commodityText.XdaCommodityInfoODTO;
import com.pinshang.qingyun.xda.product.service.XdaCommodityTextClient;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import tk.mybatis.mapper.entity.Example;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <p>
 * 优惠券  服务service
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-19
 */
@Slf4j
@Service
public class OrderMtCouponService {

    @Autowired
    private MtCouponTobClient mtCouponTobClient;

    @Autowired
    private OrderListMapper orderListMapper;
    @Autowired
    private OrderMapper orderMapper;

    @Autowired
    private XdaCommodityTextClient xdaCommodityTextClient;

    /**
     * 计算优惠券优惠
     */
    public MtOrderODTO processCoupon(Long storeId, Long couponUserId, ShoppingCartV4ODTO shoppingCart) {
        // 过滤不可用券的商品并组装订单信息  后台设置不可用券/特惠/赠品
        List<MtOrderInfoIDTO> orderInfos = getOrderInfos(shoppingCart);

        // 如果有可参与优惠券的订单，向营销系统请求计算优惠
        if (!CollectionUtils.isEmpty(orderInfos)) {
            // 构建订单对象 用于获取优惠券优惠
            MtOrderIDTO mtOrderReq = new MtOrderIDTO();
            mtOrderReq.setUserId(storeId);
            mtOrderReq.setBusinessModel(MtBusinessModelEnum.XDA.getCode());
            mtOrderReq.setOrderInfos(orderInfos);
            mtOrderReq.setCouponUserId(couponUserId);
            MtOrderODTO mtOrderRes = mtCouponTobClient.processCoupon(mtOrderReq);
            log.info("计算优惠券优惠入参:{},计算结果：{}", JSON.toJSONString(mtOrderReq), JSON.toJSONString(mtOrderRes));
            return mtOrderRes;
        }
        return null;
    }

    /**
     * 将商品拆分成订单信息，处理特价商品
     */
    private List<MtOrderInfoIDTO> splitOrderInfo(ShoppingCartCommodityV4ODTO odto) {
        List<MtOrderInfoIDTO> orderInfos = new ArrayList<>();

        if (Objects.equals(odto.getIsSpecialPrice(), 1)) {
            // 处理特价商品
            if (Objects.nonNull(odto.getSpecialQuantity()) && odto.getSpecialQuantity().compareTo(BigDecimal.ZERO) > 0) {
                orderInfos.add(createOrderInfo(odto.getCommodityId(), odto.getSpecialPrice(), 1, odto.getSpecialQuantity(), odto.getXdaSecondCategoryId()));
            }
            if (Objects.nonNull(odto.getNormalQuantity()) && odto.getNormalQuantity().compareTo(BigDecimal.ZERO) > 0) {
                orderInfos.add(createOrderInfo(odto.getCommodityId(), odto.getCommodityPrice(), 2, odto.getNormalQuantity(), odto.getXdaSecondCategoryId()));
            }
        } else {
            // 处理促销或者普通商品
            BigDecimal price;
            BigDecimal deliveryPrice = odto.getDeliveryPrice();
            if (Objects.nonNull(deliveryPrice)) {
                if ("kg".equals(odto.getCommodityUnitName().trim())) {
                    price = deliveryPrice.multiply(BigDecimal.valueOf(2)).setScale(2, RoundingMode.HALF_UP);
                } else {
                    price = deliveryPrice;
                }
            } else {
                price = odto.getCommodityPrice();
            }
            orderInfos.add(createOrderInfo(odto.getCommodityId(), price, 3, odto.getQuantity(), odto.getXdaSecondCategoryId()));
        }

        return orderInfos;
    }

    /**
     * 创建订单信息
     *
     * @param type 1.特价 2.特价超限 3.正常 4.特惠 5.赠品
     */
    private MtOrderInfoIDTO createOrderInfo(Long commodityId, BigDecimal price, int type, BigDecimal quantity, Long xdaSecondCategoryId) {
        MtOrderInfoIDTO mtOrderInfoReq = new MtOrderInfoIDTO();
        mtOrderInfoReq.setCommodityId(commodityId);
        mtOrderInfoReq.setCommodityPrice(price);
        mtOrderInfoReq.setQuantity(quantity);
        mtOrderInfoReq.setCommodityOrderType(type);
        mtOrderInfoReq.setSecondCategoryId(xdaSecondCategoryId);
        return mtOrderInfoReq;
    }

    /**
     * 核销优惠券
     */
    public void useCoupon(Long couponUserId, Long storeId, String orderCode) {
        if (Objects.nonNull(couponUserId) && couponUserId > 0 && Objects.nonNull(storeId)) {
            MtCouponUserStatusIDTO idto = new MtCouponUserStatusIDTO();
            idto.setCouponUserId(couponUserId);
            idto.setStatus(XSCouponUserStatusEnums.USED.getCode());
            idto.setUserId(storeId);
            idto.setOrderCode(orderCode);
            mtCouponTobClient.markCouponUserStatus(idto);
        }
    }

    /**
     * 根据订单id 返还优惠券
     */
    public void refundCoupon(Long orderId, Long storeId) {
        QYAssert.isTrue(orderId != null, "订单id不能为空");
        Order order = orderMapper.selectByPrimaryKey(orderId);
        QYAssert.isTrue(order != null, "订单号不存在");

        Example orderListExample = new Example(OrderList.class);
        orderListExample.createCriteria().andEqualTo("orderId", orderId);
        List<OrderList> orderList = orderListMapper.selectByExample(orderListExample);
        List<OrderList> filterList = orderList.stream().filter(p -> p.getCouponUserId() != null).collect(Collectors.toList());

        // 订单里面使用了优惠券
        if (!CollectionUtils.isEmpty(filterList)) {
            MtCouponUserStatusIDTO idto = new MtCouponUserStatusIDTO();
            idto.setCouponUserId(filterList.get(0).getCouponUserId());
            idto.setStatus(XSCouponUserStatusEnums.UNUSED.getCode());
            idto.setUserId(storeId);
            idto.setOrderCode(order.getOrderCode());
            mtCouponTobClient.markCouponUserStatus(idto);
        }
    }

    /**
     * 订单异常 返还优惠券
     */
    public void refundCoupon(String orderCode, Long couponUserId, Long storeId) {
        if (Objects.nonNull(couponUserId) && StringUtils.isNotBlank(orderCode) && Objects.nonNull(storeId)) {
            MtCouponUserStatusIDTO idto = new MtCouponUserStatusIDTO();
            idto.setCouponUserId(couponUserId);
            idto.setStatus(XSCouponUserStatusEnums.UNUSED.getCode());
            idto.setUserId(storeId);
            idto.setOrderCode(orderCode);
            mtCouponTobClient.markCouponUserStatus(idto);
        }
    }

    /**
     * 订单页优惠券列表
     */
    public MtOrderCouponODTO orderCouponList(Long storeId, Long couponUserId, ShoppingCartV4ODTO shoppingCart) {
        // 过滤不可用券的商品并组装订单信息  后台设置不可用券/特惠/赠品
        List<MtOrderInfoIDTO> orderInfos = getOrderInfos(shoppingCart);
        if (CollectionUtils.isEmpty(orderInfos)) {
            //如果没有可用的商品，那用户所有未使用的券都在不可用列表中
            return fetchUnavailableCoupons(storeId);
        }
        MtOrderIDTO mtOrderReq = new MtOrderIDTO();
        mtOrderReq.setCouponUserId(couponUserId);
        mtOrderReq.setUserId(storeId);
        mtOrderReq.setOrderInfos(orderInfos);
        mtOrderReq.setBusinessModel(MtBusinessModelEnum.XDA.getCode());
        return mtCouponTobClient.orderCouponList(mtOrderReq);
    }

    /**
     * 获取客户不可用的优惠券列表
     */
    private MtOrderCouponODTO fetchUnavailableCoupons(Long storeId) {
        MtMyCouponUserIDTO couponRequest = new MtMyCouponUserIDTO();
        couponRequest.setStatus(0);
        couponRequest.setStoreId(storeId);
        couponRequest.setPageNo(1);
        couponRequest.setPageSize(Integer.MAX_VALUE);
        PageInfo<MtMyCouponUserODTO> couponPageInfo = mtCouponTobClient.queryMtUserCouponList(couponRequest);
        List<MtMyCouponUserODTO> list = couponPageInfo.getList();
        if (CollectionUtils.isEmpty(list)) {
            return null;
        }
        MtOrderCouponODTO orderCouponData = new MtOrderCouponODTO();
        orderCouponData.setAvailableCouponInfos(Collections.emptyList());
        List<MtCouponODTO> unavailableCouponInfos = new ArrayList<>();
        for (MtMyCouponUserODTO userCoupon : list) {
            MtCouponODTO couponData = BeanCloneUtils.copyTo(userCoupon, MtCouponODTO.class);
            unavailableCouponInfos.add(couponData);
        }
        orderCouponData.setUnavailableCouponInfos(unavailableCouponInfos);
        return orderCouponData;
    }

    private List<MtOrderInfoIDTO> getOrderInfos(ShoppingCartV4ODTO shoppingCart) {
        //设置前台分类ID，从product查出来的是后台分类ID，因为促销要后台的，但是优惠券还是要前台的
        setFrontCategoryId(shoppingCart);

        List<ShoppingCartCommodityV4ODTO> shoppingCartCommodityList = shoppingCart.getNormalGroup().getCommodities();
        List<MtOrderInfoIDTO> orderInfos = new ArrayList<>();
        if (!CollectionUtils.isEmpty(shoppingCartCommodityList)) {
            shoppingCartCommodityList.stream()
                    .filter(commodity -> !BooleanUtils.isTrue(commodity.getIsGift()) && !Objects.equals(commodity.getEnableCoupon(), 0))
                    .forEach(commodity -> orderInfos.addAll(splitOrderInfo(commodity)));
        }
        List<ShoppingCartGroupV4ODTO> promotionGroup = shoppingCart.getPromotionGroup();
        promotionGroup.stream().filter(group -> !CollectionUtils.isEmpty(group.getCommodities()) && Objects.nonNull(group.getPromotionId()))
                .forEach(group -> group.getCommodities().stream()
                        .filter(commodity -> !BooleanUtils.isTrue(commodity.getIsGift()) && !Objects.equals(commodity.getEnableCoupon(), 0))
                        .forEach(commodity -> orderInfos.addAll(splitOrderInfo(commodity))));
        return orderInfos;
    }


    private void setFrontCategoryId(ShoppingCartV4ODTO shoppingCart) {
        if (shoppingCart == null) {
            return;
        }

        List<ShoppingCartCommodityV4ODTO> normalCommodities = Optional.ofNullable(shoppingCart.getNormalGroup())
                .map(ShoppingCartGroupV4ODTO::getCommodities)
                .orElse(Collections.emptyList());

        List<ShoppingCartCommodityV4ODTO> promotionCommodities = Optional.ofNullable(shoppingCart.getPromotionGroup())
                .orElse(Collections.emptyList())
                .stream()
                .flatMap(group -> Optional.ofNullable(group.getCommodities()).orElse(Collections.emptyList()).stream())
                .collect(Collectors.toList());

        List<Long> allCommodityIds = Stream.concat(
                normalCommodities.stream().map(ShoppingCartCommodityV4ODTO::getCommodityId),
                promotionCommodities.stream().map(ShoppingCartCommodityV4ODTO::getCommodityId)
        ).collect(Collectors.toList());

        if (CollectionUtils.isEmpty(allCommodityIds)) {
            return;
        }

        // 查询前台品类 ID
        SelectXdaCommodityInfoListIDTO dto = new SelectXdaCommodityInfoListIDTO();
        dto.setCommodityIdList(allCommodityIds);
        List<XdaCommodityInfoODTO> result = xdaCommodityTextClient.selectXdaCommodityInfoList(dto);

        // 构建商品 ID 到前台分类 ID 的映射
        Map<Long, Long> resultMap = result.stream()
                .collect(Collectors.toMap(
                        XdaCommodityInfoODTO::getCommodityId,
                        XdaCommodityInfoODTO::getXdaSecondCategoryId,
                        (existing, replacement) -> existing
                ));

        // 统一处理普通商品和促销商品
        Stream.concat(normalCommodities.stream(), promotionCommodities.stream())
                .forEach(commodity -> {
                    Long frontCategoryId = resultMap.get(commodity.getCommodityId());
                    if (Objects.nonNull(frontCategoryId)) {
                        commodity.setXdaSecondCategoryId(frontCategoryId);
                    }
                });
    }


}
