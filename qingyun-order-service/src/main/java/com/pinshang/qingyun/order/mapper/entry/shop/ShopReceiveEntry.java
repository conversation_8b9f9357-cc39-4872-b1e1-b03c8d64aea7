package com.pinshang.qingyun.order.mapper.entry.shop;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2017/6/7.
 */
@Data
public class ShopReceiveEntry {

    private String id;

    private Integer logisticsModel;

    @ApiModelProperty("订单id")
    private String subOrderId;

    @ApiModelProperty("订单编号")
    private String subOrderCode;

    private BigDecimal totalPrice;

    private BigDecimal subOrderTotalPrice;

    private String supplierId;

    @ApiModelProperty("供应商")
    private String supplierName;

    private String status;

    @ApiModelProperty("送货日期")
    private Date orderTime;

    private Date receiveTime;

    private BigDecimal orderAmount;

    private String receiveId;

    private String realName;
    
    //下单时间
    private Date createTime;

    //门店类型
    private Integer shopType;

    //下单人
    private String createName;

    private Long createId;
    private Long receiveUserId;
    private Long storeId;

    private Long stallId;
    private String stallName;
}
