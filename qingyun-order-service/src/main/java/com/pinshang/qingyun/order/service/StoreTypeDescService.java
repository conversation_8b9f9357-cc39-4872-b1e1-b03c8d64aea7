package com.pinshang.qingyun.order.service;

import com.pinshang.qingyun.base.configure.expand.QYAssert;
import com.pinshang.qingyun.box.utils.SpringUtil;
import com.pinshang.qingyun.store.dto.storeType.StoreTypeDescIDTO;
import com.pinshang.qingyun.store.dto.storeType.StoreTypeDescODTO;
import com.pinshang.qingyun.store.service.StoreTypeClient;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class StoreTypeDescService {

    private final StoreTypeClient storeTypeClient;


    public StoreTypeDescService(StoreTypeClient storeTypeClient) {
        this.storeTypeClient = storeTypeClient;
    }

    public StoreTypeDescODTO selectByStoreTypeId(Long storeTypeId){
        QYAssert.isTrue(storeTypeId != null,"客户类型id不能为空");
        StoreTypeDescIDTO storeTypeDescIDTO = new StoreTypeDescIDTO();
        storeTypeDescIDTO.setId(storeTypeId);
        List<StoreTypeDescODTO> storeTypeDescODTOS = storeTypeClient.selectStoreTypeData(storeTypeDescIDTO);
        if(SpringUtil.isNotEmpty(storeTypeDescODTOS)){
            return storeTypeDescODTOS.get(0);
        }
        return null;
    }
}
