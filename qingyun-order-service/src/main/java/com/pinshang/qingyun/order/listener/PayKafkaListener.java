package com.pinshang.qingyun.order.listener;

import com.alibaba.fastjson.JSON;
import com.google.common.util.concurrent.ThreadFactoryBuilder;
import com.pinshang.qingyun.base.annotations.OnlineSwitchWatcher;
import com.pinshang.qingyun.base.api.QYApplicationContext;
import com.pinshang.qingyun.base.enums.YesOrNoEnums;
import com.pinshang.qingyun.base.enums.pay.AppCodeEnum;
import com.pinshang.qingyun.base.enums.pay.PayTypeIntEnum;
import com.pinshang.qingyun.base.enums.xd.XdPayTypeEnum;
import com.pinshang.qingyun.box.utils.JsonUtil;
import com.pinshang.qingyun.kafka.base.BaseKafkaOnlineSwitchProcessor;
import com.pinshang.qingyun.kafka.base.KafkaMessageWrapper;
import com.pinshang.qingyun.kafka.base.KafkaTopicConstant;
import com.pinshang.qingyun.order.constant.RedissKeyConstant;
import com.pinshang.qingyun.order.dto.CallBackIDTO;
import com.pinshang.qingyun.order.model.order.XdaPreOrder;
import com.pinshang.qingyun.order.service.WeChatSendMessageService;
import com.pinshang.qingyun.order.service.pay.callback.PfThirdPartyPayCallbackEvent;
import com.pinshang.qingyun.order.service.pay.callback.ThirdPartyPayCallbackEvent;
import com.pinshang.qingyun.order.service.pf.recharge.PfRechargeService;
import com.pinshang.qingyun.order.service.recharge.RechargeService;
import com.pinshang.qingyun.order.service.xda.v4.XdaPreOrderService;
import com.pinshang.qingyun.order.util.PayTypeConvertUtil;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.List;
import java.util.concurrent.ScheduledThreadPoolExecutor;
import java.util.concurrent.ThreadFactory;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * qingyun-pay 支付消息通知
 */
@Component
@Slf4j
@OnlineSwitchWatcher
public class PayKafkaListener extends BaseKafkaOnlineSwitchProcessor {

    @Autowired
    private RechargeService rechargeService;
    
    @Autowired
    private PfRechargeService pfRechargeService;
    @Autowired
    private XdaPreOrderService xdaPreOrderService;
    @Autowired
    private RedissonClient redissonClient;
    @Autowired
    private WeChatSendMessageService weChatSendMessageService;

    private static ThreadFactory namedThreadFactory = new ThreadFactoryBuilder()
            .setNameFormat("process-Pool").build();
    private static ScheduledThreadPoolExecutor POOL= new ScheduledThreadPoolExecutor(4,namedThreadFactory);
    @Override
    public List<String> getKafkaIds() {
        return Arrays.asList(
                QYApplicationContext.applicationNameSwitch + KafkaTopicConstant.PAY_CALLBACK_TOPIC + "xdOrderKafkaListenerContainerFactory"
        );
    }

    @org.springframework.kafka.annotation.KafkaListener(topics = "${application.name.switch}" + KafkaTopicConstant.PAY_CALLBACK_TOPIC
            ,errorHandler = "kafkaConsumerErrorHandler"
            ,containerFactory = "kafkaListenerContainerFactory")
    public void onPayChangeMessage(String message) {
        String msg = "topic:" + KafkaTopicConstant.PAY_CALLBACK_TOPIC + " ====================message==" + message;
        log.warn(msg);

        KafkaMessageWrapper messageWrapper = JSON.parseObject(message, KafkaMessageWrapper.class);
        CallBackIDTO idto = JsonUtil.json2java(messageWrapper.getData().toString(), CallBackIDTO.class);

        RLock lock = redissonClient.getLock(RedissKeyConstant.PAY_CALL_BACK + idto.getOrderCode());
        lock.lock(5L, TimeUnit.SECONDS);
        try {

            if (idto.getAppCode().equals(AppCodeEnum.XDA.getCode())) {
                XdPayTypeEnum xdPayTypeEnum = PayTypeConvertUtil.payTypeTOXdaPayType(idto.getPayType());

                // 支付回调，账户充值
                ThirdPartyPayCallbackEvent build = ThirdPartyPayCallbackEvent.build(idto.getTransactionId(), idto.getOrderCode(), idto.getJson(), true, xdPayTypeEnum, idto.getPayTime(), idto.getAttach());
                rechargeService.callBackEvent(build);
                // 走事务查询预订单
                XdaPreOrder xdaPreOrder = xdaPreOrderService.queryXdaPreOrderByBillCode(idto.getOrderCode());
                if(xdaPreOrder != null){
                    if(YesOrNoEnums.NO.getCode().equals(xdaPreOrder.getPayStatus())){
                        xdaPreOrderService.xdaPreOrderToOrder(build, xdaPreOrder);
                    }else {
                        log.warn("预订单状态异常,orderCode {}  status {}", idto.getOrderCode(), xdaPreOrder.getPayStatus());
                    }
                }else {
                    log.warn("预订单不存在,orderCode {}", idto.getOrderCode());
                }
            }

            if (idto.getAppCode().equals(AppCodeEnum.PF.getCode())) {
                PayTypeIntEnum payTypeEnum = PayTypeIntEnum.getByCode(idto.getPayType());
                pfRechargeService.callBackEvent(PfThirdPartyPayCallbackEvent.build(idto.getTransactionId(), idto.getOrderCode(), idto.getJson(), true,  payTypeEnum, idto.getPayTime()));
            }


            // 银联C扫B充值
            if (idto.getAppCode().equals(AppCodeEnum.UNION_C_TO_B.getCode())) {
                rechargeService.cToBAddRecharge(idto.getAttach().getShopId(), idto.getOrderCode(), idto.getPayAmount(), idto.getPayTime());
            }

        } catch (Exception e) {
            StringBuffer sb = new StringBuffer();
            sb.append("支付回调异常,billCode:" + idto.getOrderCode());
            //发送微信模板信息
            weChatSendMessageService.sendWeChatMessage(sb.toString());
            log.error("支付回调异常:{}", msg, e);
        }finally {
            if (lock.isLocked()){
                lock.unlock();
            }
        }

    }
}
