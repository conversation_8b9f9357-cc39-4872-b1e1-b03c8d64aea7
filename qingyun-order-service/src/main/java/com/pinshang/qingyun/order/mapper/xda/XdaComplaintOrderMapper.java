package com.pinshang.qingyun.order.mapper.xda;

import com.pinshang.qingyun.base.mybatis.MyMapper;
import com.pinshang.qingyun.order.dto.xda.*;
import com.pinshang.qingyun.order.model.xda.XdaComplaint;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @Version 0.1.0
 * @Date 2020/12/30 15:21
 * @Copyright © 2019-2020 qm
 * @Description -- 类说明
 **/
@Repository
public interface XdaComplaintOrderMapper extends MyMapper<XdaComplaint> {
    /**
     * 投诉单列表 最多显示3个月的售后单
     * @param dto
     * @return
     */
    List<XdaComplaintOrderODTO> queryComplaintOrderList(QueryXdaComplaintDTO dto);

    /**
     * 更新
     * @param xdaComplaint
     * @return
     */
    int updateXdaComplaintOrder(XdaComplaint xdaComplaint);

    List<XdaComplaintCommodityItemDTO> queryComplaintCommodity(@Param("dto") XdaComplaintCommodityDTO xdaComplaintCommodityDTO);

    List<XdaComplaintCommodityItemDTO> queryComplaintCompleteCommodity(@Param("storeId") Long storeId,@Param("deliveryDate")String deliveryDate, @Param("complaintType") Integer complaintType, @Param("commodityIdList") List<Long> commodityIdList);

    List<XdaComplaintCommodityQuantityDTO> queryComplaintCompleteCommodityQuantity(@Param("storeId") Long storeId,@Param("deliveryDate")String deliveryDate, @Param("complaintType") Integer complaintType, @Param("commodityIdList") List<Long> commodityIdList);

    XdaComplaint queryComplaint(@Param("storeId") Long storeId, @Param("deliveryDate") String deliveryDate, @Param("complaintType") Integer complaintType);

    /**
     * 根据客户id和送货时间查询订单中的送货员
     * @param storeId
     * @param deliveryDate
     * @return
     */
    XdaComplaintDeliveryManODTO findOrderDeliveryMan(@Param("storeId") Long storeId,@Param("deliveryDate") String deliveryDate);

    /**
     * 查询客户表送货员信息
     * @param storeId
     * @return
     */
    XdaComplaintDeliveryManODTO findStoreDeliveryMan(@Param("storeId") Long storeId);
}
