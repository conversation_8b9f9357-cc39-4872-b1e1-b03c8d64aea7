package com.pinshang.qingyun.order.mapper.entry.order;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

public class OrderListEntry {
	
	private Long orderId;
	private Long subOrderId;
	private Long supplierId;
	private String supplierName;//供应商名称
	private Date createTime;//订单创建时间
	private Date orderTime;
	private String orderCode;//订单编码
	private BigDecimal orderAmount;//订单总金额
	private Integer orderStatus;//订单状态(订单状态(0正常,1取消))
	private Integer logisticsStatus;//物流状态(状态:0-待收货, 1-待审核, 2-审核未通过,3-审核通过)
	private Integer logisticsModel;//物流模式( 0-直送, 1-配送, 2-直通)
	
	
	private Date approvingTime;//审核时间
	private Long approvingId;//审核人
	private Long realOrderCode;//订单code


	private String createName;//订单创建人
	
	
	private List<OrderItemEntry> orderItems;
	
	private String deliveryBatchRemark;
	private Long warehouseId;

	private String warehouseName;
	private Long stallId;
	private String stallName;

	public String getStallName() {
		return stallName;
	}

	public void setStallName(String stallName) {
		this.stallName = stallName;
	}

	public Long getStallId() {
		return stallId;
	}

	public void setStallId(Long stallId) {
		this.stallId = stallId;
	}

	public Long getOrderId() {
		return orderId;
	}

	public void setOrderId(Long orderId) {
		this.orderId = orderId;
	}

	public Long getSubOrderId() {
		return subOrderId;
	}

	public void setSubOrderId(Long subOrderId) {
		this.subOrderId = subOrderId;
	}

	public String getSupplierName() {
		return supplierName;
	}

	public void setSupplierName(String supplierName) {
		this.supplierName = supplierName;
	}

	public Date getCreateTime() {
		return createTime;
	}

	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}

	public String getOrderCode() {
		return orderCode;
	}

	public void setOrderCode(String orderCode) {
		this.orderCode = orderCode;
	}

	public BigDecimal getOrderAmount() {
		return orderAmount;
	}

	public void setOrderAmount(BigDecimal orderAmount) {
		this.orderAmount = orderAmount;
	}

	public Integer getOrderStatus() {
		return orderStatus;
	}

	public void setOrderStatus(Integer orderStatus) {
		this.orderStatus = orderStatus;
	}

	public Integer getLogisticsStatus() {
		return logisticsStatus;
	}

	public void setLogisticsStatus(Integer logisticsStatus) {
		this.logisticsStatus = logisticsStatus;
	}

	public Integer getLogisticsModel() {
		return logisticsModel;
	}

	public void setLogisticsModel(Integer logisticsModel) {
		this.logisticsModel = logisticsModel;
	}

	public List<OrderItemEntry> getOrderItems() {
		return orderItems;
	}

	public void setOrderItems(List<OrderItemEntry> orderItems) {
		this.orderItems = orderItems;
	}

	public Date getApprovingTime() {
		return approvingTime;
	}

	public void setApprovingTime(Date approvingTime) {
		this.approvingTime = approvingTime;
	}

	public Long getApprovingId() {
		return approvingId;
	}

	public void setApprovingId(Long approvingId) {
		this.approvingId = approvingId;
	}

	public Long getRealOrderCode() {
		return realOrderCode;
	}

	public void setRealOrderCode(Long realOrderCode) {
		this.realOrderCode = realOrderCode;
	}

	public String getCreateName() {
		return createName;
	}

	public void setCreateName(String createName) {
		this.createName = createName;
	}

	public String getDeliveryBatchRemark() {
		return deliveryBatchRemark;
	}

	public void setDeliveryBatchRemark(String deliveryBatchRemark) {
		this.deliveryBatchRemark = deliveryBatchRemark;
	}

	public Long getSupplierId() {
		return supplierId;
	}

	public void setSupplierId(Long supplierId) {
		this.supplierId = supplierId;
	}

	public Date getOrderTime() {
		return orderTime;
	}

	public void setOrderTime(Date orderTime) {
		this.orderTime = orderTime;
	}

	public Long getWarehouseId() {
		return warehouseId;
	}

	public void setWarehouseId(Long warehouseId) {
		this.warehouseId = warehouseId;
	}

	public String getWarehouseName() {
		return warehouseName;
	}

	public void setWarehouseName(String warehouseName) {
		this.warehouseName = warehouseName;
	}
}
