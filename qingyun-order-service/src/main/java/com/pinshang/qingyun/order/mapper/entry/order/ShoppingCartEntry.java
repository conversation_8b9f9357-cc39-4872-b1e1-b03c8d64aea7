package com.pinshang.qingyun.order.mapper.entry.order;

import com.pinshang.qingyun.base.enums.YesOrNoEnums;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

public class ShoppingCartEntry {
	//shoppingCartId
	private String id;
	private Long storeId;
	//公司(供应商id),如果是配送则是上海清美绿色食品有限公司, 供货时间则是客户下单时间;
	private String companyName;
	//配送模式
	private Integer logisticsModel;
	//仓库
	private Long warehouseId;
	private String warehouseName;
	//供货时间
	private Long supplyId;
	private String supplyTime;
	//供货开始时间
	private String supplyStartTime;
	//供货结束时间
	private String supplyEndTime;
	//商品金额
	private BigDecimal totalPrice;
	//明细
	private List<ShoppingCartItemEntry> items;
	//箱规
	private BigDecimal salesBoxCapacity;
	//客户下单时间
	private String beginTime;
	private String endTime;
	// 送货日期范围
	private String deleveryTimeRange;
	// 用户id
	private Long createId;

	/** 状态：2-开业前、0-暂停营业、1-营业中、3-永久停业，（原0-停用,1-启用） */
	private Integer shopStatus;

	private Date openDate;
	//店铺名字
	private String shopName;
	private Integer shopType;

	private String orderTime;
	private String deliveryBatch;

	private Integer autoStatus;
	private Boolean autoCommodity; // 是否自动订货

	private Long shoppingCartId;
	private Long shoppingCartItemId;
	private Long commodityId;
	private BigDecimal quantity;
	private Long consignmentId; // 代销商id
	private Integer adminStatus; // 是否管理员数据
	private Long stallId;
	private Integer managementMode;

	public Integer getManagementMode() {
		return managementMode;
	}

	public void setManagementMode(Integer managementMode) {
		this.managementMode = managementMode;
	}

	public Long getStallId() {
		return stallId;
	}
	public void setStallId(Long stallId) {
		this.stallId = stallId;
	}
	public Integer getAdminStatus() {
		return adminStatus;
	}
	public void setAdminStatus(Integer adminStatus) {
		this.adminStatus = adminStatus;
	}
	public Long getConsignmentId() {
		return consignmentId;
	}

	public void setConsignmentId(Long consignmentId) {
		this.consignmentId = consignmentId;
	}

	public BigDecimal getQuantity() {
		return quantity;
	}
	public void setQuantity(BigDecimal quantity) {
		this.quantity = quantity;
	}
	public Integer getAutoStatus() {
		return autoStatus;
	}
	public void setAutoStatus(Integer autoStatus) {
		this.autoStatus = autoStatus;
	}
	public Long getShoppingCartId() {
		return shoppingCartId;
	}
	public void setShoppingCartId(Long shoppingCartId) {
		this.shoppingCartId = shoppingCartId;
	}
	public Long getShoppingCartItemId() {
		return shoppingCartItemId;
	}
	public void setShoppingCartItemId(Long shoppingCartItemId) {
		this.shoppingCartItemId = shoppingCartItemId;
	}
	public Long getCommodityId() {
		return commodityId;
	}
	public void setCommodityId(Long commodityId) {
		this.commodityId = commodityId;
	}
	public Boolean getAutoCommodity() {
		if(autoStatus != null && autoStatus.equals(YesOrNoEnums.YES.getCode())){
			return true;
		}else {
			return false;
		}
	}

	public void setAutoCommodity(Boolean autoCommodity) {
		this.autoCommodity = autoCommodity;
	}

	public String getShopName() {
		return shopName;
	}
	public void setShopName(String shopName) {
		this.shopName = shopName;
	}
	public Integer getShopType() {
		return shopType;
	}
	public void setShopType(Integer shopType) {
		this.shopType = shopType;
	}
	public String getOrderTime() {
		return orderTime;
	}
	public void setOrderTime(String orderTime) {
		this.orderTime = orderTime;
	}
	public String getDeliveryBatch() {
		return deliveryBatch;
	}
	public void setDeliveryBatch(String deliveryBatch) {
		this.deliveryBatch = deliveryBatch;
	}

	public Long getStoreId() {
		return storeId;
	}

	public void setStoreId(Long storeId) {
		this.storeId = storeId;
	}

	public Date getOpenDate() {
		return openDate;
	}

	public void setOpenDate(Date openDate) {
		this.openDate = openDate;
	}

	public String getCompanyName() {
		return companyName;
	}
	public void setCompanyName(String companyName) {
		this.companyName = companyName;
	}
	public Integer getLogisticsModel() {
		return logisticsModel;
	}
	public void setLogisticsModel(Integer logisticsModel) {
		this.logisticsModel = logisticsModel;
	}
	public String getWarehouseName() {
		return warehouseName;
	}
	public void setWarehouseName(String warehouseName) {
		this.warehouseName = warehouseName;
	}
	public String getSupplyTime() {
		return supplyTime;
	}
	public void setSupplyTime(String supplyTime) {
		this.supplyTime = supplyTime;
	}
	public BigDecimal getTotalPrice() {
		return totalPrice;
	}
	public void setTotalPrice(BigDecimal totalPrice) {
		this.totalPrice = totalPrice;
	}
	public List<ShoppingCartItemEntry> getItems() {
		return items;
	}
	public void setItems(List<ShoppingCartItemEntry> items) {
		this.items = items;
	}
	public String getId() {
		return id;
	}
	public void setId(String id) {
		this.id = id;
	}
	public String getBeginTime() {
		return beginTime;
	}
	public void setBeginTime(String beginTime) {
		this.beginTime = beginTime;
	}
	public String getEndTime() {
		return endTime;
	}
	public void setEndTime(String endTime) {
		this.endTime = endTime;
	}
	public String getSupplyStartTime() {
		return supplyStartTime;
	}
	public void setSupplyStartTime(String supplyStartTime) {
		this.supplyStartTime = supplyStartTime;
	}
	public String getSupplyEndTime() {
		return supplyEndTime;
	}
	public void setSupplyEndTime(String supplyEndTime) {
		this.supplyEndTime = supplyEndTime;
	}
	public BigDecimal getSalesBoxCapacity() {
		return salesBoxCapacity;
	}
	public void setSalesBoxCapacity(BigDecimal salesBoxCapacity) {
		this.salesBoxCapacity = salesBoxCapacity;
	}

	public String getDeleveryTimeRange() {
		return deleveryTimeRange;
	}

	public void setDeleveryTimeRange(String deleveryTimeRange) {
		this.deleveryTimeRange = deleveryTimeRange;
	}

	public Long getSupplyId() {
		return supplyId;
	}

	public void setSupplyId(Long supplyId) {
		this.supplyId = supplyId;
	}

	public Long getWarehouseId() {
		return warehouseId;
	}

	public void setWarehouseId(Long warehouseId) {
		this.warehouseId = warehouseId;
	}

	public Long getCreateId() {
		return createId;
	}

	public void setCreateId(Long createId) {
		this.createId = createId;
	}

	public Integer getShopStatus() {
		return shopStatus;
	}

	public void setShopStatus(Integer shopStatus) {
		this.shopStatus = shopStatus;
	}
}
