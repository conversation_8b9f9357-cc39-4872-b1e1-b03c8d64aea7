package com.pinshang.qingyun.order.controller;


import com.github.pagehelper.PageInfo;
import com.pinshang.qingyun.base.api.TokenInfo;
import com.pinshang.qingyun.base.configure.expand.QYAssert;
import com.pinshang.qingyun.base.util.FastThreadLocalUtil;
import com.pinshang.qingyun.order.dto.order.SupplierBlackODTO;
import com.pinshang.qingyun.order.dto.order.SupplierBlackVo;
import com.pinshang.qingyun.order.mapper.entry.MdShopOrderSettingEntry;
import com.pinshang.qingyun.order.mapper.entry.MdShopOrderSettingLogEntry;
import com.pinshang.qingyun.order.model.shop.MdShopOrderSetting;
import com.pinshang.qingyun.order.service.MdShopOrderSettingService;
import com.pinshang.qingyun.order.vo.shop.*;
import com.pinshang.qingyun.renderer.annotation.MethodRender;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/mdShopOrderSetting")
public class MdShopOrderSettingController {

    @Autowired
    private MdShopOrderSettingService mdShopOrderSettingService;

    /**
     * 分页查询门店下单设置表
     * @param vo
     * @return
     */
    @PostMapping("/queryMdShopOrderSettingListByParams")
    public PageInfo<MdShopOrderSettingEntry> queryMdShopOrderSettingListByParams(@RequestBody MdShopOrderSettingVo vo){
        return mdShopOrderSettingService.queryMdShopOrderSettingListByParams(vo);
    }


    /**
     * 根据商品IDList，通用客户id -1 查询门店订货通用设置
     * @return
     */
    @RequestMapping(value = "/queryMdShopOrderSettingListByIds", method = RequestMethod.POST)
    public List<MdShopOrderSettingEntry> queryMdShopOrderSettingListByIds(@RequestBody List<String> commodityIds){
        QYAssert.notNull(commodityIds,"商品idList不能为空");
        return mdShopOrderSettingService.queryMdShopOrderSettingListByIds(-1L, commodityIds);
    }

    /**
     * 根据商品IDList，门店类型 查询门店订货通用设置
     * @param commodityIds
     * @return
     */
    @RequestMapping(value = "/queryMdShopOrderSettingListByShopType", method = RequestMethod.POST)
    public List<MdShopOrderSettingEntry> queryMdShopOrderSettingListByShopType(@RequestBody MdShopOrderSettingQueryVO queryVO){
        QYAssert.notNull(queryVO.getShopType(),"门店类型不能为空");
        QYAssert.isTrue(CollectionUtils.isNotEmpty(queryVO.getCommodityIds()), "商品idList不能为空");
        return mdShopOrderSettingService.getMdShopOrderSettingEntries(queryVO.getShopType(), queryVO.getCommodityIds());
    }

    /**
     * 根据主键ID查询门店订货通用设置
     * @param id
     * @return
     */
    @PostMapping("/findMdShopOrderSettingById")
    public MdShopOrderSetting findMdShopOrderSettingById(@RequestParam(value = "id",required = false) Long id){
        return mdShopOrderSettingService.findMdShopOrderSettingById(id);
    }

    /**
     * 更新门店订货通用设置
     * @param vo
     * @return
     */
    @PostMapping("/updateMdShopOrderSettingById")
    public Integer updateMdShopOrderSettingById(@RequestBody MdShopOrderSetting vo){
        return this.mdShopOrderSettingService.updateMdShopOrderSettingById(vo);
    }

    /**
     * 删除门店订货通用设置
     * @param id
     * @return
     */
    @PostMapping("/deleteMdShopOrderSettingById")
    public Integer deleteMdShopOrderSettingById(@RequestParam(value = "id",required = false) Long id,@RequestParam(value = "createId",required = false)Long createId){
        return mdShopOrderSettingService.deleteMdShopOrderSettingById(id,createId);
    }
    /**
     * 批量导入新增
     * @param list
     * @return
     */
    @RequestMapping(value = {"/saveMdShopOrderSettingList"}, method = {RequestMethod.POST})
    public  Integer saveMdShopOrderSettingList(@RequestBody List<MdShopOrderSettingVo> list) {
        return mdShopOrderSettingService.saveMdShopOrderSettingList(list);
    }

    /**
     *  分页查询门店下单设置日志表
     * @param vo
     * @return
     */
    @PostMapping("/queryMdShopOrderSettingLogListByParams")
    public PageInfo<MdShopOrderSettingLogEntry> queryMdShopOrderSettingLogListByParams(@RequestBody MdShopOrderSettingLogVo vo){
        return mdShopOrderSettingService.queryMdShopOrderSettingLogListByParams(vo);
    }

    /**
     * 处理门店订货通用设置(用户设置物流模式，设置首选供应商，修改供应商时间，设置首选仓库，修改仓库时间)
     * com.pinshang.qingyun.product.service.CommodityService#setLogistics() 商品设置物流模式
     * com.pinshang.qingyun.storage.service.commoditySupplier.impl.CommoditySupplierService#setDefaultSupplier()  设置首选供应商
     * com.pinshang.qingyun.storage.service.commoditySupplier.impl.CommoditySupplierService#syncToOrderSetting()  新增默认供应商
     * com.pinshang.qingyun.storage.service.commodityWarehouse.impl.CommodityWarehouseService#setDefaultWarehouse()  设置首选仓库
     * com.pinshang.qingyun.supplier.service.impl.SupplierService#updateSupplier() 修改供应商
     * com.pinshang.qingyun.storage.service.warehouse.impl.WarehouseService#updateWarehouseInfo() 更新仓库信息
     * @param vo
     * @return
     */
    @PostMapping("/dealMdShopOrderSetting")
    public Integer dealMdShopOrderSetting(@RequestBody ShopOrderSettingDealVo vo){
        return this.mdShopOrderSettingService.dealMdShopOrderSetting(vo);
    }

    /**
     * 批量处理门店订货通用设置(用于新增商品审核通过)
     * com.pinshang.qingyun.product.service.CommodityApplyService#commodityApprovalPass() 审批通过
     * com.pinshang.qingyun.product.service.CommodityApplyService#autoApprovalSaveCommodityApply() 自动审批新增商品申请
     * com.pinshang.qingyun.product.service.CommodityService#autoApprovalSaveList()  自动审批通过公共方法
     * com.pinshang.qingyun.product.service.CommodityService#autoApprovalSave() 新增商品自动审批保存
     * @param list
     * @return
     */
    @PostMapping("/batchDealMdShopOrderSettingDetails")
    public Integer batchDealMdShopOrderSettingDetails(@RequestBody List<ShopOrderSettingDetailsDealVo> list){
        return this.mdShopOrderSettingService.batchDealMdShopOrderSettingDetails(list);
    }

    @PostMapping("/queryMdShopOrderSettingListByCommodityIds")
    public List<MdShopOrderSettingEntry> queryMdShopOrderSettingListByCommodityIds(@RequestParam(value = "commodityIds",required = false) List<String> commodityIds, @RequestParam(value = "storeId",required = false) Long storeId){
        return mdShopOrderSettingService.queryMdShopOrderSettingListByCommodityIds(commodityIds, storeId);
    }


    /**
     * 分页查询日日鲜自动订货供应商黑名单
     * @param vo
     * @return
     */
    @MethodRender
    @PostMapping("/queryOrderSupplierBlackPage")
    @ApiOperation(value = "分页查询日日鲜自动订货供应商黑名单", notes = "分页查询日日鲜自动订货供应商黑名单",produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public PageInfo<SupplierBlackODTO> queryOrderSupplierBlackPage(@RequestBody SupplierBlackVo vo){
        return mdShopOrderSettingService.queryOrderSupplierBlackPage(vo);
    }


    @ApiOperation(value = "添加日日鲜自动订货供应商黑名单", notes = "添加日日鲜自动订货供应商黑名单")
    @PostMapping("addOrderSupplierBlack")
    public boolean addOrderSupplierBlack(@RequestParam("supplierId") Long supplierId){
        TokenInfo ti = FastThreadLocalUtil.getQY();
        return mdShopOrderSettingService.addOrderSupplierBlack(supplierId, ti.getUserId(), ti.getRealName());
    }

    @ApiOperation(value = "删除日日鲜自动订货供应商黑名单", notes = "删除日日鲜自动订货供应商黑名单")
    @PostMapping("deleteOrderSupplierBlack")
    public boolean deleteOrderSupplierBlack(@RequestParam("supplierId") Long supplierId){
        TokenInfo ti = FastThreadLocalUtil.getQY();
        return mdShopOrderSettingService.deleteOrderSupplierBlack(supplierId, ti.getUserId(), ti.getRealName());
    }

}
