package com.pinshang.qingyun.order.service;

import com.pinshang.qingyun.base.enums.BusinessTypeEnums;
import com.pinshang.qingyun.box.utils.JsonUtil;
import com.pinshang.qingyun.box.utils.SpringUtil;
import com.pinshang.qingyun.order.mapper.OrderMapper;
import com.pinshang.qingyun.order.mapper.SubOrderMapper;
import com.pinshang.qingyun.order.mapper.entry.order.SubOrderInfoListEntry;
import com.pinshang.qingyun.order.model.order.Order;
import com.pinshang.qingyun.order.vo.OrderRealSubOrderDoneStatusVO;
import com.pinshang.qingyun.storage.dto.deliveryOrder.OrderOutStockStatusIDTO;
import com.pinshang.qingyun.storage.dto.deliveryOrder.OrderOutStockStatusODTO;
import com.pinshang.qingyun.storage.service.DeliveryOrderClient;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/7/2 11:29
 */
@Service
@Slf4j
public class OrderRealSubOrderDoneStatusService {

    @Autowired
    private OrderMapper orderMapper;

    @Autowired
    private SubOrderMapper subOrderMapper;

    @Autowired
    private DeliveryOrderClient deliveryOrderClient;

    /***
     * JOB跑批
     * 订单数据要区分 是否通达销售  通达销售不需要过滤 物流模式为直送类型（直送类型进大仓）  其它B端需要过滤物流模式为直送类型  （直送类型不进大仓）
     * @param vo
     */
    public void processOrderRealSubOrderDoneStatus(OrderRealSubOrderDoneStatusVO vo) {

        if (StringUtils.isNotBlank(vo.getOrderIds())) {

            Set<Long> orderIdSet = new HashSet<>();
            for (String s : vo.getOrderIds().split(",")) {
                if (StringUtils.isNotBlank(s)) {
                    orderIdSet.add(Long.valueOf(s));
                }
            }
            if(SpringUtil.isEmpty(orderIdSet)){
                return;
            }
            List<SubOrderInfoListEntry> subOrderInfoList = orderMapper.selectOrderByOrderIdList(orderIdSet);
            if (SpringUtil.isEmpty(subOrderInfoList)) {
                return;
            }
            List<Long> orderIdList = new ArrayList<>();
            List<Long> tdOrderIdList = new ArrayList<>();
            businessTypeForInsertList(subOrderInfoList, orderIdList, tdOrderIdList);
            tdBatchSetRealSubOrderDoneStatus(tdOrderIdList);
            batchSetRealSubOrderDoneStatus(orderIdList);
            return;
        }

        List<SubOrderInfoListEntry> subOrderInfoList = orderMapper.selectOrderIds(vo.getStartTime(), vo.getEndTime());
        if (SpringUtil.isEmpty(subOrderInfoList)) {
            return;
        }

        List<Long> orderIdList = new ArrayList<>();
        List<Long> tdOrderIdList = new ArrayList<>();
        businessTypeForInsertList(subOrderInfoList, orderIdList, tdOrderIdList);
        tdBatchSetRealSubOrderDoneStatus(tdOrderIdList);
        batchSetRealSubOrderDoneStatus(orderIdList);
    }

    /***
     * 区分那些订单是通达销售
     * @param subOrderInfoList
     * @param orderIdList
     * @param tdOrderIdList
     */
    private void businessTypeForInsertList(List<SubOrderInfoListEntry> subOrderInfoList, List<Long> orderIdList, List<Long> tdOrderIdList) {
        subOrderInfoList.stream().forEach(o -> {
            if (o.getBusinessType() != null && BusinessTypeEnums.TD_SALE.getCode().equals(o.getBusinessType())) {
                tdOrderIdList.add(o.getOrderId());
            } else {
                orderIdList.add(o.getOrderId());
            }
        });
    }

    /***
     * 通达销售的订单 不过滤直送物流模式
     * @param tdOrderList
     */
    private void tdBatchSetRealSubOrderDoneStatus(List<Long> tdOrderList) {
        if (SpringUtil.isEmpty(tdOrderList)) {
            return;
        }

        if (tdOrderList.size() <= 1000) {
            setRealSubOrderDoneStatus(selectSubOrderByOrderIds(tdOrderList, null));
            return;
        }
        List<Long> subList = new ArrayList<>();
        tdOrderList.stream().forEach(o -> {
            if (subList.size() == 1000) {
                setRealSubOrderDoneStatus(selectSubOrderByOrderIds(subList, null));
                subList.clear();
            }
            subList.add(o);
        });

        if (SpringUtil.isNotEmpty(subList)) {
            setRealSubOrderDoneStatus(selectSubOrderByOrderIds(subList, null));
            subList.clear();
        }

    }

    /***
     * B端订单过滤直送物流模式
     * @param orderList
     */
    private void batchSetRealSubOrderDoneStatus(List<Long> orderList) {

        if (SpringUtil.isEmpty(orderList)) {
            return;
        }
        if (orderList.size() <= 1000) {
            setRealSubOrderDoneStatus(selectSubOrderByOrderIds(orderList, 0));
            return;
        }
        List<Long> subList = new ArrayList<>();
        orderList.stream().forEach(o -> {
            if (subList.size() == 1000) {
                setRealSubOrderDoneStatus(selectSubOrderByOrderIds(subList, 0));
                subList.clear();
            }
            subList.add(o);
        });

        if (SpringUtil.isNotEmpty(subList)) {
            setRealSubOrderDoneStatus(selectSubOrderByOrderIds(subList, 0));
            subList.clear();
        }

    }

    /***
     * 根据订单查询子单
     * @param orderIds
     * @param logisticsModel
     * @return
     */
    private Map<Long, List<Long>> selectSubOrderByOrderIds(List<Long> orderIds, Integer logisticsModel) {
        List<SubOrderInfoListEntry> subOrderInfoList = subOrderMapper.selectSubOrderInfoListByOrderIds(orderIds, logisticsModel);
        if (SpringUtil.isEmpty(subOrderInfoList)) {
            return null;
        }
        return subOrderInfoList.stream().collect(Collectors.groupingBy(SubOrderInfoListEntry::getOrderId, Collectors.mapping(SubOrderInfoListEntry::getSubOrderId, Collectors.toList())));
    }

    /***
     * 调用大仓 返回订单是否出库完成 更新订单处理完成标识
     * @param orderMap
     */
    private void setRealSubOrderDoneStatus(Map<Long, List<Long>> orderMap) {

        if (SpringUtil.isEmpty(orderMap)) {
            return;
        }
        OrderOutStockStatusIDTO idto = new OrderOutStockStatusIDTO();
        idto.setOrderMap(orderMap);
        List<OrderOutStockStatusODTO> orderOutStockStatusList = deliveryOrderClient.queryOrderOutStockStatus(idto);
        if (SpringUtil.isEmpty(orderOutStockStatusList)) {
            return;
        }
        log.info("调用大仓查询出库完成的订单 req：{} \n 返回结果为：{}",JsonUtil.java2json(idto), JsonUtil.java2json(orderOutStockStatusList));
        Set<Long> orderIds = orderOutStockStatusList.stream().filter(o -> o.getOutStockStatus() != null && o.getOutStockStatus().booleanValue()).map(OrderOutStockStatusODTO::getOrderId).collect(Collectors.toSet());
        if (SpringUtil.isEmpty(orderIds)) {
            return;
        }
        Example orderExample = new Example(Order.class);
        orderExample.createCriteria().andIn("id", orderIds).andCondition("(real_sub_order_done_status IS NULL OR real_sub_order_done_status !=1)");
        Order order = new Order();
        order.setRealSubOrderDoneStatus(1);
        orderMapper.updateByExampleSelective(order, orderExample);
    }
}
