package com.pinshang.qingyun.order.service.cup.strategy;

import com.pinshang.qingyun.base.configure.expand.QYAssert;
import com.pinshang.qingyun.box.utils.DateTimeUtil;
import com.pinshang.qingyun.box.utils.SpringUtil;
import com.pinshang.qingyun.order.dto.cup.EditOrderDto;
import com.pinshang.qingyun.order.enums.CombTypeEnum;
import com.pinshang.qingyun.order.enums.ProductTypeEnums;
import com.pinshang.qingyun.order.mapper.OrderMapper;
import com.pinshang.qingyun.order.model.commodity.Commodity;
import com.pinshang.qingyun.order.model.order.Order;
import com.pinshang.qingyun.order.service.CommodityService;
import com.pinshang.qingyun.order.service.OrderService;
import com.pinshang.qingyun.order.service.cup.OrderCupService;
import com.pinshang.qingyun.price.dto.storePromotion.StorePromotionCommodityPriceODTO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.DependsOn;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

import static java.util.stream.Collectors.toList;

@Component
public class CopyOrderDetailStrategy extends AbstractOrderDetail implements OrderDetailStrategy{

    @Autowired
    OrderService orderService;

    @Autowired
    OrderMapper orderMapper;

    @Autowired
    CommodityService commodityService;

    @Autowired
    OrderCupService orderCupService;

    @Override
    public Order detailCheck(Long orderId) {

        Order order = orderService.findOrderById(orderId);
        QYAssert.isTrue(null != order, "无法复制该订单!");

        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.DATE, 1);
        order.setOrderTime(calendar.getTime());
        return order;
    }

    @Override
    public void processDetail(Order order, EditOrderDto dto) {
        List<Commodity> commoditylist = orderMapper.findCommodityByOrderId(order.getId(),null, ProductTypeEnums.PRODUCT.getCode());
        List<Commodity> processedCommoditylist = commoditylist.stream().filter(commodity -> CombTypeEnum.NOT_COMB.getCode() == commodity.getCombType()).collect(toList());
        if (commoditylist.size() != processedCommoditylist.size()) {
            dto.setCombTypeCommodityFilterFlag(true);//设置组合品过滤标识
        }

        List<Commodity> mergeProcessedCommoditylist = mergeCommodities(processedCommoditylist);
        dto.setCommoditylist(mergeProcessedCommoditylist);

        BigDecimal orderAmount = BigDecimal.ZERO;
        if (SpringUtil.isNotEmpty(processedCommoditylist)) {
            Map<Long, StorePromotionCommodityPriceODTO> storePromotionMap = commodityService.selectCommodityPromotionToMap(
                    mergeProcessedCommoditylist
                            .stream()
                            .map(Commodity::getId)
                            .distinct()
                            .collect(Collectors.toList()),order.getStoreId(),DateTimeUtil.formatDate(order.getOrderTime(), "yyyy-MM-dd"));

            orderCupService.processCommodityPromotion(mergeProcessedCommoditylist,storePromotionMap);
            dto.setCommoditylist(mergeProcessedCommoditylist);

            for (Commodity c : mergeProcessedCommoditylist) {
                if(c.getPromotionFlag() == 1){
                    BigDecimal lineOrderAmount =
                            c.getCommodityPrice().multiply(c.getPromotionCount())
                                    .add(c.getOriginalCommodityPrice().multiply(c.getNormalCount()))
                                    .setScale(2, BigDecimal.ROUND_HALF_UP);
                    orderAmount = orderAmount.add(lineOrderAmount);
                    c.setCommodityLinePrice(lineOrderAmount);
                }else{
                    BigDecimal lineOrderAmount = c.getCommodityPrice().multiply(c.getProductNumber()).setScale(2, BigDecimal.ROUND_HALF_UP);
                    orderAmount = orderAmount.add(lineOrderAmount);
                    c.setCommodityLinePrice(lineOrderAmount);
                }
            }
        }
        dto.setOrderAmount(orderAmount);
    }

    private List<Commodity> mergeCommodities(List<Commodity> commodities) {
        LinkedHashMap<Long, Commodity> commodityMap = new LinkedHashMap<>();

        for (Commodity commodity : commodities) {
            if (commodityMap.containsKey(commodity.getId())) {
                Commodity existingCommodity = commodityMap.get(commodity.getId());
                existingCommodity.setProductNumber(existingCommodity.getProductNumber().add(commodity.getProductNumber()));
            } else {
                commodityMap.put(commodity.getId(), commodity);
            }
        }

        return new ArrayList<>(commodityMap.values());
    }

    @Override
    public String getEditType() {
        return "copy";
    }
}
