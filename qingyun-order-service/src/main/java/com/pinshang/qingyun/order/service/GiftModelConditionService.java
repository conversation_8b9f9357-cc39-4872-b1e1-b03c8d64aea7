package com.pinshang.qingyun.order.service;

import com.pinshang.qingyun.box.utils.SpringUtil;
import com.pinshang.qingyun.order.mapper.GiftModelConditionMapper;
import com.pinshang.qingyun.order.mapper.GiftModelMapper;
import com.pinshang.qingyun.order.model.gift.GiftModel;
import com.pinshang.qingyun.order.model.gift.GiftModelCondition;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;

import java.util.Collections;
import java.util.List;

/**
 * @Author: sk
 * @Date: 2024/7/18
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class GiftModelConditionService {

    private final GiftModelConditionMapper giftModelConditionMapper;

    public List<GiftModelCondition> findListByGiftModelId(Long giftModelId){

        Example example = new Example(GiftModelCondition.class);
        example.createCriteria().andEqualTo("giftModelId",giftModelId);
        return giftModelConditionMapper.selectByExample(example);
    }

    public List<GiftModelCondition> listByGiftModelIds(List<Long> giftModelIdList){
        if(SpringUtil.isEmpty(giftModelIdList)){
            return Collections.emptyList();
        }

        Example example = new Example(GiftModelCondition.class);
        example.createCriteria().andIn("giftModelId",giftModelIdList);
        return giftModelConditionMapper.selectByExample(example);
    }


}
