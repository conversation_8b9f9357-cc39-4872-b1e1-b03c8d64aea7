package com.pinshang.qingyun.order.service;

import com.alibaba.fastjson.JSONObject;
import com.pinshang.qingyun.base.api.QYApplicationContext;
import com.pinshang.qingyun.base.api.TokenInfo;
import com.pinshang.qingyun.base.configure.expand.QYAssert;
import com.pinshang.qingyun.base.enums.YesOrNoEnums;
import com.pinshang.qingyun.base.util.FastThreadLocalUtil;
import com.pinshang.qingyun.box.utils.DateUtil;
import com.pinshang.qingyun.box.utils.JsonUtil;
import com.pinshang.qingyun.box.utils.SpringUtil;
import com.pinshang.qingyun.common.service.DictionaryClient;
import com.pinshang.qingyun.infrastructure.components.IMqSenderComponent;
import com.pinshang.qingyun.infrastructure.components.params.mq.MqMessage;
import com.pinshang.qingyun.kafka.base.KafkaMessageWrapper;
import com.pinshang.qingyun.kafka.base.KafkaTopicConstant;
import com.pinshang.qingyun.kafka.enums.KafkaMessageOperationTypeEnum;
import com.pinshang.qingyun.kafka.enums.KafkaMessageTypeEnum;
import com.pinshang.qingyun.order.mapper.CommodityMapper;
import com.pinshang.qingyun.order.mapper.ShopMapper;
import com.pinshang.qingyun.order.mapper.SubOrderCountMapper;
import com.pinshang.qingyun.order.mapper.entry.commodity.CommodityBasicEntry;
import com.pinshang.qingyun.order.model.order.SubOrderItemCount;
import com.pinshang.qingyun.order.model.shop.Shop;
import com.pinshang.qingyun.order.vo.commodity.CommodityVO;
import com.pinshang.qingyun.order.vo.order.*;
import com.pinshang.qingyun.smm.dto.org.OrgAndParentInfoODTO;
import com.pinshang.qingyun.smm.dto.org.SelectShopOrgInfoListIDTO;
import com.pinshang.qingyun.smm.service.OrgClient;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RBucket;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionSynchronizationAdapter;
import org.springframework.transaction.support.TransactionSynchronizationManager;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class SubOrderCountService {

    @Autowired
    private SubOrderCountMapper subOrderCountMapper;

    @Autowired
    private ShopMapper shopMapper;

    @Autowired
    private CommodityMapper commodityMapper;

    @Autowired
    private OrgClient orgClient;

    @Autowired
    private DictionaryClient dictionaryClient;

    @Autowired
    private IMqSenderComponent mqSenderComponent;

    @Autowired
    private RedissonClient redissonClient;

    private static String COMMODITY_COUNT_OCCUPY = "ORDER:COMMODITY_COUNT_OCCUPY:";

    private static String COUNT_COMMIT_LOCK = "COUNT_COMMIT_LOCK";

    public ShopOrderNumVo shopOrderNum() {
        TokenInfo token = FastThreadLocalUtil.getQY();
        Long shopId = token.getShopId();
        Shop shop = shopMapper.selectByPrimaryKey(shopId);
        QYAssert.isTrue(null != shop, "门店不存在");
        String date = DateUtil.get4yMd(new Date());
        ShopOrderNumVo shopOrderNumVo = subOrderCountMapper.shopOrderNum(shop.getStoreId(), date, shop.getId());
        shopOrderNumVo.setShopId(shop.getId());
        shopOrderNumVo.setShopName(shop.getShopName());
        return shopOrderNumVo;
    }

    public CountCommodityInfoVo countCommodityInfo(String barCode) {

        // 是否为称重码(称重码为2打头 18位)
        boolean isWeightCode = barCode.length() == 18 && barCode.startsWith("2");
        String barCodeCopy = barCode;
        if(isWeightCode){
            // 获取条形码
            barCode = barCode.substring(1, 7);
        }

        TokenInfo token = FastThreadLocalUtil.getQY();
        Long shopId = token.getShopId();
        Shop shop = shopMapper.selectByPrimaryKey(shopId);
        QYAssert.isTrue(null != shop, "门店不存在");

        CountCommodityInfoVo vo = new CountCommodityInfoVo();
        Long commodityId = commodityMapper.findCommodityByBarCode(barCode);

        String date = DateUtil.get4yMd(new Date());

        RBucket<String> bucket = redissonClient.getBucket(COMMODITY_COUNT_OCCUPY + shopId + commodityId + date);
        //同一个操作员进来不提示，允许重复扫描
        if (!StringUtils.isBlank(bucket.get()) && !bucket.get().equals(token.getUserId().toString())) {
            vo.setErrorCode("002");
            return vo;
        }

        if (null == commodityId) {
            vo.setErrorCode("001");
        } else {
            vo = subOrderCountMapper.countCommodityInfo(shop.getStoreId(), date, commodityId);
            if (null == vo) {
                vo = new CountCommodityInfoVo();
                vo.setErrorCode("001");
            } else {
//                List<DictionaryODTO> dictionaryODTOS = dictionaryClient.findListByIds(Arrays.asList(vo.getCommodityUnitId(), vo.getCommodityPackageId()));
//                if (null != dictionaryODTOS) {
//                    for (DictionaryODTO dictionaryODTO : dictionaryODTOS) {
//                        if (dictionaryODTO.getId().equals(vo.getCommodityUnitId())) {
//                            vo.setCommodityUnit(dictionaryODTO.getOptionName());
//                        } else {
//                            vo.setCommodityPackageSpec(dictionaryODTO.getOptionName());
//                        }
//                    }
//                }
                List<SubOrderItemCount> countList = subOrderCountMapper.countCommodityList(shopId, date, Arrays.asList(commodityId));
                vo.setCountNum(countList.stream().map(SubOrderItemCount::getCountQuantity).reduce(BigDecimal.ZERO, BigDecimal::add));
                if(null == vo.getOrderNum()){
                    vo.setOrderNum(BigDecimal.ZERO);
                }
                if(null == vo.getCountNum()){
                    vo.setCountNum(BigDecimal.ZERO);
                }
                vo.setDiffQuantity(vo.getCountNum().subtract(vo.getOrderNum()));
                //是称重商品，但是入参不是称重码
                if (YesOrNoEnums.YES.getCode().equals(vo.getIsWeight()) && !isWeightCode) {
                    vo.setErrorCode("003");
                } else {
                    vo.setNowNum(BigDecimal.ONE);
                    //是称重码解析数量
                    if (isWeightCode) {
                        // 重量(2位整数, 3位小数)
                        String weight = barCodeCopy.substring(12, 14);
                        String weightDecimals = barCodeCopy.substring(14,17);
                        BigDecimal quantity = new BigDecimal(weight + "." + weightDecimals).setScale(3,BigDecimal.ROUND_DOWN);
                        vo.setNowNum(quantity);
                    }
                    //默认五分钟释放
                    bucket.set(token.getUserId().toString(), 10L, TimeUnit.MINUTES);
                }
            }
        }

        return vo;
    }


    @Transactional
    public Boolean submitCountCommodity(CountCommoditySubmitVo vo) {
        vo.check();

        TokenInfo token = FastThreadLocalUtil.getQY();
        String date = DateUtil.get4yMd(new Date());
        List<Long> commodityIds = Arrays.asList(vo.getCommodityId());
        //查询改商品的订货信息
        List<OrderCountVo> reserveList = subOrderCountMapper.subOrderItemInfo(token.getStoreId(), date, commodityIds);
        QYAssert.isTrue(null != reserveList, "今日送货商品中没有该商品");

        //查询改商品的点货信息
        List<SubOrderItemCount> countList = subOrderCountMapper.countCommodityList(token.getShopId(), date, commodityIds);

        SubOrderItemCount count = null;

        if (null != countList && countList.size() > 0) {
            count = countList.get(0);
            //增加
            if (vo.getType() == 1) {
                count.setCountQuantity(count.getCountQuantity().add(vo.getAdjustNum()));
            } else if (vo.getType() == 2) {
                //减少
                QYAssert.isTrue(count.getCountQuantity().subtract(vo.getAdjustNum()).compareTo(BigDecimal.ZERO) >= 0, "调整后数量不能为负数");
                count.setCountQuantity(count.getCountQuantity().subtract(vo.getAdjustNum()));
            } else {
                count.setCountQuantity(vo.getAdjustNum());
            }
            count.setQuantity(reserveList.get(0).getReserveNum());
            count.setUpdateId(token.getUserId());
            count.setUpdateTime(new Date());
            subOrderCountMapper.updateByPrimaryKeySelective(count);
        } else {
            count = new SubOrderItemCount();
            count.setShopId(token.getShopId());
            count.setCommodityId(vo.getCommodityId());
            count.setSendDate(new Date());
            count.setQuantity(reserveList.get(0).getReserveNum());
            count.setCountQuantity(vo.getAdjustNum());
            count.setUpdateId(token.getUserId());
            count.setUpdateTime(new Date());
            count.setCreateId(token.getUserId());
            count.setCreateTime(new Date());
            subOrderCountMapper.insert(count);
        }
        RBucket<String> bucket = redissonClient.getBucket(COMMODITY_COUNT_OCCUPY + token.getShopId() + vo.getCommodityId() + date);
        bucket.getAndDelete();

        insertCountLog(Arrays.asList(count));
        return Boolean.TRUE;
    }


    public void insertCountLog(List<SubOrderItemCount> list) {

        TokenInfo token = FastThreadLocalUtil.getQY();

        //查询部门信息
        SelectShopOrgInfoListIDTO selectShopOrgInfoListIDTO = new SelectShopOrgInfoListIDTO();
        selectShopOrgInfoListIDTO.setShopIdList(Arrays.asList(token.getShopId()));
        List<OrgAndParentInfoODTO> orgList = orgClient.selectShopOrgInfoList(selectShopOrgInfoListIDTO);

        //查询商品信息
        List<Long> commodityIds = list.stream().map(SubOrderItemCount::getCommodityId).collect(Collectors.toList());
        CommodityVO vo = new CommodityVO();
        vo.setCommodityIdList(commodityIds);
        List<CommodityBasicEntry> commodityBasicEntryList = commodityMapper.findCommodityBasicListByParam(vo);
        Map<String, CommodityBasicEntry> commodityMap = commodityBasicEntryList.stream().collect(Collectors.toMap(CommodityBasicEntry::getCommodityId, e -> e));

        List<OrderItemCountLog> logs = new ArrayList<>();
        OrderItemCountLog logInfo = null;
        CommodityBasicEntry commodityBasicEntry = null;
        Date date = new Date();

        String dateStr = DateUtil.get4yMd(date);

        Shop shop = shopMapper.selectByPrimaryKey(token.getShopId());

        for (SubOrderItemCount count : list) {
            logInfo = new OrderItemCountLog();
            logInfo.setShopId(token.getShopId());
            logInfo.setShopName(shop.getShopName());
            logInfo.setOrgName(orgList.get(0).getParentOrgName());
            logInfo.setSendDate(dateStr);
            logInfo.setCommodityId(count.getCommodityId());
            commodityBasicEntry = commodityMap.get(count.getCommodityId().toString());
            logInfo.setCommodityCode(commodityBasicEntry.getCommodityCode());
            logInfo.setCommodityName(commodityBasicEntry.getCommodityName());
            logInfo.setBarCode(commodityBasicEntry.getBarCode());
            logInfo.setCommoditySpec(commodityBasicEntry.getCommoditySpec());
            logInfo.setCountQuantity(count.getCountQuantity());
            logInfo.setCreateId(token.getUserId());
            logInfo.setCreateTime(DateUtil.get4yMdHms(date));
            logInfo.setCreateName(token.getRealName());
            logs.add(logInfo);
        }

        JSONObject jsonObject = new JSONObject();
        jsonObject.put("tableName", "t_log_sub_order_item_count");
        jsonObject.put("data", logs);
        String data = JsonUtil.java2json(jsonObject);
        TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronizationAdapter() {
            @Override
            public void afterCommit() {
               KafkaMessageWrapper message = new KafkaMessageWrapper(KafkaMessageTypeEnum.LOG_CREATE, data, KafkaMessageOperationTypeEnum.INSERT);
                try {
                    mqSenderComponent.send(QYApplicationContext.applicationNameSwitch + KafkaTopicConstant.LOG_CREATE_TOPIC,
                            data,
                            MqMessage.MQ_KAFKA,
                            KafkaMessageTypeEnum.LOG_CREATE.name(),
                            KafkaMessageOperationTypeEnum.INSERT.name());
                } catch (Exception e) {
                    log.error("送货点数日志 发送消息-出错 json={}", JsonUtil.java2json(message), e);
                }
            }
        });
    }

    public Boolean redisDeleteCommodity(List<Long> commodityIds) {
        TokenInfo token = FastThreadLocalUtil.getQY();
        String date = DateUtil.get4yMd(new Date());

        commodityIds.forEach(e -> {
            RBucket<String> bucket = redissonClient.getBucket(COMMODITY_COUNT_OCCUPY + token.getShopId() + e + date);
            bucket.getAndDelete();
        });

        return Boolean.TRUE;
    }

    /**
     * 逐个扫码，批量提交，默认是再原来基础上增加
     * @param list
     * @return
     */
    @Transactional
    public Boolean submitCountMultiCommodity(List<CountCommoditySubmitVo> list) {
        RLock lock = redissonClient.getLock(COUNT_COMMIT_LOCK);
        if(lock.tryLock()) {
            try {
                List<Long> commodityIds = list.stream().map(CountCommoditySubmitVo::getCommodityId).distinct().collect(Collectors.toList());
                QYAssert.isTrue(list.size() == commodityIds.size(), "存在重复商品");

                TokenInfo token = FastThreadLocalUtil.getQY();
                String date = DateUtil.get4yMd(new Date());

                QYAssert.isTrue(SpringUtil.isNotEmpty(commodityIds), "提交商品为空!");
                //查询商品的送货信息
                List<OrderCountVo> reserveList = subOrderCountMapper.subOrderItemInfo(token.getStoreId(), date, commodityIds);
                QYAssert.isTrue(commodityIds.size() == reserveList.size(), "提交商品和送货商品不符");
                Map<Long, BigDecimal> reserveMap = reserveList.stream().collect(Collectors.toMap(OrderCountVo::getCommodityId, OrderCountVo::getReserveNum));
                //查询商品的点货信息
                List<SubOrderItemCount> countList = subOrderCountMapper.countCommodityList(token.getShopId(), date, commodityIds);
                Map<Long, SubOrderItemCount> countMap = countList.stream().collect(Collectors.toMap(SubOrderItemCount::getCommodityId, e->e));

                List<SubOrderItemCount> dateAdd = new ArrayList<>();
                List<SubOrderItemCount> dateUpdate = new ArrayList<>();
                SubOrderItemCount count = null;
                Date today = new Date();
                for (CountCommoditySubmitVo vo : list) {
                    if (countMap.containsKey(vo.getCommodityId())) {
                        count = countMap.get(vo.getCommodityId());
                        count.setQuantity(reserveMap.get(vo.getCommodityId()));
                        count.setCountQuantity(count.getCountQuantity().add(vo.getAdjustNum()));
                        count.setUpdateId(token.getUserId());
                        count.setUpdateTime(today);
                        subOrderCountMapper.updateByPrimaryKeySelective(count);
                        dateUpdate.add(count);
                    } else {
                        count = new SubOrderItemCount();
                        count.setShopId(token.getShopId());
                        count.setSendDate(today);
                        count.setCommodityId(vo.getCommodityId());
                        count.setQuantity(reserveMap.get(vo.getCommodityId()));
                        count.setCountQuantity(vo.getAdjustNum());
                        count.setCreateId(token.getUserId());
                        count.setCreateTime(today);
                        count.setUpdateId(token.getUserId());
                        count.setUpdateTime(today);
                        dateAdd.add(count);
                    }
                }

                if (dateAdd.size() > 0) {
                    subOrderCountMapper.insertList(dateAdd);
                }

                //释放锁
                list.forEach(e->{
                    RBucket<String> bucket = redissonClient.getBucket(COMMODITY_COUNT_OCCUPY + token.getShopId() + e.getCommodityId() + date);
                    bucket.getAndDelete();
                });
                //保存日志
                dateUpdate.addAll(dateAdd);
                insertCountLog(dateUpdate);
            } finally {
                lock.unlock();
            }
        } else {
            log.error("批量点货提交并发",list);
        }
        return Boolean.TRUE;
    }

    public List<CountCommodityInfoVo> countCommodityList() {
        TokenInfo token = FastThreadLocalUtil.getQY();
        String date = DateUtil.get4yMd(new Date());
        List<CountCommodityInfoVo> list = subOrderCountMapper.todayCountCommodityList(token.getShopId(), date, null);
        if(SpringUtil.isNotEmpty(list)){
            list.forEach(it ->{
                if(null == it.getCountNum()){
                    it.setCountNum(BigDecimal.ZERO);
                }
                if(null == it.getOrderNum()){
                    it.setOrderNum(BigDecimal.ZERO);
                }
                it.setDiffQuantity(it.getCountNum().subtract(it.getOrderNum()));
            });
        }
        return list;
    }

    /**
     * 查询单个已点商品
     * @param barCode
     * @return
     */
    public CountCommodityInfoVo commodityInfo(String barCode) {
        TokenInfo token = FastThreadLocalUtil.getQY();
        String date = DateUtil.get4yMd(new Date());
        CountCommodityInfoVo vo = new CountCommodityInfoVo();

        boolean isWeightCode = barCode.length() == 18 && barCode.startsWith("2");
        if(isWeightCode){
            // 获取条形码
            barCode = barCode.substring(1, 7);
        }
        Long commodityId = commodityMapper.findCommodityByBarCode(barCode);
        QYAssert.isTrue(null != commodityId, "该商品不存在");

        //查询商品的送货信息
        List<OrderCountVo> reserveList = subOrderCountMapper.subOrderItemInfo(token.getStoreId(), date, Arrays.asList(commodityId));
        if (null == reserveList || reserveList.size() == 0) {
            vo.setErrorCode("001");
        } else {
            //查询该商品的点货信息
            List<CountCommodityInfoVo> list = subOrderCountMapper.todayCountCommodityList(token.getShopId(), date, commodityId);
            if (null ==list || list.size() == 0) {
                vo.setErrorCode("004");
            } else {
                vo = list.get(0);
                if(vo.getCountNum() == null){
                    vo.setCountNum(BigDecimal.ZERO);
                }
                if(vo.getOrderNum() == null){
                    vo.setOrderNum(BigDecimal.ZERO);
                }
                vo.setDiffQuantity(vo.getCountNum().subtract(vo.getOrderNum()));
            }
        }

        return vo;
    }

}
