package com.pinshang.qingyun.order.mapper.entry.order;

import java.math.BigDecimal;

public class FrozenProductEntry {
	
	private String productId;
	
	private Integer frozen;//速冻

	private String productName;
	
	private BigDecimal salesBoxCapacity;//销售箱规
	private BigDecimal xdSalesBoxCapacity;//xd销售箱规
	
	public String getProductId() {
		return productId;
	}

	public void setProductId(String productId) {
		this.productId = productId;
	}

	public Integer getFrozen() {
		return frozen;
	}

	public void setFrozen(Integer frozen) {
		this.frozen = frozen;
	}

	public BigDecimal getSalesBoxCapacity() {
		return salesBoxCapacity;
	}

	public void setSalesBoxCapacity(BigDecimal salesBoxCapacity) {
		this.salesBoxCapacity = salesBoxCapacity;
	}

	public BigDecimal getXdSalesBoxCapacity() {
		return xdSalesBoxCapacity;
	}

	public void setXdSalesBoxCapacity(BigDecimal xdSalesBoxCapacity) {
		this.xdSalesBoxCapacity = xdSalesBoxCapacity;
	}

	public String getProductName() {
		return productName;
	}

	public void setProductName(String productName) {
		this.productName = productName;
	}
	
}
