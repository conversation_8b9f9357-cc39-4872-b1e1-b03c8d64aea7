package com.pinshang.qingyun.order.mapper.entry.shop;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2017/6/14.
 */
@Data
public class AuditInfoEntry {

	private Integer logisticsModel;

	private String subOrderCode;

	private BigDecimal totalPrice;

	private String supplierId;

	private String supplierName;

	private Integer orderStatus;

	private Integer status;

	private Date receiveTime;

	private Date orderTime;

	private String shopName;

	private String operateName;

	private Date approvingTime;
	private String approvingName;
	private String realOrderCode;

	private Long stallId;
	private String stallName;
}
