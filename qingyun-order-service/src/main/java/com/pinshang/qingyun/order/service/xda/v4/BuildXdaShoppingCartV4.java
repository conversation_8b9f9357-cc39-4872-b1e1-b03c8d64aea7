package com.pinshang.qingyun.order.service.xda.v4;

import com.google.common.collect.Maps;
import com.pinshang.qingyun.base.enums.SalesPromotionStatusEnums;
import com.pinshang.qingyun.base.enums.YesOrNoEnums;
import com.pinshang.qingyun.base.enums.marketing.MarketSourceTypeEnum;
import com.pinshang.qingyun.base.spring.SpringBeanFinder;
import com.pinshang.qingyun.box.utils.BeanCloneUtils;
import com.pinshang.qingyun.box.utils.CollectorsUtil;
import com.pinshang.qingyun.box.utils.DateUtil;
import com.pinshang.qingyun.box.utils.SpringUtil;
import com.pinshang.qingyun.marketing.dto.FreeBuyODTO;
import com.pinshang.qingyun.marketing.dto.app.*;
import com.pinshang.qingyun.marketing.service.MtPromotionClient;
import com.pinshang.qingyun.order.dto.giftLimit.GiftLeftQuantityODTO;
import com.pinshang.qingyun.order.dto.giftLimit.GiftLimitQuantityQueryDetailIDTO;
import com.pinshang.qingyun.order.dto.giftLimit.GiftLimitQuantityQueryIDTO;
import com.pinshang.qingyun.order.dto.shopcart.v4.DiscountDetailODTO;
import com.pinshang.qingyun.order.dto.shopcart.v4.ShoppingCartCommodityV4ODTO;
import com.pinshang.qingyun.order.dto.shopcart.v4.ShoppingCartGroupV4ODTO;
import com.pinshang.qingyun.order.dto.shopcart.v4.ShoppingCartV4ODTO;
import com.pinshang.qingyun.order.dto.xda.XdaCommodityLimit4AppODTO;
import com.pinshang.qingyun.order.enums.StockLevelEnums;
import com.pinshang.qingyun.order.manage.freebuy.FreeBuyGiftNumCalculator;
import com.pinshang.qingyun.order.mapper.*;
import com.pinshang.qingyun.order.model.order.XdaShoppingCart;
import com.pinshang.qingyun.order.model.store.Store;
import com.pinshang.qingyun.order.model.store.StoreDuration;
import com.pinshang.qingyun.order.service.StoreService;
import com.pinshang.qingyun.order.service.giftLimit.GiftLimitService;
import com.pinshang.qingyun.product.dto.commodity.CommodityItemODTO;
import com.pinshang.qingyun.storage.dto.tob.CommodityInventoryDetailIDTO;
import com.pinshang.qingyun.storage.dto.tob.CommodityInventoryODTO;
import com.pinshang.qingyun.xda.product.dto.commodityText.CommodityTextTagInfoODTO;
import com.pinshang.qingyun.xda.product.dto.front.XdaShoppingCartV2IDTO;
import com.pinshang.qingyun.xda.product.dto.shoppingCart.XdaShoppingCartV3IDTO;
import com.pinshang.qingyun.xda.product.dto.shoppingCart.XdaShoppingCartV3ODTO;
import com.pinshang.qingyun.xda.product.service.XdaCommodityFrontClient;
import com.pinshang.qingyun.xda.product.service.XdaShoppingCartController;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.redisson.api.RedissonClient;
import org.springframework.util.CollectionUtils;
import tk.mybatis.mapper.entity.Example;
import tk.mybatis.spring.mapper.SpringBootBindUtil;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static java.util.stream.Collectors.*;

/**
 * <AUTHOR>
 * @Date 2024/3/4 16:06
 */
@Slf4j
public class BuildXdaShoppingCartV4 {
    private XdaShoppingCartMapper xdaShoppingCartMapper;

    private OrderMapper orderMapper;

    private OrderListMapper orderListMapper;

    private XdaCommodityFrontClient xdaCommodityFrontClient;

    private XdaShoppingCartController xdaShoppingCartController;

    private MtPromotionClient mtPromotionClient;

    private StoreService storeService;

    private StoreDurationMapper storeDurationMapper;

    private CommodityFreezeGroupMapper commodityFreezeGroupMapper;

    private ToBService toBService;

    private GiftLimitService giftLimitService;

    private Long storeId;

    private Date deliveryDate;
    private Integer deliveryBatch; //配送批次：1-一配、2-二配		—— 参见枚举： DeliveryBatchEnums
    private String deliveryTimeRange; // 送货时间段
    private static final BigDecimal freezing_multiple = BigDecimal.valueOf(30);

    private ShoppingCartV4ODTO shoppingCartODTO;

    private Map<Long,BigDecimal> tbSalesPromotionCommodityLimitMap;

    private RedissonClient redissonClient;

    private Long couponUserId;

    public BuildXdaShoppingCartV4(Long storeId, Date deliveryDate, ToBService toBService,
                                  XdaShoppingCartMapper xdaShoppingCartMapper, OrderMapper orderMapper, OrderListMapper orderListMapper,
                                  XdaCommodityFrontClient xdaCommodityFrontClient, XdaShoppingCartController xdaShoppingCartController, StoreService storeService, StoreDurationMapper storeDurationMapper,
                                  CommodityFreezeGroupMapper commodityFreezeGroupMapper, MtPromotionClient mtPromotionClient,
                                  String deliveryTimeRange, Integer deliveryBatch,RedissonClient redissonClient, Long couponUserId){
        this.storeId = storeId;
        this.deliveryDate = deliveryDate;
        this.toBService  = toBService;
        this.xdaShoppingCartMapper = xdaShoppingCartMapper;
        this.orderMapper = orderMapper;
        this.orderListMapper = orderListMapper;
        this.xdaCommodityFrontClient = xdaCommodityFrontClient;
        this.xdaShoppingCartController = xdaShoppingCartController;
        this.storeService = storeService;
        this.storeDurationMapper = storeDurationMapper;
        this.commodityFreezeGroupMapper = commodityFreezeGroupMapper;
        this.mtPromotionClient = mtPromotionClient;
        this.deliveryTimeRange = deliveryTimeRange;
        this.deliveryBatch = deliveryBatch;
        this.redissonClient = redissonClient;
        this.couponUserId = couponUserId;
        this.giftLimitService = SpringBeanFinder.getBean(GiftLimitService.class);
    }

    public void initShoppingCartODTO(){
        shoppingCartODTO = new ShoppingCartV4ODTO();
        shoppingCartODTO.setSummation(BigDecimal.ZERO);
        shoppingCartODTO.setOriginalAmount(BigDecimal.ZERO);
        shoppingCartODTO.setDiscountTotal(BigDecimal.ZERO);
        shoppingCartODTO.setNormalGroup(ShoppingCartGroupV4ODTO.initialization());
        shoppingCartODTO.setInvalidateGroup(ShoppingCartGroupV4ODTO.initialization());
        shoppingCartODTO.setThGroups(ShoppingCartGroupV4ODTO.initialization());
        shoppingCartODTO.setPromotionGroup(new ArrayList<>());
        shoppingCartODTO.setConditionMap(new HashMap<>());
        shoppingCartODTO.setCanSettlement(true);
    }

    public ShoppingCartV4ODTO shopCartList(){
        ShoppingCartV4ODTO shoppingCart = buildCommodityInfo(queryShoppingCart(storeId, null, null));
        // 组装优惠券信息
        processXdaCoupon(shoppingCart);
        return shoppingCart;
    }

    public void processXdaCoupon(ShoppingCartV4ODTO shoppingCart) {
        // 计算券优惠
        BigDecimal couponAmount = shoppingCart.getCouponAmount();
        if (Objects.isNull(couponAmount) || couponAmount.compareTo(BigDecimal.ZERO) <= 0) {
            couponAmount = BigDecimal.ZERO;
        }

        BigDecimal activityDiscount = shoppingCart.getOriginalAmount().subtract(shoppingCart.getSummation());
        shoppingCart.setSummation(shoppingCart.getSummation().subtract(couponAmount));
        shoppingCart.setDiscountTotal(shoppingCart.getOriginalAmount().subtract(shoppingCart.getSummation()));

        //设置优惠明细
        DiscountDetailODTO discountDetail = DiscountDetailODTO.builder()
                .activityDiscount(activityDiscount)
                .couponAmount(couponAmount)
                .discountTotal(shoppingCart.getDiscountTotal())
                .originalAmount(shoppingCart.getOriginalAmount())
                .summation(shoppingCart.getSummation())
                .build();
        if (discountDetail.getDiscountTotal().compareTo(BigDecimal.ZERO) > 0) {
            shoppingCart.setDiscountDetail(discountDetail);
        }
    }

    /**
     * 校验购物车商品是否可结算
     * @param shoppingCarts
     * @return
     */
    public ShoppingCartV4ODTO buildCommodityInfo(List<XdaShoppingCart> shoppingCarts){
        /* 初始化返回集合*/
        this.initShoppingCartODTO();
        /* 查询客户对应的订货时间段*/
        shoppingCartODTO.setTopTips(getStoreDuration(storeId));

        if(shoppingCarts.isEmpty()) return shoppingCartODTO;
        /* 查询商品基本信息*/
        List<ShoppingCartCommodityV4ODTO> shoppingCartCommodityV3ODTOList = this.getValidCommodity(shoppingCarts, deliveryDate, storeId);
        if (shoppingCartCommodityV3ODTOList.isEmpty()) {
            shoppingCartODTO.setCanSettlement(Boolean.FALSE);
            return shoppingCartODTO;
        }
        //计算商品价格、限量、凑整
        singleCommodityLogic(shoppingCartCommodityV3ODTOList, shoppingCarts);

        // 计算商品金额是否达到目标
        Store store = storeService.findStoreByStoreId(storeId);
        BigDecimal minDeliveryAmount = store.getMinDeliveryAmount();
        if(null != minDeliveryAmount && minDeliveryAmount.compareTo(shoppingCartODTO.getSummation()) > 0){
            BigDecimal subAmount = minDeliveryAmount.subtract(shoppingCartODTO.getSummation());
            shoppingCartODTO.setBottomTips("起送金额为"+minDeliveryAmount+"元，还差"+subAmount.setScale(2,BigDecimal.ROUND_HALF_UP)+"元");
            shoppingCartODTO.setCanSettlement(Boolean.FALSE);
        }
        // 设置品类数量
//        shoppingCartODTO.setVarietySum(this.getValidCommoditySum(shoppingCartODTO));
        shoppingCartODTO.setVarietySum(getShoppingCartNoZeroVarietySum(shoppingCartODTO));
        shoppingCartODTO.setCommodityNum(this.getValidCommodityNumSum(shoppingCartODTO));
        shoppingCartODTO.setDiscountTotal(shoppingCartODTO.getOriginalAmount().subtract(shoppingCartODTO.getSummation()));
        return shoppingCartODTO;
    }

    /**
     * 商品信息 处理
     * @param shoppingCartCommodityV4ODTOList
     * @param shoppingCarts
     */
    public void singleCommodityLogic(List<ShoppingCartCommodityV4ODTO> shoppingCartCommodityV4ODTOList,List<XdaShoppingCart> shoppingCarts) {
        Map<Integer, Map<Long, XdaShoppingCart>> shopCartMap = shoppingCarts.stream().collect(Collectors.groupingBy(XdaShoppingCart::getCommodityType, Collectors.toMap(XdaShoppingCart::getCommodityId, Function.identity())));
        /* 普通商品 */
        Map<Long, XdaShoppingCart> xdaShoppingCartMap = shopCartMap.get(1);

        /* 特惠商品 */
        Map<Long, XdaShoppingCart> thXdaShoppingCartMap = shopCartMap.get(2);
        List<Long> longList = new ArrayList<>();
        if(SpringUtil.isNotEmpty(xdaShoppingCartMap)){
            longList = xdaShoppingCartMap.keySet().stream().collect(Collectors.toList());
        }

        Map<Long, BigDecimal> longBigDecimalMap = new HashMap<>();
        CartODTO cartODTO = new CartODTO();
        Map<Long, ShoppingCartCommodityV4ODTO> shoppingCartCommodityV3ODTOMap = shoppingCartCommodityV4ODTOList.stream().collect(Collectors.toMap(ShoppingCartCommodityV4ODTO::getCommodityId, e -> e));

        //获取普通商品组对应的 买赠分组
        if(SpringUtil.isNotEmpty(longList)){
            longBigDecimalMap = xdaShoppingCartController.selectCommodityPackageSpecByCommodityIdList(longList);
            long l1 = System.currentTimeMillis();
            cartODTO = getTbSalesPromotionList(xdaShoppingCartMap,shoppingCartCommodityV3ODTOMap,longBigDecimalMap);
            long l2 = System.currentTimeMillis();
            log.info("购物车列表调用促销查询耗时:" + (l2 - l1));

            shoppingCartODTO.setCouponAvailableNumber(cartODTO.getAvailableNumber());
            shoppingCartODTO.setCouponAmount(cartODTO.getCouponDiscount());
            shoppingCartODTO.setCouponUserId(cartODTO.getCouponUserId());
            shoppingCartODTO.setCouponId(cartODTO.getCouponId());
        }

        long l1 = System.currentTimeMillis();
        // 处理普通商品组信息
        normalGroupAmountLogic(xdaShoppingCartMap,shoppingCartCommodityV3ODTOMap, cartODTO);
        long l2 = System.currentTimeMillis();
        log.info("购物车列表计算普通商品耗时:" + (l2 - l1));

        BigDecimal normalGroupAmount = getNormalGroupAmount(shoppingCartCommodityV4ODTOList, xdaShoppingCartMap,cartODTO);

        //处理特惠商品
        thGroupAmountLogic(normalGroupAmount,thXdaShoppingCartMap,shoppingCartCommodityV3ODTOMap);
        long l3 = System.currentTimeMillis();
        log.info("购物车列表计算特惠商品耗时:" + (l3 - l2));
        //处理买赠分组
        setTbSalesPromotionCommodityGiftGroup(cartODTO,shoppingCartCommodityV3ODTOMap,longBigDecimalMap);
        long l4 = System.currentTimeMillis();
        log.info("购物车列表计算买赠商品耗时:" + (l4 - l3));

        //计算库存
        xdaCommodityLimit();

        // 计算凑整商品
        xdaFreezeRoundingCommodity();

    }
    public void setThCommodityTips(BuildXdaShoppingCartV4 buildXdaShoppingCartV4, ShoppingCartV4ODTO shoppingCartV4ODTO, YesOrNoEnums yesOrNoEnums) {
        List<XdaShoppingCart> xdaShoppingCarts = queryShoppingCart(storeId, null, 1);
        XdaShoppingCartV2IDTO xdaShoppingCartV2IDTO = new XdaShoppingCartV2IDTO();
        xdaShoppingCartV2IDTO.setStoreId(storeId);
        xdaShoppingCartV2IDTO.setOrderTime(deliveryDate);
        BigDecimal orderTargetByStoreIdAndOrderTime = xdaCommodityFrontClient.findOrderTargetByStoreIdAndOrderTime(xdaShoppingCartV2IDTO);
        if(null == orderTargetByStoreIdAndOrderTime){
            shoppingCartV4ODTO.setThCategoryTips("");
            shoppingCartV4ODTO.setThGroups(ShoppingCartGroupV4ODTO.initialization());
            return;
        }
        List<ShoppingCartCommodityV4ODTO> thCommodityGroups = new ArrayList<>();

        if(YesOrNoEnums.YES.equals(yesOrNoEnums)){
            if(null == shoppingCartV4ODTO.getThGroups()){
                return;
            }
            thCommodityGroups = shoppingCartV4ODTO.getThGroups().getCommodities();
        }else {
//            shoppingCartV3ODTO.setNormalGroupAmount();
        }
        if(SpringUtil.isEmpty(xdaShoppingCarts) && SpringUtil.isNotEmpty(thCommodityGroups)){
            String thCategoryTips = getThCategoryTips(BigDecimal.ZERO, orderTargetByStoreIdAndOrderTime);
            shoppingCartV4ODTO.getThGroups().setFreezeWarnTips(thCategoryTips);
            shoppingCartV4ODTO.setThCategoryTips(thCategoryTips);
            return;
        }
        if(SpringUtil.isEmpty(xdaShoppingCarts) && SpringUtil.isEmpty(thCommodityGroups)){
            String thCategoryTips = getThCategoryTips(BigDecimal.ZERO, orderTargetByStoreIdAndOrderTime);
            shoppingCartV4ODTO.setThCategoryTips(thCategoryTips);
            return;
        }
        if(SpringUtil.isNotEmpty(xdaShoppingCarts)){
            List<ShoppingCartCommodityV4ODTO> validCommodity = buildXdaShoppingCartV4.getValidCommodity(xdaShoppingCarts, deliveryDate, storeId);
            validCommodity.removeIf(item -> null != item.getIsCanOrder() && !item.getIsCanOrder());
            if(SpringUtil.isEmpty(validCommodity)){
                String thCategoryTips = getThCategoryTips(BigDecimal.ZERO, orderTargetByStoreIdAndOrderTime);
                // 如果商品已经淘汰了，则会报空指针。进行兼容处理
                if(shoppingCartV4ODTO.getThGroups() == null) {
                    shoppingCartV4ODTO.setThGroups(new ShoppingCartGroupV4ODTO());
                }
                shoppingCartV4ODTO.getThGroups().setFreezeWarnTips(thCategoryTips);
                shoppingCartV4ODTO.setThCategoryTips(thCategoryTips);
                return;
            }
            BigDecimal normalGroupAmount = BigDecimal.ZERO;

            if(YesOrNoEnums.YES.equals(yesOrNoEnums)){
                normalGroupAmount = shoppingCartV4ODTO.getNormalGroupAmount();
            }else {
                normalGroupAmount= buildXdaShoppingCartV4.shopCartList().getNormalGroupAmount();
            }
            if(null != normalGroupAmount){
                if(normalGroupAmount.compareTo(orderTargetByStoreIdAndOrderTime) > -1){
                    shoppingCartV4ODTO.setIsThInvalidate(Boolean.TRUE);
                }
                shoppingCartV4ODTO.setNormalGroupAmount(normalGroupAmount);
                shoppingCartV4ODTO.setThFullPrice(orderTargetByStoreIdAndOrderTime);
                String thCategoryTips = getThCategoryTips(normalGroupAmount, orderTargetByStoreIdAndOrderTime);
                shoppingCartV4ODTO.setThCategoryTips(thCategoryTips);
                if (YesOrNoEnums.YES.equals(yesOrNoEnums) && null != shoppingCartV4ODTO.getThGroups()){
                    shoppingCartV4ODTO.getThGroups().setFreezeWarnTips(thCategoryTips);
                }
            }
        }
    }
    private String getThCategoryTips(BigDecimal normalGroupAmount,BigDecimal orderTargetToDay){
        String thCategoryTips = "";
        if(null == orderTargetToDay || null == normalGroupAmount){
            return thCategoryTips;
        }
        if(normalGroupAmount.compareTo(orderTargetToDay) < 0){
            //订货金额< 目标金额
            BigDecimal subtract = orderTargetToDay.subtract(normalGroupAmount).setScale(2, BigDecimal.ROUND_HALF_UP);
            thCategoryTips = "订货目标"+ orderTargetToDay + "元, 还差"+ subtract +"元可选购特惠商品";
        }else {
            thCategoryTips = "订货目标已满"+ orderTargetToDay + "元, 可选购特惠商品";
        }
        return thCategoryTips;
    }
    /**
     * 计算凑整商品
     */
    public void xdaFreezeRoundingCommodity(){
        //普通商品组
        Map<Long, BigDecimal> normalGroupQuantity = shoppingCartODTO.getNormalGroup().getCommodities().stream()
                .filter(item -> item.getIsFreezeRoundingGroup() == 1 && !item.getIsInvalidate())
                .collect(Collectors.groupingBy(ShoppingCartCommodityV4ODTO::getCommodityId, CollectorsUtil.summingBigDecimalMax(ShoppingCartCommodityV4ODTO::getQuantity)));
        // 买赠商品组
        Map<Long, BigDecimal> promotionGroupQuantity = shoppingCartODTO.getPromotionGroup().stream().flatMap(group -> group.getCommodities().stream())
                .filter(item -> null != item.getIsFreezeRoundingGroup() && item.getIsFreezeRoundingGroup() == 1 && !item.getIsInvalidate()).collect(Collectors.groupingBy(ShoppingCartCommodityV4ODTO::getCommodityId, CollectorsUtil.summingBigDecimalMax(ShoppingCartCommodityV4ODTO::getQuantity)));
        // 特惠商品组
        Map<Long, BigDecimal> thGroupsQuantity = shoppingCartODTO.getThGroups().getCommodities().stream()
                .filter(item -> item.getIsFreezeRoundingGroup() == 1 && !item.getIsInvalidate())
                .collect(Collectors.groupingBy(ShoppingCartCommodityV4ODTO::getCommodityId, CollectorsUtil.summingBigDecimalMax(ShoppingCartCommodityV4ODTO::getQuantity)));
        Map<Long,BigDecimal> commodityQuantitySum = new HashMap<>();
        setFreezeRoundingCommodityQuantitySum(normalGroupQuantity,commodityQuantitySum);
        setFreezeRoundingCommodityQuantitySum(promotionGroupQuantity,commodityQuantitySum);
        setFreezeRoundingCommodityQuantitySum(thGroupsQuantity,commodityQuantitySum);
        if(SpringUtil.isNotEmpty(commodityQuantitySum)){
            BigDecimal reduce = commodityQuantitySum.values().stream().reduce(BigDecimal.ZERO, BigDecimal::add);
            BigDecimal remainder = reduce.divideAndRemainder(freezing_multiple)[1];
            if(remainder.compareTo(BigDecimal.ZERO) != 0){
                setWarnMessage("凑整商品总数量须是"+freezing_multiple+"整倍数，请确认数量");
            }
        }
    }
    /**
     * 汇总凑整商品数量
     * @param commodityQuantity
     * @param commodityQuantitySum
     */
    public void setFreezeRoundingCommodityQuantitySum(Map<Long,BigDecimal> commodityQuantity,Map<Long,BigDecimal> commodityQuantitySum){
        commodityQuantity.forEach((key,value)->{
            if(commodityQuantitySum.containsKey(key)){
                commodityQuantitySum.put(key,value.add(commodityQuantitySum.get(key)));
            }else {
                commodityQuantitySum.put(key,value);
            }
        });
    }
    /**
     * 计算商品库存
     * 1、计算赠品 修改赠品数量
     * 2、计算特惠
     * 3、计算普通商品
     */
    private void xdaCommodityLimit(){
        List<CommodityInventoryDetailIDTO> orderCommodityList = toBService.getInventoryOrderCommodityList(shoppingCartODTO);
        if (CollectionUtils.isEmpty(orderCommodityList)){
            return;
        }
        List<CommodityInventoryODTO> commodityInventoryODTOS = toBService.queryCommodityInventory(deliveryDate, orderCommodityList, storeId, deliveryTimeRange, deliveryBatch);
        //订单判断使用
        Map<Long, Integer> commodityStockTypeMap = commodityInventoryODTOS.stream().collect(Collectors.toMap(CommodityInventoryODTO::getCommodityId, CommodityInventoryODTO::getStockType, (v1, v2) -> v1));
        shoppingCartODTO.setCommodityStockTypeMap(commodityStockTypeMap);
        //将大仓返回的库存信息按 类型分组
        Map<Integer, Map<Long, CommodityInventoryODTO>> resultMap = commodityInventoryODTOS.stream()
                .collect(Collectors.groupingBy(
                        CommodityInventoryODTO::getLevel,
                        Collectors.toMap(CommodityInventoryODTO::getCommodityId, Function.identity())));

        //判断特价商品组库存
        shoppingCartODTO.getNormalGroup().getCommodities()
                .stream()
                .filter(x -> Objects.equals(1, x.getIsSpecialPrice()))
                //组合品在前，先满足组合品
                .sorted(Comparator.comparingInt(ShoppingCartCommodityV4ODTO::getProductType))
                .forEach(
                        item -> {
                            if (Objects.equals(item.getProductType(), 2)) {
                                //判断特价商品中的组合品库存
                                checkCombiCommodityLimit(resultMap.get(StockLevelEnums.SPECIAL_COMB.getCode()), item);
                            } else if (Objects.equals(item.getProductType(), 1)) {
                                //判断特价商品中的非组合品库存
                                checkCommodityLimit(resultMap.get(StockLevelEnums.SPECIAL.getCode()), item);
                            }
                        }
                );

        //判断正常商品组库存
        shoppingCartODTO.getNormalGroup().getCommodities()
                .stream()
                .filter(x -> Objects.equals(0, x.getIsSpecialPrice()))
                .sorted(Comparator.comparingInt(ShoppingCartCommodityV4ODTO::getProductType))
                .forEach(
                        item -> {
                            if (Objects.equals(item.getProductType(), 2)) {
                                //判断正常商品中的组合品库存
                                checkCombiCommodityLimit(resultMap.get(StockLevelEnums.NORMAL_COMB.getCode()), item);
                            } else if (Objects.equals(item.getProductType(), 1)) {
                                //判断正常商品中的非组合品库存
                                checkCommodityLimit(resultMap.get(StockLevelEnums.NORMAL.getCode()), item);
                            }
                        }
                );

        //判断促销普通商品组库存
        shoppingCartODTO.getPromotionGroup()
                .stream()
                .flatMap(x -> x.getCommodities().stream())
                .filter(isGift -> !isGift.getIsGift())
                .sorted(Comparator.comparingInt(ShoppingCartCommodityV4ODTO::getProductType))
                .forEach(
                        item -> {
                            if (Objects.equals(item.getProductType(), 2)) {
                                //判断促销普通商品组中的组合品库存
                                checkCombiCommodityLimit(resultMap.get(StockLevelEnums.NORMAL_COMB.getCode()), item);
                            } else if (Objects.equals(item.getProductType(), 1)) {
                                //判断促销普通商品组中的非组合品库存
                                checkCommodityLimit(resultMap.get(StockLevelEnums.NORMAL.getCode()), item);
                            }
                        }
                );

        //判断特惠品组库存
        shoppingCartODTO.getThGroups().getCommodities()
                .stream()
                .sorted(Comparator.comparingInt(ShoppingCartCommodityV4ODTO::getProductType))
                .forEach(
                        item -> {
                            if (!item.getIsInvalidate()) {
                                if (null != item.getThLimitNumber() && item.getQuantity().compareTo(item.getThLimitNumber()) > 0) {
                                    item.setStockWarningTips("特惠商品限量，余量为" + item.getThLimitNumber());
                                    item.setAbleAdd(Boolean.FALSE);
                                    shoppingCartODTO.setCanSettlement(Boolean.FALSE);
                                }
                                if (Objects.equals(item.getProductType(), 2)) {
                                    //判断特惠品中的组合品库存
                                    checkCombiCommodityLimit(resultMap.get(StockLevelEnums.TH_COMB.getCode()), item);
                                } else if (Objects.equals(item.getProductType(), 1)) {
                                    //判断特惠品中的非组合品库存
                                    checkCommodityLimit(resultMap.get(StockLevelEnums.TH.getCode()), item);
                                }
                            }
                        }
                );

        //赠品库存信息
        Map<Long, CommodityInventoryODTO> giftMap = resultMap.computeIfAbsent(StockLevelEnums.GIFT.getCode(), k -> Collections.emptyMap());

        //获取当日当前客户已定赠品商品数量
        List<Long> giftCommodityIdList = shoppingCartODTO.getPromotionGroup().stream().flatMap(item -> item.getCommodities().stream().filter(ShoppingCartCommodityV4ODTO::getIsGift)).map(ShoppingCartCommodityV4ODTO::getCommodityId).collect(Collectors.toList());
        List<XdaCommodityLimit4AppODTO> xdaCommodityLimit4AppODTOList = this.getUsedQuantityCommodityByStoreIdMap(giftCommodityIdList,storeId,2,null);
        Map<Long, Map<Long, BigDecimal>> promotionGiftCommodityQuantityMap = xdaCommodityLimit4AppODTOList.stream().collect(Collectors.groupingBy(XdaCommodityLimit4AppODTO::getGiftModelId, Collectors.toMap(XdaCommodityLimit4AppODTO::getCommodityId, XdaCommodityLimit4AppODTO::getTotalQuantity)));
        //获取促销活动下赠送的商品已送数量
        Map<Long, Map<Long, BigDecimal>> giftLimitTotalQuantityMap = this.getTotalUsedQuantityCommodityByPromotionIdMap(shoppingCartODTO, null);

        //判断促销赠品组库存
        shoppingCartODTO.getPromotionGroup().forEach(item->{
            Map<Long, BigDecimal> giftCommodityQuantityMap =  new HashMap<>();
            if(promotionGiftCommodityQuantityMap.containsKey(Long.valueOf(item.getPromotionId()))){
                giftCommodityQuantityMap = promotionGiftCommodityQuantityMap.get(Long.valueOf(item.getPromotionId()));
            }
            Map<Long,BigDecimal> giftCommodityTotalLimitQuantityMap = Maps.newHashMap();
            if(giftLimitTotalQuantityMap.containsKey(Long.valueOf(item.getPromotionId()))){
                giftCommodityTotalLimitQuantityMap = giftLimitTotalQuantityMap.get(Long.valueOf(item.getPromotionId()));
            }

            List<ShoppingCartCommodityV4ODTO> giftCommodityList = item.getCommodities().stream().filter(ShoppingCartCommodityV4ODTO::getIsGift).collect(Collectors.toList());
            for (ShoppingCartCommodityV4ODTO promotion : giftCommodityList){
                //赠品活动赠送数量
                BigDecimal giftQuantity = promotion.getQuantity();
                FreeBuyGiftNumCalculator freeBuyGiftNumCalculator = new FreeBuyGiftNumCalculator(promotion.getGiftLimitNumber(),giftCommodityQuantityMap,promotion.getCommodityId(),giftQuantity);
                BigDecimal storeDayMaxGiftNum = freeBuyGiftNumCalculator.getMaxAvailableGiftNum();

                FreeBuyGiftNumCalculator totalMaxGiftNumCalculator = new FreeBuyGiftNumCalculator(promotion.getGiftTotalLimitNumber(),giftCommodityTotalLimitQuantityMap,promotion.getCommodityId(),giftQuantity);
                BigDecimal totalMaxGiftNum = totalMaxGiftNumCalculator.getMaxAvailableGiftNum();

                //实际能赠送的数量
                BigDecimal availableGiftNum = storeDayMaxGiftNum.min(totalMaxGiftNum);
                if(promotion.getIsGift() && giftMap.containsKey(promotion.getCommodityId())){

                    CommodityInventoryODTO commodityInventoryODTO = giftMap.get(promotion.getCommodityId());
                    if(null != commodityInventoryODTO && null != commodityInventoryODTO.getInventoryQuantity()){
                        BigDecimal inventoryQuantity = commodityInventoryODTO.getInventoryQuantity();
                        //实际赠送的数量= min(客户单日限量、总限量、库存)
                        promotion.setQuantity(inventoryQuantity.min(availableGiftNum));
                        BigDecimal quantity = promotion.getQuantity();
                        if(quantity.compareTo(giftQuantity) < 0){
                            promotion.setIsGiftExceedLimit(true);
                            promotion.setGiftExceedQuantity(giftQuantity.subtract(promotion.getQuantity()));
                            promotion.setStockWarningTips("赠完即止，余量为" +promotion.getQuantity().stripTrailingZeros().toPlainString());
                        }
                        commodityInventoryODTO.setInventoryQuantity(commodityInventoryODTO.getInventoryQuantity().subtract(promotion.getQuantity()));
                    }

                }
            }
        });
    }


    private Map<Long, Map<Long, BigDecimal>> getTotalUsedQuantityCommodityByPromotionIdMap(ShoppingCartV4ODTO shoppingCartODTO, Long orderId){
        //获取促销活动下赠送的商品已送数量
        List<GiftLimitQuantityQueryDetailIDTO> detailList = shoppingCartODTO.getPromotionGroup().stream()
//                .filter(promotionGroup->SpringUtil.isNotEmpty(promotionGroup.getCommodities()) && promotionGroup.getCommodities().stream().filter(ShoppingCartCommodityV4ODTO::getIsGift).findAny().isPresent())
                .map(
                        promotionGroup->{
                            List<ShoppingCartCommodityV4ODTO> giftList = promotionGroup.getCommodities().stream().filter(ShoppingCartCommodityV4ODTO::getIsGift).collect(toList());
                            //可能存在没达到B端买赠门槛，此时giftList就会为空,但是促销组的非赠品还是不为空
                            if(SpringUtil.isNotEmpty(giftList)){
                                String promotionId = promotionGroup.getPromotionId();
                                List<Long> commodityIdList = giftList.stream().map(ShoppingCartCommodityV4ODTO::getCommodityId).collect(toList());
                                return new GiftLimitQuantityQueryDetailIDTO(Long.parseLong(promotionId),commodityIdList);
                            }
                            return null;

                        }
                ).filter(Objects::nonNull).collect(toList());

        Map<Long, Map<Long, BigDecimal>> giftLimitTotalQuantityMap = Maps.newHashMap();

        if(SpringUtil.isNotEmpty(detailList)){
            GiftLimitQuantityQueryIDTO giftLimitQuantityQueryIDTO = new GiftLimitQuantityQueryIDTO();
            giftLimitQuantityQueryIDTO.setDetailList(detailList);
            giftLimitQuantityQueryIDTO.setOrderId(orderId);
            List<GiftLeftQuantityODTO> giftLeftQuantityODTOList = giftLimitService.queryGiftLeftQuantityList(giftLimitQuantityQueryIDTO);
            giftLimitTotalQuantityMap = giftLeftQuantityODTOList.stream().collect(groupingBy(GiftLeftQuantityODTO::getGiftPrommotionId, toMap(GiftLeftQuantityODTO::getCommodityId, GiftLeftQuantityODTO::getTotalQuantity)));
        }

        return giftLimitTotalQuantityMap;
    }
    /**
     * 库存限量提示
     * @param commodityInventoryODTOMap
     * @param item
     */
    private void checkCommodityLimit(Map<Long, CommodityInventoryODTO> commodityInventoryODTOMap, ShoppingCartCommodityV4ODTO item) {
        if (MapUtils.isEmpty(commodityInventoryODTOMap)) {
            return;
        }
        if(commodityInventoryODTOMap.containsKey(item.getCommodityId())){
            BigDecimal quantity = item.getQuantity();
            CommodityInventoryODTO commodityInventoryODTO = commodityInventoryODTOMap.get(item.getCommodityId());
            if(null == commodityInventoryODTO || null == commodityInventoryODTO.getInventoryQuantity()){
                return;
            }
            BigDecimal inventoryQuantity = commodityInventoryODTO.getInventoryQuantity();
            if(!commodityInventoryODTO.getHaveInventory() ||  commodityInventoryODTO.getInventoryQuantity().compareTo(BigDecimal.ZERO) <= 0){
                if(quantity.compareTo(inventoryQuantity) > 0){
                    commodityInventoryODTO.setInventoryQuantity(BigDecimal.ZERO);
                    BigDecimal bigDecimal = inventoryQuantity.compareTo(BigDecimal.ZERO) < 0 ? BigDecimal.ZERO : inventoryQuantity;
                    this.setMessage(item,"库存不足，余量为" + bigDecimal.stripTrailingZeros().toPlainString());
//                shoppingCartODTO.setStockWarningTips("库存不足，余量为" + inventoryQuantity);
                    setWarnMessage(item.getCommodityName()+"库存不足，余量为"+bigDecimal.stripTrailingZeros().toPlainString()+"，请确认数量");
                }else {
                    commodityInventoryODTO.setInventoryQuantity(inventoryQuantity.subtract(quantity));
                }
            }else {
                commodityInventoryODTO.setInventoryQuantity(inventoryQuantity.subtract(quantity));
            }
            commodityInventoryODTOMap.put(item.getCommodityId(),commodityInventoryODTO);
        }
    }

    /**
     * 组合商品库存限量提示
     */
    private void checkCombiCommodityLimit(Map<Long, CommodityInventoryODTO> commodityInventoryODTOMap, ShoppingCartCommodityV4ODTO item) {
        if (MapUtils.isEmpty(commodityInventoryODTOMap)){
            return;
        }
        BigDecimal quantity = item.getQuantity();
        // 根据组合商品id查询子商品列表
        List<CommodityItemODTO> commodityItemList = toBService.getCommodityItemList(item.getCommodityId());
        if (CollectionUtils.isEmpty(commodityItemList)) {
            return;
        }
        BigDecimal minStock = toBService.calculateMinStock(commodityItemList, commodityInventoryODTOMap);
        if (quantity.compareTo(minStock) > 0) {
            this.setMessage(item, "库存不足");
            setWarnMessage(item.getCommodityName() + "库存不足");
        }
        for (CommodityItemODTO commodityItemODTO : commodityItemList) {
            Long commodityId = Long.valueOf(commodityItemODTO.getCommodityItemId());
            CommodityInventoryODTO commodityInventoryODTO = commodityInventoryODTOMap.get(commodityId);
            if (null == commodityInventoryODTO || null == commodityInventoryODTO.getInventoryQuantity()) {
                return;
            }
            //3C=3*（1A+2B）
            BigDecimal orderNum = quantity.multiply(commodityItemODTO.getCommodityNum());
            BigDecimal inventoryQuantity = commodityInventoryODTO.getInventoryQuantity();
            if (quantity.compareTo(minStock) > 0) {
                //TODO 验证库存不足时
                if (orderNum.compareTo(inventoryQuantity) > 0) {
                    commodityInventoryODTO.setInventoryQuantity(BigDecimal.ZERO);
                } else {
                    commodityInventoryODTO.setInventoryQuantity(inventoryQuantity.subtract(orderNum));
                }
            } else {
                commodityInventoryODTO.setInventoryQuantity(inventoryQuantity.subtract(orderNum));
            }
        }
    }

    /**
     * 普通商品逻辑
     * 买赠分组
     * @param cartODTO
     * @param xdaShoppingCartV4ODTOMap
     */
    private void setTbSalesPromotionCommodityGiftGroup(CartODTO cartODTO,Map<Long, ShoppingCartCommodityV4ODTO> xdaShoppingCartV4ODTOMap,Map<Long, BigDecimal> longBigDecimalMap){
        if(null == cartODTO || SpringUtil.isEmpty(cartODTO.getPromotionGroups())) return;
        List<GiftCommodityODTO> giftCommodityODTOList = cartODTO.getPromotionGroups().stream().flatMap(giftGroup -> {
            if(SpringUtil.isNotEmpty(giftGroup.getGiftList())){
                return giftGroup.getGiftList().stream();
            }
            return Stream.empty();
        }).collect(Collectors.toList());
        List<Long> giftCommodityIdList = giftCommodityODTOList.stream().map(GiftCommodityODTO::getCommodityId).collect(Collectors.toList());
        //查询赠送商品基本信息
        List<ShoppingCartCommodityV4ODTO> shoppingCartCommodityV4ODTOList = new ArrayList<>();
        if(SpringUtil.isNotEmpty(giftCommodityIdList)){
            shoppingCartCommodityV4ODTOList = this.getValidCommodityGift(giftCommodityIdList);
        }
        Map<Long, ShoppingCartCommodityV4ODTO> shoppingCartsGiftMap = shoppingCartCommodityV4ODTOList.stream().collect(Collectors.toMap(ShoppingCartCommodityV4ODTO::getCommodityId, e -> e));
        Map<Long, BigDecimal> finalLongBigDecimalMap = longBigDecimalMap;
        //价格中心返回数据结果集
        for (CartGroupODTO promotionId : cartODTO.getPromotionGroups()) {
            Integer enableCoupon = promotionId.getEnableCoupon();

            /* 初始化当前分组 */
            ShoppingCartGroupV4ODTO shoppingCartGroupV3ODTO = ShoppingCartGroupV4ODTO.initializationCartPromotionId(promotionId);
            List<CartCommodityODTO> commodityODTOList = promotionId.getCommodityODTOList();
            List<GiftCommodityODTO> giftList = promotionId.getGiftList();

            List<ShoppingCartCommodityV4ODTO> invalidateGroup = new ArrayList<>();

            List<ShoppingCartCommodityV4ODTO> normalGroup = new ArrayList<>();

            List<ShoppingCartCommodityV4ODTO> giftGroup = new ArrayList<>();

            commodityODTOList.forEach(item->{
                /* 商品基本信息 */
                ShoppingCartCommodityV4ODTO shoppingCartCommodityV4ODTO = xdaShoppingCartV4ODTOMap.get(item.getCommodityId());
                shoppingCartCommodityV4ODTO.setEnableCoupon(enableCoupon);
                shoppingCartCommodityV4ODTO.setQuantity(new BigDecimal(item.getNum()));
                if(finalLongBigDecimalMap.containsKey(item.getCommodityId())){
                    shoppingCartCommodityV4ODTO.setQuantity(new BigDecimal(item.getNum()).multiply(finalLongBigDecimalMap.get(item.getCommodityId())));
                }
                //当存在促销时不展示特价
                shoppingCartCommodityV4ODTO.setIsThPrice(0);
                shoppingCartCommodityV4ODTO.setThPrice(null);
                shoppingCartCommodityV4ODTO.setIsSpecialPrice(0);
                shoppingCartCommodityV4ODTO.setSpecialPrice(null);
                /* 是否需要计算，0 不计算，只获取特价促销信息，不计算总件数，总金额，不参与促销计算，1，参与促销计算
                 * 购物车中不计算商品为失效商品
                 * 经产品确认 失效商品只展示原价*/
                if(item.getComputeStatus().equals(0)){
                    shoppingCartCommodityV4ODTO.setCommodityPrice(item.getOriginPrice());
                    shoppingCartCommodityV4ODTO.setIsInvalidate(Boolean.TRUE);
                    invalidateGroup.add(shoppingCartCommodityV4ODTO);
                }else if(item.getComputeStatus().equals(1)){
                    // 促销方案 正常商品组
                    shoppingCartCommodityV4ODTO.setCommodityPrice(item.getOriginPrice());
                    shoppingCartCommodityV4ODTO.setSpecialPrice(item.getSpecialPrice());
                    if(null != item.getSpecialPrice() && null != item.getSpecialLimit()){
                        shoppingCartCommodityV4ODTO.setSpecialPriceLimit(BigDecimal.valueOf(item.getSpecialLimit()));
                    }
                    //买赠不需要到手价
                    if(SalesPromotionStatusEnums.GRADIENT_DISCOUNT.getCode() == promotionId.getPromotionType().intValue()){
                        if (null != shoppingCartGroupV3ODTO.getFullStatus() && shoppingCartGroupV3ODTO.getFullStatus() == 1) {
                            // 达到满减条件才设置到手价
                            shoppingCartCommodityV4ODTO.setDeliveryPrice(item.getPromotionPrice().setScale(2, RoundingMode.HALF_UP));
                            if ("kg".equals(shoppingCartCommodityV4ODTO.getCommodityUnitName().trim())) {
                                BigDecimal divide = shoppingCartCommodityV4ODTO.getDeliveryPrice().divide(BigDecimal.valueOf(2), 6, RoundingMode.HALF_UP);
                                shoppingCartCommodityV4ODTO.setDeliveryPrice(divide);
                            }
                        }
                        shoppingCartCommodityV4ODTO.setSumPrice(item.getSubTotal());
                    }
                    shoppingCartCommodityV4ODTO.setCouponDiscountAmount(item.getCouponDiscountAmount());
                    shoppingCartCommodityV4ODTO.setCouponId(item.getCouponId());
                    shoppingCartCommodityV4ODTO.setCouponUserId(item.getCouponUserId());
                    normalGroup.add(shoppingCartCommodityV4ODTO);
                }
            });
            shoppingCartODTO.setOriginalAmount(shoppingCartODTO.getOriginalAmount().add(promotionId.getTotalOriginalPrice()));
            shoppingCartODTO.setSummation(shoppingCartODTO.getSummation().add(promotionId.getSubTotal()));
            shoppingCartODTO.setNormalGroupAmount(shoppingCartODTO.getNormalGroupAmount().add(promotionId.getSubTotal()));
            longBigDecimalMap = new HashMap<>();
            if(SpringUtil.isNotEmpty(giftList)){

                List<Long> longList = giftList.stream().map(GiftCommodityODTO::getCommodityId).collect(Collectors.toList());
                longBigDecimalMap = xdaShoppingCartController.selectCommodityPackageSpecByCommodityIdList(longList);

                for (GiftCommodityODTO giftCommodityODTO : giftList) {
                    Long commodityId = giftCommodityODTO.getCommodityId();
                    //赠品相同时 需要重新创建对象赋值  避免不通方案使用同一个堆栈
                    ShoppingCartCommodityV4ODTO shoppingCartCommodityV4ODTO = new ShoppingCartCommodityV4ODTO();
                    if(SpringUtil.isEmpty(shoppingCartsGiftMap) || !shoppingCartsGiftMap.containsKey(commodityId)){
                        continue;
                    }
                    SpringUtil.copyProperties(shoppingCartsGiftMap.get(commodityId),shoppingCartCommodityV4ODTO);
                    if(null == shoppingCartCommodityV4ODTO || shoppingCartCommodityV4ODTO.getIsInvalidate()
                            || shoppingCartCommodityV4ODTO.getIsCanOrder() == null || !shoppingCartCommodityV4ODTO.getIsCanOrder()){
                        continue;
                    }
                    shoppingCartCommodityV4ODTO.setCommodityPrice(BigDecimal.ZERO);
                    BigDecimal quantity = new BigDecimal("0");
                    if(null != giftCommodityODTO.getNum()){
                        quantity = BigDecimal.valueOf(giftCommodityODTO.getNum());
                        if(longBigDecimalMap.containsKey(commodityId)){
                            quantity = quantity.multiply(longBigDecimalMap.get(commodityId));
                        }
                    }
                    BigDecimal quantityLimit = null;
                    if(null != giftCommodityODTO.getLimitNum() && giftCommodityODTO.getLimitNum() != 0){
                        quantityLimit = BigDecimal.valueOf(giftCommodityODTO.getLimitNum());
                        if(longBigDecimalMap.containsKey(commodityId)){
                            quantityLimit = quantityLimit.multiply(longBigDecimalMap.get(commodityId));
                        }
                    }

                    BigDecimal giftTotalLimit = null;
                    if(null != giftCommodityODTO.getTotalLimitNumber() && giftCommodityODTO.getTotalLimitNumber() != 0){
                        giftTotalLimit = BigDecimal.valueOf(giftCommodityODTO.getTotalLimitNumber());
                        if(longBigDecimalMap.containsKey(commodityId)){
                            giftTotalLimit = giftTotalLimit.multiply(longBigDecimalMap.get(commodityId));
                        }
                    }
                    shoppingCartCommodityV4ODTO.setIsGift(Boolean.TRUE);
                    shoppingCartCommodityV4ODTO.setQuantity(quantity);
                    shoppingCartCommodityV4ODTO.setGiftLimitNumber(quantityLimit);
                    shoppingCartCommodityV4ODTO.setGiftTotalLimitNumber(giftTotalLimit);
                    giftGroup.add(shoppingCartCommodityV4ODTO);
                }
            }
            //经APP沟通 促销分组内所有商品放在普通组中，根据字段区分
            if(SpringUtil.isNotEmpty(giftGroup))
                normalGroup.addAll(giftGroup);
            if(SpringUtil.isNotEmpty(invalidateGroup))
                shoppingCartODTO.getInvalidateGroup().getCommodities().addAll(invalidateGroup);
            shoppingCartGroupV3ODTO.setCommodities(normalGroup);
            if(SpringUtil.isNotEmpty(normalGroup)){
                shoppingCartODTO.getPromotionGroup().add(shoppingCartGroupV3ODTO);
            }
        }
    }
    public List<ShoppingCartCommodityV4ODTO> getValidCommodityGift(List<Long> commodityIds){
        XdaShoppingCartV3IDTO appIDTO = new XdaShoppingCartV3IDTO();
        appIDTO.setCommodityIdList(commodityIds);
        appIDTO.setOrderTime(deliveryDate);
        appIDTO.setStoreId(storeId);
        appIDTO.setIsShoppingCart(Boolean.TRUE);
        List<XdaShoppingCartV3ODTO> xdaCommodityInfoAppODTOList = xdaShoppingCartController.getValidCommodityGift(appIDTO);
        List<ShoppingCartCommodityV4ODTO> commodityODTOS = BeanCloneUtils.copyTo(xdaCommodityInfoAppODTOList, ShoppingCartCommodityV4ODTO.class);
        return commodityODTOS;
    }
    /**
     * 获取当日已定数量
     * @param commodityIdList
     * @param storeId
     * @return
     */
    private List<XdaCommodityLimit4AppODTO> getUsedQuantityCommodityByStoreIdMap(List<Long> commodityIdList,Long storeId,Integer type,Long promotionId){
        List<XdaCommodityLimit4AppODTO> xdaCommodityLimit4AppODTOS = new ArrayList<>();

        if(!commodityIdList.isEmpty()){
            String date = DateUtil.getDateFormate(deliveryDate, "yyyy-MM-dd");
            //客户当日订货数量
            if(null != storeId){
                xdaCommodityLimit4AppODTOS = orderListMapper.queryCommodityLimitByStoreId(date,storeId,type, commodityIdList,promotionId,null);
            }else {
                //所有客户订货数量
                xdaCommodityLimit4AppODTOS = orderListMapper.queryCommodityLimit(date, commodityIdList,promotionId);
            }
//            limitCommodityMap = xdaCommodityLimit4AppODTOS.stream().collect(Collectors.toMap(XdaCommodityLimit4AppODTO::getCommodityId,XdaCommodityLimit4AppODTO::getTotalQuantity));
        }
        return xdaCommodityLimit4AppODTOS;
    }
    /**
     * 特惠商品商品信息设置
     * @param thXdaShoppingCartMap
     * @param shoppingCartCommodityV4ODTOMap
     */
    private void thGroupAmountLogic(BigDecimal normalGroupAmount,Map<Long, XdaShoppingCart> thXdaShoppingCartMap,Map<Long, ShoppingCartCommodityV4ODTO> shoppingCartCommodityV4ODTOMap){
        if(SpringUtil.isEmpty(thXdaShoppingCartMap)) return;
        //查询是否有特惠方案
        XdaShoppingCartV2IDTO xdaShoppingCartV2IDTO = new XdaShoppingCartV2IDTO();
        xdaShoppingCartV2IDTO.setStoreId(storeId);
        xdaShoppingCartV2IDTO.setOrderTime(deliveryDate);
        BigDecimal orderTargetToDay = xdaCommodityFrontClient.findOrderTargetByStoreIdAndOrderTime(xdaShoppingCartV2IDTO);
        if(null == orderTargetToDay){
            shoppingCartODTO.setThGroups(ShoppingCartGroupV4ODTO.initialization());
            return;
        }
        List<ShoppingCartCommodityV4ODTO> thGroupList = new ArrayList<>();

        //移除购物车中的不再是特惠品的商品
        List<Long> shoppingCartThCommodityIdsNotInThCommoditySet = removeShoppingCartCommoditiesWithThType();

        shoppingCartCommodityV4ODTOMap.forEach((commodityId,value)->{
            if(thXdaShoppingCartMap.containsKey(commodityId)
            &&!shoppingCartThCommodityIdsNotInThCommoditySet.contains(commodityId)){
                XdaShoppingCart xdaShoppingCart = thXdaShoppingCartMap.get(commodityId);
                ShoppingCartCommodityV4ODTO newShoppingCartCommodityV4ODTO = newThlShoppingCartCommodityV4ODTO(value);
                newShoppingCartCommodityV4ODTO.setQuantity(xdaShoppingCart.getQuantity());
                newShoppingCartCommodityV4ODTO.setIsInvalidate(Boolean.TRUE);
                //当前商品是否可订货 ，判断当前客户是否有特惠方案
                if(null != newShoppingCartCommodityV4ODTO.getIsCanOrder() &&  newShoppingCartCommodityV4ODTO.getIsCanOrder()
                        && null != orderTargetToDay && !shoppingCartODTO.getThGroups().getIsInvalidate()){
                    // 判断是否满足特惠
                    if(normalGroupAmount.compareTo(orderTargetToDay) >= 0){
                        newShoppingCartCommodityV4ODTO.setIsInvalidate(Boolean.FALSE);
                        // 计算商品原价
                        BigDecimal originAmount = newShoppingCartCommodityV4ODTO.getCommodityPrice().multiply(xdaShoppingCart.getQuantity()).setScale(2, BigDecimal.ROUND_HALF_UP);
                        shoppingCartODTO.setOriginalAmount(shoppingCartODTO.getOriginalAmount().add(originAmount));

                        //计算商品特惠价格
                        BigDecimal thPrice = newShoppingCartCommodityV4ODTO.getThPrice().multiply(xdaShoppingCart.getQuantity()).setScale(2, BigDecimal.ROUND_HALF_UP);
                        shoppingCartODTO.setSummation(shoppingCartODTO.getSummation().add(thPrice));
                    }
                }else {
                    newShoppingCartCommodityV4ODTO.setIsInvalidate(Boolean.TRUE);
                }
                thGroupList.add(newShoppingCartCommodityV4ODTO);
            }

        });
        List<ShoppingCartCommodityV4ODTO> collect = thGroupList.stream().filter(item -> !item.getIsInvalidate()).collect(Collectors.toList());
        if(SpringUtil.isEmpty(collect)){
            shoppingCartODTO.getThGroups().setIsInvalidate(Boolean.TRUE);
        }
        if(SpringUtil.isNotEmpty(thGroupList)){
            List<ShoppingCartCommodityV4ODTO> thCommodityList = thGroupList.stream().sorted(Comparator.comparing(ShoppingCartCommodityV4ODTO::getIsInvalidate)).collect(Collectors.toList());
            thCommodityList.forEach(item->{
                item.setIsThPrice(1);
            });
            shoppingCartODTO.getThGroups().setCommodities(thCommodityList);
        }
    }

    private List<Long> removeShoppingCartCommoditiesWithThType() {
//        XdaTokenInfo tokenInfo = FastThreadLocalUtil.getXDA();
//        Long storeId =  tokenInfo.getStoreId();
//        QYAssert.isTrue(storeId != null, "未能获取到用户信息,请重新登录!");

        //移除购物车中不在特惠组中的商品
        List<Long> shoppingCartThCommodityIdsNotInThCommoditySet =
                xdaShoppingCartMapper.shopCartIdsNotInThCommoditySet(storeId,2);


        //异步删除购物车中已经不是特惠品的商品
        if(!CollectionUtils.isEmpty(shoppingCartThCommodityIdsNotInThCommoditySet)){
            xdaShoppingCartMapper.deleteShopCartWithCommodityType(storeId, shoppingCartThCommodityIdsNotInThCommoditySet,2);

        }

        return shoppingCartThCommodityIdsNotInThCommoditySet;
    }


    public ShoppingCartCommodityV4ODTO newThlShoppingCartCommodityV4ODTO(ShoppingCartCommodityV4ODTO shoppingCartCommodityV4ODTO){
        ShoppingCartCommodityV4ODTO newShoppingCartCommodityV4ODTO = BeanCloneUtils.copyTo(shoppingCartCommodityV4ODTO,ShoppingCartCommodityV4ODTO.class);
        newShoppingCartCommodityV4ODTO.setIsSpecialPrice(0);
        newShoppingCartCommodityV4ODTO.setSpecialPrice(null);
        newShoppingCartCommodityV4ODTO.setDeliveryPrice(null);
        //特惠不拷贝标签
        newShoppingCartCommodityV4ODTO.setTagV2List(null);
        newShoppingCartCommodityV4ODTO.setNormalQuantity(null);
        newShoppingCartCommodityV4ODTO.setSpecialQuantity(null);
        newShoppingCartCommodityV4ODTO.setPricePromotionId(null);
        newShoppingCartCommodityV4ODTO.setIsExceedLimit(null);
        newShoppingCartCommodityV4ODTO.setSpecialCouponDiscountAmount(null);
        newShoppingCartCommodityV4ODTO.setCouponDiscountAmount(null);
        newShoppingCartCommodityV4ODTO.setCouponUserId(null);
        newShoppingCartCommodityV4ODTO.setCouponId(null);
        return newShoppingCartCommodityV4ODTO;
    }
    /**
     * 普通商品处理分组 赠品数量
     * @param xdaShoppingCartMap
     * @param shoppingCartCommodityV4ODTOMap
     */
    private void normalGroupAmountLogic(Map<Long, XdaShoppingCart> xdaShoppingCartMap, Map<Long, ShoppingCartCommodityV4ODTO> shoppingCartCommodityV4ODTOMap,CartODTO cartODTO){
        if(SpringUtil.isEmpty(xdaShoppingCartMap)){
            return;
        }
        List<ShoppingCartCommodityV4ODTO> normalComm = new ArrayList<>(),
                invalidateComm = new ArrayList<>();
        List<CartCommodityODTO> normalCommodityGroup = new ArrayList<>();
        if(SpringUtil.isNotEmpty(cartODTO.getValidateGroup())){
            normalCommodityGroup = cartODTO.getValidateGroup();
        }
        List<Long> commodityIdList = new ArrayList<>();
        if(null != cartODTO && SpringUtil.isNotEmpty(cartODTO.getPromotionGroups())){
            List<CartGroupODTO> promotionGroups = cartODTO.getPromotionGroups();
            commodityIdList = promotionGroups.stream().map(item -> {
                return item.getCommodityODTOList().stream().map(CartCommodityODTO::getCommodityId).collect(Collectors.toList());
            }).flatMap(List::stream).collect(Collectors.toList());
        }
        Map<Long, CartCommodityODTO> normalCommodityIdMap = normalCommodityGroup.stream().collect(Collectors.toMap(CartCommodityODTO::getCommodityId, e -> e));
        List<Long> finalCommodityIdList = commodityIdList;
        xdaShoppingCartMap.forEach((commodityId, cart)->{
            /* 校验购物车商品是否存在 & 不是促销分组内的商品 */
            if(shoppingCartCommodityV4ODTOMap.containsKey(commodityId) && !finalCommodityIdList.contains(commodityId)){
                ShoppingCartCommodityV4ODTO shoppingCartCommodityV4ODTO = shoppingCartCommodityV4ODTOMap.get(commodityId);
                BigDecimal orderQuantity = cart.getQuantity();
                shoppingCartCommodityV4ODTO.setQuantity(orderQuantity);
                //销售箱规
                BigDecimal salesBoxCapacity = shoppingCartCommodityV4ODTO.getSalesBoxCapacity();
                if(normalCommodityIdMap.containsKey(commodityId)){
                    CartCommodityODTO cartCommodityODTO = normalCommodityIdMap.get(commodityId);
                    //是否可用券
                    Integer enableCoupon = cartCommodityODTO.getEnableCoupon();
                    shoppingCartCommodityV4ODTO.setEnableCoupon(enableCoupon);
                    /* 理论上不会为空 */
                    if (null != cartCommodityODTO.getSpecialPrice() && cartCommodityODTO.getOriginPrice().compareTo(cartCommodityODTO.getSpecialPrice()) > 0) {
                        BigDecimal specialQuantity;
                        BigDecimal normalQuantity;
                        BigDecimal availableLimit = cartCommodityODTO.getAvailableLimit();
                        // 计算 availableLimit 为 销售箱规 的最大可用倍数
                        BigDecimal maxUsableLimit = salesBoxCapacity.multiply(availableLimit.divide(salesBoxCapacity, 0, RoundingMode.FLOOR));
                        //可用限购
                        BigDecimal availableStoreLimit = cartCommodityODTO.getAvailableStoreLimit();
                        //可用限量
                        BigDecimal availableTotalLimit = cartCommodityODTO.getAvailableTotalLimit();
                        Integer specialLimit = cartCommodityODTO.getSpecialLimit();

                        //处理特价限购提示
                        toBService.handleSpecialLimitWarningTips(shoppingCartCommodityV4ODTO, storeId, cart.getCommodityId(),
                                cart.getQuantity(), availableTotalLimit, availableStoreLimit, specialLimit);

                        if (maxUsableLimit.compareTo(orderQuantity) > 0) {
                            specialQuantity = orderQuantity;
                        } else {
                            specialQuantity = maxUsableLimit;
                        }
                        String tagName;
                        if (99999 == specialLimit || 0 == specialLimit) {
                            //不限购
                            tagName = "特价";
                        } else {
                            tagName = "特价|每天限" + specialLimit + "份";
                        }
                        CommodityTextTagInfoODTO specialTag = new CommodityTextTagInfoODTO(tagName, "#FF5733", commodityId);
                        List<CommodityTextTagInfoODTO> tagV2List = new ArrayList<>();
                        tagV2List.add(specialTag);
                        shoppingCartCommodityV4ODTO.setTagV2List(tagV2List);
                        normalQuantity = orderQuantity.subtract(specialQuantity);
                        shoppingCartCommodityV4ODTO.setNormalQuantity(normalQuantity.stripTrailingZeros());
                        shoppingCartCommodityV4ODTO.setSpecialQuantity(specialQuantity.stripTrailingZeros());
                        shoppingCartCommodityV4ODTO.setPricePromotionId(cartCommodityODTO.getPricePromotionId());
                        if (specialQuantity.compareTo(BigDecimal.ZERO) > 0) {
                            shoppingCartCommodityV4ODTO.setIsSpecialPrice(1);
                            shoppingCartCommodityV4ODTO.setSpecialPrice(cartCommodityODTO.getSpecialPrice());
                        } else {
                            //无特价，全部按原价购买
                            shoppingCartCommodityV4ODTO.setIsSpecialPrice(0);
                            shoppingCartCommodityV4ODTO.setSpecialPrice(cartCommodityODTO.getOriginPrice());
                            shoppingCartCommodityV4ODTO.setTagV2List(null);
                        }
                    }
                    if(null != cartCommodityODTO.getOriginPrice()){
                        shoppingCartCommodityV4ODTO.setCommodityPrice(cartCommodityODTO.getOriginPrice());
                    }
                    shoppingCartCommodityV4ODTO.setCouponDiscountAmount(cartCommodityODTO.getCouponDiscountAmount());
                    shoppingCartCommodityV4ODTO.setSpecialCouponDiscountAmount(cartCommodityODTO.getSpecialCouponDiscountAmount());
                    shoppingCartCommodityV4ODTO.setCouponId(cartCommodityODTO.getCouponId());
                    shoppingCartCommodityV4ODTO.setCouponUserId(cartCommodityODTO.getCouponUserId());
                }
                /* 处理商品是否失效 */
                Map<Integer, ShoppingCartCommodityV4ODTO> integerShoppingCartCommodityV4ODTOMap = commodityGroup(shoppingCartCommodityV4ODTO, cart);
                if(null !=  integerShoppingCartCommodityV4ODTOMap.get(1)){
                    normalComm.add(newNormalShoppingCartCommodityV4ODTO(integerShoppingCartCommodityV4ODTOMap.get(1)));
                }else if(null !=  integerShoppingCartCommodityV4ODTOMap.get(2)){
                    ShoppingCartCommodityV4ODTO newShoppingCartCommodityV3ODTO = integerShoppingCartCommodityV4ODTOMap.get(2);
                    newShoppingCartCommodityV3ODTO.setIsInvalidate(Boolean.TRUE);
                    invalidateComm.add(newNormalShoppingCartCommodityV4ODTO(newShoppingCartCommodityV3ODTO));
                }
            }
            shoppingCartODTO.setNormalGroupAmount(shoppingCartODTO.getSummation());
        });
        shoppingCartODTO.getNormalGroup().setCommodities(normalComm);
        shoppingCartODTO.getInvalidateGroup().setCommodities(invalidateComm);
    }

    /**
     * 处理商品是否失效
     * @param shoppingCartCommodityV4ODTO
     * @param cart
     * @return
     */
    private Map<Integer,ShoppingCartCommodityV4ODTO> commodityGroup(ShoppingCartCommodityV4ODTO shoppingCartCommodityV4ODTO,XdaShoppingCart cart){
        Map<Integer,ShoppingCartCommodityV4ODTO> map = new HashMap<>();
        shoppingCartCommodityV4ODTO.setIsThPrice(0);
        shoppingCartCommodityV4ODTO.setShoppingCartId(cart.getId());
        shoppingCartCommodityV4ODTO.setQuantity(cart.getQuantity());
        //判断是否可订货 如果不可订货放入对应的失效组
        if(null != shoppingCartCommodityV4ODTO.getIsCanOrder() && shoppingCartCommodityV4ODTO.getIsCanOrder()){
            shoppingCartCommodityV4ODTO.setAbleAdd(Boolean.TRUE);
            // 计算普通商品组 商品限量设置提示
            map.put(1,shoppingCartCommodityV4ODTO);
            // 计算商品原价
            BigDecimal originAmount = shoppingCartCommodityV4ODTO.getCommodityPrice().multiply(cart.getQuantity()).setScale(2, BigDecimal.ROUND_HALF_UP);
            shoppingCartODTO.setOriginalAmount(shoppingCartODTO.getOriginalAmount().add(originAmount));
            //计算商品优惠之后的价格
            BigDecimal yhPrice;
            if (Objects.equals(shoppingCartCommodityV4ODTO.getIsSpecialPrice(), 1)) {
                BigDecimal specialTotal = shoppingCartCommodityV4ODTO.getSpecialPrice()
                        .multiply(shoppingCartCommodityV4ODTO.getSpecialQuantity())
                        .setScale(2, RoundingMode.HALF_UP);

                BigDecimal normalTotal = shoppingCartCommodityV4ODTO.getCommodityPrice()
                        .multiply(shoppingCartCommodityV4ODTO.getNormalQuantity())
                        .setScale(2, RoundingMode.HALF_UP);

                yhPrice = specialTotal.add(normalTotal);
            } else {
                yhPrice = shoppingCartCommodityV4ODTO.getSpecialPrice().multiply(cart.getQuantity()).setScale(2, RoundingMode.HALF_UP);
            }
            shoppingCartODTO.setSummation(shoppingCartODTO.getSummation().add(yhPrice));
        }else {
            shoppingCartCommodityV4ODTO.setAbleAdd(Boolean.FALSE);
            shoppingCartCommodityV4ODTO.setIsInvalidate(Boolean.TRUE);
            map.put(2,shoppingCartCommodityV4ODTO);
        }
        return map;
    }
    public ShoppingCartCommodityV4ODTO newNormalShoppingCartCommodityV4ODTO(ShoppingCartCommodityV4ODTO shoppingCartCommodityV4ODTO){
        ShoppingCartCommodityV4ODTO newShoppingCartCommodityV4ODTO = BeanCloneUtils.copyTo(shoppingCartCommodityV4ODTO,ShoppingCartCommodityV4ODTO.class);
        newShoppingCartCommodityV4ODTO.setIsThPrice(0);
        newShoppingCartCommodityV4ODTO.setThPrice(null);
        newShoppingCartCommodityV4ODTO.setDeliveryPrice(null);
        return newShoppingCartCommodityV4ODTO;
    }
    /**
     * 普通商品组优惠后的价格 用于计算是否满足特惠规则
     * --需要保证商品基本信息接口返回原价&特价--
     * @param shoppingCartCommodityV4ODTOList 商品基本信息
     * @param xdaShoppingCartMap 购物车商品
     * @return 普通商品组优惠后的价
     */
    private BigDecimal getNormalGroupAmount(List<ShoppingCartCommodityV4ODTO> shoppingCartCommodityV4ODTOList,Map<Long, XdaShoppingCart> xdaShoppingCartMap,CartODTO cartODTO){
        Map<Long, CartCommodityODTO> promotionCommodityIdMap = new HashMap<>();
        if(null != cartODTO && SpringUtil.isNotEmpty(cartODTO.getPromotionGroups())){
            List<CartCommodityODTO> promotionCommodityList  = cartODTO.getPromotionGroups().stream().filter(item->null != item.getFullStatus() && item.getFullStatus() == 1).flatMap(item->{
                return item.getCommodityODTOList().stream();
            }).collect(Collectors.toList());
            promotionCommodityIdMap = promotionCommodityList.stream().collect(Collectors.toMap(CartCommodityODTO::getCommodityId, e -> e));
        }
        Map<Long, ShoppingCartCommodityV4ODTO> shoppingCartCommodityV4ODTOMap = shoppingCartCommodityV4ODTOList.stream().collect(Collectors.toMap(ShoppingCartCommodityV4ODTO::getCommodityId, e -> e));
        Map<Long, CartCommodityODTO> finalPromotionCommodityIdMap = promotionCommodityIdMap;
        return shoppingCartCommodityV4ODTOList.stream().collect(CollectorsUtil.summingBigDecimalMax(item -> {
            if(null != shoppingCartCommodityV4ODTOMap.get(item.getCommodityId())
                    && null != shoppingCartCommodityV4ODTOMap.get(item.getCommodityId()).getIsCanOrder()
                    && shoppingCartCommodityV4ODTOMap.get(item.getCommodityId()).getIsCanOrder()){
                if(null != finalPromotionCommodityIdMap && finalPromotionCommodityIdMap.containsKey(item.getCommodityId())){
                    CartCommodityODTO cartCommodityODTO = finalPromotionCommodityIdMap.get(item.getCommodityId());
                    return cartCommodityODTO.getSubTotal();
                }

                if (null != xdaShoppingCartMap && xdaShoppingCartMap.containsKey(item.getCommodityId())) {
                    if (Objects.equals(item.getIsSpecialPrice(), 1)) {
                        BigDecimal specialAmount = item.getSpecialPrice().multiply(item.getSpecialQuantity()).setScale(2, RoundingMode.HALF_UP);
                        BigDecimal normalAmount = item.getCommodityPrice().multiply(item.getNormalQuantity()).setScale(2, RoundingMode.HALF_UP);
                        return specialAmount.add(normalAmount);
                    } else {
                        return item.getSpecialPrice().multiply(xdaShoppingCartMap.get(item.getCommodityId()).getQuantity()).setScale(2, RoundingMode.HALF_UP);
                    }
                }
            }

            return BigDecimal.ZERO;
        }));
    }
    private CartODTO getTbSalesPromotionList(Map<Long, XdaShoppingCart> xdaShoppingCartMap,Map<Long, ShoppingCartCommodityV4ODTO> shoppingCartCommodityV4ODTOMap,Map<Long, BigDecimal> finalLongBigDecimalMap){
        CartIDTO cartIDTO = new CartIDTO();
        cartIDTO.setShopId(storeId);
        cartIDTO.setOrderTime(deliveryDate);
        cartIDTO.setChannelType(MarketSourceTypeEnum.XDA.getCode());
        List<CartCommodityIDTO> cartCommodityIDTOList = new ArrayList<>();
        xdaShoppingCartMap.forEach((key, value)->{
            if(shoppingCartCommodityV4ODTOMap.containsKey(key)){
                ShoppingCartCommodityV4ODTO shoppingCartCommodityV4ODTO = shoppingCartCommodityV4ODTOMap.get(key);
                CartCommodityIDTO cartCommodityIDTO = new CartCommodityIDTO();
                cartCommodityIDTO.setComputeStatus(shoppingCartCommodityV4ODTO.getIsCanOrder()? 1:0);
                cartCommodityIDTO.setShoppingCartId(value.getId());
                cartCommodityIDTO.setCommodityId(key);
                cartCommodityIDTO.setCategoryId(shoppingCartCommodityV4ODTO.getXdaSecondCategoryId());
                cartCommodityIDTO.setNum(value.getQuantity().intValue());
                if(finalLongBigDecimalMap.containsKey(key)){
                    BigDecimal num =value.getQuantity()
                            .divide(finalLongBigDecimalMap.get(key),0, BigDecimal.ROUND_DOWN)
                            .setScale(0, BigDecimal.ROUND_DOWN);
                    cartCommodityIDTO.setNum(num.intValue());
                }
                cartCommodityIDTOList.add(cartCommodityIDTO);
            }
        });
        if(SpringUtil.isEmpty(cartCommodityIDTOList)){
            return new CartODTO();
        }
        cartIDTO.setCartCommodityIDTOList(cartCommodityIDTOList);
        cartIDTO.setNeedAvailableLimit(1);

        if (couponUserId != null) {
            // 兼容不使用优惠券传0和-1的情况
            cartIDTO.setFullCouponId(couponUserId > 0 ? couponUserId : 0L);
        }
        return mtPromotionClient.commodityForCart(cartIDTO);
    }
    /**
     * 获取商品种类数量
     * @return
     */
    public Integer getValidCommoditySum(){
        List<XdaShoppingCart> shoppingCarts = this.queryShoppingCart(storeId,null,null);
        if(shoppingCarts.isEmpty()) return 0;
        XdaShoppingCartV3IDTO xdaShoppingCartV3IDTO = new XdaShoppingCartV3IDTO();
        xdaShoppingCartV3IDTO.setStoreId(storeId);
        xdaShoppingCartV3IDTO.setOrderTime(deliveryDate);
        xdaShoppingCartV3IDTO.setCommodityIdList(shoppingCarts.stream().map(XdaShoppingCart::getCommodityId).distinct().collect(Collectors.toList()));
        xdaShoppingCartV3IDTO.setIsShoppingCart(Boolean.TRUE);
        List<XdaShoppingCartV3ODTO> varietySum = xdaShoppingCartController.getVarietySum(xdaShoppingCartV3IDTO);
        varietySum.removeIf(item -> null != item.getIsCanOrder() && !item.getIsCanOrder());
        return varietySum.size();
    }

    public static Integer getShoppingCartNoZeroVarietySum(ShoppingCartV4ODTO shoppingCartV4ODTO){
        //克隆一个购物车查询对象，不影响原来的
        ShoppingCartV4ODTO cloneShoppingCart = BeanCloneUtils.copyTo(shoppingCartV4ODTO, ShoppingCartV4ODTO.class);

        //移除克隆出的购物车中买赠组中赠品为0的商品
        cloneShoppingCart.getPromotionGroup().forEach(
                item -> {
                    if(SpringUtil.isNotEmpty(item.getCommodities())){
                        item.getCommodities().removeIf(subItem-> BooleanUtils.isTrue(subItem.getIsGift()) && Objects.equals(subItem.getQuantity(),BigDecimal.ZERO));
                    }
                }
        );

        return getValidCommoditySum(cloneShoppingCart);

    }

    public static Integer getValidCommoditySum(ShoppingCartV4ODTO shoppingCartV4ODTO){
        Set<Long> commodityList = new HashSet<>();
        if(null != shoppingCartV4ODTO.getNormalGroup()){
            if(SpringUtil.isNotEmpty(shoppingCartV4ODTO.getNormalGroup().getCommodities())){
                List<Long> longList = shoppingCartV4ODTO.getNormalGroup().getCommodities().stream().map(ShoppingCartCommodityV4ODTO::getCommodityId).collect(Collectors.toList());
                if(SpringUtil.isNotEmpty(longList)){
                    commodityList.addAll(longList);
                }
            }
            List<ShoppingCartGroupV4ODTO> promotionGroup = shoppingCartV4ODTO.getPromotionGroup();
            if(SpringUtil.isNotEmpty(promotionGroup)){
                List<ShoppingCartCommodityV4ODTO> promotionGroupCommodityList = promotionGroup.stream().flatMap(item -> item.getCommodities().stream()).collect(Collectors.toList());
                List<Long> promotionCommoditySize = promotionGroupCommodityList.stream().filter(item -> !item.getIsInvalidate()).map(ShoppingCartCommodityV4ODTO::getCommodityId).collect(Collectors.toList());
                if(SpringUtil.isNotEmpty(promotionCommoditySize)){
                    commodityList.addAll(promotionCommoditySize);
                }
            }
            if(SpringUtil.isNotEmpty(shoppingCartV4ODTO.getThGroups().getCommodities())){
                List<Long> longList = shoppingCartV4ODTO.getThGroups().getCommodities().stream().filter(item -> !item.getIsInvalidate()).map(ShoppingCartCommodityV4ODTO::getCommodityId).collect(Collectors.toList());
                if(SpringUtil.isNotEmpty(longList)){
                    commodityList.addAll(longList);
                }
            }
        }
        return commodityList.size();
    }
    public BigDecimal getValidCommodityNumSum(ShoppingCartV4ODTO shoppingCartV4ODTO){
        BigDecimal commodityNumSum = BigDecimal.ZERO;
        if(null != shoppingCartV4ODTO.getNormalGroup()){
            if(SpringUtil.isNotEmpty(shoppingCartV4ODTO.getNormalGroup().getCommodities())){
                commodityNumSum = commodityNumSum.add(shoppingCartV4ODTO.getNormalGroup().getCommodities().stream().collect(CollectorsUtil.summingBigDecimalMax(ShoppingCartCommodityV4ODTO::getQuantity)));
            }
            List<ShoppingCartGroupV4ODTO> promotionGroup = shoppingCartV4ODTO.getPromotionGroup();
            if(SpringUtil.isNotEmpty(promotionGroup)){
                List<ShoppingCartCommodityV4ODTO> promotionGroupCommodityList = promotionGroup.stream().flatMap(item -> item.getCommodities().stream()).collect(Collectors.toList());
                List<ShoppingCartCommodityV4ODTO> promotionCommodity = promotionGroupCommodityList.stream().filter(item -> !item.getIsInvalidate()).collect(Collectors.toList());
                if(SpringUtil.isNotEmpty(promotionCommodity)){
                    commodityNumSum = commodityNumSum.add(promotionCommodity.stream().collect(CollectorsUtil.summingBigDecimalMax(ShoppingCartCommodityV4ODTO::getQuantity)));
                }
            }
            if(SpringUtil.isNotEmpty(shoppingCartV4ODTO.getThGroups().getCommodities())){
                List<ShoppingCartCommodityV4ODTO> thCommodity = shoppingCartV4ODTO.getThGroups().getCommodities().stream().filter(item -> !item.getIsInvalidate()).collect(Collectors.toList());
                if(SpringUtil.isNotEmpty(thCommodity)){
                    commodityNumSum = commodityNumSum.add(thCommodity.stream().collect(CollectorsUtil.summingBigDecimalMax(ShoppingCartCommodityV4ODTO::getQuantity)));
                }
            }
        }
        return commodityNumSum;
    }
    /**
     * 调用接口获取商品基本信息
     * @param shoppingCarts
     * @param orderDate
     * @param storeId
     * @return
     */
    public List<ShoppingCartCommodityV4ODTO> getValidCommodity(List<XdaShoppingCart> shoppingCarts,Date orderDate,Long storeId){
        List<Long> commodityIds =  shoppingCarts.stream().map(XdaShoppingCart :: getCommodityId).distinct().collect(Collectors.toList());

        // 调用接口获取商品基本信息
        List<XdaShoppingCartV3ODTO> xdaCommodityInfoAppODTOList = queryCommodityBaseInfo(commodityIds,orderDate, storeId, true);
        if (xdaCommodityInfoAppODTOList.isEmpty()){
            return Collections.EMPTY_LIST;
        }
        List<ShoppingCartCommodityV4ODTO> commodityODTOS = BeanCloneUtils.copyTo(xdaCommodityInfoAppODTOList, ShoppingCartCommodityV4ODTO.class);
        setIsFreezeRoundingGroup(commodityODTOS);
        return commodityODTOS;
    }
    /**
     * 查询商品基本信息
     * @param commodityIds
     * @return
     */
    public List<XdaShoppingCartV3ODTO> queryCommodityBaseInfo(List<Long> commodityIds, Date orderTime, Long storeId, boolean isShopCart){
        XdaShoppingCartV3IDTO appIDTO = new XdaShoppingCartV3IDTO();
        appIDTO.setCommodityIdList(commodityIds);
        appIDTO.setOrderTime(orderTime);
        appIDTO.setStoreId(storeId);
        appIDTO.setIsShoppingCart(Boolean.TRUE);
        List<XdaShoppingCartV3ODTO> xdaCommodityInfoAppODTOList = xdaShoppingCartController.getShopCartList(appIDTO);

        if(commodityIds.size() != xdaCommodityInfoAppODTOList.size()){
            Set<Long> differenceSet = new HashSet<>(commodityIds);
            differenceSet.removeAll(xdaCommodityInfoAppODTOList.stream().map(XdaShoppingCartV3ODTO::getCommodityId).collect(Collectors.toSet()));
            log.error("部分商品未能找到!ids:{}", Arrays.toString(differenceSet.toArray()));
            if (isShopCart && !differenceSet.isEmpty()){
                xdaShoppingCartMapper.deleteShopCart(storeId, new ArrayList<>(differenceSet));
            }
        }
        return xdaCommodityInfoAppODTOList;
    }
    /**
     * 设置是否凑整
     * @param commodityODTOS
     */
    private void setIsFreezeRoundingGroup(List<ShoppingCartCommodityV4ODTO> commodityODTOS){
        List<Long> commodityIdList = queryFreezeGroupMap(commodityODTOS.stream().map(ShoppingCartCommodityV4ODTO::getCommodityId).distinct().collect(Collectors.toList()));
        //设置凑整商品
        commodityODTOS.forEach(item->{
            item.setIsLimit(0);
            item.setLimitNumber(null);
            item.setIsFreezeRoundingGroup(0);
            if(commodityIdList.contains(item.getCommodityId())){
                item.setIsFreezeRoundingGroup(1);
                item.setIsFreezeRoundingGroupMultiple(30);
            }
        });
    }
    /**
     * 查询凑整商品
     * @param commodityIdList
     * @return
     */
    public List<Long> queryFreezeGroupMap(List<Long> commodityIdList){
        if(SpringUtil.isEmpty(commodityIdList)){
            return Collections.EMPTY_LIST;
        }
        return commodityFreezeGroupMapper.selectOldCommodity(commodityIdList);
    }
    private String getStoreDuration(Long storeId){
        Example example = new Example(StoreDuration.class);
        example.createCriteria().andEqualTo("storeId", storeId);
        StoreDuration storeDuration = storeDurationMapper.selectOneByExample(example);
        if(null == storeDuration ){
            return "";
        }
        return "客户订货时间段："+storeDuration.getBeginTime() + "~"+ storeDuration.getEndTime();
    }
    /**
     * 根据条件查询购物车
     * @param storeId
     * @param commodityId
     * @return
     */
    public List<XdaShoppingCart> queryShoppingCart(Long storeId, Long commodityId, Integer commodityType){
        Example ex = new Example(XdaShoppingCart.class);
        Example.Criteria criteria = ex.createCriteria();
        criteria.andEqualTo("storeId", storeId);
        if(null != commodityType){
            criteria.andEqualTo("commodityType",commodityType);
        }
        if(null != commodityId){
            criteria.andEqualTo("commodityId", commodityId);
        }
        return xdaShoppingCartMapper.selectByExample(ex);
    }
    /**
     * 商品分组提示信息
     * @param shoppingCartCommodityV4ODTO
     * @param message
     */
    private void setMessage(ShoppingCartCommodityV4ODTO shoppingCartCommodityV4ODTO,String message){
        shoppingCartCommodityV4ODTO.setStockWarningTips(message);
        shoppingCartCommodityV4ODTO.setAbleAdd(Boolean.FALSE);
        shoppingCartODTO.setCanSettlement(Boolean.FALSE);
    }
    /**
     *  给结算和下单用，下单和结算凭此依据判断购物车商品能否结算
     */
    private void setWarnMessage(String message){
        shoppingCartODTO.setWarnMessage(message);
        shoppingCartODTO.setCanSettlement(Boolean.FALSE);
    }
}
