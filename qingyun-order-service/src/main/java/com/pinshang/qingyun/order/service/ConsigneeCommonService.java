package com.pinshang.qingyun.order.service;

import com.pinshang.qingyun.base.configure.expand.QYAssert;
import com.pinshang.qingyun.base.enums.IogisticsModelEnums;
import com.pinshang.qingyun.base.enums.SaleReturnReasonEnums;
import com.pinshang.qingyun.base.enums.YesOrNoEnums;
import com.pinshang.qingyun.box.utils.BeanCloneUtils;
import com.pinshang.qingyun.common.dto.DelayMsgIDTO;
import com.pinshang.qingyun.common.service.DelayMsgClient;
import com.pinshang.qingyun.order.mapper.CommodityMapper;
import com.pinshang.qingyun.order.mapper.ShopMapper;
import com.pinshang.qingyun.order.mapper.bigShop.DdReceiveDocLogMapper;
import com.pinshang.qingyun.order.mapper.bigShop.DdReceiveDocPicMapper;
import com.pinshang.qingyun.order.mapper.entry.commodity.CommodityBasicEntry;
import com.pinshang.qingyun.order.model.bigShop.DdReceiveDocLog;
import com.pinshang.qingyun.order.model.bigShop.DdReceiveDocPic;
import com.pinshang.qingyun.order.model.order.SaleReturnOrderPic;
import com.pinshang.qingyun.order.model.shop.Shop;
import com.pinshang.qingyun.order.vo.commodity.CommodityVO;
import com.pinshang.qingyun.order.vo.order.SaleReturnAddVo;
import com.pinshang.qingyun.order.vo.order.SaleReturnItemAddPicVO;
import com.pinshang.qingyun.order.vo.order.SaleReturnItemAddVo;
import com.pinshang.qingyun.shop.service.consignment.ConsignmentClient;
import com.pinshang.qingyun.xd.wms.dto.StockIDTO;
import com.pinshang.qinyun.cache.enums.RedisDelayQueueEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.entity.Example;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 代销商通用service
 * @Author: sk
 * @Date: 2023/4/12
 */
@Slf4j
@Service
public class ConsigneeCommonService {
    @Autowired
    private ShopMapper shopMapper;
    @Autowired
    private SaleReturnService saleReturnService;
    @Autowired
    private ConsignmentClient consignmentClient;

    @Autowired
    private CommodityMapper commodityMapper;
    @Autowired
    private DdReceiveDocLogMapper ddReceiveDocLogMapper;
    @Autowired
    private WeChatSendMessageService weChatSendMessageService;
    @Autowired
    private DelayMsgClient delayMsgClient;
    @Autowired
    private DdReceiveDocPicMapper ddReceiveDocPicMapper;


    /**
     * 获取收货单图片(以pc端保存的图片为主)
     */
    private void getDdReceiveDocPicList(Long docId, Map<String, List<SaleReturnItemAddPicVO>> picMap,
                                                         Map<String, List<SaleReturnItemAddPicVO>> videoMap) {

        Set<DdReceiveDocPic> resultList = new HashSet<>();

        Example picEx = new Example(DdReceiveDocPic.class);
        picEx.createCriteria().andEqualTo("docId", docId);
        List<DdReceiveDocPic> ddReceiveDocPicList = ddReceiveDocPicMapper.selectByExample(picEx);
        if(CollectionUtils.isNotEmpty(ddReceiveDocPicList)) {
            Map<Long, List<DdReceiveDocPic>> pcPicMap = ddReceiveDocPicList.stream().filter(p -> DdReceiveDocPic.ReceiveTypeEnums.PC.getCode().equals(p.getReceiveType())).collect(Collectors.groupingBy(DdReceiveDocPic::getCommodityId));
            for(DdReceiveDocPic pic : ddReceiveDocPicList) {

                 // 以pc端保存的图片为主
                if(pcPicMap.containsKey(pic.getCommodityId())) {
                    resultList.addAll(pcPicMap.get(pic.getCommodityId()));
                }else {
                    resultList.add(pic);
                }
            }

            List<DdReceiveDocPic> picList = resultList.stream().filter(picItem -> {return SaleReturnOrderPic.PicTypeEnums.PIC.getCode().equals(picItem.getPicType());}).collect(Collectors.toList());
            List<DdReceiveDocPic> videoList = resultList.stream().filter(picItem -> {return SaleReturnOrderPic.PicTypeEnums.VIDEO.getCode().equals(picItem.getPicType());}).distinct().collect(Collectors.toList());

            Map<Long, List<DdReceiveDocPic>> docPicMap = picList.stream().collect(Collectors.groupingBy(DdReceiveDocPic::getCommodityId));
            Map<Long, List<DdReceiveDocPic>> docVideoMap = videoList.stream().collect(Collectors.groupingBy(DdReceiveDocPic::getCommodityId));

            docPicMap.forEach((commodityId, list) -> {
                picMap.put(commodityId.toString(), BeanCloneUtils.copyTo(list, SaleReturnItemAddPicVO.class));
            });

            docVideoMap.forEach((commodityId, list) -> {
                videoMap.put(commodityId.toString(), BeanCloneUtils.copyTo(list, SaleReturnItemAddPicVO.class));
            });
        }
    }

    /**
     * 大店收货，少货时候生成少货单(通过延迟队列)
     * 不判断库存情况
     * @param shortList
     */
    @Transactional(rollbackFor = Exception.class)
    public Boolean addBigShopShortReturnDelay(Long docId, Long enterpriseId, Long userId) {
        Example logEx = new Example(DdReceiveDocLog.class);
        logEx.createCriteria().andEqualTo("docId", docId)
                .andEqualTo("shortStatus", YesOrNoEnums.NO.getCode())
                .andGreaterThan("shortReceiveQuantity", 0);
        List<DdReceiveDocLog> logList = ddReceiveDocLogMapper.selectByExample(logEx);

        if(CollectionUtils.isNotEmpty(logList)) {
            try {
                Map<String, List<SaleReturnItemAddPicVO>> picMap = new HashMap<>();
                Map<String, List<SaleReturnItemAddPicVO>> videoMap = new HashMap<>();
                getDdReceiveDocPicList(docId, picMap, videoMap);

                List<Long> commodityIdList = logList.stream().map(item -> item.getCommodityId()).collect(Collectors.toList());
                CommodityVO vo = new CommodityVO();
                vo.setCommodityIdList(commodityIdList);
                List<CommodityBasicEntry> commodityBasicList = commodityMapper.findCommodityBasicListByParam(vo);
                Map<String, CommodityBasicEntry> commodityBasicMap = commodityBasicList.stream().collect(Collectors.toMap(CommodityBasicEntry::getCommodityId, Function.identity()));

                // 生成少货单
                addBigShopShortReturn(logList, commodityBasicMap, enterpriseId, userId, picMap, videoMap);

                // 更新为已经生成少货单
                DdReceiveDocLog updateLog = new DdReceiveDocLog();
                updateLog.setShortStatus(YesOrNoEnums.YES.getCode());
                updateLog.setUpdateTime(new Date());
                ddReceiveDocLogMapper.updateByExampleSelective(updateLog, logEx);
            }catch (Exception e){
                log.warn("大店延迟生成少货单失败, docId {}", docId, e);
                weChatSendMessageService.sendWeChatMessage("大店延迟生成少货单失败, docId " + docId);

                // 报错后再放入延迟队列30分钟
                String data = docId + "," + enterpriseId + "," + userId;
                DelayMsgIDTO delayMsgIDTO = new DelayMsgIDTO(
                        RedisDelayQueueEnum.DD_RECEIVE_SHORT_RETURN.getCode(),
                        data,
                        30L,
                        TimeUnit.MINUTES);
                delayMsgClient.addDelayQueue(delayMsgIDTO);
            }
        }

        return Boolean.TRUE;
    }


    /**
     * 大店收货，少货时候生成少货单
     * 不判断库存情况
     * @param shortList
     */
    @Transactional(rollbackFor = Exception.class)
    public void addBigShopShortReturn(List<DdReceiveDocLog> shortList, Map<String, CommodityBasicEntry> commodityBasicMap,
                                      Long enterpriseId, Long userId,
                                      Map<String, List<SaleReturnItemAddPicVO>> picMap,
                                      Map<String, List<SaleReturnItemAddPicVO>> videoMap) {
        DdReceiveDocLog ddReceiveDocLog = shortList.get(0);
        SaleReturnAddVo vo = new SaleReturnAddVo();
        vo.setShopId(ddReceiveDocLog.getShopId());
        vo.setStallId(ddReceiveDocLog.getStallId());
        vo.setXdQuality(false);
        vo.setCreateId(userId);
        vo.setRemark(ddReceiveDocLog.getRemark());

        Shop shop = shopMapper.selectByPrimaryKey(vo.getShopId());
        vo.setStoreId(shop.getStoreId());
        vo.setEnterpriseId(enterpriseId);
        List<SaleReturnItemAddVo> saleReturnItems = new ArrayList<>();
        for(DdReceiveDocLog item : shortList){
            SaleReturnItemAddVo saleReturnItem = new SaleReturnItemAddVo();
            saleReturnItem.setReturnReason(SaleReturnReasonEnums.少货.getCode());
            saleReturnItem.setCommodityId(item.getCommodityId() + "");
            saleReturnItem.setReturnQuantity(item.getShortReceiveQuantity());

            saleReturnItems.add(saleReturnItem);
        }
        vo.setSaleReturnItems(saleReturnItems);

        // 设置商品价格和供应商，仓库
        saleReturnService.setXdPriceSupplierWarehouse(saleReturnItems, shop, vo.getStallId());

        List<SaleReturnItemAddVo> sendingItems = new ArrayList<>();
        //直通、配送item
        List<SaleReturnItemAddVo> companyItems = new ArrayList<>();
        for (SaleReturnItemAddVo saleReturnItem : saleReturnItems) {
            QYAssert.isTrue(null != saleReturnItem.getPrice(), "商品信息异常，价格不能为空");
            String commodtiyCode = commodityBasicMap.get(saleReturnItem.getCommodityId()).getCommodityCode();
            if(IogisticsModelEnums.DIRECT_SENDING.getCode() == saleReturnItem.getLogisticsModel()){
                QYAssert.isTrue(saleReturnItem.getSupplierId() != null, commodtiyCode + " 请设置首选供应商");
                sendingItems.add(saleReturnItem);
            }else{
                QYAssert.isTrue(saleReturnItem.getWarehouseId() != null, commodtiyCode + " 请设置首选仓库");
                companyItems.add(saleReturnItem);
            }
        }

        List<StockIDTO> stockList = new ArrayList<>();
        //处理直送
        if(sendingItems.size() > 0){
            Map<Long, List<SaleReturnItemAddVo>> sendingMap = sendingItems.stream().collect(Collectors.groupingBy(SaleReturnItemAddVo::getSupplierId));
            saleReturnService.doSaveSaleReturn(vo, sendingMap, IogisticsModelEnums.DIRECT_SENDING.getCode(), shop , picMap, videoMap);

        }
        //处理直通、配送
        if(companyItems.size() > 0){
            Map<Long, List<SaleReturnItemAddVo>> companyMap = companyItems.stream().collect(Collectors.groupingBy(SaleReturnItemAddVo::getWarehouseId));
            saleReturnService.doSaveSaleReturn(vo, companyMap, IogisticsModelEnums.DISPATCHING.getCode(), shop, picMap, videoMap);

        }
    }
}
