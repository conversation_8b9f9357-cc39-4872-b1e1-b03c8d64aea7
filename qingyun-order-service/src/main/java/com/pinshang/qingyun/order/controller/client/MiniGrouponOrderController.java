package com.pinshang.qingyun.order.controller.client;

import com.alibaba.fastjson.JSON;
import com.pinshang.qingyun.base.configure.expand.QYAssert;
import com.pinshang.qingyun.base.constant.QingyunConstant;
import com.pinshang.qingyun.box.utils.SpringUtil;
import com.pinshang.qingyun.order.dto.miniGroupon.MiniGrouponAutoOrderIDTO;
import com.pinshang.qingyun.order.dto.miniGroupon.MiniGrouponAutoOrderItemODTO;
import com.pinshang.qingyun.order.dto.miniGroupon.MiniGrouponAutoOrderODTO;
import com.pinshang.qingyun.order.service.miniGroupon.IMiniGrouponOrderService;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/miniGroupon/order")
@Slf4j
public class MiniGrouponOrderController {
    @Autowired
    private IMiniGrouponOrderService iMiniGrouponOrderService;

    @PostMapping("/miniGrouponAutoOrder")
    @ApiOperation(value = "mini团购自动下单")
    public String miniGrouponAutoOrder(@RequestBody List<MiniGrouponAutoOrderIDTO> idtoList, HttpServletResponse response){
        response.addHeader(QingyunConstant.WRAP_RESPONSE_FLAG,QingyunConstant.WRAP_RESPONSE_FLAG_VALUE);
        QYAssert.isTrue(SpringUtil.isNotEmpty(idtoList),"请输入有效的参数");
        Map<String,List<MiniGrouponAutoOrderIDTO>> allDataMap = idtoList.stream().collect(Collectors.groupingBy(MiniGrouponAutoOrderIDTO::getCutTime));
        List<MiniGrouponAutoOrderODTO> resultInfoList = new ArrayList<>();
        //根据截单时间分批做下单操作
        for (Map.Entry<String, List<MiniGrouponAutoOrderIDTO>> itemMap : allDataMap.entrySet()) {
            MiniGrouponAutoOrderODTO resultInfo = new MiniGrouponAutoOrderODTO(itemMap.getKey());
            try {
                List<MiniGrouponAutoOrderItemODTO> itemList = iMiniGrouponOrderService.miniGrouponAutoOrder(itemMap.getValue());
                resultInfoList.add(resultInfo.initSuccess(itemList));
            }catch (Exception e){
                log.error("团购自动下单异常：",e);
                resultInfoList.add(resultInfo.initFail(e.getMessage()));
            }
        }
        return JSON.toJSONString(resultInfoList);
    }
}
