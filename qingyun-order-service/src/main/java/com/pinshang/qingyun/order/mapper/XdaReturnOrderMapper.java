package com.pinshang.qingyun.order.mapper;


import com.pinshang.qingyun.base.mybatis.MyMapper;
import com.pinshang.qingyun.order.dto.xda.QueryXdaComplaintDTO;
import com.pinshang.qingyun.order.dto.xda.XdaComplaintCommodityItemDTO;
import com.pinshang.qingyun.order.dto.xda.XdaComplaintCommodityQuantityDTO;
import com.pinshang.qingyun.order.dto.xda.XdaComplaintOrderODTO;
import com.pinshang.qingyun.order.dto.xda.tda.ReturnOrderODTO;
import com.pinshang.qingyun.order.dto.xda.tda.ReturnOrderSearchIDTO;
import com.pinshang.qingyun.order.dto.xda.tda.WarehousePendingODTO;
import com.pinshang.qingyun.order.dto.xda.tda.WarehousePendingSearchIDTO;
import com.pinshang.qingyun.order.model.xda.XdaReturnOrder;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface XdaReturnOrderMapper extends MyMapper<XdaReturnOrder> {

    /***
     * 退货单列表
     */
    List<ReturnOrderODTO> queryReturnOrderList(ReturnOrderSearchIDTO vo);

    ReturnOrderODTO selectById(@Param("id") Long id);

    List<WarehousePendingODTO> queryWarehousePendingList(WarehousePendingSearchIDTO vo);

    List<XdaComplaintCommodityItemDTO> queryReturnOrderByDeliveryDateAndStoreId(@Param("storeId") Long storeId, @Param("deliveryDate") String deliveryDate, @Param("returnOrderType") Integer returnOrderType);

    List<XdaComplaintOrderODTO> queryComplaintOrderList(QueryXdaComplaintDTO dto);

    XdaReturnOrder selectByReturnOrderCode(@Param("returnOrderCode") String returnOrderCode);

    XdaReturnOrder selectByReturnOrderSeq(@Param("returnOrderSeq") String returnOrderSeq);

    List<XdaComplaintCommodityQuantityDTO> queryCompleteCommodityQuantity(@Param("storeId") Long storeId, @Param("deliveryDate") String deliveryDate, @Param("returnOrderType") Integer returnOrderType, @Param("commodityIdList") List<Long> commodityIdList);

}

