package com.pinshang.qingyun.order.mapper;

import com.pinshang.qingyun.base.mybatis.MyMapper;
import com.pinshang.qingyun.order.dto.cup.LineGroupODTO;
import com.pinshang.qingyun.order.model.dictionary.Dictionary;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface DictionaryMapper extends MyMapper<Dictionary>{

    List<Dictionary> queryDictionaryConfigList(@Param("optionCodeList")List<String> optionCodeList);

    List<LineGroupODTO> findLineGroups();
}
