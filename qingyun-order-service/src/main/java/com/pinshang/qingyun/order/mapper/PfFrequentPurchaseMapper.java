package com.pinshang.qingyun.order.mapper;

import java.util.List;
import java.util.Set;

import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import com.pinshang.qingyun.base.mybatis.MyMapper;
import com.pinshang.qingyun.order.model.order.PfFrequentPurchase;


@Repository
public interface PfFrequentPurchaseMapper extends MyMapper<PfFrequentPurchase>{

   List<PfFrequentPurchase> queryFrequentPurchase(@Param("storeId") Long storeId ,@Param("idList") Set<Long> idList);

   int batchUpdate(@Param("purchaseList") List<PfFrequentPurchase> frequentPurchaseList);
}