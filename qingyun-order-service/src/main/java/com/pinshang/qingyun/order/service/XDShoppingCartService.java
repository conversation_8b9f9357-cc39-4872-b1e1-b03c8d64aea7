package com.pinshang.qingyun.order.service;

import com.pinshang.qingyun.base.configure.expand.QYAssert;
import com.pinshang.qingyun.base.enums.IogisticsModelEnums;
import com.pinshang.qingyun.base.enums.ShopShopStatusEnums;
import com.pinshang.qingyun.base.enums.order.OrderTypeEnum;
import com.pinshang.qingyun.base.enums.shop.ShopTypeEnums;
import com.pinshang.qingyun.base.spring.SpringBeanFinder;
import com.pinshang.qingyun.box.utils.DateTimeUtil;
import com.pinshang.qingyun.box.utils.DateUtil;
import com.pinshang.qingyun.box.utils.JsonUtil;
import com.pinshang.qingyun.box.utils.SpringUtil;
import com.pinshang.qingyun.order.constant.ThreadPoolBeanConstants;
import com.pinshang.qingyun.order.dto.OrderReferenceDetailODTO;
import com.pinshang.qingyun.order.dto.XDCommodityODTO;
import com.pinshang.qingyun.order.dto.XDShopCommodityODTO;
import com.pinshang.qingyun.order.enums.DeliveryBatchTypeEnum;
import com.pinshang.qingyun.order.job.GenerateCreateXdOrderTasks;
import com.pinshang.qingyun.order.mapper.*;
import com.pinshang.qingyun.order.mapper.entry.MdShopOrderSettingEntry;
import com.pinshang.qingyun.order.mapper.entry.commodity.CommodityResultEntry;
import com.pinshang.qingyun.order.mapper.entry.order.ShoppingCartEntry;
import com.pinshang.qingyun.order.mapper.entry.order.ShoppingCartItemEntry;
import com.pinshang.qingyun.order.mapper.entry.order.XdProposedOrderSettingEntry;
import com.pinshang.qingyun.order.model.order.ShoppingCart;
import com.pinshang.qingyun.order.model.order.XdShoppingCartTemp;
import com.pinshang.qingyun.order.model.order.XdShoppingCartTempError;
import com.pinshang.qingyun.order.model.shop.Shop;
import com.pinshang.qingyun.order.util.OrderUtil;
import com.pinshang.qingyun.order.util.StallUtils;
import com.pinshang.qingyun.order.util.ThreadLocalUtils;
import com.pinshang.qingyun.order.vo.commodity.CommodityListRequestVO;
import com.pinshang.qingyun.order.vo.order.*;
import com.pinshang.qingyun.order.vo.shop.ReceiveOrderVo;
import com.pinshang.qingyun.price.dto.commodity.CommodityListRequestIDTO;
import com.pinshang.qingyun.price.dto.commodity.CommodityResultODTO;
import com.pinshang.qingyun.price.service.ProductPriceModelClient;
import com.pinshang.qingyun.report.dto.xd.XdCommodityTaxODTO;
import com.pinshang.qingyun.report.service.XdCommodityTaxClient;
import com.pinshang.qingyun.shop.dto.shopCommodity.ShopCommodityInfoODTO;
import com.pinshang.qingyun.xd.order.dto.XdOrderSaleStatisticsODTO;
import com.pinshang.qingyun.xd.order.service.XdOrderSaleStatisticsClient;
import com.pinshang.qingyun.xd.product.dto.XdShopCommodityLackODTO;
import com.pinshang.qingyun.xd.product.service.XdShopCommodityLackClient;
import com.pinshang.qingyun.xd.wms.dto.ShopCommodityStockDTO;
import com.pinshang.qingyun.xd.wms.dto.ShopStockDTO;
import com.pinshang.qingyun.xd.wms.service.XdStockClient;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.redisson.api.RBucket;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import tk.mybatis.mapper.entity.Example;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @Author: sk
 * @Date: 2019/11/28
 */

@Slf4j
@Service(value = "xdShoppingCartService")
public class XDShoppingCartService {

    @Autowired
    private ShoppingCartService shoppingCartService;

    @Autowired
    private OrderService orderService;

    @Autowired
    private ShoppingCartMapper shoppingCartMapper;

    @Autowired
    private ShopMapper shopMapper;

    @Autowired
    private ProductService productService;

    @Autowired
    private CommodityMapper commodityMapper;

    @Lazy
    @Autowired
    private MdShopOrderSettingService mdShopOrderSettingService;

    @Autowired
    private OrderMapper orderMapper;

    @Autowired
    private XdCommodityTaxClient xdCommodityTaxClient;

    @Autowired
    private XdShopCommodityLackClient xdShopCommodityLackClient;

    @Autowired
    private XdOrderSaleStatisticsClient xdOrderSaleStatisticsClient;

    @Autowired
    private XdStockClient xdStockClient;

    @Autowired
    private RedissonClient redissonClient;

    @Autowired
    private XdShoppingCartTempMapper xdShoppingCartTempMapper;

    @Autowired
    private XdShoppingCartTempErrorMapper xdShoppingCartTempErrorMapper;

    @Autowired
    private MdShopOrderSettingMapper mdShopOrderSettingMapper;

    @Autowired
    private GenerateCreateXdOrderTasks generateCreateXdOrderTasks;
    @Autowired
    private ShopService shopService;
    @Autowired
    private ProductPriceModelClient productPriceModelClient;
    @Autowired
    private CommonService commonService;
    @Autowired
    private WeChatSendMessageService weChatSendMessageService;
    @Autowired
    private BStockService bStockService;

    /**
     * 根据门店id获取前置仓商品(门店订货通用设置没有的过滤掉)
     * @param storeId
     * @return
     */
    public List<XDShopCommodityODTO> getShopCommoditylist(Long storeId){
        List<XDShopCommodityODTO> commodityInfoODTOList = new ArrayList<>();
        Shop shop = shopService.getShopByStoreId(storeId);

        // 查询门店下面可采购的商品
        CommodityListRequestVO vo = new CommodityListRequestVO();
        vo.setShopId(shop.getId());
        List<Long> commodityPurchaseList = commodityMapper.findShopCommodityPurchaseList(vo);
        if(CollectionUtils.isEmpty(commodityPurchaseList)){
            return new ArrayList<>();
        }

        CommodityListRequestIDTO idto = new CommodityListRequestIDTO();
        idto.setStoreId(storeId + "");
        idto.setCommodityPurchaseIdList(commodityPurchaseList);
        List<CommodityResultODTO> commodityResultList = productPriceModelClient.findStoreCommodityList(idto);
        if(CollectionUtils.isEmpty(commodityResultList)){
            return new ArrayList<>();
        }

        List<XDShopCommodityODTO> shopCommodityIdList = new ArrayList<>();
        for(CommodityResultODTO commodityResult : commodityResultList){
            XDShopCommodityODTO shopCommodityODTO = new XDShopCommodityODTO();
            shopCommodityODTO.setCommodityId(Long.valueOf(commodityResult.getCommodityId()));
            shopCommodityODTO.setCommodityPackageSpec(commodityResult.getCommodityPackageSpec());
            shopCommodityODTO.setSalesBoxCapacity(commodityResult.getXdSalesBoxCapacity());
            shopCommodityIdList.add(shopCommodityODTO);
        }
        //List<XDShopCommodityODTO> shopCommodityIdList = commodityMapper.selectXdShopCommodityList(storeId);
        //if(CollectionUtils.isEmpty(shopCommodityIdList)){
        //    return new ArrayList<>();
        //}
        List<String> commodityIdList = shopCommodityIdList.stream().map(item -> item.getCommodityId()+"").collect(Collectors.toList());
        // 没有在门店订货通用设置里面过滤掉
        List<MdShopOrderSettingEntry> settingList = mdShopOrderSettingService.queryMdShopOrderSettingListByIds(storeId, commodityIdList);
        if(CollectionUtils.isEmpty(settingList)){
            return new ArrayList<>();
        }
        Map<Long, MdShopOrderSettingEntry> settMap = settingList.stream().collect(Collectors.toMap(MdShopOrderSettingEntry::getCommodityId, Function.identity()));

        for(XDShopCommodityODTO com : shopCommodityIdList){
            MdShopOrderSettingEntry entry = settMap.get(com.getCommodityId());
            if(entry != null && StringUtils.isNotBlank(entry.getDefaultSupplierEndTime()) && StringUtils.isNotBlank(entry.getDefaultWarehouseEndTime())){
                com.setDeleveryTimeRange(entry.getDeleveryTimeRange());
                commodityInfoODTOList.add(com);
            }
        }
        return commodityInfoODTOList;
    }

    /**
     * 线程执行时间
     * @param threadPool
     * @param currentTimeMillis
     */
    public void threadOver(ThreadPoolExecutor threadPool, long currentTimeMillis, String remark){
        try {
            boolean loop = true;
            do {
                //等待所有线程执行完毕当前任务结束
                loop = !threadPool.awaitTermination(2, TimeUnit.SECONDS);//等待2秒
            } while (loop);

            if (loop != true) {
                log.info(remark+ " 所有线程执行完毕");
            }

        } catch (InterruptedException e) {
            log.error(remark+" 线程执行时间异常",e);
        } finally {
            log.info(remark+" 耗时：" + (System.currentTimeMillis() - currentTimeMillis) + " 毫秒");
        }
    }

    /**
     *建议订货量并且加入临时购物车(鲜道前置仓)
     * @return
     */
    @Async
    public void batchAddShoppingCartTemp()  {
        String day = getDate();
        // 查询建议订货量系数
        XdProposedOrderSettingEntry settingEntry = orderMapper.getXdProposedOrderSettingEntry();

        // 查询所有前置仓(营业中)
        List<Shop> shopList = this.getAllXDShop();
        if(CollectionUtils.isEmpty(shopList)){return;}

        long currentTimeMillis = System.currentTimeMillis();

        // 获取线程池
        ThreadPoolTaskExecutor threadPool = (ThreadPoolTaskExecutor) SpringBeanFinder.getBean(ThreadPoolBeanConstants.ORDER_THREADPOOL);

        for(Shop shop : shopList){
            //创建任务并提交到线程池中
            threadPool.execute(new Runnable() {
                @Override
                public void run() {
                    try {
                        xdAddShoppingCartTempByShopId(shop,day,settingEntry);
                    } catch (Exception e) {
                        log.error("建议订货量并且加入临时购物车异常: ",e);
                        weChatSendMessageService.sendWeChatMessage("建议订货量并且加入临时购物车异常");
                    }
                }
            });
            try {
                Thread.sleep(10);
            } catch (InterruptedException e) {
                log.error("建议订货量并且加入临时购物车异常: ",e);
            }
        }

    }

    /**
     * 鲜道按一个门店一个门店的加入临时购物车
     * @param shop
     * @param day
     * @param settingEntry
     */
    public void xdAddShoppingCartTempByShopId(Shop shop,String day,XdProposedOrderSettingEntry settingEntry){
        List<XdShoppingCartTemp> xdShoppingCartTempList = new ArrayList<>();
        Long shopId = shop.getId();

        // 查询前置仓的商品
        List<XDShopCommodityODTO> commodityIdList = getShopCommoditylist(shop.getStoreId());
        if(CollectionUtils.isEmpty(commodityIdList)){return;}

        //获取销售量map
        Map<Long,Integer> saleStatisticsMap = getSaleStatisticsMap(shopId,day);

        //查询正常库存
        Map<Long,ShopStockDTO> stockMap = getStockMap(shopId);


        // 查询前置仓缺品被查看次数
        Map<Long,Integer> lackMap = getlackMap(shopId,day);

        // 查询损耗
        Map<String,Object> breakageMap = getBreakageMap(day,shopId);
        Map<Long,BigDecimal> normalBreakageMap = breakageMap.get("normalBreakageMap") != null ? (Map<Long, BigDecimal>) breakageMap.get("normalBreakageMap") : new HashMap<>();
        Map<Long,BigDecimal> overNormalBreakageMap = breakageMap.get("overNormalBreakageMap") != null ? (Map<Long, BigDecimal>) breakageMap.get("overNormalBreakageMap") :new HashMap<>();

        for(XDShopCommodityODTO commodityODTO : commodityIdList){
            // 获取特定日期增量
            BigDecimal specialDayQuantity = getSpecialDayQuantity(commodityODTO.getDeleveryTimeRange(),settingEntry);

            Long commodityId = commodityODTO.getCommodityId();
            XdShoppingCartTemp cartTemp = new XdShoppingCartTemp();
            cartTemp.setCreateId(-1L);
            cartTemp.setCreateTime(new Date());
            cartTemp.setShopId(shopId);
            cartTemp.setStoreId(shop.getStoreId());
            cartTemp.setCommodityId(commodityId);

            ShopStockDTO shopStockDTO = stockMap.get(commodityId);
            // 当app未上架时，商品的建议订货量为0
            if(null != shopStockDTO && shopStockDTO.getAppStatus() == 0){
                // 商品A的建议订货量（份数）=销售量*1+正常库存*（-1）+缺品被查看次数*0.2+损耗1（过保&临保）*（-1）+损耗2（非过保非临保）*1+ x（特定日期增量）
                BigDecimal saleShares = saleStatisticsMap.get(commodityId) != null ? new BigDecimal(saleStatisticsMap.get(commodityId)) : BigDecimal.ZERO;
                BigDecimal stockShares = new BigDecimal(shopStockDTO.getStockNumber());
                BigDecimal lackShares = lackMap.get(commodityId) != null ? new BigDecimal(lackMap.get(commodityId)) : BigDecimal.ZERO;
                BigDecimal normalBreakageShares = normalBreakageMap.get(commodityId) != null ? normalBreakageMap.get(commodityId) : BigDecimal.ZERO;
                BigDecimal overNormalBreakageShares = overNormalBreakageMap.get(commodityId) != null ? overNormalBreakageMap.get(commodityId) : BigDecimal.ZERO;

                BigDecimal shares1 = saleShares.multiply(settingEntry.getSaleRatio());
                BigDecimal shares2 = stockShares.multiply(settingEntry.getStockRatio());
                BigDecimal shares3 = lackShares.multiply(settingEntry.getDefectiveRatio());
                BigDecimal shares4 = normalBreakageShares.multiply(settingEntry.getLoss1Ratio());
                BigDecimal shares5 = overNormalBreakageShares.multiply(settingEntry.getLoss2Ratio());

                BigDecimal finalShares = shares1.add(shares2).add(shares3).add(shares4).add(shares5).add(specialDayQuantity);
                // 得出负数的建议订货份数，则取0.
                finalShares = finalShares.compareTo(BigDecimal.ZERO) > 0 ? finalShares : BigDecimal.ZERO;

                // 注：建议订货量（份数），应该等于“销售箱规/包装规格”的整数倍，不够的话，向上取整。
                BigDecimal times = commodityODTO.getSalesBoxCapacity().divide(commodityODTO.getCommodityPackageSpec(),2,BigDecimal.ROUND_HALF_UP);
                // 有余数
                if(times != null && times.compareTo(BigDecimal.ZERO) > 0 && finalShares.remainder(times).compareTo(BigDecimal.ZERO) > 0 ){
                    BigDecimal c = finalShares.divide(times,0,BigDecimal.ROUND_DOWN);
                    finalShares = c.add(new BigDecimal("1")).multiply(times);
                }
                cartTemp.setQuantity(finalShares.multiply(commodityODTO.getCommodityPackageSpec()));
            }else {
                cartTemp.setQuantity(BigDecimal.ZERO);
            }

            xdShoppingCartTempList.add(cartTemp);
        }
        if(!CollectionUtils.isEmpty(xdShoppingCartTempList)){
            // 批量加入临时购物车
            if(xdShoppingCartTempList.size() > 2000){
                batchInsertXdShoppingCartTemp(xdShoppingCartTempList);
            }else {
                xdShoppingCartTempMapper.insertList(xdShoppingCartTempList);
            }
        }

    }

    /**
     * 批量加入
     * @param cs
     */
    public void batchInsertXdShoppingCartTemp(List<XdShoppingCartTemp> cs) {
        int index = 0;
        int count = 2000;
        while (true) {
            List<XdShoppingCartTemp> items = cs.stream().skip(index).limit(count).collect(Collectors.toList());
            if (items.size() > 0) {
                // 一次插入2000条
                xdShoppingCartTempMapper.insertList(items);
                index += items.size();
            } else {
                break;
            }
        }
    }

    /**
     * 获取前置仓商品库存
     * @param shopId
     * @return
     */
    public Map<Long,ShopStockDTO> getStockMap(Long shopId){
        Map<Long,ShopStockDTO> stockMap = new HashMap<>();
        List<ShopStockDTO> stockList = xdStockClient.queryShopStock(shopId);
        if(CollectionUtils.isEmpty(stockList)){
            return new HashMap<>();
        }
        stockMap = stockList.stream().collect(Collectors.toMap(ShopStockDTO::getCommodityId, Function.identity()));
        return stockMap;
    }
    /**
     * 获取销售量 map
     * @param shopId
     * @param day
     * @return
     */
     public Map<Long,Integer> getSaleStatisticsMap(Long shopId,String day){
         Map<Long,Integer> saleStatisticsMap = new HashMap<>();
         List<XdOrderSaleStatisticsODTO> saleStatisticsList = xdOrderSaleStatisticsClient.queryXdSaleStatisticsList(shopId,DateUtil.parseDate(day,"yyyy-MM-dd"));
         if(CollectionUtils.isEmpty(saleStatisticsList)){
             return new HashMap<>();
         }
         saleStatisticsMap = saleStatisticsList.stream().collect(Collectors.toMap(XdOrderSaleStatisticsODTO::getCommodityId,XdOrderSaleStatisticsODTO::getTotalNumber,(key1 , key2)-> key2));
         return saleStatisticsMap;
     }

    /**
     * 获取仓缺品被查看次数 map
     * @param shopId
     * @param day
     * @return
     */
     public Map<Long,Integer> getlackMap(Long shopId,String day){
         Map<Long,Integer> lackMap = new HashMap<>();
         List<XdShopCommodityLackODTO> lackList = xdShopCommodityLackClient.queryShopLackCommodityListByDate(day,shopId);
         if(CollectionUtils.isEmpty(lackList)){
             return new HashMap<>();
         }
         lackMap = lackList.stream().collect(Collectors.toMap(XdShopCommodityLackODTO::getCommodityId,XdShopCommodityLackODTO::getLackQuantity,(key1 , key2)-> key2));
         return lackMap;
     }

    /**
     * 查询损耗
     * @param day
     * @param shopId
     * @return
     */
     public Map<String,Object> getBreakageMap(String day, Long shopId){
         Map<String,Object> map = new HashMap<>();
         Map<Long,BigDecimal> normalBreakageMap = new HashMap<>();
         Map<Long,BigDecimal> overNormalBreakageMap = new HashMap<>();
         // 查询订货信息,销售信息,损耗信息
         List<XdCommodityTaxODTO> taxList = xdCommodityTaxClient.findXdCommodityTaxList(day,day,shopId,0L);
         if(CollectionUtils.isEmpty(taxList)){
             return new HashMap<>();
         }

         taxList.forEach(item->{
             normalBreakageMap.put(item.getCommodityId(),item.getNormalBreakageQuantity());
             overNormalBreakageMap.put(item.getCommodityId(),item.getOverNormalBreakageQuantity());
         });

         map.put("normalBreakageMap",normalBreakageMap);
         map.put("overNormalBreakageMap",overNormalBreakageMap);
         return map;
     }


    /**
     * 获取建议订货量 时间
     * @return
     */
    public String getDate(){
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.DATE,-1);
        return DateUtil.getDateFormate(calendar.getTime(),"yyyy-MM-dd");
    }


    /**
     *根据当前日期获取特定日期增量
     * @return
     */
    public BigDecimal getSpecialDayQuantity(String deleveryTimeRange, XdProposedOrderSettingEntry settingEntry){
        String time = OrderUtil.getOrderTime(deleveryTimeRange);
        BigDecimal quantity = BigDecimal.ZERO;
        Calendar cal = Calendar.getInstance();
        cal.setTime(DateUtil.parseDate(time,"yyyy-MM-dd"));
        int weekDay = cal.get(Calendar.DAY_OF_WEEK);
        switch(weekDay) {
            case 1 : quantity = settingEntry.getDay7(); break;
            case 2 : quantity = settingEntry.getDay1(); break;
            case 3 : quantity = settingEntry.getDay2(); break;
            case 4 : quantity = settingEntry.getDay3(); break;
            case 5 : quantity = settingEntry.getDay4(); break;
            case 6 : quantity = settingEntry.getDay5(); break;
            case 7 : quantity = settingEntry.getDay6(); break;
            default: break;
        }
        return quantity;
    }

    /**
     * 从临时购物车加入购物车
     */
    @Async
    public void batchAddShoppingCart(){
        // 查询鲜道临时购物车
        Example cartTempEx = new Example(XdShoppingCartTemp.class);
        cartTempEx.setOrderByClause("shop_id ASC");
        List<XdShoppingCartTemp> cartTempList = xdShoppingCartTempMapper.selectByExample(cartTempEx);
        if(CollectionUtils.isEmpty(cartTempList)){return;}

        Map<Long, List<XdShoppingCartTemp>> cartTempMap = cartTempList.stream().collect(Collectors.groupingBy(XdShoppingCartTemp::getShopId));
        long currentTimeMillis = System.currentTimeMillis();

        // 获取线程池
        ThreadPoolTaskExecutor threadPool = (ThreadPoolTaskExecutor) SpringBeanFinder.getBean(ThreadPoolBeanConstants.ORDER_THREADPOOL);

        for(List<XdShoppingCartTemp> cartList : cartTempMap.values()){
            // 创建任务并提交到线程池中
            threadPool.execute(new Runnable() {
                @Override
                public void run() {
                    try {
                        batchAddShoppingCartByShopId(cartList);
                    } catch (Exception e) {
                        log.error("从临时购物车加入购物车异常: ",e);
                    }
                }
            });

          try {
            Thread.sleep(10);
          } catch (InterruptedException e) {
            log.error("从临时购物车加入购物车异常: ",e);
          }
        }

    }

    /**
     * 批量加入购物车
     */
    public void batchAddShoppingCartByShopId(List<XdShoppingCartTemp> cartList){
        List<Long> shopIdList = new ArrayList<>();
        List<Long> commodityIdList = new ArrayList<>();
        for(XdShoppingCartTemp temp : cartList){
            ThreadLocalUtils.setXd(true);
            ShoppingCartVo shoppingCartVo = new ShoppingCartVo();
            shoppingCartVo.setCreateId(-1L);
            shoppingCartVo.setStoreId(temp.getStoreId());
            shoppingCartVo.setEnterpriseId(78L);
            shoppingCartVo.setInternal(true);
            shoppingCartVo.setCommodityId(temp.getCommodityId());
            shoppingCartVo.setQuantity(temp.getQuantity());
            shoppingCartVo.setStallId(-1L);
            //调用加入购物车
            try {
                shoppingCartService.addShoppingCart(shoppingCartVo);
            }catch (Throwable e){
                // 出错了记录下来
                XdShoppingCartTempError error = new XdShoppingCartTempError();
                BeanUtils.copyProperties(temp,error);
                String trace = e.getStackTrace()[0]+"";
                String message = e.toString();
                if(null != message){
                    error.setErrorMessage("信息："+ (message.length() > 200 ? message.substring(0,200) : message ) + "路径："+ (trace.length() > 200 ? trace.substring(0,200) : trace ));
                }
                xdShoppingCartTempErrorMapper.insert(error);
            }finally {
                ThreadLocalUtils.remove();
            }

            shopIdList.add(temp.getShopId());
            commodityIdList.add(temp.getCommodityId());
        }
        Example deleteEx = new Example(XdShoppingCartTemp.class);
        //deleteEx.createCriteria().andEqualTo("shopId",temp.getShopId()).andEqualTo("commodityId",temp.getCommodityId());
        deleteEx.createCriteria().andIn("shopId",shopIdList).andIn("commodityId",commodityIdList);
        xdShoppingCartTempMapper.deleteByExample(deleteEx);
    }
    /**
     * 种植任务提交订单校验重复执行
     * @param endTime
     */
    public void validateRepeatJob(String endTime){
        String lockkey = endTime.replaceAll(":","") + "xd_auto_create_order";
        RLock lock = redissonClient.getLock(lockkey);
        if (!lock.isLocked()) {
            lock.lock(60L, TimeUnit.SECONDS);
            try{
                String key =  "xd_auto_create_order" + endTime.replaceAll(":","");
                RBucket<Object> bucket = redissonClient.getBucket(key);
                Object value = bucket.get();
                // value 不等于null 说明当天已经执行过任务,直接返回
                if(value != null){ return;}
                bucket.set(0);
                Date dayOfEnd = DateUtil.parseDate(DateUtil.getDateFormate(new Date(),"yyMMdd") + " 23:59:59", "yyMMdd HH:mm:ss");
                bucket.expireAt(dayOfEnd);

            }finally{
                lock.unlock();
            }
        }
    }
    /**
     *  鲜道自动提交订单
     */
    @Async
    public void autoCreateXDOrder(String endTime){
        // 校验重复任务
        validateRepeatJob(endTime);

        // 首选查询 前置仓所有的 购物车
        List<ShoppingCartEntry> shoppingCartList = shoppingCartMapper.xdShoppingCartDetail();
        commonService.setShopCartItemInfo(shoppingCartList);
        if(CollectionUtils.isEmpty(shoppingCartList)){return;}

        // 满足条件的鲜道购物车
        List<ShoppingCartEntry> addCartList = getXdShoppingCartData(endTime, shoppingCartList);

        // 购物车为空直接返回
        if(CollectionUtils.isEmpty(addCartList)){return;}

        // 组装提交订单数据
        List<CreateOrderVo> cartVoList = setXdCreateOrderData(addCartList);

        Map<Long, List<CreateOrderVo>> cartMap = cartVoList.stream().collect(Collectors.groupingBy(CreateOrderVo::getStoreId));

        long currentTimeMillis = System.currentTimeMillis();

        // 获取线程池
        ThreadPoolTaskExecutor threadPool = (ThreadPoolTaskExecutor) SpringBeanFinder.getBean(ThreadPoolBeanConstants.ORDER_THREADPOOL);

        for(List<CreateOrderVo> cartList : cartMap.values()){
            // 创建任务并提交到线程池中
            threadPool.execute(new Runnable() {
                @Override
                public void run() {
                    try {
                        for(CreateOrderVo vo : cartList){
                            ThreadLocalUtils.setXd(true);
                            List<CreateOrderVo> addcartList = new ArrayList<>();
                            addcartList.add(vo);
                            try {
                                orderService.createOrder(addcartList);
                                log.info("鲜道提交的购物车id: " + vo.getShoppingCartId());

                                Thread.sleep(10);
                            } catch (Throwable e) {
                                log.info("鲜道提交异常的购物车id: " + vo.getShoppingCartId());
                                log.error("自动提交订单异常",e);
                            } finally {
                                ThreadLocalUtils.remove();
                            }
                        }
                    }catch (Throwable e){
                        log.error("自动提交订单异常",e);
                    }

                }
            });

            try {
                Thread.sleep(10);
            }catch (Throwable e){
                log.error("自动提交订单异常",e);
            }
        }

    }

    /**
     * 获取满足条件的鲜道购物车
     * @param endTime
     * @param shoppingCartList
     * @return
     */
    @NotNull
    private List<ShoppingCartEntry> getXdShoppingCartData(String endTime, List<ShoppingCartEntry> shoppingCartList) {
        List<ShoppingCartEntry> addCartList = new ArrayList<>();
        for(ShoppingCartEntry cartEntry : shoppingCartList){
            List<String> commodityIdList = new ArrayList<>();
            commodityIdList.add(cartEntry.getItems().get(0).getCommodityId()+"");
            List<MdShopOrderSettingEntry> settingList = mdShopOrderSettingService.queryMdShopOrderSettingListByIds(cartEntry.getStoreId(), commodityIdList);

            QYAssert.isTrue(SpringUtil.isNotEmpty(settingList), "门店订货配置信息有误");
            MdShopOrderSettingEntry settingEntry = settingList.get(0);
            QYAssert.isTrue(StringUtils.isNotBlank(settingEntry.getDefaultWarehouseEndTime()) && StringUtils.isNotBlank(settingEntry.getDefaultSupplierEndTime()) , "门店订货配置信息有误,供应商或者仓库时间为空");

            Integer logisticsModel = settingEntry.getLogisticsModel().intValue();
            // 配送比较仓库时间
            if( logisticsModel == IogisticsModelEnums.DISPATCHING.getCode()){
                if(DateTimeUtil.compareTime(settingEntry.getDefaultWarehouseEndTime(),endTime)){
                    addCartList.add(cartEntry);
                }

            } else if(logisticsModel == IogisticsModelEnums.DIRECT_CONNECTION.getCode()){
                //直通比较供应商时间
                if(DateTimeUtil.compareTime(settingEntry.getDefaultSupplierEndTime(),endTime)){
                    addCartList.add(cartEntry);
                }
            }

        }
        return addCartList;
    }

    /**
     * 鲜道组装自动提交订单数据
     * @param addCartList
     */
    private List<CreateOrderVo> setXdCreateOrderData(List<ShoppingCartEntry> addCartList) {
        List<CreateOrderVo> cartVoList = new ArrayList<>();
        for(ShoppingCartEntry cart:addCartList){
            CreateOrderVo cartVo = new CreateOrderVo();
            cartVo.setEnterpriseId(78L);
            cartVo.setStoreId(cart.getStoreId());
            // 配送批次固定为1配
            cartVo.setDeliveryBatch(DeliveryBatchTypeEnum.ONE_BATCH.getCode()+"");
            /**
             * 送货日期固定，取值逻辑如下：
                若A商品的送货日期范围为Tx~Ty
             若Tx≥T+1，送货日期取Tx
             若Tx＜T+1，送货日期取T+1
             */
            cartVo.setOrderTime(OrderUtil.getOrderTime(cart.getDeleveryTimeRange()));
            cartVo.setShoppingCartId(Long.valueOf(cart.getId()));
            cartVo.setCreateId(-1L);
            cartVo.setCreateName("系统");
            cartVo.setInternal(true);

            List<CreateOrderVo.CreateOrderItemIDTO> cartItemVoList = new ArrayList<>();
            for(ShoppingCartItemEntry cartItem:cart.getItems()){
                CreateOrderVo.CreateOrderItemIDTO cartItemVo = new CreateOrderVo.CreateOrderItemIDTO();
                cartItemVo.setCommodityId(cartItem.getCommodityId());
                cartItemVo.setQuantity(cartItem.getQuantity());
                cartItemVo.setShoppingCartItemId(cartItem.getShoppingCartItemId());
                if(null != cartItemVo && cartItemVo.getQuantity().compareTo(BigDecimal.ZERO) > 0){
                    cartItemVoList.add(cartItemVo);
                }
            }
            cartVo.setItems(cartItemVoList);
            // 单个购物车所有商品数量为0，则直接过滤掉
            if(!CollectionUtils.isEmpty(cartItemVoList)){
                cartVoList.add(cartVo);
            }
        }
        return cartVoList;
    }

    /**
     * 查询所有的前置仓(开业前和营业中)
     * @return
     */
    public List<Shop> getAllXDShop(){
        Example shopEx = new Example(Shop.class);
        List<Integer> shopStatusList = new ArrayList<>();
//        shopStatusList.add(ShopShopStatusEnums.BEFORE_OPEN.getCode());
        shopStatusList.add(ShopShopStatusEnums.OPEN.getCode());
        shopEx.createCriteria().andEqualTo("shopType", ShopTypeEnums.XD.getCode()).andIn("shopStatus",shopStatusList);
        return this.shopMapper.selectByExample(shopEx);
    }


    /**
     * 计算一周前之内的进、销、损数量
     * @param shopId
     * @param odto
     */
    public void setWeekSalesQuantity(Long shopId,OrderReferenceDetailODTO odto){
        Calendar start   =   Calendar.getInstance();
        start.add(Calendar.DATE,   -7);
        Calendar end   =   Calendar.getInstance();
        end.add(Calendar.DATE,   -1);

        String yesterday = DateUtil.getDateFormate(end.getTime(),"yyyy-MM-dd");
        String yesterBegin = DateUtil.getDateFormate(start.getTime(),"yyyy-MM-dd");

        List<OrderReferenceDetailODTO.CommodityOrderSalesBreakageODTO> weekList = new ArrayList<>();
        Map<String,OrderReferenceDetailODTO.CommodityOrderSalesBreakageODTO> map = new HashMap<>();
        // 查询订货信息,销售信息,损耗信息
        List<XdCommodityTaxODTO> taxList = xdCommodityTaxClient.findXdCommodityTaxList(yesterBegin,yesterday,shopId,Long.valueOf(odto.getCommodityId()));
        if(!CollectionUtils.isEmpty(taxList)){
            for(XdCommodityTaxODTO taxODTO : taxList){
                OrderReferenceDetailODTO.CommodityOrderSalesBreakageODTO commodityBreakage = new OrderReferenceDetailODTO.CommodityOrderSalesBreakageODTO();
                BeanUtils.copyProperties(taxODTO,commodityBreakage);
                commodityBreakage.setSaleDate(taxODTO.getDateTime());
                commodityBreakage.setSalesQuantity(taxODTO.getTotalQuanty());
                commodityBreakage.setOrderQuantity(taxODTO.getOrderQuanty());
                map.put(taxODTO.getDateTime(),commodityBreakage);
            }
        }

        while (start.getTime().before(end.getTime()) || start.getTime().equals(end.getTime())) {
            String dateStr = DateUtil.getDateFormate(start.getTime(),"yyyy-MM-dd");
            if(map.get(dateStr) != null){
                OrderReferenceDetailODTO.CommodityOrderSalesBreakageODTO ce = map.get(dateStr);
                ce.setSaleDate(dateStr.substring(dateStr.length()-2,dateStr.length()));
                ce.setWeek(productService.dateToWeek(dateStr));
                weekList.add(ce);
            }else{
                OrderReferenceDetailODTO.CommodityOrderSalesBreakageODTO ent = new OrderReferenceDetailODTO.CommodityOrderSalesBreakageODTO();
                ent.setSaleDate(dateStr.substring(dateStr.length()-2,dateStr.length()));
                ent.setWeek(productService.dateToWeek(dateStr));
                weekList.add(ent);
            }
            start.add(Calendar.DATE,   1);
        }
        odto.setOrderSaleBreakageList(weekList);
    }

    /**
     * 订货参考
     * @param storeId
     * @param commodityId
     */
    public OrderReferenceDetailODTO orderReference(Long storeId, Long shopId, Long commodityId,Long shoppingCartId){
        OrderReferenceDetailODTO referenceODTO = new OrderReferenceDetailODTO();
        // 查询商品信息
        XDCommodityODTO commodityODTO = commodityMapper.getXDCommodityODTOById(commodityId);
        Shop shop = shopService.getShopByStoreId(storeId);
        if(ShopTypeEnums.XD.getCode().equals(shop.getShopType()) ||  StallUtils.isStallSubcontractor(shop.getManagementMode())){
            commodityODTO.setSalesBoxCapacity(commodityODTO.getXdSalesBoxCapacity());
        }

        if(null == commodityODTO){return new OrderReferenceDetailODTO();}
        Map<String, CommodityResultEntry> commodityCodeAndPromotionPriceMap = shoppingCartService.getCommodityCodeAndPromotionPriceMap(storeId+"", null);

        BeanUtils.copyProperties(commodityODTO,referenceODTO);
        //设置是否促销
        referenceODTO.setIsPromotionProduct(commodityCodeAndPromotionPriceMap.get(commodityODTO.getCommodityCode()) != null ? true : false);
        //设置是否新品
        if(null != commodityODTO.getNewProductFlag()){
            referenceODTO.setIsNewProduct(commodityODTO.getNewProductFlag().endsWith("01") ? true : false);
        }

        // 设置进、销、损数量
        setWeekSalesQuantity(shopId,referenceODTO);

        // 查询建议订货量
        BigDecimal adviseCommodityNum = shoppingCartMapper.getAdviseCommodityNum(storeId,commodityId);
        if(null != adviseCommodityNum){
            referenceODTO.setAdviseCommodityNum(adviseCommodityNum.divide(commodityODTO.getCommodityPackageSpec()));
        }

        if(shoppingCartId != null) {
            ShoppingCart shoppingCart = shoppingCartMapper.selectByPrimaryKey(shoppingCartId);
            Long stallId = shoppingCart.getStallId();
            // 大店查询大店的库存
            if(stallId != null && stallId > 0) {
                Map<Long, ShopCommodityInfoODTO> stockMap = productService.getBigShopCommodityStock(shop.getId(), stallId, Collections.singletonList(commodityId), false);
                ShopCommodityInfoODTO shopCommodityInfoODTO = stockMap.get(commodityId);
                if(shopCommodityInfoODTO != null) {
                    referenceODTO.setStockQuantity(shopCommodityInfoODTO.getStockQuantity());
                }
            }else {
                // 获取当前库存
                List<Long> commodityIdList = new ArrayList<>();
                commodityIdList.add(commodityId);
                List<ShopCommodityStockDTO> stockList = xdStockClient.queryShopCommodityStock(shopId,commodityIdList);
                if(!CollectionUtils.isEmpty(stockList)){
                    referenceODTO.setStockQuantity(new BigDecimal(stockList.get(0).getStockNumber()));
                }
            }
        }
        return referenceODTO;
    }


    /**
     * 种植任务提交购物车
     */
    public void generateJob(){
        String nowHour = DateUtil.getDateFormate(new Date(),"HH:mm");
        List<String> endTimeList = mdShopOrderSettingMapper.getEndTimeList(nowHour);
        if(org.apache.commons.collections.CollectionUtils.isNotEmpty(endTimeList)) {
            generateCreateXdOrderTasks.generateJob(endTimeList);
        }
    }



    private List<CreateOrderVo> setAdminCreateOrderData(List<ShoppingCartEntry> cartList, Long userId, String userName) {

        List<CreateOrderVo> cartVoList = new ArrayList<>();
        for(ShoppingCartEntry cart:cartList){
            CreateOrderVo cartVo = new CreateOrderVo();
            cartVo.setEnterpriseId(78L);
            cartVo.setStoreId(cart.getStoreId());
            cartVo.setDeliveryBatch(cart.getDeliveryBatch());
            cartVo.setOrderTime(cart.getOrderTime());
            cartVo.setShoppingCartId(Long.valueOf(cart.getId()));
            cartVo.setCreateId(userId);
            cartVo.setCreateName(userName);
            cartVo.setInternal(true);

            List<CreateOrderVo.CreateOrderItemIDTO> cartItemVoList = new ArrayList<>();
            for(ShoppingCartItemEntry cartItem : cart.getItems()){
                CreateOrderVo.CreateOrderItemIDTO cartItemVo = new CreateOrderVo.CreateOrderItemIDTO();
                cartItemVo.setCommodityId(cartItem.getCommodityId());
                cartItemVo.setQuantity(cartItem.getQuantity());
                cartItemVo.setShoppingCartItemId(cartItem.getShoppingCartItemId());
                if(null != cartItemVo && cartItemVo.getQuantity().compareTo(BigDecimal.ZERO) > 0){
                    cartItemVoList.add(cartItemVo);
                }
            }
            cartVo.setItems(cartItemVoList);
            // 单个购物车所有商品数量为0，则直接过滤掉
            if(!CollectionUtils.isEmpty(cartItemVoList)){
                cartVoList.add(cartVo);
            }
        }
        return cartVoList;
    }

    /**
     *  管理员版本  一键提交订单
     * @return
     */
    public CreateOrderFilterVo batchCreateOrderAdmin(Long userId, String userName, Boolean bigShop){
        List<ShoppingCartEntry> cartList = shoppingCartMapper.shoppingCartDetailAdmin(null,null,null,null,userId,bigShop);
        QYAssert.isTrue(org.apache.commons.collections.CollectionUtils.isNotEmpty(cartList), "购物车无数据，请刷新重试!");
        QYAssert.isTrue(cartList.size() < 1000 , "一次提交购物车不能超过1000个");

        // 钱大妈类型的不能一键提交，过滤掉
        List<Long> xsjmStoreIdList = shopService.getStoreIdListByShopType(ShopTypeEnums.XSJM.getCode());
        if(!CollectionUtils.isEmpty(xsjmStoreIdList)){
            cartList = cartList.stream().filter(p -> !xsjmStoreIdList.contains(p.getStoreId())).collect(Collectors.toList());
        }

        if(CollectionUtils.isEmpty(cartList)){
            log.warn("过滤掉钱大妈的购物车之后，购物车为空");
            return new CreateOrderFilterVo();
        }

        // 组装提交订单数据
        List<CreateOrderVo> cartVoList = setAdminCreateOrderData(cartList, userId, userName);

        // 获取线程池
        ThreadPoolTaskExecutor threadPool = (ThreadPoolTaskExecutor) SpringBeanFinder.getBean(ThreadPoolBeanConstants.ADMIN_SAVE_ORDER_THREADPOOL);

        List<FilterTipVo> filterTips =  Collections.synchronizedList(new ArrayList<>());
        List<BStockShortResponseVO> shortStockList = Collections.synchronizedList(new ArrayList<>());
        long currentTimeMillis = System.currentTimeMillis();

        // 按客户维度开线程池，如果一个客户下面多个购物车，则for循环购物车
        Map<Long, List<CreateOrderVo>> storeCartMap = cartVoList.stream().collect(Collectors.groupingBy(CreateOrderVo::getStoreId));

        // 初始化和任务数量一致
        CountDownLatch countDownLatch = new CountDownLatch(storeCartMap.size());
        // 创建任务并提交到线程池中
        for(Map.Entry<Long, List<CreateOrderVo>> entry : storeCartMap.entrySet()){
            threadPool.execute(new Runnable() {
                @Override
                public void run() {
                    try {
                        Long storeId = entry.getKey();
                        List<CreateOrderVo> cartVoList = entry.getValue();

                        Boolean isXd = shoppingCartService.getIsXd(storeId);

                        for (CreateOrderVo cartVo : cartVoList) {
                            FilterTipVo filterTipVo = new FilterTipVo();
                            List<CreateOrderVo> addCartList = new ArrayList<>();
                            addCartList.add(cartVo);
                            try {

                                ThreadLocalUtils.setXd(isXd);
                                ThreadLocalUtils.setAdmin(true);
                                ThreadLocalUtils.setOrderType(OrderTypeEnum.AGENT_ORDER.getCode());

                                Map<Long, BigDecimal> orderQuantityMap = new HashMap<>();
                                cartVo.getItems().forEach(dto ->{
                                    orderQuantityMap.put(dto.getCommodityId(), dto.getQuantity());
                                });
                                List<BStockShortResponseVO> responseVOList = bStockService.checkBStock(cartVo.getStoreId(), OrderTypeEnum.AGENT_ORDER.getCode(), DateUtil.parseDate(cartVo.getOrderTime(), "yyyy-MM-dd"), orderQuantityMap, "代理一键提交订单", null,userId);
                                if(org.apache.commons.collections.CollectionUtils.isNotEmpty(responseVOList)){
                                    responseVOList.forEach(dto ->{
                                        dto.setShoppingCartId(cartVo.getShoppingCartId());
                                    });
                                    shortStockList.addAll(responseVOList);
                                }else {
                                    log.info("一键提交订单 storeId = {} 购物车信息 = {}", storeId, JsonUtil.java2json(cartVo));
                                    orderService.createOrder(addCartList);
                                }
                            } catch (Throwable e) {
                                log.error("一键提交代理购物车异常, 购物车Id {}", cartVo.getShoppingCartId(), e);
                                filterTipVo.setCommodityName("购物车Id: " + cartVo.getShoppingCartId());
                                filterTipVo.setMsg(e.getMessage());
                                filterTips.add(filterTipVo);
                            }finally {
                                ThreadLocalUtils.remove();
                            }
                        }
                    }finally {
                        countDownLatch.countDown();
                    }
                 }
             });
        }

        // 阻塞主线程，当countDownLatch = 0时，线程都完成了
        try {
            countDownLatch.await();
        } catch (InterruptedException e) {
            log.warn("countDownLatch.await() error", e);
        }

        return new CreateOrderFilterVo(CollectionUtils.isEmpty(filterTips) ? false : true,filterTips,shortStockList);
    }

    /**
     * 代理购物车商品明细数
     * @return
     */
    public Long getAdminCommodityCount(Long userId, Boolean bigShop) {
        return shoppingCartMapper.countCartAdmin(userId, bigShop);
    }
}
