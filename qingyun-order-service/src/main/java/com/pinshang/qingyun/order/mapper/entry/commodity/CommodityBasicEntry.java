package com.pinshang.qingyun.order.mapper.entry.commodity;

import lombok.Data;

import java.math.BigDecimal;

/**
 * @Author: sk
 * @Date: 2021/5/12
 */
@Data
public class CommodityBasicEntry {

    private String commodityId;
    private String commodityCode;
    private String commodityName;
    private String commoditySpec; // 型号规格
    private String barCode;
    private String barCodes;
    // 包装规格
    private BigDecimal commodityPackageSpec;
    private  Integer isWeight;// 是否称重0-不称量,1-称重
    private Integer commodityType;// 类型
    private String  packedType;//包类型(01散装,02正包)
    // 01 散装  02 整包
    private String commodityPackageKind;

    private Integer frozen;//速冻

    private String storageCondition;// 存储条件
    private Integer logisticsModel;// 物流配送模式0=直送，1＝配送，2＝直通

    private Long commodityThirdId; // 小类id
    private Long commodityFirstId; // 大类id
    private Long commoditySecondId; // 中类id

    private Long commodityUnitId; // 单位id
    private Long commodityCycleId; //新品标识 id

    // 销售箱规
    private BigDecimal salesBoxCapacity;
    private BigDecimal xdSalesBoxCapacity;

    // 箱规
    private BigDecimal boxCapacity;

    // 税率
    private BigDecimal taxRate;
}
