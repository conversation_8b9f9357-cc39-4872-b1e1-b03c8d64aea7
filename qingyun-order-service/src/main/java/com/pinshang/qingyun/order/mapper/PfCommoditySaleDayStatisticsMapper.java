package com.pinshang.qingyun.order.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import com.pinshang.qingyun.base.mybatis.MyMapper;
import com.pinshang.qingyun.order.model.order.PfCommoditySaleDayStatistics;
import com.pinshang.qingyun.order.service.pf.saledaystatistics.CommoditySaleStatisticsDto;
import com.pinshang.qingyun.order.service.pf.saledaystatistics.CommoditySaleStatisticsQueryDto;

@Repository
public interface PfCommoditySaleDayStatisticsMapper extends MyMapper<PfCommoditySaleDayStatistics> {

	int batchUpdate(@Param("dayStatisticsList") List<PfCommoditySaleDayStatistics> updateList);

	List<CommoditySaleStatisticsDto> queryCommoditySaleStatistics(@Param("list") List<CommoditySaleStatisticsQueryDto> list);

//	int insertOnDuplicatedKeyUpdate(List<PfCommoditySaleDayStatistics> dayStatisticsList);


}
