<html>
<head>
    <title>手动执行索引</title>
    <script src="${request.contextPath}/static/js/jquery-1.11.1.min.js"></script>
    <link href="${request.contextPath}/static/css/style.css" rel="stylesheet" type="text/css"/>
    <style type="text/css">
        .pageDetail {
            display: none;
        }

        .show {
            display: table-row;
        }
    </style>
    <script>
        $(function () {
            $('#list').click(function () {
                $('.pageDetail').toggleClass('show');
            });
        });

    </script>
</head>
<body>
<div class="wrapper">
    <div class="middle">
    <h1 style="padding: 50px 0 20px;">手动执行索引列表</h1>
    <table class="gridtable" style="width:100%;">
        <thead>
            <tr>
                <th>名称</th>
                <th>操作</th>
            </tr>
        </thead>
        <tbody>
            <tr>
                <td>模拟下单：(已指定客户和商品，测订单发消息同步到统计库和ES的流程)<br/>
                    订单数量：<input type="text" id="orderNum" size="50"/><br>
                    时间间隔：<input type="text" id="timeInterval" size="50"/>(单位：秒)<br>
                    持续时间：<input type="text" id="continueTime" size="50"/>(单位：秒)<br>
                </td>
                <td><button onclick="createAssignOrder('/esOrderManual/createAssignOrder')">开始下单</button></td>
            </tr>
            </tbody>
        </table>
    </div>

</div>
<script type="text/javascript">
    function createAssignOrder(url){
        var orderNum = $("#orderNum").val();
        var timeInterval = $("#timeInterval").val();
        var continueTime = $("#continueTime").val();
        $.post(url,{"orderNum":orderNum,"timeInterval":timeInterval,"continueTime":continueTime},function(data){
            if(data.success){
                alert("操作成功！ "+data.msg)
            }else {
                alert(data.msg)
            }
        })
    }
</script>
</body>
</html>