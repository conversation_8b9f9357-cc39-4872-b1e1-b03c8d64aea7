<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.pinshang.qingyun.order.mapper.PreOrderMapper">

<update id="updatePreOrderStatus" parameterType="java.lang.Long">
	update `t_md_preorder` set `order_status` = 0 , `receive_status` = 4, update_id = #{userId}  where `id` =#{orderId}
</update>

<select id="finsShopIdByStore" parameterType="java.lang.Long" resultType="java.lang.Long">
	select * from t_md_shop s where s.`store_id` =#{storeId}
</select>

<select id="getMdPreOrderList" resultType="com.pinshang.qingyun.order.mapper.entry.purchase.DirectSendingPurchaseItemEntry">
	SELECT
		p.purchase_order_id,
		m.`shop_name`,
		p.order_time,
		(case p.receive_status when 0 then '否' when 4 then '否' else '是' end) as receiveStatus,
		p.`order_code` sub_order_code
	FROM t_md_preorder p
	inner join t_md_shop m on m.`store_id` =p.`store_id`
	WHERE p.`logistics_model` =0
         and p.`supplier_id` =#{supplierId}
         AND p.order_status =2
         and to_days(p.`order_time`) = to_days(#{orderTime})
</select>


	<select id="countNotReceiveConsignmentOrder" resultType="java.lang.Integer">
		SELECT
		    count(1)
		from t_md_preorder mp
		LEFT JOIN t_md_preorder_item mpi on mpi.preorder_id = mp.id
		where mp.order_time BETWEEN #{beginDate} and #{endDate}
		  and mp.receive_status = 0
		  and mp.store_id = #{storeId}
		  and mpi.commodity_id in
		<foreach collection="commodityIdList" item="commodityId" open="(" close=")" separator=",">
			#{commodityId}
		</foreach>

		<if test="supplierIdList != null and supplierIdList.size > 0">
			and mp.consignment_id in
			<foreach collection="supplierIdList" index="index" item="supplierId" open="(" separator="," close=")">
				#{supplierId}
			</foreach>
		</if>

	</select>


	<select id="consignmentOrderReport" resultType="com.pinshang.qingyun.order.dto.consignment.ConsignmentOrderPageODTO">
		SELECT
			DATE_FORMAT(mp.order_time,'%Y-%m-%d') orderTime,
			mp.order_code,
			mp.store_id,
			md.id shopId,
			mpi.commodity_id,
			mpi.require_quantity,
			mpi.receive_quantity realReceiveQuantity,
			mpi.check_quantity auditQuantity
		from t_md_preorder mp
		 LEFT JOIN t_md_preorder_item mpi on mpi.preorder_id = mp.id
		 LEFT JOIN t_md_shop md on md.store_id = mp.store_id
		where mp.consignment_id > 0 and mp.receive_status != 4
		<if test="vo.shopId != null" >
			AND md.id = #{vo.shopId}
		</if>

		<if test="vo.shopIdList != null and vo.shopIdList.size > 0">
			AND md.id IN
			<foreach collection="vo.shopIdList" index="index" item="id" open="(" separator="," close=")">
				#{id}
			</foreach>
		</if>

		<if test="vo.beginDate != null and vo.beginDate != '' and vo.endDate != null and vo.endDate != '' ">
			and mp.order_time BETWEEN #{vo.beginDate} and #{vo.endDate}
		</if>

		<if test="vo.orderCode != null and vo.orderCode != '' ">
			and mp.order_code = #{vo.orderCode}
		</if>

		<if test="vo.consignmentId != null" >
			AND mp.consignment_id = #{vo.consignmentId}
		</if>


		<if test="vo.commodityIdList != null and vo.commodityIdList.size() > 0">
			AND mpi.commodity_id in <foreach collection="vo.commodityIdList" item="id" open="(" separator="," close=")"> #{id} </foreach>
		</if>

		 order by orderTime desc,md.shop_code,mp.order_code
	</select>
</mapper>