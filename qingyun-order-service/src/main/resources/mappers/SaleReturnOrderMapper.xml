<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.pinshang.qingyun.order.mapper.SaleReturnOrderMapper" >

    <select id="list" parameterType="java.util.Map"
            resultType="com.pinshang.qingyun.order.vo.order.SaleReturnOrderRespVo">
        SELECT
            s.store_name AS storeName,
            sro.create_time AS returnOrderDate,
            sro.order_code AS returnOrderCode,
            sro.`status` AS status,
            u.employee_name AS updateName,
            CASE WHEN sro.`status` = 2 THEN sro.update_time END updateTime,
            sro.warehouse_id,
            u1.employee_name AS createName,
            count(sro.order_code) AS categoryNum
        FROM
            t_sale_return_order sro
        INNER JOIN t_store s ON sro.store_id = s.id
        LEFT JOIN t_sale_return_order_item sroi ON sro.id = sroi.sale_return_order_id
        LEFT JOIN t_employee_user u ON sro.update_id = u.user_id
        LEFT JOIN t_employee_user u1 ON sro.create_id = u1.user_id
        <where>
            <!-- 大仓只查询1＝配送，2＝直通,不查询直送订单 -->
            sro.logistics_model in (1,2)
            <if test="startDate != null and endDate != null">
                AND sro.create_time <![CDATA[ >= ]]> CONCAT(#{startDate},' 00:00:00')
                AND sro.create_time <![CDATA[ <= ]]> CONCAT(#{endDate},' 23:59:59')
            </if>
            <if test="returnOrderCode != null">
                AND sro.order_code = #{returnOrderCode}
            </if>
            <if test="status != null">
                AND sro.status = #{status}
            </if>
            <if test="warehouseId != null">
                AND sro.warehouse_id = #{warehouseId}
            </if>
            <if test="storeFuzzy != null">
                AND (
                s.store_code LIKE CONCAT('%',#{storeFuzzy},'%')
                OR s.store_name LIKE CONCAT('%',#{storeFuzzy},'%')
                OR s.store_aid LIKE CONCAT('%',#{storeFuzzy},'%')
                )
            </if>
            <if test="ifReturnShort == true">
                AND sroi.return_reason = #{returnReason}
            </if>
            <if test="ifReturnShort == false ">
                AND sroi.return_reason != #{returnReason}
            </if>
        </where>
        GROUP BY returnOrderCode
        ORDER BY sro.create_time DESC
    </select>

    <select id="querySaleReturnOrderList" resultType="com.pinshang.qingyun.order.dto.SaleReturnOrderODTO">
        SELECT
            sro.create_time AS returnOrderDate,
            sro.order_code AS returnOrderCode,
            sro.id AS returnOrderId,
            sro.`status` AS status
        FROM
            t_sale_return_order sro
        <where>
            <!-- 大仓只查询1＝配送，2＝直通,不查询直送订单 -->
            sro.logistics_model in (1,2)
            AND sro.create_time <![CDATA[ >= ]]> CONCAT(#{startDate},' 00:00:00')
            AND sro.create_time <![CDATA[ <= ]]> CONCAT(#{endDate},' 23:59:59')
            AND sro.status IN (1,3)
            AND sro.warehouse_id = #{warehouseId}
            AND (sro.update_id = #{receiverId} OR sro.update_id IS NULL)
            AND sro.store_id = #{storeId}
        </where>
        ORDER BY sro.status DESC, sro.create_time DESC
    </select>

    <select id="queryStoreInfo" resultType="com.pinshang.qingyun.order.dto.StoreInfoODTO">
        SELECT
            sro.store_id,
            s.store_name AS storeName
        FROM
            t_sale_return_order sro INNER JOIN t_store s ON sro.store_id = s.id
        WHERE
            sro.warehouse_id = #{warehouseId}
            AND sro.logistics_model IN (1,2)
            AND sro.status IN (1,3)
            AND (sro.update_id = #{receiverId} OR sro.update_id IS NULL)
            AND sro.create_time <![CDATA[ >= ]]> CONCAT(#{beginTime},' 00:00:00')
            AND sro.create_time <![CDATA[ <= ]]> CONCAT(#{endTime},' 23:59:59')
            <if test="storeName != null and storeName != ''">
                AND s.store_name LIKE concat('%',#{storeName},'%')
            </if>
        GROUP BY sro.store_id
    </select>

    <select id="detail" resultType="com.pinshang.qingyun.order.mapper.entry.order.SaleReturnOrderDetailEntry">
        SELECT
            ts.store_name,
            sro.order_code AS returnOrderCode,
            sro.create_time AS returnOrderDate,
            sro.remark,
            sro.warehouse_id AS warehouseId,
            sro.update_id,
            u.employee_name AS updateName,
            sro.`status`
        FROM
            t_sale_return_order sro
            INNER JOIN t_store ts ON sro.store_id = ts.id
            LEFT JOIN t_employee_user u ON sro.update_id = u.user_id
        WHERE sro.order_code = #{code}
          AND sro.enterprise_id = #{enterpriseId}
    </select>

    <select id="itemList" resultType="com.pinshang.qingyun.order.mapper.entry.order.SaleReturnOrderItemEntry">
        SELECT
            tc.id AS commodityId,
            tc.commodity_name,
            tc.commodity_code,
            tc.commodity_spec,
            tc.batch_status,
            td.option_name AS unitName,
            sroi.return_quantity,
            sroi.return_reason,
            sroi.remark,
            tc.bar_code
        FROM
            t_sale_return_order_item sroi
            INNER JOIN t_sale_return_order sro ON sroi.sale_return_order_id = sro.id
            INNER JOIN t_commodity tc ON sroi.commodity_id = tc.id
            INNER JOIN t_dictionary td ON tc.commodity_unit_id = td.id
        WHERE
            sro.order_code = #{code}
        ORDER BY tc.id
    </select>


    <!-- 分页查询退货差异报表 -->
    <select id="listReturnReport" parameterType="com.pinshang.qingyun.order.vo.order.SaleReturnReportVo"
            resultType="com.pinshang.qingyun.order.dto.SaleReturnReportODTO">
        SELECT
        roi.id,
        ro.enterprise_id,
        ro.stall_id,
        ts.store_code,
        ts.store_name,
        ro.order_code,
        ro.create_time,
        ro.update_time,
        roi.commodity_id,
        tc.commodity_code,
        tc.bar_code,
        tc.commodity_name,
        tc.commodity_spec,
        roi.return_quantity,
        roi.real_return_quantity,
        roi.price,
        roi.total_price,
--         bar_codes,
        ms.shop_name shopName,
        roi.return_reason,
        ddd.option_name returnReasonName,
        roi.rp_type
        FROM t_sale_return_order_item roi
        INNER JOIN t_sale_return_order ro ON roi.sale_return_order_id = ro.id
        INNER JOIN t_store ts ON ro.store_id = ts.id
        INNER JOIN t_commodity tc ON roi.commodity_id = tc.id
--         left join ( SELECT GROUP_CONCAT(bar_code) as bar_codes, commodity_id FROM t_commodity_bar_code group by commodity_id) AS cbc on cbc.commodity_id = tc.id
        LEFT JOIN t_md_shop ms on ms.store_id=ro.store_id
        LEFT JOIN (
            SELECT
              d.option_code,
              d.option_name
            FROM
              t_dictionary d
            WHERE
              d.dictionary_id = (
                SELECT
                dd.id
                FROM
                t_dictionary dd
                WHERE
                dd.option_name = '门店退货原因'
            )
        ) ddd on ddd.option_code=roi.return_reason
        <include refid="queryReportCondition" />
        ORDER BY ro.create_time DESC
    </select>

    <!-- 退货差异报表实退金额合计 -->
    <select id="queryReturnReportSumEntry" parameterType="com.pinshang.qingyun.order.vo.order.SaleReturnReportVo"
            resultType="com.pinshang.qingyun.order.mapper.entry.order.SaleReturnReportSumEntry">
        SELECT
            sum(roi.real_return_quantity * roi.price) AS totalReturnPrice
        FROM t_sale_return_order_item roi
        INNER JOIN t_sale_return_order ro ON roi.sale_return_order_id = ro.id
        INNER JOIN t_commodity tc ON roi.commodity_id = tc.id
        LEFT JOIN t_md_shop ms on ms.store_id=ro.store_id
        <include refid="queryReportCondition" />
    </select>

    <sql id="queryReportCondition">
        WHERE ro.`status` = 2
        <choose>
            <when test="vo.shopId != null and vo.shopId !='' " > AND ms.id = #{vo.shopId} </when>
            <otherwise>
                AND ms.id IN
                <foreach collection="vo.shopIdList" index="index" item="id" open="(" separator="," close=")">
                    #{id}
                </foreach>
            </otherwise>
        </choose>
        <if test=" vo.orderCode != null and vo.orderCode != ''">
            AND ro.order_code = #{vo.orderCode}
        </if>
        <if test="vo.startTime !=null and vo.startTime !=''">
            AND ro.create_time <![CDATA[>=]]> CONCAT(#{vo.startTime},' 00:00:00')
        </if>
        <if test="vo.endTime !=null and vo.endTime !=''">
            AND ro.create_time <![CDATA[<=]]> CONCAT(#{vo.endTime},' 23:59:59')
        </if>
        <if test="vo.updateStartTime !=null and vo.updateStartTime !=''">
            AND ro.update_time <![CDATA[>=]]> CONCAT(#{vo.updateStartTime},' 00:00:00')
        </if>
        <if test="vo.updateEndTime !=null and vo.updateEndTime !=''">
            AND ro.update_time <![CDATA[<=]]> CONCAT(#{vo.updateEndTime},' 23:59:59')
        </if>
        <if test="vo.commodityId !=null">
            AND tc.id = #{vo.commodityId}
        </if>
        <if test="vo.commodityCode !=null and vo.commodityCode != ''">
            AND (
            tc.commodity_code like  concat('%',trim(#{vo.commodityCode}),'%')
            or tc.commodity_name like  concat('%',trim(#{vo.commodityCode}),'%')
            or tc.commodity_aid like  concat('%',trim(#{vo.commodityCode}),'%')
            )
        </if>
        <if test="vo.barCode !=null and vo.barCode != ''">
            AND tc.id = (SELECT commodity_id FROM t_commodity_bar_code WHERE bar_code = #{vo.barCode})
        </if>
        <if test="vo.consignmentId !=null">
            AND ro.consignment_id = #{vo.consignmentId}
        </if>
        <if test="vo.stallId !=null">
            AND ro.stall_id = #{vo.stallId}
        </if>
    </sql>


    <select id="querySaleReturnOrderListById" resultType="com.pinshang.qingyun.order.dto.SaleReturnOrderODTO">
        SELECT
            md.id shopId,
            o.id returnOrderId,
            o.order_code returnOrderCode,
            oi.commodity_id,
            oi.return_quantity,
            oi.price,
            IFNULL(t.commodity_package_spec,1) commodityPackageSpec,
            o.store_id storeId,
            oi.return_reason,
            o.stall_id
        FROM
         t_sale_return_order o
            inner JOIN t_sale_return_order_item oi ON oi.sale_return_order_id = o.id
            inner join t_md_shop md on md.store_id = o.store_id
            left join t_commodity t on t.id = oi.commodity_id
        WHERE o.id = #{saleReturnOrderId}
    </select>
</mapper>