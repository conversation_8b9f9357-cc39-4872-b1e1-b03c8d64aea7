<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.pinshang.qingyun.order.mapper.QuickGoodsMapper">

	<select id="findQuickGoodsNewList" parameterType="com.pinshang.qingyun.order.vo.order.QuickGoodsVo" resultType="com.pinshang.qingyun.order.mapper.entry.order.QuickGoodsEntry">
		select
			tms.shop_code,
			tms.shop_name,
			g.commodity_id as commodityId,
			sum(g.quantity) as quantity,
			g.pass,
			g.remark,
			ts.store_code,
		    g.store_id storeId,
			tms.id shopId,
			<if test="ifAdmin == true ">
				g.order_time,g.delivery_batch,
			</if>
			tms.shop_type,tms.management_mode,
			g.stall_id
		from
		<choose>
			<when test="ifAdmin == false">t_md_quick_goods g</when>
			<when test="ifAdmin == true">t_md_quick_goods_admin g</when>
		</choose>
		LEFT JOIN t_md_shop tms ON g.store_id = tms.store_id
		left join t_store ts on ts.id = g.store_id
		where 1=1
		<if test="storeId != null and storeId !='' ">
			and g.store_id = #{storeId}
		</if>
		<if test="!isInternal and (consignmentId == null or consignmentId == 0) and ifAdmin == false">
			and g.consignment_id = -1
		</if>
		<if test="!isInternal and consignmentId != null and consignmentId > 0 and ifAdmin == false">
			and g.consignment_id = #{consignmentId}
		</if>
		<if test="bigShop">
			and g.stall_id > 0
		</if>
		<if test="!bigShop">
			and (g.stall_id = -1 or g.stall_id is null )
		</if>
		<if test="stallIdList != null and stallIdList.size > 0">
			AND g.stall_id IN
			<foreach collection="stallIdList" index="index" item="stallid" open="(" separator="," close=")">
				#{stallid}
			</foreach>
		</if>
		<if test="ifAdmin == true and bigShop == true">
		    and g.create_id = #{createId}
			group by tms.shop_code,g.stall_id,g.commodity_id,g.order_time,g.delivery_batch
		</if>
		<if test="ifAdmin == true and bigShop == false">
			and g.create_id = #{createId}
			group by tms.shop_code,g.commodity_id,g.order_time,g.delivery_batch
		</if>
		<if test="ifAdmin == false ">
			group by tms.shop_code,g.commodity_id
		</if>
		order by g.pass
	</select>

	<select id="findQuickGoodsList" parameterType="com.pinshang.qingyun.order.vo.order.QuickGoodsVo" resultType="com.pinshang.qingyun.order.mapper.entry.order.QuickGoodsEntry">
		select
			tms.shop_code,
			tms.shop_name,
			c.id as commodityId,
			c.commodity_code,
			c.commodity_name,
			c.commodity_spec,
		    sum(g.quantity) as quantity,
		    g.pass,
	    	g.remark,
			ppml.`commodity_price` price,
			c.logistics_model,
			di.`option_name` commodityUnit,
			case
				when tms.shop_type = 5  or tms.management_mode = 3 then c.`xd_sales_box_capacity`
				when tms.shop_type != 5  then c.`sales_box_capacity`
				end as salesBoxCapacity,
			IFNULL(c.commodity_package_spec,1) commodityPackageSpec,
			<if test="ifAdmin == true ">
				g.order_time,g.delivery_batch,
			</if>
		    ts.store_code,g.store_id storeId,
		    tms.id shopId
		 from
		<choose>
			<when test="ifAdmin == false">t_md_quick_goods g</when>
			<when test="ifAdmin == true">t_md_quick_goods_admin g</when>
		</choose>
		  LEFT JOIN t_md_shop tms ON g.store_id = tms.store_id
		  inner join t_commodity c on c.id =g.commodity_id
		  left join t_store_settlement ss  on ss.store_id =g.store_id
		  left join t_product_price_model ppm  on ppm.id = ss.product_price_model_id
		  left join t_product_price_model_list ppml  on ppml.product_price_model_id = ppm.id and ppml.commodity_id =g.commodity_id
		  left  join `t_dictionary` di on di.`id` = c.`commodity_unit_id`
		  left join t_store ts on ts.id = g.store_id
		where 1=1
		<if test="storeId!=null and storeId !='' ">
			and g.store_id =#{storeId}
		</if>
		<if test="ifAdmin == true ">
			group by tms.shop_code,c.id,g.order_time,g.delivery_batch
		</if>
		<if test="ifAdmin == false ">
			group by tms.shop_code,c.id
		</if>
		order by g.pass
	</select>
	
	<select id="queryCommodityInfoByStoreId" resultType="com.pinshang.qingyun.order.mapper.entry.order.CommodityInfoEntry">
		select 
     		 c.id,
     		 c.commodity_code,
     		 c.`commodity_state` commodityStatus,
			ppml.commodity_id as commodity_id,
			ppml.`commodity_price` price,
			c.logistics_model,
			txscps.commodity_purchase_status
		 from t_commodity c 
		  left join t_store_settlement ss  on ss.store_id =#{storeId}
		  left join t_product_price_model ppm  on ppm.id = ss.product_price_model_id
		  left join t_product_price_model_list ppml  on ppml.product_price_model_id = ppm.id and ppml.commodity_id =c.id
		  left join t_xs_shop_commodity_purchase_status txscps on txscps.shop_id = (SELECT id FROM t_md_shop WHERE store_id = #{storeId}) and txscps.commodity_id = c.id
		where 1=1
		and c.commodity_code in
		<foreach collection="commodityCodes" index="index" item="commodityCode" open="(" separator="," close=")">
			#{commodityCode}
		</foreach>
	</select>

	<select id="queryCommodityPurchaseStatus" resultType="com.pinshang.qingyun.order.mapper.entry.order.CommodityInfoEntry">
		select
		    txscps.commodity_id,
			txscps.commodity_purchase_status
		 from t_xs_shop_commodity_purchase_status txscps
		where txscps.shop_id = #{shopId}
		and txscps.commodity_id in
		<foreach collection="commodityIdList" index="index" item="commodityId" open="(" separator="," close=")">
			#{commodityId}
		</foreach>
	</select>

	<select id="findFrozenProductListByProductCode" parameterType="java.lang.String" resultType="com.pinshang.qingyun.order.mapper.entry.order.FrozenProductEntry">
				select 
					c.`id` productId ,
					c.`commodity_name` productName,
					c.`commodity_is_quick_freeze` frozen,
					c.`sales_box_capacity`,
					c.`xd_sales_box_capacity`
				from  
					t_commodity c where c.`commodity_code` =#{productCode}
	</select>

	<insert id="insertListAdmin">
		<foreach collection="quickList" index="index" item="item" open="" close="" separator=";">
			insert into t_md_quick_goods_admin(store_id,commodity_id,quantity,
			logistics_model,warehouse_id,supplier_id,remark,pass,order_time,delivery_batch,create_id,stall_id)
			values (#{item.storeId},#{item.commodityId},#{item.quantity},
			#{item.logisticsModel},#{item.warehouseId},#{item.supplierId},
			#{item.remark},#{item.pass},#{item.orderTime},#{item.deliveryBatch},#{item.createId},#{item.stallId})
		</foreach>
	</insert>

	<delete id="deleteQuickGoodsAdmin">
		delete  from t_md_quick_goods_admin where create_id = #{userId}
		<if test="bigShop">
			and stall_id > 0
		</if>
		<if test="!bigShop">
			and (stall_id = -1 or stall_id is null )
		</if>
	</delete>
</mapper>