<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.pinshang.qingyun.order.mapper.auto.AutoShopCommodityLogMapper">

    <select id="batchSave" parameterType = "com.pinshang.qingyun.order.model.auto.AutoShopCommodityLog">
        INSERT INTO t_log_auto_shop_commodity(shop_id,shop_code,shop_name,commodity_id,commodity_code,commodity_name,type,create_name,create_time,create_id)
        VALUES
        <foreach collection="list" separator="," item="log">
            ( #{log.shopId}, #{log.shopCode}, #{log.shopName},#{log.commodityId},#{log.commodityCode},#{log.commodityName},#{log.type},#{log.createName},#{log.createTime},#{log.createId} )
        </foreach>
    </select>

    <select id="logList" resultType="com.pinshang.qingyun.order.model.auto.AutoShopCommodityLog"
    parameterType = "com.pinshang.qingyun.order.vo.auto.AutoShopCommodityLogRequestVO">
        SELECT
              shop_id,
              shop_code,
              shop_name,
              commodity_id,
              commodity_code,
              commodity_name,
              type,
              create_name,
              create_time,
              create_id
        FROM t_log_auto_shop_commodity sc
        <where>
            <if test = "vo.shopId != null">
               AND sc.shop_id = #{vo.shopId}
            </if>
            <if test = "vo.createId != null">
                AND sc.create_id = #{vo.createId}
            </if>
            <if test = "vo.commodityId != null">
                AND sc.commodity_id = #{vo.commodityId}
            </if>
            <if test = "vo.type != null">
                AND sc.type = #{vo.type}
            </if>
            <if test = "vo.startTime != null and vo.startTime != '' and vo.endTime != null and vo.endTime != ''">
                AND sc.create_time BETWEEN #{vo.startTime} AND #{vo.endTime}
            </if>
        </where>
        ORDER BY sc.create_time DESC
    </select>



</mapper>