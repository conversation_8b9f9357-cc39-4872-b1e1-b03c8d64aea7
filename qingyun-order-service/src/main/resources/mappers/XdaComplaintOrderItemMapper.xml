<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.pinshang.qingyun.order.mapper.xda.XdaComplaintOrderItemMapper">

    <update id="updateXdaComplaintOrderItem" >
        update
            t_complaint_commodity_list
		set enabled = #{enabled}
		where  complaint_id = #{complaintId}
        <if test="commodityIdList != null">
            and commodity_id in
            <foreach item="item" index="index" collection="commodityIdList" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </update>

    <delete id="delXdaComplaintOrderItem" >
        delete from
            t_complaint_commodity_list
		where  complaint_id = #{complaintId}
            and commodity_id = #{commodityId}
            and commodity_price = #{commodityPrice}
    </delete>

    <update id="updateXdaComplaintOrderItems" parameterType="java.util.List">
        <foreach collection="list" item="item" index="index" separator=";" open="" close="">
            update t_complaint_commodity_list
            set complaint_money = #{item.realReturnQuantity},
                complaint_number = #{item.realReturnQuantity},
                complaint_type = #{item.complaintType}
            where 1=1
            and complaint_id = #{item.complaint_id}
            and commodity_id = #{item.commodityId}

        </foreach>
    </update>

    <select id="selectXdaComplaintOrderItem" resultType="com.pinshang.qingyun.order.model.xda.XdaComplaintCommodityItem">
        SELECT
            item.commodity_id,
            item.complaint_id,
            item.complaint_money,
            item.complaint_number,
            item.commodity_price,
            item.complaint_type
        from t_complaint_commodity_list item
        WHERE   item.complaint_id = #{complaintId}
        <if test=" enabled != null ">
            AND item.enabled = #{enabled}
        </if>
        <if test="commodityIdList != null">
            AND commodity_id in
            <foreach item="item" index="index" collection="commodityIdList" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </select>

</mapper>