<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.pinshang.qingyun.order.mapper.OrderBillMapper">

    <insert id="recordTheTransaction" parameterType="com.pinshang.qingyun.order.model.order.OrderBill" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO t_order_bill
            (store_id,store_balance,order_id,ar_amount,pa_amount,order_time,bill_remark,create_time,crate_id)
        VALUES
            (#{storeId},#{storeBalance},#{orderId},#{arAmount},#{paAmount},#{orderTime},#{billRemark},#{createTime},#{crateId})
    </insert>
    <select id="queryOrderBillList"
            resultType="com.pinshang.qingyun.order.dto.job.OrderBillStoreBalanceDTO">
        SELECT t1.store_id,t1.store_balance
        FROM t_order_bill t1
        WHERE <![CDATA[ create_time < concat(#{dateStr},' 00:00:00') ]]>
        and store_id = #{storeId}
        order by create_time desc,id desc
        limit 1
    </select>
    <select id="queryChargingOrderBill"
            resultType="com.pinshang.qingyun.order.dto.job.OrderBillStoreChargingDTO">
        select store_id,pa_amount as chargingMoney from t_order_bill
        where create_time between concat(#{dateStr},' 00:00:00') and  concat(#{dateStr},' 23:59:59')
        and bill_remark like concat('%',#{billRemarkLikeStr},'%')
        and store_id IN
        <foreach collection="storeIdList" open="(" separator="," close=")" item="item">
            #{item}
        </foreach>
    </select>

</mapper>