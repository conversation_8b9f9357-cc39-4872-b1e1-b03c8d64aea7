<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.pinshang.qingyun.order.mapper.StoreRechargeMapper">

    <insert id="insertStoreRecharge" parameterType="com.pinshang.qingyun.order.model.order.StoreRecharge">
        INSERT INTO t_store_recharge
            (store_id,money,store_balance,payment_method,receipt_date,receipt_type,operation_batch,remark,create_id,create_time,update_time)
        VALUES
            (#{storeId},#{money},#{storeBalance},#{paymentMethod},#{receiptDate},#{receiptType},#{operationBatch},#{remark},#{createId},#{createTime},#{updateTime})
    </insert>

</mapper>