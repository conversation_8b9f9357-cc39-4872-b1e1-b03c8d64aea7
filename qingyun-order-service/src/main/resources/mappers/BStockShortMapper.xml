<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.pinshang.qingyun.order.mapper.BStockShortMapper" >
    <select id="page" resultType="com.pinshang.qingyun.order.dto.BStockShortPageODTO">
        select * from t_order_stock_short_log bss
        where  bss.create_time between #{beginTime} and #{endTime}
        <if test="null != storeId">
            and bss.store_id = #{storeId}
        </if>
        <if test="null != commodityId">
            and bss.commodity_id = #{commodityId}
        </if>
        <if test="null != stockType">
            and bss.stock_type = #{stockType}
        </if>
        order by id desc
    </select>
</mapper>