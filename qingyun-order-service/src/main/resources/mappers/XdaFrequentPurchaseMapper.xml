<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.pinshang.qingyun.order.mapper.XdaFrequentPurchaseMapper">

    <select id="queryFrequentPurchase" resultType="com.pinshang.qingyun.order.model.order.XdaFrequentPurchase">
        select id,store_id,commodity_id,quantity from t_xda_frequent_purchase
        where store_id =#{storeId} and commodity_id in
        <foreach collection="idList" index="index" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

    <update id="batchUpdate">
        <foreach collection="purchaseList" item="item" index="index" separator=";" open="" close="">
            UPDATE t_xda_frequent_purchase
            SET quantity = #{item.quantity}
            where store_id =#{item.storeId} and commodity_id = #{item.commodityId}
        </foreach>
    </update>
</mapper>