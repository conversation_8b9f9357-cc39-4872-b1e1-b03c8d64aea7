<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.pinshang.qingyun.order.mapper.XdReceiveDocMapper">

    <select id="getMaxDocNo" resultType="java.lang.String">
        SELECT max(t.doc_code) FROM t_xd_receive_doc t where t.delivery_time = #{orderTime}
    </select>

    <update id="batchUpdateReceiveOrderPass" >
        update t_md_receive_order ro JOIN (
                    SELECT
                            DISTINCT mro.id id
                    from t_order o
                 INNER  JOIN t_sub_order so on so.order_id = o.id
                 INNER JOIN  t_md_receive_order  mro on  mro.sub_order_id = so.id
                 INNER  JOIN t_md_shop md on md.store_id = o.store_id
                 WHERE o.order_time = #{todayStr}
                 and  md.id in
                <foreach collection="shopIdList" index="index" item="shopId" open="(" separator="," close=")">
                    #{shopId}
                </foreach>
        )t2 on t2.id = ro.id
        set ro.status = 3,ro.receive_time = NOW(),ro.receive_id = -1,ro.remark = '系统自动完成收货'
        where ro.status =0

    </update>


    <select id="getXdReceiveDocCommodityInfo" resultType="com.pinshang.qingyun.order.dto.XdReceiveDocCommodityODTO">
          SELECT
                soi.commodity_id,
              -- c.id commodityId,
              -- c.commodity_code,
              -- c.commodity_name,
              -- c.commodity_spec,
              -- c.commodity_package_kind,
              -- c.is_weight,
              -- d.option_name as unit,
              -- IFNULL(c.commodity_package_spec,1) commodityPackageSpec,
              sum(soi.quantity) quantity,
              IFNULL(sum(soi.real_delivery_quantity),0) realDeliveryQuantity,
              IFNULL(sum(soi.real_receive_quantity),0) realReceiveQuantity,
              soi.price,
              -- tt.barCodes,
              -- md.id shopId,
              -- md.shop_code,
              -- md.shop_name,
              o.store_id
                from t_order o
             INNER  JOIN t_sub_order so on so.order_id = o.id
             INNER JOIN t_sub_order_item soi on soi.sub_order_id = so.id
             INNER JOIN  t_md_receive_order  mro on  mro.sub_order_id = so.id and mro.status = 0
             -- INNER JOIN t_md_shop md ON md.store_id = o.store_id
             -- INNER JOIN t_commodity c ON c.id = soi.commodity_id
             -- left JOIN t_dictionary d ON c.commodity_unit_id = d.id
             /*LEFT JOIN (
                       SELECT
                            t.commodity_id commodityId,
                            GROUP_CONCAT(t.bar_code ORDER BY t.default_state desc) barCodes
                        from t_commodity_bar_code t
                        where 1=1
                        GROUP BY t.commodity_id
                ) tt on tt.commodityId = c.id
        */
        WHERE 1=1
         AND o.order_time = #{vo.deliveryTime}
         and o.store_id = (select store_id from t_md_shop where id = #{vo.shopId})
        <if test=" vo.commodityId != null ">
            AND soi.commodity_id = #{vo.commodityId}
        </if>
        <!--<if test="null != vo.commodityKey and vo.commodityKey != '' ">
            AND ( c.commodity_code LIKE CONCAT("%", #{vo.commodityKey} ,"%") or c.commodity_name LIKE CONCAT("%", #{vo.commodityKey} ,"%")
            OR tt.barCodes LIKE CONCAT("%", #{vo.commodityKey} ,"%")  )
        </if>-->
        <if test="vo.commodityIdList != null and vo.commodityIdList.size() > 0">
            AND soi.commodity_id in <foreach collection="vo.commodityIdList" item="id" open="(" separator="," close=")"> #{id} </foreach>
        </if>
        GROUP BY soi.commodity_id
    </select>


    <select id="getXdReceiveCommodityByBarcode" resultType="com.pinshang.qingyun.order.dto.XdReceiveDocCommodityODTO">
        SELECT
           soi.commodity_id commodityId,
           -- c.commodity_code,
            -- c.commodity_name,
            -- c.commodity_package_kind,
           --   c.is_weight,
            -- c.commodity_spec,
            -- d.option_name as unit,
           -- IFNULL(c.commodity_package_spec,1) commodityPackageSpec,
            sum(soi.quantity) quantity,
            sum(soi.real_delivery_quantity) realDeliveryQuantity,
            IFNULL(sum(soi.real_receive_quantity),0) realReceiveQuantity,
            soi.price,
            -- tt.barCodes,
            -- md.shop_code,
            -- md.shop_name
            o.store_id
        from t_order o
        INNER  JOIN t_sub_order so on so.order_id = o.id
        INNER JOIN t_sub_order_item soi on soi.sub_order_id = so.id
        -- INNER JOIN t_md_shop md ON md.store_id = o.store_id
        INNER JOIN  t_md_receive_order  mro on  mro.sub_order_id = so.id and mro.status = 0
        -- INNER JOIN t_commodity c ON c.id = soi.commodity_id
        -- left JOIN t_dictionary d ON c.commodity_unit_id = d.id
        /*LEFT JOIN (
            SELECT
            t.commodity_id commodityId,
            GROUP_CONCAT(t.bar_code ORDER BY t.default_state desc) barCodes
            from t_commodity_bar_code t
            where 1=1
            GROUP BY t.commodity_id
        ) tt on tt.commodityId = c.id*/

        WHERE  1=1
        <if test=" allComm != null  and allComm == 1 ">
            AND  soi.real_delivery_quantity > 0
        </if>
        AND o.order_time = #{deliveryTime}
        and o.store_id = (select store_id from t_md_shop where id = #{shopId})
        AND soi.commodity_id = (select cb.commodity_id from t_commodity_bar_code cb where cb.bar_code = #{barcode} )
        GROUP BY soi.commodity_id
    </select>


    <select id="getUnreceiveQuantityByParam" resultType="java.math.BigDecimal">
        SELECT
            sum(soi.real_delivery_quantity- IFNULL(soi.real_receive_quantity,0))
        from t_order o
        INNER  JOIN t_sub_order so on so.order_id = o.id
        INNER JOIN t_sub_order_item soi on soi.sub_order_id = so.id
        -- INNER JOIN t_md_shop md ON md.store_id = o.store_id
        INNER JOIN  t_md_receive_order  mro on  mro.sub_order_id = so.id and mro.status = 0
        -- INNER JOIN t_commodity c ON c.id = soi.commodity_id
        WHERE soi.real_delivery_quantity > 0
        AND o.order_time = #{deliveryTime}
        and o.store_id = (select store_id from t_md_shop where id = #{shopId})
        AND soi.commodity_id = #{commodityId}
        GROUP BY soi.commodity_id
    </select>

    <select id="getOrderCommodityInfo" resultType="com.pinshang.qingyun.order.dto.XdReceiveOrderCommodityODTO">
         SELECT
           so.id subOrderId,
           so.sub_order_code subOrderCode,
           soi.id subOrderItemId,
           soi.commodity_id,
           soi.quantity,
           IFNULL(soi.real_delivery_quantity,0) realDeliveryQuantity,
           IFNULL(soi.real_receive_quantity,0) realReceiveQuantity,
           soi.price,
           ol.remark,
           o.order_remark
           -- IFNULL(c.commodity_package_spec,1) commodityPackageSpec,
           -- c.commodity_name
        from t_order o
        INNER  JOIN t_sub_order so on so.order_id = o.id
        INNER JOIN t_sub_order_item soi on soi.sub_order_id = so.id
        -- INNER JOIN t_md_shop md ON md.store_id = o.store_id
        INNER JOIN  t_md_receive_order  mro on  mro.sub_order_id = so.id and mro.status = 0
        -- INNER JOIN t_commodity c ON c.id = soi.commodity_id
        left JOIN t_order_list ol ON ol.order_id = o.id and ol.commodity_id = soi.commodity_id
        WHERE  soi.real_delivery_quantity > 0
       AND o.order_time = #{deliveryTime}
          and o.store_id = (select store_id from t_md_shop where id = #{shopId})
        AND soi.commodity_id = #{commodityId}
     ORDER BY ol.remark desc,soi.quantity,o.id
    </select>

    <update id="updateRealReceiveQuantity">
        UPDATE t_sub_order_item
        SET real_receive_quantity = IFNULL(real_receive_quantity,0) + #{unReceiveQuantityitem}
        WHERE id = #{subOrderId}
        AND commodity_Id = #{commodityId}
    </update>

</mapper>