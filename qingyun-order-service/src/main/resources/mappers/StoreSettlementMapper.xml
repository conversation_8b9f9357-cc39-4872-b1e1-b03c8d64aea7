<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.pinshang.qingyun.order.mapper.StoreSettlementMapper">


    <resultMap id="storeSettlementResult" type="com.pinshang.qingyun.order.model.order.StoreSettlement">
        <result property="id" column="id"/>
        <result property="storeId" column="store_id"/>
        <result property="collectPrice" column="collect_price"/>
        <result property="collectStatus" column="collect_status"/>
    </resultMap>

    <select id="getStoreSettlementByStoreId" parameterType="java.lang.Long" resultMap="storeSettlementResult">
        select id,store_id,collect_price,collect_status
        from t_store_settlement
        where store_id = #{storeId}
    </select>

    <update id="updateCollectPrice">
        UPDATE t_store_settlement
        SET collect_price = collect_price - #{map.amountPrice}
        WHERE
          id = #{map.id}
        AND collect_price = #{map.collectPrice}
    </update>

    <update id="updateCollectPriceTwo">
        UPDATE t_store_settlement
        SET collect_price = collect_price + #{amountPrice}
        WHERE
          id = #{id}
    </update>

    <select id="queryBalance" resultType="com.pinshang.qingyun.order.mapper.entry.store.StoreSettlementEntry">
        SELECT store_id, collect_price FROM t_store_settlement ss
        INNER JOIN t_store s ON ss.store_id = s.id
        WHERE
        s.store_code = #{storeCode}
    </select>

    <select id="findStoreSettleByStoreIds" resultType="com.pinshang.qingyun.order.mapper.entry.store.StoreSettlementEntry">
    SELECT
           s.id storeId,
           ss.collect_price collectPrice,
           ss.id settlementId,
           ts.customer_code,
           ts.customer_name,
           ts.start_bill_date startBillDate,
           ts.xs_start_bill_date xsStartBillDate
    FROM
         t_store_settlement ss
             INNER JOIN t_store s ON ss.store_id = s.id
             INNER JOIN t_settlement ts ON ss.settlement_customer_id = ts.id
        where ss.store_id in
        <foreach collection="storeIds" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <select id="listStoreSettlementByStoreIds"
            resultType="com.pinshang.qingyun.order.model.order.StoreSettlement">
        select id,store_id,collect_price,collect_status
        from t_store_settlement
        where store_id in
        <foreach collection="storeIds" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>
</mapper>