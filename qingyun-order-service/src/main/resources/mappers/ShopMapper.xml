<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.pinshang.qingyun.order.mapper.ShopMapper">

    <select id="selectStoreList" resultType="com.pinshang.qingyun.order.mapper.entry.StoreEntry">
        SELECT
           s.id storeId,
           s.store_code,
           s.store_name,
           dl.line_name store_line_group_name,
           tms.shop_type,
           tms.shop_code,
           tms.shop_name,
           tms.id shopId,
           s.business_type
        FROM t_store s
          left join t_md_shop tms on s.id = tms.store_id
          left join t_distribution_line dl on dl.id = s.store_line_id
        where 1=1
        and s.`id` IN
        <foreach collection="storeIdList" index="index" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

    <!--查询所有门店id集合-->
    <select id="findAllShopIdList" resultType="Long">
        SELECT id FROM t_md_shop where 1=1;
    </select>

    <select id="getShopType" resultType="java.lang.Integer">
        SELECT t.shop_type FROM t_md_shop t where t.store_id =  #{storeId}
    </select>

    <select id="listShopType" resultType="com.pinshang.qingyun.order.mapper.entry.StoreEntry">
        SELECT t.shop_type,t.store_id FROM t_md_shop t where 1=1
        and t.store_id in
        <foreach collection="storeIds" index="index" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>
</mapper>