<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.pinshang.qingyun.order.mapper.PfCommoditySaleDayStatisticsMapper" >
	<update id="batchUpdate">
	    <foreach collection="dayStatisticsList" item="item" index="index" separator=";" open="" close="">
	      UPDATE t_pf_commodity_sale_day_statistics
	      SET
	          total_quantity = IFNULL(total_quantity,0) +  IFNULL(#{item.totalQuantity},0),
	          total_amount = IFNULL(total_amount,0) +  IFNULL(#{item.totalAmount},0)
	      WHERE
	        commodity_id = #{item.commodityId}
	      AND  order_time = #{item.orderTime}
	    </foreach>
  	</update>

	<select id="queryCommoditySaleStatistics" resultType="com.pinshang.qingyun.order.service.pf.saledaystatistics.CommoditySaleStatisticsDto">
		SELECT commodity_id commodityId,sum(total_amount) totalAmount ,sum(total_quantity) totalQuantity 
		FROM `t_pf_commodity_sale_day_statistics`
		where 
		<foreach collection="list" item="item" separator=" or ">
			(commodity_id=#{item.commodityId} and order_time between #{item.beginDate} and #{item.endDate})
		</foreach>
		GROUP BY commodity_id
	</select>
</mapper>