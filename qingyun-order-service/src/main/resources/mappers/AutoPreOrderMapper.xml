<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.pinshang.qingyun.order.mapper.auto.AutoPreOrderMapper">

    <select id="queryByIDTO" resultType="com.pinshang.qingyun.order.dto.AutoPreOrderODTO">
        SELECT
        apo.id as id,
        ms.shop_name as shopName,
        apo.logistics_model as logisticsModel,
        apo.order_code as orderCode,
        apo.order_amount as orderAmount,
        u.employee_name as createName,
        apo.create_time as createTime,
        u1.employee_name as auditName,
        apo.audit_time as auditTime,
        apo.status as status,
        apo.order_id as orderId,
        apo.delivery_time as deliveryTime
        FROM t_md_auto_pre_order apo
        LEFT JOIN t_md_shop ms ON apo.shop_id = ms.id
        LEFT JOIN t_employee_user u ON apo.create_id = u.user_id
        LEFT JOIN t_employee_user u1 on apo.audit_id = u1.user_id
        where 1=1
        <if test="vo.shopId != null">
            and apo.shop_id = #{vo.shopId}
        </if>
        <if test="vo.shopIdList != null and vo.shopIdList.size > 0">
            AND apo.shop_id IN
            <foreach collection="vo.shopIdList" index="index" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
        <if test="vo.orderCode != null  and vo.orderCode != '' ">
            and apo.order_code like CONCAT("%", #{vo.orderCode} ,"%")
        </if>
        <if test="vo.createId != null">
            AND apo.create_id = #{vo.createId}
        </if>
        <if test="vo.status != null">
            AND apo.status = #{vo.status}
        </if>
        <if test="vo.deliveryStartTime !=null and vo.deliveryStartTime !='' and vo.deliveryEndTime !=null and vo.deliveryEndTime !=''">
            AND apo.delivery_time BETWEEN date(#{vo.deliveryStartTime}) AND date(#{vo.deliveryEndTime})
        </if>
        <if test="!vo.isInternal and (vo.consignmentId == null or vo.consignmentId == 0)">
            and apo.consignment_id = -1
        </if>
        <if test="!vo.isInternal and vo.consignmentId != null and vo.consignmentId > 0 ">
            and apo.consignment_id = #{vo.consignmentId}
        </if>
        order by apo.create_time DESC
    </select>
    <select id="queryById" resultType="com.pinshang.qingyun.order.dto.AutoPreOrderODTO">
        SELECT
            apo.id as id,
            ms.shop_name as shopName,
            apo.logistics_model as logisticsModel,
            apo.order_code as orderCode,
            apo.order_amount as orderAmount,
            u.employee_name as createName,
            apo.create_time as createTime,
            u1.employee_name as auditName,
            apo.audit_time as auditTime,
            apo.status as status,
            apo.order_id as orderId,
            apo.delivery_time as deliveryTime,
            apo.shop_id as shopId
        FROM t_md_auto_pre_order apo
                 LEFT JOIN t_md_shop ms ON apo.shop_id = ms.id
                 LEFT JOIN t_employee_user u ON apo.create_id = u.user_id
                 LEFT JOIN t_employee_user u1 on apo.audit_id = u1.user_id where apo.id = #{id}


    </select>
</mapper>