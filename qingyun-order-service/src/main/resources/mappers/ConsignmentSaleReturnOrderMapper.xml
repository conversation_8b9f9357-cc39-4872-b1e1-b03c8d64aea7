<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.pinshang.qingyun.order.mapper.ConsignmentSaleReturnOrderMapper" >

    <select id="list" parameterType="java.util.Map"
            resultType="com.pinshang.qingyun.order.vo.order.ConsignmentSaleReturnOrderRespVo">
        SELECT
        sro.id,
        sro.supplier_id,
        s.store_name AS storeName,
        s.store_code AS storeCode,
        sro.create_time AS returnOrderDate,
        sro.confirm_time AS confirmOrderDate,
        sro.order_code AS returnOrderCode,
        sro.`status` AS status,
        u.employee_name AS confirmUser,
        u1.employee_name AS createUser,
        tms.shop_code AS shopCode,
        tms.shop_name AS shopName
        FROM
        t_consignment_sale_return_order sro
        INNER JOIN t_store s ON sro.store_id = s.id
        INNER JOIN t_consignment_supplier tcs ON sro.supplier_id = tcs.supplier_id and tcs.status=1
        INNER JOIN t_consignment_supplier_shop tcss ON sro.store_id = tcss.store_id and sro.supplier_id = tcss.supplier_id and tcss.status=1
        INNER JOIN t_md_shop tms ON  tcss.shop_id = tms.id
        LEFT JOIN t_employee_user u ON sro.confirm_id = u.user_id
        LEFT JOIN t_employee_user u1 ON sro.create_id = u1.user_id
        <where>
            <if test="returnOrderStartDate != null and returnOrderEndDate != null">
                AND sro.create_time <![CDATA[ >= ]]> #{returnOrderStartDate}
                AND sro.create_time <![CDATA[ <= ]]> #{returnOrderEndDate}
            </if>
            <if test="confirmOrderStartDate != null and confirmOrderEndDate != null">
                AND sro.confirm_time <![CDATA[ >= ]]> #{confirmOrderStartDate}
                AND sro.confirm_time <![CDATA[ <= ]]> #{confirmOrderEndDate}
            </if>
            <if test="auditOrderStartDate != null and auditOrderEndDate != null">
                AND sro.check_time <![CDATA[ >= ]]> #{auditOrderStartDate}
                AND sro.check_time <![CDATA[ <= ]]> #{auditOrderEndDate}
            </if>
            <if test="returnOrderCode != null">
                AND sro.order_code = #{returnOrderCode}
            </if>
            <if test="status != null">
                AND sro.status = #{status}
            </if>
            <if test="shopId != null">
                AND tms.id = #{shopId}
            </if>
            <if test="storeId != null">
                AND sro.store_id = #{storeId}
            </if>
            <if test="createId != null">
                AND sro.create_id = #{createId}
            </if>
            <if test="confirmId != null">
                AND sro.confirm_id = #{confirmId}
            </if>
            <if test="supplierId != null">
                AND sro.supplier_id = #{supplierId}
            </if>
            <if test="shopIdList !=null and shopIdList.size > 0">
                AND tms.id IN
                <foreach collection="shopIdList" index="index" item="id" open="(" separator="," close=")">
                    #{id}
                </foreach>
            </if>
        </where>
        ORDER BY sro.create_time DESC,tms.shop_code ASC
    </select>
    <select id="selectById"
            resultType="com.pinshang.qingyun.order.dto.consignment.ConsignmentSaleReturnOrderODTO">
        SELECT
            sro.supplier_id AS supplierId,
            s.store_name AS storeName,
            s.store_code AS storeCode,
            sro.create_time AS returnOrderDate,
            sro.order_code AS returnOrderCode,
            sro.`status` AS status,
            u1.employee_name AS createUser,
            tms.shop_code AS shopCode,
            tms.shop_name AS shopName
        FROM
            t_consignment_sale_return_order sro
                INNER JOIN t_store s ON sro.store_id = s.id
                INNER JOIN t_consignment_supplier tcs ON sro.supplier_id = tcs.supplier_id and tcs.status=1
                INNER JOIN t_consignment_supplier_shop tcss ON sro.store_id = tcss.store_id and sro.supplier_id = tcss.supplier_id and tcss.status=1
                INNER JOIN t_md_shop tms ON  tcss.shop_id = tms.id
                LEFT JOIN t_employee_user u1 ON sro.create_id = u1.user_id
        where
            sro.id = #{id}
    </select>
    <select id="countNotConfirmSaleReturnOrder" resultType="java.lang.Integer">
        SELECT
        count(1)
        from t_consignment_sale_return_order t
        where t.create_time BETWEEN #{beginDate} and #{endDate}
        and t.status = 1
        and t.store_id = #{storeId}
    </select>


    <select id="consignmentReturnOrderReport" resultType="com.pinshang.qingyun.order.dto.consignment.ConsignmentReturnOrderPageODTO">
        SELECT
            DATE_FORMAT(csro.create_time,'%Y-%m-%d') orderTime,
            csro.order_code,
            csro.store_id,
            md.id shopId,
            csroi.commodity_id,
            csroi.return_quantity,
            csroi.confirm_quantity,
            csroi.check_quantity
        from t_consignment_sale_return_order csro
        LEFT JOIN t_consignment_sale_return_order_item csroi on csroi.consignment_sale_return_order_id = csro.id
        LEFT JOIN t_md_shop md on md.store_id = csro.store_id
        where csro.status != 0
        <if test="vo.shopId != null" >
            AND md.id = #{vo.shopId}
        </if>

        <if test="vo.shopIdList != null and vo.shopIdList.size > 0">
            AND md.id IN
            <foreach collection="vo.shopIdList" index="index" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>

        <if test="vo.beginDate != null and vo.beginDate != '' and vo.endDate != null and vo.endDate != '' ">
            and csro.create_time BETWEEN #{vo.beginDate} and #{vo.endDate}
        </if>

        <if test="vo.orderCode != null and vo.orderCode != '' ">
            and csro.order_code = #{vo.orderCode}
        </if>

        <if test="vo.consignmentId != null" >
            AND csro.supplier_id = #{vo.consignmentId}
        </if>


        <if test="vo.commodityIdList != null and vo.commodityIdList.size() > 0">
            AND csroi.commodity_id in <foreach collection="vo.commodityIdList" item="id" open="(" separator="," close=")"> #{id} </foreach>
        </if>

        order by orderTime desc,md.shop_code,csro.order_code
    </select>
</mapper>