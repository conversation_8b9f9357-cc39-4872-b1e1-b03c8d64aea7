<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.pinshang.qingyun.order.mapper.MdShopOrderSettingLogMapper">

	<select id="queryMdShopOrderSettingLogListByParams" resultType="com.pinshang.qingyun.order.mapper.entry.MdShopOrderSettingLogEntry" parameterType="com.pinshang.qingyun.order.vo.shop.MdShopOrderSettingLogVo" >
		SELECT
			sosl.id,
			sosl.opreate_type,
			sosl.commodity_code,
			sosl.commodity_name,
			sosl.commodity_spec,
			sosl.shop_type,
			sosl.supplier_code,
			sosl.supplier_name,
			sosl.from_logistics_model,
			sosl.to_logistics_model,
			sosl.from_delevery_time_range,
			sosl.to_delevery_time_range,
			sosl.from_change_price_status,
			sosl.to_change_price_status,
			sosl.create_id,
			sosl.create_time,
			IFNULL(tuser.employee_name,'系统') createName
		from t_md_shop_order_setting_log sosl
		   left  JOIN  t_employee_user tuser on tuser.user_id=sosl.create_id
		WHERE 1=1
		<if test="commodityCodeOrName!=null and commodityCodeOrName !='' ">
			AND (sosl.commodity_code like concat('%',#{commodityCodeOrName},'%')   or  sosl.commodity_name like concat('%',#{commodityCodeOrName},'%') )
		</if>
		<if test="supplierId!=null">
			AND sosl.supplier_id =#{supplierId}
		</if>
		<if test="shopType != null">
			AND sosl.shop_type =#{shopType}
		</if>
		<if test="toLogisticsModel!=null">
			AND sosl.to_logistics_model =#{toLogisticsModel}
		</if>
		<if test="toDeleveryTimeRange!=null and toDeleveryTimeRange !='' ">
			AND sosl.to_delevery_time_range = #{toDeleveryTimeRange}
		</if>
		<if test="toChangePriceStatus!=null">
			AND sosl.to_change_price_status = #{toChangePriceStatus}
		</if>
		<if test="beginDate!=null and beginDate !='' and endDate!=null and endDate !='' ">
			AND sosl.create_time BETWEEN  #{beginDate}  AND  #{endDate}
		</if>
		ORDER BY sosl.create_time DESC

	</select>

</mapper>