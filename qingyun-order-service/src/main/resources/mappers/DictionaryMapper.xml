<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.pinshang.qingyun.order.mapper.DictionaryMapper">

    <select id="queryDictionaryConfigList" resultType="com.pinshang.qingyun.order.model.dictionary.Dictionary">
        SELECT d1.id,d1.dictionary_id,d2.option_code,d1.option_value
        FROM t_dictionary d1
        INNER JOIN t_dictionary d2 ON d1.dictionary_id = d2.id
        WHERE d2.option_code IN
        <foreach collection="optionCodeList" item="item" index="index" separator="," open="(" close=")">
        #{item}
        </foreach>
        AND d1.option_state =1
    </select>


    <select id="findLineGroups" resultType="com.pinshang.qingyun.order.dto.cup.LineGroupODTO">
        SELECT
            d.id id,
            d.option_name AS lineGroupName,
            dt.end_time AS deliveryTime
        FROM
            t_dictionary d
                LEFT JOIN t_delivery_time dt ON d.id = dt.line_group_id
        WHERE
            dictionary_id = 9131078242860601278
        ORDER BY
            dt.end_time
    </select>
</mapper>