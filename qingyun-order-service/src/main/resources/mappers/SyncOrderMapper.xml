<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.pinshang.qingyun.order.mapper.SyncOrderMapper">

    <resultMap id="orderResult" type="com.pinshang.qingyun.order.model.order.Order">
        <result property="id" column="id"/>
        <result property="orderCode" column="order_code"/>
        <result property="storeId" column="store_id"/>
        <result property="orderTime" column="order_time"/>
        <result property="totalAmount" column="total_amount"/>
        <result property="orderAmount" column="order_amount"/>
        <result property="finalAmount" column="final_amount"/>
    </resultMap>


    <update id="updateAmountPrice">
        update t_order
        set
          order_amount = #{finalAmount},
          sync_status = 1
        where id = #{orderId}
        AND sync_status = 0
    </update>

    <select id="listTomorrowOrderId" resultMap="orderResult">
        SELECT
            o.id,
            o.order_code,
            o.store_id,
            o.order_time,
            o.total_amount,
            o.order_amount,
            o.final_amount
        FROM
          t_order o
        LEFT JOIN t_store s ON o.store_id = s.id
        LEFT JOIN t_distribution_line dl ON s.store_line_id = dl.id
        LEFT JOIN t_delivery_time dt ON dt.line_group_id = dl.delivery_time_id
        WHERE
          dt.line_group_id = #{deliveryTime.lineGroupId}
        <if test="deliveryTime.coverFlag == true" >
            AND o.order_time BETWEEN  DATE_SUB(date(NOW()), INTERVAL 2 DAY) AND date(NOW())
        </if>
        <if test="deliveryTime.coverFlag == false" >
            AND o.order_time BETWEEN  DATE_SUB(date(NOW()), INTERVAL 1 DAY) AND DATE_ADD(date(NOW()), INTERVAL 1 DAY)
        </if>
        AND o.sync_status=0
        AND o.order_status = 0
    </select>

    <select id="listNotSyncOrderIds" resultType="java.lang.Long">
        SELECT
          o.id
        FROM
          t_order o
        LEFT JOIN t_store s ON o.store_id = s.id
        LEFT JOIN t_distribution_line dl ON s.store_line_id = dl.id
        LEFT JOIN t_delivery_time dt ON dt.line_group_id = dl.delivery_time_id
        WHERE
          dt.line_group_id = #{deliveryTime.lineGroupId}
        <if test="deliveryTime.coverFlag == true" >
            AND o.order_time BETWEEN  DATE_SUB(date(NOW()), INTERVAL 2 DAY) AND date(NOW())
        </if>
        <if test="deliveryTime.coverFlag == false" >
            AND o.order_time BETWEEN  DATE_SUB(date(NOW()), INTERVAL 1 DAY) AND DATE_ADD(date(NOW()), INTERVAL 1 DAY)
        </if>
        AND o.order_amount =  o.final_amount
        AND o.sync_status=0 AND o.order_status = 0

    </select>

    <update id="updateNotSyncByOrderId">
        UPDATE t_order
        SET  sync_status = 1
        WHERE id IN
        <foreach collection="orderIds" item="item" index="index" open="(" separator="," close=")">
            #{item}
        </foreach>
        AND sync_status = 0
    </update>
    
</mapper>