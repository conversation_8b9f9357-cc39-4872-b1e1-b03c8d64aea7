<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.pinshang.qingyun.order.mapper.RtWarehouseReceiptMapper">

    <select id="selectRtWarehouseReceiptList"
            resultType="com.pinshang.qingyun.order.mapper.entry.externalDocking.RtWarehouseReceiptEntry">
        SELECT
        rtc.rt_commodity_name AS productName,
        rtc.rt_commodity_code AS productSKU,
        DATE_FORMAT(o.order_time, '%Y-%m-%d %T') AS manufactureTime,
        SUM(lg.commodity_num * conversion_rate) AS number
        FROM
        t_order_list_gift lg
        LEFT JOIN t_order o ON lg.order_id = o.id
        LEFT JOIN t_store s ON s.id = o.store_id
        LEFT JOIN t_rt_shop rts ON s.id = rts.qm_store_id
        LEFT JOIN t_rt_commodity rtc ON lg.commodity_id = rtc.qm_commodity_id
        <where>
            rtc.is_weight = 1
            AND o.order_status = 0
            AND rts.receiving_unit != ''
            AND rts.receiving_unit IS NOT NULL
            <if test="null != deliveryTime and deliveryTime != ''">
                AND o.order_time = #{deliveryTime}
            </if>
            <if test="null != storeShortName">
                AND rts.store_short_name = #{storeShortName}
                AND rtc.store_short_name = #{storeShortName}
            </if>
        </where>
        GROUP BY rtc.rt_commodity_name,rtc.rt_commodity_code,o.order_time
        ORDER BY rtc.rt_commodity_code
    </select>
</mapper>