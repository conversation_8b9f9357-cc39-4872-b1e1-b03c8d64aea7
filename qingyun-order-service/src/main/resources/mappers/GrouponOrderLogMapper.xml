<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.pinshang.qingyun.order.mapper.groupon.GrouponOrderLogMapper" >

    <select id="queryGrouponOrderLog" resultType="java.lang.Long">
        SELECT distinct t.groupon_id
        FROM t_groupon_order_log t
        WHERE t.create_time between #{beginTime} and #{endTime}
        and t.groupon_type = #{groupType}
    </select>


    <delete id="deleteGrouponOrderDay" >
        delete from t_group_order_business
        where  order_time = #{orderTime}
    </delete>

    <insert id="insertGrouponOrderDay">
        Insert into t_group_order_business(order_time,shop_id,commodity_id,quantity,
                                    price,create_id,create_time)

        SELECT
            o.order_time,
            md.id shopId,
            soi.commodity_id,
            soi.quantity,
            soi.price,
            -1,
            NOW()
        FROM t_order o
                 inner JOIN t_sub_order so on so.order_id = o.id
                 inner JOIN t_sub_order_item soi on soi.sub_order_id = so.id
                 INNER JOIN t_md_shop md on md.store_id = o.store_id
        WHERE o.order_status = 0 and so.status != 2
          and o.order_time = #{orderTime}
          and o.order_remark = '团购'
    </insert>



    <select id="commodityGroupOrder" parameterType="com.pinshang.qingyun.order.dto.GroupOrderIDTO" resultType="com.pinshang.qingyun.order.dto.GroupOrderODTO">
        SELECT
            c.groupon_code,
            CONCAT(c.groupon_start_time,' ~ ',c.groupon_end_time) grouponTime,
            md.shop_code,
            md.shop_name,
            c.commodity_id,
            <if test="groupType == 1" >
                c.quantity grouponQuantity,
                c.quantity * c.price grouponAmount,
                b.quantity orderQuantity,
                b.quantity * b.price orderAmount
            </if>
            <if test="groupType == 2" >
                sum(c.quantity) grouponQuantity,
                sum(c.quantity * c.price) grouponAmount,
                sum(b.quantity) orderQuantity,
                sum(b.quantity * b.price) orderAmount
            </if>
        FROM
            t_groupon_order_custom c
        LEFT JOIN t_group_order_business b ON b.order_time = c.arrival_time AND b.shop_id = c.shop_id AND b.commodity_id = c.commodity_id
        LEFT JOIN t_md_shop md ON md.id = c.shop_id
        WHERE 1 = 1
        <if test="beginDate != null and endDate != null and beginDate != '' and endDate != ''">
            AND c.arrival_time between #{beginDate} and #{endDate}
        </if>
        <if test="null != shopId" >
            and c.shop_id = #{shopId}
        </if>
        <if test="null != commodityId" >
            and c.commodity_id = #{commodityId}
        </if>
        <if test="null != grouponCode and grouponCode != '' " >
            and c.groupon_code = #{grouponCode}
        </if>
        <if test="groupType == 2" >
            group by c.shop_id
        </if>
    </select>
    <select id="commodityGroupOrderSum" parameterType="com.pinshang.qingyun.order.dto.GroupOrderIDTO" resultType="com.pinshang.qingyun.order.dto.GroupOrderODTO">
        SELECT
            sum(c.quantity) grouponQuantity,
            sum(c.quantity * c.price) grouponAmount,
            sum(b.quantity) orderQuantity,
            sum(b.quantity * b.price) orderAmount
        FROM
             t_groupon_order_custom c
        LEFT JOIN t_group_order_business b ON b.order_time = c.arrival_time AND b.shop_id = c.shop_id AND b.commodity_id = c.commodity_id
        WHERE 1 = 1
        <if test="beginDate != null and endDate != null and beginDate != '' and endDate != ''">
            AND c.arrival_time between #{beginDate} and #{endDate}
        </if>
        <if test="null != shopId" >
            and c.shop_id = #{shopId}
        </if>
        <if test="null != commodityId" >
            and c.commodity_id = #{commodityId}
        </if>
        <if test="null != grouponCode and grouponCode != '' " >
            and c.groupon_code = #{grouponCode}
        </if>

    </select>
</mapper>