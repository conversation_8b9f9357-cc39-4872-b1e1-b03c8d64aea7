<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.pinshang.qingyun.order.mapper.SaleReturnItemMapper">

	<select id="getSaleReturnItemById" parameterType="java.lang.String" resultType="com.pinshang.qingyun.order.mapper.entry.order.SaleReturnItemEntry">
		SELECT
		oi.id,
		oi.commodity_id,
		c.commodity_code,
		c.commodity_name,
		c.commodity_spec,
        d.`option_name` as unit,
		oi.price,
		oi.return_quantity,
		oi.total_price,
		oi.return_reason,
		oi.real_return_quantity,
		oi.remark,
		IFNULL(c.commodity_package_spec,1) commodityPackageSpec,
		oi.rp_type
		FROM
		t_sale_return_order_item oi
		INNER JOIN t_commodity c on c.id = oi.commodity_id
		left  join `t_dictionary` d on d.`id` = c.`commodity_unit_id`
		WHERE 1=1
        and oi.sale_return_order_id = #{saleReturnOrderId}
        ORDER BY oi.commodity_id
	</select>

	<update id="confirmItems" parameterType="java.util.List">
		<foreach collection="list" item="item" index="index" separator=";"
				 open="" close="">
			update t_sale_return_order_item
			set
			real_return_quantity = #{item.realReturnQuantity},
			total_price = #{item.totalPrice}
			where 1=1
			and id = #{item.id}
		</foreach>
	</update>

	<select id="copySaleReturnItemById" parameterType="java.lang.String" resultType="com.pinshang.qingyun.order.mapper.entry.order.SaleReturnItemEntry">
		select
		oi.commodity_id
		-- c.commodity_code,
		-- c.commodity_name,
		-- c.commodity_spec,
		-- d.`option_name` as unit,
		-- c.logistics_model,
		-- c.is_weight as ifWeight,
		-- IFNULL(c.commodity_package_spec,1) commodityPackageSpec
		FROM
		t_sale_return_order_item oi
		INNER JOIN t_sale_return_order o ON o.id = oi.sale_return_order_id
    	-- INNER JOIN t_md_shop ms on o.store_id = ms.store_id
		-- INNER JOIN t_commodity c on c.id = oi.commodity_id and c.logistics_model is not null
		-- LEFT  JOIN `t_dictionary` d on d.`id` = c.`commodity_unit_id`
		WHERE 1=1
		-- ((c.logistics_model = 0) or (c.logistics_model != 0))
		and oi.sale_return_order_id = #{saleReturnOrderId}
	</select>

	<select id="getSaleReturnItemForAuditById" parameterType="java.lang.String" resultType="com.pinshang.qingyun.order.mapper.entry.order.SaleReturnItemForAuditEntry">
		SELECT
		oi.id,
		oi.commodity_id,
		c.commodity_code,
		c.commodity_name,
		c.commodity_spec,
		d.`option_name` as unit,
		oi.price,
		oi.return_quantity,
		oi.total_price,
		oi.return_reason,
		oi.real_return_quantity,
		oi.remark,
		IFNULL(c.commodity_package_spec,1) commodityPackageSpec,
		oi.rp_type,
		oi.audit_remark,
		oi.compensate_price compensatePrice,
		c.is_weight ifWeight
		FROM
		t_sale_return_order_item oi
		INNER JOIN t_commodity c on c.id = oi.commodity_id
		left  join `t_dictionary` d on d.`id` = c.`commodity_unit_id`
		WHERE 1=1
		and oi.sale_return_order_id = #{saleReturnOrderId}
		ORDER BY oi.commodity_id
	</select>

	<select id="saleReturnDetailPage" resultType="com.pinshang.qingyun.order.dto.order.SaleReturnDetailPageODTO"
			parameterType="com.pinshang.qingyun.order.dto.order.SaleReturnDetailPageIDTO">
		SELECT
		r.id returnId,
		r.`store_id`,
		ms.`id` shopId,
		ms.`shop_name` shopName,
		r.`create_time` returnDate,
		r.`order_code` returnCode,
		c.`commodity_code` commodityCode,
		c.`commodity_name` commodityName,
		c.`bar_code` barCode,
		tc.cate_name AS cateName,
		c.commodity_spec AS commoditySpec,
		td.`option_name` commodityUnit,
		ri.`price`,
		ri.`return_quantity` returnQuantity,
		ri.`return_reason` returnReasonId,
		ri.`remark`,
		ri.`real_return_quantity` realReturnQuantity,
		ri.total_price totalPrice,
		ri.rp_type reTypeId,
		ri.`compensate_price` compensatePrice,
		ri.`audit_remark` auditRemark,
		c.id commodityId,
		r.`status`,
		r.`create_id` returnUserId,
		DATE_FORMAT(r.`create_time`,'%Y-%m-%d %H:%i:%S') AS returnTime,
		r.`update_id` auditUserId,
		r.`update_id` cancelUserId,
		DATE_FORMAT(r.`update_time`,'%Y-%m-%d %H:%i:%S') AS auditTime,
		DATE_FORMAT(r.`update_time`,'%Y-%m-%d %H:%i:%S') AS cancelTime,
		r.stall_id
		FROM
		t_sale_return_order_item ri
		LEFT JOIN t_sale_return_order r
		ON r.`id` = ri.`sale_return_order_id`
		LEFT JOIN t_commodity c
		ON c.`id` = ri.`commodity_id`
		LEFT JOIN t_md_shop ms
		ON ms.`store_id` = r.`store_id`
		LEFT JOIN t_dictionary td ON c.commodity_unit_id = td.id
		LEFT JOIN t_category tc ON c.commodity_first_id = tc.id
		WHERE 1=1
		<if test="null != shopId">
			AND ms.id = #{shopId}
		</if>
		<if test="null != rpType">
			AND ri.`rp_type` = #{rpType}
		</if>
		<if test="null != returnCode and '' != returnCode">
			AND r.`order_code` = #{returnCode}
		</if>
		<if test="null != returnReason">
			AND ri.`return_reason` = #{returnReason}
		</if>
		<if test="null != returnType and returnType == 2">
			AND ri.`return_reason` = 50
		</if>
		<if test="null != returnType and returnType == 1">
		    AND ri.`return_reason` != 50
		</if>
		<if test="null != commodityId">
			AND ri.`commodity_id` = #{commodityId}
		</if>
		<if test="null != beginTime and '' != beginTime and null != endTime and '' != endTime">
			AND r.create_time between #{beginTime} and #{endTime}
		</if>
		<if test="cateId1 != null">
			AND c.commodity_first_id = #{cateId1}
		</if>
		<if test="cateId2 != null">
			AND c.commodity_second_id = #{cateId2}
		</if>
		<if test="cateId3 != null">
			AND c.commodity_third_id = #{cateId3}
		</if>
		<if test="consignmentId != null">
			AND r.consignment_id = #{consignmentId}
		</if>
		<if test="stallId != null">
			and r.stall_id = #{stallId}
		</if>
		<if test="stallIdList != null and stallIdList.size > 0">
			AND r.stall_id IN
			<foreach collection="stallIdList" index="index" item="stallid" open="(" separator="," close=")">
				#{stallid}
			</foreach>
		</if>
		ORDER BY r.`order_code` DESC, ri.`id` ASC
	</select>

	<update id="cancelBySaleReturnId">
		UPDATE
		t_sale_return_order_item ri
		INNER JOIN t_sale_return_order o
		ON o.`id` = ri.`sale_return_order_id`
		SET
		ri.`audit_remark` = #{remark}
		WHERE o.`order_code` = #{saleReturnCode}
	</update>
</mapper>