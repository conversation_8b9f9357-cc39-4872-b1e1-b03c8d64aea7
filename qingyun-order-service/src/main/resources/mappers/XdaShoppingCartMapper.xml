<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.pinshang.qingyun.order.mapper.XdaShoppingCartMapper">

    <delete id="deleteShopCart">
        delete from t_xda_shopping_cart
        where store_id =#{storeId} and commodity_id in
        <foreach collection="idList" index="index" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <delete id="deleteShopCartWithCommodityType">
        delete from t_xda_shopping_cart
        where store_id =#{storeId} and commodity_type =#{commodityType}
          and commodity_id in
        <foreach collection="idList" index="index" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="shopCartIdsNotInThCommoditySet" resultType="java.lang.Long">
        select commodity_id from t_xda_shopping_cart
        where commodity_id not in(
            select t.commodity_id from t_xda_specials_commodity_set t)
          and commodity_type = #{commodityType}
          and  store_id =#{storeId}
    </select>
</mapper>