<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.pinshang.qingyun.order.mapper.SubOrderConfirmMapper">
    <select id="queryUnConfirmSubOrderIdList"
            resultType="com.pinshang.qingyun.order.mapper.entry.order.OrderTODeliveryOrderEntry">
        SELECT
            so.order_id     AS orderId,
            c.sub_order_id  AS subOrderId,
            so.order_time   AS orderTime,
            so.warehouse_id AS warehouseId,
            o.business_type
        FROM
            t_sub_order_confirm c
            INNER JOIN t_sub_order so ON c.sub_order_id = so.id
            INNER JOIN t_order o ON o.id = so.order_id
        WHERE c.create_time <![CDATA[ < ]]> DATE_SUB(NOW(), INTERVAL 2 MINUTE) LIMIT 500
    </select>

    <delete id="batchDelete">
        DELETE FROM t_sub_order_confirm
        WHERE sub_order_id IN
        <foreach collection="subOrderIds" item="item" index="index" open="(" separator="," close=")">
            #{item}
        </foreach>
    </delete>
</mapper>