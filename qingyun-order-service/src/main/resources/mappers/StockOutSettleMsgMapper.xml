<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.pinshang.qingyun.order.mapper.StockOutSettleMsgMapper" >

    <select id="selectstockOutSettleMsgByTime" resultType="com.pinshang.qingyun.order.model.order.StockOutSettleMsg">
        SELECT
            t.id,
            t.sub_order_id,
            t.stock_out_order_code,
            t.stock_out_time,
            t.option_type
         FROM t_stock_out_settle_msg t
        WHERE t.create_time between #{beginTime} and #{endTime}
        and t.status = 0
    </select>
</mapper>