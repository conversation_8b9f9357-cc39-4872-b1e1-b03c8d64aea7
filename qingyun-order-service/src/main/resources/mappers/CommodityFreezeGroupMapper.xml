<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.pinshang.qingyun.order.mapper.CommodityFreezeGroupMapper" >
    <select id="page" resultType="com.pinshang.qingyun.order.dto.CommodityFreezeGroupODTO">
        SELECT
        c.id,
        c.commodity_id
        FROM
        t_commodity_freeze_group c
        <if test = "null != commodityId">
        WHERE c.`commodity_id` =  #{commodityId}
        </if>
    </select>

    <select id="selectOldCommodity" resultType="java.lang.Long">
        SELECT
        c.`id`
        FROM
        t_commodity_freeze_group cg
        LEFT JOIN t_commodity c
        ON c.`id` = cg.`commodity_id`
        WHERE cg.`commodity_id` IN
        <foreach collection="list" item="item" index="index" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <select id="selectInfoForLog" resultType="com.pinshang.qingyun.order.dto.CommodityFreezeGroupEntryLog">
        SELECT
        cg.`commodity_id`,
        c.`commodity_code`,
        c.`commodity_name`,
        c.`commodity_spec`,
        c.`bar_code` AS barCode,
        d.`option_name` AS unitName,
        c.is_weight,
        DATE_FORMAT(cg.`update_time`, '%Y-%m-%d %H:%i:%S') operaTime,
        eu.`employee_name` AS operaUserName
        FROM
        t_commodity_freeze_group cg
        LEFT JOIN t_commodity c
        ON c.id = cg.commodity_id
        LEFT JOIN `t_dictionary` d
        ON d.`id` = c.`commodity_unit_id`
        LEFT JOIN t_employee_user eu
        ON eu.`user_id` = cg.`create_id`
        WHERE cg.`id` IN
        <foreach collection="list" item="item" index="index" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>
</mapper>
