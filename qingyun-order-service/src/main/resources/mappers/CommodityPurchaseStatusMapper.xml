<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.pinshang.qingyun.order.mapper.CommodityPurchaseStatusMapper" >
    <update id="updateByShopCommodity" parameterType="java.util.List">
        <foreach collection="list" item="item" index="index" separator=";"
                 open="" close="">
            UPDATE t_xs_shop_commodity_purchase_status txscps
            SET txscps.commodity_purchase_status = #{item.commodityPurchaseStatus}
            WHERE 1=1
            AND txscps.shop_id = #{item.shopId}
            AND txscps.commodity_id = #{item.commodityId}
        </foreach>
    </update>
    <select id="selectShopQtOrderProduct" resultType="com.pinshang.qingyun.order.mapper.entry.shop.ShopQtOrderProductEntry" parameterType="com.pinshang.qingyun.order.vo.shop.ShopQtOrderProductVo">
        SELECT
            o.store_id AS storeId,
            ol.commodity_id AS commodityId,
            SUM(ol.commodity_num) AS purchaseQuantity,
            DATE_ADD(o.order_time, INTERVAL -1 DAY) AS produceDate,
            o.order_time AS deliveryDate
        FROM t_order o
            INNER JOIN t_order_list_gift ol ON ol.order_id = o.id
            INNER JOIN t_md_shop ms on ms.store_id = o.store_id
        WHERE
            1=1
            AND o.order_time = #{orderTime}
            AND o.enterprise_id = #{enterpriseId}
            and ms.id = #{shopId}
        GROUP BY commodity_id
    </select>
    <select id="selectProductPriceModel" resultType="com.pinshang.qingyun.order.mapper.entry.shop.PriceModelLogEntry" parameterType="com.pinshang.qingyun.order.vo.shop.PriceModelLogVo">
        select
        ca.cate_name as categoryName,
        c.bar_code,
        c.commodity_code,
--         cbc.bar_codes,
        c.commodity_name,
        c.commodity_spec,
        c.id AS commodityId,
        d.option_name as unit,
        pml.review_pass_time as adjustTime ,
        pml.commodity_price AS price
        FROM
        t_xs_product_price_model_log pml
        inner join  t_commodity c on pml.commodity_id =c.id
        LEFT join t_category ca on ca.id =c.commodity_third_id
        LEFT JOIN t_dictionary d on d.id =c.commodity_unit_id
--         LEFT JOIN (
--         SELECT
--         GROUP_CONCAT(bar_code ORDER BY default_state desc) AS bar_codes,
--         commodity_id
--         FROM
--         t_commodity_bar_code
--         GROUP BY
--         commodity_id
--         ) AS cbc ON cbc.commodity_id = c.id
        WHERE
        operation_type =24
        <if test="null != productPriceModelId">
            AND pml.product_price_id = #{productPriceModelId}
        </if>
        <if test="commodityCode!=null and commodityCode !='' ">
            and (c.`commodity_code` like concat('%',#{commodityCode},'%') or c.`commodity_name` like concat('%',#{commodityCode},'%') or c.`commodity_aid` like concat('%',#{commodityCode},'%'))
        </if>
        <if test="categoryId !=null and categoryId !='' ">
            and ((c.`commodity_first_id`=#{categoryId}) or (c.`commodity_second_id`=#{categoryId}) or (c.`commodity_third_id`=#{categoryId}))
        </if>
        <if test="barCode !=null and barCode !=''">
            and c.id = (
            SELECT
            commodity_id AS id
            FROM
            t_commodity_bar_code
            WHERE
            bar_code = #{barCode}
            )
        </if>
        <if test="beginDate !=null and beginDate!= '' and endDate != null and endDate != '' ">
            and pml.review_pass_time BETWEEN #{beginDate} and #{endDate}
        </if>
        order by pml.create_time desc
    </select>
    <select id="selectProductPriceModelListByPriceModeClode" resultType="com.pinshang.qingyun.order.mapper.entry.shop.CommodityPriceEntry">
          select
              ppml.commodity_price AS commodityPrice,
              ppml.commodity_id AS commodityId
          from t_product_price_model ppm
		  inner JOIN t_product_price_model_list ppml ON ppm.id =ppml.product_price_model_id
          WHERE ppm.price_model_code = #{priceModeClode}
    </select>
    <select id="selectCommodityByProductPriceModelId" resultType="com.pinshang.qingyun.order.mapper.entry.shop.CommodityListEntry">
        SELECT
            tc.id AS commodityId,
            tc.commodity_code AS commodityCode,
            tc.commodity_name AS commodityName,
            tc.commodity_spec AS commoditySpec,
            tc.bar_code AS barCode
        FROM
        t_product_price_model pm
        LEFT JOIN t_product_price_model_list pml ON pml.product_price_model_id = pm.id -- 拆出来
        INNER  JOIN t_commodity tc ON pml.commodity_id = tc.id
        WHERE
          pm.id = #{productPriceModelId}
    </select>

    <select id="selectXsShopCommodityPurchaseStatus" resultType="com.pinshang.qingyun.order.mapper.entry.shop.XsShopCommodityPurchaseStatusEntry" parameterType="com.pinshang.qingyun.order.vo.shop.XsShopCommodityPurchaseStatusVo">
          select
          txscps.shop_id AS shopId,
          txscps.commodity_id AS commodityId,
          txscps.commodity_purchase_status AS commodityPurchaseStatus
          from
          t_xs_shop_commodity_purchase_status txscps
          LEFT JOIN t_commodity tc on tc.id = txscps.commodity_id
          <if test="barCode !=null  and barCode !='' ">
            LEFT JOIN t_commodity_bar_code tcbc on tcbc.commodity_id = tc.id
          </if>
          WHERE
          1=1
        <if test="shopId != null and shopId != 0">
            and txscps.`shop_id` = #{shopId}
        </if>
        <if test="shopId == 0 ">
            <choose>
                <when test="shopIdList != null and shopIdList.size >0">
                    and  txscps.shop_id IN
                    <foreach collection="shopIdList" item="id" open="(" close=")" separator=",">
                        #{id}
                    </foreach>
                </when>
                <otherwise>
                    and  1 = 2
                </otherwise>
            </choose>
        </if>
        <if test="commodityCode !=null and commodityCode !='' ">
            AND (
            tc.commodity_code =  trim(#{commodityCode})
            or tc.commodity_name =  trim(#{commodityCode})
            or tc.commodity_aid =  trim(#{commodityCode})
            )
        </if>
        <if test="barCode !=null  and barCode !='' ">
            AND  tcbc.bar_code = trim(#{barCode})
        </if>
        <if test="categoryId !=null and categoryId !='' ">
            and ((tc.`commodity_first_id`=#{categoryId}) or (tc.`commodity_second_id`=#{categoryId}) or (tc.`commodity_third_id`=#{categoryId}))
        </if>
        <if test="commodityPurchaseStatus != null">
            AND txscps.commodity_purchase_status = #{commodityPurchaseStatus}
        </if>

    </select>

    <select id="selectShopCommodityPurchaseStatusList" resultType="com.pinshang.qingyun.order.mapper.entry.purchase.ShopCommodityPurchaseStatusEntry">

        SELECT
            txscps.shop_id AS shopId,
            tms.shop_code AS shopCode,
            tms.shop_name AS shopName,
            tc.commodity_code AS commodityCode,
            tc.bar_code AS barCode,
            tc.commodity_name AS commodityName,
            tc.commodity_spec AS commoditySpec,
            tc.commodity_unit_id AS commodityUnitId,
            tc.commodity_state AS commodityState,
            txscps.commodity_purchase_status AS commodityPurchaseStatus,
            txscps.commodity_id AS commodityId,
            tc.commodity_package_spec AS commodityPackageSpec,
            tc.sales_box_capacity AS salesBoxCapacity,
            tc.xd_sales_box_capacity AS xdSalesBoxCapacity,
            tc.is_weight AS isWeight,
            tc.commodity_package_id AS commodityPackageId,
            concat(c.cate_name ,'/',c2.cate_name ,'/',c3.cate_name ) AS categoryName
        FROM t_xs_shop_commodity_purchase_status txscps
        INNER JOIN t_commodity tc ON tc.id = txscps.commodity_id
        INNER JOIN t_md_shop tms ON tms.id = txscps.shop_id
        LEFT JOIN t_category c ON c.id = tc.commodity_first_id
        LEFT JOIN t_category c2 ON c2.id = tc.commodity_second_id
        LEFT JOIN t_category c3 ON c3.id = tc.commodity_third_id
        <where>
            <if test="null != shopType">
                tms.shop_type = #{shopType}
            </if>
            <if test=" null != shopId">
                AND txscps.shop_id = #{shopId}
            </if>
            <if test="null != categoryId">
                AND ((tc.`commodity_first_id`=#{categoryId})
                OR (tc.`commodity_second_id`=#{categoryId})
                OR (tc.`commodity_third_id`=#{categoryId}))
            </if>
            <if test="null != isWeight">
                AND tc.is_weight = #{isWeight}
            </if>
            <if test=" null != commodityId ">
                AND tc.id = #{commodityId}
            </if>
            <if test=" null != barCode and barCode != '' ">
                AND tc.bar_code = #{barCode}
            </if>
            <if test=" null != commodityPurchaseStatus">
                AND txscps.commodity_purchase_status = #{commodityPurchaseStatus}
            </if>
            <if test=" null != commodityState">
                AND tc.commodity_state = #{commodityState}
            </if>
            <if test="null != shopIdCommodityList and shopIdCommodityList.size >0 ">
                AND CONCAT(txscps.shop_id ,'_',txscps.commodity_id) IN
                <foreach collection="shopIdCommodityList" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
        </where>
        ORDER BY tms.shop_code ASC ,tc.commodity_code ASC
    </select>

    <select id="selectShopCommodityPurchaseStatusListByShopIdAndCommodityId" resultType="String">
        SELECT
            t.shop_commodity
        FROM
        t_xs_shop_commodity_purchase_status t
        <where>
            <choose>
                <when test="null != shopIdAndCommodityIdList and shopIdAndCommodityIdList.size >0 ">
                    AND t.shop_commodity IN
                    <foreach collection="shopIdAndCommodityIdList" item="item" open="(" close=")" separator=",">
                        #{item}
                    </foreach>
                </when>
                <otherwise>
                    AND  1 = 2
                </otherwise>
            </choose>
        </where>
    </select>

    <update id="updateCommodityPurchaseStatusByShopIdAndCommodityId">
      UPDATE
          t_xs_shop_commodity_purchase_status t
      SET t.commodity_purchase_status = 1, t.update_time = NOW()
      <where>
        <choose>
            <when test="null != shopIdAndCommodityIdList and shopIdAndCommodityIdList.size >0 ">
                AND t.shop_commodity  IN
                <foreach collection="shopIdAndCommodityIdList" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                AND  1 = 2
            </otherwise>
        </choose>
      </where>
    </update>

    <!--门店id查询商品可采状态id集合-->
    <select id="findCommodityPurchaseStatusIdListByShopId" resultType="Long">
        SELECT id FROM t_xs_shop_commodity_purchase_status where shop_commodity =0 and shop_id =  #{shopId};
    </select>

    <!--期初shop_commodity 字段-->
    <update id="initShopCommodityById">
        update t_xs_shop_commodity_purchase_status t
        SET t.shop_commodity = CONCAT(t.shop_id,'_',t.commodity_id) where id = #{id};
    </update>
</mapper>