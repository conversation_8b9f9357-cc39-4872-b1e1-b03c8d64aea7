<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.pinshang.qingyun.order.mapper.XdReceiveDocLogMapper">



    <select id="getXdReceiveDocLogList" resultType="com.pinshang.qingyun.order.model.xd.XdReceiveDocLog">
          SELECT
              t.commodity_id commodityId,
              IFNULL(sum(normal_shares+abnormal_shares),0) normalShares
          from t_xd_receive_doc_log t
            where t.doc_id = #{docId}
        <if test=" barcode != null and barcode != '' ">
            AND t.commodity_id = (select cb.commodity_id from t_commodity_bar_code cb where cb.bar_code = #{barcode} )
        </if>
        GROUP BY t.commodity_id
    </select>




</mapper>