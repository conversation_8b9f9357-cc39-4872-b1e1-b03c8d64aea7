<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pinshang.qingyun.order.mapper.ProcessOrderCommodityStatisticsAdminMapper">

    <select id="queryToBOrderStatisticPage"
            resultType="com.pinshang.qingyun.order.vo.tob.AdminToBOrderStatisticRespVo">
        select
            toborder.order_time as orderTime,
            tc.commodity_name as commodityName,
            tc.commodity_code as commodityCode,
            toborder.order_quantity as orderQuantity,
            toborder.order_number as orderNumber,
            toborder.create_time as createTime,
            toborder.update_time as updateTime
        from t_dc_tob_process_order_commodity_statistics as toborder
        left join t_commodity as tc on toborder.commodity_id = tc.id
        <where>
            <if test="startOrderTime != null and startOrderTime != ''">
                <![CDATA[ AND toborder.order_time >= #{startOrderTime,jdbcType=VARCHAR} ]]>
            </if>
            <if test="endOrderTime != null and endOrderTime != ''">
                <![CDATA[ AND toborder.order_time <= #{endOrderTime,jdbcType=VARCHAR} ]]>
            </if>
            <if test="commodityId != null and commodityId != ''">
                AND toborder.commodity_id=#{commodityId}
            </if>
        </where>
        ORDER BY toborder.order_time desc
    </select>




    <insert id="batchInsert" parameterType="com.pinshang.qingyun.order.dto.tob.ToBOrderStatisticsRepairIDTO">
        INSERT INTO t_dc_tob_process_order_commodity_statistics
        (order_time, commodity_id, order_quantity, order_number, create_id, create_time, update_id, update_time)
        VALUES
        <foreach collection="list" separator="," item="item">
            (#{item.orderTime}, #{item.commodityId}, #{item.orderQuantity}, #{item.orderNumber},#{item.createId}, #{item.createTime}, #{item.updateId}, #{item.updateTime})
        </foreach>
    </insert>

    <update id="batchUpdate">
        <foreach collection="tobOrderStatisticsList" item="item" index="index" separator=";" open="" close="">
            UPDATE t_dc_tob_process_order_commodity_statistics
            SET
            order_quantity = #{item.orderQuantity},
            order_number = #{item.orderNumber},
            update_id = #{item.updateId},
            update_time = #{item.updateTime}
            where
            order_time = #{item.orderTime} and commodity_id = #{item.commodityId}
        </foreach>
    </update>



    <select id="queryOrderStatisticsList" resultType="com.pinshang.qingyun.order.dto.tob.ToBQueryOrderStatisticsODTO">
        select
            commodity_id as commodityId,
            order_time as orderTime,
            order_quantity as orderQuantity,
            order_number as orderNumber
        from t_dc_tob_process_order_commodity_statistics
        <where>
            <if test="startOrderTime != null and startOrderTime != ''">
                <![CDATA[ AND order_time >= #{startOrderTime,jdbcType=VARCHAR} ]]>
            </if>
            <if test="endOrderTime != null and endOrderTime != ''">
                <![CDATA[ AND order_time <= #{endOrderTime,jdbcType=VARCHAR} ]]>
            </if>
            <if test="commodityId != null and commodityId != ''">
                AND commodity_id=#{commodityId}
            </if>
        </where>
        order by id desc
    </select>
</mapper>