<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.pinshang.qingyun.order.mapper.PromotionMapper" >


    <select id="findCommodityPromotionByStoreId"
            resultType="com.pinshang.qingyun.order.model.promotion.Promotion">
        select
            p.*
        from
            `t_promotion` p ,
            `t_promotion_scope` ps ,
            `t_store_settlement` ss ,
            `t_product_price_model` ppm ,
            `t_store` s
        where 1=1
          and p.`id` = ps.`promotion_id`
          and ss.`product_price_model_id`  = ppm.`id`
          and ss.`store_id` = s.`id`
          and p.`status` = 0
          and #{orderTime} between p.`start_time` and p.`end_time`
          and (ps.`type_id` = ss.`settlement_customer_id` or ps.`type_id` = ppm.`id`  or ps.`type_id` = s.`id`)
          and s.`id` = #{storeId}
        order by p.`create_time` desc
    </select>
</mapper>