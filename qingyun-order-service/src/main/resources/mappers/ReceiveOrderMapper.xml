<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pinshang.qingyun.order.mapper.ShopReceiveOrderMapper">
  <resultMap id="BaseResultMap" type="com.pinshang.qingyun.order.model.shop.ShopReceiveOrder">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="sub_order_id" jdbcType="BIGINT" property="subOrderId" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="receive_time" jdbcType="TIMESTAMP" property="receiveTime" />
    <result column="receive_id" jdbcType="BIGINT" property="receiveId" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="enterprise_id" jdbcType="BIGINT" property="enterpriseId" />
    <result column="create_id" jdbcType="BIGINT" property="createId" />
    <result column="update_id" jdbcType="BIGINT" property="updateId" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <select id="getListByCondition" resultType="com.pinshang.qingyun.order.mapper.entry.shop.ShopReceiveEntry">
    SELECT
        ro.id,
        so.logistics_model,
        so.sub_order_code,
        so.total_price,
        o.order_amount,
        so.total_price AS subOrderTotalPrice,
        so.supplier_id,
        so.id subOrderId,
        ro.status,
        -- u.employee_name AS real_name,
        so.order_time,
        ro.receive_time,
        o.create_time,
        -- uu.employee_name create_name,
        -- m.shop_type,
        o.create_id,ro.receive_id receiveUserId,o.store_id,
        o.stall_id

    FROM
        t_md_receive_order ro
     JOIN t_sub_order so ON ro.sub_order_id = so.id
     JOIN t_order o ON so.order_id = o.id
      -- LEFT JOIN t_employee_user uu ON o.create_id = uu.user_id
      -- JOIN t_md_shop m on m.store_id = o.store_id
    -- LEFT JOIN t_employee_user u on ro.receive_id = u.user_id
    WHERE
      so.status != 2
      AND
      o.store_id = #{shopReceiveVo.storeId}
      AND ro.status !=4
    <if test="shopReceiveVo.subOrderCode !=null and shopReceiveVo.subOrderCode !='' ">
      and so.sub_order_code = #{shopReceiveVo.subOrderCode}
    </if>
   <!-- <if test="shopReceiveVo.supplierStr !=null and shopReceiveVo.supplierStr !='' ">
        and (s.supplier_name like concat('%',#{shopReceiveVo.supplierStr},'%') or s.supplier_code like concat('%',#{shopReceiveVo.supplierStr},'%') or s.supplier_aid like concat('%',#{shopReceiveVo.supplierStr},'%'))
    </if>-->
      <if test="shopReceiveVo.supplyIdList !=null">
          AND po.supplier_id in
          <foreach collection="shopReceiveVo.supplyIdList" item = "item" open="(" separator="," close=")">
              #{item}
          </foreach>
      </if>
    <if test="shopReceiveVo.status !=null ">
        AND   ro.status in
        <foreach collection="shopReceiveVo.status" item = "item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </if>
    <if test="shopReceiveVo.logisticsModel !=null ">
      and so.logistics_model = #{shopReceiveVo.logisticsModel}
    </if>
      <if test="shopReceiveVo.enterpriseId !=null and shopReceiveVo.enterpriseId !='' ">
          and so.enterprise_id = #{shopReceiveVo.enterpriseId}
      </if>
    <if test="shopReceiveVo.beginDate !=null and shopReceiveVo.endDate != '' and shopReceiveVo.beginDate != null and shopReceiveVo.endDate != '' ">
      and o.order_time BETWEEN #{shopReceiveVo.beginDate} and #{shopReceiveVo.endDate}
    </if>
      <if test="!shopReceiveVo.isInternal and (shopReceiveVo.consignmentId == null or shopReceiveVo.consignmentId == 0 )">
          and o.consignment_id = -1
      </if>
      <if test="!shopReceiveVo.isInternal and shopReceiveVo.consignmentId != null and shopReceiveVo.consignmentId > 0 ">
          and o.consignment_id = #{shopReceiveVo.consignmentId}
      </if>
      <if test="shopReceiveVo.stallId != null">
          and o.stall_id = #{shopReceiveVo.stallId}
      </if>
      <if test="shopReceiveVo.stallIdList != null and shopReceiveVo.stallIdList.size > 0">
          AND o.stall_id IN
          <foreach collection="shopReceiveVo.stallIdList" index="index" item="stallid" open="(" separator="," close=")">
              #{stallid}
          </foreach>
      </if>
      ORDER BY id DESC
  </select>


    <select id = "getShopReceiveOrderEntryBySubOrderCode" resultType="com.pinshang.qingyun.order.mapper.entry.shop.ShopReceiveOrderEntry" >
        SELECT
            so.id,
            so.order_time,
            so.sub_order_code,
            so.supplier_id as supplierId ,
            ro.receive_time,
            ro.receive_id,
            ro.status,
            ro.remark,
            so.logistics_model,
           --  u.employee_name as receiveName,
          --  p.id as shopId,
          --  p.shop_name,
            ro.receive_id receiveUserId,o.store_id
        FROM
            t_sub_order so
         JOIN t_md_receive_order ro ON so.id = ro.sub_order_id
        -- LEFT JOIN t_employee_user u on u.user_id = ro.receive_id
         JOIN t_order o on o.id = so.order_id
       --  JOIN t_md_shop p on p.store_id = o.store_id
        where so.sub_order_code = #{subOrderCode}

    </select>

    <select id = "getShopReceiveOrderEntryBySubOrderId" resultType="com.pinshang.qingyun.order.mapper.entry.shop.ShopReceiveOrderEntry" >
        SELECT
        so.id,
        so.order_time,
        so.sub_order_code,
        so.supplier_id as supplierId ,
        ro.receive_time,
        ro.receive_id,
        ro.status,
        ro.remark,
        so.logistics_model,
        -- u.employee_name as receiveName,
        -- p.id as shopId,
        -- p.shop_name
        ro.receive_id receiveUserId,o.store_id
        FROM
        t_sub_order so
        JOIN t_md_receive_order ro ON so.id = ro.sub_order_id
        -- LEFT JOIN t_employee_user u on u.user_id = ro.receive_id
        JOIN t_order o on o.id = so.order_id
        -- JOIN t_md_shop p on p.store_id = o.store_id
        where so.id = #{subOrderId}

    </select>

    <select id = "getShopReceiveOrderItemEntryBySubOrderId" resultType="com.pinshang.qingyun.order.mapper.entry.shop.ShopReceiveOrderItemEntry">
        SELECT
            soi.commodity_id as commodityId,
            -- c.commodity_code,
            -- c.commodity_name,
            -- c.commodity_spec,
            -- d.option_name as unit,
            soi.price,
            soi.id as item_id,
            soi.quantity,
            soi.status,
            soi.real_receive_quantity,
            soi.real_delivery_quantity,
            soi.reject_reason,
            (SELECT DISTINCT tol.remark from t_order_list tol where tso.order_id = tol.order_id and soi.commodity_id = tol.commodity_id and soi.price = tol.commodity_price and soi.quantity = tol.commodity_num and tol.type = soi.type)  as remark
            -- IFNULL(c.commodity_package_spec,1) commodityPackageSpec,
            -- c.commodity_package_kind,
            -- tt.barCodes
            -- c.is_weight
        FROM t_sub_order_item soi
         left join t_sub_order tso on soi.sub_order_id = tso.id
         -- JOIN t_commodity c ON soi.commodity_id = c.id
         -- JOIN t_dictionary d ON c.commodity_unit_id = d.id
        /* LEFT JOIN (
               SELECT
                    t.commodity_id commodityId,
                    GROUP_CONCAT(t.bar_code ORDER BY t.default_state desc) barCodes
                from t_commodity_bar_code t
                where 1=1
                GROUP BY t.commodity_id
        ) tt on tt.commodityId = c.id*/
        WHERE
            soi.sub_order_id = #{subOrderId}
    </select>

    <select id = "getStockOutQuantityByCommodityIds" parameterType="com.pinshang.qingyun.order.vo.shop.StockOutQuantityVo" resultType="com.pinshang.qingyun.order.mapper.entry.shop.CommodityStockOutQuantityEntry">
        SELECT
            soi.commodity_id,
            soi.real_delivery_quantity as stockOutQuantity
        FROM
        t_sub_order_item soi
        INNER JOIN t_sub_order so ON so.id = soi.sub_order_id
        WHERE
        so.id = #{subOrderId}
    </select>


    <select id="getAuditList" resultType="com.pinshang.qingyun.order.mapper.entry.shop.AuditInfoEntry">
		 select
		        po.id,
		        po.logistics_model,
		        po.order_code as subOrderCode,
		        po.total_price,
		        po.supplier_id,
		        ms.shop_name,
		        po.order_status,
		        po.receive_status as status,
		        po.order_time,
		        po.create_time,
		        po.receiver_time as receive_time,
		        u.employee_name as operateName,
			po.`approving_time`,
			ap.`employee_name` approving_name,
			po.`real_order_code`,
            po.stall_id
		from
		t_md_preorder po
		inner join t_md_shop ms on po.store_id = ms.store_id
		inner join t_employee_user u on u.user_id = po.update_id
		left  join t_employee_user ap on ap.`user_id` = po.`approving_id`
		where 1=1
        <if test="auditQueryVo.subOrderCode !=null and auditQueryVo.subOrderCode !='' ">
            and po.order_code = #{auditQueryVo.subOrderCode}
        </if>
        <!--<if test="auditQueryVo.supplierStr !=null and auditQueryVo.supplierStr !='' ">
	        and (s.supplier_name like concat('%',#{auditQueryVo.supplierStr},'%') or s.supplier_code like concat('%',#{auditQueryVo.supplierStr},'%') or s.supplier_aid like concat('%',#{auditQueryVo.supplierStr},'%'))
	    </if>-->
        <if test="auditQueryVo.supplyIdList !=null">
            AND po.supplier_id in
            <foreach collection="auditQueryVo.supplyIdList" item = "item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="auditQueryVo.orderStatus !=null">
            AND po.order_status in
            <foreach collection="auditQueryVo.orderStatus" item = "item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="auditQueryVo.orderStatus ==null">
            AND po.order_status in (1,2)
        </if>
        AND po.logistics_model = 0
        <if test="auditQueryVo.supplierId !=null and auditQueryVo.supplierId !='' ">
            and po.supplier_id= #{auditQueryVo.supplierId}
        </if>
        <if test="auditQueryVo.shopStr !=null and auditQueryVo.shopStr !='' ">
            and (ms.shop_name like concat('%',#{auditQueryVo.shopStr},'%') or ms.shop_code like concat('%',#{auditQueryVo.shopStr},'%') or ms.shop_aid like concat('%',#{auditQueryVo.shopStr},'%'))
        </if>
        <if test="auditQueryVo.status !=null ">
            AND   po.receive_status = #{auditQueryVo.status}
        </if>
        <if test="auditQueryVo.enterpriseId !=null and auditQueryVo.enterpriseId !='' ">
            and po.enterprise_id = #{auditQueryVo.enterpriseId}
        </if>
        <if test="auditQueryVo.beginDate !=null and auditQueryVo.endDate != '' and auditQueryVo.beginDate != null and auditQueryVo.endDate != '' ">
            and po.order_time BETWEEN #{auditQueryVo.beginDate} and #{auditQueryVo.endDate}
        </if>
        <if test="auditQueryVo.stallId != null">
            and po.stall_id = #{auditQueryVo.stallId}
        </if>
        <if test="auditQueryVo.stallIdList != null and auditQueryVo.stallIdList.size > 0">
            AND po.stall_id IN
            <foreach collection="auditQueryVo.stallIdList" index="index" item="stallid" open="(" separator="," close=")">
                #{stallid}
            </foreach>
        </if>
        ORDER BY id DESC

    </select>

    <update id="updateReceiveOrder" >
        update t_md_receive_order ro
				set ro.status = #{status}
                   ,ro.receive_time = #{receiveTime}
                   ,ro.receive_id = #{receiveId}
                   ,ro.remark = #{remark}
					where
                   ro.sub_order_id = #{subOrderId}
                   and ro.status =0
    </update>

    <select id="getPreOrderListByCondition" resultType="com.pinshang.qingyun.order.mapper.entry.shop.ShopReceiveEntry">
        SELECT
            po.id,
            po.logistics_model,
            po.order_code as subOrderCode,
            po.total_price,
            po.supplier_id,
            po.receive_status as status,
            po.order_time,
            po.create_time,
            -- u.employee_name create_name,
            po.receiver_id receiveUserId,
            po.create_id createId,
            po.stall_id
        FROM
        t_md_preorder po
        -- LEFT JOIN t_employee_user u ON po.create_id = u.user_id
        WHERE 1=1
        <if test="shopReceiveVo.subOrderCode !=null and shopReceiveVo.subOrderCode !='' ">
            and po.order_code = #{shopReceiveVo.subOrderCode}
        </if>
        AND po.order_status in (1,2)
        AND po.logistics_model = 0
        AND po.store_id = #{shopReceiveVo.storeId}
        <if test="shopReceiveVo.supplierId !=null and shopReceiveVo.supplierId !='' ">
            and po.supplier_id = #{shopReceiveVo.supplierId}
        </if>
        <if test="shopReceiveVo.status !=null ">
            AND   po.receive_status in
            <foreach collection="shopReceiveVo.status" item = "item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="shopReceiveVo.enterpriseId !=null and shopReceiveVo.enterpriseId !='' ">
            and po.enterprise_id = #{shopReceiveVo.enterpriseId}
        </if>
        <if test="shopReceiveVo.beginDate !=null and shopReceiveVo.endDate != '' and shopReceiveVo.beginDate != null and shopReceiveVo.endDate != '' ">
            and po.order_time BETWEEN #{shopReceiveVo.beginDate} and #{shopReceiveVo.endDate}
        </if>
        <if test="shopReceiveVo.stallId != null">
            and po.stall_id = #{shopReceiveVo.stallId}
        </if>
        <if test="shopReceiveVo.stallIdList != null and shopReceiveVo.stallIdList.size > 0">
            AND po.stall_id IN
            <foreach collection="shopReceiveVo.stallIdList" index="index" item="stallid" open="(" separator="," close=")">
                #{stallid}
            </foreach>
        </if>
        ORDER BY id DESC
    </select>

    <!--总部-->
    <select id="getPreOrderHQListByCondition" resultType="com.pinshang.qingyun.order.mapper.entry.shop.ShopReceiveHQEntry">
        SELECT
        po.id,
        tms.shop_code shopCode,
        tms.shop_name AS shopName,
        po.logistics_model,
        po.order_code as subOrderCode,
        po.real_order_code orderCode,
        po.total_price,
        po.supplier_id,
        po.receive_status as status,
        po.order_time,
        po.create_time,
        u.employee_name create_name
        FROM
        t_md_preorder po
        LEFT JOIN t_employee_user u ON po.create_id = u.user_id
        LEFT JOIN t_md_shop tms ON tms.store_id = po.store_id
        <if test="shopReceiveHQVo.isReplenishment != null and shopReceiveHQVo.isReplenishment == 1">
            INNER JOIN t_order t on t.id=po.order_id AND t.mode_type=2
        </if>
        WHERE 1=1
        <if test="shopReceiveHQVo.shopId != null and shopReceiveHQVo.shopId != ''">
            AND tms.id = #{shopReceiveHQVo.shopId}
        </if>
        <if test="shopReceiveHQVo.subOrderCode !=null and shopReceiveHQVo.subOrderCode !='' ">
            and po.order_code = #{shopReceiveHQVo.subOrderCode}
        </if>
        <if test="shopReceiveHQVo.createUser !=null and shopReceiveHQVo.createUser !='' ">
            and (u.employee_name like concat('%',#{shopReceiveHQVo.createUser},'%') or u.employee_code like concat('%',#{shopReceiveHQVo.createUser},'%'))
        </if>
        <if test="shopReceiveHQVo.orderCode !=null and shopReceiveHQVo.orderCode !='' ">
            and po.real_order_code = #{shopReceiveHQVo.orderCode}
        </if>
        AND po.order_status in (1,2)
        AND po.logistics_model = 0
        <if test="shopReceiveHQVo.supplierId !=null and shopReceiveHQVo.supplierId !='' ">
            and po.supplier_id = #{shopReceiveHQVo.supplierId}
        </if>
        <if test="shopReceiveHQVo.status !=null ">
            AND   po.receive_status in
            <foreach collection="shopReceiveHQVo.status" item = "item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="shopReceiveHQVo.enterpriseId !=null and shopReceiveHQVo.enterpriseId !='' ">
            and po.enterprise_id = #{shopReceiveHQVo.enterpriseId}
        </if>
        <if test="shopReceiveHQVo.beginDate !=null and shopReceiveHQVo.endDate != '' and shopReceiveHQVo.beginDate != null and shopReceiveHQVo.endDate != '' ">
            and po.order_time BETWEEN #{shopReceiveHQVo.beginDate} and #{shopReceiveHQVo.endDate}
        </if>
        ORDER BY id DESC
    </select>

    <select id="getPreOrderByCode" resultType="com.pinshang.qingyun.order.mapper.entry.shop.ShopReceiveOrderEntry">
        SELECT
            po.id,
            po.order_code as subOrderCode,
            (case po.logistics_model when 0 then '直送' end) as logisticsModel,
            po.supplier_id,
            po.receive_status as status,
            po.order_time,
            po.create_time,
            -- u.employee_name as receiveName,
            CONCAT(u.employee_code,'_',u.employee_name) as receiveName,
            po.receiver_time as receiveTime,
            po.remark,
            ms.shop_name,
            po.store_id,
            po.receiver_id as receive_id
        FROM
        t_md_preorder po
        INNER JOIN t_md_shop ms ON po.store_id = ms.store_id
        left JOIN t_employee_user u ON u.user_id = po.receiver_id
        WHERE
        po.logistics_model = 0
        AND
        po.order_code = #{orderCode}
    </select>

    <select id="getPreOrderItemByCode" resultType="com.pinshang.qingyun.order.mapper.entry.shop.ShopReceiveOrderItemEntry">
        select
        pi.id item_id,
		c.`id` commodityId,
		c.`commodity_code`,
		c.`commodity_name`,
		c.`commodity_spec`,
		d.`option_name` unit,
		pi.price as price,
		pi.require_quantity as quantity,
		pi.real_receive_quantity,
		pi.real_receive_number,
		pi.total_price,
		pi.status,
		pi.reject_reason,
		IFNULL(c.commodity_package_spec,1) commodityPackageSpec
		from t_md_preorder_item pi
		INNER JOIN t_commodity c on c.id = pi.commodity_id
		left  join `t_dictionary` d on d.`id` = c.`commodity_unit_id`
		where 1=1
		and pi.preorder_id = #{preorderId}
    </select>
    <select id="getAutoReceiveOrder" resultType="com.pinshang.qingyun.order.vo.shop.ReceiveOrderVo">
        SELECT DISTINCT
        shop.store_id,
        shop.id as shop_id,
        so.id AS sub_order_id,
        so.sub_order_code,
        so.logistics_model
        FROM
        t_md_shop AS shop
        INNER JOIN t_order AS o ON o.store_id = shop.store_id
        INNER JOIN t_sub_order AS so ON so.order_id = o.id
        INNER JOIN t_md_receive_order AS ro ON ro.sub_order_id = so.id
        INNER JOIN t_sub_order_item soi ON soi.sub_order_id = so.id and soi.real_delivery_quantity > 0
        WHERE
        o.order_time = #{date,jdbcType=DATE}
        and so.logistics_model in
        <foreach collection="logisticsModels" item = "item" open="(" separator="," close=")">
            #{item}
        </foreach>
        and  shop.id in
        <foreach collection="shopIdList" index="index" item="shopId" open="(" separator="," close=")">
            #{shopId}
        </foreach>
        AND ro.status = 0
        AND so.status = 1
    </select>

    <select id="getAutoReceiveOrderNew" resultType="com.pinshang.qingyun.order.vo.shop.ReceiveOrderVo">
        SELECT DISTINCT
            o.store_id,
            so.id AS sub_order_id,
            so.sub_order_code,
            so.logistics_model
        FROM
          t_order AS o
        INNER JOIN t_sub_order AS so ON so.order_id = o.id
        INNER JOIN t_md_receive_order AS ro ON ro.sub_order_id = so.id
        INNER JOIN t_sub_order_item soi ON soi.sub_order_id = so.id and soi.real_delivery_quantity >= 0
        WHERE o.real_order_time between #{beginTime} and #{orderTime}
        and DATE_FORMAT(now(),'%Y-%m-%d') <![CDATA[ >= ]]> o.order_time
        and so.logistics_model in
        <foreach collection="logisticsModels" item = "item" open="(" separator="," close=")">
            #{item}
        </foreach>
        and  o.store_id in
        <foreach collection="storeIdList" index="index" item="storeId" open="(" separator="," close=")">
            #{storeId}
        </foreach>
        <if test = "xsjmStoreIdList != null and xsjmStoreIdList.size > 0">
            AND o.store_id not IN
            <foreach collection="xsjmStoreIdList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        AND ro.status = 0
        AND so.status = 1
        and (o.stall_id is null or o.stall_id = -1)
    </select>

    <select id="getAutoReceiveOrderBySubOrderIdList" resultType="com.pinshang.qingyun.order.vo.shop.ReceiveOrderVo">
        SELECT
            DISTINCT
            o.store_id,
            so.id AS sub_order_id,
            so.sub_order_code,
            so.logistics_model
        FROM
            t_sub_order AS so
        INNER JOIN t_order AS o ON so.order_id = o.id
        INNER JOIN t_md_receive_order AS ro ON ro.sub_order_id = so.id
        INNER JOIN t_sub_order_item soi ON soi.sub_order_id = so.id and soi.real_delivery_quantity >= 0
        WHERE DATE_FORMAT(now(),'%Y-%m-%d')  >=  o.order_time
          and so.logistics_model in(1,2)
           and so.id in
            <foreach collection="subOrderIdList" index="index" item="subOrderId" open="(" separator="," close=")">
                #{subOrderId}
            </foreach>
          AND ro.status = 0
          AND so.status = 1
          and o.store_id not in (
            SELECT md.store_id
            from t_md_shop_receive_setting tt
               LEFT JOIN t_md_shop md on md.id = tt.shop_id
            where tt.receive_type = 1
          )

        <if test = "xsjmStoreIdList != null and xsjmStoreIdList.size > 0">
            AND o.store_id not IN
            <foreach collection="xsjmStoreIdList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        and (o.stall_id is null or o.stall_id = -1)
    </select>


    <select id="getAutoReceiveXsjmOrder" resultType="com.pinshang.qingyun.order.vo.shop.ReceiveOrderVo">
        SELECT DISTINCT
            o.store_id,
            so.id AS sub_order_id,
            so.sub_order_code,
            so.logistics_model
        FROM
        t_order AS o
        INNER JOIN t_sub_order AS so ON so.order_id = o.id
        INNER JOIN t_md_receive_order AS ro ON ro.sub_order_id = so.id
        INNER JOIN t_sub_order_item soi ON soi.sub_order_id = so.id and soi.real_delivery_quantity >= 0
        WHERE 1 = 1
        and o.order_time = #{orderTime}
        and so.logistics_model in
        <foreach collection="logisticsModels" item = "item" open="(" separator="," close=")">
            #{item}
        </foreach>
        and  o.store_id in
        <foreach collection="storeIdList" index="index" item="storeId" open="(" separator="," close=")">
            #{storeId}
        </foreach>
        AND ro.status = 0
        AND so.status = 1
        and (o.stall_id is null or o.stall_id = -1)
    </select>

    <select id="getAutoDeliveryQuantity" resultType="com.pinshang.qingyun.order.vo.shop.Quantity">
        select
          id itemId,
          commodity_id,
          price,
          real_delivery_quantity,
          real_receive_quantity,
          quantity as requireQuantity
        from t_sub_order_item
        where sub_order_id = #{subOrderId}
    </select>



    <select id="getHandleAutoReceiveOrder" resultType="com.pinshang.qingyun.order.vo.shop.ReceiveOrderVo">
        SELECT DISTINCT
            o.store_id,
            so.id AS sub_order_id,
            so.sub_order_code,
            so.logistics_model,
            o.order_code
        FROM
        t_order AS o
        INNER JOIN t_sub_order AS so ON so.order_id = o.id
        INNER JOIN t_md_receive_order AS ro ON ro.sub_order_id = so.id
        INNER JOIN t_sub_order_item soi ON soi.sub_order_id = so.id and soi.real_delivery_quantity > 0
        WHERE
        o.order_time = #{orderTime}
        and so.logistics_model in
        <foreach collection="logisticsModels" item = "item" open="(" separator="," close=")">
            #{item}
        </foreach>
        and  o.store_id in
        <foreach collection="storeIdList" index="index" item="storeId" open="(" separator="," close=")">
            #{storeId}
        </foreach>
        AND ro.status = 0
        AND so.status = 1
        and (soi.real_receive_quantity is null or soi.real_delivery_quantity > soi.real_receive_quantity )
    </select>

    <select id="getHandleAutoDeliveryQuantity" resultType="com.pinshang.qingyun.order.vo.shop.Quantity">
        select
            commodity_id,
            price,
            real_delivery_quantity,
            real_receive_quantity,
            quantity as requireQuantity
        from t_sub_order_item
        where sub_order_id = #{subOrderId}
        and (real_receive_quantity is null or real_delivery_quantity > real_receive_quantity )
    </select>

    <insert id="batchInsertReceiveOrderMessage" parameterType="List" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO t_md_receive_order_message
        (shop_id, employee_id, employee_code, employee_name, employee_phone, business_type, `status`, content, message_type, create_id, create_time)
        VALUES
        <foreach collection="list" item="item" index="index" separator=",">
            (#{item.shopId}, #{item.employeeId}, #{item.employeeCode}, #{item.employeeName},
            #{item.employeePhone}, #{item.businessType}, #{item.status},#{item.content},
             #{item.messageType},#{item.createId}, #{item.createTime})
        </foreach>
    </insert>


    <select id="getReceiveOrderMessagePage" resultType="com.pinshang.qingyun.order.dto.ReceiveOrderMessageODTO" parameterType="com.pinshang.qingyun.order.dto.ReceiveOrderMessageIDTO">
        SELECT
            md.shop_code,
            md.shop_name,
            tm.employee_name,
            tm.employee_phone,
            tm.business_type,
            tm.status,
            tm.content,
            tm.create_time
        FROM
          t_md_receive_order_message tm
           left join  t_md_shop md on md.id = tm.shop_id
        WHERE tm.message_type = #{messageType}
        <if test="shopCode != null and shopCode != '' ">
            and md.shop_code like concat('%',#{shopCode},'%')
        </if>
        <if test="shopName != null and shopName != '' ">
            and md.shop_name like concat('%',#{shopName},'%')
        </if>
        <if test="shopId != null ">
            and tm.shop_id = #{shopId}
        </if>
        <if test="employeeName != null and employeeName != '' ">
            and tm.employee_name like concat('%',#{employeeName},'%')
        </if>
        <if test="employeePhone != null and employeePhone != '' ">
            and tm.employee_phone like concat('%',#{employeePhone},'%')
        </if>
        <if test="businessType != null ">
            and tm.business_type = #{businessType}
        </if>
        <if test="beginDate != null and beginDate != '' and endDate != null and endDate != '' ">
            and tm.create_time BETWEEN #{beginDate} and #{endDate}
        </if>
        ORDER BY tm.create_time DESC
    </select>

    <select id="queryTjPreOrderList" parameterType="com.pinshang.qingyun.order.vo.shop.TjPreOrderVO"
            resultType="com.pinshang.qingyun.order.mapper.entry.shop.TjPreOrderEntry" >
        SELECT
		    po.id AS orderId,po.order_code,po.order_time,po.store_id,s.store_code,s.store_name,
	        poi.commodity_id,c.commodity_code,c.commodity_spec,c.commodity_name,poi.require_quantity
		FROM
		    t_md_preorder po
        INNER JOIN t_store s on po.store_id = s.id
        INNER JOIN t_md_preorder_item poi ON po.id = poi.preorder_id
        INNER JOIN t_commodity c ON poi.commodity_id = c.id
        <where>
            <if test="orderTime != null ">
                and po.order_time = #{orderTime}
            </if>
            <if test="storeIds != null and storeIds.size() > 0">
                AND s.id IN
                <foreach collection="storeIds" separator="," item="item" open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="commodityIds != null and commodityIds.size() > 0">
                AND c.id IN
                <foreach collection="commodityIds" separator="," item="item" open="(" close=")">
                    #{item}
                </foreach>
            </if>
            AND po.order_status in (2)
            AND po.logistics_model = 0
        </where>
    </select>
</mapper>