<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.pinshang.qingyun.order.mapper.auto.ShopAutoOrderMapper" >

    <select id="queryAutoShopList" resultType="com.pinshang.qingyun.order.dto.AutoShopCommodityODTO">
        SELECT
            mas.shop_id,
            md.store_id
        FROM  t_md_auto_setting mas
            left join  t_md_shop md on md.id = mas.shop_id
        where mas.status = 1 and md.management_mode != 3
    </select>

    <select id="queryAutoShopCommodityList" resultType="com.pinshang.qingyun.order.dto.AutoShopCommodityODTO">
        SELECT
            masc.commodity_id,
            masc.stock_quantity safeQuantity,
            tc.commodity_is_quick_freeze,
            case
                when md.shop_type = 5 or md.management_mode = 3  then tc.`xd_sales_box_capacity`
                when md.shop_type != 5  then tc.`sales_box_capacity`
                end as salesBoxCapacity,
            tc.commodity_package_spec,
            tc.commodity_code,
            tc.commodity_name
        FROM  t_md_auto_shop_commodity masc
         left join  t_md_shop md on md.id = masc.shop_id
         left join t_commodity tc on tc.id = masc.commodity_id
         where masc.shop_id = #{shopId}
    </select>


    <select id="queryAutoOrderLogCommodityIds" resultType="java.lang.Long">
        SELECT
            distinct t.commodity_id
        FROM  t_md_auto_order_log t
        where t.create_time between  #{beginDate} and #{endDate}
        and t.store_id =  #{storeId}
    </select>

    <insert id="batchInsertAutoOrderLog" >
        INSERT INTO t_md_auto_order_log (store_id, commodity_id, quantity, create_id, create_time)
        VALUES
        <foreach collection="autoOrderLogList" item="item" index="index" separator=",">
            (#{item.storeId}, #{item.commodityId}, #{item.quantity},#{item.createId}, #{item.createTime})
        </foreach>
    </insert>
</mapper>