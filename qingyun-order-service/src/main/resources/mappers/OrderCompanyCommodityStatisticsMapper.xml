<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.pinshang.qingyun.order.mapper.OrderCompanyCommodityStatisticsMapper">
    <select id="findListByCondition" resultType="com.pinshang.qingyun.order.mapper.entry.orderStatistics.OrderCompanyCommodityStatisticsEntry"
            parameterType="com.pinshang.qingyun.order.vo.orderStatistics.OrderCompanyCommodityStatisticsVo">
       select co.company_name, f.factory_name,c.commodity_code,c.commodity_name,d.option_name as unitName,
        <if test="showType==1">
            ocs.order_quantity,ocs.order_total_amount,ocs.real_quantity,ocs.real_total_amount,ocs.order_time as showOrderTime
        </if>
        <if test="showType==2">
            sum(ocs.order_quantity) as orderQuantity,sum(ocs.order_total_amount) as orderTotalAmount,
            sum(ocs.real_quantity) as realQuantity,sum(ocs.real_total_amount) as realTotalAmount,'' as showOrderTime
        </if>
       from  t_order_company_commodity_statistics ocs
       inner join  t_commodity c on  ocs.commodity_id = c.id
       inner join  t_factory f on f.id = ocs.factory_id
       inner join  t_dictionary d on d.id = c.commodity_unit_id
       inner join t_company co on co.id = ocs.company_id
       <where>
           <if test="startOrderTime!=null">
               <![CDATA[ AND ocs.order_time >= #{startOrderTime} ]]>
           </if>
           <if test="endOrderTime!=null">
               <![CDATA[ AND ocs.order_time <= #{endOrderTime} ]]>
           </if>
           <if test="commodityId!=null">
               <![CDATA[ AND ocs.commodity_id = #{commodityId} ]]>
           </if>
           <if test="companyId!=null">
               <![CDATA[ AND ocs.company_id = #{companyId} ]]>
           </if>
           <if test="factoryId!=null">
               <![CDATA[ AND ocs.factory_id = #{factoryId} ]]>
           </if>
       </where>
        <if test="showType==2">
            group by ocs.company_id,ocs.commodity_id
        </if>
    </select>

    <select id="queryOrderCommodityStatisticsByOrderTime" resultType="com.pinshang.qingyun.order.model.statistics.OrderCompanyCommodityStatistics">
        SELECT
            o.order_time,
            co.id AS company_id,
            c.id AS commodityId,
            c.commodity_factory_id as factoryId,
            sum(g.commodity_num) AS order_quantity,
            sum(g.total_price) AS order_total_amount,
            IFNULL(sum(g.real_quantity),0) AS real_quantity,
            IFNULL(sum(g.real_total_price),0)  AS real_total_amount
        FROM
            t_order o
        INNER JOIN t_order_list_gift g ON g.order_id = o.id
        INNER JOIN t_commodity c ON c.id = g.commodity_id
        INNER JOIN t_store s ON o.store_id = s.id
        INNER JOIN t_store_company sc ON sc.store_id = s.id
        INNER JOIN t_company co ON co.id = sc.company_id
        WHERE
            o.order_time = #{orderTime}
        AND o.order_status = 0
        GROUP BY co.id,c.id
    </select>

</mapper>