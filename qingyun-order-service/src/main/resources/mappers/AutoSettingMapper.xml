<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.pinshang.qingyun.order.mapper.auto.AutoSettingMapper">

    <update id = "updateHeadItems">
       UPDATE t_md_auto_setting
       SET head_items = #{headItems}
    </update>

    <select id = "autoSettingPage" resultType = "com.pinshang.qingyun.order.vo.auto.AutoSettingPageVO"
    parameterType="com.pinshang.qingyun.order.vo.auto.AutoSettingRequestVO">
        SELECT ms.id AS shopId,
               ms.shop_code,
               ms.shop_name,
               ms.shop_status,
               IFNULL(mas.`status`, 0) AS status,
               IFNULL(mas.head_items, 0) AS headItems,
               IFNULL(mas.set_items, 0) AS setItems,
               IFNULL(mas.last_month_sale, 0) AS lastMonthSale,
               mas.last_month_sale_info
        FROM t_md_shop ms
        LEFT JOIN t_md_auto_setting mas ON ms.id = mas.shop_id
        <where>
            <if test = "vo.shopId != null and vo.shopId != ''">
                AND ms.id = #{vo.shopId}
            </if>
            <if test = "vo.shopIds != null and vo.shopIds.size > 0">
                AND ms.id IN
                <foreach collection="vo.shopIds" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <if test = "vo.shopStatus != null">
                AND ms.shop_status = #{vo.shopStatus}
            </if>
            <if test = "vo.status != null and vo.status == 1">
                AND mas.status = #{vo.status}
            </if>
            <if test = "vo.status != null and vo.status == 0">
                AND (mas.status =0 OR mas.status is null)
            </if>
        </where>
    </select>

</mapper>