<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.pinshang.qingyun.order.mapper.QuickGoodsAdminMapper">

	<select id="listGroup" resultType="com.pinshang.qingyun.order.model.order.QuickGoodsAdmin">
		SELECT g.id,g.store_id,g.commodity_id,
		       sum(g.quantity) AS quantity,
			   g.order_time,
			   g.delivery_batch,
			   g.stall_id
		FROM t_md_quick_goods_admin g
		WHERE g.pass = #{pass} and g.create_id = #{userId}
		<if test="bigShop">
			and g.stall_id > 0
		</if>
		<if test="!bigShop">
			and (g.stall_id = -1 or  g.stall_id is null )
		</if>

		<if test="bigShop">
			GROUP BY g.store_id,g.stall_id, g.commodity_id,g.order_time,g.delivery_batch
		</if>
		<if test="!bigShop">
			GROUP BY g.store_id, g.commodity_id,g.order_time,g.delivery_batch
		</if>

	</select>
</mapper>