<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.pinshang.qingyun.order.mapper.OrderReportMapper">

	<select id="findReturnOrderDetails" parameterType="com.pinshang.qingyun.order.vo.shop.ReturnOrderDetailsReportVo" resultType="com.pinshang.qingyun.order.mapper.entry.shop.ReturnOrderDetailsReportEntry">
		SELECT
		sro.create_time,
		sro.order_code,
		sro.stall_id,
		c.commodity_first_id,
		-- cate.cate_name,
		c.bar_code,
		-- cbc.bar_codes,
		-- ms.shop_name,
		c.commodity_code,
		c.commodity_name,
		c.commodity_spec,
		sroi.price,
		sroi.return_quantity,
		sroi.total_price,
		sroi.real_return_quantity,
		sroi.return_reason,
		-- ddd.option_name AS returnReasonName,
		sroi.remark,
		-- d.option_name as commodityUnit,
		sro.store_id,c.commodity_unit_id,c.commodity_first_id,sroi.return_reason,sroi.commodity_id
		FROM
		t_sale_return_order_item AS sroi
		INNER JOIN t_sale_return_order AS sro ON sro.id = sroi.sale_return_order_id
		-- LEFT JOIN t_md_shop AS ms ON ms.store_id = sro.store_id
		LEFT JOIN t_commodity AS c ON sroi.commodity_id = c.id
		-- LEFT JOIN t_dictionary d ON d.id = c.commodity_unit_id
		-- LEFT JOIN t_category AS cate ON c.commodity_first_id = cate.id
		/*LEFT JOIN (
			SELECT
				d.option_code,
				d.option_name
			FROM
			t_dictionary d
			WHERE
			d.dictionary_id = (
				SELECT
				dd.id
				FROM
				t_dictionary dd
				WHERE
				dd.option_name = '门店退货原因'
			)
		) ddd on ddd.option_code=sroi.return_reason*/
		/*LEFT JOIN (
			SELECT
				GROUP_CONCAT(bar_code ORDER BY default_state desc) AS bar_codes,
				commodity_id
			FROM
			t_commodity_bar_code
			GROUP BY
			commodity_id
		) AS cbc ON cbc.commodity_id = c.id*/
		WHERE
		sro.`status` != 0
		<if test="shopId != null and shopId != 0">
		    and sro.store_id =  (select md.store_id from t_md_shop md where md.id = #{shopId} limit 1)
		</if>
		<if test="orderCode != null and orderCode != ''">
			and sro.order_code = #{orderCode}
		</if>
		<if test="commodityFirstId != null">
			and ((c.`commodity_first_id`=#{commodityFirstId}) or (c.`commodity_second_id`=#{commodityFirstId}) or (c.`commodity_third_id`=#{commodityFirstId}))
		</if>
		<if test="searchWord != null and searchWord != ''">
			and
			(
			c.commodity_code like  concat('%',#{searchWord},'%')
			or
			c.commodity_name like  concat('%',#{searchWord},'%')
			or
			c.commodity_aid like  concat('%',#{searchWord},'%')
			)
		</if>
		<if test="barCode !=null and barCode !=''">
			and c.id = (
				SELECT
				commodity_id AS id
				FROM
				t_commodity_bar_code
				WHERE
				bar_code = #{barCode}
			)
		</if>
		<if test="orderTimeStartStr != null and orderTimeStartStr != '' and orderTimeEndStr != null and orderTimeEndStr != '' ">
			and sro.create_time between #{orderTimeStartStr} and  #{orderTimeEndStr}
		</if>
		<if test="returnReason != null">
			and sroi.return_reason = #{returnReason}
		</if>
		<if test="consignmentId != null">
			and sro.consignment_Id = #{consignmentId}
		</if>
		<if test="stallId != null">
			and sro.stall_id = #{stallId}
		</if>
		<if test="stallIdList != null and stallIdList.size > 0">
			AND sro.stall_id IN
			<foreach collection="stallIdList" index="index" item="stallid" open="(" separator="," close=")">
				#{stallid}
			</foreach>
		</if>
	</select>




	<!-- 短交报表今天的数据 -->
	<select id="shortDeliveryTodayReport" resultType="com.pinshang.qingyun.order.dto.report.ShortDeliveryReportODto" parameterType="com.pinshang.qingyun.order.dto.report.ShortDeliveryReportIDto" >

     SELECT tt.* from (
		SELECT
		    o.`store_id`,
			o.order_time delivery_date,
			o.order_code,
			soi.commodity_id,
			soi.quantity orderNum,
		    soi.price,
			soi.real_delivery_quantity deliveryNum,
			soi.quantity-IFNULL(soi.real_delivery_quantity,0) as differNum,
		    concat(FORMAT((soi.quantity-soi.real_delivery_quantity)/soi.quantity*100,2),'%') as rate,
			o.create_id,
		   (CASE WHEN md.management_mode=1 THEN '直营' WHEN md.management_mode=2 THEN '外包' WHEN md.management_mode=3 THEN '档口分包' ELSE '' END) AS managementModeName,
		    o.stall_id AS stallId
		from t_order o
		 inner join `t_sub_order` so on so.`order_id` =  o.`id`
		 inner join `t_sub_order_item` soi on soi.`sub_order_id` =  so.`id`
		 inner JOIN t_md_shop md ON md.store_id = o.store_id
		WHERE so.status != 2 and o.order_status = 0
		and soi.real_delivery_quantity is not null
		<if test="beginDate != null and endDate != '' and beginDate != null and endDate != '' ">
		  and o.order_time BETWEEN #{beginDate} and #{endDate}
		</if>
		<if test="orderCode != null and orderCode !=''">
			AND o.order_code = #{orderCode}
		</if>
		<if test="differ != null and differ == true">
			and soi.quantity != soi.real_delivery_quantity
		</if>
		<if test="shopId != null and shopId != 0 ">
			AND o.`store_id` = (select store_id from t_md_shop where id = #{shopId})
		</if>
		<if test="managementMode != null and managementMode != ''">
			and md.management_mode = #{managementMode}
		</if>
		<if test="commodityIdList != null and commodityIdList.size() > 0">
			and soi.commodity_id IN
			<foreach collection="commodityIdList" index="index" item="id" open="(" separator="," close=")">
				#{id}
			</foreach>
		</if>
		<if test="storeIdList != null and storeIdList.size() > 0">
			and o.`store_id` IN
			<foreach collection="storeIdList" index="index" item="id" open="(" separator="," close=")">
				#{id}
			</foreach>
		</if>
		<if test="shopIdList != null and shopIdList.size() > 0">
			and md.`id` IN
			<foreach collection="shopIdList" index="index" item="id" open="(" separator="," close=")">
				#{id}
			</foreach>
		</if>
		<if test="stallList != null and stallList.size() > 0">
			and o.`stall_id` IN
			<foreach collection="stallList" index="index" item="id" open="(" separator="," close=")">
				#{id}
			</foreach>
		</if>
		<if test="consignmentId != null">
			AND o.`consignment_id`  = #{consignmentId}
		</if>
		<if test="stallId != null">
			AND o.`stall_id`  = #{stallId}
		</if>

		union all

		SELECT
			so.`store_id`,
			o.order_time delivery_date,
			so.order_code,
			soi.commodity_id,
			soi.require_quantity orderNum,
			soi.price,
		    soi.real_receive_quantity deliveryNum,
			soi.require_quantity-IFNULL(soi.real_receive_quantity,0) as differNum,
		    concat(FORMAT((soi.require_quantity-soi.real_receive_quantity)/soi.require_quantity*100,2),'%') as rate,
		    so.create_id,
		    (CASE WHEN md.management_mode=1 THEN '直营' WHEN md.management_mode=2 THEN '外包' WHEN md.management_mode=3 THEN '档口分包' ELSE '' END) AS managementModeName,
		    o.stall_Id AS stallId
		from t_md_preorder so
			 inner join `t_md_preorder_item` soi on soi.`preorder_id` =  so.`id`
			 INNER JOIN t_order o ON o.id = so.order_id
		     inner JOIN t_md_shop md ON md.store_id = so.store_id
		     left join t_stall st on o.`stall_id` = st.`id`
		WHERE  so.receive_status = 3 and so.order_status in (1,2)
		and soi.real_receive_quantity is not null
		<if test="beginDate != null and endDate != '' and beginDate != null and endDate != '' ">
			and o.order_time BETWEEN #{beginDate} and #{endDate}
		</if>
		<if test="orderCode != null and orderCode !=''">
			AND so.order_code = #{orderCode}
		</if>
		<if test="differ != null and differ == true">
			and soi.require_quantity != soi.real_receive_quantity
		</if>
		<if test="shopId != null and shopId != 0 ">
			AND so.`store_id` = (select store_id from t_md_shop where id = #{shopId})
		</if>
		<if test="managementMode != null and managementMode != ''">
			and md.management_mode = #{managementMode}
		</if>
		<if test="commodityIdList != null and commodityIdList.size() > 0">
			and soi.commodity_id IN
			<foreach collection="commodityIdList" index="index" item="id" open="(" separator="," close=")">
				#{id}
			</foreach>
		</if>
		<if test="storeIdList != null and storeIdList.size() > 0">
			and so.`store_id` IN
			<foreach collection="storeIdList" index="index" item="id" open="(" separator="," close=")">
				#{id}
			</foreach>
		</if>
		<if test="shopIdList != null and shopIdList.size() > 0">
			and md.`id` IN
			<foreach collection="shopIdList" index="index" item="id" open="(" separator="," close=")">
				#{id}
			</foreach>
		</if>
		<if test="consignmentId != null and consignmentId > 0">
			AND 1 = 2
		</if>
		<if test="stallList != null and stallList.size() > 0">
			and o.`stall_id` IN
			<foreach collection="stallList" index="index" item="id" open="(" separator="," close=")">
				#{id}
			</foreach>
		</if>
		<if test="stallId != null">
			AND o.`stall_id`  = #{stallId}
		</if>
	  ) tt WHERE 1=1 order by tt.store_id ASC,tt.delivery_date DESC,tt.commodity_id
	</select>





	<!--  商品实发汇总表 公用-->
	<sql id="realDeliveryReportWhere">
		select
			soi.`commodity_id`,
			soi.quantity quantity,
			IFNULL(soi.real_delivery_quantity,0) real_delivery_quantity,
			(soi.price*IFNULL(soi.real_delivery_quantity,0)) real_delivery_amount,
			soi.quantity-IFNULL(soi.real_delivery_quantity,0) differ_num
		from t_order o
		INNER JOIN `t_sub_order` so ON so.`order_id` = o.`id`
		INNER JOIN `t_sub_order_item` soi ON soi.`sub_order_id` = so.`id`
		inner JOIN t_md_shop md ON md.store_id = o.store_id
		where 1=1
		and so. STATUS != 2 and o.order_status = 0 and soi.real_delivery_quantity >= 0

		<if test="beginDate !=null and endDate != '' and beginDate != null and endDate != '' ">
			and o.order_time BETWEEN #{beginDate} and #{endDate}
		</if>
		<if test="commodityIdList != null and commodityIdList.size >0 ">
			and soi.commodity_id in
			<foreach collection="commodityIdList" index="index" item="commodityId" open="(" separator="," close=")">
				#{commodityId}
			</foreach>
		</if>
		<if test="differ != null and differ == true">
			and (soi.real_delivery_quantity is null or soi.quantity != soi.real_delivery_quantity)
		</if>
		<if test="consignmentId != null">
			AND o.`consignment_id`  = #{consignmentId}
		</if>

		UNION ALL

		select
			soi.`commodity_id`,
			soi.require_quantity quantity,
		    soi.real_receive_quantity real_delivery_quantity,
			(soi.price*IFNULL(soi.real_receive_quantity,0)) real_delivery_amount,
		    soi.require_quantity-IFNULL(soi.real_receive_quantity,0) differ_num
		from t_md_preorder so
		INNER JOIN `t_md_preorder_item` soi ON soi.`preorder_id` = so.`id`
		INNER JOIN t_order o ON o.id = so.order_id
		inner JOIN t_md_shop md ON md.store_id = so.store_id
		where 1=1
		and so.order_status in (1,2) and so.receive_status = 3 and soi.real_receive_quantity >= 0

		<if test="beginDate !=null and endDate != '' and beginDate != null and endDate != '' ">
			and o.order_time BETWEEN #{beginDate} and #{endDate}
		</if>
		<if test="commodityIdList != null and commodityIdList.size >0 ">
			and soi.commodity_id in
			<foreach collection="commodityIdList" index="index" item="commodityId" open="(" separator="," close=")">
				#{commodityId}
			</foreach>
		</if>
		<if test="differ != null and differ == true">
			and (soi.real_receive_quantity is null or soi.require_quantity != soi.real_receive_quantity)
		</if>
		<if test="consignmentId != null and consignmentId > 0">
			AND 1 = 2
		</if>
	</sql>

	<!--  商品实发汇总表 -->
	<select id="realDeliveryReportCurrentDay" resultType="com.pinshang.qingyun.order.dto.report.RealDeliveryReportODto" parameterType="com.pinshang.qingyun.order.dto.report.RealDeliveryReportDataQueryIDto" >
		SELECT
			t.commodity_id,
			sum(t.quantity) orderNum,
			sum(IFNULL(t.real_delivery_quantity,0)) deliveryNum,
			sum(IFNULL(t.real_delivery_amount,0)) realDeliveryAmount,
			sum(t.quantity-IFNULL(t.real_delivery_quantity,0)) differNum
		FROM (
		<include refid="realDeliveryReportWhere"></include>
		) t GROUP BY t.commodity_id
		ORDER  BY  t.commodity_id
	</select>
	<!--  商品实发汇总表  合计 -->
	<select id="realTotalDeliveryReportCurrentDay" resultType="java.math.BigDecimal" parameterType="com.pinshang.qingyun.order.dto.report.RealDeliveryReportDataQueryIDto" >
		SELECT
			sum(IFNULL(t.real_delivery_amount,0))
		FROM
		(
		<include refid="realDeliveryReportWhere"></include>
		) t
	</select>





	<!--  商品实发汇总表(按客户类型) 公用 -->
	<sql id="realDeliveryStoreTypeReportWhere">
		select
			ts.store_type_id store_type_id,
			soi.`commodity_id`,
			soi.quantity quantity,
			IFNULL(soi.real_delivery_quantity,0) real_delivery_quantity,
			(soi.price*IFNULL(soi.real_delivery_quantity,0)) real_delivery_amount,
			soi.quantity-IFNULL(soi.real_delivery_quantity,0) differ_num
		from t_order o
		INNER JOIN `t_sub_order` so ON so.`order_id` = o.`id`
		INNER JOIN `t_sub_order_item` soi ON soi.`sub_order_id` = so.`id`
		LEFT join t_store ts on ts.id = o.`store_id`
		inner JOIN t_md_shop md ON md.store_id = o.store_id
		where so. STATUS != 2 and o.order_status = 0 and soi.real_delivery_quantity >= 0

		<if test="beginDate !=null and endDate != '' and beginDate != null and endDate != '' ">
			and o.order_time BETWEEN #{beginDate} and #{endDate}
		</if>
		<if test="commodityIdList != null and commodityIdList.size >0 ">
			and soi.commodity_id in
			<foreach collection="commodityIdList" index="index" item="commodityId" open="(" separator="," close=")">
				#{commodityId}
			</foreach>
		</if>
		<if test="differ != null and differ == true">
			and (soi.real_delivery_quantity is null or soi.quantity != soi.real_delivery_quantity)
		</if>
		<if test="storeTypeId != null">
			AND ts.store_type_id =  #{storeTypeId}
		</if>
		<if test="consignmentId != null">
			AND o.`consignment_id`  = #{consignmentId}
		</if>

		UNION ALL

		select
			ts.store_type_id store_type_id,
			soi.`commodity_id`,
			soi.require_quantity quantity,
			soi.real_receive_quantity real_delivery_quantity,
			(soi.price*IFNULL(soi.real_receive_quantity,0)) real_delivery_amount,
			soi.require_quantity-IFNULL(soi.real_receive_quantity,0) differ_num
		from t_md_preorder so
		INNER JOIN `t_md_preorder_item` soi ON soi.`preorder_id` = so.`id`
		INNER JOIN t_order o ON o.id = so.order_id
		LEFT join t_store ts on ts.id = so.`store_id`
		inner JOIN t_md_shop md ON md.store_id = so.store_id
		where  so.receive_status = 3 and so.order_status in (1,2) and soi.real_receive_quantity >= 0

		<if test="beginDate !=null and endDate != '' and beginDate != null and endDate != '' ">
			and o.order_time BETWEEN #{beginDate} and #{endDate}
		</if>
		<if test="commodityIdList != null and commodityIdList.size >0 ">
			and soi.commodity_id in
			<foreach collection="commodityIdList" index="index" item="commodityId" open="(" separator="," close=")">
				#{commodityId}
			</foreach>
		</if>
		<if test="differ != null and differ == true">
			and (soi.real_receive_quantity is null or soi.require_quantity != soi.real_receive_quantity)
		</if>
		<if test="storeTypeId != null">
			AND ts.store_type_id =  #{storeTypeId}
		</if>
		<if test="consignmentId != null and consignmentId > 0">
			AND 1 = 2
		</if>
	</sql>

	<!--  商品实发汇总表 (按客户类型)-->
	<select id="realDeliveryStoreTypeReportCurrentDay" resultType="com.pinshang.qingyun.order.dto.report.RealDeliveryReportODto" parameterType="com.pinshang.qingyun.order.dto.report.RealDeliveryReportIDto" >
		SELECT
			t.store_type_id,
			t.commodity_id,
			sum(t.quantity) orderNum,
			sum(IFNULL(t.real_delivery_quantity,0)) deliveryNum,
			sum(IFNULL(t.real_delivery_amount,0)) realDeliveryAmount,
			sum(t.quantity-IFNULL(t.real_delivery_quantity,0)) differNum
		FROM (
		<include refid="realDeliveryStoreTypeReportWhere"></include>
		) t GROUP BY t.store_type_id,t.commodity_id
		ORDER  BY  t.store_type_id,t.commodity_id
	</select>

	<!--  商品实发汇总表 (按客户类型) 合计 -->
	<select id="realTotalDeliveryStoreTypeReportCurrentDay" resultType="java.math.BigDecimal" parameterType="com.pinshang.qingyun.order.dto.report.RealDeliveryReportIDto" >
		SELECT
			sum(IFNULL(t.real_delivery_amount,0))
		FROM
		(
		<include refid="realDeliveryStoreTypeReportWhere"></include>
		) t
	</select>





	<!--  门店订货汇总表 -->
	<select id="shopOrderGoodReport" resultType="com.pinshang.qingyun.order.dto.report.ShopOrderGoodReportODto" parameterType="com.pinshang.qingyun.order.dto.report.ShopOrderGoodReportIDto" >
		SELECT
			t.store_id,
			t.commodity_id,
			sum(t.quantity) orderNum,
			sum(IFNULL(t.real_delivery_quantity,0)) deliveryNum,
			sum(IFNULL(t.real_delivery_amount,0)) realDeliveryAmount,
			sum(t.quantity-IFNULL(t.real_delivery_quantity,0)) differNum
		FROM (

		select
			o.`store_id`,
			soi.`commodity_id`,
			soi.quantity quantity,
			IFNULL(soi.real_delivery_quantity,0) real_delivery_quantity,
			(soi.price*IFNULL(soi.real_delivery_quantity,0)) real_delivery_amount,
			soi.quantity-IFNULL(soi.real_delivery_quantity,0) differ_num
		from t_order o
		INNER JOIN `t_sub_order` so ON so.`order_id` = o.`id`
		INNER JOIN `t_sub_order_item` soi ON soi.`sub_order_id` = so.`id`
		inner join t_md_shop md on md.store_id = o.`store_id`
		where so. STATUS != 2
		and o.order_status = 0
		<if test="beginDate !=null and endDate != '' and beginDate != null and endDate != '' ">
			and o.order_time BETWEEN #{beginDate} and #{endDate}
		</if>
		<if test="commodityIdList != null and commodityIdList.size >0 ">
			and soi.`commodity_id` in
			<foreach collection="commodityIdList" index="index" item="commodityId" open="(" separator="," close=")">
				#{commodityId}
			</foreach>
		</if>
		<if test="differ != null and differ == true">
			and soi.quantity != soi.real_delivery_quantity
		</if>
		<if test="shopId != null and shopId != 0 ">
			AND o.`store_id` = (select store_id from t_md_shop where id = #{shopId})
		</if>
		<if test="storeIdList != null and storeIdList.size() > 0">
			and o.`store_id` IN
			<foreach collection="storeIdList" index="index" item="id" open="(" separator="," close=")">
				#{id}
			</foreach>
		</if>

		UNION ALL

		select
			so.`store_id`,
			soi.`commodity_id`,
			soi.require_quantity quantity,
			soi.real_receive_quantity real_delivery_quantity,
			(soi.price*IFNULL(soi.real_receive_quantity,0)) real_delivery_amount,
			soi.require_quantity-IFNULL(soi.real_receive_quantity,0) differ_num
		from t_md_preorder so
		INNER JOIN `t_md_preorder_item` soi ON soi.`preorder_id` = so.`id`
		INNER JOIN t_order o ON o.id = so.order_id
		inner join t_md_shop md on md.store_id = so.`store_id`
		where 1=1
		and so.order_status in (1,2) AND so.receive_status = 3
		<if test="beginDate !=null and endDate != '' and beginDate != null and endDate != '' ">
			and o.order_time BETWEEN #{beginDate} and #{endDate}
		</if>
		<if test="commodityIdList != null and commodityIdList.size >0 ">
			and soi.`commodity_id` in
			<foreach collection="commodityIdList" index="index" item="commodityId" open="(" separator="," close=")">
				#{commodityId}
			</foreach>
		</if>
		<if test="differ != null and differ == true">
			and soi.require_quantity != soi.real_receive_quantity
		</if>
		<if test="shopId != null and shopId != 0 ">
			AND so.`store_id` = (select store_id from t_md_shop where id = #{shopId})
		</if>
		<if test="storeIdList != null and storeIdList.size() > 0">
			and so.`store_id` IN
			<foreach collection="storeIdList" index="index" item="id" open="(" separator="," close=")">
				#{id}
			</foreach>
		</if>

		) t GROUP BY t.store_id,t.commodity_id
		ORDER  BY  t.store_id,t.commodity_id
	</select>




	<!-- 门店实收商品分析表 -->
	<select id="actualReceiptAnalysisReport" resultType="com.pinshang.qingyun.order.dto.report.ActualReceiptAnalysisODto" parameterType="com.pinshang.qingyun.order.dto.report.ActualReceiptAnalysisIDto">
		select
			tmp.store_id,
			tmp.commodity_id,
			IFNULL(sum(tmp.totalQuantity),0) as totalQuantity,
			IFNULL(sum(tmp.totalRealReceiveQuantity),0) as totalRealReceiveQuantity,
			IFNULL(sum(tmp.totalRealDeliveryQuantity),0) as totalRealDeliveryQuantity
		from (
			SELECT
				o.`store_id`,
				soi.`commodity_id`,
				sum(soi.quantity) as totalQuantity,
				sum(soi.real_receive_quantity) as totalRealReceiveQuantity,
				sum(soi.real_delivery_quantity) as totalRealDeliveryQuantity
			FROM
		       t_order o
			inner join `t_sub_order` so on so.`order_id` =  o.`id`
			inner join `t_sub_order_item` soi on soi.`sub_order_id` =  so.`id`
			LEFT JOIN t_md_receive_order AS mro ON mro.sub_order_id = so.id
			inner JOIN t_md_shop md ON md.store_id = o.store_id
			WHERE so.status != 2 and
			(
				mro.`status` != 0  AND mro.`status` != 4
			)
			<if test="shopId != null and shopId != 0 ">
				AND o.`store_id` = (select store_id from t_md_shop where id = #{shopId})
			</if>
			<if test="beginDate != null and endDate != null ">
				and o.order_time between #{beginDate} and #{endDate}
			</if>
			<if test="commodityIdList != null and commodityIdList.size >0 ">
				and soi.`commodity_id` in
				<foreach collection="commodityIdList" index="index" item="commodityId" open="(" separator="," close=")">
					#{commodityId}
				</foreach>
			</if>
			GROUP BY soi.`commodity_id`,o.`store_id`

			UNION ALL

			SELECT
				so.`store_id`,
				soi.`commodity_id`,
				sum(soi.require_quantity) as totalQuantity,
				sum(soi.real_receive_quantity) as totalRealReceiveQuantity,
				sum(soi.real_receive_quantity) as totalRealDeliveryQuantity
			FROM
				t_md_preorder AS so
			inner join `t_md_preorder_item` soi on soi.`preorder_id` =  so.`id`
			INNER JOIN t_order o ON o.id = so.order_id
			inner JOIN t_md_shop md ON md.store_id = so.store_id
			where so.receive_status = 3
			<if test="shopId != null and shopId != 0 ">
				AND so.`store_id` = (select store_id from t_md_shop where id = #{shopId})
			</if>
			<if test="beginDate != null and endDate != null ">
				and o.order_time between #{beginDate} and #{endDate}
			</if>
			<if test="commodityIdList != null and commodityIdList.size >0 ">
				and soi.`commodity_id` in
				<foreach collection="commodityIdList" index="index" item="commodityId" open="(" separator="," close=")">
					#{commodityId}
				</foreach>
			</if>
			GROUP BY soi.`commodity_id`,o.`store_id`
		)  as tmp
		GROUP BY tmp.store_id,tmp.commodity_id
		ORDER BY
		tmp.store_id
	</select>

</mapper>