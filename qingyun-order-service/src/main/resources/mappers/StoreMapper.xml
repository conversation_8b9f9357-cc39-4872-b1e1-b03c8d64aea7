<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.pinshang.qingyun.order.mapper.StoreMapper">

    <select id="getStoreAccountList" resultType="com.pinshang.qingyun.order.mapper.entry.store.StoreAccountEntry">
        SELECT
            s.id AS storeId,
            s.store_code AS storeCode,
            s.store_name AS storeName,
            s.store_status AS storeStatus,
            e.employee_name AS deliverymanName,
            n.app_max_order_number AS maxOrderNumber,
            sd.begin_time AS beginTime,
            sd.end_time AS endTime,
            IFNULL(au.state,1) AS openStatus
        FROM
            t_store AS s
            LEFT JOIN t_employee_user AS e ON e.employee_id = s.deliveryman_id
            LEFT JOIN t_store_order_number AS n ON n.store_id = s.id
            LEFT JOIN t_store_duration AS sd ON sd.store_id = s.id
            LEFT JOIN t_app_user AS au ON au.store_id = s.id
        <where>
             s.store_status IN (0,1)
             AND (s.supervisor_id = #{employeeId} OR s.region_manager_id = #{employeeId})
            <if test="storeCode != null and storeCode !='' ">
                AND (s.store_code LIKE concat('%',#{storeCode},'%')
                OR s.store_name LIKE concat('%',#{storeCode},'%')
                OR s.store_aid LIKE concat('%',#{storeCode},'%'))
            </if>
        ORDER BY openStatus DESC,storeId
        </where>
    </select>

    <select id="getByStoreCode" resultType="com.pinshang.qingyun.order.model.store.Store">
        SELECT * FROM t_store WHERE store_code =#{storeCode}
    </select>

    <select id="getShopByStoreCode" resultType="com.pinshang.qingyun.order.model.store.Store">
        SELECT
            s.* FROM t_store s
         INNER   JOIN t_md_shop t  ON t.store_id = s.id
        WHERE s.store_code =#{storeCode}
    </select>

    <select id="getStoreByCodeList" resultType="com.pinshang.qingyun.order.model.store.Store">
        SELECT
               t.id,t.store_code,t.store_name,
               md.id shopId,
               md.shop_type,
               md.management_mode
        from t_store t
        LEFT JOIN t_md_shop md on md.store_id = t.id
        where t.store_code in
        <foreach collection="storeCodeList" item="storeCode" open="(" separator="," close=")">
            #{storeCode}
        </foreach>
    </select>

    <select id="getDistributionStoreAccountList" resultType="com.pinshang.qingyun.order.vo.shop.DistributionStoreAccountVo">
        SELECT s.id AS storeId, s.store_code, s.store_name, d.option_name AS customerType
        FROM t_store s LEFT JOIN t_dictionary d ON s.store_type_id  = d.id
        <where>
            <if test="storeTypeId != null">
                s.store_type_id = #{storeTypeId}
            </if>
            <if test="vagueStr != null and vagueStr != ''">
                AND (s.store_code LIKE CONCAT('%',#{vagueStr},'%')
                OR s.store_name LIKE CONCAT('%',#{vagueStr},'%')
                OR s.store_aid LIKE CONCAT('%',#{vagueStr},'%'))
            </if>
            <if test="excludeIds != null">
                AND s.id NOT IN
                <foreach collection="excludeIds" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </where>
        ORDER BY s.id
    </select>

    <select id="findInStoreCodes" resultType="com.pinshang.qingyun.order.model.store.Store">
        SELECT id ,store_code,store_name FROM t_store
        <where>
            store_code IN
            <foreach collection="codes" open="(" separator="," close=")" item="item">
                #{item}
            </foreach>
        </where>
    </select>

    <select id="queryStoreLineByStoreIds" resultType="com.pinshang.qingyun.order.model.distribution.DistributionLine" parameterType="java.util.List">
        SELECT
            s.id AS storeId,
            dl.*
        FROM
            t_store s
        INNER JOIN t_distribution_line dl ON s.store_line_id = dl.id
        WHERE
            s.id IN
        <foreach collection="ids" item="id" separator="," open="(" close=")" >
            #{id}
        </foreach>
    </select>

    <!--条件查询客户列表-->
    <select id="findStoreListByStoreName" resultType="com.pinshang.qingyun.order.mapper.entry.store.StoreEntry">
        SELECT
            s.id,
            s.id as idStr,
            s.store_code AS storeCode,
            s.store_name AS storeName,
            CONCAT(
                s.store_code,
                '_',
                s.store_name
            ) AS storeCodeAndName
        FROM
            t_store s
        WHERE
            s.store_status in (0,1)
        <if test="storeName !=null and storeName != ''" >
            AND
            (s.store_name like CONCAT('%',trim(#{storeName}),'%')
            OR  s.store_code like  CONCAT('%',trim(#{storeName}),'%')
            or s.store_aid like  CONCAT('%',trim(#{storeName}),'%'))
        </if>
        limit 50;
    </select>

    <!--查询所有绑定门店的客户-->
    <select id="findAllShopStoreList" resultType="com.pinshang.qingyun.order.mapper.entry.store.StoreEntry" >
        SELECT
            s.id,
            s.store_code as storeCode,
            s.store_name as storeName,
            ms.id as shopId,
            ms.shop_code as shopCode
        FROM
            t_store s
        INNER JOIN t_md_shop ms ON ms.store_id = s.id
    </select>

    <select id="findOrderNumberLimit" resultType="integer" >
        SELECT
            app_max_order_number
        FROM
            t_store_order_number
            where store_id = #{storeId}
            limit 1
    </select>

    <select id="getStoreCompanyByStoreId" resultType="com.pinshang.qingyun.order.vo.store.StoreCompanyVo" >
        SELECT
            t.id storeId,
            t.company_id companyId,
            s.store_type_id storeTypeId
        FROM
            t_store_company t,
            t_store s
        where t.store_id = #{storeId}
        and
            t.store_id = s.id
        limit 1
    </select>

    <select id="getCompanyIdByShopIdList" resultType="com.pinshang.qingyun.order.vo.StoreCompanyRespVo" parameterType="list" >
        SELECT * FROM (
            SELECT
                s.id AS shopId,
                sc.store_id,
                sc.company_id
            FROM
                t_md_shop s
                INNER JOIN t_store_company sc ON s.store_id = sc.store_id
                INNER JOIN t_company c ON sc.company_id = c.id
            WHERE
                sc.`status` = 1 AND
                s.id IN
                <foreach collection="shopIdList" item="item" open="(" close=")" separator="," index="index">
                    #{item}
                </foreach>
            ORDER BY sc.store_id ASC, c.parent_id ASC,c.id ASC
        )tmp
        GROUP BY tmp.store_id
    </select>
    
    <!-- 查询  客户线路组名称 -->
    <select id="selectStoreLineGroupName" resultType="String">
    	SELECT d.option_name AS storeLineGroupName
		FROM t_store s
		LEFT JOIN t_distribution_line tdl ON s.store_line_id = tdl.id
		LEFT JOIN t_dictionary d ON d.id = tdl.delivery_time_id
		WHERE s.id = #{storeId}
    </select>


    <select id="findSettleBillCalcTypeById" resultType="java.lang.Integer">
        SELECT
            std.settle_bill_calc_type as settleBillCalcType
        FROM
            t_store_type_desc  std where id=#{id}
    </select>


    <select id="queryStoreByStoreCodeList" resultType="com.pinshang.qingyun.order.mapper.entry.store.StoreEntry" >
        SELECT
            DISTINCT
             s.id,
             s.store_code AS storeCode,
             s.store_name AS storeName,
             s.store_short_name AS storeShortName,
             tsc.status AS storeStatus,
             s.store_is_prepay storeIsPrepay,
             ss.collect_status collectStatus,
             s.store_type storeType,
             s.business_type businessType,
             td.option_code optionCode
        FROM t_store s
             LEFT JOIN t_store_company tsc ON tsc.store_id = s.id
             LEFT JOIN t_store_settlement ss on ss.store_id = s.id
             LEFT JOIN t_dictionary td on td.id = s.store_type_id
        WHERE
            s.store_code IN
            <foreach collection="storeCodeList" item="item" open="(" close=")" separator="," index="index">
                #{item}
            </foreach>
        ORDER BY s.id ASC
    </select>
    <select id="findStoreCompanyIdByStoreId" resultType="java.lang.Long">
        SELECT tsc.company_id FROM t_store s LEFT JOIN t_store_company tsc ON tsc.store_id = s.id WHERE
				s.store_type = 2 AND s.id = #{storeId}
    </select>

    <select id="queryStoreIdsByStoreNameOrCode" resultType="java.lang.Long">
        SELECT
            s.id
        FROM t_store s
         where 1 = 1
        <if test="storeCode != null and storeCode  != '' ">
            AND s.store_code like concat('%',#{storeCode},'%')
        </if>
        <if test="storeName != null and storeName  != '' ">
            AND s.`store_name` like concat('%',#{storeName},'%')
        </if>
    </select>

</mapper>