<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.pinshang.qingyun.order.mapper.SystemPrinterMapper" >


	<select id="querySystemPrinterByUserId" resultType="com.pinshang.qingyun.order.mapper.entry.SystemPrinterEntry">
		SELECT
			p.print_name as printName,
			p.print_type as printType,
			p.data_group_type as dataGroupType
		from
			t_system_printer p ,t_user_printer u
		where
			p.id=u.print_id
			and u.user_id = #{userId}
			and u.def_printer=1
			and p.`status`=0
	</select>

	<select id="queryEmployeePrinterByDeliveryManCode" resultType="com.pinshang.qingyun.order.mapper.entry.EmployeePrinterEntry">
		SELECT
			e.id as id,
			e.`local` as local,
			e.`status` as status
		FROM
			`t_employee_printer` e
		WHERE  e.employee_code = #{employeeCode}
	</select>

	<select id="queryStorePrinterByStoreCode" resultType="com.pinshang.qingyun.order.mapper.entry.StorePrinterEntry">
		SELECT * FROM t_store_printer WHERE store_code = #{storeCode}
	</select>

</mapper>