<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.pinshang.qingyun.order.mapper.SubOrderCountMapper" >

    <select id = "shopOrderNum" resultType="com.pinshang.qingyun.order.vo.order.ShopOrderNumVo">
        SELECT IFNULL(COUNT(DISTINCT(oi.commodity_id)), 0) AS 'reserveNum',
               IFNULL(COUNT(DISTINCT(ic.commodity_id)), 0) AS 'countNum'
        FROM t_order o
        LEFT JOIN t_sub_order so ON o.id = so.order_id
        LEFT JOIN t_sub_order_item oi on oi.sub_order_id = so.id
        LEFT JOIN t_sub_order_item_count ic ON (ic.send_date = o.order_time and ic.shop_id = #{shopId} )
        WHERE o.store_id = #{storeId}
        AND o.order_time = #{date}
        AND so.status != 2
        AND oi.real_delivery_quantity IS NOT NULL AND oi.real_delivery_quantity > 0
    </select>

    <select id = "countCommodityInfo" resultType="com.pinshang.qingyun.order.vo.order.CountCommodityInfoVo">
        select c.id as commodityId,
               c.commodity_code as commodityCode,
               c.bar_code as barCode,
               c.commodity_name as commodityName,
               c.commodity_spec as commoditySpec,
               c.commodity_unit_id as commodityUnitId,
               c.commodity_package_spec as commodityPackageSpec,
               c.is_weight as isWeight,
               sum(soi.real_delivery_quantity) as orderNum
        from t_order o
        left join t_sub_order so on so.order_id = o.id
        left join t_sub_order_item soi on soi.sub_order_id = so.id
        left join t_commodity c on c.id = soi.commodity_id
        where o.store_id = #{storeId}
        and o.order_time = #{date}
        and soi.commodity_id = #{commodityId}
        AND so.status != 2
        HAVING SUM(soi.real_delivery_quantity) > 0;
    </select>

    <select id = "subOrderItemInfo" resultType="com.pinshang.qingyun.order.vo.order.OrderCountVo">
        select soi.commodity_id, IFNULL(sum(soi.real_delivery_quantity), 0) as reserveNum
        from t_order o
        left join t_sub_order so on so.order_id = o.id
        left join t_sub_order_item soi on soi.sub_order_id = so.id
        where o.store_id = #{storeId}
        and o.order_time = #{date}
        AND so.status != 2
        and soi.commodity_id in
        <foreach collection="commodityIds" item="item" index="index" open="(" separator="," close=")">
            #{item}
        </foreach>
        AND soi.real_delivery_quantity IS NOT NULL AND soi.real_delivery_quantity > 0
        group by soi.commodity_id
    </select>

    <select id = "countCommodityList" resultType="com.pinshang.qingyun.order.model.order.SubOrderItemCount">
        select c.id,
               c.shop_id,
               c.send_date,
               c.commodity_id,
               c.quantity,
               c.count_quantity
        from t_sub_order_item_count c
        where c.shop_id = #{shopId}
        and c.send_date = #{date}
        <if test = "commodityIds != null and commodityIds.size() > 0">
         and c.commodity_id in
         <foreach collection="commodityIds" item="item" index="index" open="(" separator="," close=")">
             #{item}
         </foreach>
        </if>
    </select>

    <select id = "todayCountCommodityList" resultType="com.pinshang.qingyun.order.vo.order.CountCommodityInfoVo">
        SELECT c.id as commodityId,
               c.commodity_code,
               c.bar_code,
               c.commodity_name,
               c.commodity_spec,
               c.commodity_unit_id AS commodityUnitId,
               c.commodity_package_spec AS commodityPackageSpec,
               c.is_weight,
               ic.quantity AS orderNum,
               ic.count_quantity AS countNum
        FROM t_sub_order_item_count ic
        LEFT JOIN t_commodity c ON ic.commodity_id = c.id
        WHERE ic.shop_id = #{shopId}
        AND ic.send_date = #{date}
        <if test = "null != commodityId and commodityId != ''">
            AND ic.commodity_id = #{commodityId}
        </if>
        ORDER BY c.commodity_code ASC
    </select>

    <select id="countCommodityListForPage" resultType="com.pinshang.qingyun.order.dto.ShopCountStockPageODTO">
        select
        c.shop_id,
        c.send_date orderTime,
        count(c.commodity_id) countNum
        from t_sub_order_item_count c
        where c.shop_id IN
        <foreach collection="shopIdList" item="shopId" open="(" close=")" separator=",">
            #{shopId}
        </foreach>
        AND c.send_date >= #{beginTime}
        AND c.send_date &lt; #{endTime}
        GROUP BY c.shop_id, c.send_date
    </select>

    <select id="countCommodityListForDetail" resultType="com.pinshang.qingyun.order.dto.ShopCountStockDetailItemODTO"
            parameterType="com.pinshang.qingyun.order.dto.ShopCountStockDetailIDTO">
        select
        c.shop_id,
        c.send_date orderTime,
        c.`commodity_id`,
        c.update_id updateId,
        c.count_quantity countNum,
        c.update_time updateUserTime
        from t_sub_order_item_count c
        where c.shop_id = #{shopId}
        AND c.send_date = #{orderTime}
        <if test="null != commodityId">
            AND c.commodity_id = #{commodityId}
        </if>
        <if test="barCode != null and barCode !='' ">
            and c.commodity_id = (SELECT  commodity_id FROM t_commodity_bar_code WHERE  bar_code = #{barCode})
        </if>
        GROUP BY c.shop_id, c.send_date, c.`commodity_id`
        order by c.`commodity_id`
    </select>
</mapper>