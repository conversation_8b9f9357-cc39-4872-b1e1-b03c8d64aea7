<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.pinshang.qingyun.order.mapper.SettleOrderMapper">


    <sql id="columns">
        t.kid,
        t.logisticsModel,
        t.orderCode,
        t.source_id sourceId,
        t.delivery_time deliveryTime,
        t.orderTime,
        t.delivery_total_amount deliveryTotalAmount,
        t.store_id storeId,
        t.stock_out_order_code stockOutOrderCode,
        t.total_amount totalAmount,
        s.store_code storeCode,
        s.supervisor_id supervisorId,
        s.store_type_id storeTypeId,
        s.office_director_id officeDirectorId,
        s.salesman_id salesmanId,
        s.deliveryman_id deliverymanId,
        s.store_line_id storeLineId,
        s.region_manager_id regionManagerId,
        s.delivery_address AS deliveryAddress,
        s.id storeId,
        s.store_name storeName,
        settle.id settlmentId,
        settle.customer_name settlementName
    </sql>


    <!-- 直送 -->
    <select id="findXsZsOrderByParams" resultType="com.pinshang.qingyun.order.mapper.entry.settlement.SettleOrderEntry" >
        SELECT
          <include refid="columns"/>
        FROM
        (

        SELECT
                po.id kid,
                0 logisticsModel,
                o.order_code orderCode,
                po.id source_id,
                o.order_amount AS delivery_total_amount,
                o.store_id,
                '' stock_out_order_code,
                o.order_amount total_amount,
                po.approving_time delivery_time,
                po.approving_time orderTime
            FROM
                t_md_preorder po
                INNER JOIN t_order o ON o.id = po.order_id
             WHERE
             o.order_type in (10,28,29)
                <choose>
                    <when test="ids == null ">
                            and o.order_status = 0
                            AND po.receive_status = 3
                            <if test="deliveryTimeStart !=null and deliveryTimeEnd != null and deliveryTimeStart != '' and deliveryTimeEnd != '' ">
                                AND po.approving_time  BETWEEN #{deliveryTimeStartHms} and #{deliveryTimeEndHms}
                            </if>
                            <if test="storeIds !=null">
                                AND po.store_id IN
                                <foreach collection="storeIds" item="id" open="(" separator="," close=")">
                                    #{id}
                                </foreach>
                            </if>
                    </when>
                    <when test="ids != null">
                        and po.id IN
                        <foreach collection="ids" item="id" open="(" separator="," close=")">
                            #{id}
                        </foreach>
                    </when>
                </choose>
        ) t
        LEFT JOIN t_store s ON s.id = t.store_id
        LEFT JOIN t_store_settlement ss ON ss.store_id = s.id
        LEFT JOIN t_settlement settle ON ss.settlement_customer_id = settle.id
    </select>

    <!-- 订单 -->
    <select id="findOrderByParams" resultType="com.pinshang.qingyun.order.mapper.entry.settlement.SettleOrderEntry" >
        SELECT
          <include refid="columns"/>
        FROM
            (
                SELECT
                    o.id kid,
                    5 logisticsModel,
                    o.order_code orderCode,
                    o.id source_id,
                    o.final_amount delivery_total_amount,
                    o.store_id,
                    '' stock_out_order_code,
                    o.final_amount total_amount,
                    o.order_time delivery_time,
                    o.order_time orderTime
                FROM
                  t_order o
                WHERE
                  order_type in (1,2)
                <choose>
                    <when test="ids == null ">
                        and o.order_status = 0
                        <if test="deliveryTimeStart !=null and deliveryTimeEnd != null and deliveryTimeStart != '' and deliveryTimeEnd != '' ">
                            AND o.order_time  BETWEEN #{deliveryTimeStart} and #{deliveryTimeEnd}
                        </if>
                        <if test="storeIds !=null">
                            AND o.store_id IN
                            <foreach collection="storeIds" item="id" open="(" separator="," close=")">
                                #{id}
                            </foreach>
                        </if>
                    </when>
                    <when test="ids != null">
                       and o.id IN
                        <foreach collection="ids" item="id" open="(" separator="," close=")">
                            #{id}
                        </foreach>
                    </when>
                </choose>
        ) t
        LEFT JOIN t_store s ON s.id = t.store_id
        LEFT JOIN t_store_settlement ss ON ss.store_id = s.id
        LEFT JOIN t_settlement settle ON ss.settlement_customer_id = settle.id
    </select>



    <select id="findXsOrderItemByOrderIds" resultType="com.pinshang.qingyun.order.mapper.entry.settlement.SettleOrderItemEntry">
        SELECT
            t.*,
            cate1.cate_name categoryFirstName,
            cate2.cate_name categorySecondName,
            cate3.cate_name commodityThirdName,
            c.id AS commodityId,
            c.commodity_name AS commodityName,
            c.commodity_code AS commodityCode,
            c.commodity_spec AS commoditySpec,
            c.commodity_first_id AS categoryFirstId,
            c.commodity_second_id AS categorySecondId,
            c.commodity_third_id AS commodityThirdId,
            c.commodity_unit_id commodityUnitId,
            c.tax_rate_id taxRateId,
            c.tax_rate rate,
            d.option_name commodityUnit
        FROM
        (
            <if test="logisticsModel == 5">
                SELECT
                    order_id kid,
                    id itemId,
                    commodity_id commodityId,
                    commodity_num quantity,
                    commodity_price unitPrice,
                    total_price totalPrice,
                    commodity_price AS delivery_unit_price,
                    commodity_num AS delivery_quantity,
                    now() createTime
                FROM
                  t_order_list_gift
                where order_id in
                <foreach collection="orderIds" item="code" open="(" close=")" separator=",">
                    #{code}
                </foreach>
            </if>
            <if test="logisticsModel == 0">
                SELECT
                    oi.preorder_id kid,
                    oi.id itemId,
                    oi.commodity_id AS commodityId,
                    require_quantity `quantity`,
                    oi.price unitPrice,
                    oi.total_price,
                    oi.price AS delivery_unit_price,
                    oi.real_receive_quantity AS delivery_quantity,
                    oi.create_time createTime
                FROM
                  t_md_preorder_item oi
                WHERE
                    oi. STATUS = 2
                    AND oi.preorder_id IN
                    <foreach collection="orderIds" item="code" open="(" close=")" separator=",">
                        #{code}
                    </foreach>
            </if>
            <if test="logisticsModel == 1 or logisticsModel == 2">
                SELECT
                    oi.sub_order_id kid,
                    oi.id itemId,
                    oi.commodity_id AS commodityId,
                    oi.quantity AS quantity,
                    oi.price `unit_price`,
                    oi.total_price,
                    oi.price AS delivery_unit_price,
                    oi.real_delivery_quantity AS delivery_quantity,
                    oi.create_time createTime
                FROM
                  t_sub_order_item oi
                WHERE
                    oi.real_delivery_quantity IS NOT NULL
                    AND oi.sub_order_id IN
                    <foreach collection="orderIds" item="code" open="(" close=")" separator=",">
                        #{code}
                    </foreach>
            </if>
        ) t
        LEFT JOIN t_commodity c ON c.id = t.commodityId
        LEFT JOIN t_dictionary d ON d.id = c.commodity_unit_id
        LEFT JOIN t_category cate1 ON c.commodity_first_id = cate1.id
        LEFT JOIN t_category cate2 ON c.commodity_second_id = cate2.id
        LEFT JOIN t_category cate3 ON c.commodity_third_id = cate3.id
    </select>




    <!-- 订单 -->
    <select id="findTOrderByParams" resultType="com.pinshang.qingyun.order.mapper.entry.settlement.SettleOrderEntry" >
        SELECT
        <include refid="columns"/>
        FROM
        (
        SELECT
        o.id kid,
        11 logisticsModel,
        o.order_code orderCode,
        o.id source_id,
        o.final_amount delivery_total_amount,
        o.store_id,
        '' stock_out_order_code,
        o.final_amount total_amount,
        o.order_time delivery_time,
        o.order_time orderTime
        FROM
        t_order o
        WHERE
        1=1
        <if test="notOrderTypes != null  and notOrderTypes.size > 0">
            AND o.order_type not IN
            <foreach collection="notOrderTypes" item="orderType" open="(" separator="," close=")">
                #{orderType}
            </foreach>
        </if>
        <if test="orderTypes != null  and orderTypes.size > 0">
            AND o.order_type IN
            <foreach collection="orderTypes" item="orderType" open="(" separator="," close=")">
                #{orderType}
            </foreach>
        </if>
        <choose>
            <when test="ids == null ">
                and o.order_status = 0
                <if test="deliveryTimeStart !=null and deliveryTimeEnd != null and deliveryTimeStart != '' and deliveryTimeEnd != '' ">
                    AND (
                    (
                    o.order_time  BETWEEN #{deliveryTimeStart} and #{deliveryTimeEnd}
                    )
                    or
                    (
                    o.real_order_time BETWEEN #{deliveryTimeStart} and #{deliveryTimeEnd}
                    and o.real_order_time is not null
                    and o.real_sub_order_done_status=1
                    )
                    )
                </if>
                <if test="storeIds !=null">
                    AND o.store_id IN
                    <foreach collection="storeIds" item="id" open="(" separator="," close=")">
                        #{id}
                    </foreach>
                </if>
            </when>
            <when test="ids != null">
                and o.id IN
                <foreach collection="ids" item="id" open="(" separator="," close=")">
                    #{id}
                </foreach>
            </when>
        </choose>
        ) t
        LEFT JOIN t_store s ON s.id = t.store_id
        LEFT JOIN t_store_settlement ss ON ss.store_id = s.id
        LEFT JOIN t_settlement settle ON ss.settlement_customer_id = settle.id
    </select>

    <select id="findTOrderItemByOrderIds" resultType="com.pinshang.qingyun.order.mapper.entry.settlement.SettleOrderItemEntry">
        SELECT
            t.order_id kid,
            t.id itemId,
            t.commodity_id commodityId,

            t.commodity_num quantity,
            t.commodity_price unitPrice,
            t.total_price totalPrice,

            t.commodity_price AS deliveryUnitPrice,
            t.real_total_price AS deliveryTotalPrice,
            t.real_quantity AS deliveryQuantity,

            now() createTime,
            cate1.cate_name categoryFirstName,
            cate2.cate_name categorySecondName,
            cate3.cate_name commodityThirdName,
            c.commodity_name AS commodityName,
            c.commodity_code AS commodityCode,
            c.commodity_spec AS commoditySpec,
            c.commodity_first_id AS categoryFirstId,
            c.commodity_second_id AS categorySecondId,
            c.commodity_third_id AS commodityThirdId,
            c.commodity_unit_id commodityUnitId,
            c.tax_rate_id taxRateId,
            c.tax_rate rate,
            d.option_name commodityUnit
        FROM
            t_order_list_gift t
            LEFT JOIN t_commodity c ON c.id = t.commodity_id
            LEFT JOIN t_dictionary d ON d.id = c.commodity_unit_id
            LEFT JOIN t_category cate1 ON c.commodity_first_id = cate1.id
            LEFT JOIN t_category cate2 ON c.commodity_second_id = cate2.id
            LEFT JOIN t_category cate3 ON c.commodity_third_id = cate3.id
            where t.order_id in
            <foreach collection="orderIds" item="orderId" open="(" close=")" separator=",">
                #{orderId}
            </foreach>
    </select>

</mapper>