<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.pinshang.qingyun.order.mapper.XdaPayBillMapper">

    <insert id="insertXdaPayBill" parameterType="com.pinshang.qingyun.order.model.recharge.XDAPayBill" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO t_xda_pay_bill
            (store_id,bill_code,refer_type,refer_id,pay_amount,pay_type,bill_status,tn,trade_bill_code,prepay_json_alipay,prepay_json_wechat,prepay_json_mini,prepay_json_union,pay_time,create_time,update_time,create_id,update_id)
        VALUES
            (#{storeId},#{billCode},#{referType},#{referId},#{payAmount},#{payType},#{billStatus},#{tn},#{tradeBillCode},#{prepayJsonAlipay},#{prepayJsonWechat},#{prepayJsonMini},#{prepayJsonUnion},#{payTime},#{createTime},#{updateTime},#{createId},#{updateId})
    </insert>

    <update id="updateXdaPayBill" parameterType="com.pinshang.qingyun.order.vo.recharge.RechargeSuccessVo">
        UPDATE t_xda_pay_bill
        <set>
            <if test="referId != null">
                refer_id =#{referId},
            </if>
            <if test="billStatus != null">
                bill_status = #{billStatus},
            </if>
            <if test="tradeBillCode != '' and tradeBillCode != null">
                trade_bill_code =#{tradeBillCode},
            </if>
            <if test="payTime != null">
                pay_time =#{payTime},
            </if>
            <if test="updateTime != null">
                update_time =#{updateTime},
            </if>
            <if test="updateId != null">
                update_id =#{updateId},
            </if>
        </set>
        WHERE 1=1
        <if test="billCode != null">
            AND bill_code = #{billCode}
        </if>
    </update>

    <select id="findXdaPayBillByIds" resultType="com.pinshang.qingyun.order.mapper.entry.recharge.XdaPayBillEntry">
        SELECT
            id,store_id, bill_code,refer_type,refer_id,pay_amount,pay_type,bill_status,tn,trade_bill_code,prepay_json_alipay,prepay_json_wechat,prepay_json_mini,prepay_json_union,create_id
        FROM
            t_xda_pay_bill
        <where>
            bill_code IN
            <foreach collection="billCodes" open="(" separator="," close=")" item="item">
                #{item}
            </foreach>
        </where>
    </select>

    <select id="findXdaPayBillJob" resultType="com.pinshang.qingyun.order.mapper.entry.recharge.XdaPayBillJobEntry">
        SELECT
            job.pay_bill_id,
            bill.bill_code,
            bill.trade_bill_code,
            bill.pay_type,
            bill.bill_status,
            bill.store_id
        FROM
            t_xda_pay_bill_job job INNER JOIN t_xda_pay_bill bill on job.pay_bill_id = bill.id
        WHERE
            bill.create_time <![CDATA[ < ]]> date_sub(date_format(NOW(), '%Y-%m-%d %H:%i'), interval 2 minute)
    </select>

    <insert id="insertXdaPayBillJob" parameterType="java.lang.Long">
        INSERT INTO t_xda_pay_bill_job(pay_bill_id) VALUES (#{payBillId})
    </insert>

    <delete id="deleteXdaPayBillJob">
        DELETE FROM t_xda_pay_bill_job WHERE pay_bill_id = #{payBillId}
    </delete>

</mapper>