<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.pinshang.qingyun.order.mapper.OrderListGiftMapper">


    <resultMap id="orderListGiftResultMap" type="com.pinshang.qingyun.order.model.order.OrderListGift">
        <result property="id" column="id"/>
        <result property="orderId" column="order_id"/>
        <result property="commodityId" column="commodity_id"/>
        <result property="commodityNum" column="commodity_num"/>
        <result property="totalPrice" column="total_price"/>
        <result property="commodityPrice" column="commodity_price"/>
        <result property="type" column="type"/>
        <result property="remark" column="remark"/>
    </resultMap>


    <update id="batchUpdateOrderGiftList">
        <foreach collection="orderGiftUpdateList" item="item" index="index" separator=";" open="" close="">
            UPDATE t_order_list_gift
            SET
             real_quantity = #{item.realQuantity},
             real_total_price = #{item.realTotalPrice}
            WHERE
            id = #{item.id}
        </foreach>
    </update>

	<update id="updateBatchOrderListGift" parameterType="java.util.List">
		<foreach collection="orderUpdateList" item="item" separator=";">
			UPDATE t_order_list_gift
			<set>
				<if test="item.commodityPrice != null">
					commodity_price = #{item.commodityPrice},
				</if>
				<if test="item.totalPrice != null">
					total_price = #{item.totalPrice},
				</if>
				<if test="item.realTotalPrice != null">
					real_total_price = #{item.realTotalPrice},
				</if>
			</set>
			WHERE id = #{item.id}
		</foreach>
	</update>

    <!-- 查询  订单项列表 -->
    <select id="selectOrderItemList" resultType="com.pinshang.qingyun.order.dto.xda.XdaOrderItemInfoODTO">
    	SELECT
			o.id AS orderId,
			c.id AS commodityId,
			CONCAT(unit.option_name,'|',c.commodity_name) AS commodityName,
			c.commodity_spec AS commoditySpec,
			unit.option_name AS commodityUnitName,
			olg.commodity_price AS commodityPrice,
			olg.commodity_num AS commodityQuantity,
			olg.total_price AS commodityAmount,
			olg.real_quantity AS realCommodityQuantity,
			olg.real_total_price AS realCommodityAmount,
			c.commodity_package_kind AS commodityPackageKind,
			olg.remark
		FROM t_order_list_gift olg
		INNER JOIN t_order o ON o.id = olg.order_id
		INNER JOIN t_commodity c ON c.id = olg.commodity_id
		LEFT JOIN t_dictionary unit ON unit.id = c.commodity_unit_id
		WHERE o.order_type = 8 AND o.process_status IN (13,15,19)
		  and olg.comb_type != 3
		AND o.id = #{orderId}
		;
    </select>
    
    <!-- 查询  订单项列表 -->
    <select id="selectPfOrderItemList" resultType="com.pinshang.qingyun.order.dto.pf.PfOrderItemInfoODTO">
    	SELECT
			o.id AS orderId,
			c.id AS commodityId,
			CONCAT(unit.option_name,'|',c.commodity_name) AS commodityName,
			c.commodity_spec AS commoditySpec,
			unit.option_name AS commodityUnitName,
			olg.commodity_price AS commodityPrice,
			olg.commodity_num AS commodityQuantity,
			olg.total_price AS commodityAmount,
			olg.real_quantity AS realCommodityQuantity,
			olg.real_total_price AS realCommodityAmount,
			c.commodity_package_kind AS commodityPackageKind,
			olg.remark
		FROM t_order_list_gift olg
		INNER JOIN t_order o ON o.id = olg.order_id
		INNER JOIN t_commodity c ON c.id = olg.commodity_id
		LEFT JOIN t_dictionary unit ON unit.id = c.commodity_unit_id
		WHERE o.order_type = 9 AND o.process_status IN (15,19)
		AND o.id = #{orderId}
		;
    </select>


	<delete id="deleteGiftOrderList">
		DELETE FROM t_order_list_gift WHERE type = 2  and order_id = #{orderId}
		and commodity_id in
		<foreach collection="commodityIdList" index="index" item="id" open="(" separator="," close=")">
			#{id}
		</foreach>
	</delete>

	<delete id="deleteOrderList">
		DELETE FROM t_order_list WHERE type = 2  and order_id = #{orderId}
		and commodity_id in
		<foreach collection="commodityIdList" index="index" item="id" open="(" separator="," close=")">
			#{id}
		</foreach>
	</delete>


	<select id="selectGiftOrderList" resultType="com.pinshang.qingyun.order.dto.OrderInfoODTO">
		SELECT
			ts.id subOrderId,ts.variety_total varietyTotal,tsi.id subOrderItemId,
			tsi.commodity_id
		FROM t_order t
		 LEFT JOIN t_sub_order ts on ts.order_id = t.id
		 LEFT JOIN t_sub_order_item tsi on tsi.sub_order_id = ts.id
		where t.id = #{orderId}
		  and tsi.price = 0
		 and tsi.commodity_id in
		<foreach collection="commodityIdList" index="index" item="id" open="(" separator="," close=")">
			#{id}
		</foreach>
	</select>

	<delete id="deleteSubOrderItem">
		DELETE FROM t_sub_order_item
		WHERE id in
		<foreach collection="subOrderItemIdList" index="index" item="itemId" open="(" separator="," close=")">
			#{itemId}
		</foreach>
	</delete>


	<select id="queryOrderGiftListByOrderId" resultType="com.pinshang.qingyun.order.model.order.OrderListGift">
		select
			t.id,
			t.commodity_id,
			t.commodity_num,
			t.total_price,
			t.commodity_price,
			t.real_quantity,
			t.real_total_price,
			t.type,
			t.comb_type,
			t.comb_commodity_id,
			t.price_promotion_id
		from t_order_list_gift t
		where t.order_id = #{orderId}
	</select>

	<select id="selectOrderListGiftJob" resultType="java.lang.Long">
		SELECT
			count(1)
		FROM t_order o
		LEFT JOIN t_order_list_gift olg ON olg.order_id = o.id
		<where>
			o.order_type NOT IN(14,15)
			<if test="null != startTime and null != endTime">
				and o.create_time BETWEEN #{startTime} AND #{endTime}
			</if>
		</where>
	</select>
	<select id="syncOrderItemGiftList" resultType="com.pinshang.qingyun.order.dto.sync.SyncOrderListODTO">
		SELECT
			olg.order_id AS orderId,
			olg.commodity_id AS commodityId,
			olg.commodity_num AS commodityNum,
			olg.real_quantity AS realQuantity,
			olg.total_price AS totalPrice,
			olg.commodity_price AS commodityPrice,
			olg.type AS type,
			olg.real_total_price AS realTotalPrice,
			olg.original_price AS originalPrice,
			olg.original_total_price AS originalTotalPrice,
			olg.comb_type AS combType,
			olg.comb_commodity_id AS combCommodityId,
			olg.price_promotion_id AS pricePromotionId
		FROM
			t_order_list_gift olg
			LEFT JOIN t_order o ON o.id = olg.order_id
		WHERE
			o.update_time BETWEEN #{bTime} AND #{eTime}
		  and o.order_type NOT IN (14,15)
			ORDER BY olg.id ASC
	</select>
</mapper>