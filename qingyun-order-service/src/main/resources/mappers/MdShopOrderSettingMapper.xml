<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.pinshang.qingyun.order.mapper.MdShopOrderSettingMapper">

	<select id="queryMdShopOrderSettingListByParams" resultType="com.pinshang.qingyun.order.mapper.entry.MdShopOrderSettingEntry" parameterType="com.pinshang.qingyun.order.vo.shop.MdShopOrderSettingVo" >
		SELECT
			sos.id  id,
			sos.commodity_code commodityCode,
			c.commodity_name commodityName,
			c.commodity_spec commoditySpec,
			sos.shop_type shopType,
		    sos.supplier_code supplierCode,
		    sos.supplier_name supplierName,
			sos.logistics_model logisticsModel,
			sos.delevery_time_range deleveryTimeRange,
			sos.change_price_status changePriceStatus,
		    sos.default_supplier_begin_time defaultSupplierBeginTime,
		    sos.default_supplier_end_time defaultSupplierEndTime,
		    sos.warehouse_id warehouseId,
		    sos.default_warehouse_begin_time defaultWarehouseBeginTime,
		    sos.default_warehouse_end_time defaultWarehouseEndTime,
		    sos.is_new isNew
          from t_md_shop_order_setting sos
		  INNER JOIN t_commodity c on c.id = sos.commodity_id
		WHERE 1=1
		<if test="enterpriseId !=null">
			and sos.enterprise_id =#{enterpriseId}
		</if>
		<if test="commodityCodeOrName!=null and commodityCodeOrName !='' ">
			AND (sos.commodity_code like concat('%',#{commodityCodeOrName},'%')   or  sos.commodity_name like concat('%',#{commodityCodeOrName},'%') )
		</if>
		<if test="supplierId!=null">
			AND sos.supplier_id =#{supplierId}
		</if>
		<if test="shopType != null">
			AND sos.shop_type =#{shopType}
		</if>
		<if test="logisticsModel!=null">
			AND sos.logistics_model =#{logisticsModel}
		</if>
		<if test="deleveryTimeRange!=null and deleveryTimeRange !='' ">
			AND sos.delevery_time_range = #{deleveryTimeRange}
		</if>
		<if test="changePriceStatus!=null">
			AND sos.change_price_status = #{changePriceStatus}
		</if>
		<if test="cateId1 != null">
			AND c.commodity_first_id = #{cateId1}
		</if>
		<if test="cateId2 != null">
			AND c.commodity_second_id = #{cateId2}
		</if>
		<if test="cateId3 != null">
			AND c.commodity_third_id = #{cateId3}
		</if>
		ORDER BY sos.commodity_code,sos.supplier_code
	</select>

	<update id="updateMdShopOrderSettingByPrimaryKey" parameterType="com.pinshang.qingyun.order.model.shop.MdShopOrderSetting">
		UPDATE t_md_shop_order_setting
		set
		<if test="updateId !=null ">
			update_id =#{updateId} ,
		</if>
		<if test="updateTime !=null ">
			update_time =#{updateTime} ,
		</if>
		<if test="logisticsModel !=null">
			logistics_model =#{logisticsModel},
		</if>
		<if test="deleveryTimeRange !=null">
			delevery_time_range =#{deleveryTimeRange},
		</if>
		<if test="changePriceStatus !=null">
			change_price_status =#{changePriceStatus}
		</if>

		WHERE  id = #{id}
	</update>


	<select id="queryDefaultMdShopOrderSetting" resultType="com.pinshang.qingyun.order.mapper.entry.MdShopOrderSettingEntry">
		SELECT
		sos.id  id,
		sos.commodity_id commodityId,
		sos.commodity_code commodityCode,
		sos.commodity_name commodityName,
		sos.shop_type shopType,
		sos.supplier_id supplierId,
		sos.supplier_code supplierCode,
		sos.supplier_name supplierName,
		sos.logistics_model logisticsModel,
		sos.delevery_time_range deleveryTimeRange,
		sos.change_price_status changePriceStatus,
	    sos.is_new isNew
		from t_md_shop_order_setting sos
		WHERE  sos.shop_type = -1
		  and sos.commodity_id = #{commodityId}
		AND sos.supplier_id = #{supplierId}
	</select>

	<select id="queryMdShopOrderSettingListByCommodityIds" resultType="com.pinshang.qingyun.order.mapper.entry.MdShopOrderSettingEntry">
		SELECT
			sos.id  id,
			sos.commodity_id commodityId,
			sos.commodity_id productId,
			sos.commodity_code,
			sos.commodity_name,
			sos.shop_type,
			sos.supplier_id,
			sos.supplier_code,
			sos.supplier_name,
			sos.default_supplier_begin_time,
		    sos.default_supplier_end_time,
		    sos.warehouse_id,
		    sos.default_warehouse_begin_time,
		    sos.default_warehouse_end_time,
			sos.logistics_model,
			sos.delevery_time_range,
			sos.change_price_status
		FROM t_md_shop_order_setting sos
		WHERE 1=1
		  <choose>
			  <when test="isCustom"> AND sos.shop_type = #{shopType} </when>
			  <otherwise> AND sos.shop_type = -1 </otherwise>
		  </choose>
		  AND sos.commodity_id IN <foreach collection="commodityIds" open="(" close=")" item="id" separator=","> #{id} </foreach>
		  AND sos.is_new = 1
	</select>

	<sql id="SHOP_ORDER_SETTING">
		SELECT
			sos.id  id,
			sos.commodity_id commodityId,
			sos.commodity_code commodityCode,
			c.commodity_name commodityName,
			sos.shop_type shopType,
			sos.supplier_id supplierId,
			sos.supplier_code supplierCode,
			sos.supplier_name supplierName,
			sos.logistics_model logisticsModel,
			sos.delevery_time_range deleveryTimeRange,
			sos.change_price_status changePriceStatus
		from t_md_shop_order_setting sos
				 INNER JOIN t_commodity c on c.id = sos.commodity_id
		WHERE 1=1
	</sql>

	<select id="queryMdShopOrderSettingListByMap" resultType="com.pinshang.qingyun.order.mapper.entry.MdShopOrderSettingEntry">
		select
		a.*
		from (
		<include refid="SHOP_ORDER_SETTING" />
		<if test="shopType != null">
			AND (sos.shop_type = #{shopType}  or sos.shop_type = -1 )
		</if>

		<if test="map!=null">
			AND
			<foreach collection="map" index="key" item="value" open="(" separator="OR" close=")">
				(sos.commodity_id = #{key} AND sos.supplier_id = #{value})
			</foreach>
		</if>
		order by sos.create_time desc
		) a
		GROUP BY a.commodityId
	</select>


	<select id="queryCommodityEntryByCommodityCodes" resultType="com.pinshang.qingyun.order.mapper.entry.CommodityEntry">
		SELECT
			 tc.id commodityId,
			 tc.commodity_code commodityCode,
			 tc.commodity_name commodityName,
			 tc.commodity_spec commoditySpec
		 FROM  t_commodity tc
		 WHERE tc.commodity_code in
			<foreach collection="commodityCodeList" item="code" open="(" close=")" separator=",">
				#{code}
			</foreach>
	</select>


	<select id="queryMdShopOrderSettingByCommIds" resultType="com.pinshang.qingyun.order.mapper.entry.MdShopOrderSettingEntry">
		  SELECT
			   tmsos.commodity_id,
			   tmsos.supplier_id,
			   tmsos.shop_type
		  FROM  t_md_shop_order_setting tmsos
		  WHERE tmsos.commodity_id in
		<foreach collection="commodityIdList" item="item" open="(" close=")" separator=",">
			#{item}
		</foreach>
	</select>


	<select id="queryCommonSettingByParam" resultType="com.pinshang.qingyun.order.mapper.entry.MdShopOrderSettingEntry">
		SELECT
			sos.id  id,
			sos.commodity_id commodityId,
			sos.commodity_id productId,
			sos.commodity_code,
			sos.commodity_name,
			sos.supplier_id,
			sos.supplier_code,
			sos.supplier_name,
			sos.default_supplier_begin_time,
			sos.default_supplier_end_time,
			sos.warehouse_id,
			sos.default_warehouse_begin_time,
			sos.default_warehouse_end_time,
			sos.logistics_model,
			sos.delevery_time_range,
			sos.change_price_status,
			sos.is_new isNew
		FROM t_md_shop_order_setting sos
		WHERE  sos.commodity_id = #{commodityId}
        AND  sos.supplier_id = #{supplierId}
        AND  sos.shop_type = -1
	</select>


	<select id="getEndTimeList" resultType="java.lang.String">
		 SELECT
			tt.endTime
		FROM
			(
				SELECT DISTINCT
					t.default_supplier_end_time endTime
				FROM
					t_md_shop_order_setting t
				WHERE
					t.default_supplier_end_time IS NOT NULL
				UNION
					SELECT DISTINCT
						t.default_warehouse_end_time endTime
					FROM
						t_md_shop_order_setting t
					WHERE
						t.default_warehouse_end_time IS NOT NULL
			) tt
			WHERE 1=1
			<![CDATA[ and tt.endTime > #{nowHour} ]]>
		  ORDER BY endTime
	</select>


	<select id="queryCommodityIdsByStoreLogisticsModel" resultType="java.lang.Long">
		 SELECT
		       DISTINCT t.commodity_id
		    from t_md_shop_order_setting t
		    where (t.shop_type = #{shopType} OR t.shop_type = -1)
		    and t.logistics_model = #{logisticsModel}
            and t.is_new = 1
	</select>

	<select id="findDirectCommodityList" resultType="java.lang.Long">
		SELECT
			DISTINCT mdset.commodity_id
		FROM t_md_shop_order_setting mdset
		where (mdset.shop_type = -1 or mdset.shop_type = #{shopType} )
		  AND mdset.supplier_id = #{supplierId} AND mdset.is_new = 1 and mdset.logistics_model = 0
	</select>


	<select id="queryOrderSupplierBlackPage" resultType="com.pinshang.qingyun.order.dto.order.SupplierBlackODTO" parameterType="com.pinshang.qingyun.order.dto.order.SupplierBlackVo" >
		SELECT
			t.supplier_id
		from t_order_supplier_blacklist t
		WHERE 1=1
		<if test="supplierId != null">
			AND t.supplier_id = #{supplierId}
		</if>
		order by t.supplier_id
	</select>

	<delete id="deleteOrderSupplierBlack">
		DELETE FROM t_order_supplier_blacklist WHERE supplier_id = #{supplierId}
	</delete>

	<select id="countOrderSupplierBlack" resultType="java.lang.Long" >
		SELECT count(1)  from t_order_supplier_blacklist t
		WHERE  t.supplier_id = #{supplierId}
	</select>

	<insert id="insertOrderSupplierBlack" >
		INSERT INTO t_order_supplier_blacklist
		(supplier_id,create_id,create_time,update_id,update_time)
		VALUES
		(#{supplierId},#{userId},now(),#{userId},now())
	</insert>

	<select id="queryAllOrderSupplierBlackList" resultType="java.lang.Long" >
		SELECT t.supplier_id from t_order_supplier_blacklist t
	</select>

</mapper>