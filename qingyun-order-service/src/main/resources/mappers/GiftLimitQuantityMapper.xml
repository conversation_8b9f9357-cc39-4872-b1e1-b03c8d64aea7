<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.pinshang.qingyun.order.mapper.giftLimit.GiftLimitQuantityMapper" >


    <select id="queryGiftUsedQuantityList" resultType="com.pinshang.qingyun.order.dto.giftLimit.GiftLeftQuantityODTO">
        SELECT
            t.promotion_id giftPrommotionId,
            t.commodity_id,
            sum(t.quantity) as totalQuantity
        FROM t_gift_limit_quantity t
        WHERE  t.promotion_id IN
        <foreach collection="promotionIdList" index="index" item="promotionId" open="(" separator="," close=")">
            #{promotionId}
        </foreach>

        and t.commodity_id IN
        <foreach collection="commodityIdList" index="index" item="commodityId" open="(" separator="," close=")">
            #{commodityId}
        </foreach>

        <if test="null != orderId">
            and t.order_id != #{orderId}
        </if>
        group by t.promotion_id,t.commodity_id
    </select>
</mapper>