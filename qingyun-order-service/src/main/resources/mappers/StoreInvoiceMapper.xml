<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.pinshang.qingyun.order.mapper.StoreInvoiceMapper">

    <sql id="column">
        s.id storeId,
        s.store_code storeCode,
        s.store_name storeName,
        si.uid invoiceUid,
        si.invoice_type invoiceType,
        si.invoice_title invoiceTitle,
        si.tax_code custtaxnr,
        si.addr_tel custaddrtel,
        si.bank_acct custbankacct
    </sql>
    <select id="findListByIds" resultType="com.pinshang.qingyun.order.mapper.entry.store.StoreInviceEntry">
        SELECT
              <include refid="column"/>
        FROM
             t_invoice_store si,
             t_store s
        WHERE
                si.store_id = s.id
                and si.uid = #{uid}
    </select>

    <select id="findListByStoreIds" resultType="com.pinshang.qingyun.order.mapper.entry.store.StoreInviceEntry">
        SELECT
          <include refid="column"/>
        FROM
        t_invoice_store si,
        t_store s
        WHERE
        si.store_id = s.id
        and s.id IN
        <foreach collection="storeIds" item="id" separator="," open="(" close=")" >
            #{id}
        </foreach>

    </select>

</mapper>