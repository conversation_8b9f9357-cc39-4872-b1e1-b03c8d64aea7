<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.pinshang.qingyun.order.mapper.PayTypeMapper">

    <resultMap id = "queryAllValidPayTypeMap" type="com.pinshang.qingyun.order.mapper.entry.payType.PayTypeAndTipEntry">
        <id property="id" column="id" />
        <result property="payType" column="pay_type" />
        <result property="code" column="code" />
        <result property="name" column="name" />
        <result property="status" column="status" />
        <collection property="payTypeAndTips" column="id" javaType="ArrayList"
                    ofType="com.pinshang.qingyun.order.mapper.entry.payType.PayTypeTipEntry" select="queryValidPayMethodTip"/>
    </resultMap>

    <resultMap id="queryAllValidPayTypeTipMap" type="com.pinshang.qingyun.order.mapper.entry.payType.PayTypeTipEntry" >
        <id property="id" column="id" jdbcType="INTEGER" javaType="java.lang.Long" />
        <result property="paytypeId" column="paytype_id" jdbcType="INTEGER" javaType="java.lang.Long" />
        <result property="tip" column="tip" jdbcType="VARCHAR" javaType="java.lang.String" />
        <result property="status" column="status" jdbcType="TINYINT" javaType="java.lang.Integer" />
        <result property="beginTime" column="begin_time" jdbcType="DATE" javaType="java.util.Date" />
        <result property="endTime" column="end_time" jdbcType="DATE" javaType="java.util.Date" />
    </resultMap>


    <select id="queryValidPayMethodAndTip" resultMap="queryAllValidPayTypeMap">
        SELECT
            id,code,pay_type,name,status
        FROM
            t_xda_paytype
        WHERE
            status = 1
    </select>

    <select id="queryValidPayMethodTip" parameterType="java.lang.Integer"  resultMap="queryAllValidPayTypeTipMap">
        SELECT
            id,paytype_id,tip,status,begin_time,end_time
        FROM
            t_xda_paytype_tip
        WHERE
            paytype_id = #{id} AND
            status = 1 AND
            <![CDATA[ begin_time <= now() AND end_time >= now() ]]>
    </select>
</mapper>