<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.pinshang.qingyun.order.mapper.PurchaseListMapper">

	<select id="findConnectionPurchaseDetailNew" resultType="com.pinshang.qingyun.order.mapper.entry.purchase.ConnectionPurchaseDetailEntry" parameterType="com.pinshang.qingyun.order.vo.purchase.PurchaseListVo" >
		SELECT
			olg.commodity_id as commodityId,
			sum(olg.quantity) as commodityNum,
			t.store_type_id
		FROM t_order o
		INNER JOIN t_sub_order so on so.order_id = o.id
		INNER JOIN t_sub_order_item olg on olg.sub_order_id = so.id
		LEFT JOIN t_store t ON t.id = o.store_id
		where 1=1 and so.logistics_model = 2
		AND o.order_time = #{orderDate}
		AND o.order_status = 0
		<if test="storeTypeId != null">
			AND t.store_type_id = #{storeTypeId}
		</if>
		<if test="commodityIdList != null and commodityIdList.size() > 0">
			AND olg.commodity_id in <foreach collection="commodityIdList" item="id" open="(" separator="," close=")"> #{id} </foreach>
		</if>
		<if test="commodityIdList2 != null and commodityIdList2.size() > 0">
			AND olg.commodity_id in <foreach collection="commodityIdList2" item="id" open="(" separator="," close=")"> #{id} </foreach>
		</if>
		<if test="createOrderBeginDate != null and createOrderBeginDate != '' and createOrderEndDate != null and createOrderEndDate != '' ">
			and o.create_time BETWEEN #{createOrderBeginDate} and #{createOrderEndDate}
		</if>
		GROUP BY t.store_type_id,olg.commodity_id
		order by t.store_type_id,olg.commodity_id
	</select>


	<select id="findConnectionPurchaseDetail" resultType="com.pinshang.qingyun.order.mapper.entry.purchase.ConnectionPurchaseDetailEntry" parameterType="com.pinshang.qingyun.order.vo.purchase.PurchaseListVo" >
		SELECT
		olg.commodity_id as commodityId,
		tca.cate_name as categoryName,
		c.commodity_code as commodityCode,
		c.commodity_name as commodityName,
		c.commodity_spec as commoditySpec,
		c.box_capacity as boxCapacity,
		d.option_name as commodityUnit,
		sum(olg.quantity) as commodityNum,
		cr.ratio * sum(olg.quantity) as commodityOrderNum,
		d1.option_name storeTypeName
		FROM t_order o
		INNER JOIN t_sub_order so on so.order_id = o.id
		INNER JOIN t_sub_order_item olg on olg.sub_order_id = so.id
		INNER JOIN t_commodity c ON c.id =olg.`commodity_id`
		LEFT JOIN t_dictionary d ON d.id = c.commodity_unit_id
		INNER JOIN t_category tca ON c.commodity_third_id = tca.id
		LEFT JOIN t_store t ON t.id = o.store_id
		LEFT JOIN t_dictionary d1 ON d1.id = t.store_type_id
        LEFT JOIN t_commodity_relation cr on olg.commodity_id = cr.pack_commodity_id
		where 1=1 and so.logistics_model = 2
		AND o.order_time = #{orderDate}
		AND o.order_status = 0
		<if test="storeTypeId != null">
			AND t.store_type_id = #{storeTypeId}
		</if>
		<if test="commodityKey!=null and commodityKey !='' ">
			and (c.`commodity_name` like concat('%',#{commodityKey},'%') or c.`commodity_aid` like concat('%',#{commodityKey},'%') or c.`commodity_code` like concat('%',#{commodityKey},'%') or c.`bar_code` like concat('%',#{commodityKey},'%')  )
		</if>
		<if test="barCode!=null and barCode !='' ">
			and c.`commodity_code` = #{barCode}
		</if>
		<if test="cateId1 != null">
			AND c.commodity_first_id = #{cateId1}
		</if>
		<if test="cateId2 != null">
			AND c.commodity_second_id = #{cateId2}
		</if>
		<if test="cateId3 != null">
			AND c.commodity_third_id = #{cateId3}
		</if>
		<if test="commodityIdList != null and commodityIdList.size() > 0">
			AND c.id in <foreach collection="commodityIdList" item="id" open="(" separator="," close=")"> #{id} </foreach>
		</if>
		<if test="createOrderBeginDate != null and createOrderBeginDate != '' and createOrderEndDate != null and createOrderEndDate != '' ">
			and o.create_time BETWEEN #{createOrderBeginDate} and #{createOrderEndDate}
		</if>
		GROUP BY t.store_type_id,olg.commodity_id
		order by t.store_type_id,olg.commodity_id
	</select>

	<select id="findSendPurchaseByPage" resultType="com.pinshang.qingyun.order.mapper.entry.purchase.ConnectionPurchaseEntry" parameterType="com.pinshang.qingyun.order.vo.purchase.PurchaseListVo" >
		SELECT
			p.supplier_id,
			date_format(p.order_time, '%Y-%m-%d') as orderDate,
			count(*) AS preOrderNum,
			count(purchase_order_id IS NOT NULL) AS purchaseOrderNum
		FROM t_md_preorder p
		WHERE p.logistics_model = 0
			and p.order_status = 2
			and p.order_time = #{orderDate}
			<if test="supplierId!=null and supplierId !='' ">
				and p.supplier_id = #{supplierId}
			</if>
		group by p.supplier_id
		order by null
	</select>

	<select id="findSendPurchaseDetail" resultType="com.pinshang.qingyun.order.mapper.entry.purchase.SendPurchaseDetailEntry" parameterType="com.pinshang.qingyun.order.vo.purchase.PurchaseListVo" >
		SELECT
			DATE_FORMAT(p.order_time,'%Y-%m-%d') as orderDate,
			st.id as storeId,
			st.store_name as storeName,
			p.order_code as orderCode,
			p.total_price as totalPrice,
			p.purchase_code as purchaseCode
		FROM t_md_preorder p
		INNER JOIN t_store st ON st.id = p.store_id
		where p.logistics_model = 0 AND p.order_time = #{orderDate} AND p.`supplier_id` is not null
		AND p.order_status =2
		AND p.supplier_id = #{supplierId}
	</select>

</mapper>