<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.pinshang.qingyun.order.mapper.auto.AutoCommodityMapper">
    <insert id="saveList">
        INSERT INTO t_md_auto_commodity(commodity_id,create_id,create_time,update_id,update_time)
        VALUES
        <foreach collection="commodityIds" separator="," item="commodityId">
            ( #{commodityId}, #{createId}, NOW(),#{createId},NOW() )
        </foreach>

    </insert>

    <select id="queryCommodityListByIdto" resultType="com.pinshang.qingyun.order.dto.AutoCommodityODTO">

        select
        c.id as commodityId,
        c.commodity_code as commodityCode,
        c.bar_code as barCode,
        c.commodity_name as commodityName,
        c.commodity_spec as commoditySpec,
        td.option_name AS commodityUnitName,
        fcate.cate_name AS firstCateName,
        scate.cate_name AS secondCateName,
        tcate.cate_name AS thirdCateName,
        c.commodity_is_quick_freeze as isFreeze
        from t_md_auto_commodity ac
        left join t_commodity c on ac.commodity_id = c.id
        LEFT JOIN t_category fcate ON c.commodity_first_id = fcate.id
        LEFT JOIN t_category scate ON c.commodity_second_id = scate.id
        LEFT JOIN t_category tcate ON c.commodity_third_id = tcate.id
        LEFT JOIN t_dictionary td ON c.commodity_unit_id = td.id
        where 1=1
        <if test="commodityId != null and commodityId !=''">
            and c.`id` = #{commodityId}
        </if>
        <if test="barCode != null and barCode !='' ">
            and c.`id` = (SELECT commodity_id FROM t_commodity_bar_code WHERE bar_code = #{barCode})
        </if>
        <if test="cateId1 != null">
            AND c.commodity_first_id = #{cateId1}
        </if>
        <if test="cateId2 != null">
            AND c.commodity_second_id = #{cateId2}
        </if>
        <if test="cateId3 != null">
            AND c.commodity_third_id = #{cateId3}
        </if>
        <if test="isFreeze != null">
            AND c.commodity_is_quick_freeze = #{isFreeze}
        </if>
        ORDER BY c.commodity_code ASC
    </select>
    <select id="queryCommodityIds" resultType="java.lang.String">
        SELECT commodity_id FROM t_md_auto_commodity
        <where>
            commodity_id IN
            <foreach collection="commodityIds" open="(" separator="," close=")" item="item">
                #{item}
            </foreach>
        </where>
    </select>
    <select id="queryDirectSendingCommodity" resultType="com.pinshang.qingyun.order.dto.XDCommodityODTO">
        SELECT c.id commodityId,
               c.commodity_code,
               c.commodity_name,
               c.`commodity_spec`,
               IFNULL(c.commodity_package_spec,1) commodityPackageSpec,
               c.`commodity_is_quick_freeze` frozen,
               c.`sales_box_capacity`,
               c.`xd_sales_box_capacity`,
               c.`commodity_state` commodityStatus,
               c.logistics_model
        FROM t_md_auto_commodity sc
                 LEFT JOIN t_commodity c ON sc.commodity_id = c.id
        WHERE c.logistics_model = #{logisticsModel}


    </select>


    <select id="queryAutoCommodityCodes" resultType="java.lang.String">
        SELECT
             tc.commodity_code
        FROM t_md_auto_commodity t
         left join t_commodity tc on tc.id = t.commodity_id
    </select>

</mapper>