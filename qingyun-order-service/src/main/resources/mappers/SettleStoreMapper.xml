<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.pinshang.qingyun.order.mapper.SettleStoreMapper">

    <!--条件查询客户-->
    <select id="selectStore" resultType="com.pinshang.qingyun.order.mapper.entry.StoreEntry">
        SELECT
            ts.id AS scopeId,
            ts.store_code AS storeCode,
            ts.store_name AS storeName,
            (CASE ts.store_status  WHEN 1 THEN '停用'  WHEN 0 THEN  '启用' WHEN -1 THEN '待指定线路' WHEN 2 THEN '待指定结账客户' END)AS storeStatus,
            tms.shop_name AS shopName
        FROM
            t_store ts
            LEFT JOIN t_md_shop tms ON tms.store_id = ts.id
        WHERE
        1=1
        <if test="null != storeCode and storeCode != '' ">
            AND (ts.store_code LIKE CONCAT("%", #{storeCode} ,"%")
            OR ts.store_name LIKE CONCAT("%", #{storeCode} ,"%"))
        </if>
        <if test="null != shopName and shopName != '' ">
            AND  tms.shop_name LIKE CONCAT("%", #{shopName} ,"%")
        </if>
        <if test="null != shopId">
            AND  tms.id = #{shopId}
        </if>
    </select>
    <!--条件查询结账客户-->
    <select id="selectSettleStore" resultType="com.pinshang.qingyun.order.mapper.entry.SettlementStoreEntry">
        SELECT
            ts.id AS storeId,
            ts.customer_code AS customerCode,
            ts.customer_name AS customerName,
            ts.xs_start_bill_date AS xsStartBillDate,
            (CASE ts.customer_status WHEN 1 THEN '停用'  WHEN 0 THEN  '启用' END) AS customerStatus
        FROM
            t_settlement ts
        WHERE
        1=1
        <if test="null != customerCode and customerCode != '' ">
            AND (ts.customer_code LIKE CONCAT("%", #{customerCode} ,"%")
            OR ts.customer_name LIKE CONCAT("%", #{customerCode} ,"%"))
        </if>
    </select>
    <select id="selectStoreByStores" resultType="com.pinshang.qingyun.order.mapper.entry.StoreEntry">
        SELECT
        ts.id AS scopeId,
        ts.store_code AS storeCode,
        ts.store_name AS storeName,
        (CASE ts.store_status  WHEN 1 THEN '停用'  WHEN 0 THEN  '启用' WHEN -1 THEN '待指定线路' WHEN 2 THEN '待指定结账客户' END)AS storeStatus,
        tms.shop_name AS shopName
        FROM
        t_store ts
        LEFT JOIN t_md_shop tms ON tms.store_id = ts.id
        WHERE
        1=1
        <if test="null != storeCode and storeCode != '' ">
            AND ts.store_code = #{storeCode}
        </if>
    </select>

</mapper>