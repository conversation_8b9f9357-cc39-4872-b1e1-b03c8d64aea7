<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.pinshang.qingyun.order.mapper.ShopCommodityMapper">

    <select id="listByShopAndCommodityCode" resultType = "com.pinshang.qingyun.order.vo.auto.ImportStockQuantityVO">
        SELECT c.id AS commodityId,
               c.commodity_code AS commodityCode,
               c.commodity_name AS commodityName,
               c.logistics_model AS logisticsModel
        FROM t_xs_shop_commodity sc
        INNER JOIN t_commodity c ON sc.commodity_id = c.id AND sc.shop_id = #{shopId}
        <where>
            and c.product_type != 2
            AND c.commodity_code IN
            <foreach collection="list" item="commodityCode" open="(" close=")" separator=",">
                #{commodityCode}
            </foreach>
        </where>
    </select>

    <select id="commodityInfo" resultType = "com.pinshang.qingyun.order.vo.auto.AutoCommodityVO">
        SELECT
           c.id AS commodityId,
           c.commodity_code AS commodityCode,
           c.commodity_name AS commodityName,
           c.commodity_spec AS commoditySpec,
           c.commodity_unit_id AS commodityUnitId,
           c.logistics_model AS logisticsModel,
           c.commodity_package_spec AS commodityPackageSpec
        FROM t_xs_shop_commodity sc
        INNER JOIN t_commodity c ON sc.commodity_id = c.id AND sc.shop_id = #{shopId}
        WHERE sc.commodity_id = (SELECT commodity_id FROM t_commodity_bar_code WHERE bar_code = #{barCode} )
    </select>
    <select id="queryShopCommodityWeightPrice"
            resultType="com.pinshang.qingyun.order.vo.shop.ShopCommodityWeightPriceInfoVO">
        SELECT
        t.shop_id shopId,
        t.commodity_id commodityId,
        t.weight_price weightPrice
        FROM t_xs_shop_commodity t
        where 1=1
        <if test="shopIdList != null and shopIdList.size() > 0">
            AND t.shop_id IN
            <foreach collection="shopIdList" item="id" open="(" close=")" separator=",">
                #{id}
            </foreach>
        </if>
        <if test="commodityIdList != null and commodityIdList.size() > 0">
            AND t.commodity_id IN
            <foreach collection="commodityIdList" item="id" open="(" close=")" separator=",">
                #{id}
            </foreach>
        </if>

    </select>
</mapper>