<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.pinshang.qingyun.order.mapper.CommoditySaleMultiDayStatisticsMapper" >
  <!-- 更新销售统计数据，先删除数据，再统计 -->
  <insert id="insertSaleStatistics" parameterType="com.pinshang.qingyun.order.model.order.XdaCommoditySaleDayStatistics">
    INSERT INTO t_xda_commodity_sale_multi_day_statistics(commodity_id,total_quantity,create_time)
    VALUES
    <foreach collection="list" separator="," item="item">
      (  #{item.commodityId}, #{item.totalQuantity}, NOW() )
    </foreach>
  </insert>

</mapper>