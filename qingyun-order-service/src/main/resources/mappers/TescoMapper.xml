<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.pinshang.qingyun.order.mapper.TescoMapper" >

    <select id="findTescoList" resultType="com.pinshang.qingyun.order.mapper.entry.externalDocking.TescoEntry">
        SELECT
            o.id,
            c.commodity_code commodityCode,
            rtc.rt_commodity_name rtCommodityName,
            rtc.rt_commodity_code rtCommodityCode,
            sum(ol.commodity_num) commodityNum,
            d.option_name unit,
            o.order_time orderTime,
            '上海清美绿色食品（集团）有限公司' company,
            '上海市浦东新区宣桥镇三灶工业园区宣春路201号' address,
            '8009880026' rtcAdddress,
            rts.rt_shop_code rtShopCode,
            rts.rt_shop_name rtShopName,
            rts.receiving_unit email,
            '上海清美绿色食品（集团）有限公司' madeCompany,
            DATE_FORMAT(date_add(o.order_time , interval -1 day),'%Y-%m-%d') deliveryDate,
            DATE_FORMAT(date_add(o.order_time , interval -1 day),'%Y-%m-%d') deliveryDatePc,
            '' a1,
            '' a2,
            '' a3,
            '上海浦东新区' a4
        FROM
        t_rt_shop rts
        INNER JOIN t_order o ON rts.qm_store_id = o.store_id
        AND rts.store_short_name = 3
        INNER JOIN t_order_list_gift ol ON o.id = ol.order_id
        INNER JOIN t_commodity c ON ol.commodity_id = c.id
        INNER JOIN t_dictionary d ON c.commodity_unit_id = d.id
        INNER JOIN t_rt_commodity rtc ON ol.commodity_id = rtc.qm_commodity_id
        AND rtc.store_short_name = 3
        <where>
            o.order_status = 0
            <if test="null != storeId">
                and rts.qm_store_id = #{storeId}
            </if>
            <if test="null != tescoName and tescoName != ''">
                and (rts.rt_shop_code LIKE concat('%',#{tescoName},'%') or rts.rt_shop_name LIKE concat('%',#{tescoName},'%'))
            </if>
            <if test="null != orderTime and orderTime != ''">
                and o.order_time = #{orderTime}
            </if>
        </where>
        group by ol.commodity_id,rts.qm_store_code
        ORDER BY rts.rt_shop_code ASC
    </select>
</mapper>