<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.pinshang.qingyun.order.mapper.ShoppingCartMapper">
	<select id="queryCommoditySplitInfo" resultType="com.pinshang.qingyun.order.mapper.entry.order.SplitOrderListEntry">
		SELECT 
			  c.`logistics_model`,
			  c.`id` AS commodity_id
			from t_commodity c
			LEFT JOIN t_xs_shop_commodity_purchase_status txsc ON txsc.commodity_id = c.id
		    where txsc.shop_id = (select id from t_md_shop where store_id = #{storeId}) and c.id = #{commodityId} and c.`enterprise_id` =#{enterpriseId}
			<if test="ifAdmin == true">
				AND c.status = 1 and  txsc.commodity_purchase_status = 1
			</if>
			<if test="ifAdmin == false">
				AND c.status = 1 and c.commodity_state = 1 and txsc.commodity_purchase_status = 1
			</if>
			limit 1 
	</select>

	
	<resultMap id="shoppingCartEntry" type="com.pinshang.qingyun.order.mapper.entry.order.ShoppingCartEntry">
		  <id column="id" property="id" jdbcType="VARCHAR" />
		  <result column="supplier_id" property="supplyId"/>
		  <result column="company_name" property="companyName" jdbcType="VARCHAR"/>
		  <result column="logistics_model" property="logisticsModel" jdbcType="INTEGER" />
		  <result column="create_id" property="createId" />
		  <result column="delevery_time_range" property="deleveryTimeRange" jdbcType="VARCHAR" />
		  <result column="warehouse_id" property="warehouseId"/>
		  <result column="warehouse_name" property="warehouseName" jdbcType="VARCHAR" />
		  <result column="supply_time" property="supplyTime" jdbcType="VARCHAR"/>
		  <result column="supply_start_time" property="supplyStartTime" jdbcType="VARCHAR"/>
		  <result column="supply_end_time" property="supplyEndTime" jdbcType="VARCHAR"/>
		  <result column="begin_time" property="beginTime" jdbcType="VARCHAR"/>
		  <result column="end_time" property="endTime" jdbcType="VARCHAR"/>
		 <result column="open_date" property="openDate" jdbcType="DATE"/>
		<result column="shop_status" property="shopStatus"/>
		<result column="store_id" property="storeId"/>
		<result column="shop_name" property="shopName"/>
		<result column="shop_type" property="shopType"/>
		<result column="order_time" property="orderTime"/>
		<result column="delivery_batch" property="deliveryBatch"/>
		<result column="auto_status" property="autoStatus"/>
		<result column="stall_id" property="stallId"/>
		<result column="management_mode" property="managementMode"/>
		  <collection property="items" column="id" ofType="com.pinshang.qingyun.order.mapper.entry.order.ShoppingCartItemEntry"
		            select="selectItemsByShoppingId" />
 	</resultMap>

	<select id="queryAllShoppingCart" resultType="com.pinshang.qingyun.order.mapper.entry.order.ShoppingCartEntry">
		select
		    c.id,
			c.`warehouse_id` warehouseId,
			c.`supplier_id` supplyId,
			c.`logistics_model` logisticsModel,
			c.create_id createId,
			c.delevery_time_range deleveryTimeRange,
			sd.`begin_time` beginTime,
			sd.`end_time` endTime
		from t_md_shopping_cart c
		INNER join `t_store_duration` sd on sd.`store_id` =c.`store_id`
		where 1=1
	</select>

	<select id="shoppingCartDetail" resultMap="shoppingCartEntry">
				select c.id,
				   c.`warehouse_id`,
				   c.`supplier_id`,
			       c.`logistics_model`,
				   c.create_id,
			       c.delevery_time_range,
			       sd.`begin_time`,
			       sd.`end_time`,
		           t.open_date,
		           t.shop_status,
		           t.shop_name,
				   t.shop_type,
				   t.management_mode,
				   c.order_time,
				   c.delivery_batch,
				   c.auto_status
			 from t_md_shopping_cart c
			INNER join `t_store_duration` sd on sd.`store_id` =c.`store_id`
		    LEFT JOIN t_md_shop t ON t.store_id = c.store_id
			where 1=1 and c.admin_status = 0
			<if test="storeId !=null">
				and c.`store_id` = #{storeId}
			</if>
			<if test="shoppingCartId !=null">
				and c.id =#{shoppingCartId}
			</if>
			<!--<if test="isXd and isInternal">
				and t.shop_type = 5 and c.create_id = #{userId}
			</if>
			<if test="isXd and !isInternal">
				and t.shop_type = 5 and c.create_id = -1
			</if>-->
	</select>

	<select id="shoppingCartDetailConsignment" resultMap="shoppingCartEntry">
		select c.id,
			c.`warehouse_id`,
			c.`supplier_id`,
			c.`logistics_model`,
			c.create_id,
			c.delevery_time_range,
			sd.`begin_time`,
			sd.`end_time`,
			t.open_date,
			t.shop_status,
			t.shop_name,
			t.shop_type,
			t.management_mode,
			c.order_time,
			c.delivery_batch,
			c.auto_status,
			c.stall_id
		from t_md_shopping_cart c
		INNER join `t_store_duration` sd on sd.`store_id` =c.`store_id`
		LEFT JOIN t_md_shop t ON t.store_id = c.store_id
		where 1=1 and c.admin_status = 0
		<if test="storeId !=null">
			and c.`store_id` = #{storeId}
		</if>
		<if test="shoppingCartId !=null">
			and c.id =#{shoppingCartId}
		</if>
		<if test="!isInternal and (consignmentId == null or consignmentId == 0)">
			and c.consignment_id = -1
		</if>
		<if test="!isInternal and consignmentId != null and consignmentId > 0">
			and c.consignment_id = #{consignmentId}
		</if>
		<if test = "stallIdList != null and stallIdList.size > 0">
			AND c.stall_id IN
			<foreach collection="stallIdList" item="item" separator="," open="(" close=")">
				#{item}
			</foreach>
		</if>
	</select>

	<select id="shoppingCartDetailAdmin" resultMap="shoppingCartEntry">
		select
		    c.id,
			c.`warehouse_id`,
			c.`supplier_id`,
			c.`logistics_model`,
			c.create_id,
			c.delevery_time_range,
			sd.`begin_time`,
			sd.`end_time`,
			t.open_date,
			t.shop_status,
			t.shop_name,
			t.shop_type,
			t.management_mode,
			c.order_time,
			c.delivery_batch,
			t.store_id,
			c.stall_id
		from t_md_shopping_cart c
		INNER join `t_store_duration` sd on sd.`store_id` =c.`store_id`
		LEFT JOIN t_md_shop t ON t.store_id = c.store_id
		where 1=1 and c.admin_status = 1
		<if test="storeId !=null">
			and c.`store_id` = #{storeId}
		</if>
		<if test="shoppingCartId !=null">
			and c.id =#{shoppingCartId}
		</if>
		<if test="userId != null">
			and c.create_id = #{userId}
		</if>
		<if test="bigShop">
			and c.stall_id > 0
		</if>
		<if test="!bigShop">
			and (c.stall_id = -1 or  c.stall_id is null )
		</if>
		<!--<if test="isXd and isInternal">
            and t.shop_type = 5 and c.create_id = #{userId}
        </if>
        <if test="isXd and !isInternal">
            and t.shop_type = 5 and c.create_id = -1
        </if>-->
	</select>

	<select id="xdShoppingCartDetail" resultMap="shoppingCartEntry">
		select
		    c.id,
			c.`warehouse_id`,
			c.`supplier_id`,
			c.`logistics_model`,
			c.create_id,
			c.delevery_time_range,
			t.open_date,
			c.store_id,
			t.shop_type,
			t.management_mode
		from t_md_shopping_cart c
		INNER JOIN t_md_shop t ON t.store_id = c.store_id AND t.shop_type = 5 -- 前置仓
		where 1=1 and t.shop_status = 1 and c.admin_status = 0

	</select>

	<select id="selectItemsByShoppingId" parameterType="java.lang.Long" resultType="com.pinshang.qingyun.order.mapper.entry.order.ShoppingCartItemEntry">
		  select
		        ci.id as shopping_cart_item_id,
				ci.`commodity_id`,
				ci.`commodity_num`  quantity,
		        ci.create_id,
		        ci.create_time
		 from t_md_shopping_cart_item ci
		inner join t_md_shopping_cart sc on sc.id =ci.shopping_cart_id and sc.id= #{id}

	</select>

	<!--查找商品客户价格方案  -->
	<select id="findStoreCommodityPriceByStoreIdAndCommodityIds" parameterType="java.util.Map" resultType="com.pinshang.qingyun.order.mapper.entry.order.ProductPriceEntry">
				select
				  c.`id` product_id,
				  c.`commodity_code` product_code,
				  c.`commodity_name` product_name,
				  ppml.`commodity_price` price
				from
				  `t_store_settlement` s 
				  inner join `t_product_price_model` ppm  on ppm.`id` = s.`product_price_model_id` 
				  inner join `t_product_price_model_list` ppml  on ppml.`product_price_model_id` = ppm.`id` 
				  inner join `t_commodity` c  on ppml.`commodity_id` = c.`id` 
				where 1 = 1 
				  and s.`store_id` =#{storeId}
				  and c.`id` in 
			<foreach collection="commodityIds" index="index" item="commodityId" open="(" separator="," close=")">
				#{commodityId}
			</foreach>
	</select>
	
	<!--查找产品特价方案(带限量)  -->
	<select id="findProductCommodityPriceByStoreIdAndOrderTime"  parameterType="java.util.Map" resultType="com.pinshang.qingyun.order.mapper.entry.order.ProductPriceEntry">
		select 
		  c.id product_id,
		  c.`commodity_code` product_code,
		  c.`commodity_name` product_name,
		  p.`price`,
		  p.`limit_number` limit_num
		from
		  `t_promotion_product` p  
		 inner join  (SELECT 
			    p.id id
			  FROM
			    `t_promotion` p,
			    `t_promotion_scope` ps,
			    `t_store_settlement` ss,
			    `t_product_price_model` ppm,
			    `t_store` s 
			  WHERE 1 = 1 
			    AND p.`id` = ps.`promotion_id` 
			    AND ss.`product_price_model_id` = ppm.`id` 
			    AND ss.`store_id` = s.`id` 
			    AND p.`status` = 0 
			    AND #{orderTime} between p.`start_time` and p.`end_time`
			    AND (
			      ps.`type_id` = ss.`settlement_customer_id` 
			      OR ps.`type_id` = ppm.`id` 
			      OR ps.`type_id` = s.`id`
			    ) 
			    AND s.`id` = #{storeId}
			  ORDER BY p.`create_time` DESC 
			  LIMIT 1) temp on temp.id =p.promotion_id
		inner join t_commodity c on  c.`commodity_code` = p.`product_code` and c.`id` in
		<foreach collection="commodityIds" index="index" item="commodityId" open="(" separator="," close=")">
			#{commodityId}
		</foreach>
	</select>
	
	<select id="getSplitOrderInfo" resultType="com.pinshang.qingyun.order.mapper.entry.order.SplitOrderListEntry">
		select c.`logistics_model`,
       			c.id commodity_id
		from t_commodity c
		where 1=1
		and c.id in
		<foreach collection="commodityIds" index="index" item="commodityId" open="(" separator="," close=")">
			#{commodityId}
		</foreach>
	</select>
	
	<select id="getDeliveryBatchDay" resultType="java.lang.Integer">
		select delivery_batch_day from t_xs_back_setting limit 1 
	</select>
	
	<select id="findDeliveryBatchByCode" resultType="com.pinshang.qingyun.order.mapper.entry.order.DeliveryBatchEntry" flushCache="true">
		SELECT
		  d.option_name,
		  d.option_code,
		  d.option_value,
		  d.memo
		FROM
			t_dictionary d,
			t_dictionary p
		WHERE
			p.option_code = 'DeliveryBatch'
		AND p.id = d.dictionary_id and d.option_state = 1
		<if test="optionCode !=null and optionCode !='' ">
			and d.option_code =#{optionCode} 
		</if>
	</select>

	<select id="getAdviseCommodityNum" resultType="java.math.BigDecimal">
		 SELECT
		    tt.advise_commodity_num
		  FROM t_md_shopping_cart t
		  LEFT JOIN t_md_shopping_cart_item tt ON tt.shopping_cart_id = t.id
		  WHERE t.store_id = #{storeId}
		  AND tt.commodity_id = #{commodityId}
		  LIMIT 1
	</select>

	<select id="selectShpppingCartIdList" resultType="java.lang.Long">
		SELECT id FROM t_md_shopping_cart
            WHERE store_id in (SELECT t.store_id from t_md_shop t WHERE t.shop_type = 5 )
	</select>


	<delete id="deleteXDShoppingCartItem">
		DELETE FROM t_md_shopping_cart_item WHERE shopping_cart_id in
		<foreach collection="shoppingCartIdList" index="index" item="cartId" open="(" separator="," close=")">
			#{cartId}
		</foreach>
	</delete>

	<delete id="deleteXdShoppingCart">
		DELETE FROM t_md_shopping_cart WHERE id in
		<foreach collection="shoppingCartIdList" index="index" item="cartId" open="(" separator="," close=")">
			#{cartId}
		</foreach>
	</delete>

	<select id="getDistinctShoppingCartAdmin" resultType="com.pinshang.qingyun.order.model.order.ShoppingCart">
		SELECT DISTINCT t.store_id
		FROM  t_md_shopping_cart t
		WHERE t.admin_status = 1
		and t.create_id = #{cartVo.userId}
		<if test="cartVo.bigShop">
			and t.stall_id > 0
		</if>
		<if test="!cartVo.bigShop">
			and (t.stall_id = -1 or  t.stall_id is null )
		</if>
		ORDER BY t.store_id
	</select>

	<select id="countCartAdmin" resultType="java.lang.Long">
		SELECT count(1) FROM t_md_shopping_cart_item t
		where t.shopping_cart_id in
			  (
				  SELECT
				        tt.id
				  from t_md_shopping_cart tt
				  where tt.admin_status = 1
			      and tt.create_id = #{userId}
					<if test="bigShop">
						and tt.stall_id > 0
					</if>
					<if test="!bigShop">
						and (tt.stall_id = -1 or  tt.stall_id is null )
					</if>
			  )
	</select>

	<select id="findShoppingCartCreateId" resultType="com.pinshang.qingyun.order.dto.ShelvesShoppingCartODTO">
		   SELECT
			   sci.commodity_id,
		       sci.create_id
		   from t_md_shopping_cart sc
		LEFT JOIN t_md_shopping_cart_item sci on sci.shopping_cart_id = sc.id
		where sc.store_id = #{storeId} and sc.admin_status = 0
	</select>


	<select id="queryAutoShoppingCart" resultType="com.pinshang.qingyun.order.mapper.entry.order.ShoppingCartEntry">
		SELECT
			sc.store_id,
			sc.id shoppingCartId,
			sc.auto_status,
			sc.consignment_id,
			sci.id shoppingCartItemId,
			sci.commodity_id,
			sci.commodity_num quantity,
		    sc.create_id,
		    sc.admin_status,
		    sc.stall_id
		FROM
			t_md_shopping_cart sc
		INNER JOIN t_md_shopping_cart_item sci ON sci.shopping_cart_id = sc.id
		where 1=1
	</select>


	<select id="shoppingCartAdminByStoreIdList" resultType="com.pinshang.qingyun.order.mapper.entry.order.ShoppingCartAdminEntry">
		select
			c.id shoppingCartId,
			c.`warehouse_id`,
			c.`supplier_id` supplyId,
			c.`logistics_model`,
			c.delevery_time_range,
			t.shop_name,
			t.shop_type,

			t.store_id,
			ci.`commodity_id`,
			ci.`commodity_num`  quantity,
			c.stall_id
		from t_md_shopping_cart c
		    left join t_md_shopping_cart_item ci on ci.shopping_cart_id = c.id
		LEFT JOIN t_md_shop t ON t.store_id = c.store_id
		where c.admin_status = 1 and c.create_id = #{userId}
		and c.`store_id` in
		<foreach collection="storeIdList" index="index" item="storeId" open="(" separator="," close=")">
			#{storeId}
		</foreach>
		<if test="bigShop">
			and c.stall_id > 0
		</if>
		<if test="!bigShop">
			and (c.stall_id = -1 or  c.stall_id is null )
		</if>
        order by c.store_id
	</select>

</mapper>