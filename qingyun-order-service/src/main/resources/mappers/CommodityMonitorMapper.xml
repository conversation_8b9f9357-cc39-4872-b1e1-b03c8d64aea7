<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.pinshang.qingyun.order.mapper.CommodityMonitorMapper" >



    <select id="queryCommodityOrderQuantity" parameterType="com.pinshang.qingyun.order.vo.commodity.CommodityMonitorRequestVo"
            resultType="com.pinshang.qingyun.order.mapper.entry.commodity.monitor.CommodityOrderMonitorEntry">
        SELECT
        SUM(tolg.commodity_num) AS orderNum,
        MAX(tor.create_time) AS lastOrderTime
        FROM
        t_commodity tc
        INNER JOIN t_order_list_gift tolg ON tc.id = tolg.commodity_id
        INNER JOIN t_order tor ON tolg.order_id = tor.id
        AND tor.order_status = 0
        <if test="commodityCode != null and commodityCode != '' ">
            AND tc.commodity_code = #{commodityCode}
        </if>
        <if test=" orderTime != null ">
            AND tor.order_time = #{orderTime}
        </if>
        <if test=" createTime != null ">
            AND tor.create_time <![CDATA[ <= ]]> #{createTime}
        </if>
        WHERE
        tc.enterprise_id = #{enterpriseId}
        GROUP BY tc.commodity_code
    </select>

    <select id="queryCommodityCancelOrderQuantity" parameterType="com.pinshang.qingyun.order.vo.commodity.CommodityMonitorRequestVo"
            resultType="java.math.BigDecimal">
        SELECT
        SUM(tolg.commodity_num) AS orderCancelNum
        FROM
        t_commodity tc
        INNER JOIN t_order_list_gift tolg ON tc.id = tolg.commodity_id
        INNER JOIN t_order tor ON tolg.order_id = tor.id
        AND tor.order_status = 2
        <if test=" orderTime != null ">
            AND tor.order_time = #{orderTime}
        </if>
        <if test=" createTime != null ">
            AND tor.create_time <![CDATA[ <= ]]> #{createTime}
        </if>
        WHERE
        tc.enterprise_id = #{enterpriseId}
        <if test="commodityCode != null and commodityCode != '' ">
            AND tc.commodity_code = #{commodityCode}
        </if>
        GROUP BY tc.commodity_code
    </select>

    <select id="queryCommoditySubOrderQuantity" parameterType="com.pinshang.qingyun.order.vo.commodity.CommodityMonitorRequestVo"
            resultType="com.pinshang.qingyun.order.mapper.entry.commodity.monitor.CommoditySubOrderMonitorEntry">
        SELECT
        tso.id,
        tsoi.quantity,
        tso.create_time
        FROM
        t_commodity tc
        INNER JOIN t_sub_order_item tsoi ON tc.id = tsoi.commodity_id
        INNER JOIN t_sub_order tso ON tsoi.sub_order_id = tso.id
        AND tso.status in (0,1)
        <if test=" orderTime != null ">
            AND tso.order_time = #{orderTime}
        </if>
        <if test=" createTime != null ">
            AND tso.create_time <![CDATA[ <= ]]> #{createTime}
        </if>
        WHERE
        tc.enterprise_id = #{enterpriseId}
        <if test="commodityCode != null and commodityCode != '' ">
            AND tc.commodity_code = #{commodityCode}
        </if>
        ORDER BY tso.create_time DESC
    </select>

</mapper>