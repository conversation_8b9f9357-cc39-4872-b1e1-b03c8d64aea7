<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pinshang.qingyun.order.mapper.StorePlaceOrderLogMapper">
    <select id="selectStorePlaceOrderLogByStoreId" resultType="com.pinshang.qingyun.order.model.store.StorePlaceOrderLog">
        SELECT
            ts.id AS storeId,
            ts.store_code AS storeCode,
            ts.store_name AS storeName,
            tsc.`status` AS status,
            ts.deliveryman_id AS deliverymanId, -- 送货员
            ts.store_line_id AS distributionLineId, -- 线路
            tdic.id AS lineGroupId, -- 线路组
            tsd.begin_time AS storeDurationBeginTime, -- 送货时间段
            tsd.end_time AS storeDurationEndTime, -- 送货时间段
            tson.app_max_order_number AS orderNumber, -- 订单数
            tau.state AS xs_app_status, -- 生鲜app状态
            txu.`status` AS xda_app_status, -- 鲜达app 状态
            txu.user_name AS phone_number -- 手机号
        FROM
            t_store ts
            LEFT JOIN t_store_company tsc ON tsc.store_id = ts.id
            LEFT JOIN t_distribution_line tdl ON tdl.id = ts.store_line_id
            LEFT JOIN t_dictionary tdic ON tdic.id = tdl.delivery_time_id AND tdic.`dictionary_id` = 9131078242860601278
            LEFT JOIN t_store_duration tsd ON tsd.store_id = ts.id
            LEFT JOIN t_store_order_number tson ON tson.store_id = ts.id
            LEFT JOIN t_app_user tau ON tau.store_id = ts.id
            LEFT JOIN t_xda_user txu ON txu.store_id = ts.id
        <where>
            <if test="null != storeId">
                AND ts.id = #{storeId}
            </if>
        </where>
    </select>
</mapper>