<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pinshang.qingyun.order.mapper.OrderBillLogMapper">

	<resultMap id="BaseResultMap" type="com.pinshang.qingyun.order.model.OrderBillLog">
		<result column="store_id" property="storeId" />
		<result column="host" property="host" />
		<result column="method_type" property="methodType" />
		<result column="param" property="param" />
		<result column="response" property="response" />
		<result column="create_time" property="createTime" />
	</resultMap>

    <!-- 通用查询结果列-->
    <sql id="Base_Column_List">
        store_id AS storeId, host, method_type AS methodType, param, response, create_time AS createTime
    </sql>



</mapper>
