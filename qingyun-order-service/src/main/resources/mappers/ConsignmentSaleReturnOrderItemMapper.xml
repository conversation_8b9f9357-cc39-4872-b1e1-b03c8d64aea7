<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.pinshang.qingyun.order.mapper.ConsignmentSaleReturnOrderItemMapper" >

    <update id="batchUpdateByConfirm">
        <foreach collection="list" item="item" index="index" open="" close="" separator=";">
            UPDATE t_consignment_sale_return_order_item
            SET confirm_quantity = #{item.confirmQuantity},
            rejection_reason = null
            WHERE id = #{item.id}
        </foreach>
    </update>

    <update id="batchUpdateByAudit">
        <foreach collection="list" item="item" index="index" open="" close="" separator=";">
            UPDATE t_consignment_sale_return_order_item
            SET check_quantity = #{item.checkQuantity},
            rejection_reason = #{item.rejectionReason}
            WHERE id = #{item.id}
        </foreach>
    </update>


    <select id="queryBySaleReturnOrderId"
            resultType="com.pinshang.qingyun.order.dto.consignment.ConsignmentSaleReturnOrderItemODTO">
        select
               tsroi.id,
               tc.id commodity_id,
               tc.commodity_code,
               tc.commodity_name,
               tc.commodity_package_spec,
               tc.bar_code,
               tc.is_weight,
               d.option_name commodity_unit_name,
               tsroi.return_quantity,
               tsroi.confirm_quantity,
               tsroi.check_quantity,
               tsroi.rejection_reason,
               tsroi.price
        from
             t_consignment_sale_return_order_item tsroi
            left join t_commodity tc on tsroi.commodity_id = tc.id
            LEFT JOIN t_dictionary d ON d.id = tc.commodity_unit_id
        where tsroi.consignment_sale_return_order_id = #{saleReturnOrderId}
    </select>
</mapper>