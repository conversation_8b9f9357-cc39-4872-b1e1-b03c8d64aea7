<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.pinshang.qingyun.order.mapper.cup.OrderCupReportMapper" >

    <select id="noOrderPageSearch" resultType="com.pinshang.qingyun.order.dto.cup.NoOrderStoreODTO">
        SELECT
            s.id storeId,
            s.store_code,
            s.store_name,
            e.employee_name as deliveryName,
            s.not_order_remark remark
        FROM
            t_store s
            LEFT JOIN t_employee_user e ON s.deliveryman_id = e.employee_id
        WHERE s.store_status in (0,1)

        <if test="vo.storeCode != null and vo.storeCode  != '' ">
            AND s.store_code like concat('%',#{vo.storeCode},'%')
        </if>
        <if test="vo.storeName != null and vo.storeName  != '' ">
            AND (s.store_name like concat('%',#{vo.storeName},'%') or s.store_aid like concat('%',#{vo.storeName},'%'))
        </if>
        <if test = "vo.deliverymanId != null">
            AND s.deliveryman_id = #{vo.deliverymanId}
        </if>
        <if test = "vo.storeTypeId != null">
            AND s.store_type_id = #{vo.storeTypeId}
        </if>

        <if test = "vo.lineGroupId != null">
            AND s.store_line_id in (SELECT t.id from t_distribution_line t  where t.delivery_time_id = #{vo.lineGroupId} )
        </if>
        <if test = "vo.settlementId != null">
            AND s.settlement_customer_id = #{vo.settlementId}
        </if>
        <if test="vo.noOrderRemark != null and vo.noOrderRemark  != '' ">
            AND s.not_order_remark like concat('%',#{vo.noOrderRemark},'%')
        </if>
        <if test="vo.lineSendDate != null and vo.lineSendDate  != '' ">
            AND s.store_line_id in (SELECT l.id from t_distribution_line l,t_delivery_time dt where l.delivery_time_id=dt.line_group_id and dt.end_time = #{vo.lineSendDate})
        </if>
        ORDER BY s.store_code
    </select>

    <select id="findStoreOrder" resultType="com.pinshang.qingyun.order.dto.cup.OrderTimeCountODTO">
        SELECT
            DATE_FORMAT(t.order_time, '%Y/%m/%d') orderTimeForamt,
            count(1) orderTimeCount
        FROM t_order t
        WHERE t.store_id = #{storeId}
          AND t.order_time BETWEEN DATE_FORMAT(#{startDate}, '%Y-%m-%d') AND DATE_FORMAT(#{endDate}, '%Y-%m-%d')
          AND t.order_status = 0
        GROUP BY t.order_time
    </select>


    <select id="getDeliveryTime" resultType="com.pinshang.qingyun.order.dto.cup.DeliveryTimeODTO">
        SELECT
            dt.line_group_id AS lineGroupIdStr,
            dt.end_time endTime,
            d.option_name AS lineGroup
        FROM t_delivery_time dt
             INNER JOIN t_dictionary d ON dt.line_group_id = d.id
        ORDER BY d.option_name ASC
    </select>


    <select id="findStoreCommodityByStoreIdAndCommodityCode" resultType="com.pinshang.qingyun.order.model.commodity.Commodity">
        select
            ppml.`id` product_price_model_list_id,
            ppml.`commodity_price` commodityPrice,
            ppml.`commodity_price` originalCommodityPrice,
            dic.option_name  commodityUnitName,
            (case when cg.id is null then '否' else '是' end) as isRoundedGoods,
            c.*
        from
            `t_store_settlement` s
                inner join `t_product_price_model` ppm  on ppm.`id` = s.`product_price_model_id`
                inner join `t_product_price_model_list` ppml  on ppml.`product_price_model_id` = ppm.`id`
                inner join `t_commodity` c  on ppml.`commodity_id` = c.`id`
                left join t_commodity_freeze_group cg  on cg.commodity_id = c.id
                left join t_dictionary dic on dic.id = c.commodity_unit_id
        where 1 = 1
          and ppm.`price_model_state` = 1
          and c.`commodity_state` = 1
          and s.`store_id` = #{storeId}

            <if test = "addCommodityType == 1">
                AND c.bar_code IN
                <foreach collection="commodityCodeList" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>

            <if test = "addCommodityType == 2">
                AND c.commodity_code IN
                <foreach collection="commodityCodeList" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>

    </select>


    <select id="findOrderListByPage" resultType="com.pinshang.qingyun.order.dto.cup.OrderCupPageResultODTO">

        SELECT
            o.id orderId,
            o.order_code,
            o.store_id,
            o.create_time,
            o.order_remark,
            o.order_amount,
            o.order_status,
            o.order_time,
            s.store_code,
            s.store_name,
            CASE
                WHEN o.business_type = 10 then o.driver_id
                ELSE om.delivery_man_id
                END AS deliveryman_id,
            CASE
                WHEN o.business_type = 10 then du.employee_name
                ELSE om.delivery_man_name
                END AS deliveryman_name,
            om.supervision_id		AS supervisor_id,
            om.supervision_name		AS supervisor_name,
            o.create_id,
            u.employee_name AS createUserName,
            o.order_type,
            d.id as lineGroupId,
            d.option_name lineGroupName,
            s.business_type
        FROM
            t_order o
            LEFT JOIN t_store s ON o.store_id = s.id
            LEFT JOIN t_employee_user u ON u.user_id = o.create_id
            LEFT JOIN t_distribution_line dl on dl.id = s.store_line_id
            LEFT JOIN t_dictionary d on d.id = dl.delivery_time_id
            LEFT JOIN t_order_mirror om ON om.order_id = o.id
            LEFT JOIN t_employee_user du ON du.user_id = o.driver_id

        where o.order_status in (0,1,2)

        <if test="vo.orderTimeStart != null and vo.orderTimeStart != '' and vo.orderTimeEnd != null and vo.orderTimeEnd != '' ">
            and o.`order_time` BETWEEN #{vo.orderTimeStart} and #{vo.orderTimeEnd}
        </if>
        <if test="vo.orderCode != null and vo.orderCode  != '' ">
            AND o.`order_code` like concat('%',#{vo.orderCode},'%')
        </if>
        <if test = "vo.orderStatus != null">
            AND o.`order_status` = #{vo.orderStatus}
        </if>
        <if test = "vo.deliverymanId != null">
            AND om.delivery_man_id = #{vo.deliverymanId}
        </if>
        <if test = "vo.lineGroupId != null">
            AND d.id = #{vo.lineGroupId}
        </if>
        <if test = "vo.supervisorId != null">
            AND om.supervision_id  = #{vo.supervisorId}
        </if>
        <if test = "vo.createId != null">
            AND o.create_id  = #{vo.createId}
        </if>
        <if test = "vo.storeId != null">
            AND o.store_id  = #{vo.storeId}
        </if>
        <if test = "vo.storeName != null and vo.storeName != ''">
            AND s.store_name like concat('%',#{vo.storeName},'%')
        </if>
        <if test = "vo.storeCode != null and vo.storeCode != ''">
            AND s.store_code like concat('%',#{vo.storeCode},'%')
        </if>

        <if test="null != vo.storeIdList and vo.storeIdList.size > 0">
            and o.store_id in
            <foreach collection="vo.storeIdList" item="code" open="(" close=")" separator=",">
                #{code}
            </foreach>
        </if>
        order by o.create_time desc

    </select>
</mapper>