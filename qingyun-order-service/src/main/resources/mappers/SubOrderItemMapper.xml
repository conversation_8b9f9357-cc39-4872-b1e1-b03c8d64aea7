<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.pinshang.qingyun.order.mapper.SubOrderItemMapper" >
    <update id="batchUpdate" parameterType="java.util.List">
        <foreach collection="list" item="item" index="index" separator=";"
                 open="" close="">
            UPDATE t_sub_order_item SET real_delivery_quantity = #{item.realDeliveryQuantity}, real_receive_quantity = #{item.realDeliveryQuantity}
            WHERE sub_order_id = #{item.subOrderId} AND commodity_Id = #{item.commodityId}
        </foreach>
    </update>


    <insert id="batchInsertHandleReceiveOrderLog" parameterType="List" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO t_md_receive_order_log
        (shop_id, order_code, sub_order_id, commodity_id, received_quantity, quantity, create_id, create_time)
        VALUES
        <foreach collection="list" item="item" index="index" separator=",">
            (#{item.shopId}, #{item.orderCode}, #{item.subOrderId}, #{item.commodityId},
            #{item.receivedQuantity}, #{item.quantity}, #{item.createId}, #{item.createTime})
        </foreach>
    </insert>

    <select id="queryBySubOrderId"  resultType="com.pinshang.qingyun.order.model.order.SubOrderItem">
        select
           soi.id,
           soi.commodity_id,
           soi.quantity,
           soi.real_delivery_quantity,
           soi.real_receive_quantity,
           soi.type,
           soi.comb_type,
           soi.comb_commodity_id,
           soi.comb_order_list_id,
           soi.price,
           soi.price_promotion_id
        from t_sub_order_item soi
        where soi.sub_order_id = #{subOrderId}
    </select>
    <select id="queryByOrderIdAndCommodityIds" resultType="com.pinshang.qingyun.order.model.order.SubOrderItem">
        select
            soi.id,
            soi.sub_order_id,
            soi.commodity_id,
            soi.quantity,
            soi.real_delivery_quantity,
            soi.real_receive_quantity,
            soi.type,
            soi.comb_type,
            soi.comb_commodity_id,
            soi.comb_order_list_id
        from t_sub_order_item soi
            inner join t_sub_order tso on soi.sub_order_id = tso.id
            inner join t_order tor on tso.order_id = tor.id
         where tor.id = #{orderId}
         and soi.commodity_id in
        <foreach collection="commodityList" index="index" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

    <select id="queryByOrderId" resultType="com.pinshang.qingyun.order.model.order.SubOrderItem">
        select
        soi.id,
        soi.sub_order_id,
        soi.commodity_id,
        soi.quantity,
        soi.real_delivery_quantity,
        soi.real_receive_quantity,
        soi.type,
        soi.comb_type,
        soi.comb_commodity_id,
        soi.comb_order_list_id
        from t_sub_order_item soi
        inner join t_sub_order tso on soi.sub_order_id = tso.id
        inner join t_order tor on tso.order_id = tor.id
        where tor.id = #{orderId}
    </select>
</mapper>