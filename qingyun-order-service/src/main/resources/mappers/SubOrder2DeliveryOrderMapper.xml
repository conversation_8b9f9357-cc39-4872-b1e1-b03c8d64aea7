<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.pinshang.qingyun.order.mapper.SubOrder2DeliveryOrderMapper">

	<!-- 根据线路组 查询要生成DO单的订单ID -->
	<select id="listCurrentDayOrderId" resultType="java.lang.Long">
		SELECT
			DISTINCT o.id
		FROM
			t_order o
		INNER JOIN t_store s ON o.store_id = s.id
		INNER JOIN t_distribution_line dl ON s.store_line_id = dl.id
		INNER JOIN t_delivery_time dt ON dt.line_group_id = dl.delivery_time_id
		INNER JOIN t_sub_order so ON so.order_id = o.id
		WHERE
			dt.line_group_id = #{deliveryTime.lineGroupId}
		<if test="deliveryTime.coverFlag == true" >
			AND o.order_time BETWEEN  DATE_SUB(date(NOW()), INTERVAL 1 DAY) AND date(NOW())
		</if>
		<if test="deliveryTime.coverFlag == false" >
			AND o.order_time BETWEEN  DATE(NOW()) AND DATE_ADD(DATE(NOW()),INTERVAL 1 DAY)
		</if>
		AND (so.logistics_model =1 OR (so.logistics_model = 2
				AND s.store_type_id IN ( SELECT d1.option_value FROM t_dictionary d1 INNER JOIN  t_dictionary d2 ON d1.dictionary_id = d2.id
											AND d2.option_code ='storage-direct-store-type' and d1.option_state =1)
				))
		AND so.status =0
		AND o.order_status = 0
	</select>


	<select id="listCurrentDayOrderIdV2" resultType="java.lang.Long">
		SELECT
			DISTINCT o.id
		FROM
		t_order o
		INNER JOIN t_store s ON o.store_id = s.id
		INNER JOIN t_distribution_line dl ON s.store_line_id = dl.id
		INNER JOIN t_delivery_time dt ON dt.line_group_id = dl.delivery_time_id
		INNER JOIN t_sub_order so ON so.order_id = o.id
		WHERE
		dt.line_group_id = #{deliveryTime.lineGroupId}
		<if test="deliveryTime.coverFlag == true" >
			AND o.order_time BETWEEN  DATE_SUB(date(NOW()), INTERVAL 1 DAY) AND date(NOW())
		</if>
		<if test="deliveryTime.coverFlag == false" >
			AND o.order_time BETWEEN  DATE(NOW()) AND DATE_ADD(DATE(NOW()),INTERVAL 1 DAY)
		</if>
		<if test=" businessType == null or businessType != 10">
			AND (o.business_type is null OR o.business_type != 10)
			AND (so.logistics_model =1
			<if test="directStoreTypeList != null and directStoreTypeList.size > 0">
				OR (
				so.logistics_model = 2
				AND s.store_type_id in
				<foreach collection="directStoreTypeList" separator="," open="(" close=")" item="item">
					#{item}
				</foreach>
				)
			</if>
			<if test="tobStoreTypeList != null and tobStoreTypeList.size > 0">
				OR (
				so.logistics_model = 2
				AND s.store_type_id in
				<foreach collection="tobStoreTypeList" separator="," open="(" close=")" item="item">
					#{item}
				</foreach>
				AND so.warehouse_id in
				<foreach collection="tobWarehouseList" separator="," open="(" close=")" item="item">
					#{item}
				</foreach>
				)
			</if>
			)
		</if>
		<if test=" businessType != null and businessType == 10">
			AND o.business_type = 10
		</if>
		AND so.status =0
		AND o.order_status = 0
	</select>
</mapper>