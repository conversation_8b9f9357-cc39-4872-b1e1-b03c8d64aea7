<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.pinshang.qingyun.order.mapper.PurchaseOrderMapper">

	<resultMap id="BaseResultMap" type="com.pinshang.qingyun.order.mapper.entry.order.generatePurchaseOrder.SubOrderEntry">
		<id column="id" property="id" jdbcType="BIGINT"/>
		<result column="logistics_model" property="logisticsModel" />
		<result column="supplier_id" property="supplierId" />
		<result column="create_id" property="createId" />
		<result column="create_time" property="createTime"/>
		<result column="enterprise_id" property="enterpriseId" />
		<result column="order_code" property="subOrderCode"/>
		<result column="total_price" property="totalPrice"/>
		<result column="order_time" property="orderTime" />
		<result column="store_id" property="storeId" />
		<result column="order_status" property="orderStatus"/>
		<collection property="subOrderItems" column="sub_order_id"
					ofType="com.pinshang.qingyun.order.mapper.entry.order.generatePurchaseOrder.SubOrderItemModel">
			<id column="item_id" property="itemId" />
			<result column="preorder_id" property="subOrderId"/>
			<result column="commodity_id" property="commodityId" />
			<result column="quantity" property="quantity"/>
			<result column="price" property="price" />
			<result column="item_total_price" property="totalPrice" />
			<result column="create_id" property="createId"/>
			<result column="create_time" property="createTime" />
			<result column="item_status" property="status"/>
		</collection>
	</resultMap>

    <select id="queryCommodityIds" parameterType="java.lang.String" resultType="java.lang.String">
        select
          DISTINCT pi.commodity_id
        from
          t_md_preorder p
        inner join t_md_preorder_item pi on pi.preorder_id = p.id
        where 1=1
		<![CDATA[ AND p.order_time >= DATE_SUB(NOW(), INTERVAL 7 DAY) ]]>
        AND p.`logistics_model` = 0
        AND p.purchase_order_id is null
        <![CDATA[ and date_format(p.order_time,'%Y-%m-%d') <= #{endTime} ]]>
    </select>

	<select id="getSubOrderBeforeDeadline" resultMap="BaseResultMap">
		select p.id,
		p.enterprise_id,
		p.order_code,
		p.logistics_model,
		p.supplier_id,
		p.receive_status as status,
		p.total_price,
		p.order_time,
		p.store_id,
		p.create_id,
		p.create_time,
		pi.id as item_id,
		pi.preorder_id,
		pi.commodity_id,
		pi.require_quantity as quantity,
		pi.price,
		pi.total_price as item_total_price,
		pi.create_id,
		pi.create_time,
		pi.status as item_status,
		p.`order_status`
		from
		t_md_preorder p
    	inner join t_md_preorder_item pi on pi.preorder_id = p.id
		where 1=1
		AND DATE_FORMAT(p.create_time,'%T') &lt; #{endTime}
		AND TO_DAYS(p.create_time) = TO_DAYS(NOW())
		AND p.receive_status != 4
		AND p.`logistics_model` = 0
		and p.purchase_order_id is null
	</select>
	
	<select id="getSubOrderBeforeDeadlineById" resultMap="BaseResultMap">
		select p.id,
		p.enterprise_id,
		p.order_code,
		p.logistics_model,
		p.supplier_id,
		p.receive_status as status,
		p.total_price,
		p.order_time,
		p.store_id,
		p.create_id,
		p.create_time,
		pi.id as item_id,
		pi.preorder_id,
		pi.commodity_id,
		pi.require_quantity as quantity,
		pi.price,
		pi.total_price as item_total_price,
		pi.create_id,
		pi.create_time,
		pi.status as item_status,
		p.`order_status`
		from
		t_md_preorder p
    	inner join t_md_preorder_item pi on pi.preorder_id = p.id
		where 1=1
		AND p.`logistics_model` = 0
		and p.purchase_order_id is null
		and p.supplier_id =#{supplierId}
		AND DATE_FORMAT(p.create_time,'%T') &lt; #{endTime}
		AND TO_DAYS(p.create_time) = TO_DAYS(NOW())
	</select>

	<select id="getSubOrderBeforeDeadlineByPreorderId" resultMap="BaseResultMap">
		select p.id,
		p.enterprise_id,
		p.order_code,
		p.logistics_model,
		p.supplier_id,
		p.receive_status as status,
		p.total_price,
		p.order_time,
		p.store_id,
		p.create_id,
		p.create_time,
		pi.id as item_id,
		pi.preorder_id,
		pi.commodity_id,
		pi.require_quantity as quantity,
		pi.price,
		pi.total_price as item_total_price,
		pi.create_id,
		pi.create_time,
		pi.status as item_status,
		p.`order_status`
		from t_md_preorder p
    	inner join t_md_preorder_item pi on pi.preorder_id = p.id
		where p.id = #{preOrderId}

	</select>

	<select id="getSubOrderByDateAndId" resultMap="BaseResultMap">
		select p.id,
		p.enterprise_id,
		p.order_code,
		p.logistics_model,
		p.supplier_id,
		p.receive_status as status,
		p.total_price,
		p.order_time,
		p.store_id,
		p.create_id,
		p.create_time,
		pi.id as item_id,
		pi.preorder_id,
		pi.commodity_id,
		pi.require_quantity as quantity,
		pi.price,
		pi.total_price as item_total_price,
		pi.create_id,
		pi.create_time,
		pi.status as item_status,
		p.`order_status`
		from
		t_md_preorder p
    	inner join t_md_preorder_item pi on pi.preorder_id = p.id
		where 1=1
		AND p.`logistics_model` = 0
		and p.purchase_order_id is null
		and p.supplier_id =#{supplierId}
		and date_format(p.order_time,'%Y-%m-%d') =#{orderTime}
	</select>


</mapper>