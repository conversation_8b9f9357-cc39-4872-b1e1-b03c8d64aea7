<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.pinshang.qingyun.order.mapper.ShopReceiveSettingMapper">

    <select id="getShopReceiveSettingPage" resultType="com.pinshang.qingyun.order.dto.ShopReceiveSettingODTO">
          select
            md.id shopId,
            md.shop_code,
            md.shop_name,
            md.shop_type,
            md.shop_status,
            sett.receive_type,
            sett.id settingId
       from t_md_shop md
          left join t_md_shop_receive_setting sett on md.id = sett.shop_id
        WHERE 1=1
        <if test=" vo.shopType != null ">
            AND md.shop_type = #{vo.shopType}
        </if>
        <if test=" vo.shopId != null ">
            AND md.id = #{vo.shopId}
        </if>
        <!--<if test=" vo.receiveType != null ">
            AND sett.receive_type = #{vo.receiveType}
        </if>-->
        <choose>
            <when test="vo.receiveType != null and vo.receiveType == 0">
                AND (sett.receive_type = 0 or sett.receive_type is null )
            </when>
            <when test="vo.receiveType != null and vo.receiveType == 1">
                AND sett.receive_type = 1
            </when>
        </choose>
        order by md.shop_code
    </select>

    <select id="getShopReceiveSettingLogPage" resultType="com.pinshang.qingyun.order.dto.ShopReceiveSettingODTO">
        select
            md.id shopId,
            md.shop_code,
            md.shop_name,
            md.shop_type,
            md.shop_status,
            log.receive_type,
            log.create_name,
            log.create_time
        from  t_md_shop_receive_setting_log log
        left join t_md_shop md on md.id = log.shop_id
        WHERE 1=1

        <if test=" vo.shopId != null ">
            AND log.shop_id = #{vo.shopId}
        </if>
        order by log.create_time desc
    </select>

</mapper>