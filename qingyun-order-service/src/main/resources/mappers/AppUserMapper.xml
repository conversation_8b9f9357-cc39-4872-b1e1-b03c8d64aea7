<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.pinshang.qingyun.order.mapper.AppUserMapper" >
    <insert id="openAccount" parameterType="com.pinshang.qingyun.order.model.user.AppUser">
        INSERT INTO t_app_user(name,password,store_id,store_code,modify,state,create_user_id)
        VALUES (#{name},#{password},#{storeId},#{storeCode},#{modify},#{state},#{createUserId})
    </insert>

    <update id="editByName" parameterType="com.pinshang.qingyun.order.model.user.AppUser">
        UPDATE t_app_user
        <set>
            <if test="password !=null and password!=''">
                password =#{password},
            </if>
            <if test="state !=null">
                state = #{state},
            </if>
            <if test="modify !=null">
                modify =#{modify},
            </if>
        </set>
        WHERE 1=1
        <if test="id !=null">
          AND id=#{id}
        </if>
        AND name = #{name}
    </update>

    <select id="findByStoreCode" resultType="com.pinshang.qingyun.order.model.user.AppUser">
        SELECT * FROM t_app_user WHERE store_code =#{storeCode}
    </select>
</mapper>