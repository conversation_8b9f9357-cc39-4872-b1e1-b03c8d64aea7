<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.pinshang.qingyun.order.mapper.auto.AutoPreOrderItemMapper">

    <select id="queryByPreOrderId" resultType="com.pinshang.qingyun.order.dto.AutoPreOrderItemODTO">
        SELECT c.id                        as commodityId,
               c.commodity_code            AS commodityCode,
               c.bar_code                  as barCode,
               c.commodity_name            as commodityName,
               c.commodity_spec            as commoditySpec,
               c.commodity_is_quick_freeze as isFreeze,
               ap.stock_number             as stockNumber,
               ap.stock_quantity           as stockQuantity,
               d.option_name               as commodityUnit,
               ap.price                    as supplyPrice
        FROM t_md_auto_pre_order_item ap
                 LEFT JOIN t_commodity c ON ap.commodity_id = c.id
                 left join t_dictionary d ON c.commodity_unit_id = d.id
        where ap.auto_pre_id = #{preOrderId}

    </select>


    <select id="getAutoPreOrderItemsByCode" resultType="com.pinshang.qingyun.order.dto.AutoPreOrderAuditItemDTO">
        SELECT po.shop_id,
               md.store_id,
               poi.commodity_id,
               poi.stock_quantity,
               po.create_id,
               po.consignment_id,
               po.id orderId,
               po.order_code
        FROM t_md_auto_pre_order po
                 LEFT JOIN t_md_auto_pre_order_item poi ON poi.auto_pre_id = po.id
                 left join t_md_shop md ON md.id = po.shop_id
        where po.order_code = #{autoPreOrderCode}
    </select>

</mapper>