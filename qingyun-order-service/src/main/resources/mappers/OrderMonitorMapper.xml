<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.pinshang.qingyun.order.mapper.OrderMonitorMapper" >

    <resultMap id="OrderResultMap" type="com.pinshang.qingyun.order.mapper.entry.order.OrderMonitorEntry">
        <id column="order_id" property="orderId"/>
        <result column="order_code" property="orderCode" />
        <result column="store_code" property="storeCode"/>
        <result column="store_name" property="storeName"/>
        <result column="order_time" property="orderTime"/>
        <result column="order_amount" property="orderAmount"/>
        <result column="final_amount" property="finalAmount"/>
        <result column="create_time" property="createTime"/>
        <result column="order_status" property="orderStatus"/>
        <result column="sync_status" property="syncStatus"/>
        <result column="order_type" property="orderType"/>
        <result column="process_status" property="processStatus"/>
        <result column="order_duration_time" property="orderDurationTime"/>
        <result column="delivery_batch" property="deliveryBatch"/>
        <result column="change_price_status" property="changePriceStatus"/>
        <result column="real_sub_order_done_status" property="realSubOrderDoneStatus"/>
        <result column="presale_status" property="presaleStatus"/>

        <collection property="orderItem" column="item_id" ofType="com.pinshang.qingyun.order.mapper.entry.order.OrderItemMonitorEntry">
            <id column="item_id" property="itemId"/>
            <result column="commodity_id" property="commodityId"/>
            <result column="commodity_code" property="commodityCode"/>
            <result column="commodity_name" property="commodityName"/>
            <result column="commodity_spec" property="commoditySpec"/>
            <result column="commodity_num" property="commodityNum"/>
            <result column="commodity_price" property="commodityPrice"/>
            <result column="type" property="type"/>
            <result column="logistics_model" property="logisticsModel"/>
            <result column="comb_type" property="combType"/>
        </collection>
    </resultMap>

    <select id="queryOrderInfo" parameterType="java.lang.String" resultMap="OrderResultMap">
        SELECT
			o.id AS order_id,
			o.order_code,
			s.store_code,
			s.store_name,
			o.order_time,
			o.order_amount,
			o.final_amount,
			o.create_time,
			o.order_status,
			o.sync_status,
			o.order_type,
            o.process_status,
            o.order_duration_time,
            o.delivery_batch,
            o.change_price_status,
            o.real_sub_order_done_status,
            o.presale_status,
			item.id AS item_id,
			item.commodity_id,
			c.commodity_code,
			c.commodity_name,
			c.commodity_spec,
			item.commodity_num,
			item.commodity_price,
			item.type,
			c.logistics_model,
            tol.comb_type
		FROM t_order o
		LEFT JOIN t_store s ON o.store_id = s.id
		LEFT JOIN t_order_list_gift item ON o.id = item.order_id
		LEFT JOIN t_commodity c ON item.commodity_id = c.id
		LEFT JOIN t_order_list tol on o.id = tol.order_id and item.commodity_id = tol.commodity_id
		WHERE o.order_code = #{orderCode};
    </select>

    <!-- 子单信息  -->
    <resultMap id="SubOrderResultMap" type="com.pinshang.qingyun.order.mapper.entry.order.SubOrderMonitorEntry">
        <id column="id" property="subOrderId"/>
        <result column="subOrderCode" property="subOrderCode" />
        <result column="create_time" property="createTime"/>
        <result column="status" property="status"/>
        <result column="logistics_model" property="logisticsModel"/>
        <result column="variety_total" property="varietyTotal"/>
        <result column="update_time" property="updateTime"/>
        <result column="stock_type" property="stockType"/>
        <result column="supplier_id" property="supplierId"/>
        <result column="warehouse_id" property="warehouseId"/>
        <collection property="subOrderItem" column="item_id" ofType="com.pinshang.qingyun.order.mapper.entry.order.SubOrderItemMonitorEntry">
            <id column="item_id" property="itemId"/>
            <result column="commodity_id" property="commodityId"/>
            <result column="commodity_code" property="commodityCode"/>
            <result column="commodity_name" property="commodityName"/>
            <result column="commodity_spec" property="commoditySpec"/>
            <result column="quantity" property="quantity"/>
            <result column="price" property="price"/>
            <result column="real_delivery_quantity" property="realDeliveryQuantity"/>
            <result column="real_receive_quantity" property="realReceiveQuantity"/>
            <result column="create_time" property="createTime"/>
            <result column="comb_type" property="combType"/>
            <result column="group_commodity_id" property="groupCommodityId"/>
            <result column="group_commodity_code" property="groupCommodityCode"/>
            <result column="group_commodity_name" property="groupSubCommodityName"/>
        </collection>
    </resultMap>

    <select id="querySubOrderInfo" parameterType="java.lang.String" resultMap="SubOrderResultMap">
        SELECT
			so.id,
			so.sub_order_code AS subOrderCode,
			so.create_time,
			so.`status`,
			so.logistics_model,
			so.variety_total,
			so.update_time,
            so.stock_type,
			item.id AS item_id,
			item.commodity_id,
			c.commodity_code,
			c.commodity_name,
			c.commodity_spec,
			item.quantity,
			item.price,
			item.real_delivery_quantity,
			item.real_receive_quantity,
			item.create_time,
			item.comb_type,
            group_c.id AS group_commodity_id,
            group_c.commodity_code AS group_commodity_code,
            group_c.commodity_name AS group_commodity_name,
            so.supplier_id,
            so.warehouse_id
		FROM t_sub_order so
		INNER JOIN t_order o ON so.order_id = o.id AND o.order_code = #{orderCode}
		LEFT JOIN t_sub_order_item item ON item.sub_order_id = so.id
		LEFT JOIN t_commodity c ON item.commodity_id = c.id
        LEFT JOIN t_commodity group_c ON item.comb_commodity_id = group_c.id
    </select>
</mapper>