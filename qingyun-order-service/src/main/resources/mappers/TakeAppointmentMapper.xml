<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.pinshang.qingyun.order.mapper.TakeAppointmentCardMapper">


	<select id="selectQuantityAndNumber" resultType="com.pinshang.qingyun.order.dto.tob.TobOrderTakeQuantityAndNumberODTO">
		select
		tta.commodity_id as commodityId, tta.appoint_date as orderTime,
		count(*) as realTakeQuantity,
		ceil(count(*)/tc.commodity_package_spec) as realTakeNumber
		from t_take_applintment_card as ttac
		left join t_take_appointment as tta on tta.id = ttac.take_appointment_id
		left join t_commodity tc on tta.commodity_id = tc.id
		<where>
			<if test="orderStatus != null">
				and tta.order_status = #{orderStatus}
			</if>
			<if test="startOrderTime != null and startOrderTime != ''">
				<![CDATA[ AND tta.appoint_date >= #{startOrderTime,jdbcType=VARCHAR} ]]>
			</if>
			<if test="endOrderTime != null and endOrderTime != ''">
				<![CDATA[ AND tta.appoint_date <= #{endOrderTime,jdbcType=VARCHAR} ]]>
			</if>
			<if test="orderTimes != null and orderTimes.size() > 0">
				and tta.appoint_date in
				<foreach collection="orderTimes"  item="item" index="index" open="(" separator="," close=")">
					#{item}
				</foreach>
			</if>
			<if test="commodityIds != null and commodityIds.size() > 0">
				and tta.commodity_id in
				<foreach collection="commodityIds" item="item" index="index" open="(" separator="," close=")">
					#{item}
				</foreach>
			</if>
		</where>
		group by tta.commodity_id, tta.appoint_date
	</select>
</mapper>