<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.pinshang.qingyun.order.mapper.XdaReturnOrderItemMapper">

    <resultMap id="returnOrderItem" type="com.pinshang.qingyun.order.dto.xda.tda.ReturnOrderItemODTO">
        <id column="id" property="id"/>
        <result column="return_order_id" property="returnOrderId"/>
        <result column="commodity_id" property="commodityId"/>
        <result column="is_weight" property="isWeight"/>
        <result column="commodity_code" property="commodityCode"/>
        <result column="commodity_name" property="commodityName"/>
        <result column="commodity_app_name" property="commodityAppName"/>
        <result column="commodity_price" property="commodityPrice"/>
        <result column="return_order_type" property="returnOrderType"/>
        <result column="commodity_order_quantity" property="commodityOrderQuantity"/>
        <result column="bar_code" property="barCode"/>
        <result column="commodity_unit_name" property="commodityUnitName"/>
        <result column="commodity_package_spec" property="commodityPackageSpec"/>
        <result column="apply_number" property="applyNumber"/>
        <result column="apply_money" property="applyMoney"/>
        <result column="apply_quantity" property="applyQuantity"/>
        <result column="check_money" property="checkMoney"/>
        <result column="check_quantity" property="checkQuantity"/>
        <result column="check_number" property="checkNumber"/>
        <result column="confirm_number" property="confirmNumber"/>
        <result column="return_reason_type" property="returnReasonType"/>
        <result column="return_order_type" property="returnOrderType"/>
        <result column="return_reason" property="returnReason"/>
        <result column="responsible_party" property="responsibleParty"/>
        <result column="remark" property="remark"/>
        <result column="complaint_content" property="complaintContent"/>
        <result column="status" property="status"/>
        <result column="del_flag" property="delFlag"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <collection property="returnOrderItemPicList" ofType="com.pinshang.qingyun.order.dto.xda.tda.ReturnOrderItemPicODTO">
            <result column="pic_id" property="id"/>
            <result column="return_order_item_id" property="returnOrderItemId"/>
            <result column="img_pic_url" property="imgPicUrl"/>
        </collection>
    </resultMap>

    <insert id="batchInsertSelective" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO t_xda_return_order_item
        (return_order_id,return_order_type,return_reason_type, commodity_order_quantity,commodity_id,commodity_price, check_number, check_money, check_quantity, apply_number, apply_money, apply_quantity,complaint_content, responsible_party)
        VALUES
        <foreach collection="list" item="item" separator=",">
            (
            #{item.returnOrderId},
            #{item.returnOrderType},
            #{item.returnReasonType},
            #{item.commodityOrderQuantity},
            #{item.commodityId},
            #{item.commodityPrice},
            #{item.checkNumber},
            #{item.checkMoney},
            #{item.checkQuantity},
            #{item.applyNumber},
            #{item.applyMoney},
            #{item.applyQuantity},
            #{item.complaintContent},
            #{item.responsibleParty}
            )
        </foreach>
    </insert>
    <update id="batchUpdateByAudit">
        <foreach collection="list" item="item" index="index" open="" close="" separator=";">
            UPDATE t_xda_return_order_item
            SET check_number = #{item.checkNumber},
            check_quantity = #{item.checkQuantity},
            check_money = #{item.checkMoney},
            responsible_party = #{item.responsibleParty},
            remark = #{item.auditRemark},
            commodity_price = CASE
            WHEN commodity_price IS NULL THEN #{item.commodityPrice}
            ELSE commodity_price
            END
            WHERE id = #{item.id}
        </foreach>
    </update>
    <update id="batchUpdateByConfirm">
        <foreach collection="list" item="item" index="index" open="" close="" separator=";">
            UPDATE t_xda_return_order_item
            SET confirm_money = #{item.confirmMoney},
            confirm_number = #{item.confirmNumber},
            confirm_quantity = #{item.confirmQuantity}
            WHERE id = #{item.id}
        </foreach>
    </update>
    <select id="queryByReturnOrderId" resultMap="returnOrderItem">
        select txroi.id,
               txroi.commodity_id,
               tc.commodity_code,
               tc.commodity_name,
               tc.commodity_name,
               tc.is_weight,
               text.commodity_app_name AS commodity_app_name,
               txroi.commodity_price,
               txroi.return_order_type,
               tc.bar_code,
               d.option_name commodity_unit_name,
               tc.commodity_package_spec,
               txroi.apply_number,
               txroi.return_order_id,
               txroi.apply_quantity,
               txroi.apply_money,
               txroi.return_reason_type,
               txroi.return_order_type,
               dd.option_name return_reason,
               txroi.check_number,
               txroi.check_quantity,
               txroi.check_money,
               txroi.commodity_order_quantity,
               txroi.responsible_party,
               txroi.remark,
               txroi.complaint_content,
               txroi.status,
               txroi.del_flag,
               txroi.create_time,
               txroi.update_time,
               txroip.id as pic_id,
               txroip.return_order_item_id,
               txroip.img_pic_url
        from t_xda_return_order_item txroi
                 left join t_commodity tc on txroi.commodity_id = tc.id
                 left join t_xda_return_order txro on txro.id = txroi.return_order_id
                 LEFT JOIN t_xda_commodity_text text on tc.id = text.commodity_id
                 LEFT JOIN t_dictionary d ON d.id = tc.commodity_unit_id
                 LEFT JOIN t_dictionary dd ON dd.id = txroi.return_reason_type
                 left join t_xda_return_order_item_pic txroip on txroi.id = txroip.return_order_item_id
        where txroi.return_order_id = #{returnOrderId}
        AND (
        (txro.status = 5 and txroi.status = 1 and txroi.del_flag = 0)
            OR (txro.status != 5 and txroi.del_flag = 0)
        )
    </select>
    <select id="queryWarehousePendingItemList" resultMap="returnOrderItem">
        select txroi.id,
               txroi.commodity_id,
               tc.commodity_code,
               tc.commodity_name,
               txroi.commodity_price,
               tc.bar_code,
               d.option_name   commodity_unit_name,
               tc.commodity_package_spec,
               txroi.apply_number,
               txroi.return_order_id,
               txroi.apply_quantity,
               tc.retail_price retailPrice,
               txroi.apply_money,
               txroi.return_reason_type,
               dd.option_name return_reason,
               txroi.check_number,
               txroi.check_quantity,
               txroi.check_money,
               txroi.responsible_party,
               txroi.confirm_number,
               txroi.remark,
               txroi.status,
               txroi.del_flag,
               txroi.create_time,
               txroi.update_time,
               txroip.id as pic_id,
               txroip.return_order_item_id,
               txroip.img_pic_url
        from t_xda_return_order_item txroi
                 left join t_commodity tc on txroi.commodity_id = tc.id
                 LEFT JOIN t_dictionary d ON d.id = tc.commodity_unit_id
                 LEFT JOIN t_dictionary dd ON dd.id = txroi.return_reason_type
                 left join t_xda_return_order_item_pic txroip on txroi.id = txroip.return_order_item_id
        where txroi.return_order_id = #{returnOrderId}
    </select>
</mapper>