<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.pinshang.qingyun.order.mapper.PfPayBillJobMapper">

	<select id="findPfPayBillJob" resultType="com.pinshang.qingyun.order.mapper.entry.pf.recharge.PfPayBillJobEntry">
        SELECT
            job.pay_bill_id,
            bill.bill_code,
            bill.trade_bill_code,
            bill.pay_type,
            bill.bill_status
        FROM
            t_pf_pay_bill_job job INNER JOIN t_pf_pay_bill bill on job.pay_bill_id = bill.id
        WHERE
            bill.create_time <![CDATA[ < ]]> date_sub(date_format(NOW(), '%Y-%m-%d %H:%i'), interval 2 minute)
    </select>

</mapper>