<?xml version="1.0" encoding="UTF-8" ?>

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.pinshang.qingyun.order.mapper.common.EmployeeUserMapper">


    <select id="queryEmployeeUserByEmployeeId"  resultType="String">
        SELECT employee_name
        FROM t_employee_user
        WHERE employee_id = #{employeeId}
    </select>

    <select id="queryEmployeeUserByUserId"  resultType="String">
        SELECT employee_name
        FROM t_employee_user
        WHERE user_id = #{userId}
    </select>
</mapper>