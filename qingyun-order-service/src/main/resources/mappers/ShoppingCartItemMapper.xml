<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.pinshang.qingyun.order.mapper.ShoppingCartItemMapper">
	<delete id="deleteItemByCartId">
		DELETE FROM t_md_shopping_cart_item WHERE shopping_cart_id =#{shoppingCartId}
	</delete>

	<select id="queryShoppingCartItemList" resultType="com.pinshang.qingyun.order.model.order.ShoppingCartItem">
		SELECT
			tmsci.id,
			tmsci.shopping_cart_id,
			tmsci.commodity_id,
			tmsci.commodity_num,
			tmsci.create_time,
			tc.commodity_first_id
		FROM t_md_shopping_cart_item tmsci
		LEFT JOIN t_commodity tc ON tmsci.commodity_id = tc.id
		WHERE tmsci.shopping_cart_id = #{shoppingCartId}
		ORDER BY tc.commodity_first_id ASC
	</select>
</mapper>