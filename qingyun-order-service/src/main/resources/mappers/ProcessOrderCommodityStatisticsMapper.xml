<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pinshang.qingyun.order.mapper.ProcessOrderCommodityStatisticsMapper">


    <update id="updateStatistics" parameterType="com.pinshang.qingyun.order.model.tob.ToBProcessOrderCommodityStatistics">
        update t_dc_tob_process_order_commodity_statistics
        set
        <if test="orderQuantity != null">
            order_quantity = IFNULL(order_quantity,0) + #{orderQuantity},
        </if>
        <if test="orderNumber != null">
            order_number = IFNULL(order_number,0) + #{orderNumber},
        </if>
        update_id = #{updateId},
        update_time = #{updateTime}
        where id = #{id}
        AND (IFNULL(order_quantity,0) + #{orderQuantity} <![CDATA[ >= ]]> 0)
        AND (IFNULL(order_number,0) + #{orderNumber} <![CDATA[ >= ]]> 0)
    </update>

    <select id="selectByCommodityIdAndDate" resultType="com.pinshang.qingyun.order.model.tob.ToBProcessOrderCommodityStatistics">
        SELECT
        order_time,
        commodity_id,
        order_number
        FROM
        t_dc_tob_process_order_commodity_statistics
        WHERE commodity_id IN
        <foreach collection="commodityIdList" item="commodityId" open="(" close=")" separator=",">
            #{commodityId}
        </foreach>
        <if test="null == endDate or '' == endDate">
            AND order_time = #{beginDate}
        </if>
        <if test="null != endDate and '' != endDate">
            AND order_time BETWEEN #{beginDate} and #{endDate}
        </if>
    </select>

</mapper>