<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.pinshang.qingyun.order.mapper.orderStatistics.OrderStatisticsMonitorMapper" >

    <sql id="getCondition">
        <where>
            <if test="list != null">
                AND o.order_code IN
                <foreach collection="list" item="orderCode" open="(" close=")" separator=",">
                    #{orderCode}
                </foreach>
            </if>
            <if test="vo.filterOrderTypeList != null and  vo.filterOrderTypeList.size > 0">
                AND o.order_type NOT IN
                <foreach collection="vo.filterOrderTypeList" item="orderType" open="(" close=")" separator=",">
                    #{orderType}
                </foreach>
            </if>
            <if test="vo.orderTime != null">
                AND o.order_time = date(#{vo.orderTime})
            </if>
            <if test="vo.startTime !=null and vo.endTime !=null">
                AND o.order_time BETWEEN date(#{vo.startTime}) AND date(#{vo.endTime})
            </if>
            <if test="vo.sysTime != null">
                AND o.create_time != o.update_time
                AND o.create_time <![CDATA[ < ]]> #{vo.sysTime}
            </if>
            <if test="null != vo.startCreateTime and null != vo.endCreateTime">
                AND o.create_time BETWEEN #{vo.startCreateTime} AND #{vo.endCreateTime}
            </if>
        </where>
    </sql>

    <!-- 查询订单统计信息，用于订单比较 -->
    <select id="queryOrderStatisticsInfo" resultType="com.pinshang.qingyun.order.mapper.entry.orderStatistics.OrderStatisticsEntry">
        SELECT
            COUNT(o.id) AS totalOrderCount,
            SUM(IF(o.order_status=0,1,0)) AS normalOrderCount,
            SUM(o.final_amount) AS totalOrderAmount
        FROM t_order o
        <include refid="getCondition"/>
    </select>

    <!-- 查询订单编号，用于比较差异订单 -->
    <select id="queryOrderDiffList" resultType="java.lang.String">
        SELECT
            o.order_code
        FROM t_order o
        <include refid="getCondition"/>
    </select>

    <!-- 查询订单主表信息，用于订单同步-->
    <select id="queryOrderList" resultType="com.pinshang.qingyun.order.mapper.entry.orderStatistics.OrderSyncEntry">
        SELECT
            o.id,
            o.company_id,
            o.order_code,
            o.store_id,
            o.order_time,
            o.order_type,
            o.mode_type,
            o.print_num,
            o.print_type,
            o.total_amount,
            o.final_amount,
            o.order_amount,
            o.freight_amount,
            o.order_remark,
            o.create_id,
            o.create_time,
            o.update_id,
            o.update_time,
            o.order_status,
            o.settle_status,
            o.delivery_batch,
            o.change_price_status,
           <!--  CASE o.order_type WHEN 2 THEN s.store_name ELSE u1.employee_name END AS createName,
            CASE o.order_type WHEN 2 THEN s.store_name ELSE u2.employee_name END AS updateName -->
            (CASE o.order_type 
        	WHEN 2 THEN s.store_name -- 清美生鲜App
        	WHEN 8 THEN s.store_name -- 清美鲜达App
        	WHEN 9 THEN s.store_name -- 清美批发App
        	ELSE u1.employee_name END) AS createName,
        	(CASE o.order_type 
        	WHEN 2 THEN s.store_name -- 清美生鲜App
        	WHEN 8 THEN s.store_name -- 清美鲜达App
        	WHEN 9 THEN s.store_name -- 清美批发App
        	ELSE u2.employee_name END) AS updateName
        FROM t_order o
        INNER JOIN t_store s ON o.store_id = s.id
        LEFT JOIN t_employee_user u1 ON o.create_id = u1.user_id
        LEFT JOIN t_employee_user u2 ON o.update_id = u2.user_id
        <include refid="getCondition"/>
    </select>

    <select id="queryOrderMirrorSyncList" resultType="com.pinshang.qingyun.order.mapper.entry.orderStatistics.OrderMirrorSyncEntry">
        SELECT
            o.id AS orderId,
            o.order_code AS orderCode,
            om.delivery_man_id AS deliverymanId,
            om.delivery_man_name AS deliverymanName,
            om.salesman_id AS salesmanId,
            om.salesman_name AS salesmanName,
            om.supervision_id AS supervisorId,
            om.supervision_name AS supervisorName,
            om.regional_manager_id AS regionManagerId,
            om.regional_manager_name AS regionManagerName,
            om.director_id AS officeDirectorId,
            om.director_name AS officeDirectorName
        FROM
            t_order o
        INNER JOIN t_order_mirror om ON o.id = om.order_id
        <include refid="getCondition"/>
    </select>
    <select id="orderByCompanyAndStoreTypeReport" resultType="com.pinshang.qingyun.order.mapper.entry.orderStatistics.OrderCompanyAndStoreTypeEntry">
    select c.company_name as companyName, d.option_name as storeTypeName,sum(o.final_amount) as  totalAmount
        from t_order o
        inner join t_store s on o.store_id = s.id
        inner join t_store_company sc on sc.store_id = s.id
        inner join t_company c on c.id = sc.company_id
        inner join t_dictionary d on d.id = s.store_type_id
    where o.order_time between #{startOrderTime} and #{endOrderTime}  and o.order_status =0
    group by c.id , s.store_type_id
    </select>

</mapper>