<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pinshang.qingyun.order.mapper.CommodityItemMapper">

    <!--审批id,查询组合商品 子商品明细-->
    <select id="findCommodityItemListByCommodityId" resultType="com.pinshang.qingyun.order.mapper.entry.commodity.CommodityItemEntry">
        SELECT
            ci.id,
            ci.commodity_item_id as commodityItemId,
            ci.commodity_num,
            c.commodity_code,
            c.commodity_name,
            c.commodity_spec,
            d.option_name as commodityUnitName
        FROM
            t_commodity_item ci
        LEFT JOIN t_commodity c ON c.id = ci.commodity_item_id
        LEFT JOIN t_dictionary d ON d.id = c.commodity_unit_id
        WHERE
            ci.commodity_id =  #{commodityId}
    </select>
    <select id="findCommodityItemListByCommodityIdList"
            resultType="com.pinshang.qingyun.order.mapper.entry.commodity.CommodityItemEntry">
        SELECT
            ci.id,
            ci.commodity_id as commodityId,
            ci.commodity_item_id as commodityItemId,
            ci.commodity_num,
            c.commodity_code,
            c.commodity_name,
            c.commodity_spec,
            d.option_name as commodityUnitName
        FROM
            t_commodity_item ci
                LEFT JOIN t_commodity c ON c.id = ci.commodity_item_id
                LEFT JOIN t_dictionary d ON d.id = c.commodity_unit_id
        WHERE
            ci.commodity_id  IN
        <foreach item="item" index="index" collection="commodityIdList" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <select id="findCommodityMasterListByCommodityItemId" resultType="com.pinshang.qingyun.order.mapper.entry.commodity.CommodityItemEntry">
        SELECT
            ci.id,
            ci.commodity_id,
            ci.commodity_item_id as commodityItemId,
            ci.commodity_num,
            c.commodity_code,
            c.commodity_name,
            c.commodity_spec,
            d.option_name as commodityUnitName
        FROM
            t_commodity_item ci
                LEFT JOIN t_commodity c ON c.id = ci.commodity_item_id
                LEFT JOIN t_dictionary d ON d.id = c.commodity_unit_id
        WHERE
            ci.commodity_item_id =  #{commodityItemId}
    </select>
</mapper>