<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.pinshang.qingyun.order.mapper.ProductPriceModelListMapper">
	<select id="getModelPrice" resultType="java.math.BigDecimal">
		SELECT
			ppml.commodity_price
		FROM
			t_product_price_model_list AS ppml
		INNER JOIN `t_product_price_model` AS ppm ON ppm.id = ppml.product_price_model_id
		INNER JOIN t_store_settlement AS s ON ppm.id = s.`product_price_model_id`
		WHERE
			s.store_id = #{storeId}
		AND ppml.commodity_id = #{commodityId}
		LIMIT 1;	
	</select>
	<select id="findCommodityPriceByStoreIdAndCommodityId" resultType="com.pinshang.qingyun.order.model.order.ProductPriceModelList">
		select
			ppml.*
		from
			`t_store_settlement` s
				inner join `t_product_price_model` ppm  on ppm.`id` = s.`product_price_model_id`
				inner join `t_product_price_model_list` ppml  on ppml.`product_price_model_id` = ppm.`id`
				inner join `t_commodity` c  on ppml.`commodity_id` = c.`id`
		where  s.`store_id` = #{storeId}
		and c.`id` IN
		<foreach collection="commodityIdList" index="index" item="commodityId" open="(" separator="," close=")">
			#{commodityId}
		</foreach>
	</select>

</mapper>