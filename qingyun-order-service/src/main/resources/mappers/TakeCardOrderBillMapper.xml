<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.pinshang.qingyun.order.mapper.TakeCardOrderBillMapper">

    <insert id="insertTakeCardOrderBills" parameterType="com.pinshang.qingyun.order.model.order.TakeCardOrderBill">
        insert into t_take_card_order_bill
            (card_no, card_sn, store_id, shop_code, shop_name,
             appoint_date, commodity_id, commodity_code, commodity_name, real_quantity,
             commodity_price, real_total_price, create_id,  update_id)
    values
        <foreach collection="list" separator="," item="b">
        (#{b.cardNo}, #{b.cardSn}, #{b.storeId}, #{b.shopCode}, #{b.shopName},
         #{b.appointDate}, #{b.commodityId}, #{b.commodityCode}, #{b.commodityName}, #{b.realQuantity},
         #{b.commodityPrice}, #{b.realTotalPrice}, #{b.createId},  #{b.updateId})
        </foreach>
    </insert>

    <select id="findTakeCardOrderBillByStatus"  resultType="com.pinshang.qingyun.order.model.order.TakeCardOrderBill">
        select id, card_no, card_sn, store_id, shop_code, shop_name, appoint_date, commodity_id, commodity_code, commodity_name, real_quantity, commodity_price, real_total_price, create_id, create_time, update_id, update_time
        from t_take_card_order_bill
        <where>
             status=0
            and update_time &gt; DATE_SUB(NOW(), INTERVAL 7 DAY)
            limit 10
        </where>
    </select>

    <update id="upStatusById" parameterType="com.pinshang.qingyun.order.model.order.TakeCardOrderBill">
        update t_take_card_order_bill set status=#{status},remark=#{remark} where id=#{id}
    </update>



</mapper>