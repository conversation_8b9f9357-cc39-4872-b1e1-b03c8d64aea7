<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.pinshang.qingyun.order.mapper.PrePaidAccountReqMapper" >
    <insert id="insertRecord" parameterType="com.pinshang.qingyun.order.model.order.PrePaidAccountReq" 
    	useGeneratedKeys="true" keyProperty="id"> 
    	insert into t_pre_paid_account_req
    		(req_type, store_id, amount, remarks, req_status, err_msg,
			ref_bill_type, ref_bill_id, ref_bill_time,    		
   			creater_id, create_time, update_time)
    	values(#{reqType},#{storeId},#{amount},#{remarks},#{reqStatus}, #{errMsg}, 
    		#{refBillType}, #{refBillId}, #{refBillTime},
    		#{createrId}, #{createTime}, #{updateTime})
    </insert>
</mapper>