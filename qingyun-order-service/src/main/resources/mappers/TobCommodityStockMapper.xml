<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.pinshang.qingyun.order.mapper.TobCommodityStockMapper" >

    <insert id="batchInsert" parameterType="com.pinshang.qingyun.order.dto.tob.ToBStockRepairIDTO">
        INSERT INTO t_dc_tob_commodity_stock
            (commodity_id, warehouse_id, stock_type, limit_number, effect_type, stock_status, tda_stock_status, create_time, update_time)
        VALUES
        <foreach collection="list" separator="," item="item">
            (#{item.commodityId}, #{item.warehouseId}, #{item.stockType}, #{item.limitNumber}, #{item.effectType}, #{item.stockStatus}, #{item.tdaStockStatus}, #{item.createTime}, #{item.updateTime})
        </foreach>
    </insert>

    <update id="batchUpdate">
        <foreach collection="tobCommodityStockList" item="item" index="index" separator=";" open="" close="">
            UPDATE t_dc_tob_commodity_stock
            SET
                warehouse_id = #{item.warehouseId},
                stock_type = #{item.stockType},
                limit_number = #{item.limitNumber},
                effect_type = #{item.effectType},
                stock_status = #{item.stockStatus},
                update_time = #{item.updateTime}
            where commodity_id = #{item.commodityId}
        </foreach>
    </update>

    <select id="selectCommodityIdByStockType" resultType="java.lang.Long">
        SELECT commodity_id FROM t_dc_tob_commodity_stock WHERE stock_type = #{stockType}
    </select>

    <select id="selectStockByCommodityIdList" resultType="com.pinshang.qingyun.order.model.tob.TobCommodityStock">
        SELECT
        commodity_id,
        stock_type,
        effect_type,
        limit_number,
        stock_status,
        tda_stock_status
        FROM
        t_dc_tob_commodity_stock
        WHERE commodity_id IN
        <foreach collection="list" item="commodityId" open="(" close=")" separator=",">
            #{commodityId}
        </foreach>
    </select>
</mapper>