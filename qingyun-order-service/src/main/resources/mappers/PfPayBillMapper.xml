<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.pinshang.qingyun.order.mapper.PfPayBillMapper">

	<select id="findPfPayBillByBillCodes" resultType="com.pinshang.qingyun.order.mapper.entry.pf.recharge.PfPayBillEntry">
        SELECT
            id,store_id, bill_code,refer_type,refer_id,pay_amount,pay_type,bill_status,tn,trade_bill_code,prepay_json_alipay,prepay_json_wechat,prepay_json_mini,prepay_json_union,create_id
        FROM
            t_pf_pay_bill
        <where>
            bill_code IN
            <foreach collection="billCodes" open="(" separator="," close=")" item="item">
                #{item}
            </foreach>
        </where>
    </select>

</mapper>