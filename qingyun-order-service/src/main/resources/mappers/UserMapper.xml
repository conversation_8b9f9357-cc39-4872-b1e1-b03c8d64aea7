<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.pinshang.qingyun.order.mapper.UserMapper">

    <select id = "getEmployeeUserByUserId" resultType="com.pinshang.qingyun.order.model.order.User" >
        select
             eu.user_id,
             eu.employee_code,
             eu.employee_name
          from t_employee_user eu
          where 1=1
         AND  eu.user_id in
        <foreach collection="userIdList" item = "userId" open="(" separator="," close=")">
            #{userId}
        </foreach>
    </select>

</mapper>