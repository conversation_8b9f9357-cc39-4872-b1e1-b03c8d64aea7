<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.pinshang.qingyun.order.mapper.XdaReturnOrderMapper">
    <resultMap id="commodityItem" type="com.pinshang.qingyun.order.dto.xda.XdaComplaintOrderODTO" >
        <id column="uid" property="complainId"/>
        <result column="complaint_code" property="complaintCode"/>
        <result column="deliveryDate" property="deliveryDate"/>
        <result column="pick_up_time_range" property="pickUpTimeRange"/>
        <result column="created_at" property="createTime"/>
        <result column="complainTime" property="complainTime"/>
        <result column="complaint_handle_status" property="complaintHandleStatus"/>
        <result column="complaint_remark" property="complaintReason"/>
        <result column="orderComplaintType" property="orderComplaintType"/>
        <collection property="complaintCommodityList" ofType="com.pinshang.qingyun.order.dto.xda.XdaComplaintCommodityItemDTO">
            <id column="item_id" property="id"/>
            <result column="commodity_id" property="commodityId"/>
            <result column="commodityName" property="commodityName"/>
            <result column="complaint_type" property="complaintType"/>
            <result column="default_pic_url" property="commodityPicUrl"/>
            <result column="question_type" property="questionType"/>
            <result column="commodity_order_quantity" property="realDeliveryQuantity"/>
            <result column="realDeliveryQuantityStr" property="realDeliveryQuantityStr"/>
            <result column="complaint_number" property="realReturnQuantity"/>
            <result column="realReturnQuantityStr" property="realReturnQuantityStr"/>
            <result column="complaint_money" property="complaintMoney"/>
            <result column="commodity_price" property="commodityPrice"/>
            <result column="commodityUnit" property="commodityUnit"/>
            <result column="commodity_code" property="commodityCode"/>
            <result column="commodity_spec" property="commoditySpec"/>
            <result column="commodityPriceName" property="commodityPriceName"/>
            <result column="check_quantity" property="checkQuantity"/>
            <result column="checkQuantityStr" property="checkQuantityStr"/>
        </collection>
    </resultMap>

    <select id="queryReturnOrderList" resultType="com.pinshang.qingyun.order.dto.xda.tda.ReturnOrderODTO">
        select
        txro.id,
        txro.store_id,
        ts.store_code as storeCode,
        ts.store_name as storeName,
        d.option_name as storeTypeName,
        txro.return_order_code,
        txro.return_order_seq,
        txro.return_type,
        txro.source_order_id,
        txro.source_order_code,
        txro.phone,
        txro.logistics_center_id,
        txro.return_order_type as returnOrderType,
        txro.status,
        txro.delivery_batch,
        txro.delivery_time,
        txro.pick_up_time_range,
        txro.delivery_address,
        txro.create_time,
        txro.driver_id,
        txro.driver_name,
        txro.waybill_code as waybillCode,
        txro.business_type
        from
        t_xda_return_order txro
        left join t_store ts on txro.store_id = ts.id
        LEFT JOIN t_dictionary d ON d.id = ts.store_type_id
        <where>
            <if test="deliveryBeginTime != null and deliveryBeginTime !='' ">
                and txro.delivery_time &gt;= #{deliveryBeginTime}
            </if>
            <if test="deliveryEndTime != null and deliveryEndTime !='' ">
                and txro.delivery_time &lt;= #{deliveryEndTime}
            </if>
            <if test="status != null">
                and txro.status = #{status}
            </if>
            <if test="phone != null  and phone !='' ">
                and txro.phone = #{phone}
            </if>
            <if test="returnType != null">
                and txro.return_type = #{returnType}
            </if>
            <if test="returnOrderSeq != null  and returnOrderSeq !='' ">
                and txro.return_order_seq = #{returnOrderSeq}
            </if>
            <if test="sourceOrderCode != null  and sourceOrderCode !='' ">
                and txro.source_order_code = #{sourceOrderCode}
            </if>
            <if test="logisticsCenterId != null">
                and txro.logistics_center_id = #{logisticsCenterId}
            </if>
            <if test="storeId != null">
                AND txro.store_id = #{storeId}
            </if>
            <if test="deliveryBatch != null  and deliveryBatch !='' ">
                and txro.delivery_batch = #{deliveryBatch}
            </if>
            <if test="returnOrderType != null">
                and txro.return_order_type = #{returnOrderType}
            </if>
            <if test="waybillCode != null  and waybillCode !='' ">
                and txro.waybill_code = #{waybillCode}
            </if>
            and txro.create_time between #{createBeginTime} and #{createEndTime}
            order by txro.create_time desc
        </where>
    </select>
    <select id="selectById" resultType="com.pinshang.qingyun.order.dto.xda.tda.ReturnOrderODTO">
        select
            txro.id,
            txro.store_id,
            ts.store_code as storeCode,
            ts.store_name as storeName,
            d.option_name as storeTypeName,
            txro.return_order_code,
            txro.return_order_seq,
            txro.return_type,
            txro.return_source,
            txro.source_order_id,
            txro.source_order_code,
            txro.phone,
            txro.logistics_center_id,
            txro.return_order_type,
            txro.status,
            txro.pick_up_time,
            txro.pick_up_time_range,
            txro.delivery_batch,
            txro.delivery_time,
            txro.delivery_end_time,
            txro.delivery_address,
            txro.create_time,
            txro.business_type,
            txro.waybill_code,
            txro.check_user_id,
            txro.check_time,
            txro.total_apply_money,
            txro.total_check_money,
            txro.driver_id,
            txro.driver_name,
            txro.remark
        from
            t_xda_return_order txro
                left join t_store ts on txro.store_id = ts.id
                LEFT JOIN t_dictionary d ON d.id = ts.store_type_id
        where
            txro.id = #{id}

    </select>
    <select id="queryWarehousePendingList" resultType="com.pinshang.qingyun.order.dto.xda.tda.WarehousePendingODTO">
        select
        txro.id,
        txro.store_id,
        ts.store_code as storeCode,
        ts.store_name as storeName,
        d.option_name as storeTypeName,
        txro.return_order_code,
        txro.return_order_seq,
        txro.return_type,
        txro.source_order_id,
        txro.source_order_code,
        txro.phone,
        txro.logistics_center_id,
        txro.return_order_type,
        txro.status,
        txro.delivery_end_time,
        txro.pick_up_time_range,
        txro.delivery_address,
        txro.waybill_code,
        txro.driver_id,
        txro.driver_name,
        txro.business_type,
        txro.confirm_time,
        txro.confirm_user_id
        from
        t_xda_return_order txro
        left join t_store ts on txro.store_id = ts.id
        LEFT JOIN t_dictionary d ON d.id = ts.store_type_id
        <where>
            <if test="status != null">
                and txro.status = #{status}
            </if>
            <if test="waybillCode != null and waybillCode !='' ">
                and txro.waybill_code = #{waybillCode}
            </if>
            <if test="phone != null and phone !='' ">
                and txro.phone = #{phone}
            </if>
            <if test="returnType != null">
                and txro.return_type = #{returnType}
            </if>
            <if test="returnOrderType != null">
                and txro.return_order_type = #{returnOrderType}
            </if>
            <if test="returnOrderSeq != null and returnOrderSeq !='' ">
                and txro.return_order_seq = #{returnOrderSeq}
            </if>
            <if test="sourceOrderCode != null  and sourceOrderCode !='' ">
                and txro.source_order_code = #{sourceOrderCode}
            </if>
            <if test="driverId != null">
                and txro.driver_id = #{driverId}
            </if>
            and txro.return_order_type = 1
            <if test="pickBeginTime != null  and pickBeginTime !='' ">
                and txro.delivery_end_time &gt; #{pickBeginTime}
            </if>
            <if test="pickEndTime != null  and pickEndTime !='' ">
                and txro.delivery_end_time &lt; #{pickEndTime}
            </if>
            <if test="confirmBeginTime != null  and confirmBeginTime !='' ">
                and txro.confirm_time &gt; #{confirmBeginTime}
            </if>
            <if test="confirmEndTime != null  and confirmEndTime !='' ">
                and txro.confirm_time &lt; #{confirmEndTime}
            </if>
            <if test="status == 4">
                order by txro.delivery_end_time desc
            </if>
            <if test="status == 6">
                order by txro.confirm_time desc
            </if>
        </where>
    </select>
    <select id="queryReturnOrderByDeliveryDateAndStoreId"
            resultType="com.pinshang.qingyun.order.dto.xda.XdaComplaintCommodityItemDTO">
        SELECT
        txro.store_id,
        CASE
        WHEN txro.return_order_type = 1 THEN 2
        WHEN txro.return_order_type = 2 THEN 1
        WHEN txro.return_order_type = 3 THEN 3
        ELSE 0
        END as complaintType,
        txro.pick_up_time_range as pickUpTimeRange,
        txroi.commodity_id,
        txroi.commodity_order_quantity  as realDeliveryQuantity,
        txroi.apply_quantity AS realReturnQuantity,
        txroi.commodity_price,
        text.commodity_app_name AS commodityName,
        pic.pic_url AS commodityPicUrl,
        com.is_weight,
        com.commodity_code,
        com.commodity_spec
        from t_xda_return_order txro
        LEFT JOIN t_xda_return_order_item txroi on txro.id = txroi.return_order_id
        LEFT JOIN t_commodity com on txroi.commodity_id = com.id
        LEFT JOIN t_xda_commodity_text text on com.id = text.commodity_id
        LEFT JOIN t_xda_commodity_text_pic pic on com.id = pic.commodity_id AND pic.is_default = 1 AND pic.pic_type = 1
        WHERE txro.store_id = #{storeId}
        AND txro.delivery_time = #{deliveryDate}
        <if test="returnOrderType != null">
            AND txro.return_order_type = #{returnOrderType}
        </if>
        AND txro.return_source in(1,2)
        AND txro.status in (1,2,3,4,6)
        AND (txroi.status = 0 AND txroi.del_flag=0)
    </select>
    <select id="queryComplaintOrderList" resultMap="commodityItem">
        SELECT
        complain.return_order_code AS uid,
        complain.return_order_seq complaint_code,
        DATE_FORMAT( complain.delivery_time, '%Y-%m-%d' ) AS deliveryDate,
        complain.create_time,
        (CASE WHEN complain.update_time - complain.create_time > 0 THEN
        CONCAT( complain.update_time,' 更新') ELSE
        CONCAT( complain.update_time,' 创建') END
        ) AS complainTime,
        CASE
            WHEN complain.status = 1 THEN 1
            WHEN complain.status = 2 THEN 5
            WHEN complain.status = 3 THEN 8
            WHEN complain.status = 4 THEN 4
            WHEN complain.status = 5 THEN 3
            WHEN complain.status = 6 THEN 6
            WHEN complain.status = 7 THEN 7
        ELSE 1
        END as complaint_handle_status,
        complain.remark complaint_remark,
        complain.pick_up_time_range  pick_up_time_range,
        CASE
        WHEN complain.return_order_type = 1 THEN 1
        WHEN complain.return_order_type = 2 THEN 0
        ELSE 0
        END as orderComplaintType,
        CASE
        WHEN item.return_order_type = 1 THEN 2
        WHEN item.return_order_type = 2 THEN 1
        WHEN item.return_order_type = 3 THEN 3
        ELSE 0
        END as complaint_type,
        item.id item_id,
        item.return_reason_type as questionType,
        item.commodity_order_quantity ,
        item.apply_quantity complaintNumber,
        item.apply_money complaintMoney,
        item.check_quantity,
        CONCAT(ABS(item.check_quantity), dic.option_name) AS checkQuantityStr,
        item.commodity_id,
        item.commodity_price,
        text.commodity_app_name AS commodityName,
        com.commodity_code,
        com.commodity_spec,
        pic.pic_url AS default_pic_url,
        dic.option_name AS commodityUnit,
        CONCAT(ABS(item.apply_quantity), dic.option_name) AS realReturnQuantityStr,
        CONCAT(ABS(item.commodity_order_quantity), dic.option_name) AS realDeliveryQuantityStr,
        CONCAT('￥', item.commodity_price, '/', dic.option_name) AS commodityPriceName
        FROM t_xda_return_order complain
        LEFT JOIN t_xda_return_order_item item on complain.id = item.return_order_id
        LEFT JOIN t_commodity com on item.commodity_id = com.id
        LEFT JOIN t_xda_commodity_text text on com.id = text.commodity_id
        LEFT JOIN t_xda_commodity_text_pic pic on com.id = pic.commodity_id AND pic.is_default = 1 AND pic.pic_type = 1
        LEFT JOIN t_dictionary dic on com.commodity_unit_id = dic.id
        WHERE complain.store_id = #{storeId}
        <if test="status != null">
            <choose>
                <when test="status == 1">
                    AND complain.status = 1
                </when>
                <when test="status == 2">
                    AND complain.status IN (2, 3, 4, 6, 7)
                </when>
            </choose>
        </if>
        AND item.del_flag = 0
        AND (
        (complain.status = 5 AND item.status = 1)
        OR complain.status != 5
        )
        AND complain.return_source in (1,2)
        AND complain.create_time >= date_sub(curdate(), interval 3 month)
        AND CASE WHEN item.return_order_type = 1 THEN item.return_reason_type IN (9131078242860601471, 9131078242860601472, 9131078242860601473, 9131078242860601474)
        ELSE item.return_reason_type IN (9131078242860604176, 9131078242860601561)
        END
        ORDER BY complain.create_time DESC, item.create_time DESC
    </select>
    <select id="selectByReturnOrderCode" resultType="com.pinshang.qingyun.order.model.xda.XdaReturnOrder">
        select
            txro.id,
            txro.store_id,
            ts.store_code as storeCode,
            ts.store_name as storeName,
            d.option_name as storeTypeName,
            txro.return_order_code,
            txro.return_order_seq,
            txro.return_type,
            txro.return_source,
            txro.source_order_id,
            txro.source_order_code,
            txro.phone,
            txro.logistics_center_id,
            txro.return_order_type,
            txro.status,
            txro.pick_up_time,
            txro.pick_up_time_range,
            txro.delivery_batch,
            txro.delivery_time,
            txro.delivery_address,
            txro.create_time,
            txro.business_type,
            txro.check_user_id,
            txro.check_time,
            txro.total_apply_money,
            txro.remark
        from
            t_xda_return_order txro
                left join t_store ts on txro.store_id = ts.id
                LEFT JOIN t_dictionary d ON d.id = ts.store_type_id
        where
            txro.return_order_code = #{returnOrderCode}
    </select>
    <select id="selectByReturnOrderSeq" resultType="com.pinshang.qingyun.order.model.xda.XdaReturnOrder">
        select
            txro.id,
            txro.store_id,
            ts.store_code as storeCode,
            ts.store_name as storeName,
            d.option_name as storeTypeName,
            txro.return_order_code,
            txro.return_order_seq,
            txro.return_type,
            txro.return_source,
            txro.source_order_id,
            txro.source_order_code,
            txro.phone,
            txro.logistics_center_id,
            txro.return_order_type,
            txro.status,
            txro.pick_up_time,
            txro.pick_up_time_range,
            txro.delivery_batch,
            txro.delivery_time,
            txro.delivery_address,
            txro.create_time,
            txro.business_type,
            txro.check_user_id,
            txro.check_time,
            txro.total_apply_money,
            txro.remark
        from
            t_xda_return_order txro
                left join t_store ts on txro.store_id = ts.id
                LEFT JOIN t_dictionary d ON d.id = ts.store_type_id
        where
            txro.return_order_seq = #{returnOrderSeq}
    </select>

    <select id="queryCompleteCommodityQuantity"
            resultType="com.pinshang.qingyun.order.dto.xda.XdaComplaintCommodityQuantityDTO">
        SELECT
        txro.store_id,
        txroi.commodity_id,
        txroi.commodity_price,
        tc.is_weight,
        CASE
        WHEN txroi.return_order_type = 1 THEN 2
        WHEN txroi.return_order_type = 2 THEN 1
        WHEN txroi.return_order_type = 3 THEN 3
        ELSE 0
        END as complaintType,
        txroi.apply_quantity AS realReturnQuantity
        from t_xda_return_order txro
        LEFT JOIN t_xda_return_order_item txroi on txro.id = txroi.return_order_id
        LEFT JOIN t_commodity tc on txroi.commodity_id = tc.id
        WHERE txro.store_id = #{storeId}
        AND txro.delivery_time = #{deliveryDate}
        AND txro.return_source = 1
        <if test="returnOrderType != null">
            AND txro.return_order_type = #{returnOrderType}
        </if>
        <if test="commodityIdList != null">
            AND txroi.commodity_id in
            <foreach item="item" index="index" collection="commodityIdList" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        AND txro.status in (1,2,3,4,6)
        AND (txroi.status = 0 AND txroi.del_flag=0)
        and tc.is_weight = 0
    </select>

</mapper>