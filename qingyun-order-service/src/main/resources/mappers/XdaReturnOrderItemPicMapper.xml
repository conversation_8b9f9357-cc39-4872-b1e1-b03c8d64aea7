<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.pinshang.qingyun.order.mapper.XdaReturnOrderItemPicMapper">

    <insert id="batchInsertSelective"  useGeneratedKeys="true" keyProperty="id">
        INSERT INTO t_xda_return_order_item_pic
        (return_order_item_id, img_pic_url)
        VALUES
        <foreach collection="list" item="item" separator=",">
            (
            #{item.returnOrderItemId},
            #{item.imgPicUrl}
            )
        </foreach>
    </insert>
</mapper>