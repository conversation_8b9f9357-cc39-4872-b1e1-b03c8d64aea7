<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.pinshang.qingyun.order.mapper.GiftModelMapper" >

    <select id="findGiftModelByStoreId" resultType="com.pinshang.qingyun.order.model.gift.GiftModel">
        select
            distinct gm.*
        from
            `t_gift_model` gm ,
            `t_gift_model_scope` ms ,
            `t_store_settlement` ss ,
            `t_product_price_model` ppm ,
            `t_store` s
        where 1=1
          and gm.`id` = ms.`gift_model_id`
          and ss.`product_price_model_id` = ppm.`id`
          and ss.`store_id` = s.`id`
          and gm.`status` = 1
          and #{orderTime} between gm.`begin_date` and gm.`end_date`
          and (ms.`type_id` = ss.`settlement_customer_id` or ms.`type_id` = ppm.`id` or ms.`type_id` = s.`id`)
          and s.`id` = #{storeId}
        order by gm.`create_time` desc
    </select>
</mapper>