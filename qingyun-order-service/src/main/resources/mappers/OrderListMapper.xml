<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.pinshang.qingyun.order.mapper.OrderListMapper">
    <select id="getGiftedOrStkedNumForShop" resultType="com.pinshang.qingyun.order.model.order.OrderList">
        SELECT
            <choose>
                <when test="type == 2">  concat(gift_model_id, '-', tol.commodity_id) </when>
                <when test="type == 3">  concat(promotion_id, '-', tol.commodity_id) </when>
            </choose> AS modeIdAndCommodityIdKey,
            tol.commodity_id,
            sum(tol.commodity_num) as commodityNum
        FROM t_order_list tol
        LEFT JOIN t_order o ON tol.order_id = o.id
        WHERE store_id = #{storeId} AND o.order_time = #{orderTime} AND tol.type = #{type} AND o.order_status = 0
        AND tol.commodity_id IN
        <foreach collection="commodityIdList" index="index" item="commodityId" open="(" separator="," close=")">
            #{commodityId}
        </foreach>
        <choose>
            <when test="type == 2"> AND gift_model_id = #{giftModelOrPromotionId} </when>
            <when test="type == 3"> AND promotion_id = #{giftModelOrPromotionId} </when>
        </choose>
        GROUP BY
            <choose>
                <when test="type == 2">  gift_model_id, tol.commodity_id</when>
                <when test="type == 3">  promotion_id, tol.commodity_id</when>
            </choose>
        HAVING tol.commodity_id IS NOT NULL
    </select>

    <select id="queryCommodityLimit" resultType="com.pinshang.qingyun.order.dto.xda.XdaCommodityLimit4AppODTO">
        SELECT
        tol.commodity_id as commodityId,
        sum(tol.commodity_num) as totalQuantity
        FROM t_order_list_gift tol
        LEFT JOIN t_order o ON tol.order_id = o.id
        WHERE o.order_time = #{orderTime} AND o.order_status = 0
        AND tol.commodity_id IN
        <foreach collection="commodityIdList" index="index" item="commodityId" open="(" separator="," close=")">
            #{commodityId}
        </foreach>
        <if test="null != promotionId">
            and tol.gift_model_id = #{promotionId}
        </if>

        GROUP BY tol.commodity_id
        HAVING tol.commodity_id IS NOT NULL
    </select>
    <select id="queryCommodityLimitByStoreId" resultType="com.pinshang.qingyun.order.dto.xda.XdaCommodityLimit4AppODTO">
        SELECT
        tol.commodity_id as commodityId,
        sum(tol.commodity_num) as totalQuantity,
        tol.type AS commodityType,
        tol.gift_model_id AS giftModelId
        FROM t_order_list_gift tol
        LEFT JOIN t_order o ON tol.order_id = o.id
        WHERE  tol.commodity_id IN
        <foreach collection="commodityIdList" index="index" item="commodityId" open="(" separator="," close=")">
            #{commodityId}
        </foreach>
        AND o.order_time = #{orderTime} AND o.order_status = 0
        <if test="null != storeId">
            AND o.store_id = #{storeId}
        </if>
          <if test="null != promotionId">
              and tol.gift_model_id = #{promotionId}
          </if>
        <if test="null != orderId">
            and o.id != #{orderId}
        </if>
        <if test="null != type and type == 1">
            AND tol.type in(1,4)
            GROUP BY tol.commodity_id,tol.type
        </if>
        <if test="null != type and type == 2">
            AND tol.type = 2 and tol.gift_model_id is NOT NULL
            GROUP BY tol.commodity_id,tol.type,tol.gift_model_id
        </if>
--         GROUP BY tol.commodity_id
--         HAVING tol.commodity_id IS NOT NULL
    </select>

    <select id="queryPfCommodityLimit" resultType="com.pinshang.qingyun.order.dto.pf.PfCommodityLimit4AppODTO">
        SELECT
        tol.commodity_id as commodityId,
        sum(tol.commodity_num) as totalQuantity
        FROM t_order_list tol
        LEFT JOIN t_order o ON tol.order_id = o.id
        WHERE o.order_type = 9
        AND o.order_status = 0
        <if test="null != idtoList and idtoList.size() > 0">
            AND
            <foreach collection="idtoList" index="index" item="item" open="(" separator="OR" close=")">
                (tol.commodity_id = #{item.commodityId} AND o.order_time BETWEEN #{item.startTime} AND #{item.endTime})
            </foreach>
        </if>
        GROUP BY tol.commodity_id
        HAVING tol.commodity_id IS NOT NULL
    </select>
    <select id="queryPfCommodityV2Limit" resultType="com.pinshang.qingyun.order.dto.pf.PfCommodityLimit4AppODTO">
        SELECT
        tol.commodity_id as commodityId,
        sum(tol.commodity_num) as totalQuantity
        FROM t_order_list tol
        LEFT JOIN t_order o ON tol.order_id = o.id
        WHERE o.order_type = 9
        AND o.order_status = 0
        AND tol.gift_model_id = #{conditionId}
        <if test="null != idtoList and idtoList.size() > 0">
            AND
            <foreach collection="idtoList" index="index" item="item" open="(" separator="OR" close=")">
                (tol.commodity_id = #{item.commodityId} AND o.order_time BETWEEN #{item.startTime} AND #{item.endTime})
            </foreach>
        </if>
        GROUP BY tol.commodity_id
        HAVING tol.commodity_id IS NOT NULL
    </select>
    <select id="queryXdaCommodityLimit" resultType="com.pinshang.qingyun.order.dto.xda.XdaCommodityLimit4AppODTO">
        SELECT
        tol.commodity_id as commodityId,
        sum(tol.commodity_num) as totalQuantity
        FROM t_order_list tol
        LEFT JOIN t_order o ON tol.order_id = o.id
        WHERE o.order_type = 8
        AND o.order_status = 0
        <if test="null != idtoList and idtoList.size() > 0">
            AND
            <foreach collection="idtoList" index="index" item="item" open="(" separator="OR" close=")">
                (tol.commodity_id = #{item.commodityId} AND o.order_time BETWEEN #{item.startTime} AND #{item.endTime})
            </foreach>
        </if>
        GROUP BY tol.commodity_id
        HAVING tol.commodity_id IS NOT NULL
    </select>
    <select id="queryOrderItem4App" resultType="com.pinshang.qingyun.order.dto.xda.XdaOrderItem4AppODTO">
        SELECT
            tor.id as orderId,
            tol.commodity_id as commodityId,
            sum(tol.commodity_price) as commodityPrice,
            sum(tol.commodity_num) as commodityNum,
            sum(tol.real_quantity) as realDeliveryQuantity,
            tol.type
        FROM
            t_order tor
        INNER JOIN t_order_list tol ON tor.id = tol.order_id
        WHERE tor.id in
        <foreach collection="orderIds" index="index" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        group by tor.id, tol.commodity_id,tol.type
    </select>
    
    <select id="queryOrderItem4PfApp" resultType="com.pinshang.qingyun.order.dto.pf.PfOrderItem4AppODTO">
        SELECT
            tor.id as orderId,
            tol.commodity_id as commodityId,
            sum(tol.commodity_price) as commodityPrice,
            sum(tol.commodity_num) as commodityNum,
            sum(tol.real_quantity) as realDeliveryQuantity,
            tol.type
        FROM
            t_order tor
        INNER JOIN t_order_list tol ON tor.id = tol.order_id
        WHERE tor.id in
        <foreach collection="orderIds" index="index" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        group by tor.id, tol.commodity_id,tol.type
    </select>
    
    <select id="findAllByOrderCode" resultType="com.pinshang.qingyun.order.dto.AutoPreOrderGiftItemODTO">
        SELECT ol.commodity_id    as commodityId,
               ol.commodity_num   as stockQuantity,
               ol.commodity_price as price,
               ol.type            as type
        FROM t_order o
                 LEFT JOIN t_order_list ol ON o.id = ol.order_id
        where o.order_code = #{orderCode}
    </select>
    <select id="queryOrderItemXdaApp" resultType="com.pinshang.qingyun.order.dto.xda.v2.XdaOrderItemAppV2ODTO">
        SELECT
        tor.id as orderId,
        tol.commodity_id as commodityId,
        sum(tol.commodity_price) as commodityPrice,
        sum(tol.commodity_num) as commodityNum,
        sum(tol.real_quantity) as realDeliveryQuantity,
        tol.type
        FROM
        t_order tor
        INNER JOIN t_order_list tol ON tor.id = tol.order_id and tol.type in(1,2,5)
        WHERE tor.id in
        <foreach collection="orderIds" index="index" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        group by tor.id, tol.commodity_id,tol.type

    </select>
    <select id="queryOrderCommodityDetailById"
            resultType="com.pinshang.qingyun.order.dto.xda.v2.XdaOrderItemAppV2ODTO">
        SELECT
            tol.order_id AS orderId,
            tol.commodity_id AS commodityId,
            tol.type AS commodityType,
            tol.original_price AS originalPrice,
            tol.special_price AS specialPrice,
            tol.commodity_price AS commodityPrice,
            tol.special_price AS specialPrice,
            tol.commodity_num AS commodityNum,
            tol.commodity_num AS quantity,
            tol.real_quantity AS realDeliveryQuantity,
            tol.real_total_price AS realPrice,
            tol.coupon_id AS couponId,
            tol.coupon_user_id as couponUserId,
            tol.coupon_discount_amount AS couponDiscountAmount
        FROM
            t_order_list tol
        WHERE
            tol.order_id = #{orderId}
    </select>
    <select id="queryOrderItemXdaAppV3"
            resultType="com.pinshang.qingyun.order.dto.xda.v3.XdaOrderItemAppV3ODTO">
        SELECT
        tor.id as orderId,
        tol.commodity_id as commodityId,
        sum(tol.commodity_price) as commodityPrice,
        sum(tol.commodity_num) as commodityNum,
        sum(tol.real_quantity) as realDeliveryQuantity,
        tol.type
        FROM
        t_order tor
        INNER JOIN t_order_list tol ON tor.id = tol.order_id and tol.type in(1,2,5)
        WHERE tor.id in
        <foreach collection="orderIds" index="index" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        group by tor.id, tol.commodity_id,tol.type
    </select>
    <select id="queryOrderItemXdaAppV4"
            resultType="com.pinshang.qingyun.order.dto.xda.v4.XdaOrderItemAppV4ODTO">
        SELECT
        tor.id as orderId,
        tol.commodity_id as commodityId,
        sum(tol.commodity_price) as commodityPrice,
        sum(tol.commodity_num) as commodityNum,
        sum(tol.real_quantity) as realDeliveryQuantity,
        tol.type
        FROM
        t_order tor
        INNER JOIN t_order_list tol ON tor.id = tol.order_id and tol.type in(1,2,5) and tol.comb_type in(1,2)
        WHERE tor.id in
        <foreach collection="orderIds" index="index" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        group by tor.id, tol.commodity_id,tol.type
    </select>
    <select id="queryOrderCommodityDetailV3ById"
            resultType="com.pinshang.qingyun.order.dto.xda.v3.XdaOrderItemAppV3ODTO">
        SELECT
            tol.order_id AS orderId,
            tol.commodity_id AS commodityId,
            tol.type AS commodityType,
            tol.original_price AS originalPrice,
            tol.special_price AS specialPrice,
            tol.commodity_price AS commodityPrice,
            tol.special_price AS specialPrice,
            tol.commodity_num AS commodityNum,
            tol.commodity_num AS quantity,
            tol.real_quantity AS realDeliveryQuantity,
            tol.gift_model_id AS giftModelId,
            tol.total_price AS totalPrice,
            tol.real_total_price AS realPrice,
            tol.price_promotion_id pricePromotionId
        FROM
            t_order_list tol
        WHERE
            tol.order_id = #{orderId}
            and tol.comb_type in (1,2)
    </select>
    <select id="selectOrderListByOrderIdList" resultType="com.pinshang.qingyun.order.model.order.OrderList">
        SELECT *
        FROM
            t_order_list t
        WHERE
        t.order_id in
        <foreach collection="orderIdList" index="index" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>


    <update id="batchUpdateOrderList">
        <foreach collection="orderUpdateList" item="item" index="index" separator=";" open="" close="">
            UPDATE t_order_list
            SET
             real_quantity = #{item.realQuantity},
             real_total_price = #{item.realTotalPrice}
            WHERE
            id = #{item.id}
        </foreach>
    </update>

    <!-- 批量更新订单明细 -->
    <update id="updateBatchOrderList" parameterType="java.util.List">
        <foreach collection="orderUpdateList" item="item" separator=";">
            UPDATE t_order_list
            <set>
                <if test="item.commodityPrice != null">
                    commodity_price = #{item.commodityPrice},
                </if>
                <if test="item.totalPrice != null">
                    total_price = #{item.totalPrice},
                </if>
                <if test="item.realTotalPrice != null">
                    real_total_price = #{item.realTotalPrice},
                </if>
                update_time = NOW()
            </set>
            WHERE id = #{item.id}
        </foreach>
    </update>


    <select id="queryOrderListByOrderId" resultType="com.pinshang.qingyun.order.model.order.OrderList">
        select
            t.id,
            t.order_id,
            t.commodity_id,
            t.commodity_num,
            t.total_price,
            t.commodity_price,
            t.real_quantity,
            t.real_total_price,
            t.type,
            t.comb_type,
            t.comb_commodity_id,
            t.price_promotion_id
        from t_order_list t
        where t.order_id = #{orderId}
    </select>


    <select id="queryRealTotalPrice" resultType="java.math.BigDecimal">
        select
            sum(t.real_total_price)
        from t_order_list_gift t
        where t.order_id = #{orderId} and t.comb_type in (1,2)
    </select>


    <select id="selectOrderListGiftByOrderIdAndCommodityIdList" resultType="com.pinshang.qingyun.order.mapper.entry.order.OrderListInfoEntry">
        select
            t.id,
            t.order_id,
            t.commodity_id,
            t.commodity_num,
            t.total_price,
            t.commodity_price,
            t.real_quantity,
            t.real_total_price,
            t.type,
            t.comb_type,
            t.comb_commodity_id
        from
        <choose>
            <when test="isGift">
                t_order_list_gift t
            </when>
            <otherwise>
                t_order_list t
            </otherwise>
        </choose>
        WHERE t.order_id = #{vo.orderId}
        AND t.commodity_id IN
        <foreach collection="vo.commoditySet" item="commodityId" open="(" close=")" separator=",">
            #{commodityId}
        </foreach>
    </select>
    <select id="findByOrderIdAndProductType" resultType="com.pinshang.qingyun.order.model.order.OrderList">
        SELECT
            i.id,
            i.order_id,
            i.commodity_id,
            i.commodity_num,
            i.commodity_price,
            i.total_price,
            i.type,
            i.remark,
            i.comb_type,
            i.coupon_id,
            i.coupon_discount_amount,
            c.commodity_name,
            c.commodity_code,
            c.commodity_spec,
            t.order_status,
            t.order_time
        FROM
            t_order_list i LEFT JOIN t_commodity c ON i.commodity_id = c.id
                           left join t_order t on i.order_id=t.id
        where  i.order_id =#{orderId}
          <if test="productType != null">
              and i.`type` =#{productType}
          </if>
    </select>

    <select id="queryOrderCommodityDetailV3ByIds"
            resultType="com.pinshang.qingyun.order.dto.xda.v3.XdaOrderItemAppV3ODTO">
        SELECT
            tol.order_id AS orderId,
            tol.commodity_id AS commodityId,
            tol.type AS commodityType,
            tol.original_price AS originalPrice,
            tol.special_price AS specialPrice,
            tol.commodity_price AS commodityPrice,
            tol.special_price AS specialPrice,
            tol.commodity_num AS commodityNum,
            tol.commodity_num AS quantity,
            tol.real_quantity AS realDeliveryQuantity,
            tol.gift_model_id AS giftModelId,
            tol.total_price AS totalPrice,
            tol.real_total_price AS realPrice,
            tol.price_promotion_id pricePromotionId
        FROM
            t_order_list tol
        WHERE
            tol.order_id
            in
            <foreach collection="orderIdList" item="orderId" open="(" close=")" separator=",">
                #{orderId}
             </foreach>
          and tol.comb_type in (1,2)


    </select>

</mapper>