<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.pinshang.qingyun.order.mapper.SubOrder4DistributionMapper">

    <resultMap id="BaseResultMap" type="com.pinshang.qingyun.order.mapper.entry.shop.SubOrder4DistributionEntry">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="logistics_model" property="logisticsModel" />
        <result column="store_id" property="storeId" />
        <result column="shop_id" property="shopId" />
        <collection property="itemList" column="id"
                    ofType="com.pinshang.qingyun.order.mapper.entry.shop.SubOrderItem4DistributionEntry"
                    select="findItem">
            <result column="commodity_id" property="commodityId" />
            <result column="price" property="price" />
            <result column="quantity" property="quantity"/>
            <result column="requireQuantity" property="requireQuantity"/>
        </collection>
    </resultMap>


    <select id="find4DistributionOk" resultMap="BaseResultMap">
        SELECT sb.id ,sb.logistics_model, o.store_id, ms.id AS shop_id FROM t_sub_order sb
        INNER JOIN t_order o ON o.id = sb.order_id
        INNER JOIN t_md_shop ms ON ms.store_id = o.store_id
        WHERE sb.id IN
        <foreach collection="subOrderIds" index="index" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <select id="findItem" resultType="com.pinshang.qingyun.order.mapper.entry.shop.SubOrderItem4DistributionEntry">
        SELECT soi.price ,soi.commodity_id , soi.real_delivery_quantity AS quantity,soi.quantity AS requireQuantity
        FROM t_sub_order_item soi
        WHERE soi.sub_order_id = #{subOrderId}
    </select>
</mapper>