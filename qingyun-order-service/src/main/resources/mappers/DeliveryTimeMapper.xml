<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.pinshang.qingyun.order.mapper.DeliveryTimeMapper">

	<select id="queryDeliveryTimeList" resultType="com.pinshang.qingyun.order.mapper.entry.DeliveryTimeEntry">
		SELECT
			dt.id,
			dt.begin_time AS beginTime,
			dt.end_time AS endTime
		FROM
			t_delivery_time dt
		GROUP BY dt.end_time
		ORDER BY dt.end_time
	</select>

	<select id="getDeliveryTimeByStoreId" resultType="com.pinshang.qingyun.order.model.order.DeliveryTime">
		SELECT
		dt.id,
		dt.begin_time,
		dt.end_time,
		dt.line_group_id,
		dt.update_time,
		dt.create_time,
		dt.create_user_id,
		dt.`status`,
		dt.cover_time
		FROM t_delivery_time dt
		LEFT JOIN t_distribution_line dl ON dl.delivery_time_id = dt.line_group_id
		LEFT JOIN t_store s ON s.store_line_id = dl.id
		WHERE s.id = #{storeId}
	</select>

    <select id="selectDeliveryTimeEndTimeByLineIds" resultType="java.lang.String">
        SELECT
        <![CDATA[
           DISTINCT d.end_time
            ]]>
        FROM
        t_delivery_time d
        INNER JOIN t_distribution_line line ON line.delivery_time_id = d.line_group_id
        WHERE line.id in
        <foreach collection="lineIdList" item="lineId" separator="," open="(" close=")">
            #{lineId}
        </foreach>
        ORDER BY LENGTH(d.end_time)DESC, d.end_time DESC
    </select>
</mapper>