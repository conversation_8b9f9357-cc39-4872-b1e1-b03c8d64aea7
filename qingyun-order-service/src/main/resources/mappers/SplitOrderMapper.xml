<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.pinshang.qingyun.order.mapper.SplitOrderMapper">

	<sql id="findNonSplitOrderWhere">
		<where>
			<if test="orderId!=null">
				AND o.id = #{orderId}
			</if>
			<if test="orderTime!=null">
				AND o.order_time =#{orderTime}
			</if>
			<if test="orderCode!=null and orderCode!=''">
				AND o.order_code =#{orderCode}
			</if>
			<if test="storeCode!=null and storeCode!=''">
				AND s.store_code =#{storeCode}
			</if>
			<if test=" orderStatus != null ">
				AND o.order_status = #{orderStatus}
			</if>
			AND so.order_id IS NULL
			AND (o.business_type is null OR o.business_type != 10)
		</where>
	</sql>
	<select id="findNonSplitOrderList" parameterType="com.pinshang.qingyun.order.vo.orderMonitor.NonSplitOrderQueryVo" resultType="com.pinshang.qingyun.order.vo.orderMonitor.NonSplitOrderVo">
		SELECT o.id as orderId,o.order_code,s.store_code,s.store_name ,o.order_status,o.create_time,o.update_time
		FROM t_order o
		LEFT JOIN t_store s ON o.store_id = s.id
		LEFT JOIN t_sub_order so ON o.id = so.order_id
		<include refid="findNonSplitOrderWhere"></include>
	</select>

	<select id="findNonSplitByOrderId" resultType="com.pinshang.qingyun.order.vo.orderMonitor.NonSplitCommodityDetail">
		select g.order_id, c.id as commodityId,c.commodity_code,c.commodity_name,c.logistics_model
		from t_order_list_gift g
		left join t_commodity c on g.commodity_id = c.id
		where g.order_id = #{orderId};
	</select>

	<select id="findNonSplitOrder" parameterType="com.pinshang.qingyun.order.vo.orderMonitor.NonSplitOrderQueryVo" resultType="java.lang.Long">
		SELECT o.id as orderId FROM t_order o
		LEFT JOIN t_store s ON o.store_id = s.id
		LEFT JOIN t_sub_order so ON o.id = so.order_id
		<include refid="findNonSplitOrderWhere"></include>
	</select>

	<select id="querySplitOrderDetail" resultType="com.pinshang.qingyun.order.mapper.entry.splitOrder.SplitOrderListEntry">
		SELECT
		    ol.id orderListId,
			ol.commodity_id,
			c.logistics_model,
			ol.commodity_num AS quantity,
			ol.commodity_price AS price,
			ol.total_price,
			ol.original_price,
			ol.type,
			ol.comb_type,
			ol.comb_commodity_id,
			ol.price_promotion_id
		FROM
			t_order_list ol
			INNER JOIN t_commodity c ON c.id = ol.commodity_id
		WHERE
			ol.order_id = #{orderId}
			AND c.logistics_model IN (0, 1, 2)
	</select>


	<select id="queryGiftOrderRationDetail" resultType="com.pinshang.qingyun.order.mapper.entry.splitOrder.SplitOrderListEntry">
		SELECT
			ol.commodity_id,
			c.logistics_model,
			ol.commodity_num AS quantity,
			ol.commodity_price AS price,
			ol.total_price,
			ol.original_price,
			ol.type,
			ol.comb_type,
			ol.comb_commodity_id,
			ol.price_promotion_id
		FROM
			t_order_list_gift ol
				INNER JOIN t_commodity c ON c.id = ol.commodity_id
		WHERE
			ol.order_id = #{orderId}
		  AND c.logistics_model IN (0, 1, 2)
		  and ol.type = 3
	</select>

	<select id="queryGiftOrderProductDetail" resultType="com.pinshang.qingyun.order.mapper.entry.splitOrder.SplitOrderListEntry">
		SELECT
			ol.commodity_id,
			c.logistics_model,
			ol.commodity_num AS quantity,
			ol.commodity_price AS price,
			ol.total_price,
			ol.original_price,
			ol.type,
			ol.comb_type,
			ol.comb_commodity_id,
			ol.price_promotion_id
		FROM
			t_order_list_gift ol
				INNER JOIN t_commodity c ON c.id = ol.commodity_id
		WHERE
			ol.order_id = #{orderId}
		  AND c.logistics_model IN (0, 1, 2)
		  and ol.type = 1
	</select>


	<select id="findUnSplitOrderList" resultType="java.lang.String">
		select
			distinct t1.orderId
		from (
				 select t.id orderId,l.commodity_id
				 from t_order t
				  left join t_order_list l on l.order_id = t.id
				where  t.order_status != 2
				and l.comb_type = 1
				and t.order_type in(1,2,8)
				and l.comb_type != 2
				and t.create_time BETWEEN #{startTime} and #{endTime}

		) t1 where not exists (
			select
				1
			from t_order t
			 left join t_sub_order so on so.order_id = t.id
			 left join t_sub_order_item i on i.sub_order_id = so.id
			WHERE t.id = t1.orderId
			  AND i.commodity_id = t1.commodity_id
			  AND t.order_type IN (1,2,8)
			  and t.order_status != 2
   			 AND t.create_time BETWEEN #{startTime} and #{endTime}
		)
	</select>

</mapper>