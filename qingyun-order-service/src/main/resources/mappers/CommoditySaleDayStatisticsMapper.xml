<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.pinshang.qingyun.order.mapper.CommoditySaleDayStatisticsMapper" >

  <update id="batchUpdate">
    <foreach collection="dayStatisticsList" item="item" index="index" separator=";" open="" close="">
      UPDATE t_xda_commodity_sale_day_statistics
      SET
          total_quantity = IFNULL(total_quantity,0) +  IFNULL(#{item.totalQuantity},0),
          total_amount = IFNULL(total_amount,0) +  IFNULL(#{item.totalAmount},0)
      WHERE
        commodity_id = #{item.commodityId}
      AND  order_time = #{item.orderTime}
    </foreach>
  </update>

    <select id="queryDayStatisticsList" resultType="com.pinshang.qingyun.order.model.order.XdaCommoditySaleDayStatistics">
        SELECT
            ss.commodity_id AS commodityId,
            SUM(ss.total_quantity) total_quantity
        FROM
            t_xda_commodity_sale_day_statistics ss
        WHERE
            ss.order_time >= DATE_FORMAT(date_add(now(), interval -7 day),'%Y-%m-%d') AND
            ss.order_time <![CDATA[ < ]]> DATE_FORMAT(now(),'%Y-%m-%d')
        GROUP BY  ss.commodity_id
    </select>

    <select id="queryDayCommodityQuantity" resultType="com.pinshang.qingyun.order.model.order.XdaCommoditySaleDayStatistics">
        SELECT
            ss.commodity_id AS commodityId,
            SUM(ss.total_quantity) total_quantity
        FROM
            t_xda_commodity_sale_day_statistics ss
        WHERE
            ss.order_time = #{date}
          and  ss.commodity_id IN
            <foreach collection="ids" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        GROUP BY  ss.commodity_id
    </select>



    <delete id="deleteSaleDayStatistics">
        DELETE FROM t_xda_commodity_sale_day_statistics WHERE order_time = #{orderTime}
    </delete>

    <insert id="insertSaleDayStatistics">

        Insert into t_xda_commodity_sale_day_statistics(commodity_id,total_quantity,total_amount,order_time)

        SELECT
            ol.commodity_id,
            sum(ol.commodity_num),
            sum(ol.total_price),
            t.order_time
        FROM t_order t
                 LEFT JOIN t_order_list ol on ol.order_id = t.id
        where t.order_status = 0 and t.order_type = 8
          and t.order_time = #{orderTime}
          and ol.type in(1,5)
        GROUP BY ol.commodity_id

    </insert>

</mapper>