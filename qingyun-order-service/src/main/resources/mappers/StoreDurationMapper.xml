<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pinshang.qingyun.order.mapper.StoreDurationMapper">
    <select id ="findLineDurationByStoreCode" resultType="com.pinshang.qingyun.order.model.store.StoreDuration">
        SELECT s.id as storeId ,s.store_code as storeCode ,dt.begin_time as beginTime , dt.end_time as endTime
              ,dt.`line_group_id` AS lineGroupId
        FROM t_store s ,t_distribution_line dl,t_dictionary d,t_delivery_time dt
        WHERE s.store_line_id = dl.id AND dl.delivery_time_id = d.id AND d.`dictionary_id` =9131078242860601278
             AND  dt.line_group_id =dl.delivery_time_id AND s.`store_code` =#{storeCode}
    </select>
</mapper>