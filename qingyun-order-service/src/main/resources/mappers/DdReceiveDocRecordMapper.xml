<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pinshang.qingyun.order.mapper.bigShop.DdReceiveDocRecordMapper">

	<resultMap id="BaseResultMap" type="com.pinshang.qingyun.order.model.bigShop.DdReceiveDocRecord">
		<id column="id" property="id" />
		<result column="doc_id" property="docId" />
		<result column="commodity_id" property="commodityId" />
		<result column="storage_area" property="storageArea" />
		<result column="goods_allocation_id" property="goodsAllocationId" />
		<result column="real_receive_quantity" property="realReceiveQuantity" />
		<result column="remark" property="remark" />
		<result column="create_id" property="createId" />
		<result column="create_time" property="createTime" />
	</resultMap>

    <!-- 通用查询结果列-->
    <sql id="Base_Column_List">
        id, doc_id AS docId, commodity_id AS commodityId, storage_area AS storageArea, goods_allocation_id AS goodsAllocationId, real_receive_quantity AS realReceiveQuantity, remark, create_id AS createId, create_time AS createTime
    </sql>



</mapper>
