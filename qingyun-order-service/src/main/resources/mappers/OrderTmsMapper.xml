<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.pinshang.qingyun.order.mapper.tms.OrderTmsMapper" >

	<sql id="RefOrder_Order">
		11 AS refType,
		o.id AS refOrderId,
		o.order_code AS refOrderCode,
		o.order_amount AS refOrderAmount,
		o.order_status AS refOrderStatus,
		o.direct_status AS refDirectStatus,
		o.delivery_time_range,
		o.business_type,
		o.logistics_center_id,
		o.delivery_batch,
		o.order_time AS deliveryDate,
		o.stall_id,
		o.store_id,
		s.store_code,
		s.store_name,
		s.store_linkman,
		s.linkman_mobile AS storePhone,
		s.delivery_address AS storeAddress
	</sql>
	<sql id="RefOrder_ReturnOrder">
		21 AS refType,
		o.id AS refOrderId,
		o.return_order_seq AS refOrderCode,
		o.total_check_money AS refOrderAmount,
		o.status AS refOrderStatus,
		NULL AS refDirectStatus,
		o.pick_up_time_range AS delivery_time_range,
		o.business_type,
		o.logistics_center_id,
		o.delivery_batch,
		o.delivery_time AS deliveryDate,
		NULL AS stallId,
		o.store_id,
		s.store_code,
		s.store_name,
		s.store_linkman,
		s.linkman_mobile AS storePhone,
		s.delivery_address AS storeAddress
	</sql>

	<!-- 查询【源单-订单】 -->
    <select id="selectOrder" 
    	resultType="com.pinshang.qingyun.order.dto.tms.RefOrderForWaybillODTO">
    	SELECT <include refid="RefOrder_Order" />
		FROM t_order o
		LEFT JOIN t_store s ON s.id = o.store_id
		WHERE o.id = #{refOrderId}
    </select>
    <!-- 查询【源单明细-订单】列表 -->
    <select id="selectOrderItemList" 
    	resultType="com.pinshang.qingyun.order.dto.tms.RefOrderItemForWaybillODTO">
        SELECT
			i.order_id AS refOrderId,
			
			i.commodity_id,
			c.is_weight,
			c.commodity_package_spec,
			
			i.commodity_num AS quantity,
			NULL AS number, <!-- 代码中计算 -->
			i.total_price AS amount
		FROM t_order_list_gift i
		LEFT JOIN t_commodity c ON c.id = i.commodity_id
		WHERE i.order_id = #{refOrderId}
		AND i.comb_type IN (1,3)
    </select>
    
    
    <!-- 查询【源单-退货单】 -->
    <select id="selectReturnOrder" 
    	resultType="com.pinshang.qingyun.order.dto.tms.RefOrderForWaybillODTO">
     	SELECT <include refid="RefOrder_ReturnOrder" />	
		FROM t_xda_return_order o
		LEFT JOIN t_store s ON s.id = o.store_id
		WHERE o.id = #{refOrderId}
		AND o.return_type = 1	<!-- 客户退货  -->
		AND o.return_order_type = 1 <!-- 1-退货 -->
    </select>
    <!-- 查询【源单明细-退货单】列表 -->
    <select id="selectReturnOrderItemList" 
		resultType="com.pinshang.qingyun.order.dto.tms.RefOrderItemForWaybillODTO">
    	SELECT
       		i.return_order_id AS refOrderId,

			i.commodity_id,
			c.is_weight,
			c.commodity_package_spec,

			i.check_quantity AS quantity,
			i.check_number AS number,
			i.check_money AS amount
		FROM t_xda_return_order_item i
		LEFT JOIN t_commodity c ON c.id = i.commodity_id
		WHERE i.return_order_id = #{refOrderId}
    </select>
    
    
    <!-- 查询【大仓确认信息】列表 -->
	<select id="selectWarehouseConfirmInfoList" 
		resultType="com.pinshang.qingyun.order.dto.tms.WarehouseConfirmInfoODTO">
		SELECT
			i.commodity_id,
			c.is_weight,

			i.confirm_quantity,
			i.confirm_number,
			i.confirm_money AS confirmAmount
		FROM t_xda_return_order_item i
		LEFT JOIN t_commodity c ON c.id = i.commodity_id
		WHERE i.return_order_id = #{refOrderId}
    </select>
	
</mapper>