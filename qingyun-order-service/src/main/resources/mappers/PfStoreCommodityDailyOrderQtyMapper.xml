<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.pinshang.qingyun.order.mapper.PfStoreCommodityDailyOrderQtyMapper">

	<insert id="insertOrUpdate">
		insert into t_pf_store_commodity_daily_order_qty(store_id,commodity_id,order_date,order_quantity)
		values
		<foreach collection="list" item="item" separator=",">
			(#{item.storeId},#{item.commodityId},#{item.orderDate},#{item.orderQuantity})
		</foreach>
		on duplicate key
		update order_quantity=order_quantity+values(order_quantity)
	</insert>

</mapper>