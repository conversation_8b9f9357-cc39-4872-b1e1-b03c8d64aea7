<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.pinshang.qingyun.order.mapper.SaleReturnMapper">

	<select id="getSaleReturnListByCondition" resultType="com.pinshang.qingyun.order.mapper.entry.order.SaleReturnEntry">
		SELECT
		ro.id,
		ro.logistics_model,
		ro.order_code,
		ro.supplier_id,
		ro.status,
		ro.return_amount,
		-- u.employee_name as createName,
		ro.create_time,
		-- m.shop_name,
        count(ro.order_code) AS categoryNum,
		ro.create_id,ro.store_id,ro.stall_id
		FROM
		t_sale_return_order ro
        LEFT JOIN t_sale_return_order_item roi ON ro.id = roi.sale_return_order_id
		-- INNER JOIN t_md_shop m on m.store_id = ro.store_id
		-- INNER JOIN t_employee_user u on ro.create_id = u.user_id
		WHERE 1=1
		AND ro.store_id = #{saleReturnVo.storeId}
		<if test="saleReturnVo.orderCode !=null and saleReturnVo.orderCode !='' ">
			and ro.order_code = #{saleReturnVo.orderCode}
		</if>
		<if test="saleReturnVo.status !=null ">
			AND   ro.status in
			<foreach collection="saleReturnVo.status" item = "item" open="(" separator="," close=")">
				#{item}
			</foreach>
		</if>
		<if test="saleReturnVo.enterpriseId !=null and saleReturnVo.enterpriseId !='' ">
			and ro.enterprise_id = #{saleReturnVo.enterpriseId}
		</if>
		<if test="saleReturnVo.beginDate !=null and saleReturnVo.beginDate != '' and saleReturnVo.endDate != null and saleReturnVo.endDate != '' ">
			and ro.create_time BETWEEN #{saleReturnVo.beginDate} and #{saleReturnVo.endDate}
		</if>
		<if test="saleReturnVo.ifReturnShort == true">
			AND roi.return_reason = #{saleReturnVo.returnReason}
		</if>
		<if test="saleReturnVo.ifReturnShort == false ">
			AND roi.return_reason != #{saleReturnVo.returnReason}
		</if>
		<if test = "null != saleReturnVo.consignmentId">
		    AND ro.consignment_id = #{saleReturnVo.consignmentId}
		</if>
		<if test="saleReturnVo.stallId != null">
			and ro.stall_id = #{saleReturnVo.stallId}
		</if>
		<if test="saleReturnVo.stallIdList != null and saleReturnVo.stallIdList.size > 0">
			AND ro.stall_id IN
			<foreach collection="saleReturnVo.stallIdList" index="index" item="stallid" open="(" separator="," close=")">
				#{stallid}
			</foreach>
		</if>
        GROUP BY ro.order_code
		ORDER BY id DESC
	</select>

	<select id="getSaleReturnByCode" parameterType="java.lang.String" resultType="com.pinshang.qingyun.order.mapper.entry.order.SaleReturnEntry">
		SELECT
		ro.id,
		ro.logistics_model,
		ro.order_code,
		ro.supplier_id,
		ro.status,
		ro.return_amount,
		u.employee_name as createName,
		ro.create_time,
		m.shop_name,
	    m.id shopId
		FROM
		t_sale_return_order ro
		INNER JOIN t_md_shop m on m.store_id = ro.store_id
		INNER JOIN t_employee_user u on ro.create_id = u.user_id
		WHERE 1=1
		and ro.order_code = #{orderCode}
	</select>


	<select id="getSaleReturnSendList" resultType="com.pinshang.qingyun.order.mapper.entry.order.SaleReturnEntry">
		SELECT
		ro.id,
		ro.logistics_model,
		ro.order_code,
		ro.supplier_id,
		ro.status,
		ro.return_amount,
		u.employee_name as createName,
		ro.create_time,
		m.shop_name,
		uu.employee_name as updateName,
		ro.update_time
		FROM
		t_sale_return_order ro
		INNER JOIN t_md_shop m on m.store_id = ro.store_id
		INNER JOIN t_employee_user u on ro.create_id = u.user_id
		LEFT JOIN t_employee_user uu on ro.update_id = uu.user_id
		WHERE 1=1
		and ro.logistics_model = 0
		and ro.supplier_id is not null
		<if test="saleReturnVo.shopId !=null and saleReturnVo.shopId !='' ">
			and m.id = #{saleReturnVo.shopId}
		</if>
		<if test="saleReturnVo.supplierId !=null and saleReturnVo.supplierId !='' ">
			and ro.supplier_id = #{saleReturnVo.supplierId}
		</if>
		<if test="saleReturnVo.orderCode !=null and saleReturnVo.orderCode !='' ">
			and ro.order_code = #{saleReturnVo.orderCode}
		</if>
		<if test="saleReturnVo.status !=null ">
			AND   ro.status in
			<foreach collection="saleReturnVo.status" item = "item" open="(" separator="," close=")">
				#{item}
			</foreach>
		</if>
		<if test="saleReturnVo.enterpriseId !=null and saleReturnVo.enterpriseId !='' ">
			and ro.enterprise_id = #{saleReturnVo.enterpriseId}
		</if>
		<if test="saleReturnVo.beginDate !=null and saleReturnVo.endDate != '' and saleReturnVo.beginDate != null and saleReturnVo.endDate != '' ">
			and ro.create_time BETWEEN #{saleReturnVo.beginDate} and #{saleReturnVo.endDate}
		</if>
		ORDER BY id DESC
	</select>


	<select id="xdReturnList" resultType="com.pinshang.qingyun.order.dto.XdSaleReturnODTO">
		SELECT
			so.id,
			so.order_code,
			md.shop_code,
			md.shop_name,
			soi.commodity_id,
			-- c.commodity_code,
			-- c.commodity_name,
			-- c.commodity_spec,
		   (CASE  when so.status = 2 then  soi.real_return_quantity  when so.status != 2 then soi.return_quantity end) as realReturnQuantity,
			soi.price,
		   (CASE  when so.status = 2 then  soi.real_return_quantity  when so.status != 2 then soi.return_quantity end)*soi.price totalPrice,
			soi.return_reason,
			so.create_time,
			so.status
		FROM t_sale_return_order so
		LEFT JOIN t_sale_return_order_item soi on soi.sale_return_order_id = so.id
		INNER JOIN t_md_shop md ON md.store_id = so.store_id and md.shop_type = 5
		-- LEFT JOIN t_commodity c on c.id = soi.commodity_id
		where  1=1
		<if test="xdSaleReturnIDTO.shopId !=null and xdSaleReturnIDTO.shopId != 0 ">
			and md.id = #{xdSaleReturnIDTO.shopId}
		</if>
		<if test="xdSaleReturnIDTO.beginDate !=null and xdSaleReturnIDTO.beginDate != '' and xdSaleReturnIDTO.endDate != null and xdSaleReturnIDTO.endDate != '' ">
			and so.create_time BETWEEN #{xdSaleReturnIDTO.beginDate} and #{xdSaleReturnIDTO.endDate}
		</if>
		<if test="xdSaleReturnIDTO.status !=null  ">
			and so.status = #{xdSaleReturnIDTO.status}
		</if>
		<if test="xdSaleReturnIDTO.orderCode !=null and xdSaleReturnIDTO.orderCode !='' ">
			and so.order_code = #{xdSaleReturnIDTO.orderCode}
		</if>
		<!--<if test="xdSaleReturnIDTO.commodityKey !=null and xdSaleReturnIDTO.commodityKey !='' ">
			and (c.`commodity_code` like concat('%',#{xdSaleReturnIDTO.commodityKey},'%') or c.`commodity_name` like concat('%',#{xdSaleReturnIDTO.commodityKey},'%') or c.`commodity_aid` like concat('%',#{xdSaleReturnIDTO.commodityKey},'%'))
		</if>-->
		<if test="xdSaleReturnIDTO.commodityIdList != null and xdSaleReturnIDTO.commodityIdList.size() > 0">
			AND soi.commodity_id in <foreach collection="xdSaleReturnIDTO.commodityIdList" item="id" open="(" separator="," close=")"> #{id} </foreach>
		</if>
		order by so.order_code desc
	</select>

	<select id="getSaleReturnAuditByCode" parameterType="java.lang.String" resultType="com.pinshang.qingyun.order.mapper.entry.order.SaleReturnForAuditEntry">
		SELECT
		ro.id,
		ro.logistics_model,
		ro.order_code,
		ro.supplier_id,
		ro.status,
		ro.return_amount,
		u.employee_name AS createName,
		ro.create_time,
		m.shop_name,
		m.id shopId,
		ro.warehouse_id,
		ro.`enterprise_id`,
		ro.`store_id`,
		s.store_name AS storeName
		FROM
		t_sale_return_order ro
		INNER JOIN t_md_shop m ON m.store_id = ro.store_id
		INNER JOIN t_employee_user u ON ro.create_id = u.user_id
		LEFT JOIN t_store s ON s.`id` = ro.`store_id`
		WHERE 1=1
		and ro.order_code = #{orderCode}
	</select>
</mapper>