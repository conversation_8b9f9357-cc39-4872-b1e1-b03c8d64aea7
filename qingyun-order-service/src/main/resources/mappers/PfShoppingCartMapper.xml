<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.pinshang.qingyun.order.mapper.PfShoppingCartMapper">

    <delete id="deleteShopCart">
        delete from t_pf_shopping_cart
        where store_id =#{storeId} and commodity_id in
        <foreach collection="idList" index="index" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>