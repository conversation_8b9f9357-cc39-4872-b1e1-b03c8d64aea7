<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.pinshang.qingyun.order.mapper.PromotionSaleMapper" >


    <select id="findRatioByStoreId" resultType="com.pinshang.qingyun.order.model.promotionSale.PromotionSale">
        select
            distinct ps.*
        from
            `t_promotion_sale` ps,
            `t_promotion_sale_condition` psc ,
            `t_store_settlement` ss ,
            `t_product_price_model` ppm ,
            `t_store` s
        where 1=1
          and ps.`id` = psc.`promotion_id`
          and ss.`product_price_model_id` = ppm.`id`
          and ss.`store_id` = s.`id`
          and ps.`status` = 0
          and #{orderTime} between ps.`start_time` and ps.`end_time`
          and (psc.`source_id` = ss.`settlement_customer_id` or psc.`source_id` = ppm.`id` or psc.`source_id` = s.`id`)
          and s.`id` = #{storeId}
        order by ps.`create_time` desc
    </select>
</mapper>