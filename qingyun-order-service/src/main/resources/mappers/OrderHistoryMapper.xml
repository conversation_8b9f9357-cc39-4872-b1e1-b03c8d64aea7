<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.pinshang.qingyun.order.mapper.OrderHistoryMapper">

	<select id="getOrderLogList" resultType="com.pinshang.qingyun.order.mapper.entry.order.OrderLogListEntry">
		SELECT
			DATE_FORMAT(create_time, '%Y-%m-%d  %T') AS create_time,
			CONCAT(IFNULL(eu.employee_code, ''),' ',create_name) createName,
			CASE operation_type
				WHEN 11 THEN '订单_新建'
				WHEN 12 THEN '订单_取消'
				WHEN 13 THEN '修改送货日期'
				WHEN 21 THEN '商品_添加'
				WHEN 22 THEN '商品_修改'
				WHEN 23 THEN '商品_删除'
				WHEN 31 THEN '修改送货日期'
				WHEN 32 THEN '直送订单审核'
				WHEN 33 THEN '直送补单'
				END operation_type,
			CASE entity_type
				WHEN 1 THEN '订单'
				WHEN 2 THEN '商品'
				END entity_type,
			code,
			name,
			new_value,
			old_value,
			add_type
		FROM t_order_history oh
		left JOIN t_employee_user eu on eu.user_id = oh.create_id
		WHERE order_id = #{orderId}
		ORDER BY oh.create_time asc
	</select>

</mapper>