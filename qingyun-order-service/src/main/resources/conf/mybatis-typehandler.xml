<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE configuration
        PUBLIC "-//mybatis.org//DTD Config 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-config.dtd">

<configuration>
    <properties>
        <property name="dialect" value="mysql"/>
    </properties>
    <settings>
        <setting name="useGeneratedKeys" value="true" />
        <setting name="mapUnderscoreToCamelCase" value="true" />
        <setting name="logImpl" value="STDOUT_LOGGING"/>
    </settings>

    <typeHandlers>
        <typeHandler handler="com.pinshang.qingyun.order.enums.EnumHandler" javaType="com.pinshang.qingyun.order.enums.OrderPrintTypeEnum"/>
        <typeHandler handler="com.pinshang.qingyun.order.enums.EnumHandler" javaType="com.pinshang.qingyun.order.enums.StoreOpenStatusEnum"/>
        <typeHandler handler="com.pinshang.qingyun.order.enums.EnumHandler" javaType="com.pinshang.qingyun.order.enums.AppUserStatusEnum"/>
        <typeHandler handler="com.pinshang.qingyun.order.enums.EnumHandler" javaType="com.pinshang.qingyun.order.enums.AppUserModifyEnum"/>
        <typeHandler handler="com.pinshang.qingyun.order.enums.EnumHandler" javaType="com.pinshang.qingyun.order.enums.ComplaintTypeEnum"/>
        <typeHandler handler="org.apache.ibatis.type.InstantTypeHandler" />
        <typeHandler handler="org.apache.ibatis.type.LocalDateTimeTypeHandler" />
        <typeHandler handler="org.apache.ibatis.type.LocalDateTypeHandler" />
        <typeHandler handler="org.apache.ibatis.type.LocalTimeTypeHandler" />
        <typeHandler handler="org.apache.ibatis.type.OffsetDateTimeTypeHandler" />
        <typeHandler handler="org.apache.ibatis.type.OffsetTimeTypeHandler" />
        <typeHandler handler="org.apache.ibatis.type.ZonedDateTimeTypeHandler" />
    </typeHandlers>

</configuration>