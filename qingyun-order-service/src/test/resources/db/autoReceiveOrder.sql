CREATE TABLE `t_order` (
`id` bigint(32) NOT NULL AUTO_INCREMENT COMMENT '主键id',
`enterprise_id` bigint(32) DEFAULT NULL COMMENT '企业id',
`company_id` bigint(20) DEFAULT NULL COMMENT '所属公司ID',
`order_code` varchar(20) DEFAULT NULL COMMENT '订单号',
`store_id` bigint(32) DEFAULT NULL COMMENT '商铺id',
`real_order_time` date DEFAULT NULL COMMENT '实发日期',
`order_time` date DEFAULT NULL COMMENT '订单时间',
`mode_type` int(11) DEFAULT '0' COMMENT '订单类型：（0：普通订单，1：补货单）',
`order_type` int(32) DEFAULT NULL COMMENT '订单类型(1-PC下单,2-APP下单, 8-鲜达APP 9-批发app 10=门店订货下单 12-清美订单 13-mini团购订单 14-玖琅自动下单 15-礼擎自动下单 16-礼擎全国版 21-团购订单 22-门店订货PC版 23-门店订货PDA版 24-门店订货代理补货 25-大仓配货自动下单 26-门店自动订货 27-代理订货 28-直送补单 29-直送审核通过 30-提货卡自动下单 31-门店加货申请单 32-日日鲜商品自动下单 33-提货卡预约订单 34-云超团购自动下单 40-小鹅通推送订单 41-计划销售订单)',
`print_num` int(11) DEFAULT NULL COMMENT '打印份数',
`print_type` int(11) DEFAULT NULL COMMENT '打印类型(1：本地,2：送货员,3：不打印)',
`total_amount` decimal(15, 2) DEFAULT '0.00' COMMENT '应付金额（参与促销活动之前的源始金额）',
`real_total_price` decimal(15, 2) DEFAULT '0.00' COMMENT '实发总金额',
`final_amount` decimal(15, 2) DEFAULT '0.00' COMMENT '最终金额',
`order_amount` decimal(19, 2) DEFAULT NULL COMMENT '订单金额',
`freight_amount` decimal(6, 2) DEFAULT '0.00' COMMENT '订单运费金额（免运费为0）',
`promotion_amount` decimal(15, 2) DEFAULT '0.00' COMMENT '优惠金额合计',
`order_remark` varchar(1000) DEFAULT NULL COMMENT '备注',
`create_time` datetime DEFAULT NULL COMMENT '创建日期',
`update_id` bigint(32) DEFAULT NULL COMMENT '更新者',
`update_time` datetime DEFAULT NULL COMMENT '更新时间',
`order_status` int(2) DEFAULT NULL COMMENT '订单状态(0正常,1删除,2取消)',
`process_status` int(2) UNSIGNED DEFAULT NULL COMMENT '流程状态(鲜达新加) 0=已取消 1=待拣货 7=待发货 11=出库中 12=待揽收 13=待配送　15=配送中　19=配送完成　20=配送失败',
`order_duration_time` datetime DEFAULT NULL COMMENT '订单截止时间(鲜达新加),根据当前客户截止时间与订单中的最晚商品截止时间得来;订单取消与是否进大仓时使用',
`create_id` bigint(32) DEFAULT NULL COMMENT '创建者',
`sync_status` int(1) NOT NULL DEFAULT '0' COMMENT '同步标识位，0为未同步，1为已同步',
`settle_status` int(11) DEFAULT '0' COMMENT ' 0:正常   1：未结算',
`delivery_batch` varchar(2) DEFAULT '0' COMMENT '0-无需批次配送, 1-1配，2-2配，3-3配，9-临时批次',
`delivery_batch_remark` varchar(30) DEFAULT '0_',
`change_price_status` tinyint(2) DEFAULT '0' COMMENT '是否可变价 :1是 0否',
`cacel_reason_id` bigint(20) DEFAULT '0' COMMENT '取消原因(字典ID)',
`consignment_id` bigint(20) DEFAULT '-1' COMMENT '代销商ID',
`real_sub_order_done_status` tinyint(1) DEFAULT NULL COMMENT '子订单是否出库完标识：只有1代表所有子单(不包括直送)已出库',
`presale_status` tinyint(4) UNSIGNED DEFAULT '1' COMMENT '是否预售  0 否   1 是',
`delivery_time_range` varchar(100) DEFAULT NULL COMMENT '送货时间段',
`logistics_center_id` bigint(20) DEFAULT NULL COMMENT '物流中心id',
`logistics_center` varchar(100) DEFAULT NULL COMMENT '物流中心',
`business_type` tinyint(4) DEFAULT NULL COMMENT '业务类型：10-通达销售 13=大店销售',
`settle_order_time` date DEFAULT NULL COMMENT '结算日期-结算单这边使用，出库完成修改为最终结算日期',
`store_type_id` bigint(20) DEFAULT NULL COMMENT '客户类型Id',
`driver_id` bigint(20) DEFAULT NULL COMMENT '配送司机',
`stall_id` bigint(20) DEFAULT '-1' COMMENT '档口id',
`direct_status` tinyint(4) NOT NULL DEFAULT '0' COMMENT '是否直送 0=否  1=是',
`logistics_carrier_code` varchar(20) DEFAULT NULL COMMENT '物流承运商code'
) ENGINE = InnoDB;


CREATE TABLE `t_order_list` (
`id` bigint(32) NOT NULL AUTO_INCREMENT,
`order_id` bigint(32) DEFAULT NULL COMMENT '订单id',
`commodity_id` bigint(32) DEFAULT NULL COMMENT '商品id',
`commodity_num` decimal(20, 3) DEFAULT NULL COMMENT '商品数量',
`real_quantity` decimal(14, 2) DEFAULT '0.00' COMMENT '实发数量',
`original_price` decimal(14, 2) DEFAULT NULL COMMENT '原价',
`special_price` decimal(14, 2) DEFAULT NULL COMMENT '特价',
`original_total_price` decimal(15, 2) DEFAULT NULL COMMENT '原金额(原价*数量)',
`commodity_price` decimal(19, 2) DEFAULT NULL COMMENT '促销后单价(原含义-单价)',
`total_price` decimal(15, 2) DEFAULT '0.00' COMMENT '促销后的商品金额(原含义-商品销售总金额)',
`real_total_price` decimal(15, 2) DEFAULT '0.00' COMMENT '实发总金额',
`type` smallint(6) DEFAULT '1' COMMENT '类型:1订单商品,2赠品,3配货商品,5特惠商品',
`gift_model_id` bigint(20) DEFAULT NULL COMMENT '赠送方案id',
`promotion_id` bigint(20) DEFAULT NULL COMMENT '配货或配比方案id',
`specials_id` bigint(20) DEFAULT NULL COMMENT '鲜达特惠商品t_xda_specials_commodity_set',
`remark` varchar(1000) DEFAULT NULL COMMENT '备注',
`presale_status` tinyint(4) UNSIGNED DEFAULT '0' COMMENT '是否预售  0 否   1 是',
`comb_type` tinyint(2) DEFAULT '1' COMMENT '商品类型-1-非组合 2-组合  3-组合子品',
`update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
`create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
`comb_commodity_id` bigint(32) DEFAULT NULL COMMENT '属于组合商品id',
`price_promotion_id` bigint(20) DEFAULT NULL COMMENT '鲜达B端特价id',
`coupon_discount_amount` decimal(15, 2) DEFAULT '0.00' COMMENT '优惠券分摊金额',
`coupon_id` bigint(32) DEFAULT NULL COMMENT '优惠券id',
`coupon_user_id` bigint(32) DEFAULT NULL COMMENT '用户优惠券id'
) ENGINE = InnoDB;


CREATE TABLE `t_order_list_gift` (
`id` bigint(20) NOT NULL AUTO_INCREMENT,
`order_id` bigint(20) NOT NULL,
`commodity_id` bigint(20) DEFAULT NULL,
`commodity_num` decimal(20, 3) DEFAULT NULL COMMENT '商品数量',
`real_quantity` decimal(14, 2) DEFAULT '0.00' COMMENT '实发数量',
`total_price` decimal(15, 2) DEFAULT '0.00',
`commodity_price` decimal(14, 6) DEFAULT '0.000000',
`gift_model_id` bigint(20) DEFAULT NULL COMMENT '赠送方案id',
`promotion_id` bigint(20) DEFAULT NULL COMMENT '配货或配比方案id',
`type` smallint(6) DEFAULT '1' COMMENT '1-订单商品,2-赠品,3-配货商品,5-特惠商品',
`real_total_price` decimal(15, 2) DEFAULT '0.00' COMMENT '实发总金额',
`remark` varchar(1000) DEFAULT NULL,
`original_price` decimal(14, 6) DEFAULT NULL COMMENT '原价',
`original_total_price` decimal(14, 2) DEFAULT NULL COMMENT '原金额(原价*数量)',
`comb_type` tinyint(2) DEFAULT '1' COMMENT '商品类型-1-非组合 2-组合  3-组合子品',
`comb_commodity_id` bigint(32) DEFAULT NULL COMMENT '属于组合商品id',
`price_promotion_id` bigint(20) DEFAULT NULL COMMENT '鲜达B端特价id',
`coupon_discount_amount` decimal(15, 2) DEFAULT '0.00' COMMENT '优惠券分摊金额',
`coupon_id` bigint(32) DEFAULT NULL COMMENT '优惠券id',
`coupon_user_id` bigint(32) DEFAULT NULL COMMENT '用户优惠券id'
) ENGINE = InnoDB;

CREATE TABLE `t_sub_order` (
`id` bigint(32) UNSIGNED NOT NULL AUTO_INCREMENT,
`order_id` bigint(32) NOT NULL COMMENT '订单id',
`logistics_model` int(2) NOT NULL COMMENT '物流配送模式, 0-直送, 1-配送, 2-直通',
`variety_total` int(5) NOT NULL COMMENT '冗余字段,商品品种类型数(如可乐,雪碧则是2,表示的不是种类饮料,表示的是几种商品)',
`supplier_id` bigint(32) UNSIGNED DEFAULT NULL COMMENT '供应商id',
`warehouse_id` bigint(32) UNSIGNED DEFAULT NULL COMMENT '仓库id',
`create_id` bigint(32) DEFAULT NULL,
`create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
`update_id` bigint(32) DEFAULT NULL,
`update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
`enterprise_id` bigint(32) UNSIGNED DEFAULT '78',
`status` int(2) UNSIGNED DEFAULT NULL COMMENT '0-未生成do单, 1-已生成do单，2-已取消,3=3-过了覆盖时间修改已生成拣货单的订单',
`order_time` date DEFAULT NULL,
`purchase_order_id` bigint(32) UNSIGNED DEFAULT NULL COMMENT '采购单id',
`sub_order_code` varchar(32) DEFAULT NULL COMMENT '子订单编号',
`total_price` decimal(10, 2) DEFAULT NULL COMMENT '子订单总金额',
`writeback_real_qty_flag` tinyint(1) DEFAULT '0' COMMENT '已回填实发数量标识，0=未回填，1=已回填',
`presale_status` tinyint(4) UNSIGNED DEFAULT '1' COMMENT '是否预售  0 否   1 是',
`stock_type` tinyint(4) UNSIGNED DEFAULT NULL COMMENT '库存依据  1=依据大仓, 2=不限量订货, 3=限量供应'
) ENGINE = InnoDB;

CREATE TABLE `t_sub_order_item` (
`id` bigint(32) UNSIGNED NOT NULL AUTO_INCREMENT,
`sub_order_id` bigint(32) NOT NULL COMMENT '销售子单id',
`commodity_id` bigint(32) NOT NULL COMMENT '商品id',
`quantity` decimal(20, 3) UNSIGNED NOT NULL COMMENT '数量',
`price` decimal(10, 2) NOT NULL COMMENT '销售单价',
`total_price` decimal(10, 2) DEFAULT NULL COMMENT '总价',
`create_id` bigint(32) DEFAULT NULL COMMENT '创建记录人',
`create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '记录创建时间',
`update_id` bigint(32) DEFAULT NULL,
`update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
`real_receive_quantity` decimal(10, 3) UNSIGNED DEFAULT NULL COMMENT '实收数量',
`status` int(2) DEFAULT NULL COMMENT '状态: 0-审核未通过,1-审核通过',
`reject_reason` varchar(255) DEFAULT NULL COMMENT '驳回原因',
`real_delivery_quantity` decimal(10, 3) UNSIGNED DEFAULT NULL COMMENT '实发数量',
`change_price_status` tinyint(2) DEFAULT '0' COMMENT '是否可变价',
`target_commodity_id` bigint(20) DEFAULT NULL COMMENT '组合商品转换的最小单位商品ID',
`convert_status` tinyint(4) DEFAULT '0' COMMENT '组合商品转换状态：0=无转换，1=有转换',
`source_ratio` int(11) DEFAULT NULL COMMENT '组合商品源转换比率',
`target_ratio` int(11) DEFAULT NULL COMMENT '组合商品目标转换比率',
`target_quantity` decimal(10, 3) DEFAULT NULL COMMENT '组合商品目标数量 : 向下取整 ( quantity / source_ratio * target_ratio)',
`type` tinyint(2) DEFAULT '1' COMMENT '类型:1订单商品 2赠品 3配货商品 4、配比商品 5特惠商品',
`comb_type` tinyint(2) DEFAULT '1' COMMENT '商品类型-1-非组合 2-组合  3-组合子品',
`comb_commodity_id` bigint(32) DEFAULT NULL COMMENT '属于组合商品id',
`comb_order_list_id` bigint(32) DEFAULT NULL COMMENT '对应t_order_list表的主键id',
`price_promotion_id` bigint(20) DEFAULT NULL COMMENT '鲜达B端特价id'
) ENGINE = InnoDB;


CREATE TABLE `t_md_receive_order` (
`id` bigint(32) UNSIGNED NOT NULL AUTO_INCREMENT,
`sub_order_id` bigint(32) NOT NULL,
`status` int(1) NOT NULL COMMENT '状态:0-待收货, 1-待审核, 2-审核未通过,3-审核通过, 4-已取消',
`receive_time` timestamp NULL DEFAULT NULL COMMENT '收货时间',
`receive_id` bigint(32) DEFAULT NULL COMMENT '收货人',
`remark` varchar(255) DEFAULT NULL COMMENT '备注',
`enterprise_id` bigint(32) UNSIGNED DEFAULT NULL COMMENT '企业id',
`create_id` bigint(32) NOT NULL COMMENT '记录创建人',
`update_id` bigint(32) DEFAULT NULL COMMENT '更新人',
`create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '记录创建时间',
`update_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '记录更新时间'
) ENGINE = InnoDB;

INSERT INTO `t_order` (`id`, `enterprise_id`, `company_id`, `order_code`, `store_id`, `real_order_time`, `order_time`, `mode_type`, `order_type`, `print_num`, `print_type`, `total_amount`, `real_total_price`, `final_amount`, `order_amount`, `freight_amount`, `promotion_amount`, `order_remark`, `create_time`, `update_id`, `update_time`, `order_status`, `process_status`, `order_duration_time`, `create_id`, `sync_status`, `settle_status`, `delivery_batch`, `delivery_batch_remark`, `change_price_status`, `cacel_reason_id`, `consignment_id`, `real_sub_order_done_status`, `presale_status`, `delivery_time_range`, `logistics_center_id`, `logistics_center`, `business_type`, `settle_order_time`, `store_type_id`, `driver_id`, `stall_id`, `direct_status`, `logistics_carrier_code`) VALUES ('*********', '78', '1', '1754471538629852591', '999872035591560711', NULL, '2025-08-13', '0', '22', '1', '3', '12.65', NULL, '12.65', '12.65', '0.00', '0.00', NULL, '2025-08-06 17:12:19', '1151', '2025-08-07 17:04:10', '0', '11', NULL, '1151', '0', '0', '1', '1配_YC', '0', NULL, '-1', NULL, '0', NULL, '1', '上海中心仓', '13', '2025-08-07', '9131078242860601507', NULL, '16', '0', NULL);

INSERT INTO `t_order_list` (`id`, `order_id`, `commodity_id`, `commodity_num`, `real_quantity`, `original_price`, `special_price`, `original_total_price`, `commodity_price`, `total_price`, `real_total_price`, `type`, `gift_model_id`, `promotion_id`, `specials_id`, `remark`, `presale_status`, `comb_type`, `update_time`, `create_time`, `comb_commodity_id`, `price_promotion_id`, `coupon_discount_amount`, `coupon_id`, `coupon_user_id`) VALUES ('2584065443', '*********', '850466551156770560', '1.000', '1.000', '5.65', NULL, '5.65', '5.65', '5.65', '5.65', '1', NULL, NULL, NULL, '订单商品', '0', '1', '2025-08-13 15:55:38', '2025-08-06 17:12:19', NULL, NULL, NULL, NULL, NULL);
INSERT INTO  `t_order_list` (`id`, `order_id`, `commodity_id`, `commodity_num`, `real_quantity`, `original_price`, `special_price`, `original_total_price`, `commodity_price`, `total_price`, `real_total_price`, `type`, `gift_model_id`, `promotion_id`, `specials_id`, `remark`, `presale_status`, `comb_type`, `update_time`, `create_time`, `comb_commodity_id`, `price_promotion_id`, `coupon_discount_amount`, `coupon_id`, `coupon_user_id`) VALUES ('2584065444', '*********', '32289764792662004', '1.000', '1.000', '7.00', NULL, '7.00', '7.00', '7.00', '7.00', '1', NULL, NULL, NULL, '订单商品', '0', '1', '2025-08-13 15:55:40', '2025-08-06 17:12:19', NULL, NULL, NULL, NULL, NULL);

INSERT INTO `t_order_list_gift` (`id`, `order_id`, `commodity_id`, `real_quantity`, `commodity_num`, `total_price`, `real_total_price`, `commodity_price`, `gift_model_id`, `promotion_id`, `type`, `remark`, `original_price`, `original_total_price`, `comb_type`, `comb_commodity_id`, `price_promotion_id`, `coupon_discount_amount`, `coupon_id`, `coupon_user_id`) VALUES ('2571187829', '*********', '850466551156770560', '1.00', '1.00', '5.6500', '5.65', '5.65', NULL, NULL, '1', NULL, '5.650000', '5.65', '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO `t_order_list_gift` (`id`, `order_id`, `commodity_id`, `real_quantity`, `commodity_num`, `total_price`, `real_total_price`, `commodity_price`, `gift_model_id`, `promotion_id`, `type`, `remark`, `original_price`, `original_total_price`, `comb_type`, `comb_commodity_id`, `price_promotion_id`, `coupon_discount_amount`, `coupon_id`, `coupon_user_id`) VALUES ('2571187830', '*********', '32289764792662004', '1.00', '1.00', '7.0000', '7.00', '7.00', NULL, NULL, '1', NULL, '7.000000', '7.00', '1', NULL, NULL, NULL, NULL, NULL);

INSERT INTO `t_sub_order` (`id`, `order_id`, `logistics_model`, `variety_total`, `supplier_id`, `warehouse_id`, `create_id`, `create_time`, `update_id`, `update_time`, `enterprise_id`, `status`, `order_time`, `purchase_order_id`, `sub_order_code`, `total_price`, `writeback_real_qty_flag`, `presale_status`, `stock_type`) VALUES ('197233159', '*********', '2', '2', '161', '10000', '1151', '2025-08-06 17:12:21', NULL, '2025-08-07 17:04:10', '78', '1', '2025-08-07', NULL, '1754471538629852591', '12.65', NULL, '0', '2');

INSERT INTO `t_sub_order_item` (`id`, `sub_order_id`, `commodity_id`, `quantity`, `price`, `total_price`, `create_id`, `create_time`, `update_id`, `update_time`, `real_receive_quantity`, `status`, `reject_reason`, `real_delivery_quantity`, `change_price_status`, `target_commodity_id`, `convert_status`, `source_ratio`, `target_ratio`, `target_quantity`, `type`, `comb_type`, `comb_commodity_id`, `comb_order_list_id`, `price_promotion_id`) VALUES ('2364832376', '197233159', '850466551156770560', '1.00', '5.65', '5.65', '1151', '2025-08-06 17:12:21', NULL, '2025-08-13 15:56:06', NULL, '0', NULL, '1.00', '0', NULL, '0', NULL, NULL, NULL, '1', '1', NULL, '2584065443', NULL);
INSERT INTO `t_sub_order_item` (`id`, `sub_order_id`, `commodity_id`, `quantity`, `price`, `total_price`, `create_id`, `create_time`, `update_id`, `update_time`, `real_receive_quantity`, `status`, `reject_reason`, `real_delivery_quantity`, `change_price_status`, `target_commodity_id`, `convert_status`, `source_ratio`, `target_ratio`, `target_quantity`, `type`, `comb_type`, `comb_commodity_id`, `comb_order_list_id`, `price_promotion_id`) VALUES ('2364832377', '197233159', '32289764792662004', '1.00', '7.00', '7.00', '1151', '2025-08-06 17:12:21', NULL, '2025-08-13 15:56:06', NULL, '0', NULL, '1.00', '0', NULL, '0', NULL, NULL, NULL, '1', '1', NULL, '2584065444', NULL);

INSERT INTO `t_md_receive_order` (`id`, `sub_order_id`, `status`, `receive_time`, `receive_id`, `remark`, `enterprise_id`, `create_id`, `update_id`, `create_time`, `update_time`) VALUES ('5529428', '197233159', '0', '2025-08-06 17:21:28', '-1', NULL, '78', '1151', '-1', '2025-08-06 17:12:21', '2025-08-13 15:55:14');



CREATE TABLE `t_md_shop_receive_setting` (
`id` bigint(32) NOT NULL AUTO_INCREMENT,
`shop_id` bigint(32) DEFAULT NULL,
`receive_type` tinyint(2) DEFAULT '0' COMMENT '收货方式 0 自动收货  1 手动收货',
`create_id` bigint(32) NOT NULL COMMENT '创建人',
`create_time` datetime NOT NULL COMMENT '创建时间',
`update_id` bigint(32) NOT NULL COMMENT '更新人',
`update_time` datetime NOT NULL COMMENT '更新时间'
) ENGINE = InnoDB;

INSERT INTO `t_md_shop_receive_setting` (`id`, `shop_id`, `receive_type`, `create_id`, `create_time`, `update_id`, `update_time`) VALUES ('519', '100052', '0', '1', '2025-08-13 11:26:05', '1', '2025-08-13 11:26:08');


CREATE TABLE `t_md_shop` (
`id` bigint(32) UNSIGNED NOT NULL AUTO_INCREMENT,
`enterprise_id` bigint(20) UNSIGNED NOT NULL COMMENT '企业id',
`store_id` bigint(20) NOT NULL COMMENT '客户id',
`shop_code` varchar(32) NOT NULL COMMENT '门店code',
`shop_name` varchar(128) NOT NULL COMMENT '门店名称',
`shop_short_name` varchar(20) DEFAULT NULL COMMENT '门店简称',
`shop_aid` varchar(100) NOT NULL COMMENT '助记码',
`shop_type` tinyint(2) NOT NULL DEFAULT '1' COMMENT '门店类型：1-门店，2-鲜食店, 3-乐食，4-包子铺',
`management_mode` tinyint(2) DEFAULT NULL COMMENT '经营模式：1-直营、2-外包',
`contacts` varchar(20) DEFAULT NULL COMMENT '联系人',
`mobile` varchar(20) NOT NULL COMMENT '手机',
`phone` varchar(32) DEFAULT NULL COMMENT '电话',
`remark` varchar(100) DEFAULT NULL COMMENT '备注',
`status` tinyint(2) UNSIGNED DEFAULT NULL COMMENT '状态:0-线上不可见,1-线上可见',
`shop_status` tinyint(2) UNSIGNED NOT NULL COMMENT '状态：2-开业前、0-暂停营业、1-营业中、3-永久停业，（原0-停用,1-启用）',
`open_date` date DEFAULT NULL COMMENT '开业日期',
`first_on_visible_date` date DEFAULT NULL COMMENT '首次线上可见时间',
`country_id` bigint(20) DEFAULT NULL COMMENT '国家id',
`province_id` bigint(20) DEFAULT NULL COMMENT '省份id',
`city_id` bigint(20) DEFAULT NULL COMMENT '市id',
`area_id` bigint(20) DEFAULT NULL COMMENT '区域id',
`town_id` bigint(20) DEFAULT NULL COMMENT '乡镇街道ID',
`detail_address` varchar(100) DEFAULT NULL COMMENT '详细地址:乡村街道等',
`shop_area` decimal(12, 2) DEFAULT NULL COMMENT '门店面积',
`longitude` decimal(12, 6) DEFAULT NULL COMMENT '经度',
`latitude` decimal(12, 6) DEFAULT NULL COMMENT '纬度',
`baidu_longitude` decimal(12, 6) DEFAULT NULL COMMENT '百度经度',
`baidu_latitude` decimal(12, 6) DEFAULT NULL COMMENT '百度纬度',
`polygon` text COMMENT '配送范围:多边形:二维经纬度json数据',
`shop_img` varchar(200) DEFAULT NULL COMMENT '门店图片',
`create_id` bigint(32) UNSIGNED NOT NULL COMMENT '创建人',
`create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
`update_id` bigint(32) UNSIGNED NOT NULL COMMENT '更新人',
`update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
`business_license_name` varchar(50) DEFAULT '' COMMENT '营业执照名称',
`real_detail_address` varchar(100) DEFAULT NULL,
`befor_detail_address` varchar(100) DEFAULT NULL COMMENT 'detail_address数据备份'
) ENGINE = InnoDB;


INSERT INTO `t_md_shop` (`id`, `enterprise_id`, `store_id`, `shop_code`, `shop_name`, `shop_short_name`, `shop_aid`, `shop_type`, `management_mode`, `contacts`, `mobile`, `phone`, `remark`, `status`, `shop_status`, `open_date`, `first_on_visible_date`, `country_id`, `province_id`, `city_id`, `area_id`, `town_id`, `detail_address`, `shop_area`, `longitude`, `latitude`, `baidu_longitude`, `baidu_latitude`, `polygon`, `shop_img`, `create_id`, `create_time`, `update_id`, `update_time`, `business_license_name`, `real_detail_address`, `befor_detail_address`) VALUES ('100052', '78', '999872035591560711', '0531', '工坊1号大店', '工坊1号大店', 'GFD1', '20', '3', '13344444444444444', '***********', '***********', '', '1', '1', '2024-09-24', NULL, '22', '264', '429', '1354', '3951', '\r\n上海市静安区延安中路1218号', '123.00', '121.449012', '31.224119', '121.455447', '31.230449', '[[121.418971:31.224280],[121.449034:31.196125],[121.478886:31.224609],[121.449023:31.252122],[121.418971:31.224280]]', '', '18196', '2024-09-24 09:44:11', '100004', '2024-10-25 14:56:14', '', '延安中路1218号', NULL);


CREATE TABLE `t_dd_receive_doc` (
`id` bigint(32) NOT NULL AUTO_INCREMENT,
`doc_code` varchar(100) NOT NULL COMMENT '单据号',
`order_time` date NOT NULL COMMENT '送货日期',
`shop_id` bigint(32) NOT NULL COMMENT '门店id',
`stall_id` bigint(32) NOT NULL COMMENT '档口id',
`delivery_batch` tinyint(2) NOT NULL COMMENT '配送批次 1=1配 + 补货  2=2配  8=新开店',
`doc_status` tinyint(2) NOT NULL COMMENT '单据状态：0 待收货 1 已经收货',
`receive_user_id` bigint(32) DEFAULT NULL COMMENT '收货人',
`receive_time` datetime DEFAULT NULL COMMENT '收货时间',
`create_id` bigint(32) NOT NULL COMMENT '创建者',
`create_time` datetime NOT NULL COMMENT '创建日期',
`update_id` bigint(32) NOT NULL COMMENT '更新者',
`update_time` datetime NOT NULL COMMENT '更新时间'
) ENGINE = InnoDB;


INSERT INTO `t_dd_receive_doc` (`id`, `doc_code`, `order_time`, `shop_id`, `stall_id`, `delivery_batch`, `doc_status`, `receive_user_id`, `receive_time`, `create_id`, `create_time`, `update_id`, `update_time`) VALUES ('38', 'DDSH2025080700001', '2025-08-13', '100052', '16', '1', '0', '-1', '2025-08-06 17:21:28', '-1', '2025-08-06 17:12:21', '-1', '2025-08-06 17:21:28');


CREATE TABLE `t_dd_receive_doc_log` (
`id` bigint(32) NOT NULL AUTO_INCREMENT,
`doc_id` bigint(32) NOT NULL COMMENT '单据号',
`order_time` date NOT NULL COMMENT '送货日期',
`shop_id` bigint(32) NOT NULL COMMENT '门店id',
`stall_id` bigint(32) NOT NULL COMMENT '档口id',
`delivery_batch` tinyint(2) NOT NULL COMMENT '配送批次 1=1配 + 补货  2=2配  8=新开店',
`commodity_id` bigint(32) NOT NULL COMMENT '商品id',
`quantity` decimal(10, 3) DEFAULT NULL COMMENT '订货数量',
`real_delivery_quantity` decimal(10, 3) DEFAULT NULL COMMENT '实发数量',
`real_receive_quantity` decimal(10, 3) DEFAULT NULL COMMENT '实际收货数量',
`short_receive_quantity` decimal(10, 3) DEFAULT NULL COMMENT '少收数量',
`storage_area` tinyint(4) DEFAULT NULL COMMENT '库区 1排面区 2拣货区 3存储区',
`goods_allocation_id` bigint(20) DEFAULT NULL COMMENT '货位号id',
`short_status` tinyint(2) DEFAULT '0' COMMENT '是否已经生成少货单 0-未生成  1-已生成',
`create_id` bigint(32) NOT NULL COMMENT '创建者',
`create_time` datetime NOT NULL COMMENT '创建日期',
`update_id` bigint(32) NOT NULL COMMENT '更新者',
`update_time` datetime NOT NULL COMMENT '更新时间'
) ENGINE = InnoDB;


CREATE TABLE `t_dd_receive_doc_order` (
`id` bigint(32) NOT NULL AUTO_INCREMENT,
`doc_id` bigint(32) NOT NULL COMMENT '单据号',
`order_time` date NOT NULL COMMENT '送货日期',
`shop_id` bigint(32) NOT NULL COMMENT '门店id',
`order_code` varchar(20) DEFAULT NULL COMMENT '订单号',
`logistics_model` int(2) NOT NULL COMMENT '物流配送模式, 0-直送, 1-配送, 2-直通',
`order_amount` decimal(19, 2) DEFAULT NULL COMMENT '订单金额',
`create_id` bigint(32) NOT NULL COMMENT '创建者',
`create_time` datetime NOT NULL COMMENT '创建日期',
`update_id` bigint(32) NOT NULL COMMENT '更新者',
`update_time` datetime NOT NULL COMMENT '更新时间'
) ENGINE = InnoDB;



CREATE TABLE `t_commodity` (
`id` bigint(32) NOT NULL AUTO_INCREMENT,
`enterprise_id` bigint(20) DEFAULT NULL COMMENT 'ä¼ä¸šid',
`commodity_code` varchar(32) DEFAULT NULL COMMENT '产品代码',
`commodity_name` varchar(100) DEFAULT NULL COMMENT '产品名称',
`commodity_sub_name` varchar(100) DEFAULT NULL COMMENT '产品副名称(副标题)',
`commodity_describe` varchar(1000) DEFAULT NULL COMMENT '产品描述',
`commodity_aid` varchar(100) DEFAULT NULL COMMENT '助记码',
`commodity_old_code` varchar(32) DEFAULT NULL COMMENT '旧代码',
`commodity_spec` varchar(100) DEFAULT NULL COMMENT '型号规格',
`brand_id` decimal(32, 0) DEFAULT NULL COMMENT '品牌ID',
`brand_code` varchar(100) DEFAULT NULL COMMENT '品牌编号',
`commodity_cycle_id` decimal(32, 0) DEFAULT NULL COMMENT '生命周期ID',
`commodity_cycle` varchar(6) DEFAULT NULL COMMENT '生命周期名称',
`commodity_first_id` decimal(32, 0) DEFAULT NULL COMMENT '大类ID',
`commodity_first_kind` varchar(32) DEFAULT NULL COMMENT '大类',
`commodity_second_id` decimal(32, 0) DEFAULT NULL COMMENT '中类ID',
`commodity_second_kind` varchar(32) DEFAULT NULL COMMENT '中类',
`commodity_third_id` decimal(32, 0) DEFAULT NULL COMMENT '小类ID',
`commodity_third_kind` varchar(32) DEFAULT NULL COMMENT '小类',
`commodity_type_id` decimal(32, 0) DEFAULT NULL COMMENT '类型ID',
`commodity_type` varchar(32) DEFAULT NULL COMMENT '类型',
`commodity_weight` varchar(100) DEFAULT NULL COMMENT '净含量',
`commodity_unit_id` bigint(20) DEFAULT NULL COMMENT '单位ID',
`commodity_unit` varchar(32) DEFAULT NULL COMMENT '单位',
`commodity_state` varchar(6) NOT NULL COMMENT '状态：0-不可售,1-可售',
`commodity_package_id` decimal(32, 0) DEFAULT NULL COMMENT '包装类型ID',
`commodity_package_kind` varchar(32) DEFAULT NULL COMMENT '包装类型',
`commodity_factory_id` decimal(32, 0) DEFAULT NULL COMMENT '工厂ID(t_factory.id)',
`commodity_factory` varchar(32) DEFAULT NULL COMMENT '工厂',
`new_workshop_id` decimal(32, 0) DEFAULT NULL COMMENT '新车间ID(生产组t_factory_workshop.id)',
`new_workshop` varchar(100) DEFAULT NULL COMMENT '新车间',
`old_workshop_id` decimal(32, 0) DEFAULT NULL COMMENT '旧车间ID',
`old_workshop` varchar(100) DEFAULT NULL COMMENT '旧车间',
`retail_price` decimal(16, 2) DEFAULT NULL COMMENT '零售价',
`cost_price` decimal(16, 2) DEFAULT NULL COMMENT '成本价',
`first_cost` decimal(16, 2) DEFAULT NULL COMMENT '生产成本价',
`tax_rate_id` bigint(20) DEFAULT NULL COMMENT '税率id',
`tax_rate` decimal(10, 4) DEFAULT NULL COMMENT '税率',
`other_price` decimal(16, 2) DEFAULT NULL COMMENT '其他价',
`launch_date` datetime DEFAULT NULL COMMENT '上市日期',
`delisting_date` datetime DEFAULT NULL COMMENT '下士日期',
`pause_begin_date` datetime DEFAULT NULL COMMENT '暂停开始时间',
`pause_end_date` datetime DEFAULT NULL COMMENT '暂停结束时间',
`pause_remark` varchar(1000) DEFAULT NULL COMMENT '暂停备注',
`order_is_time_range` smallint(6) DEFAULT NULL COMMENT '按时间段订货',
`min_order` decimal(16, 2) DEFAULT NULL COMMENT '起订量',
`time_limit_order` smallint(6) DEFAULT NULL COMMENT '限时订货',
`piece_rate` decimal(16, 2) DEFAULT NULL COMMENT '计件单价',
`commodity_is_quick_freeze` smallint(6) DEFAULT NULL COMMENT '是否速冻产品',
`commodity_is_instant` smallint(6) DEFAULT '0' COMMENT '是否即食',
`commodity_color` varchar(100) DEFAULT NULL COMMENT '颜色',
`commodity_shape` varchar(100) DEFAULT NULL COMMENT '形状',
`quality_days` int(11) DEFAULT NULL COMMENT '保质期天数',
`quality_unit_id` decimal(32, 0) DEFAULT NULL COMMENT '保质期单位ID',
`quality_unit` varchar(100) DEFAULT NULL COMMENT '保质期单位',
`storage_condition` varchar(1000) DEFAULT NULL COMMENT '保质期贮存条件',
`commodity_remark` varchar(1000) DEFAULT NULL COMMENT '备注',
`company_standard` varchar(1000) DEFAULT NULL,
`create_time` datetime DEFAULT NULL,
`update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
`is_frame` int(11) DEFAULT '0',
`storage_type_id` bigint(20) DEFAULT NULL COMMENT '贮存类型',
`weight_unit_id` bigint(20) DEFAULT NULL COMMENT '净含量单位id',
`com_standard_id` bigint(20) DEFAULT NULL COMMENT '公司标准id',
`excute_standard_id` bigint(20) DEFAULT NULL COMMENT '执行标准',
`grade_id` bigint(20) DEFAULT NULL COMMENT '等级id',
`safe_type_id` bigint(20) DEFAULT NULL COMMENT '安全类别',
`down_type_id` bigint(20) DEFAULT NULL COMMENT '下浮类型id',
`category_code_name` varchar(100) DEFAULT NULL COMMENT '分类代码名称',
`is_brand_locked` int(2) DEFAULT NULL COMMENT '是否定牌',
`is_weight` int(2) DEFAULT '0' COMMENT '是否称重0-不称量,1-称重',
`is_summary` tinyint(1) UNSIGNED NOT NULL DEFAULT '0' COMMENT '是否框汇总(0=否，1=是)',
`is_btb` int(2) DEFAULT '0' COMMENT '是否B2B',
`is_oto` int(2) DEFAULT '0' COMMENT '是否O2O',
`bar_code` varchar(50) DEFAULT NULL COMMENT '条形码',
`origin` varchar(100) DEFAULT NULL COMMENT '产地',
`category_code_id` bigint(20) DEFAULT NULL COMMENT '分类代码名称id',
`pic_url1` varchar(500) DEFAULT NULL COMMENT '图片路径，下同',
`pic_url2` varchar(500) DEFAULT NULL,
`pic_url3` varchar(500) DEFAULT NULL,
`default_pic_url` varchar(500) DEFAULT NULL COMMENT '默认图片',
`img_text_pic_url` varchar(500) DEFAULT NULL COMMENT '文描长图',
`length` float DEFAULT NULL COMMENT '长',
`width` float DEFAULT NULL,
`height` float DEFAULT NULL,
`weight` float DEFAULT NULL,
`logistics_model` tinyint(4) DEFAULT NULL COMMENT '物流配送模式0=直送，1＝配送，2＝直通',
`batch_status` tinyint(4) DEFAULT '0' COMMENT '批次状态,0:非批次 1:批次',
`sync_pos_status` int(2) DEFAULT '0' COMMENT '同步Pos状态:0未同步,1已同步',
`commodity_code_siss` varchar(30) DEFAULT NULL COMMENT '同步到思讯pos自编码',
`box_capacity` decimal(10, 2) UNSIGNED DEFAULT '1.00' COMMENT '采购箱规（正数）：用于大仓向供应商采购',
`sales_box_capacity` decimal(10, 3) UNSIGNED DEFAULT '1.000' COMMENT '销售箱装量（正数）：用于清美向门店销售',
`hi_tech_status` tinyint(4) DEFAULT '0' COMMENT '是否高新：0-否、1-是，默认0',
`tax_category_code` varchar(30) DEFAULT NULL COMMENT '税收分类编码',
`tax_free_type_id` bigint(20) DEFAULT '9131078242860601487' COMMENT '免税分类（字典表taxFreeType）',
`create_id` bigint(20) UNSIGNED DEFAULT NULL,
`update_id` bigint(20) DEFAULT NULL,
`sell_weight` int(5) DEFAULT NULL COMMENT '起卖重量(克)',
`sell_weight_range` int(11) DEFAULT NULL COMMENT '加重幅度(克)',
`purchase_status` tinyint(2) DEFAULT '1' COMMENT '可采状态:1-可采，0-不可采',
`status` tinyint(2) DEFAULT '1' COMMENT '淘汰状态:0-淘汰,1-正常',
`commodity_package_spec` decimal(10, 3) DEFAULT NULL COMMENT '包装规格',
`product_type` tinyint(2) NOT NULL DEFAULT '1' COMMENT '商品类型-1-普通商品，2-组合商品,3-资产商品',
`xd_sales_box_capacity` decimal(10, 3) DEFAULT '0.000' COMMENT '大店销售箱规',
`pf_box_capacity` decimal(10, 3) DEFAULT NULL COMMENT '批发箱规',
`hyd_sales_box_capacity` decimal(10, 3) DEFAULT NULL COMMENT '会员店销售箱规',
`stock_type` tinyint(4) DEFAULT NULL COMMENT '库存依据：1=依据大仓, 2=不限量订货,3=限量供应'
) ENGINE = InnoDB;


INSERT INTO `t_commodity` (`id`, `enterprise_id`, `commodity_code`, `commodity_name`, `commodity_sub_name`, `commodity_describe`, `commodity_aid`, `commodity_old_code`, `commodity_spec`, `brand_id`, `brand_code`, `commodity_cycle_id`, `commodity_cycle`, `commodity_first_id`, `commodity_first_kind`, `commodity_second_id`, `commodity_second_kind`, `commodity_third_id`, `commodity_third_kind`, `commodity_type_id`, `commodity_type`, `commodity_weight`, `commodity_unit_id`, `commodity_unit`, `commodity_state`, `commodity_package_id`, `commodity_package_kind`, `commodity_factory_id`, `commodity_factory`, `new_workshop_id`, `new_workshop`, `old_workshop_id`, `old_workshop`, `retail_price`, `cost_price`, `first_cost`, `tax_rate_id`, `tax_rate`, `other_price`, `launch_date`, `delisting_date`, `pause_begin_date`, `pause_end_date`, `pause_remark`, `order_is_time_range`, `min_order`, `time_limit_order`, `piece_rate`, `commodity_is_quick_freeze`, `commodity_is_instant`, `commodity_color`, `commodity_shape`, `quality_days`, `quality_unit_id`, `quality_unit`, `storage_condition`, `commodity_remark`, `company_standard`, `create_time`, `update_time`, `is_frame`, `storage_type_id`, `weight_unit_id`, `com_standard_id`, `excute_standard_id`, `grade_id`, `safe_type_id`, `down_type_id`, `category_code_name`, `is_brand_locked`, `is_weight`, `is_summary`, `is_btb`, `is_oto`, `bar_code`, `origin`, `category_code_id`, `pic_url1`, `pic_url2`, `pic_url3`, `default_pic_url`, `img_text_pic_url`, `length`, `width`, `height`, `weight`, `logistics_model`, `batch_status`, `sync_pos_status`, `commodity_code_siss`, `box_capacity`, `sales_box_capacity`, `hi_tech_status`, `tax_category_code`, `tax_free_type_id`, `create_id`, `update_id`, `sell_weight`, `sell_weight_range`, `purchase_status`, `status`, `commodity_package_spec`, `product_type`, `xd_sales_box_capacity`, `pf_box_capacity`, `hyd_sales_box_capacity`, `stock_type`) VALUES ('32289764792662004', '78', '000171', '大油豆腐', NULL, NULL, 'DYDF', '0171', '200克/袋', '796111228688878720', NULL, '342300044389326400', NULL, '996135984224522756', '001', '996135984224522767', '00110', '996135984224522777', '0011001', '838304195119172608', '01', '200.00', '58885492592457704', NULL, '1', '669621680953902336', '02', '59570401667998008', '02', '37', '01037', NULL, NULL, NULL, '0.00', '0.00', '9131078242860601467', '0.0900', '0.00', '2005-11-30 00:00:00', '1900-01-01 00:00:00', '1900-01-01 00:00:00', '1900-01-01 00:00:00', NULL, NULL, '0.00', '0', '0.00', '0', '0', NULL, NULL, NULL, NULL, NULL, '0-10℃', '101', NULL, '2013-09-11 00:00:00', '2025-05-21 15:12:58', '0', '838304195119172608', NULL, '1201', '1301', '541489875733328640', '1401', '1102', NULL, NULL, '0', '0', NULL, NULL, '6927128266863', '上海', NULL, '/PRODUCT/2017/11/28/949f5d90766246cabe8d51d98d592997.jpg', '/PRODUCT/2017/11/28/e7dd32f7a41641d8968ed11db3c4e11a.jpg', '/PRODUCT/2017/11/28/99d1f4c9fd05401a80ed9e52c8e3404d.jpg', '/PRODUCT/2017/11/28/949f5d90766246cabe8d51d98d592997.jpg', '/PRODUCT/2018/05/16/0bfd3c925def4f0abbb2388127c89700.jpg', '10.00', NULL, NULL, NULL, '2', '0', '1', '000123', '1.00', '1.000', '1', '1030113020000000000', '9131078242860601487', NULL, NULL, NULL, NULL, '1', '1', '1.000', '1', '1.000', '1.000', '1.000', '0');
INSERT INTO `t_commodity` (`id`, `enterprise_id`, `commodity_code`, `commodity_name`, `commodity_sub_name`, `commodity_describe`, `commodity_aid`, `commodity_old_code`, `commodity_spec`, `brand_id`, `brand_code`, `commodity_cycle_id`, `commodity_cycle`, `commodity_first_id`, `commodity_first_kind`, `commodity_second_id`, `commodity_second_kind`, `commodity_third_id`, `commodity_third_kind`, `commodity_type_id`, `commodity_type`, `commodity_weight`, `commodity_unit_id`, `commodity_unit`, `commodity_state`, `commodity_package_id`, `commodity_package_kind`, `commodity_factory_id`, `commodity_factory`, `new_workshop_id`, `new_workshop`, `old_workshop_id`, `old_workshop`, `retail_price`, `cost_price`, `first_cost`, `tax_rate_id`, `tax_rate`, `other_price`, `launch_date`, `delisting_date`, `pause_begin_date`, `pause_end_date`, `pause_remark`, `order_is_time_range`, `min_order`, `time_limit_order`, `piece_rate`, `commodity_is_quick_freeze`, `commodity_is_instant`, `commodity_color`, `commodity_shape`, `quality_days`, `quality_unit_id`, `quality_unit`, `storage_condition`, `commodity_remark`, `company_standard`, `create_time`, `update_time`, `is_frame`, `storage_type_id`, `weight_unit_id`, `com_standard_id`, `excute_standard_id`, `grade_id`, `safe_type_id`, `down_type_id`, `category_code_name`, `is_brand_locked`, `is_weight`, `is_summary`, `is_btb`, `is_oto`, `bar_code`, `origin`, `category_code_id`, `pic_url1`, `pic_url2`, `pic_url3`, `default_pic_url`, `img_text_pic_url`, `length`, `width`, `height`, `weight`, `logistics_model`, `batch_status`, `sync_pos_status`, `commodity_code_siss`, `box_capacity`, `sales_box_capacity`, `hi_tech_status`, `tax_category_code`, `tax_free_type_id`, `create_id`, `update_id`, `sell_weight`, `sell_weight_range`, `purchase_status`, `status`, `commodity_package_spec`, `product_type`, `xd_sales_box_capacity`, `pf_box_capacity`, `hyd_sales_box_capacity`, `stock_type`) VALUES ('850466551156770560', '78', '000017', '0.5素鸡00', NULL, NULL, '0.5SJ00', '0017', '0.5斤/只', '796111228688878720', NULL, '342300044389326400', NULL, '996135984224522756', '001', '996135984224522766', '00105', '996135984224522776', '0010501', '838304195119172608', '01', '250.00', '808415463808344576', NULL, '1', '669621680953902336', '02', '978083652094889728', '01', '10', '01010', NULL, NULL, NULL, '0.00', '0.00', '9131078242860601467', '0.0900', '0.00', '2003-06-03 00:00:00', '1900-01-01 00:00:00', '1900-01-01 00:00:00', '1900-01-01 00:00:00', NULL, NULL, '0.00', '0', '0.00', '0', '0', NULL, NULL, NULL, NULL, NULL, '0-10℃', '145', NULL, '2013-09-11 00:00:00', '2025-05-21 15:12:51', '0', '838304195119172608', NULL, '1201', '1301', '541489875733328640', '1401', '1102', NULL, NULL, '0', '0', NULL, NULL, '6927128255270', '上海', '15', '/PRODUCT/2018/01/02/af22360f0549404a86ad306c2d13c475.jpg', '/PRODUCT/2018/01/02/8b3687a097144c29a39302a5cb6009a4.jpg', '/PRODUCT/2018/01/02/bac2b0cfad2b4b65bb81301fecfd4d46.jpg', '/PRODUCT/2018/01/02/af22360f0549404a86ad306c2d13c475.jpg', '/PRODUCT/2018/05/10/0afd2510c0ac4f1981d173321cb3cbc5.jpg', '10.00', NULL, NULL, NULL, '2', '0', '1', '000008', '1.00', '1.000', '1', '1030113020000000000', '9131078242860601487', NULL, '1611', NULL, NULL, '1', '1', '1.000', '1', '1.000', '1.000', '1.000', '0');
