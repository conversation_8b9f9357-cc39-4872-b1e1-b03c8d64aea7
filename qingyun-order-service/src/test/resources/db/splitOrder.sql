CREATE TABLE `t_order` (
`id` bigint(32) NOT NULL AUTO_INCREMENT COMMENT '主键id',
`enterprise_id` bigint(32) DEFAULT NULL COMMENT '企业id',
`order_code` varchar(20) DEFAULT NULL COMMENT '订单号',
`store_id` bigint(32) DEFAULT NULL COMMENT '商铺id',
`real_order_time` date DEFAULT NULL COMMENT '实发日期',
`order_time` date DEFAULT NULL COMMENT '订单时间',
`company_id` bigint(20) NOT NULL DEFAULT '1' COMMENT '公司id ;t_company表主键id',
`mode_type` int(11) DEFAULT '0' COMMENT '订单类型：（0：普通订单，1：补货单）',
`order_type` int(32) DEFAULT NULL COMMENT '订单类型(1=PC下单,2=APP下单, 8=鲜达APP 9-批发app 10-门店订货下单 12-清美订单 13-mini团购订单 21-团购订单 22-门店订货PC版 23-门店订货PDA版 24-门店订货代理补货 25-大仓配货自动下单 26-门店自动订货 27-代理订货 28-直送补单 29-直送审核通过 30-提货卡自动下单 31-门店加货申请单 32-日日鲜商品自动下单 33-提货卡预约订单 34-云超团购自动下单 35-玖琅自动下单',
`print_num` int(11) DEFAULT NULL COMMENT '打印份数',
`print_type` int(11) DEFAULT NULL COMMENT '打印类型(1：本地,2：送货员,3：不打印)',
`total_amount` decimal(15, 2) DEFAULT '0.00' COMMENT '应付金额（参与促销活动之前的源始金额）',
`real_total_price` decimal(15, 2) DEFAULT '0.00' COMMENT '实发总金额',
`final_amount` decimal(15, 2) DEFAULT '0.00' COMMENT '最终金额',
`order_amount` decimal(19, 2) DEFAULT NULL COMMENT '订单金额',
`freight_amount` decimal(6, 2) DEFAULT '0.00' COMMENT '订单运费金额（免运费为0）',
`promotion_amount` decimal(15, 2) DEFAULT '0.00' COMMENT '优惠金额合计',
`order_remark` varchar(1000) DEFAULT NULL COMMENT '备注',
`create_time` datetime DEFAULT NULL COMMENT '创建日期',
`update_id` bigint(32) DEFAULT NULL COMMENT '更新者',
`update_time` datetime DEFAULT NULL COMMENT '更新时间',
`order_status` int(2) DEFAULT NULL COMMENT '订单状态(0正常,1删除,2取消)',
`process_status` int(2) UNSIGNED DEFAULT NULL COMMENT '流程状态(鲜达新加) 0=已取消 1=待拣货 7=待发货 11=出库中 12=待揽收 13=待配送　15=配送中　19=配送完成　20=配送失败',
`order_duration_time` datetime DEFAULT NULL COMMENT '订单截止时间(鲜达新加),根据当前客户截止时间与订单中的最晚商品截止时间得来;订单取消与是否进大仓时使用',
`create_id` bigint(32) DEFAULT NULL COMMENT '创建者',
`sync_status` int(1) NOT NULL DEFAULT '0' COMMENT '同步标识位，0为未同步，1为已同步',
`settle_status` int(11) DEFAULT '0' COMMENT ' 0:正常   1：未结算',
`delivery_batch` varchar(2) DEFAULT '0' COMMENT '0-无需批次配送, 1-1配，2-2配，3-3配，9-临时批次',
`delivery_batch_remark` varchar(30) DEFAULT '0_',
`change_price_status` tinyint(2) DEFAULT '0' COMMENT '是否可变价 :1是 0否',
`cacel_reason_id` bigint(20) DEFAULT NULL COMMENT '取消原因(字典ID)',
`consignment_id` bigint(20) DEFAULT '-1' COMMENT '代销商ID',
`real_sub_order_done_status` tinyint(1) DEFAULT NULL COMMENT '子订单是否出库完标识：只有1代表所有子单(不包括直送)已出库',
`presale_status` tinyint(4) UNSIGNED DEFAULT '1' COMMENT '是否预售  0 否   1 是',
`delivery_time_range` varchar(100) DEFAULT NULL COMMENT '送货时间段',
`logistics_center_id` bigint(20) DEFAULT NULL COMMENT '物流中心id',
`logistics_center` varchar(100) DEFAULT NULL COMMENT '物流中心',
`business_type` tinyint(4) DEFAULT NULL COMMENT '业务类型：10-通达销售  13-大店销售',
`settle_order_time` date DEFAULT NULL COMMENT '结算日期-结算单这边使用，出库完成修改为最终结算日期',
`store_type_id` bigint(20) DEFAULT NULL COMMENT '客户类型Id',
`driver_id` bigint(20) DEFAULT NULL COMMENT '配送司机',
`stall_id` bigint(20) DEFAULT '-1' COMMENT '档口id',
`direct_status` tinyint(4) NOT NULL DEFAULT '0' COMMENT '是否直送 0=否  1=是'
) ENGINE = InnoDB;


CREATE TABLE `t_order_list` (
`id` bigint(32) NOT NULL AUTO_INCREMENT,
`order_id` bigint(32) DEFAULT NULL COMMENT '订单id',
`commodity_id` bigint(32) DEFAULT NULL COMMENT '商品id',
`commodity_num` decimal(20, 3) DEFAULT NULL COMMENT '商品数量',
`real_quantity` decimal(14, 2) DEFAULT '0.00' COMMENT '实发数量',
`original_price` decimal(14, 2) DEFAULT NULL COMMENT '原价',
`special_price` decimal(14, 2) DEFAULT NULL COMMENT '特价',
`original_total_price` decimal(15, 2) DEFAULT NULL COMMENT '原金额(原价*数量)',
`commodity_price` decimal(19, 2) DEFAULT NULL COMMENT '促销后单价(原含义-单价)',
`total_price` decimal(15, 2) DEFAULT '0.00' COMMENT '促销后的商品金额(原含义-商品销售总金额)',
`real_total_price` decimal(15, 2) DEFAULT '0.00' COMMENT '实发总金额',
`type` smallint(6) DEFAULT '1' COMMENT '类型:1订单商品,2赠品,3配货商品,5特惠商品',
`gift_model_id` bigint(20) DEFAULT NULL COMMENT '赠送方案id',
`promotion_id` bigint(20) DEFAULT NULL COMMENT '配货或配比方案id',
`specials_id` bigint(20) DEFAULT NULL COMMENT '鲜达特惠商品t_xda_specials_commodity_set',
`remark` varchar(1000) DEFAULT NULL COMMENT '备注',
`presale_status` tinyint(4) UNSIGNED DEFAULT '0' COMMENT '是否预售  0 否   1 是',
`comb_type` tinyint(2) DEFAULT '1' COMMENT '商品类型-1-非组合 2-组合  3-组合子品',
`update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
`create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
`comb_commodity_id` bigint(32) DEFAULT NULL COMMENT '属于组合商品id',
`price_promotion_id` bigint(20) DEFAULT NULL COMMENT '鲜达B端特价id',
`coupon_discount_amount` decimal(15, 2) DEFAULT '0.00' COMMENT '优惠券分摊金额',
`coupon_id` bigint(32) DEFAULT NULL COMMENT '优惠券id',
`coupon_user_id` bigint(32) DEFAULT NULL COMMENT '用户优惠券id'
) ENGINE = InnoDB;


CREATE TABLE `t_order_list_gift` (
`id` bigint(20) NOT NULL AUTO_INCREMENT,
`order_id` bigint(20) NOT NULL,
`commodity_id` bigint(20) DEFAULT NULL,
`commodity_num` decimal(20, 3) DEFAULT NULL COMMENT '商品数量',
`real_quantity` decimal(14, 2) DEFAULT '0.00' COMMENT '实发数量',
`total_price` decimal(15, 2) DEFAULT '0.00',
`commodity_price` decimal(14, 6) DEFAULT '0.000000',
`gift_model_id` bigint(20) DEFAULT NULL COMMENT '赠送方案id',
`promotion_id` bigint(20) DEFAULT NULL COMMENT '配货或配比方案id',
`type` smallint(6) DEFAULT '1' COMMENT '1-订单商品,2-赠品,3-配货商品,5-特惠商品',
`real_total_price` decimal(15, 2) DEFAULT '0.00' COMMENT '实发总金额',
`remark` varchar(1000) DEFAULT NULL,
`original_price` decimal(14, 6) DEFAULT NULL COMMENT '原价',
`original_total_price` decimal(14, 2) DEFAULT NULL COMMENT '原金额(原价*数量)',
`comb_type` tinyint(2) DEFAULT '1' COMMENT '商品类型-1-非组合 2-组合  3-组合子品',
`comb_commodity_id` bigint(32) DEFAULT NULL COMMENT '属于组合商品id',
`price_promotion_id` bigint(20) DEFAULT NULL COMMENT '鲜达B端特价id',
`coupon_discount_amount` decimal(15, 2) DEFAULT '0.00' COMMENT '优惠券分摊金额',
`coupon_id` bigint(32) DEFAULT NULL COMMENT '优惠券id',
`coupon_user_id` bigint(32) DEFAULT NULL COMMENT '用户优惠券id'
) ENGINE = InnoDB;

CREATE TABLE `t_sub_order` (
`id` bigint(32) UNSIGNED NOT NULL AUTO_INCREMENT,
`order_id` bigint(32) NOT NULL COMMENT '订单id',
`logistics_model` int(2) NOT NULL COMMENT '物流配送模式, 0-直送, 1-配送, 2-直通',
`variety_total` int(5) NOT NULL COMMENT '冗余字段,商品品种类型数(如可乐,雪碧则是2,表示的不是种类饮料,表示的是几种商品)',
`supplier_id` bigint(32) UNSIGNED DEFAULT NULL COMMENT '供应商id',
`warehouse_id` bigint(32) UNSIGNED DEFAULT NULL COMMENT '仓库id',
`create_id` bigint(32) DEFAULT NULL,
`create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
`update_id` bigint(32) DEFAULT NULL,
`update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
`enterprise_id` bigint(32) UNSIGNED DEFAULT '78',
`status` int(2) UNSIGNED DEFAULT NULL COMMENT '0-未生成do单, 1-已生成do单，2-已取消,3=3-过了覆盖时间修改已生成拣货单的订单',
`order_time` date DEFAULT NULL,
`purchase_order_id` bigint(32) UNSIGNED DEFAULT NULL COMMENT '采购单id',
`sub_order_code` varchar(32) DEFAULT NULL COMMENT '子订单编号',
`total_price` decimal(10, 2) DEFAULT NULL COMMENT '子订单总金额',
`writeback_real_qty_flag` tinyint(1) DEFAULT '0' COMMENT '已回填实发数量标识，0=未回填，1=已回填',
`presale_status` tinyint(4) UNSIGNED DEFAULT '1' COMMENT '是否预售  0 否   1 是',
`stock_type` tinyint(4) UNSIGNED DEFAULT NULL COMMENT '库存依据  1=依据大仓, 2=不限量订货, 3=限量供应'
) ENGINE = InnoDB;

CREATE TABLE `t_sub_order_item` (
`id` bigint(32) UNSIGNED NOT NULL AUTO_INCREMENT,
`sub_order_id` bigint(32) NOT NULL COMMENT '销售子单id',
`commodity_id` bigint(32) NOT NULL COMMENT '商品id',
`quantity` decimal(20, 3) UNSIGNED NOT NULL COMMENT '数量',
`price` decimal(10, 2) NOT NULL COMMENT '销售单价',
`total_price` decimal(10, 2) DEFAULT NULL COMMENT '总价',
`create_id` bigint(32) DEFAULT NULL COMMENT '创建记录人',
`create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '记录创建时间',
`update_id` bigint(32) DEFAULT NULL,
`update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
`real_receive_quantity` decimal(10, 3) UNSIGNED DEFAULT NULL COMMENT '实收数量',
`status` int(2) DEFAULT NULL COMMENT '状态: 0-审核未通过,1-审核通过',
`reject_reason` varchar(255) DEFAULT NULL COMMENT '驳回原因',
`real_delivery_quantity` decimal(10, 3) UNSIGNED DEFAULT NULL COMMENT '实发数量',
`change_price_status` tinyint(2) DEFAULT '0' COMMENT '是否可变价',
`target_commodity_id` bigint(20) DEFAULT NULL COMMENT '组合商品转换的最小单位商品ID',
`convert_status` tinyint(4) DEFAULT '0' COMMENT '组合商品转换状态：0=无转换，1=有转换',
`source_ratio` int(11) DEFAULT NULL COMMENT '组合商品源转换比率',
`target_ratio` int(11) DEFAULT NULL COMMENT '组合商品目标转换比率',
`target_quantity` decimal(10, 3) DEFAULT NULL COMMENT '组合商品目标数量 : 向下取整 ( quantity / source_ratio * target_ratio)',
`type` tinyint(2) DEFAULT '1' COMMENT '类型:1订单商品 2赠品 3配货商品 4、配比商品 5特惠商品',
`comb_type` tinyint(2) DEFAULT '1' COMMENT '商品类型-1-非组合 2-组合  3-组合子品',
`comb_commodity_id` bigint(32) DEFAULT NULL COMMENT '属于组合商品id',
`comb_order_list_id` bigint(32) DEFAULT NULL COMMENT '对应t_order_list表的主键id',
`price_promotion_id` bigint(20) DEFAULT NULL COMMENT '鲜达B端特价id'
) ENGINE = InnoDB;


INSERT INTO `t_order` (`id`, `enterprise_id`, `order_code`, `store_id`, `real_order_time`, `order_time`, `company_id`, `mode_type`, `order_type`, `print_num`, `print_type`, `total_amount`, `real_total_price`, `final_amount`, `order_amount`, `freight_amount`, `promotion_amount`, `order_remark`, `create_time`, `update_id`, `update_time`, `order_status`, `process_status`, `order_duration_time`, `create_id`, `sync_status`, `settle_status`, `delivery_batch`, `delivery_batch_remark`, `change_price_status`, `cacel_reason_id`, `consignment_id`, `real_sub_order_done_status`, `presale_status`, `delivery_time_range`, `logistics_center_id`, `logistics_center`, `business_type`, `settle_order_time`, `store_type_id`, `driver_id`, `stall_id`, `direct_status`) VALUES ('*********', '78', '1751865468828361866', '999872035591660747', NULL, '2025-07-08', '1', '0', '1', '1', '1', '12.00', '0.00', '12.00', '12.00', '0.00', '0.00', '订单导入', '2025-07-07 13:17:48', '64865', '2025-07-07 13:17:54', '0', NULL, '2025-07-07 21:00:00', '64865', '1', '0', '0', '0_', '0', NULL, '-1', NULL, '0', NULL, NULL, NULL, NULL, '2025-07-08', '9131078242860604325', NULL, '-1', '0');

INSERT INTO `t_order_list` (`id`, `order_id`, `commodity_id`, `commodity_num`, `real_quantity`, `original_price`, `special_price`, `original_total_price`, `commodity_price`, `total_price`, `real_total_price`, `type`, `gift_model_id`, `promotion_id`, `specials_id`, `remark`, `presale_status`, `comb_type`, `update_time`, `create_time`, `comb_commodity_id`, `price_promotion_id`, `coupon_discount_amount`, `coupon_id`, `coupon_user_id`) VALUES ('2584065351', '*********', '468788174651305600', '2.000', NULL, '6.00', NULL, '12.00', '6.00', '12.00', NULL, '1', NULL, NULL, NULL, NULL, '0', '1', '2025-07-07 13:17:49', '2025-07-07 13:17:49', NULL, NULL, NULL, NULL, NULL);

INSERT INTO `t_order_list_gift` (`id`, `order_id`, `commodity_id`, `commodity_num`, `real_quantity`, `total_price`, `commodity_price`, `gift_model_id`, `promotion_id`, `type`, `real_total_price`, `remark`, `original_price`, `original_total_price`, `comb_type`, `comb_commodity_id`, `price_promotion_id`, `coupon_discount_amount`, `coupon_id`, `coupon_user_id`) VALUES ('2571187740', '*********', '468788174651305600', '2.000', NULL, '12.00', '6.000000', NULL, NULL, '1', NULL, NULL, '6.000000', '12.00', '1', NULL, NULL, NULL, NULL, NULL);
