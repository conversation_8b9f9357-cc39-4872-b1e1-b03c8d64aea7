package com.pinshang.qingyun.test;

import com.pinshang.qingyun.ApplicationOrderService;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit.jupiter.SpringExtension;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
import org.springframework.transaction.annotation.Transactional;

@RunWith(SpringJUnit4ClassRunner.class)
@SpringBootTest(classes = ApplicationOrderService.class,properties =
        {"spring.profiles.active=dev","application.name.switch=dev-two-"}
//        {"spring.profiles.active=wg-hd-prod","application.name.switch=hd-"}

        )
//@WebAppConfiguration
@Transactional
@ExtendWith(SpringExtension.class)
public  abstract class AbstractJunitBase {}