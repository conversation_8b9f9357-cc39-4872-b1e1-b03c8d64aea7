package com.pinshang.qingyun.test;

import com.pinshang.qingyun.base.configure.expand.QYAssert;
import com.pinshang.qingyun.base.enums.StoreBillTypeEnums;
import com.pinshang.qingyun.box.utils.SpringUtil;
import com.pinshang.qingyun.order.bo.StoreDeductionBO;
import com.pinshang.qingyun.order.bo.StoreRechargeBO;
import com.pinshang.qingyun.order.mapper.OrderBillMapper;
import com.pinshang.qingyun.order.mapper.OrderListMapper;
import com.pinshang.qingyun.order.mapper.OrderMapper;
import com.pinshang.qingyun.order.mapper.StoreSettlementMapper;
import com.pinshang.qingyun.order.model.order.Order;
import com.pinshang.qingyun.order.model.order.OrderBill;
import com.pinshang.qingyun.order.model.order.OrderList;
import com.pinshang.qingyun.order.model.order.StoreSettlement;
import com.pinshang.qingyun.order.service.StoreRechargeService;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.annotation.Commit;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.entity.Example;

import java.math.BigDecimal;
import java.util.*;

@Slf4j
public class OrderRechargeOrReductionServiceTest extends AbstractJunitBase {
    @Autowired
    private OrderMapper orderMapper;
    @Autowired
    private OrderListMapper orderListMapper;
    @Autowired
    private StoreRechargeService storeRechargeService;
    @Autowired
    StoreSettlementMapper storeSettlementMapper;
    @Autowired
    OrderBillMapper orderBillMapper;


    /**
     * PC回款
     */
    @Test
    @Commit
    public void updateOrder() {
        Example example = new Example(Order.class);
        example.createCriteria().andEqualTo("id", "76217626");
        Order order = orderMapper.selectOneByExample(example);
        List<String> orderCodeList = new ArrayList<>();
        updateOrder(order, new BigDecimal("18.12"), null, orderCodeList);
    }

    private void updateOrder(Order o, BigDecimal orderTotalPrice, Long userId, List<String> orderCodeList) {
        if (orderTotalPrice.compareTo(o.getOrderAmount()) == 0) {
            return;
        }
        orderCodeList.add(o.getOrderCode());
        log.info("开始更新Order --> id:" + o.getId() + " \t 价格 ：" + orderTotalPrice.toString());
        Order order = new Order();
        order.setId(o.getId());
        order.setFinalAmount(orderTotalPrice);
        //取list上得集合
        BigDecimal orderAmount = BigDecimal.ZERO;
        OrderList record = new OrderList();
        record.setOrderId(o.getId());
        List<OrderList> orderLists = orderListMapper.select(record);
        for (OrderList ol : orderLists) {
            orderAmount = orderAmount.add(ol.getTotalPrice());
        }
        order.setOrderAmount(orderAmount);
        orderMapper.updateByPrimaryKeySelective(order);
        log.info("更新Order --> id:" + o.getId() + "完成");
        if (o.getOrderAmount().compareTo(order.getOrderAmount()) == 0) {
            return;
        }
        //按照以前的订单回款                                                           							按照新订单扣款
        if (addPaymentForOrder(o.getStoreId(), o.getOrderAmount())) {
            saveOrderReceivable(o, userId);
        }
        if (updatePayment(o.getStoreId(), order.getOrderAmount())) {
            Order orderLog = new Order();
            SpringUtil.copyProperties(o, orderLog);
            orderLog.setOrderAmount(orderTotalPrice);
            saveOrderDeductions(orderLog, userId);
        }
//        Boolean preStore = storeRechargeService.isPreStore(o.getStoreId());
//        //预付费客户
//        if (Objects.equals(preStore, Boolean.TRUE)) {
//            //回款
//            StoreRechargeBO rechargeBO = StoreRechargeBO.builder()
//                    .orderCode(o.getOrderCode())
//                    .tradeCode(o.getOrderCode())
//                    .money(o.getOrderAmount().doubleValue())
//                    .storeId(o.getStoreId())
//                    .tradeTime(new Date())
//                    .receiptDate(new Date())
//                    .billType(StoreBillTypeEnums.PC_REFUNDMENT.getCode())
//                    .remark("<--PC回款:" + o.getOrderCode() + " -->")
//                    .userId(-1L)
//                    .build();
//            storeRechargeService.storeRecharge(rechargeBO);
//            log.info("进行订单回款orderId" + o.getId() + "完成");
//            //扣款
//            StoreDeductionBO deductionBO = StoreDeductionBO.builder()
//                    .orderCode(o.getOrderCode())
//                    .orderId(o.getId())
//                    .orderAmount(order.getOrderAmount())
//                    .storeId(o.getStoreId())
//                    .tradeCode(o.getOrderCode())
//                    .tradeTime(new Date())
//                    .orderTime(o.getOrderTime())
//                    .billType(StoreBillTypeEnums.PC_DEDUCTION.getCode())
//                    .remark("<--PC扣款:" + o.getOrderCode() + " -->")
//                    .userId(-1L)
//                    .build();
//            storeRechargeService.storeDeduction(deductionBO);
//            log.info("进行订单退款orderId" + o.getId() + "完成");
//        }

    }


    /**
     * 扣
     *
     * @param storeId
     * @param orderAmount
     * @return
     */
    private boolean updatePayment(Long storeId, BigDecimal orderAmount) {
        StoreSettlement record = new StoreSettlement();
        record.setStoreId(storeId);
        StoreSettlement ss = storeSettlementMapper.selectOne(record);
        int rowNum = 0;
        if (ss.getCollectStatus()) {
            if (ss.getCollectPrice() == null) {
                ss.setCollectPrice(orderAmount.doubleValue());
            } else {
                ss.setCollectPrice((BigDecimal.valueOf(ss.getCollectPrice()).subtract(orderAmount)).doubleValue());
            }
            StoreSettlement newStoreSettlement = new StoreSettlement();
            newStoreSettlement.setId(ss.getId());
            newStoreSettlement.setCollectPrice(ss.getCollectPrice());
            rowNum = storeSettlementMapper.updateByPrimaryKeySelective(newStoreSettlement);
        }
        return rowNum > 0 ? true : false;
    }

    /**
     * 回
     *
     * @param storeId
     * @param orderAmount
     * @return
     */
    private boolean addPaymentForOrder(Long storeId, BigDecimal orderAmount) {
        StoreSettlement record = new StoreSettlement();
        record.setStoreId(storeId);
        StoreSettlement ss = storeSettlementMapper.selectOne(record);
        if (ss.getCollectStatus()) {
            int rowNum = 0;
            if (null == ss.getCollectPrice()) {
                ss.setCollectPrice(0.0);
            }
            ss.setCollectPrice((BigDecimal.valueOf(ss.getCollectPrice()).add(orderAmount)).doubleValue());
            StoreSettlement newStoreSettlement = new StoreSettlement();
            newStoreSettlement.setId(ss.getId());
            newStoreSettlement.setCollectPrice(ss.getCollectPrice());
            rowNum = storeSettlementMapper.updateByPrimaryKeySelective(newStoreSettlement);
            return rowNum > 0 ? true : false;
        } else {
            return true;
        }
    }


    /**
     * 订单扣款
     *
     * @param order
     */
    private void saveOrderDeductions(Order order, Long userId) {
        log.info("进行订单退款 orderId" + order.getId());
        if (null != order && order.getStoreId() != null) {
            StoreSettlement record = new StoreSettlement();
            record.setStoreId(order.getStoreId());
            StoreSettlement ss = storeSettlementMapper.selectOne(record);
            if (null != ss) {
                if (ss.getCollectStatus()) {
                    if (ss.getCollectPrice() != null) {
                        OrderBill ob = new OrderBill();
                        ob.setStoreId(order.getStoreId());
                        ob.setOrderId(order.getId());
                        ob.setArAmount(order.getOrderAmount());
                        ob.setPaAmount(BigDecimal.ZERO);
                        ob.setOrderTime(order.getOrderTime());
                        ob.setStoreBalance(BigDecimal.valueOf(ss.getCollectPrice()));
                        ob.setBillRemark("<--PC扣款: " + order.getOrderCode() + "-->");
                        ob.setCreateTime(new Date());
                        ob.setCrateId(order.getCreateId());
                        orderBillMapper.insert(ob);
                    }
                }
            }
        }
        log.info("进行订单退款orderId" + order.getId() + "完成");
    }

    /**
     * 订单回款
     */
    private void saveOrderReceivable(Order order, Long userId) {
        log.info("进行订单回款 orderId" + order.getId());
        if (order != null && order.getStoreId() != null) {
            StoreSettlement record = new StoreSettlement();
            record.setStoreId(order.getStoreId());
            StoreSettlement ss = storeSettlementMapper.selectOne(record);
            if (null != ss) {
                if (ss.getCollectStatus()) {
                    if (ss.getCollectPrice() != null) {
                        OrderBill ob = new OrderBill();
                        ob.setStoreId(order.getStoreId());
                        ob.setOrderId(order.getId());
                        ob.setArAmount(BigDecimal.ZERO);
                        ob.setPaAmount(order.getOrderAmount());
                        ob.setOrderTime(order.getOrderTime());
                        ob.setBillRemark("<--PC回款:" + order.getOrderCode() + " -->");
                        ob.setStoreBalance(BigDecimal.valueOf(ss.getCollectPrice()));
                        ob.setCreateTime(new Date());
                        ob.setCrateId(userId);
                        orderBillMapper.insert(ob);
                    }
                }
            }
        }
        log.info("进行订单回款orderId" + order.getId() + "完成");
    }


    /**
     * JOB清美订单回款
     */
    @Test
    @Commit
    public void jobQingyun() {
        Example example = new Example(Order.class);
        example.createCriteria().andEqualTo("id", "34637231");
        Order order = orderMapper.selectOneByExample(example);

        jobQingyunNew(order);

//        jobQingyunOld(order);
    }

    private void jobQingyunNew(Order order) {
        Boolean preStore = storeRechargeService.isPreStore(order.getStoreId());
        if (Objects.equals(preStore, Boolean.TRUE)) {
            //回款
            StoreRechargeBO rechargeBO = StoreRechargeBO.builder()
                    .orderCode(order.getOrderCode())
                    .tradeCode(order.getOrderCode())
                    .money(order.getOrderAmount().doubleValue())
                    .storeId(order.getStoreId())
                    .tradeTime(new Date())
                    .receiptDate(new Date())
                    .billType(StoreBillTypeEnums.JOB_ORDER_DEPOSIT.getCode())
                    .remark("<--JOB清美订单回款: " + order.getOrderCode() + "-->")
                    .userId(-1L)
                    .build();
            storeRechargeService.storeRecharge(rechargeBO);
            //扣款
            StoreDeductionBO deductionBO = StoreDeductionBO.builder()
                    .orderCode(order.getOrderCode())
                    .orderId(order.getId())
                    .orderAmount(order.getFinalAmount())
                    .storeId(order.getStoreId())
                    .tradeCode(order.getOrderCode())
                    .tradeTime(new Date())
                    .orderTime(order.getOrderTime())
                    .billType(StoreBillTypeEnums.JOB_ORDER_DEDUCTION.getCode())
                    .remark("<--JOB清美订单扣款: " + order.getOrderCode() + "-->")
                    .userId(-1L)
                    .build();
            storeRechargeService.storeDeduction(deductionBO);
        }
    }

    private void jobQingyunOld(Order order) {
        StoreSettlement storeSettlement = storeSettlementMapper.getStoreSettlementByStoreId(order.getStoreId());
        BigDecimal oldCollectPrice = storeSettlement.getCollectPrice() == null ? null : new BigDecimal(storeSettlement.getCollectPrice());
        //修改客户余额
        Map collectPriceMap = new HashMap();
        collectPriceMap.put("id", storeSettlement.getId());
        collectPriceMap.put("amountPrice", order.getFinalAmount().subtract(order.getOrderAmount()).doubleValue());
        collectPriceMap.put("collectPrice", storeSettlement.getCollectPrice());
        int count = storeSettlementMapper.updateCollectPrice(collectPriceMap);
        QYAssert.isTrue(count > 0, "订单覆盖时，修改预付客户余额异常。");
        //记录回款、扣款账单
        List<OrderBill> billList = this.getStoreOrderBillList(order, oldCollectPrice);
        orderBillMapper.insertList(billList);
    }

    private List<OrderBill> getStoreOrderBillList(Order order, BigDecimal oldCollectPrice) {
        List<OrderBill> billList = new ArrayList<>();
        //回款
        oldCollectPrice = oldCollectPrice == null ? BigDecimal.ZERO : oldCollectPrice;
        OrderBill paOrderBill = new OrderBill();
        paOrderBill.setStoreId(order.getStoreId());
        if (oldCollectPrice != null) {
            paOrderBill.setStoreBalance(oldCollectPrice.add(order.getOrderAmount()));
        }
        paOrderBill.setOrderId(order.getId());
        paOrderBill.setPaAmount(order.getOrderAmount());
        paOrderBill.setBillRemark("<--JOB清美订单回款: " + order.getOrderCode() + "-->");
        paOrderBill.setOrderTime(order.getOrderTime());
        paOrderBill.setCrateId(-1L);
        paOrderBill.setCreateTime(new Date());
        billList.add(paOrderBill);

        //扣款
        OrderBill arOrderBill = new OrderBill();
        arOrderBill.setStoreId(order.getStoreId());
        if (oldCollectPrice != null) {
            arOrderBill.setStoreBalance(oldCollectPrice.add(order.getOrderAmount()).subtract(order.getFinalAmount()));
        }
        arOrderBill.setOrderId(order.getId());
        arOrderBill.setArAmount(order.getFinalAmount());
        arOrderBill.setBillRemark("<--JOB清美订单扣款: " + order.getOrderCode() + "-->");
        arOrderBill.setOrderTime(order.getOrderTime());
        arOrderBill.setCrateId(-1L);
        arOrderBill.setCreateTime(new Date());
        billList.add(arOrderBill);

        return billList;
    }


    /**
     * 提货卡扣款
     */
    @Test
    @Commit
    public void tiHuoKa() {
        Example example = new Example(Order.class);
        example.createCriteria().andEqualTo("id", "76217626");
        Order order = orderMapper.selectOneByExample(example);

        tiHuoKaNew(order);

//        tiHuoKaOld(order);
    }

    private void tiHuoKaNew(Order order) {
        String remark = "<--提货卡扣款:"+order.getOrderCode()+" -->";
        int billType = StoreBillTypeEnums.TIHUOKA_DEDUCTION.getCode();
        //扣款
        StoreDeductionBO deductionBO = StoreDeductionBO.builder()
                .orderCode(order.getOrderCode())
                .orderId(order.getId())
                .orderAmount(order.getOrderAmount())
                .storeId(order.getStoreId())
                .tradeCode(order.getOrderCode())
                .tradeTime(new Date())
                .orderTime(order.getOrderTime())
                .billType(billType)
                .remark(remark)
                .userId(order.getStoreId())
                .build();
        storeRechargeService.storeDeduction(deductionBO);
    }

    private void tiHuoKaOld(Order order) {
        updatePayment(order.getStoreId(), order.getOrderAmount());
        tiHuokaDeduction(order, order.getStoreId());
    }

    /**
     * 订单扣款 对账
     *
     * @param order
     */
    @Transactional(rollbackFor = Exception.class)
    public void tiHuokaDeduction(Order order, Long storeId) {
        if (null != storeId) {
            Example ex = new Example(StoreSettlement.class);
            ex.createCriteria().andEqualTo("storeId", storeId);

            List<StoreSettlement> ssList = this.storeSettlementMapper.selectByExample(ex);
            StoreSettlement ss = null;
            if (SpringUtil.isNotEmpty(ssList)) {
                ss = ssList.get(0);
            }
            if (null != ss) {
                if (ss.getCollectStatus()) {
                    if (ss.getCollectPrice() != null) {
                        OrderBill ob = new OrderBill();
                        ob.setStoreId(order.getStoreId());
                        ob.setOrderId(order.getId());
                        ob.setArAmount(order.getOrderAmount());
                        ob.setPaAmount(BigDecimal.ZERO);
                        ob.setOrderTime(order.getOrderTime());
                        ob.setStoreBalance(BigDecimal.valueOf(ss.getCollectPrice()));
                        ob.setBillRemark("<--提货卡扣款:" + order.getOrderCode() + " -->");
                        ob.setCreateTime(new Date());
                        ob.setCrateId(storeId);
                        this.orderBillMapper.insert(ob);
                    }
                }
            }
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public boolean updatePayment(String storeId, BigDecimal orderAmount) {

        Example ex = new Example(StoreSettlement.class);
        ex.createCriteria().andEqualTo("storeId", storeId);

        List<StoreSettlement> ssList = this.storeSettlementMapper.selectByExample(ex);
        StoreSettlement ss = null;
        if (SpringUtil.isNotEmpty(ssList)) {
            ss = ssList.get(0);
        }
        int rowNum = 0;
        if (ss != null) {
            if (ss.getCollectStatus()) {
                if (ss.getCollectPrice() == null) {
                    ss.setCollectPrice(BigDecimal.ZERO.doubleValue());
                }

                ss.setCollectPrice((BigDecimal.valueOf(ss.getCollectPrice()).subtract(orderAmount)).doubleValue());
                rowNum = storeSettlementMapper.updateByPrimaryKey(ss);
            }
        }
        return rowNum > 0 ? true : false;
    }
}
