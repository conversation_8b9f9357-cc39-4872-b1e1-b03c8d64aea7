@startuml
skinparam backgroundColor white
skinparam handwritten false
skinparam defaultFontName "Microsoft YaHei"
skinparam activity {
  BackgroundColor #f0f8ff
  BorderColor #87ceeb
  FontColor black
  ArrowColor #4682b4
}
skinparam note {
  BackgroundColor #fffacd
  BorderColor #daa520
}
skinparam partition {
  BackgroundColor #e6e6fa
  BorderColor #9370db
}

title **鲜达现付单下单流程**\n

start

:<&user> 客户端请求下单;

partition "**前置校验**" {
  :<&shield> 获取用户信息;

  if (是否游客?) then (是)
    #pink:<&circle-x> 抛出异常: 游客无法下单;
    stop
  endif

  if (是否有未支付订单?) then (是)
    #pink:<&circle-x> 抛出异常: 已有待支付订单;
    stop
  endif
  
  :<&shield> 校验客户状态;
  :<&clipboard> 校验下单数据;
  note right
    **校验项目:**
    <&check> 特价限购
    <&check> 结算条件
    <&check> 订货时间
    <&check> 订单金额
    <&check> 品种数量
    <&check> 商品数量
    <&check> 账户余额
    <&check> 下单次数
  end note
}

partition "**订单创建**" {
  :<&layers> 组装订单商品数据;
  note right: 按配送模式+仓库+供应商拆单
  
  :<&clock> 计算订单截止时间;
    if (是否超过订单截止时间?) then (是)
      #pink:<&circle-x> 抛出异常: 超时;
      stop
    endif
  
  :<&lock-locked> 冻结库存;
  
  if (库存冻结是否成功?) then (否)
    :<&lock-unlocked> 解冻库存;
    :<&action-undo> 返还优惠券;
    #pink:<&circle-x> 返回错误信息;
    stop
  endif

  :<&file> 保存预订单;
  :<&tag> 保存促销方案;
  :<&credit-card> 优惠券核销;
  :<&trash> 清空购物车;

  if (保存预订单是否成功?) then (否)
    #pink:<&circle-x> 返回错误信息;
    stop
  endif

  :<&credit-card> 充值支付;
  note right
    **支付流程:**
    <&check> 1. 校验支付参数
    <&check> 2. 校验充值权限
    <&check> 3. 校验充值金额
    <&check> 4. 生成支付账单
    <&check> 5. 保存支付流水
    <&check> 6. 调用支付接口
  end note

  if (支付是否成功?) then (否)
    :<&lock-unlocked> 解冻库存;
    #pink:<&circle-x> 抛出异常;
    stop
  endif
}

partition "**事务后处理**" {
  fork
    :<&clock> 加入延迟队列;
    note right: 超时自动取消订单
  fork again
    :<&graph> 维护特价限购记录;
  end fork
}

:<&check> 下单完成;

stop

@enduml
