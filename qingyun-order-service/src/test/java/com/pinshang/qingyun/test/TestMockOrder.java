package com.pinshang.qingyun.test;

import com.pinshang.qingyun.base.enums.StoreBillTypeEnums;
import com.pinshang.qingyun.base.util.IdWorker;
import com.pinshang.qingyun.box.utils.DateUtil;
import com.pinshang.qingyun.box.utils.TimeUtil;
import com.pinshang.qingyun.order.bo.StoreRechargeBO;
import com.pinshang.qingyun.order.service.MockOrderService;
import com.pinshang.qingyun.order.service.OrderSaveService;
import com.pinshang.qingyun.order.service.OrderService;
import com.pinshang.qingyun.order.service.StoreRechargeService;
import com.pinshang.qingyun.order.vo.mock.MockOrderItemVo;
import com.pinshang.qingyun.order.vo.mock.MockOrderVo;
import com.pinshang.qingyun.order.vo.order.CreateOrderVo;
import com.pinshang.qingyun.order.vo.order.OrderItemRequestVo;
import com.pinshang.qingyun.order.vo.order.OrderRequestVo;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.annotation.Commit;
import org.springframework.test.annotation.Rollback;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;


public class TestMockOrder extends  AbstractJunitBase {
    @Autowired
    private MockOrderService mockOrderService;
    @Autowired
    private OrderService orderService;
    @Autowired
    private OrderSaveService orderSaveService;
    @Autowired
    private StoreRechargeService storeRechargeService;

    @Test
    @Commit
    public void dxc() {
        List<Thread> threads = new ArrayList<>();
        for (int i = 1; i <= 100; i++) {
            int finalI = i;
            //finalI 当成每次下单金额，循环100次，应扣除5050元
            Thread thread = new Thread(() -> {
//                orderSaveService.saveOneOrder(finalI);
                long orderCode = IdWorker.getId();
                StoreRechargeBO rechargeBO = StoreRechargeBO.builder()
                        .orderCode(String.valueOf(orderCode))
                        .tradeCode(orderCode + "_" + TimeUtil.toString(new Date(), TimeUtil.DATE_FORMAT_LONG))
                        .money((double) finalI)
                        .storeId(999872035591511220L)
                        .tradeTime(new Date())
                        .receiptDate(new Date())
                        .billType(StoreBillTypeEnums.PC_REFUNDMENT.getCode())
                        .remark("<--PC回款:" + orderCode + " -->")
                        .userId(-1L)
                        .build();
                storeRechargeService.storeRecharge(rechargeBO);
            });
            threads.add(thread);
            thread.start();
        }

        // 等待所有线程完成
        for (Thread thread : threads) {
            try {
                thread.join();
            } catch (InterruptedException e) {
                e.printStackTrace();
            }
        }
    }

    /**
     * 创建ToB订单
     */
    @Test
    @Rollback(false)
    public void createOrderToB(){
        //客户类型为超市，已分配不同类型的播种位
        //崇明南门46美食（农工商）：    id=2844607535052400, code=1041177, 播种位类型=1，播种区域=一厂冷库(上海)，  无分组
        //罗阳348（华联）:           id=242852513778404416,code=1012052，播种位类型=2，播种区域=一厂冷库(上海)，  无分组
        //保定62美食（农工商）：       id=6550058901027200，code=1041065， 播种位类型=3，播种区域=一厂冷库(上海)，   分组ID=22
        //华润万家乐购（菊园店）:       id=587120668201600, code=1116071,  播种位类型=4，播种区域=七厂冷库（申城）， 分组ID=22
        List<Long> storeIds = Arrays.asList(2844607535052400L,242852513778404416L,6550058901027200L,587120668201600L);
        MockOrderVo vo = new MockOrderVo();
        vo.setOrderTime(DateUtil.parseDate("2021-05-12","yyyy-MM-dd"));
        vo.setDeliveryBatch(0);
        vo.setStoreIdList(storeIds);
        //选择品上12库，存在拣货位的商品
        //佳洁士外柔内刚牙刷3支装：id=999965182752689980，拣货位区域=日用、日化、运动器材
        //李施德林漱口水天然橙味： id=999965182752690477，拣货位区域=日用、日化、运动器材
        //清风B106CN抽取式卫生纸：id=999965182752691257，拣货位区域=日用、日化、运动器材
        //上好佳原味薯条：        id=999965182752692764，拣货位区域=方便面、休闲、进口商品
        //清美小黄豆：            id=999965182752700052，拣货位区域=南北干货
        //List<Long> commodityIds = Arrays.asList(999965182752689980L,999965182752690477L,999965182752691257L,999965182752692764L);
        List<Long> commodityIds = Arrays.asList(999965182752689980L,999965182752692764L);
        List<MockOrderItemVo> itemList = new ArrayList<>();
        MockOrderItemVo itemVo = new MockOrderItemVo();
        itemVo.setCommodityId(999965182752689980L);
        itemVo.setQuantity(BigDecimal.valueOf(8));
        itemList.add(itemVo);
//        MockOrderItemVo itemVo = new MockOrderItemVo();
//        itemVo.setCommodityId(999965182752689980L);
//        itemVo.setQuantity(BigDecimal.valueOf(8));
//        itemList.add(itemVo);
//
//        MockOrderItemVo itemVo1 = new MockOrderItemVo();
//        itemVo1.setCommodityId(999965182752690477L);
//        itemVo1.setQuantity(BigDecimal.valueOf(7));
//        itemList.add(itemVo1);
//
//        MockOrderItemVo itemVo6 = new MockOrderItemVo();
//        itemVo6.setCommodityId(999965182752690477L);
//        itemVo6.setQuantity(BigDecimal.valueOf(4));
//        itemList.add(itemVo6);
//
//        MockOrderItemVo itemVo2 = new MockOrderItemVo();
//        itemVo2.setCommodityId(999965182752692764L);
//        itemVo2.setQuantity(BigDecimal.valueOf(6));
//        itemList.add(itemVo2);
//
//        MockOrderItemVo itemVo3 = new MockOrderItemVo();
//        itemVo3.setCommodityId(999965182752692764L);
//        itemVo3.setQuantity(BigDecimal.valueOf(4));
//        itemList.add(itemVo2);
//
//        MockOrderItemVo itemVo4 = new MockOrderItemVo();
//        itemVo4.setCommodityId(999965182752689980L);
//        itemVo4.setQuantity(BigDecimal.valueOf(4));
//        itemList.add(itemVo4);
//
//        MockOrderItemVo itemVo5 = new MockOrderItemVo();
//        itemVo5.setCommodityId(999965182752700052L);
//        itemVo5.setQuantity(BigDecimal.valueOf(5));
//        itemList.add(itemVo5);

        vo.setItemList(itemList);
        mockOrderService.mockCreateOrder(vo);
    }

    @Test
    @Rollback(false)
    public void TestMockOrder(){
        MockOrderVo vo = new MockOrderVo();
        vo.setOrderTime(DateUtil.getNowDate());
        vo.setDeliveryBatch(0);
        //vo.setStoreIdList(Arrays.asList(999872035591509085L,1042841496430500L,1498435664785800L));
        vo.setStoreIdList(Arrays.asList(999872035591509085L,999872035591509440L,999872035591509444L));
        List<MockOrderItemVo> itemList = new ArrayList<>();
        for(int i=0;i<5;i++) {
            MockOrderItemVo itemVo = new MockOrderItemVo();
            itemVo.setCommodityId((999965182752701771L)+Long.valueOf(i));
            itemVo.setQuantity(BigDecimal.valueOf(5));
            itemList.add(itemVo);
        }
        vo.setItemList(itemList);
        mockOrderService.mockCreateOrder(vo);
    }

    /**
     * 创建ToB订单
     */
    @Test
    @Rollback(false)
    public void createOrderToB1(){
        //客户类型为超市，已分配不同类型的播种位
        //崇明南门46美食（农工商）：    id=2844607535052400, code=1041177, 播种位类型=1，播种区域=一厂冷库(上海)，  无分组
        //罗阳348（华联）:           id=242852513778404416,code=1012052，播种位类型=2，播种区域=一厂冷库(上海)，  无分组
        //保定62美食（农工商）：       id=6550058901027200，code=1041065， 播种位类型=3，播种区域=一厂冷库(上海)，   分组ID=22
        //华润万家乐购（菊园店）:       id=587120668201600, code=1116071,  播种位类型=4，播种区域=七厂冷库（申城）， 分组ID=22
        //嘉定108(欧尚专柜)：          id=1885868660353600, code=1163001, 播种区域=一厂冷库(上海)
        //加盟类型的客户： id=999872035591509085,code=6800101
        List<Long> storeIds = Arrays.asList(2844607535052400L,242852513778404416L,6550058901027200L,587120668201600L,1885868660353600L,999872035591509085L);
//        List<Long> storeIds = Arrays.asList(242852513778404416L,999872035591509085L);
        //10005
//        List<Long> commodityIds = Arrays.asList(999965182752689980L,999965182752692764L,999965182752698698L,999965182752698699L,
//                999965182752765971L,999965182752765961L,999965182752699573L);
        //10005, 含不同类型的商品
        List<Long> commodityIds = Arrays.asList(
                999965182752706836L,999965182752706838L,999965182752712013L,
                999965182752712005L,999965182752712002L,999965182752712008L
        );
        Random rand = new Random();
        for(int i=0;i<storeIds.size();i++){
            MockOrderVo vo = new MockOrderVo();
            vo.setOrderTime(DateUtil.parseDate("2021-07-07","yyyy-MM-dd"));
            vo.setDeliveryBatch(0);
            vo.setStoreIdList(Collections.singletonList(storeIds.get(i)));

            List<MockOrderItemVo> itemList = new ArrayList<>();
            commodityIds.forEach(cId->{
                MockOrderItemVo itemVo = new MockOrderItemVo();
                itemVo.setCommodityId(cId);
                itemVo.setQuantity(BigDecimal.valueOf(rand.nextInt(20) + 20 ));
                itemList.add(itemVo);
            });
            MockOrderItemVo itemVo = new MockOrderItemVo();
            itemVo.setCommodityId(999965182752689980L);
            itemVo.setQuantity(BigDecimal.valueOf(rand.nextInt(10) + 10 ));
            itemList.add(itemVo);

            MockOrderItemVo itemVo1 = new MockOrderItemVo();
            itemVo1.setCommodityId(999965182752692764L);
            itemVo1.setQuantity(BigDecimal.valueOf(rand.nextInt(8) + 8 ));
            itemList.add(itemVo1);

            vo.setItemList(itemList);
            mockOrderService.mockCreateOrder(vo);

        }

    }


    @Test
    @Rollback(false)
    public void createOrderForSeed(){
        List<Long> storeIds = Arrays.asList(
                620957946566100L,
                587120668201600L,
                920991438398200L,
                999872035591611626L

        );
        List<Long> commodityIds = Arrays.asList(
                56095697583367000L,
                447452911264559232L,
                292986401891937024L
        );

        List<Long> storeIds1 = Arrays.asList(
                587120668201600L,
                999872035591611717L,
                999872035591611549L,
                999872035591611556L
        );
        List<Long> commodityIds1 = Arrays.asList(
                999965182752680979L,
                999965182752680986L
        );

        Random rand = new Random();
        for(int i=0;i<storeIds.size();i++){
            MockOrderVo vo = new MockOrderVo();
            vo.setOrderTime(DateUtil.parseDate("2021-07-01","yyyy-MM-dd"));
            vo.setDeliveryBatch(0);
            vo.setStoreIdList(Collections.singletonList(storeIds.get(i)));

            List<MockOrderItemVo> itemList = new ArrayList<>();
            commodityIds.forEach(cId->{
                MockOrderItemVo itemVo = new MockOrderItemVo();
                itemVo.setCommodityId(cId);
                itemVo.setQuantity(BigDecimal.valueOf(rand.nextInt(3) + 5 ));
                itemList.add(itemVo);
            });
            vo.setItemList(itemList);
            mockOrderService.mockCreateOrder(vo);

        }

        for(int i=0;i<storeIds1.size();i++){
            MockOrderVo vo = new MockOrderVo();
            vo.setOrderTime(DateUtil.parseDate("2021-07-01","yyyy-MM-dd"));
            vo.setDeliveryBatch(0);
            vo.setStoreIdList(Collections.singletonList(storeIds.get(i)));

            List<MockOrderItemVo> itemList = new ArrayList<>();
            commodityIds1.forEach(cId->{
                MockOrderItemVo itemVo = new MockOrderItemVo();
                itemVo.setCommodityId(cId);
                itemVo.setQuantity(BigDecimal.valueOf(rand.nextInt(3) + 5 ));
                itemList.add(itemVo);
            });
            vo.setItemList(itemList);
            mockOrderService.mockCreateOrder(vo);
        }

    }

    @Test
    @Rollback(false)
    public void createOrderForDiffWarehuoseDev(){
        List<Long> storeIds = Arrays.asList(2844607535052400L,242852513778404416L,6550058901027200L,587120668201600L,1885868660353600L,999872035591509085L);
//        List<Long> storeIds = Arrays.asList(242852513778404416L,999872035591509085L);
        //10005
//        List<Long> commodityIds = Arrays.asList(999965182752689980L,999965182752692764L,999965182752698698L,999965182752698699L,
//                999965182752765971L,999965182752765961L,999965182752699573L);
        //10005, 含不同类型的商品
        List<Long> commodityIds1 = Arrays.asList(
                999965182752706836L,999965182752706838L,999965182752712013L,
                999965182752712005L,999965182752712002L,999965182752712008L
        );
        List<Long> commodityIds2 = Arrays.asList(
                999965182752695788L,999965182752748151L,999965182752763581L
        );
        List<Long> commodityIds3 = Arrays.asList(
                999965182752699962L,
                999965182752699961L,
                8107187447387601L,
                10758910621399202L
        );
        Random rand = new Random();
        for(int i=0;i<storeIds.size();i++){
            MockOrderVo vo = new MockOrderVo();
            vo.setOrderTime(DateUtil.parseDate("2021-07-08","yyyy-MM-dd"));
            vo.setDeliveryBatch(0);
            vo.setStoreIdList(Collections.singletonList(storeIds.get(i)));

            List<MockOrderItemVo> itemList = new ArrayList<>();
            commodityIds1.forEach(cId->{
                MockOrderItemVo itemVo = new MockOrderItemVo();
                itemVo.setCommodityId(cId);
                itemVo.setQuantity(BigDecimal.valueOf(rand.nextInt(5) + 3 ));
                itemList.add(itemVo);
            });
            commodityIds2.forEach(cId->{
                MockOrderItemVo itemVo = new MockOrderItemVo();
                itemVo.setCommodityId(cId);
                itemVo.setQuantity(BigDecimal.valueOf(rand.nextInt(5) + 3 ));
                itemList.add(itemVo);
            });
            commodityIds3.forEach(cId->{
                MockOrderItemVo itemVo = new MockOrderItemVo();
                itemVo.setCommodityId(cId);
                itemVo.setQuantity(BigDecimal.valueOf(rand.nextInt(5) + 3 ));
                itemList.add(itemVo);
            });
            vo.setItemList(itemList);
            mockOrderService.mockCreateOrder(vo);
        }

    }

    @Test
    @Rollback(false)
    public void createOrderForDiffWarehuoseTest(){
        List<Long> storeIds = Arrays.asList(999872035591611750L,999872035591509085L);
        List<Long> commodityIds1 = Arrays.asList(
                468788174651305600L,116176507808327216L, //标品
                413791180690901056L,345139349439155712L // 称重包装品
        );
        for(int i=0;i<storeIds.size();i++){
            MockOrderVo vo = new MockOrderVo();
            vo.setOrderTime(DateUtil.parseDate("2021-07-11","yyyy-MM-dd"));
            vo.setDeliveryBatch(0);
            vo.setStoreIdList(Collections.singletonList(storeIds.get(i)));

            List<MockOrderItemVo> itemList = new ArrayList<>();
            commodityIds1.forEach(cId->{
                MockOrderItemVo itemVo = new MockOrderItemVo();
                itemVo.setCommodityId(cId);
                itemVo.setQuantity(BigDecimal.valueOf(1));
                itemList.add(itemVo);
            });
            MockOrderItemVo itemVo = new MockOrderItemVo();
            itemVo.setCommodityId(413791180690901056L);
            itemVo.setQuantity(BigDecimal.valueOf(1));
            itemList.add(itemVo);
            MockOrderItemVo itemVo1 = new MockOrderItemVo();
            itemVo1.setCommodityId(345139349439155712L);
            itemVo1.setQuantity(BigDecimal.valueOf(1));
            itemList.add(itemVo1);
            vo.setItemList(itemList);
            mockOrderService.mockCreateOrder(vo);
        }

    }


    @Test
    @Rollback(false)
    public void createOrder() {
        ThreadPoolExecutor executor = new ThreadPoolExecutor(
                10,15,10, TimeUnit.SECONDS
                ,new ArrayBlockingQueue(8,true),new ThreadPoolExecutor.CallerRunsPolicy()
        );
        for(int k=0;k<20;k++){
            executor.execute(()->{
                CreateOrderVo vo1 = new CreateOrderVo();
                vo1.setStoreId(999872035591509085L);
                vo1.setEnterpriseId(78L);
                vo1.setCreateId(1101L);
                vo1.setCreateName("测试");
                vo1.setInternal(false);
                vo1.setOrderTime("2024-01-03");
                vo1.setDeliveryBatch("1");
                List<CreateOrderVo.CreateOrderItemIDTO> itemList1 = new ArrayList<>();
                List<Long> commodityIds1 = Arrays.asList(
                        116176507808327216L, //标品
                        413791180690901056L,345139349439155712L // 称重包装品
                );
                commodityIds1.forEach(cId->{
                    CreateOrderVo.CreateOrderItemIDTO itemVo = new CreateOrderVo.CreateOrderItemIDTO();
                    itemVo.setCommodityId(cId);
                    itemVo.setQuantity(BigDecimal.valueOf(3));
                    itemList1.add(itemVo);
                });
                vo1.setItems(itemList1);

                OrderRequestVo orderRequestVo = new OrderRequestVo();
                orderRequestVo.setStoreId("999872035591509085");
                orderRequestVo.setOrderTime("2024-01-03");
                orderRequestVo.setEnterpriseId(78L);
                orderRequestVo.setUserId(1101L);
                orderRequestVo.setCreateName("测试");
                orderRequestVo.setLogisticsModel(2);
                orderRequestVo.setSupplierId(161L);
                orderRequestVo.setWarehouseId(10000L);
                orderRequestVo.setDeliveryBatch("1");
                orderRequestVo.setDeleveryTimeRange("0-1");
                orderRequestVo.setConsignmentId(-1L);

                List<OrderItemRequestVo> itemsList = new ArrayList<OrderItemRequestVo>();
                commodityIds1.forEach(i -> {
                    OrderItemRequestVo item = new OrderItemRequestVo();
                    item.setProductId(i.toString());
                    item.setProductNum(BigDecimal.valueOf((int)(1+Math.random()*10)));
                    itemsList.add(item);
                });
                orderRequestVo.setItemsList(itemsList);
                orderService.createOrder(orderRequestVo, vo1);
                System.out.println("=========="+(1+1));
            });
        }
    }

}
