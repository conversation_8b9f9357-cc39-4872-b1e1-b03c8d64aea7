package com.pinshang.qingyun.test;

import com.alibaba.fastjson.JSONObject;
import com.pinshang.qingyun.ApplicationOrderService;
import lombok.extern.slf4j.Slf4j;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.RequestBuilder;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import org.springframework.web.context.WebApplicationContext;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;

/**
 * 
 * <AUTHOR>
 * @Date 2018/4/8 15:17
 */
@Slf4j
@RunWith(SpringJUnit4ClassRunner.class)
@SpringBootTest(classes = ApplicationOrderService.class,properties = {"spring.profiles.active=dev","application.name.switch=jxl-"})
public class TestControllerApi {
    @Autowired
    private WebApplicationContext wac;
    private MockMvc mvc;

    private final String APPLICATION2JSON="application/json";

    @Before
    public void setUp() throws Exception {
        mvc = MockMvcBuilders.webAppContextSetup(wac).build();//项目拦截器有效，表示加载整个Web
    }

    @Test
    public void testApi() throws Exception {
        RequestBuilder request = null;

        JSONObject jsonObject = new JSONObject();
        jsonObject.put("employeeId","712413876643181824");
        jsonObject.put("orderTime","2017-05-05");
        request = post("/order/outstanding").contentType(APPLICATION2JSON)
                    .content(jsonObject.toJSONString());
        log.error("1.测试查询未订货客户:"+mvc.perform(request).andReturn()
                    .getResponse().getContentAsString());
//
//        jsonObject.clear();
//        jsonObject.put("employeeCode","000118");
//        jsonObject.put("storeCode","");
//        request = post("/storeAccount/list").contentType(APPLICATION2JSON).content(jsonObject.toJSONString());
//        log.error("2.测试查询客户账号管理列表:"+mvc.perform(request).andReturn().getResponse().getContentAsString());
//
//
//        jsonObject.clear();
//        jsonObject.put("orderDate","2017-05-05");
//        jsonObject.put("directorCode","000713");
//        jsonObject.put("deliveryTime","21:08");
//        jsonObject.put("lineGroupId","9131078242860601282");
//        jsonObject.put("warehouseId","9131078242860601286");
//        request = post("/shipments/list").contentType(APPLICATION2JSON).content(jsonObject.toJSONString());
//        log.error("3.测试生产组发货单列表:"+mvc.perform(request).andReturn().getResponse().getContentAsString());

//        request = post("/order/subOrder4distribution/15").content(APPLICATION2JSON).content("");
//        log.error("4.测试配送完成时,子订单数据准备查询:"+mvc.perform(request).andReturn().getResponse().getContentAsString());

    }



}
