package com.pinshang.qingyun.test;

import com.pinshang.qingyun.order.service.OrderChangePriceService;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.annotation.Rollback;

/**
 * Created by weican on 2018-05-28.
 */
public class OrderListenerTest extends  AbstractJunitBase {
    @Autowired
    private OrderChangePriceService orderChangePriceService;


    @Test
    @Rollback(false)
    public void testUpdateOrderPrices(){
        // 接收到订单改价消息：{"wrapper":"kafka","id":1863376835,"data":{"productPriceModelId":2013101615150000001,"userId":64861},"topic":"md-test-update_order_price_key","type":"UPDATE_ORDER_PRICE_KEY","optionType":"UPDATE","mqServiceType":1,"uuid":"2a7199d5-be60-48b4-a098-deb9705a5d63","keyId":"0","consumerFailHandleType":0,"remark":"{\"producerIP\":\"*************\",\"consumeFailHandleType\":0,\"idempotency\":0}","createTime":1733974162603,"idempotency":0,"produceIP":"*************:9033","needPersistDb":true}"}
        orderChangePriceService.updateOrderPrices(2013101615150000001L,64861L);
    }


}
