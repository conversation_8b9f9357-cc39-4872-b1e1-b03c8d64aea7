package com.pinshang.qingyun.test;

import com.pinshang.qingyun.base.enums.XSPayBillStatusEnums;
import com.pinshang.qingyun.order.mapper.XdaPayBillMapper;
import com.pinshang.qingyun.order.model.recharge.XDAPayBill;
import com.pinshang.qingyun.order.service.orderSync.IOrderSyncToDcService;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.annotation.Rollback;
import tk.mybatis.mapper.entity.Example;

import java.util.*;


public class OrderSyncToDcServiceTest extends AbstractJunitBase {

    @Autowired
    private IOrderSyncToDcService iOrderSyncToDcService;
    @Autowired
    private XdaPayBillMapper xdaPayBillMapper;

    @Test
    @Rollback(false)
    public void queryOrderListBySubOrderIds() {
        List<Long> subOrderIds = new ArrayList<>();
        subOrderIds.add(50490959L);
        subOrderIds.add(50490960L);
        subOrderIds.add(50490962L);
        System.out.println("=============list="+iOrderSyncToDcService.queryOrderListBySubOrderIds(subOrderIds));
    }
    @Test
    public void test(){
        Example ex = new Example(XDAPayBill.class);
        ex.createCriteria().andEqualTo("billCode", "123").andEqualTo("billStatus", XSPayBillStatusEnums.PAY_FINISHED.getCode());
        List<XDAPayBill> entries = xdaPayBillMapper.selectByExample(ex);
    }

}
