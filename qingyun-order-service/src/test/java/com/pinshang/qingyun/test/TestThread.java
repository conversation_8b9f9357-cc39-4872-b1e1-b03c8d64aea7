package com.pinshang.qingyun.test;

import java.util.ArrayList;
import java.util.List;

/**
 * Created by weican on 2018-05-30.
 */
public class TestThread  implements  Runnable{
    public static void main(String[] args) {
        TestThread myThread  = new TestThread();
//        Thread thread = new Thread(myThread);
        for(int i=0;i<1000 ;i++){
            Thread thread = new Thread(myThread);
            thread.start();
        }
    }
    @Override
    public void run() {
        List<Integer> list =  deliveryTimeList();
        list.forEach(i->{
            main(i);
        });
    }

    public synchronized List<Integer> deliveryTimeList(){
        List<Integer> list = new ArrayList<>() ;
        list.add(1);
        //list.add(2);
       // list.add(3);
        return list;
    }
    public  synchronized void main(int i  ){
        sub1(i);
        sub2(i);
    }
    public void sub1(int i ){
        i = i+10;
        System.out.println( " sub 1 *** "+i );
    }
    public void sub2(int i ){
        i = i+20;
        try {
            Thread.sleep(100);
        } catch (InterruptedException e) {
            e.printStackTrace();
        }
        System.out.println( "  -----------  sub 2 *** "+i );
    }
}
