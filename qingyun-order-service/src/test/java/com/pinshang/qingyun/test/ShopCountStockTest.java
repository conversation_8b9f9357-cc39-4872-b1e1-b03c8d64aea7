package com.pinshang.qingyun.test;

import com.github.pagehelper.PageInfo;
import com.pinshang.qingyun.base.api.TokenInfo;
import com.pinshang.qingyun.base.util.FastThreadLocalUtil;
import com.pinshang.qingyun.order.dto.ShopCountStockDetailIDTO;
import com.pinshang.qingyun.order.dto.ShopCountStockDetailODTO;
import com.pinshang.qingyun.order.dto.ShopCountStockPageIDTO;
import com.pinshang.qingyun.order.dto.ShopCountStockPageODTO;
import com.pinshang.qingyun.order.service.ShopCountStockService;
import com.pinshang.qingyun.shop.dto.ShopDto;
import com.pinshang.qingyun.shop.service.ShopClient;
import com.pinshang.qingyun.smm.dto.user.SelectUserShopIdListIDTO;
import com.pinshang.qingyun.smm.service.SMMUserClient;
import org.junit.Test;
import org.junit.jupiter.api.MethodOrderer;
import org.junit.jupiter.api.TestMethodOrder;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static reactor.core.publisher.Mono.when;

/**
 * @ClassName ShopCountStockTest
 * <AUTHOR>
 * @Date 2022/12/13 15:33
 * @Description ShopCountStockTest
 * @Version 1.0
 */
@TestMethodOrder(MethodOrderer.MethodName.class)
public class ShopCountStockTest extends AbstractJunitBase{
    @Autowired
    ShopCountStockService shopCountStockService;
    @MockBean
    SMMUserClient smmUserClient;
    @MockBean
    ShopClient shopClient;

    @Test
    public void pageTest(){
        ShopCountStockPageIDTO idto = new ShopCountStockPageIDTO();
        idto.setBeginTime("2022-12-15");
        idto.setEndTime("2022-12-15");
        idto.setPageNo(1);
        idto.setPageSize(20);
        TokenInfo tokenInfo = new TokenInfo();
        tokenInfo.setShopId(16L);
        tokenInfo.setUserId(18203L);
        FastThreadLocalUtil.setQY(tokenInfo);
        Mockito.when(smmUserClient.selectUserShopIdList(any(SelectUserShopIdListIDTO.class))).thenReturn(this.selectUserShopIdList());
        Mockito.when(shopClient.selectShopListByParentOrgCode(anyString())).thenReturn(this.getShopDtos());
        PageInfo<ShopCountStockPageODTO> pageInfo = shopCountStockService.page(idto);
        System.out.println(pageInfo.getList().toString());
    }

    @Test
    public void detailTest(){
        ShopCountStockDetailIDTO idto = new ShopCountStockDetailIDTO();
        idto.setOrderTime("2022-12-16");
        idto.setBarCode("692712822587777");
        idto.setPageNo(1);
        idto.setPageSize(20);
        idto.setShopId(16L);
        ShopCountStockDetailODTO pageInfo = shopCountStockService.detail(idto);
        System.out.println(pageInfo.getItemList().toString());
    }

    private List<ShopDto> getShopDtos(){
        List<ShopDto> shopDtosList = new ArrayList<>();
        ShopDto shopDto = new ShopDto();
        shopDto.setId(16L);
        shopDtosList.add(shopDto);
        return shopDtosList;
    }

    private List<Long> selectUserShopIdList(){
        return Collections.singletonList(16L);
    }
}
