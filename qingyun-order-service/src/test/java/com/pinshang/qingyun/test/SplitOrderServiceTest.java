package com.pinshang.qingyun.test;

import com.pinshang.qingyun.kafka.enums.KafkaMessageOperationTypeEnum;
import com.pinshang.qingyun.order.service.SplitOrderService;
import com.pinshang.qingyun.order.service.WeChatSendMessageService;
import com.pinshang.qingyun.order.vo.splitOrder.SplitOrderKafkaVo;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.annotation.Rollback;

/**
 * <AUTHOR>
 * @date 2018/11/3 15:30.
 * @blog http://linuxsogood.org
 */
public class SplitOrderServiceTest extends AbstractJunitBase {

    @Autowired
    SplitOrderService splitOrderService;
    @Autowired
    WeChatSendMessageService weChatSendMessageService;
    @Test
    @Rollback(false)
    public void batchUpdateDeliveryQuantityV2Test() throws InterruptedException {
       /* SplitOrderKafkaVo vo = new SplitOrderKafkaVo();
        vo.setOrderId(34636886l);
        vo.setEnterpriseId(78l);
        vo.setCreateId(100002l);
        vo.setType(KafkaMessageOperationTypeEnum.INSERT);
        splitOrderService.execute(vo, splitOrderService, weChatSendMessageService);

        Thread.sleep(600000);*/

    }



}
