package com.pinshang.qingyun.test;

import com.github.pagehelper.PageInfo;
import com.pinshang.qingyun.base.enums.DeliveryOrderTypeEnums;
import com.pinshang.qingyun.base.enums.IogisticsModelEnums;
import com.pinshang.qingyun.base.enums.xda.TdaOrderProcessStatusEnum;
import com.pinshang.qingyun.box.utils.DateUtil;
import com.pinshang.qingyun.order.controller.SubOrderController;
import com.pinshang.qingyun.order.dto.xda.tda.LogisticsCommodityItem;
import com.pinshang.qingyun.order.dto.xda.tda.LogisticsDeliveryMessage;
import com.pinshang.qingyun.order.mapper.entry.order.SubOrderListEntry;
import com.pinshang.qingyun.order.mapper.entry.splitOrder.SplitOrderListEntry;
import com.pinshang.qingyun.order.model.order.DeliveryTime;
import com.pinshang.qingyun.order.model.order.Order;
import com.pinshang.qingyun.order.service.DictionaryService;
import com.pinshang.qingyun.order.service.OrderAsyncKafkaService;
import com.pinshang.qingyun.order.service.SubOrderService;
import com.pinshang.qingyun.order.service.syncOrderJob.SubOrder2DeliveryOrderService;
import com.pinshang.qingyun.order.vo.order.PickSubOrderItemRespVo;
import com.pinshang.qingyun.order.vo.order.PickSubOrderVo;
import com.pinshang.qingyun.order.vo.order.SubOrderSearchVo;
import com.pinshang.qingyun.order.vo.order.SubOrderVo;
import com.pinshang.qingyun.storage.dto.CommodityDefaultDcODto;
import com.pinshang.qingyun.storage.service.CommodityWarehouseClient;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.annotation.Rollback;

import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2018/11/3 15:30.
 * @blog http://linuxsogood.org
 */
public class SubOrderServiceTest extends AbstractJunitBase {

    @Autowired
    private SubOrderService subOrderService;
    @Autowired
    private CommodityWarehouseClient commodityWarehouseClient;
    @Autowired
    private SubOrder2DeliveryOrderService subOrder2DeliveryOrderService;
    @Autowired
    private DictionaryService dictionaryService;
    @Autowired
    private SubOrderController subOrderController;
    @Autowired
    private OrderAsyncKafkaService orderAsyncKafkaService;

    @Test
    @Rollback(false)
    public void batchUpdateDeliveryQuantityV2Test() {
//        List<PickSubOrderVo> params = new ArrayList<>();
//        PickSubOrderVo subOrderVo = new PickSubOrderVo();
//        subOrderVo.setSubOrderId(4205019L);
//        List<PickSubOrderItemRespVo> list = new ArrayList<>();
//        PickSubOrderItemRespVo vo = new PickSubOrderItemRespVo();
//        vo.setCommodityId(800929389372736000L);
//        vo.setRealReceiveQuantity(BigDecimal.valueOf(5));
//        vo.setRealDeliveryQuantity(BigDecimal.valueOf(5));
//        list.add(vo);
//
//        PickSubOrderItemRespVo vo1 = new PickSubOrderItemRespVo();
//        vo1.setCommodityId(839032304990497920L);
//        vo1.setRealReceiveQuantity(BigDecimal.valueOf(6));
//        vo1.setRealDeliveryQuantity(BigDecimal.valueOf(6));
//        list.add(vo1);
//
//        PickSubOrderItemRespVo vo2 = new PickSubOrderItemRespVo();
//        vo2.setCommodityId(800929389372736000L);
//        vo2.setRealReceiveQuantity(BigDecimal.valueOf(1));
//        vo2.setRealDeliveryQuantity(BigDecimal.valueOf(1));
//        list.add(vo2);
//
//        subOrderVo.setItemList(list);
//        params.add(subOrderVo);
//        subOrderService.batchUpdateDeliveryQuantityV2(params);

        List<PickSubOrderVo> params = new ArrayList<>();
        PickSubOrderVo subOrderVo = new PickSubOrderVo();
        subOrderVo.setSubOrderId(50494146L);
        subOrderVo.setStoreId(999872035591660701L);
        List<PickSubOrderItemRespVo> list = new ArrayList<>();
        PickSubOrderItemRespVo vo = new PickSubOrderItemRespVo();
        vo.setCommodityId(24684610518094804L);
        vo.setRealReceiveQuantity(BigDecimal.valueOf(23));
        vo.setRealDeliveryQuantity(BigDecimal.valueOf(23));
        list.add(vo);

        subOrderVo.setItemList(list);
        params.add(subOrderVo);
//        subOrderService.batchUpdateDeliveryQuantityV2(params);
        subOrderService.batchUpdateOrderRealQuantity(params);
    }

    @Test
    @Rollback(false)
    public void splitOrderTest() {

        Long[] cmdIds = {
                999965182752692312L,999965182752692313L,999965182752692314L,999965182752692315L,
                999965182752692746L,999965182752692747L,999965182752692886L,999965182752692887L,
                999965182752708282L,999965182752708283L,999965182752708285L,999965182752708347L,
                999965182752709402L,999965182752748021L,999965182752788861L,999965182752747591L,
                999965182752777901L,999965182752810511L,999965182752780061L,999965182752701907L,
                999965182752791131L,999965182752772201L,204324266854513024L,999965182752822311L
        };
        List<SplitOrderListEntry> list = new ArrayList<>();
        for(int i=0;i< cmdIds.length;i++){
            Long commodityId = Arrays.asList(cmdIds).get(i);
            Integer model = i<8?0:(i<16?1:2);
            SplitOrderListEntry entry = new SplitOrderListEntry();
            entry.setCommodityId(commodityId);
            entry.setLogisticsModel(model);
            entry.setQuantity(BigDecimal.valueOf(i));
            entry.setPrice(BigDecimal.valueOf(10));
            entry.setTotalPrice(BigDecimal.valueOf(10*i));
            list.add(entry);
        }
        //Map<物流模式,<(配送|直通)仓库|(直送)供应商|, List<vo>> map;
        Map<Integer, Map<Long, List<SplitOrderListEntry>>> map = new HashMap<Integer, Map<Long, List<SplitOrderListEntry>>>();
        //查商品的默认仓库和供应商
        List<Long> commodityIds = list.stream().mapToLong(SplitOrderListEntry::getCommodityId).boxed().distinct().collect(Collectors.toList());
        Map<Long, CommodityDefaultDcODto> commodityDefaultMap = commodityWarehouseClient.queryCommodityDefaultWarehouseAndSupplier(commodityIds);
        if(commodityDefaultMap.isEmpty()){
            return;
        }
        for (SplitOrderListEntry item : list) {
            //设置商品的默认仓库和默认供应商
            CommodityDefaultDcODto commodityDefaultODTO = commodityDefaultMap.get(item.getCommodityId());
            if(commodityDefaultODTO!=null){
                item.setWarehouseId(commodityDefaultODTO.getWarehouseId());
                item.setSupplierId(commodityDefaultODTO.getSupplierId());
            }
            Integer logicModel = item.getLogisticsModel();
            //进大仓的物流模式
            Boolean isDcModel = IogisticsModelEnums.isDcLogisticsModel(logicModel);
            if (isDcModel && null == item.getWarehouseId()) {
                continue;
            }
            if (!isDcModel && null == item.getSupplierId()) {
                continue;
            }
            Map<Long, List<SplitOrderListEntry>> childMap = map.get(logicModel);
            Long childMapKey = isDcModel?item.getWarehouseId():item.getSupplierId();
            if(childMap==null || childMap.isEmpty() ){
                childMap = new HashMap<>();
                List<SplitOrderListEntry> subList = new ArrayList<>();
                subList.add(item);
                childMap.put(childMapKey, subList);
            }else{
                List<SplitOrderListEntry> subList = childMap.get(childMapKey);
                if(CollectionUtils.isEmpty(subList)){
                    subList = new ArrayList<>();
                }
                subList.add(item);
                childMap.put(childMapKey, subList);
            }
            map.put(item.getLogisticsModel(), childMap);
        }
//        insertSubOrder(order,map,subOrderStatus);
    }

    @Test
    //@Rollback(false)
    public void queryDirectDcConfig() {
        System.out.println("=========="+dictionaryService.queryDirectDcConfig());
    }


    @Test
    //@Rollback(false)
    public void subOrder2DeliveryOrder() {
        DeliveryTime deliveryTime = new DeliveryTime();
        deliveryTime.setBeginTime("00:00");
        deliveryTime.setEndTime("19:00");
        deliveryTime.setLineGroupId("9131078242860602341");
        deliveryTime.setCoverFlag(true);
        subOrder2DeliveryOrderService.subOrder2DeliveryOrder(deliveryTime, DeliveryOrderTypeEnums.SALE.getCode());
    }
    @Test
    public void queryUnDeliverySubOrderList(){
        SubOrderVo vo = new  SubOrderVo();
        vo.setOrderTime( DateUtil.getDate("2023","4","26") );
        vo.setWarehouseId(10003L);
        vo.setBusinessType(1);
        PageInfo<SubOrderListEntry> list =subOrderService.queryUnDeliverySubOrderListV2(vo);
        System.out.println(list.getList());
    }

    @Test
    @Rollback(false)
    public void creatDeliveryOrder() {
        SubOrderSearchVo vo = new SubOrderSearchVo();
        vo.setOrderIds(Arrays.asList(34633187L, 34633188L, 34633190L, 34633191L, 34633192L, 34633193L));
        System.out.println("==========" + subOrderController.creatDeliveryOrder(vo));
    }

    @Test
    @Rollback(false)
    public void testTopic() {
        Order order = new Order();
        order.setId(347584L);
        orderAsyncKafkaService.sendKafkaAsyncOrderToDCMessage(order);
    }

    @Test
    @Rollback(false)
    public void testUpdateTdaDeliveryQuantity(){
        LogisticsDeliveryMessage logisticsMessage = new LogisticsDeliveryMessage();
        logisticsMessage.setWaybillCode("111");
        logisticsMessage.setDriverId(-1L);
        logisticsMessage.setDeliveryDate(new Date());
        logisticsMessage.setStoreTypeId(1L);
        logisticsMessage.setStoreId(752908786325622016L);
        logisticsMessage.setPhone("13000000001");
        logisticsMessage.setLogisticsCenterId(1L);
        logisticsMessage.setDeliveryAddress("上海市秋月路矽岸国际1号楼616");
        logisticsMessage.setOrderCode("1717138010297482728");
        logisticsMessage.setOrderId(76217454L);
        logisticsMessage.setProcessStatus(TdaOrderProcessStatusEnum.REAL_DELIVERY_EMPTY.getCode());

        List<LogisticsCommodityItem> logisticsCommodityItems = Lists.newArrayList();
        LogisticsCommodityItem item1 = new LogisticsCommodityItem();
        item1.setCommodityId(40687390315133904L);
        item1.setCommodityPrice(new BigDecimal("17.42"));
        item1.setRealDeliveryQuantity(new BigDecimal(10));
        item1.setRealDeliveryNumber(1L);

        LogisticsCommodityItem item2 = new LogisticsCommodityItem();
        item2.setCommodityId(40687390315133904L);
        item2.setCommodityPrice(new BigDecimal("17.42"));
        item2.setRealDeliveryQuantity(new BigDecimal(10));
        item2.setRealDeliveryNumber(1L);

        LogisticsCommodityItem item3 = new LogisticsCommodityItem();
        item3.setCommodityId(40687390315133904L);
        item3.setCommodityPrice(new BigDecimal("17.42"));
        item3.setRealDeliveryQuantity(new BigDecimal(0));
        item3.setRealDeliveryNumber(0L);

        LogisticsCommodityItem item4 = new LogisticsCommodityItem();
        item4.setCommodityId(40687390315133904L);
        item4.setCommodityPrice(new BigDecimal("17.42"));
        item4.setRealDeliveryQuantity(new BigDecimal(10));
        item4.setRealDeliveryNumber(1L);

        LogisticsCommodityItem item5 = new LogisticsCommodityItem();
        item5.setCommodityId(40687390315133904L);
        item5.setCommodityPrice(new BigDecimal("17.42"));
        item5.setRealDeliveryQuantity(new BigDecimal(0));
        item5.setRealDeliveryNumber(0L);

        LogisticsCommodityItem item6 = new LogisticsCommodityItem();
        item6.setCommodityId(40687390315133904L);
        item6.setCommodityPrice(new BigDecimal("17.42"));
        item6.setRealDeliveryQuantity(new BigDecimal(10));
        item6.setRealDeliveryNumber(1L);
        logisticsCommodityItems.add(item1);
        logisticsCommodityItems.add(item2);
        logisticsCommodityItems.add(item3);
        logisticsCommodityItems.add(item4);
        logisticsCommodityItems.add(item5);
        logisticsCommodityItems.add(item6);
        logisticsMessage.setComplaintItemList(logisticsCommodityItems);
        subOrderService.updateTdaDeliveryQuantity(logisticsMessage);

    }


    @Test
    @Rollback(false)
    public void testShotDelivery(){
        LogisticsDeliveryMessage logisticsMessage = new LogisticsDeliveryMessage();
        logisticsMessage.setWaybillCode("111");
        logisticsMessage.setDriverId(-1L);
        logisticsMessage.setDeliveryDate(new Date());
        logisticsMessage.setStoreTypeId(1L);
        logisticsMessage.setStoreId(752908786325622016L);
        logisticsMessage.setPhone("13000000001");
        logisticsMessage.setLogisticsCenterId(1L);
        logisticsMessage.setDeliveryAddress("上海市秋月路矽岸国际1号楼616");
        logisticsMessage.setOrderCode("1716785626505933424");
        logisticsMessage.setOrderId(76217220L);
        logisticsMessage.setProcessStatus(13);

        List<LogisticsCommodityItem> logisticsCommodityItems = Lists.newArrayList();
        LogisticsCommodityItem item = new LogisticsCommodityItem();
        item.setCommodityId(40687390315133904L);
        item.setCommodityPrice(new BigDecimal("17.42"));
        item.setRealDeliveryQuantity(new BigDecimal(25));
        item.setRealDeliveryNumber(25L);
        logisticsCommodityItems.add(item);
        logisticsMessage.setComplaintItemList(logisticsCommodityItems);
        subOrderService.handleShotDelivery(logisticsMessage);

    }

    @Test
    @Rollback(false)
    public void testRefund(){
        LogisticsDeliveryMessage logisticsMessage = new LogisticsDeliveryMessage();
        logisticsMessage.setWaybillCode("111");
        logisticsMessage.setDriverId(-1L);
        logisticsMessage.setDeliveryDate(new Date());
        logisticsMessage.setStoreTypeId(1L);
        logisticsMessage.setStoreId(752908786325622016L);
        logisticsMessage.setPhone("13000000001");
        logisticsMessage.setLogisticsCenterId(1L);
        logisticsMessage.setDeliveryAddress("上海市秋月路矽岸国际1号楼616");
        logisticsMessage.setOrderCode("1716785626505933424");
        logisticsMessage.setOrderId(76217220L);
        logisticsMessage.setProcessStatus(13);

        List<LogisticsCommodityItem> logisticsCommodityItems = Lists.newArrayList();
        LogisticsCommodityItem item = new LogisticsCommodityItem();
        item.setCommodityId(40687390315133904L);
        item.setCommodityPrice(new BigDecimal("17.42"));
        item.setRealDeliveryQuantity(new BigDecimal(25));
        item.setRealDeliveryNumber(25L);
        logisticsCommodityItems.add(item);
        logisticsMessage.setComplaintItemList(logisticsCommodityItems);
        subOrderService.handleRefund(logisticsMessage, "",null);

    }

    @Test
    public void testCreatDeliveryOrder() throws ParseException {
        SubOrderSearchVo param = new SubOrderSearchVo();
        param.setBusinessType(0);
        param.setOrderIds(Arrays.asList(76222448L,76222449L));
        param.setWarehouseId(10061L);
        param.setUserId(1611L);
        param.setOrderTime(new SimpleDateFormat("yyyy-MM-dd").parse("2024-12-13"));
        subOrderController.creatDeliveryOrder(param);

    }

}
