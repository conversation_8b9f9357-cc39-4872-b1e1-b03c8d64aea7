package com.pinshang.qingyun.test;

import com.alibaba.fastjson.JSON;
import com.pinshang.qingyun.box.utils.BeanCloneUtils;
import com.pinshang.qingyun.common.service.CodeClient;
import com.pinshang.qingyun.order.enums.ProductTypeEnums;
import com.pinshang.qingyun.order.service.recharge.RechargeService;
import com.pinshang.qingyun.order.vo.order.OrderDto;
import com.pinshang.qingyun.order.vo.order.OrderItemDto;
import org.apache.commons.collections.CollectionUtils;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.*;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.function.Function;
import java.util.stream.Collectors;

public class OrderServiceTest extends AbstractJunitBase {

    @Autowired
    private RechargeService rechargeService;

    @Autowired
    private CodeClient codeClient;

    @Test
    public void testGenerateCode() {
        // 并发线程数
        int threadCount = 200;
        Set<String> uniqueCodes = Collections.synchronizedSet(new HashSet<>());
        CountDownLatch latch = new CountDownLatch(threadCount);
        ExecutorService executorService = Executors.newFixedThreadPool(20);

        for (int i = 0; i < threadCount; i++) {
            executorService.execute(() -> {
                try {
                    String code = rechargeService.getNewBillCode();
//                    String code = codeClient.createCode("XDA_PAY_BILL_CODE");
                    // 将生成的订单号添加到集合中
                    if (!uniqueCodes.add(code)) {
                        System.out.println("Duplicate order code found: " + code);
                    }
                } catch (Exception e) {
                    System.err.println("Error occurred: " + e.getMessage());
                } finally {
                    // 线程完成后，计数器减1
                    latch.countDown();
                }
            });
        }

        // 等待所有线程完成
        try {
            latch.await();
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            System.err.println("Test interrupted: " + e.getMessage());
        }

        // 关闭线程池
        executorService.shutdown();

        // 输出测试结果
        System.out.println("Total unique codes generated: " + uniqueCodes.size());
        System.out.println("Total unique codes generated: " + JSON.toJSONString(uniqueCodes));
        System.out.println("Test completed.");
    }

    @Test
    public void testSubOrder() {
        String msg1 = "{\"storeId\":1,\"enterpriseId\":1,\"userId\":1,\"printType\":1,\"printNum\":1,\"modeType\":1,\"items\":[{\"productId\":\"1\",\"logisticsModel\":1,\"stockType\":1},{\"productId\":\"2\",\"logisticsModel\":2,\"stockType\":2},{\"productId\":\"3\",\"logisticsModel\":2,\"stockType\":2}],\"giftItems\":[{\"productId\":\"4\",\"logisticsModel\":2,\"stockType\":2},{\"productId\":\"5\",\"logisticsModel\":1,\"stockType\":3}],\"logisticsModel\":1,\"supplierId\":1,\"warehouseId\":1,\"deliveryBatch\":\"1\",\"deleveryTimeRange\":\"demoData\",\"orderType\":1}";

        OrderDto orderDto = JSON.parseObject(msg1, OrderDto.class);
        List<OrderDto> orderDtos = buildOrderDtos(orderDto);
        System.out.println("orderDtos = " + JSON.toJSONString(orderDtos));
    }

    public static void main(String[] args) {
        String msg1 = "{\"storeId\":1,\"enterpriseId\":1,\"userId\":1,\"printType\":1,\"printNum\":1,\"modeType\":1,\"items\":[{\"productId\":\"1\",\"logisticsModel\":1,\"stockType\":1},{\"productId\":\"2\",\"logisticsModel\":2,\"stockType\":2},{\"productId\":\"3\",\"logisticsModel\":2,\"stockType\":2}],\"giftItems\":[{\"productId\":\"4\",\"logisticsModel\":2,\"stockType\":2},{\"productId\":\"5\",\"logisticsModel\":1,\"stockType\":3}],\"logisticsModel\":1,\"supplierId\":1,\"warehouseId\":1,\"deliveryBatch\":\"1\",\"orderType\":1}";

        OrderDto orderDto = JSON.parseObject(msg1, OrderDto.class);
        List<OrderDto> orderDtos = buildOrderDtos(orderDto);
        System.out.println("orderDtos = " + JSON.toJSONString(orderDtos));
    }

    public static List<OrderDto> buildOrderDtos(OrderDto orderDto) {
        List<OrderItemDto> giftAndRationItemList = orderDto.getItems().stream().filter(item -> ProductTypeEnums.GIFT.getCode().equals(item.getType()) || ProductTypeEnums.RATION.getCode().equals(item.getType())).collect(Collectors.toList());

        List<OrderDto> orderDtoList = new ArrayList<>();
        List<OrderItemDto> originItems = orderDto.getItems().stream().filter(item -> ProductTypeEnums.PRODUCT.getCode().equals(item.getType())).collect(Collectors.toList());
        Map<Integer, List<OrderItemDto>> stockTypeMap = originItems.stream().collect(Collectors.groupingBy(OrderItemDto::getStockType));
        // 说明原始原始商品有多个库存类型，需要拆单
        if (stockTypeMap.size() > 1) {
            stockTypeMap.entrySet().forEach(entry -> {
                List stockTypeOriginItems = entry.getValue();
                OrderDto newOderDto = BeanCloneUtils.copyTo(orderDto, OrderDto.class);
                newOderDto.setItems(stockTypeOriginItems);
                orderDtoList.add(newOderDto);
            });
        } else {
            orderDto.setItems(originItems);
            orderDtoList.add(orderDto);
        }

        String msg2 = "[{\"storeId\":1,\"enterpriseId\":1,\"userId\":1,\"printType\":1,\"printNum\":1,\"modeType\":1,\"items\":[{\"productId\":\"4\",\"logisticsModel\":2,\"stockType\":2}],\"logisticsModel\":1,\"supplierId\":1,\"warehouseId\":1,\"deliveryBatch\":\"1\",\"deleveryTimeRange\":\"demoData\",\"orderType\":1},{\"storeId\":1,\"enterpriseId\":1,\"userId\":1,\"printType\":1,\"printNum\":1,\"modeType\":1,\"items\":[{\"productId\":\"5\",\"logisticsModel\":1,\"stockType\":3}],\"logisticsModel\":1,\"supplierId\":1,\"warehouseId\":1,\"deliveryBatch\":\"1\",\"deleveryTimeRange\":\"demoData\",\"orderType\":1}]";

        List<OrderDto> giftAndRationOrderDtoList = JSON.parseArray(msg2, OrderDto.class);
        // 如果logisticsModel + warehouseId + stockType + supplierId 都一样，则把giftAndRationOrderDtoList中的items 加到orderDtoList的items下
        mergeGiftAndRationOrders(orderDtoList, giftAndRationOrderDtoList);
//            orderDtoList.addAll(giftAndRationOrderDtoList);

        return orderDtoList;
    }

    public static void mergeGiftAndRationOrders(List<OrderDto> orderDtoList, List<OrderDto> giftAndRationOrderDtoList) {
        if (CollectionUtils.isEmpty(giftAndRationOrderDtoList)) {
            return;
        }
        Map<String, OrderDto> keyToOrderMap = orderDtoList.stream().collect(Collectors.toMap(orderDto -> orderDto.getLogisticsModel() + "_" + orderDto.getWarehouseId() + "_" + orderDto.getItems().get(0).getStockType() + "_" + orderDto.getSupplierId(), Function.identity()));

        for (OrderDto giftAndRationOrder : giftAndRationOrderDtoList) {
            String key = generateOrderKey(giftAndRationOrder);
            OrderDto existingOrder = keyToOrderMap.get(key);
            if (Objects.nonNull(existingOrder)) {
                existingOrder.getItems().addAll(giftAndRationOrder.getItems());
            } else {
                orderDtoList.add(giftAndRationOrder);
            }
        }
    }

    public static String generateOrderKey(OrderDto orderDto) {
        return orderDto.getLogisticsModel() + "_" + orderDto.getWarehouseId() + "_" + orderDto.getItems().get(0).getStockType() + "_" + orderDto.getSupplierId();
    }

}
