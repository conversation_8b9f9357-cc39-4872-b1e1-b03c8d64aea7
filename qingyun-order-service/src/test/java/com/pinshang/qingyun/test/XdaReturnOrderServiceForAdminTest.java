package com.pinshang.qingyun.test;

import com.alibaba.fastjson.JSON;
import com.pinshang.qingyun.base.enums.xda.TdaOrderProcessStatusEnum;
import com.pinshang.qingyun.box.utils.BeanCloneUtils;
import com.pinshang.qingyun.box.utils.DateUtil;
import com.pinshang.qingyun.order.dto.xda.tda.LogisticsDeliveryMessage;
import com.pinshang.qingyun.order.dto.xda.tda.ReturnOrderDetailODTO;
import com.pinshang.qingyun.order.dto.xda.tda.SaveReturnOrderODTO;
import com.pinshang.qingyun.order.dto.xda.tda.SyncReturnOrderIDTO;
import com.pinshang.qingyun.order.model.xda.XdaReturnOrder;
import com.pinshang.qingyun.order.service.tda.XdaReturnOrderServiceForAdmin;
import com.pinshang.qingyun.order.service.tda.factory.ReturnOrderStrategyFactory;
import com.pinshang.qingyun.renderer.service.IRenderService;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.annotation.Commit;

import java.util.Date;
import java.util.Objects;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2024/05/13
 * @Version 1.0
 */
public class XdaReturnOrderServiceForAdminTest extends AbstractJunitBase {
    @Autowired
    private XdaReturnOrderServiceForAdmin xdaReturnOrderServiceForAdmin;
    @Autowired
    private ReturnOrderStrategyFactory resultOrderStrategyFactory;
    @Autowired
    private IRenderService renderService;
    @Test
    public void stockShortTest() {
//        XdaComplaintCommodityDTO dto = new XdaComplaintCommodityDTO();
//        dto.setStoreId(620957946566100L);
//        dto.setStoreCode("1032252");
//        dto.setDeliveryDate("2024-05-10");
//        dto.setComplaintType(ComplaintTypeEnum.RETURN);
//        dto.setComplaintTotalMoney(BigDecimal.valueOf(1));
//        dto.setComplaintReason("wuyu");
//        List<XdaComplaintCommodityItemDTO> complaintItemList = new ArrayList<>();
//        XdaComplaintCommodityItemDTO itemDTO = new XdaComplaintCommodityItemDTO();
//        itemDTO.setCommodityId(999965182752826768L);
//        itemDTO.setComplaintType(ComplaintTypeEnum.RETURN.getCode());
//        itemDTO.setCommodityPicUrl("http://www.img.com");
//        itemDTO.setCommodityCode("2");
//        itemDTO.setCommodityName("自测商品");
//        itemDTO.setCommodityUnit("罐");
//        itemDTO.setRealDeliveryQuantity(new BigDecimal("3"));
//        itemDTO.setRealDeliveryQuantityStr("3");
//        itemDTO.setComplaintMoney(new BigDecimal("4"));
//        itemDTO.setRealReturnQuantity(new BigDecimal("4"));
//        itemDTO.setRealReturnQuantityStr("4");
//        itemDTO.setQuestionType(0L);
//        List<String> complaintPicList = new ArrayList<>();
//        complaintPicList.add("http://www.img1.com");
//        complaintPicList.add("http://www.img2.com");
//        complaintPicList.add("http://www.img3.com");
//        itemDTO.setComplaintPicList(complaintPicList);
//        itemDTO.setCommodityPrice(new BigDecimal("6.60"));
//        itemDTO.setCommoditySpec("罐");
//        itemDTO.setCommodityPriceName("自测商品");
//        itemDTO.setOrderTime(new Date());
//        itemDTO.setLogisticsCenterId(11L);
//        itemDTO.setDeliveryBatch("一配");
//        itemDTO.setDeliveryTimeRange("00:00-00:06");
//        complaintItemList.add(itemDTO);
//        XdaComplaintCommodityItemDTO itemDTO2 = new XdaComplaintCommodityItemDTO();
//        itemDTO2.setCommodityId(999965182752826768L);
//        itemDTO2.setComplaintType(ComplaintTypeEnum.RETURN.getCode());
//        itemDTO2.setCommodityPicUrl("http://www.img.com");
//        itemDTO2.setCommodityCode("2");
//        itemDTO2.setCommodityName("自测商品");
//        itemDTO2.setCommodityUnit("罐");
//        itemDTO2.setRealDeliveryQuantity(new BigDecimal("3"));
//        itemDTO2.setRealDeliveryQuantityStr("3");
//        itemDTO2.setComplaintMoney(new BigDecimal("4"));
//        itemDTO2.setRealReturnQuantity(new BigDecimal("4"));
//        itemDTO2.setRealReturnQuantityStr("4");
//        itemDTO2.setQuestionType(0L);
//        List<String> complaintPicList2 = new ArrayList<>();
//        complaintPicList2.add("http://www.img1.com");
//        complaintPicList2.add("http://www.img2.com");
//        complaintPicList2.add("http://www.img3.com");
//        itemDTO2.setComplaintPicList(complaintPicList2);
//        itemDTO2.setCommodityPrice(new BigDecimal("6.60"));
//        itemDTO2.setCommoditySpec("罐");
//        itemDTO2.setCommodityPriceName("自测商品");
//        itemDTO2.setOrderTime(new Date());
//        itemDTO2.setLogisticsCenterId(11L);
//        itemDTO2.setDeliveryBatch("一配");
//        itemDTO2.setDeliveryTimeRange("00:00-00:06");
//        complaintItemList.add(itemDTO2);
//        dto.setComplaintItemList(complaintItemList);
//        dto.setPickUpTimeRange("00:00-00:06");
//        SaveReturnOrderODTO dto2 = BeanCloneUtils.copyTo(dto, SaveReturnOrderODTO.class);
//        dto2.setReturnSource(TDaReturnSourceEnum.APP_RETURN.getCode());
//        xdaReturnOrderService.generateReturnOrder(dto2);
    }

    @Test
    @Commit
    public void testMq() {
//        String msg= "{\"returnOrderId\":null,\"waybillCode\":\"111\",\"driverId\":-1,\"deliveryDate\":\"2024-05-12T16:00:00.000+00:00\",\"storeTypeId\":1,\"storeId\":999872035591639000,\"phone\":\"13000000001\",\"logisticsCenterId\":1,\"deliveryAddress\":\"上海市秋月路矽岸国际1号楼616\",\"orderCode\":\"1\",\"orderId\":1,\"processStatus\":4,\"complaintItemList\":[{\"commodityId\":468788174651305600,\"commodityPrice\":756.52,\"realDeliveryQuantity\":11,\"realDeliveryNumber\":12},{\"commodityId\":56095697583367000,\"commodityPrice\":52.36,\"realDeliveryQuantity\":6,\"realDeliveryNumber\":6},{\"commodityId\":808789655259185700,\"commodityPrice\":25,\"realDeliveryQuantity\":2,\"realDeliveryNumber\":3}]}";
        String msg = "{\"businessType\":10,\"waybillCode\":\"YD20240528000005\",\"driverId\":1041,\"deliveryBatch\":2,\"deliveryDate\":1716825600000,\"storeTypeId\":9131078242888821882,\"storeId\":999872035591660701,\"phone\":\"***********\",\"logisticsCenterId\":1,\"deliveryAddress\":\"中国上海市上海市浦东新区张江镇秋月路26号\",\"orderCode\":\"1716864911513052709\",\"orderId\":76217252,\"orderAmount\":231.00,\"processStatus\":6,\"complaintItemList\":[{\"commodityId\":999965182752681039,\"commodityPrice\":231.00,\"realDeliveryQuantity\":7.000,\"realDeliveryNumber\":7,\"commodityOrderQuantity\":7.000}]}";
        LogisticsDeliveryMessage logisticsMessage = JSON.parseObject(msg, LogisticsDeliveryMessage.class);
        SaveReturnOrderODTO saveReturnOrderODTO = BeanCloneUtils.copyTo(logisticsMessage, SaveReturnOrderODTO.class);
        if (Objects.nonNull(logisticsMessage.getDeliveryDate())) {
            saveReturnOrderODTO.setDeliveryDate(DateUtil.getDateFormate(logisticsMessage.getDeliveryDate(), "yyyy-MM-dd"));
        }
        saveReturnOrderODTO.setReturnSource(3);
        saveReturnOrderODTO.setComplaintTotalMoney(logisticsMessage.getOrderAmount());
        resultOrderStrategyFactory.execute(saveReturnOrderODTO);
    }

    @Test
    public void testPxSave() {
        String msg = "{\n" +
                "    \"returnSource\": 2,\n" +
                "    \"storeId\": 50423070765752104,\n" +
                "    \"deliveryDate\": \"2024-05-23\",\n" +
                "    \"complaintType\": 1,\n" +
                "    \"complaintTotalMoney\": 100.01,\n" +
                "    \"complaintItemList\": [{\n" +
                "        \"commodityId\": 56095697583367000,\n" +
                "        \"realReturnQuantity\": 2,\n" +
                "        \"complaintType\": 1,\n" +
                "        \"questionType\": 9131078242860601471\n" +
                "    }],\n" +
                "    \"pickUpTimeRange\": \"13:00~18:00\",\n" +
                "    \"businessType\": 10,\n" +
                "    \"returnOrderCode\": \"1716443324696\"\n" +
                "}";
        SaveReturnOrderODTO saveReturnOrderODTO = JSON.parseObject(msg, SaveReturnOrderODTO.class);
        resultOrderStrategyFactory.execute(saveReturnOrderODTO);

    }

    @Test
    @Commit
    public void testQhWc() {

        String msg = "{\"businessType\":10,\"waybillCode\":\"YD20240528000037\",\"driverId\":1041,\"refType\":21,\"deliveryBatch\":2,\"deliveryDate\":1716825600000,\"storeTypeId\":9131078242888821882,\"storeId\":999872035591611908,\"phone\":\"***********\",\"logisticsCenterId\":1,\"deliveryAddress\":\"中国上海市上海市金山区亭林镇亭林路25号\",\"orderCode\":\"00000000b84fccc6e8f748568e354a22b867b41c\",\"orderId\":26,\"orderAmount\":0.02,\"processStatus\":7}";
        LogisticsDeliveryMessage logisticsMessage = JSON.parseObject(msg, LogisticsDeliveryMessage.class);
        SaveReturnOrderODTO saveReturnOrderODTO = BeanCloneUtils.copyTo(logisticsMessage, SaveReturnOrderODTO.class);
        if (Objects.nonNull(logisticsMessage.getDeliveryDate())) {
            saveReturnOrderODTO.setDeliveryDate(DateUtil.getDateFormate(logisticsMessage.getDeliveryDate(), "yyyy-MM-dd"));
        }
        Integer refType = logisticsMessage.getRefType();
        if (Objects.equals(refType, 21)) {
            logisticsMessage.setReturnOrderId(logisticsMessage.getOrderId());
            saveReturnOrderODTO.setReturnOrderId(logisticsMessage.getOrderId());
        }
        Integer processStatus = logisticsMessage.getProcessStatus();
        if (Objects.equals(processStatus, TdaOrderProcessStatusEnum.PICK_WAIT_CONFIRM.getCode())) {
            //更新退货单状态
//            handleUpdateReturnOrder(logisticsMessage, new Date(), TDaReturnStatusEnum.PENDING_WAREHOUSE_CONFIRMATION.getCode());
            //取货完成，同步到投诉表
            ReturnOrderDetailODTO returnOrderDetail = xdaReturnOrderServiceForAdmin.queryReturnOrderItemList(logisticsMessage.getReturnOrderId());
            xdaReturnOrderServiceForAdmin.syncReturnOrder2Complaint(BeanCloneUtils.copyTo(returnOrderDetail, SyncReturnOrderIDTO.class));
        }
    }
    private void handleUpdateReturnOrder(LogisticsDeliveryMessage dto, Date deliveryEndTime, Integer status) {
        if (Objects.isNull(dto.getReturnOrderId())){
            return;
        }
        XdaReturnOrder returnOrder = new XdaReturnOrder();
        returnOrder.setId(dto.getReturnOrderId());
        returnOrder.setStatus(status);
        returnOrder.setDeliveryEndTime(deliveryEndTime);
//        returnOrder.setSourceOrderId(dto.getOrderId());
        returnOrder.setLogisticsCenterId(dto.getLogisticsCenterId());
        returnOrder.setDeliveryBatch(String.valueOf(dto.getDeliveryBatch()));
        returnOrder.setDriverId(dto.getDriverId());
        returnOrder.setWaybillCode(dto.getWaybillCode());
        renderService.render(returnOrder,"handleUpdateReturnOrder");
        xdaReturnOrderServiceForAdmin.updateReturnOrder(returnOrder);
    }
}
