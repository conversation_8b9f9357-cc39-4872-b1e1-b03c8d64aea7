package com.pinshang.qingyun.test.tob;

import com.pinshang.qingyun.box.utils.DateUtil;
import com.pinshang.qingyun.order.dto.tob.EsXdaCommoditySoldOutIDTO;
import com.pinshang.qingyun.order.dto.tob.EsXdaCommoditySoldOutODTO;
import com.pinshang.qingyun.order.service.tob.ITobCommodityStatisticsService;
import com.pinshang.qingyun.order.service.tob.impl.TobCommodityStockForEsServiceImpl;
import com.pinshang.qingyun.order.vo.tob.ToBOrderStatisticsReqVo;
import com.pinshang.qingyun.test.AbstractJunitBase;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.annotation.Rollback;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

public class TobCommodityStatisticsServiceTest extends AbstractJunitBase {
    @Autowired
    private ITobCommodityStatisticsService iTobCommodityStatisticsService;

    @Autowired
    private TobCommodityStockForEsServiceImpl tobCommodityStockForEsServiceImpl;

    @Test
    @Rollback(false)
    public void updateTobOrderStatistics() {
        ToBOrderStatisticsReqVo reqVo = new ToBOrderStatisticsReqVo();
        reqVo.setCommodityId(999965182752825095L);
        reqVo.setOrderTime(DateUtil.parseDate("2024-04-16","yyyy-MM-dd"));
        reqVo.setType(2);
        reqVo.setNumber(20);
        reqVo.setQuantity(new BigDecimal(20));
        iTobCommodityStatisticsService.updateTobOrderStatistics(reqVo);
    }

    @Test
    @Rollback(false)
    public void queryCommodityInventory() {
        EsXdaCommoditySoldOutIDTO idto = new EsXdaCommoditySoldOutIDTO();
        idto.setDate(new Date());
        idto.setType(2);
        idto.setCommodityIdList(Arrays.asList(7951724559737401L));
        List<EsXdaCommoditySoldOutODTO> result = tobCommodityStockForEsServiceImpl.queryCommodityInventory(idto);
        System.out.println(result);
    }


}
