package com.pinshang.qingyun.test;

import com.pinshang.qingyun.base.spring.MockMultipartFile;
import com.pinshang.qingyun.order.service.auto.ShopAutoOrderService;
import com.pinshang.qingyun.order.service.syncOrderJob.SyncOrderJobService;
import com.pinshang.qingyun.upload.dto.odto.FileUploadRestSingleODTO;
import com.pinshang.qingyun.upload.service.FileUploadClientFactory;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.test.annotation.Rollback;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;

/**
 * Created by weican on 2018-05-28.
 */
public class TestSyncOrder extends  AbstractJunitBase {
    @Autowired
    private SyncOrderJobService syncOrderJobService;
    @Autowired
    private FileUploadClientFactory fileUploadClientFactory;
    @Autowired
    private ShopAutoOrderService shopAutoOrderService;


    @Test
    @Rollback(false)
    public void testAuto(){
        shopAutoOrderService.createShopAutoOrder(16L,999872035591509085L,"23:50",new ArrayList<>());
    }

    @Test
    public  void upload() throws IOException {
            File file = new File("D:\\20220323.txt");
            InputStream inputStream = new FileInputStream(file);
            MultipartFile multipartFile = new MockMultipartFile("file", file.getName(), MediaType.MULTIPART_FORM_DATA_VALUE, inputStream);
            FileUploadRestSingleODTO fileUploadRestSingleODTO = fileUploadClientFactory.getXsClient().restUpload(multipartFile, "PF_DELIVERY_ORDER_FILE");

        System.out.println( fileUploadRestSingleODTO);
        }
    @Test
    @Rollback(false)
    public void coverOrder(){
        syncOrderJobService.execute(null);
    }
}
