package com.pinshang.qingyun.test;

import com.pinshang.qingyun.base.enums.xd.XdPayTypeEnum;
import com.pinshang.qingyun.order.service.pay.callback.ThirdPartyPayCallbackEvent;
import com.pinshang.qingyun.order.service.recharge.RechargeService;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 */
@Slf4j
public class RechargeServiceTest extends AbstractJunitBase {

    @Autowired
    private RechargeService rechargeService;
/*
    *//**
     * 测试并发调用callEvent，验证重复调用充值接口的问题
     *//*
    @Test
    public void testConcurrentCallBackEvent() throws InterruptedException {
        // 模拟事件
        ThirdPartyPayCallbackEvent event = ThirdPartyPayCallbackEvent.build(
                "2024091822001458871458486484"
                , "701725354367187"
                , "{\"miniuser\":\"2019010762862511\",\"msgType\":\"trade.appPreOrder\",\"package\":\"Sign=ALI\",\"minipath\":\"pages/appPay/index/index\",\"appScheme\":\"iOS:QMFreshDelivery;Android:com.tramy.fresh_arrive     \",\"sign\":\"ruRnoVimDBqUmmtQisvSbikOgdGesYyvmmiuheSKuJlgIPNrKFmRFPGFThZDAOhV\",\"prepayid\":\"ori=11DY17701726625171429420\",\"noncestr\":\"uxGvScvVbsHTcwZxZNvsaOwbraxVFiJV\",\"timestamp\":\"20240918100442\"}"
                , true
                , XdPayTypeEnum.ALIPAY);


        // 使用线程池来模拟并发
        int threadCount = 10; // 并发线程数
        ExecutorService executorService = Executors.newFixedThreadPool(threadCount);
        CountDownLatch latch = new CountDownLatch(threadCount);


        // 创建多个并发任务
        for (int i = 0; i < threadCount; i++) {
            executorService.submit(() -> {
                try {
                    latch.await(); // 确保所有线程同时开始
                    rechargeService.callBackEvent(event);
                } catch (InterruptedException e) {
                    e.printStackTrace();
                }
            });
            latch.countDown(); // 释放线程
        }

        // 等待所有线程执行完毕
        executorService.shutdown();
        executorService.awaitTermination(1, TimeUnit.MINUTES);

    }

    @Test
    public void runMultiThreadTestCallBackEvent() {
        ThirdPartyPayCallbackEvent event = ThirdPartyPayCallbackEvent.build(
                "2024091822001458871458486484"
                , "701725262198978"
                , "{\"miniuser\":\"2019010762862511\",\"msgType\":\"trade.appPreOrder\",\"package\":\"Sign=ALI\",\"minipath\":\"pages/appPay/index/index\",\"appScheme\":\"iOS:QMFreshDelivery;Android:com.tramy.fresh_arrive     \",\"sign\":\"ruRnoVimDBqUmmtQisvSbikOgdGesYyvmmiuheSKuJlgIPNrKFmRFPGFThZDAOhV\",\"prepayid\":\"ori=11DY17701726625171429420\",\"noncestr\":\"uxGvScvVbsHTcwZxZNvsaOwbraxVFiJV\",\"timestamp\":\"20240918100442\"}"
                , true
                , XdPayTypeEnum.ALIPAY);
        int numberOfThreads = 10; // 设定线程数量
        ExecutorService executorService = Executors.newFixedThreadPool(numberOfThreads);

        for (int i = 1; i <= numberOfThreads; i++) {
            executorService.submit(() -> rechargeService.callBackEvent(event)); // 提交任务给线程池执行
        }

        // 关闭线程池并等待所有线程执行完毕
        executorService.shutdown();
        try {
            if (!executorService.awaitTermination(60, TimeUnit.SECONDS)) {
                executorService.shutdownNow();
            }
        } catch (InterruptedException e) {
            executorService.shutdownNow();
        }
    }

    *//**
     * 测试并发调用callEvent，验证重复调用充值接口的问题
     *//*
    @Test
    public void testCallBackEvent() {
        // 模拟事件
        ThirdPartyPayCallbackEvent event = ThirdPartyPayCallbackEvent.build(
                "2024091822001458871458486484"
                , "701725262467818"
                , "{\"miniuser\":\"2019010762862511\",\"msgType\":\"trade.appPreOrder\",\"package\":\"Sign=ALI\",\"minipath\":\"pages/appPay/index/index\",\"appScheme\":\"iOS:QMFreshDelivery;Android:com.tramy.fresh_arrive     \",\"sign\":\"ruRnoVimDBqUmmtQisvSbikOgdGesYyvmmiuheSKuJlgIPNrKFmRFPGFThZDAOhV\",\"prepayid\":\"ori=11DY17701726625171429420\",\"noncestr\":\"uxGvScvVbsHTcwZxZNvsaOwbraxVFiJV\",\"timestamp\":\"20240918100442\"}"
                , true
                , XdPayTypeEnum.ALIPAY);
        rechargeService.callBackEvent(event);
    }*/
}
