@startuml

legend center
==解决技术债务==
现阶段存在的主要问题：
1. 代码模块之间边界感不强
2. 代码实现缺少层次感
3. 流程耦合度高
4. 业务逻辑复杂
==影响开发效率==
即使我们接手项目已经有一段时间，并对项目足够了解时，但排查问题起来依然费时费力时，而且系统内部代码错综复杂，
调用链路交错，难以正常维护
==重构目标==
* 不影响业务的正常运转和迭代
* 改善现有代码结构设计，让代码易于扩展，提升开发效率
* 采用新的购物车service逐步替代原有类，旧类逐渐废弃
end legend

skinparam ParticipantPadding 20
skinparam BoxPadding 10

box "鲜达Order服务" #LightBlue
participant "购物车服务" as Cart
participant "促销计算器" as PriceCalculator
participant "优惠券计算器" as PromotionCalculator
participant "库存校验器" as StockValidator
end box

box "外部服务" #LightYellow
participant "商品服务" as Product
participant "营销服务" as Marketing
participant "大仓服务" as Stock
participant "数据库" as DB
end box
note left
a note
can also be defined
on several lines
end note
== 初始化阶段 ==
Cart -> DB: 1. 获取购物车信息
Cart -> Product: 2. 批量查询购物车商品基础信息
Cart -> Cart: 3. 商品分组(普通/特惠)

== 营销计算阶段 ==
Cart -> Marketing: 8. 返回促销活动和优惠券计算结果

== 商品价格处理阶段 ==
Cart -> Cart: 4. 获取店铺订货目标金额
Cart -> Cart: 5. 处理普通商品组
group 普通商品组处理
    Cart -> Marketing: 5.1 获取促销和特价信息
    Cart -> Cart: 5.2 计算商品原价和优惠价
    Cart -> Cart: 5.3 处理特价商品限购
    Cart -> Cart: 5.4 设置商品标签信息
end

Cart -> Cart: 6. 处理特惠商品组
group 特惠商品组处理
    Cart -> Cart: 6.1 校验是否达到订货目标金额
    Cart -> Cart: 6.2 计算特惠商品价格
    Cart -> Cart: 6.3 处理特惠商品限量
end

Cart -> Cart: 7. 处理买赠商品组
group 买赠商品组处理
    Cart -> Cart: 7.1 计算赠品数量
    Cart -> Cart: 7.2 处理赠品限量
    Cart -> Cart: 7.3 设置赠品价格为0
end

== 库存校验阶段 ==
Cart -> Stock: 9. 批量查询库存
Cart -> StockValidator: 10. 库存和限购校验
group 库存校验处理
    StockValidator -> StockValidator: 10.1 校验普通商品库存
    StockValidator -> StockValidator: 10.2 校验组合商品库存
    StockValidator -> StockValidator: 10.3 校验特惠商品库存
    StockValidator -> StockValidator: 10.4 校验赠品库存
end
StockValidator -> StockValidator: 11. 处理凑整规则

== 最终处理阶段 ==
Cart -> Cart: 12. 校验起送规则
Cart -> Cart: 13. 计算最终价格
Cart -> Cart: 14. 设置品类数量
Cart -> DB: 15. 更新购物车
@enduml