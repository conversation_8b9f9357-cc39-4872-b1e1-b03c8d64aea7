package com.pinshang.qingyun.test;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.pinshang.qingyun.ApplicationOrderService;
import com.pinshang.qingyun.order.enums.OrderPrintTypeEnum;
import com.pinshang.qingyun.order.mapper.OrderMapper;
import com.pinshang.qingyun.order.model.order.Order;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
import tk.mybatis.mapper.entity.Example;

import java.io.IOException;
import java.util.List;

/**
 * Create by JXL on 2018/4/4
 */
@RunWith(SpringJUnit4ClassRunner.class)
@SpringBootTest(classes = ApplicationOrderService.class,properties = {"spring.profiles.active=dev","application.name.switch=jxl-"})
public class TestEnumConverter {
    @Autowired
    private OrderMapper orderMapper;


    @Test
    public void query(){
        long orderId = 10;
        Example ex =new Example(Order.class);
        ex.createCriteria().andEqualTo("id", orderId);
        List<Order> orderList = this.orderMapper.selectByExample(ex);
        System.out.println(1);

    }

    @Test
    public void insert(){
        long orderId = 10;
        Example ex =new Example(Order.class);
        ex.createCriteria().andEqualTo("id", orderId);
        List<Order> orderList = this.orderMapper.selectByExample(ex);
        Order order = orderList.get(0);
        order.setPrintType(OrderPrintTypeEnum.LOCAL);
        order.setId(null);
        orderMapper.insert(order);
        System.out.println(order.getId());
        System.out.println(2);
    }

    @Test
    public void serializeAndDeseri(){
        long orderId = 10;
        Example ex =new Example(Order.class);
        ex.createCriteria().andEqualTo("id", orderId);
        List<Order> orderList = this.orderMapper.selectByExample(ex);
        Order order = orderList.get(0);
        ObjectMapper mapper = new ObjectMapper();
        try {
            String s = mapper.writeValueAsString(order);
            System.out.println(s);
            Order order1 = mapper.readValue(s, Order.class);
            System.out.println("");
        } catch (JsonProcessingException e) {
            e.printStackTrace();
        } catch (IOException e) {
            e.printStackTrace();
        }


    }
}
