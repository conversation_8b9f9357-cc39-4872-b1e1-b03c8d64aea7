package com.pinshang.qingyun.test;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.pinshang.qingyun.box.utils.SpringUtil;
import com.pinshang.qingyun.kafka.MessageOperationType;
import com.pinshang.qingyun.kafka.MessageType;
import com.pinshang.qingyun.kafka.MessageWrapper;
import com.pinshang.qingyun.order.enums.OrderPrintTypeEnum;
import com.pinshang.qingyun.order.model.order.Order;
import com.pinshang.qingyun.order.model.order.OrderList;
import org.junit.Test;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2019/1/16 18:18.
 */
public class Demo {

    @Test
    public void testJSON() throws JsonProcessingException {
        Order order = new Order();
        order.setId(219569L);
        order.setOrderCode("1547608171269787305");
        order.setModeType(0);
        order.setCreateId(372L);
        order.setCreateTime(new Date(1547608171000L));
        order.setFinalAmount(BigDecimal.valueOf(10.5));
        order.setOrderAmount(BigDecimal.valueOf(10.5));
        order.setOrderStatus(0);
        order.setOrderTime(new Date(1547654400000L));
        order.setOrderType(1);
        order.setPrintNum(1);
        order.setPrintType(OrderPrintTypeEnum.NOPRInteger);
        order.setSettleStatus(0);
        order.setStoreId(752908786325622016L);
        order.setUpdateId(372L);
        order.setUpdateTime(new Date(1547608171000L));
        List<OrderList> orderList = new ArrayList<>();
        OrderList orderItem1 = new OrderList();
        orderItem1.setId(5318315L);
        orderItem1.setCommodityCode("004207");
        orderItem1.setCommodityId(227000091358522304L);
        orderItem1.setCommodityName("(碗装)什锦腐竹");
        orderItem1.setCommodityNum(BigDecimal.ONE);
        orderItem1.setCommodityPrice(BigDecimal.valueOf(3.5));
        orderItem1.setCommoditySpec("120克/盒");
        orderItem1.setOrderId(219569L);
        orderItem1.setTotalPrice(BigDecimal.valueOf(3.5));
        orderItem1.setType(1);
        orderList.add(orderItem1);
        OrderList orderItem2 = new OrderList();
        orderItem2.setId(5318316L);
        orderItem2.setCommodityCode("004204");
        orderItem2.setCommodityId(970288549453778176L);
        orderItem2.setCommodityName("(碗装)川香豆腐皮");
        orderItem2.setCommodityNum(BigDecimal.valueOf(2));
        orderItem2.setCommodityPrice(BigDecimal.valueOf(3.5));
        orderItem2.setCommoditySpec("120克/盒");
        orderItem2.setOrderId(219569L);
        orderItem2.setTotalPrice(BigDecimal.valueOf(7));
        orderItem2.setType(1);
        orderList.add(orderItem2);
        order.setOrderList(orderList);

        ObjectMapper objectMapper = new ObjectMapper();
        String value = objectMapper.writeValueAsString(order);
        System.out.println(value);
        JSON.parseObject(value);
        MessageWrapper wrapper = new MessageWrapper(MessageType.ORDER, order, MessageOperationType.INSERT);
        String s = objectMapper.writeValueAsString(wrapper);
        JSONObject jsonObject = JSON.parseObject(s);
    }

    private void c() throws JsonProcessingException {
        A a = new A();
        a.setId(10);
        List<B> list = new ArrayList<>();
        B b1 = new B();
        b1.setId(1);
        b1.setName("wechat");
        list.add(b1);
        a.setItemList(list);

        C c = new C();
        SpringUtil.copyProperties(a, c);
        ObjectMapper mapper = new ObjectMapper();
        String x = mapper.writeValueAsString(c);
        System.out.println(x);
        System.out.println(JSON.toJSONString(c));
        C c1 = JSON.parseObject(x, C.class);
    }
}
