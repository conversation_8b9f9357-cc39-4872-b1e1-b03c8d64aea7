package com.pinshang.qingyun.test;

import com.alibaba.fastjson.JSON;
import com.github.pagehelper.PageInfo;
import com.pinshang.qingyun.order.dto.consignment.ConfirmConsignmentReturnOrdeItemIDTO;
import com.pinshang.qingyun.order.dto.consignment.ConfirmConsignmentReturnOrderIDTO;
import com.pinshang.qingyun.order.dto.consignment.ConsignmentSaleReturnOrderDetailODTO;
import com.pinshang.qingyun.order.dto.consignment.SaveConsignmentSaleReturnOrderODTO;
import com.pinshang.qingyun.order.mapper.SaleReturnOrderMapper;
import com.pinshang.qingyun.order.model.order.SaleReturnOrder;
import com.pinshang.qingyun.order.service.SaleReturnOrderService;
import com.pinshang.qingyun.order.vo.order.ConsignmentSaleReturnOrderReqVo;
import com.pinshang.qingyun.order.vo.order.ConsignmentSaleReturnOrderRespVo;
import com.pinshang.qingyun.order.vo.order.SaleReturnOrderItemQuantityWrapperVo;
import com.pinshang.qingyun.order.vo.order.SaleReturnOrderUpdateQuantityWrapperVo;
import org.apache.commons.compress.utils.Lists;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.ss.usermodel.WorkbookFactory;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.annotation.Rollback;
import tk.mybatis.mapper.entity.Example;

import java.io.File;
import java.io.InputStream;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2018/10/16 9:55.
 * @blog http://linuxsogood.org
 */
public class SaleReturnOrderServiceTest extends AbstractJunitBase {

    @Autowired
    private SaleReturnOrderService saleReturnOrderService;

    @Autowired
    private SaleReturnOrderMapper saleReturnOrderMapper;

    @Test
    public void testSelectOrder() {
        String orderCode = "TH201810110002";
        List<Integer> model = Arrays.asList(1, 2);
        SaleReturnOrder saleReturnOrder = saleReturnOrderService.selectByOrderCodeAndLogisticsModel(orderCode, model);
        System.out.println(JSON.toJSONString(saleReturnOrder));
    }

    @Test
    public void testUpdateSaleReturnOrderQuantity() {
        SaleReturnOrderUpdateQuantityWrapperVo vo = new SaleReturnOrderUpdateQuantityWrapperVo();
        vo.setId(2603L);
        List<SaleReturnOrderItemQuantityWrapperVo> itemList = new ArrayList<>();
        SaleReturnOrderItemQuantityWrapperVo vo1 = new SaleReturnOrderItemQuantityWrapperVo();
        vo1.setCommodityId(4511066835394600L);
        vo1.setBreakageQuantity(BigDecimal.valueOf(3));
        vo1.setRealReturnQuantity(BigDecimal.valueOf(5));
        itemList.add(vo1);
        SaleReturnOrderItemQuantityWrapperVo vo2 = new SaleReturnOrderItemQuantityWrapperVo();
        vo2.setBreakageQuantity(BigDecimal.ONE);
        vo2.setRealReturnQuantity(BigDecimal.valueOf(3));
        vo2.setCommodityId(28910784838826800L);
        itemList.add(vo2);
        vo.setItemList(itemList);
        saleReturnOrderService.processReturnOrderItemQuantity(vo);
    }

    @Test
    public void testMapper() {
        Example ex = new Example(SaleReturnOrder.class);
        ex.createCriteria().andEqualTo("id", 2605);
        List<SaleReturnOrder> saleReturnOrders = saleReturnOrderMapper.selectByExample(ex);
        System.out.println("aaa");
    }

    @Test
    public void testConsignmentReturnOrderImport(){


        Workbook wb = null;
        try {
            InputStream in =  this.getClass().getClassLoader().getResourceAsStream("testImport.xlsx");
            wb = WorkbookFactory.create(in);
        } catch (Exception e) {
            System.out.println(e);
        }
        saleReturnOrderService.consignmentReturnOrderImport(wb,28L);

    }

    @Test
    @Rollback(false)
    public void testBatchSaveConsignmentReturnOrder(){
        List<SaveConsignmentSaleReturnOrderODTO> saveConsignmentSaleReturnOrderODTOS = Lists.newArrayList();
        SaveConsignmentSaleReturnOrderODTO dto = new SaveConsignmentSaleReturnOrderODTO();
        dto.setSupplierId(28L);
        dto.setStoreId(999872035591506747L);
        dto.setCommodityId(2029110267185300L);
        dto.setTotalQuantity(new BigDecimal(3.88));
        saveConsignmentSaleReturnOrderODTOS.add(dto);
        saleReturnOrderService.batchSaveConsignmentReturnOrder(saveConsignmentSaleReturnOrderODTOS,null);

    }


    @Test
    @Rollback(false)
    public void testQueryConsignmentSaleReturnOrderItemList(){
        ConsignmentSaleReturnOrderDetailODTO consignmentSaleReturnOrderDetailODTO = saleReturnOrderService.queryConsignmentSaleReturnOrderItemList(13L);
        System.out.println(consignmentSaleReturnOrderDetailODTO);

    }

    @Test
    @Rollback(false)
    public void testConfirmSaleReturnOrder(){
        ConsignmentSaleReturnOrderDetailODTO consignmentSaleReturnOrderDetailODTO = saleReturnOrderService.queryConsignmentSaleReturnOrderItemList(13L);
        ConfirmConsignmentReturnOrderIDTO idto = new ConfirmConsignmentReturnOrderIDTO();
        idto.setConfirmId(100002L);
        idto.setId(13L);
        List<ConfirmConsignmentReturnOrdeItemIDTO> confirmSaleReturnOrderItems = Lists.newArrayList();
        ConfirmConsignmentReturnOrdeItemIDTO iddto = new ConfirmConsignmentReturnOrdeItemIDTO();
        iddto.setId(11L);
        iddto.setConfirmQuantity(new BigDecimal(2.88));
        confirmSaleReturnOrderItems.add(iddto);
        idto.setConfirmSaleReturnOrderItems(confirmSaleReturnOrderItems);
        Boolean aBoolean = saleReturnOrderService.confirmSaleReturnOrder(idto);
        System.out.println(aBoolean);

    }

    @Test
    @Rollback(false)
    public void testQueryConsignmentSaleReturnOrderItemList2(){
        ConsignmentSaleReturnOrderDetailODTO consignmentSaleReturnOrderDetailODTO = saleReturnOrderService.queryConsignmentSaleReturnOrderItemList(17L);
        System.out.println(consignmentSaleReturnOrderDetailODTO);

    }

    @Test
    @Rollback(false)
    public void testConsignmentReturnOrderlist(){
        ConsignmentSaleReturnOrderReqVo req  = new ConsignmentSaleReturnOrderReqVo();
        req.setStoreId("999872035591506747");
        PageInfo<ConsignmentSaleReturnOrderRespVo> consignmentSaleReturnOrderRespVoPageInfo = saleReturnOrderService.consignmentReturnOrderlist(req);
        System.out.println(consignmentSaleReturnOrderRespVoPageInfo);

    }


}
