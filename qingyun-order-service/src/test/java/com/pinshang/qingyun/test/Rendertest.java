package com.pinshang.qingyun.test;

import com.github.pagehelper.PageInfo;
import com.pinshang.qingyun.order.controller.PurchaseListController;
import com.pinshang.qingyun.order.mapper.entry.purchase.ConnectionPurchaseDetailEntry;
import com.pinshang.qingyun.order.vo.purchase.PurchaseListVo;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.restdocs.AutoConfigureRestDocs;

/**
 * @ClassName Refertest
 * <AUTHOR>
 * @Date 2022/9/26 16:50
 * @Description Refertest
 * @Version 1.0
 */
public class Rendertest extends AbstractJunitBase{
    @Autowired
    private PurchaseListController purchaseListController;

    @Test
    public void findConnectionPurchaseDetailTest(){
        PurchaseListVo vo = new PurchaseListVo();
        //vo.setSupplierId(161L);
        vo.setOrderDate("2022-09-24");
        PageInfo<ConnectionPurchaseDetailEntry> result = purchaseListController.findConnectionPurchaseDetail(vo);

        System.out.println("======================"+result.getList().toString());
    }

}
