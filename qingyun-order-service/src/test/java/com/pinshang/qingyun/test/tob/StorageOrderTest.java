package com.pinshang.qingyun.test.tob;

import com.pinshang.qingyun.base.enums.DeliveryOrderTypeEnums;
import com.pinshang.qingyun.base.enums.YesOrNoEnums;
import com.pinshang.qingyun.box.utils.DateUtil;
import com.pinshang.qingyun.order.dto.OrderItemMockDTO;
import com.pinshang.qingyun.order.dto.OrderMockDTO;
import com.pinshang.qingyun.order.model.order.SubOrder;
import com.pinshang.qingyun.order.service.SubOrderService;
import com.pinshang.qingyun.order.vo.OrderMockVO;
import com.pinshang.qingyun.test.AbstractJunitBase;
import lombok.extern.slf4j.Slf4j;

import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.annotation.Rollback;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

public class StorageOrderTest extends AbstractJunitBase {
    @Autowired
    private SubOrderService subOrderService;

    @Test
    @Rollback(false)
    public void createOrderTest() {
        List<Long> commodityIds = Arrays.asList(56095697583367000L,
                621083635535889536L,
                38386123835502304L,
                438143086454694400L,
                261830719339504928L,
                850466551156770560L,
                292986401891937024L,
                345139349439155712L,
                2580787591198700L,
                999965182752680984L,
                177037907001800L);
        List<OrderItemMockDTO> orderItemMockDTOS =new ArrayList<>();
        commodityIds.forEach(c->{
            OrderItemMockDTO orderItemMockDTO = new OrderItemMockDTO();
            orderItemMockDTO.setCommodityId(c);
            orderItemMockDTO.setOrderQuantity(BigDecimal.TEN);
            orderItemMockDTO.setOrderNumber(10);
            orderItemMockDTOS.add(orderItemMockDTO);
        });
        for (int i = 0; i < 2; i++) {
            Thread thread = new Thread(() -> {
                OrderMockDTO orderMockDTO = new OrderMockDTO();
                orderMockDTO.setStallId(1L);
                orderMockDTO.setStoreId(999872035591509085L);
                orderMockDTO.setBusinessType(DeliveryOrderTypeEnums.BIGSHOP_SALE.convert());
                orderMockDTO.setOrderTime(new Date());
                orderMockDTO.setPresaleStatus(YesOrNoEnums.NO.convert());
                orderMockDTO.setItems(orderItemMockDTOS);
                OrderMockVO mockOrder = subOrderService.createMockOrder(orderMockDTO);
                System.out.print(mockOrder);
            });
            thread.start();
        }

    }
}
