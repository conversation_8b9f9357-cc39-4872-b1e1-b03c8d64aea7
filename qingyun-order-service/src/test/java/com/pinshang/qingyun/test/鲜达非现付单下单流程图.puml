@startuml
skinparam backgroundColor white
skinparam handwritten false
skinparam defaultFontName "Microsoft YaHei"
skinparam activity {
  BackgroundColor #f0f8ff
  BorderColor #87ceeb
  FontColor black
  ArrowColor #4682b4
}
skinparam note {
  BackgroundColor #fffacd
  BorderColor #daa520
}
skinparam partition {
  BackgroundColor #e6e6fa
  BorderColor #9370db
}

title **鲜达非现付单下单流程**\n

start

:<&user> 客户端请求下单;

partition "**前置校验**" {
  :<&shield> 获取用户信息;

  if (是否游客?) then (是)
    #pink:<&circle-x> 抛出异常: 游客无法下单;
    stop
  endif

  if (是否有未支付订单?) then (是)
    #pink:<&circle-x> 抛出异常: 已有待支付订单;
    stop
  endif
  
  :<&shield> 校验客户状态;
  :<&clipboard> 校验下单数据;
  note right
    **校验项目:**
    <&check> 特价限购
    <&check> 结算条件
    <&check> 订货时间
    <&check> 订单金额
    <&check> 品种数量
    <&check> 商品数量
    <&check> 账户余额
    <&check> 下单次数
  end note
}

partition "**订单创建**" {
  :<&layers> 组装订单商品数据;
  note right: 按配送模式+仓库+供应商拆单
  
  :<&clock> 计算订单截止时间;
  if (是否超过订单截止时间?) then (是)
    #pink:<&circle-x> 抛出异常: 超时;
    stop
  endif
  
  :<&lock-locked> 冻结库存;
  
  if (库存冻结是否成功?) then (否)
    :<&lock-unlocked> 解冻库存;
    :<&action-undo> 返还优惠券;
    #pink:<&circle-x> 返回错误信息;
    stop
  endif

  partition "**保存订单数据**" {
    :<&file> 保存订单主表;
    note right
      <&check> 订单基本信息
      <&check> 订单金额
      <&check> 订单状态
      <&check> 配送信息
    end note

    :<&list> 保存订单明细;
    note right
      <&check> 商品信息
      <&check> 商品价格
      <&check> 商品数量
      <&check> 促销信息
    end note

    :<&gift> 保存赠品明细;
    :<&share> 保存子订单;
    :<&list-rich> 保存子订单明细;
    :<&file> 保存订单镜像;
  }

  :<&credit-card> 优惠券核销;
  
  if (是否预付款用户?) then (是)
    :<&dollar> 执行账户扣款;
  endif
}

:<&trash> 清空购物车;
:<&pencil> 记录订单日志;

partition "**事务后处理**" {
  fork
    :<&envelope-closed> 发送订单消息;
  fork again  
    :<&truck> 发送物流消息;
  fork again
    :<&share> 发送拆单消息;
  fork again
    :<&graph> 维护特价记录;
  fork again
    :<&calculator> 维护优惠统计;
  end fork
}

:<&check> 下单完成;

stop

@enduml
