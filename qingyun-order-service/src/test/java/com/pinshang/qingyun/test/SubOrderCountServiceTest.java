package com.pinshang.qingyun.test;

import com.pinshang.qingyun.base.api.TokenInfo;
import com.pinshang.qingyun.base.util.FastThreadLocalUtil;
import com.pinshang.qingyun.box.utils.DateUtil;
import com.pinshang.qingyun.order.mapper.SubOrderCountMapper;
import com.pinshang.qingyun.order.service.SubOrderCountService;
import com.pinshang.qingyun.order.vo.order.CountCommodityInfoVo;
import com.pinshang.qingyun.order.vo.order.CountCommoditySubmitVo;
import com.pinshang.qingyun.order.vo.order.OrderCountVo;
import com.pinshang.qingyun.order.vo.order.ShopOrderNumVo;
import org.junit.Test;
import org.reactivestreams.Publisher;
import org.junit.jupiter.api.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.annotation.Commit;
import org.springframework.test.annotation.Rollback;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.when;

@TestMethodOrder(MethodOrderer.MethodName.class)
public class SubOrderCountServiceTest extends AbstractJunitBase {

    @Autowired
    private SubOrderCountService subOrderCountService;
    @MockBean
    SubOrderCountMapper subOrderCountMapper;


    @Test
    public void shopOrderNum() {
        TokenInfo token = new TokenInfo();
        token.setShopId(16L);
        token.setUserId(51362L);
        FastThreadLocalUtil.setQY(token);
        ShopOrderNumVo vo = subOrderCountService.shopOrderNum();
        System.out.println(vo);
    }

    @Test
    public void countCommodityInfo() {

        TokenInfo token = new TokenInfo();
        token.setShopId(16L);
        token.setUserId(51361L);
        FastThreadLocalUtil.setQY(token);

//        String barCode = "043548";
//        CountCommodityInfoVo vo = subOrderCountService.countCommodityInfo(barCode);
//        System.out.println(vo);
        String barCode = "204354800956009108";
        CountCommodityInfoVo vo1 = subOrderCountService.countCommodityInfo(barCode);
        System.out.println(vo1);
    }

    @Test
    @Commit
    public void submitCountCommodity() {
        TokenInfo token = new TokenInfo();
        token.setShopId(16L);
        token.setUserId(51361L);
        token.setStoreId(999872035591509085L);
        FastThreadLocalUtil.setQY(token);

        CountCommoditySubmitVo vo = new CountCommoditySubmitVo();
        vo.setCommodityId(999965182752756151L);
        vo.setAdjustNum(BigDecimal.TEN);
        vo.setType(3);
        subOrderCountService.submitCountCommodity(vo);
    }

    @Test
    @Rollback()
    public void submitCountMultiCommodity() {
        TokenInfo token = new TokenInfo();
        token.setShopId(16L);
        token.setUserId(51361L);
        token.setStoreId(999872035591509085L);
        FastThreadLocalUtil.setQY(token);

        OrderCountVo a = new OrderCountVo();
        a.setCommodityId(56095697583367000L);
        a.setReserveNum(BigDecimal.TEN);

        OrderCountVo b = new OrderCountVo();
        b.setCommodityId(999965182752756151L);
        b.setReserveNum(BigDecimal.TEN);
        List<OrderCountVo> subOrderItemInfo = new ArrayList<>();
        subOrderItemInfo.add(a);
        subOrderItemInfo.add(b);

        CountCommoditySubmitVo vo = new CountCommoditySubmitVo();
        vo.setCommodityId(56095697583367000L);
        vo.setAdjustNum(BigDecimal.ONE);

        CountCommoditySubmitVo vo1 = new CountCommoditySubmitVo();
        vo1.setCommodityId(999965182752756151L);
        vo1.setAdjustNum(BigDecimal.ONE);

        List<CountCommoditySubmitVo> list = new ArrayList<>();
        list.add(vo);
        list.add(vo1);

        when(subOrderCountMapper.subOrderItemInfo(anyLong(), anyString(), anyList())).thenReturn(subOrderItemInfo);

        subOrderCountService.submitCountMultiCommodity(list);
    }


}
