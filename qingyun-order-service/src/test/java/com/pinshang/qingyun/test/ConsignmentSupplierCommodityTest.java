package com.pinshang.qingyun.test;

import com.pinshang.qingyun.base.configure.expand.QYAssert;
import com.pinshang.qingyun.box.utils.SpringUtil;
import com.pinshang.qingyun.order.vo.order.SaleReturnAddVo;
import com.pinshang.qingyun.order.vo.order.SaleReturnItemAddVo;
import com.pinshang.qingyun.shop.admin.dto.ConsignmentSupplierInfoODTO;
import com.pinshang.qingyun.shop.admin.service.ConsignmentSupplierClient;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2024/06/13
 * @Version 1.0
 */
public class ConsignmentSupplierCommodityTest extends AbstractJunitBase{
    @Autowired
    private ConsignmentSupplierClient consignmentSupplierClient;
    @Test
    public void checkConsignmentSupplierCommodity(){
        SaleReturnAddVo vo = new SaleReturnAddVo();
        vo.setShopId(16L);
        List<SaleReturnItemAddVo> saleReturnItems = new ArrayList<>();
        SaleReturnItemAddVo vo1 = new SaleReturnItemAddVo();
        vo1.setCommodityId("2029110267185300");
        vo1.setCommodityName("111");
        saleReturnItems.add(vo1);
        SaleReturnItemAddVo vo2 = new SaleReturnItemAddVo();
        vo2.setCommodityId("999872035591506747");
        vo2.setCommodityName("222");
        saleReturnItems.add(vo2);
        vo.setSaleReturnItems(saleReturnItems);
        List<ConsignmentSupplierInfoODTO> consignmentCommodityList = consignmentSupplierClient.queryByShopId(vo.getShopId());
        if(SpringUtil.isNotEmpty(consignmentCommodityList)){
            List<Long> consignmentCommodityIdList = consignmentCommodityList.stream()
                    .map(ConsignmentSupplierInfoODTO::getCommodityId)
                    .distinct()
                    .collect(Collectors.toList());
            List<String> consignmentCommodityNameList = vo.getSaleReturnItems().stream()
                    .filter(it -> consignmentCommodityIdList.contains(Long.parseLong(it.getCommodityId())))
                    .map(SaleReturnItemAddVo::getCommodityName)
                    .collect(Collectors.toList());
            QYAssert.isTrue(SpringUtil.isEmpty(consignmentCommodityNameList), "存在代销商品:" + String.join(",",consignmentCommodityNameList));
        }
    }
}
