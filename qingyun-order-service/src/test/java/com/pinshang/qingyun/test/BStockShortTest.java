package com.pinshang.qingyun.test;

import com.pinshang.qingyun.order.service.BStockShortService;
import com.pinshang.qingyun.order.vo.BStockLackVO;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2024/03/13
 * @Version 1.0
 */
public class BStockShortTest extends AbstractJunitBase{
    @Autowired
    private BStockShortService bStockShortService;

    @Test
    public void stockShortTest(){
        List<BStockLackVO> voList = new ArrayList<>();
        BStockLackVO vo = new BStockLackVO();
        vo.setStockType(null);
        vo.setStockQuantity(new BigDecimal("10.011"));
        vo.setRemark("remarks");
        vo.setCommodityId(37027L);
        vo.setCreateId(1L);
        vo.setNeedQuantity(new BigDecimal("20.231"));
        vo.setOrderType(1);
        vo.setStoreId(620957946566100L);
        voList.add(vo);
        bStockShortService.stockShort(voList);
    }

}
