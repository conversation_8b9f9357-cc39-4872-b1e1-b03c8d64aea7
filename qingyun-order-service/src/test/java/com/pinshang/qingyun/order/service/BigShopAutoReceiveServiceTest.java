package com.pinshang.qingyun.order.service;/**
 * @Author: sk
 * @Date: 2025/8/13
 */

import com.github.pagehelper.PageInfo;
import com.pinshang.qingyun.ApplicationOrderService;
import com.pinshang.qingyun.infrastructure.springcloud.common.ComponentConfigLoad;
import com.pinshang.qingyun.infrastructure.springcloud.common.ComponentManageConfig;
import com.pinshang.qingyun.infrastructure.springcloud.common.event.ComponentListener;
import com.pinshang.qingyun.infrastructure.test.BaseDbUnitTest;
import com.pinshang.qingyun.order.dto.bigShop.DdReceiveDocODTO;
import com.pinshang.qingyun.order.dto.bigShop.DdReceiveDocPageIDTO;
import com.pinshang.qingyun.order.mapper.ShopReceiveOrderMapper;
import com.pinshang.qingyun.order.mapper.SubOrderItemMapper;
import com.pinshang.qingyun.order.mapper.bigShop.DdReceiveDocLogMapper;
import com.pinshang.qingyun.order.mapper.bigShop.DdReceiveDocMapper;
import com.pinshang.qingyun.order.mapper.bigShop.DdReceiveDocOrderMapper;
import com.pinshang.qingyun.order.model.bigShop.DdReceiveDoc;
import com.pinshang.qingyun.order.model.bigShop.DdReceiveDocLog;
import com.pinshang.qingyun.order.model.bigShop.DdReceiveDocOrder;
import com.pinshang.qingyun.order.model.order.SubOrderItem;
import com.pinshang.qingyun.order.model.shop.ShopReceiveOrder;
import com.pinshang.qingyun.order.service.bigShop.BigShopAutoReceiveDetailService;
import com.pinshang.qingyun.order.service.bigShop.BigShopAutoReceiveService;
import com.pinshang.qingyun.order.service.bigShop.BigShopReceiveService;
import com.pinshang.qingyun.renderer.service.IRenderService;
import com.pinshang.qingyun.shop.dto.bigShop.StallODTO;
import com.pinshang.qingyun.shop.service.bigShop.StallClient;
import com.pinshang.qingyun.smm.service.UserStallClient;
import com.pinshang.qingyun.xd.wms.service.XdStockClient;
import org.junit.jupiter.api.Test;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.context.annotation.Import;
import org.springframework.mail.javamail.JavaMailSenderImpl;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

import static org.junit.Assert.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

/**
 * <AUTHOR>
 * @date 2025年08月13日 上午11:04
 */
@SpringBootTest(classes = {ApplicationOrderService.class, BaseDbUnitTest.Application.class},
        properties = {"application.name.switch=unit-test-","pinshang.img-server-url=xxx",
                "pinshang.img-xd-server-url=xxx","print.path.rootPath=xxx","print.path.upPath=xxx",
                "print.path.url=xxx","pinshang.xda.isThTips=xxx"})
@Import(value = {BigShopAutoReceiveService.class})
public class BigShopAutoReceiveServiceTest extends BaseDbUnitTest{

    @MockBean
    IRenderService renderService;
    @MockBean
    RLock lock;
    @MockBean
    RedissonClient redissonClient;
    @MockBean
    private JavaMailSenderImpl javaMailSender;
    @MockBean
    ComponentManageConfig componentManageConfig;
    @MockBean
    ComponentListener componentListener;
    @MockBean
    ComponentConfigLoad componentConfigLoad;
    @MockBean
    XdStockClient xdStockClient;
    @MockBean
    private UserStallClient userStallClient;
    @MockBean
    StallClient stallClient;

    @Autowired
    private BigShopAutoReceiveService bigShopAutoReceiveService;
    @Autowired
    private BigShopAutoReceiveDetailService bigShopAutoReceiveDetailService;
    @Autowired
    private DdReceiveDocMapper ddReceiveDocMapper;
    @Autowired
    private DdReceiveDocOrderMapper ddReceiveDocOrderMapper;
    @Autowired
    private DdReceiveDocLogMapper ddReceiveDocLogMapper;
    @Autowired
    private SubOrderItemMapper subOrderItemMapper;
    @Autowired
    private ShopReceiveOrderMapper shopReceiveOrderMapper;
    @Autowired
    private BigShopReceiveService bigShopReceiveService;

    @Override
    protected List<String> getInitSqlScripts() {
        // 指定自定义初始化脚本
        return Arrays.asList(
                "db/autoReceiveOrder.sql"
        );
    }


    @Test
    public void getDdAutoShopIdList() {
        // 获取大店
        List<Long> shopIdList = bigShopAutoReceiveService.getDdAutoShopIdList();

        assertEquals("大店数量大于0",shopIdList.size() > 0, true);
        assertEquals("包含门店100052",shopIdList.contains(100052L), true);
    }


    @Test
    public void getDdReceiveDocs() {
        // 获取收货单
        List<Long> shopIdList = new ArrayList<>();
        shopIdList.add(100052L);
        List<DdReceiveDoc> ddReceiveDocs = bigShopAutoReceiveService.getDdReceiveDocs("2025-08-13", shopIdList);

        assertEquals("收货单个数",ddReceiveDocs.size() > 0, true);

        assertEquals("收货单内容",ddReceiveDocs.get(0).getDeliveryBatch().equals(1), true);
    }


    @Test
    public void bigShopAutoReceiveDetail() {

        DdReceiveDoc ddReceiveDoc = ddReceiveDocMapper.selectByPrimaryKey(38L);
        assertEquals("收货单不为空",ddReceiveDoc != null, true);

        // 自动收货操作
        bigShopAutoReceiveDetailService.bigShopAutoReceiveDetail(ddReceiveDoc);


        // 验证自动收货数据是否正常
        DdReceiveDoc newDdReceiveDoc = ddReceiveDocMapper.selectByPrimaryKey(38L);
        assertEquals("收货单更新为已经完成收货", newDdReceiveDoc.getDocStatus().equals(1), true);

        DdReceiveDocLog ddReceiveDocLog = new DdReceiveDocLog();
        ddReceiveDocLog.setDocId(ddReceiveDoc.getId());
        List<DdReceiveDocLog> logList = ddReceiveDocLogMapper.select(ddReceiveDocLog);
        assertEquals("收货单日志数量等于2",logList.size() == 2, true);

        logList.forEach(log -> {
            if(log.getCommodityId().equals(850466551156770560L)) {
                assertEquals("850466551156770560 实收",log.getRealReceiveQuantity().compareTo(new BigDecimal("1")) == 0, true);
            }

            if(log.getCommodityId().equals(32289764792662004L)) {
                assertEquals("32289764792662004 实收",log.getRealReceiveQuantity().compareTo(new BigDecimal("1")) == 0, true);
            }
        });

        DdReceiveDocOrder ddReceiveDocOrder = new DdReceiveDocOrder();
        ddReceiveDocOrder.setDocId(ddReceiveDoc.getId());
        List<DdReceiveDocOrder> ddReceiveDocOrderList = ddReceiveDocOrderMapper.select(ddReceiveDocOrder);
        assertEquals("收货单订单记录表",ddReceiveDocOrderList.size() == 1, true);

        List<String> orderCodeList = ddReceiveDocOrderList.stream().map(item -> item.getOrderCode()).collect(Collectors.toList());
        assertEquals("收货单订单记录表2,订单已记录 ",orderCodeList.contains("1754471538629852591"), true);


        SubOrderItem subOrderItem = new SubOrderItem();
        subOrderItem.setSubOrderId(197233159L);
        List<SubOrderItem> subOrderItems = subOrderItemMapper.select(subOrderItem);
        subOrderItems.forEach(item -> {
            assertEquals("子单实收数量 ",item.getRealReceiveQuantity().compareTo(new BigDecimal("1")) == 0, true);
        });


        ShopReceiveOrder shopReceiveOrder = new ShopReceiveOrder();
        shopReceiveOrder.setSubOrderId(197233159L);
        List<ShopReceiveOrder> shopReceiveOrderList = shopReceiveOrderMapper.select(shopReceiveOrder);
        assertEquals("子单收货单 已经完成收货 ",shopReceiveOrderList.get(0).getStatus().equals(3), true);

    }

    @Test
    public void testQueryPage() {
        List<StallODTO> stallODTOList = new ArrayList<>();
        StallODTO stallODTO = new StallODTO();
        stallODTO.setId(16L);
        stallODTO.setStallName("test");
        stallODTOList.add(stallODTO);
        when(stallClient.queryStallByIds(any())).thenReturn(stallODTOList);

        List<Long> stallIdList = new ArrayList<>();
        stallIdList.add(16L);
        when(userStallClient.selectUserStallIdList(any())).thenReturn(stallIdList);

        DdReceiveDocPageIDTO idto = new DdReceiveDocPageIDTO();
        idto.setShopId(100052L);
        idto.setOrderTime("2025-08-13");
        idto.setPageNo(1);
        idto.setPageSize(20);
        idto.setOrderCode("1754471538629852591");
        PageInfo<DdReceiveDocODTO> pageDate = bigShopReceiveService.getReceiveDocList(idto);

        assertEquals("收货单数量大于0",pageDate.getList().size() > 0, true);
    }

}
