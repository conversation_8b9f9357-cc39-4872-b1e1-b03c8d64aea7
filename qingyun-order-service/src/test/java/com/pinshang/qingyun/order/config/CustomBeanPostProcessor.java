package com.pinshang.qingyun.order.config;

import com.pinshang.qingyun.base.configure.FeignClientProperties;
import org.springframework.beans.factory.config.BeanPostProcessor;

import org.springframework.stereotype.Component;

@Component
public class CustomBeanPostProcessor implements BeanPostProcessor {

    @Override
    public Object postProcessBeforeInitialization(Object bean, String beanName) {
        if(beanName.equals("pinshang.service-com.pinshang.qingyun.base.configure.FeignClientProperties")){
            FeignClientProperties bean1 = (FeignClientProperties) bean;
            bean1.setUsername("testUser");
            bean1.setPassword("testPw");
            return bean1;
        }
        return bean;
    }

    @Override
    public Object postProcessAfterInitialization(Object bean, String beanName) {
        if(beanName.equals("pinshang.service-com.pinshang.qingyun.base.configure.FeignClientProperties")){
            System.out.println(bean);
        }
        return bean;
    }
}
