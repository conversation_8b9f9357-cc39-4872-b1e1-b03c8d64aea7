package com.pinshang.qingyun.order.config;

import org.mockito.Mockito;
import org.redisson.api.RedissonClient;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.FactoryBean;
import org.springframework.beans.factory.config.BeanDefinition;
import org.springframework.beans.factory.config.ConfigurableListableBeanFactory;
import org.springframework.beans.factory.support.BeanDefinitionRegistry;
import org.springframework.beans.factory.support.BeanDefinitionRegistryPostProcessor;
import org.springframework.beans.factory.support.GenericBeanDefinition;
import org.springframework.cloud.openfeign.FeignClient;
import org.reflections.Reflections;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;
import org.springframework.aop.support.AopUtils;


@Component
public class ClientMockRegistrar implements BeanDefinitionRegistryPostProcessor {
    @Override
    public void postProcessBeanDefinitionRegistry(BeanDefinitionRegistry registry) {
        // 注册FeignClient接口的Mock对象
        new Reflections("com.pinshang.qingyun")
                .getTypesAnnotatedWith(FeignClient.class)
                .forEach(clazz -> registerMockBean(registry, clazz));

        // 注册RedissonClient的Mock对象
        registerMockBean(registry, RedissonClient.class);

        GenericBeanDefinition definition = new GenericBeanDefinition();
        definition.setBeanClass(MockitoFactoryBean.class);
        definition.getConstructorArgumentValues()
                .addGenericArgumentValue(RedissonClient.class);
        registry.registerBeanDefinition("infrastructureRedissonClient", definition);


    }

    private void registerMockBean(BeanDefinitionRegistry registry, Class<?> targetClazz) {

        Class<?> targetClass = AopUtils.isAopProxy(targetClazz)
                ? AopUtils.getTargetClass(targetClazz)
                : targetClazz;

        if(StringUtils.hasText(targetClass.getSimpleName())){
            String beanName = StringUtils.uncapitalize(targetClass.getSimpleName());
            GenericBeanDefinition definition = new GenericBeanDefinition();

            // 使用FactoryBean模式注册原始类型
            definition.setBeanClass(MockitoFactoryBean.class);
            definition.getConstructorArgumentValues()
                    .addGenericArgumentValue(targetClass);

            registry.registerBeanDefinition(beanName, definition);
        }

    }

    @Override
    public void postProcessBeanFactory(ConfigurableListableBeanFactory beanFactory) {}
}

class MockitoFactoryBean implements FactoryBean<Object> {
    private final Class<?> targetType;

    public MockitoFactoryBean(Class<?> targetType) {
        this.targetType = targetType;
    }

    @Override
    public Object getObject() {
        return Mockito.mock(targetType);
    }

    @Override
    public Class<?> getObjectType() {
        return targetType;
    }
}
