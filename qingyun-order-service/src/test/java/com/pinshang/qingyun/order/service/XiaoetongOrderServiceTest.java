package com.pinshang.qingyun.order.service;/**
 * @Author: sk
 * @Date: 2025/7/10
 */

import com.pinshang.qingyun.ApplicationOrderService;
import com.pinshang.qingyun.infrastructure.springcloud.common.ComponentManageConfig;
import com.pinshang.qingyun.infrastructure.test.BaseDbUnitTest;
import com.pinshang.qingyun.order.dto.order.XiaoeTongPushOrderIDTO;
import com.pinshang.qingyun.order.service.order.XiaoetongOrderService;
import com.pinshang.qingyun.renderer.service.IRenderService;
import org.junit.jupiter.api.Test;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.context.annotation.Import;
import org.springframework.mail.javamail.JavaMailSenderImpl;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025年07月10日 上午10:26
 */
@SpringBootTest(classes = {ApplicationOrderService.class, BaseDbUnitTest.Application.class},
        properties = {"application.name.switch=unit-test-","pinshang.img-server-url=xxx",
                "pinshang.img-xd-server-url=xxx","print.path.rootPath=xxx","print.path.upPath=xxx",
                "print.path.url=xxx","pinshang.xda.isThTips=xxx"})
@Import(value = {XiaoetongOrderService.class})
public class XiaoetongOrderServiceTest extends BaseDbUnitTest {

    @Autowired
    private XiaoetongOrderService xiaoetongOrderService;

    @MockBean
    IRenderService renderService;
    @MockBean
    RLock lock;
    @MockBean
    RedissonClient redissonClient;
    @MockBean
    private JavaMailSenderImpl javaMailSender;
    @MockBean
    ComponentManageConfig componentManageConfig;

    @Override
    protected List<String> getInitSqlScripts() {
        // 指定自定义初始化脚本
        return Arrays.asList(
                "db/xiaoetong-schema.sql"
        );
    }

    @Test
    public void createXiaoeTongOrder(){
        List<XiaoeTongPushOrderIDTO> xiaoeTongPushOrderList = new ArrayList<>();
    }
}
