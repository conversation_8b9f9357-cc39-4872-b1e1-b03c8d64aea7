package com.pinshang.qingyun.order.service;/**
 * @Author: sk
 * @Date: 2025/8/18
 */

import com.pinshang.qingyun.ApplicationOrderService;
import com.pinshang.qingyun.base.util.FastThreadLocalUtil;
import com.pinshang.qingyun.common.service.DelayMsgClient;
import com.pinshang.qingyun.infrastructure.springcloud.common.ComponentConfigLoad;
import com.pinshang.qingyun.infrastructure.springcloud.common.ComponentManageConfig;
import com.pinshang.qingyun.infrastructure.springcloud.common.event.ComponentListener;
import com.pinshang.qingyun.infrastructure.test.BaseDbUnitTest;
import com.pinshang.qingyun.order.dto.bigShop.*;
import com.pinshang.qingyun.order.mapper.ShopReceiveOrderMapper;
import com.pinshang.qingyun.order.mapper.SubOrderItemMapper;
import com.pinshang.qingyun.order.mapper.bigShop.*;
import com.pinshang.qingyun.order.model.bigShop.*;
import com.pinshang.qingyun.order.model.order.SubOrderItem;
import com.pinshang.qingyun.order.model.shop.ShopReceiveOrder;
import com.pinshang.qingyun.order.service.bigShop.BigShopReceiveService;
import com.pinshang.qingyun.order.vo.order.SaleReturnOrderPicVO;
import com.pinshang.qingyun.renderer.service.IRenderService;
import com.pinshang.qingyun.shop.service.bigShop.StallClient;
import com.pinshang.qingyun.smm.service.UserStallClient;
import com.pinshang.qingyun.xd.wms.dto.bigShop.GoodsAllocationODTO;
import com.pinshang.qingyun.xd.wms.service.XdStockClient;
import com.pinshang.qingyun.xd.wms.service.bigShop.GoodsAllocationClient;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.context.annotation.Import;
import org.springframework.mail.javamail.JavaMailSenderImpl;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

import static org.junit.Assert.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.when;

/**
 * <AUTHOR>
 * @date 2025年08月18日 下午4:26
 */
@SpringBootTest(classes = {ApplicationOrderService.class, BaseDbUnitTest.Application.class},
        properties = {"application.name.switch=unit-test-","pinshang.img-server-url=xxx",
                "pinshang.img-xd-server-url=xxx","print.path.rootPath=xxx","print.path.upPath=xxx",
                "print.path.url=xxx","pinshang.xda.isThTips=xxx"})
@Import(value = {BigShopReceiveService.class})
//@Sql(scripts = "/db/bigShopReceiveOrder.sql", executionPhase = Sql.ExecutionPhase.BEFORE_TEST_METHOD)
public class BigShopReceiveServiceTest extends BaseDbUnitTest{

    @MockBean
    IRenderService renderService;
    @MockBean
    RLock lock;
    @MockBean
    RedissonClient redissonClient;
    @MockBean
    private JavaMailSenderImpl javaMailSender;
    @MockBean
    ComponentManageConfig componentManageConfig;
    @MockBean
    ComponentListener componentListener;
    @MockBean
    ComponentConfigLoad componentConfigLoad;
    @MockBean
    XdStockClient xdStockClient;
    @MockBean
    private UserStallClient userStallClient;
    @MockBean
    StallClient stallClient;
    @MockBean
    GoodsAllocationClient goodsAllocationClient;
    @MockBean
    private DelayMsgClient delayMsgClient;
    @Autowired
    private BigShopReceiveService bigShopReceiveService;
    @Autowired
    private DdReceiveDocLogMapper ddReceiveDocLogMapper;
    @Autowired
    private SubOrderItemMapper subOrderItemMapper;
    @Autowired
    private ShopReceiveOrderMapper shopReceiveOrderMapper;
    @Autowired
    private DdReceiveDocOrderMapper ddReceiveDocOrderMapper;
    @Autowired
    private DdReceiveDocPicMapper ddReceiveDocPicMapper;
    @Autowired
    private DdReceiveDocRecordMapper ddReceiveDocRecordMapper;
    @Autowired
    private DdReceiveDocMapper ddReceiveDocMapper;
    @MockBean
    private ConsignmentSupplierService consignmentSupplierService;

    @Override
    protected List<String> getInitSqlScripts() {
        // 指定自定义初始化脚本：先清理，再初始化数据
        return Arrays.asList(
                "db/clean.sql",
                "db/bigShopReceiveOrder.sql"
        );
    }

    @BeforeEach
    public void setUp() {
        // Mock档口商品校验，默认通过
        doNothing().when(consignmentSupplierService).checkStallCommodity(any(), any(), any());
    }


    @Test
    public void getReceiveDocCommodityList() {
        List<GoodsAllocationODTO> stallODTOList = new ArrayList<>();
        when(goodsAllocationClient.queryGoodsAllocationByIdList(any())).thenReturn(stallODTOList);

        DdReceiveDocODTO ddReceiveDocODTO = bigShopReceiveService.getReceiveDocCommodityList(41L, null, false);

        assertEquals("收货明细",ddReceiveDocODTO != null && ddReceiveDocODTO.getCommodityList().size() > 0, true);

        ddReceiveDocODTO.getCommodityList().forEach(commodityODTO -> {
            if(commodityODTO.getCommodityId().equals(32289764792662004L)) {
                assertEquals("是否PDA收货",commodityODTO.getPdaStatus() == 1, true);

                assertEquals("图片list",commodityODTO.getPicList().size() > 0, true);
            }
        });
    }

    @Test
    public void addBigShopReceive() {
        Long docId = 41L;
        DdReceiveDocSaveIDTO idto = new DdReceiveDocSaveIDTO();
        idto.setDocId(docId);
        idto.setUserId(FastThreadLocalUtil.getQY().getUserId());

        List<DdReceiveDocCommodityODTO> commodityList = new ArrayList<>();
        DdReceiveDocCommodityODTO commodity1 = new DdReceiveDocCommodityODTO();
        commodity1.setCommodityId("32289764792662004");
        commodity1.setCommodityCode("000171");
        commodity1.setCommodityName("大油豆腐");
        commodity1.setStorageArea(1);
        commodity1.setRealReceiveQuantity(new BigDecimal("9"));
        commodity1.setRealDeliveryQuantity(new BigDecimal("10"));

        List<SaleReturnOrderPicVO> picList = new ArrayList<>();
        SaleReturnOrderPicVO pic = new SaleReturnOrderPicVO();
        pic.setPicUrl("https://picsum.photos/200/300");
        picList.add( pic);
        commodity1.setPicList(picList);
        commodityList.add(commodity1);


        DdReceiveDocCommodityODTO commodity2 = new DdReceiveDocCommodityODTO();
        commodity2.setCommodityId("864419333914674304");
        commodity2.setStorageArea(1);
        commodity2.setCommodityCode("000072");
        commodity2.setCommodityName("素肠");
        commodity2.setRealReceiveQuantity(new BigDecimal("1"));
        commodity2.setRealDeliveryQuantity(new BigDecimal("1"));
        commodityList.add(commodity2);

        idto.setCommodityList(commodityList);
        bigShopReceiveService.addBigShopReceive(idto);



        // 验证结果

        DdReceiveDocLog ddReceiveDocLog = new DdReceiveDocLog();
        ddReceiveDocLog.setDocId(docId);
        List<DdReceiveDocLog> logList = ddReceiveDocLogMapper.select(ddReceiveDocLog);
        assertEquals("收货单日志数量等于2",logList.size() == 2, true);

        logList.forEach(log -> {
            if(log.getCommodityId().equals(32289764792662004L)) {
                assertEquals("32289764792662004 实收",log.getRealReceiveQuantity().compareTo(new BigDecimal("9")) == 0, true);
            }

            if(log.getCommodityId().equals(864419333914674304L)) {
                assertEquals("864419333914674304 实收",log.getRealReceiveQuantity().compareTo(new BigDecimal("1")) == 0, true);
            }
        });

        DdReceiveDocOrder ddReceiveDocOrder = new DdReceiveDocOrder();
        ddReceiveDocOrder.setDocId(docId);
        List<DdReceiveDocOrder> ddReceiveDocOrderList = ddReceiveDocOrderMapper.select(ddReceiveDocOrder);
        assertEquals("收货单订单记录表",ddReceiveDocOrderList.size() == 1, true);

        List<String> orderCodeList = ddReceiveDocOrderList.stream().map(item -> item.getOrderCode()).collect(Collectors.toList());
        assertEquals("收货单订单记录表2,订单已记录 ",orderCodeList.contains("1755505872342396283"), true);


        SubOrderItem subOrderItem = new SubOrderItem();
        subOrderItem.setSubOrderId(197233293L);
        List<SubOrderItem> subOrderItems = subOrderItemMapper.select(subOrderItem);
        subOrderItems.forEach(item -> {

            if(item.getCommodityId().equals(32289764792662004L)) {
                assertEquals("子单实收数量 32289764792662004 实收",item.getRealReceiveQuantity().compareTo(new BigDecimal("9")) == 0, true);
            }

            if(item.getCommodityId().equals(864419333914674304L)) {
                assertEquals("子单实收数量 864419333914674304 实收",item.getRealReceiveQuantity().compareTo(new BigDecimal("1")) == 0, true);
            }
        });


        ShopReceiveOrder shopReceiveOrder = new ShopReceiveOrder();
        shopReceiveOrder.setSubOrderId(197233293L);
        List<ShopReceiveOrder> shopReceiveOrderList = shopReceiveOrderMapper.select(shopReceiveOrder);
        assertEquals("子单收货单 已经完成收货 ",shopReceiveOrderList.get(0).getStatus().equals(3), true);

        DdReceiveDocPic ddReceiveDocPic = new DdReceiveDocPic();
        ddReceiveDocPic.setDocId(docId);
        ddReceiveDocPic.setReceiveType(2);
        List<DdReceiveDocPic> pics = ddReceiveDocPicMapper.select(ddReceiveDocPic);
        assertEquals("收货图片",pics.size() == 1, true);

    }

    @Test
    public void testRecordSingleCommodity_NewRecord() {
        // 测试新增记录的情况
        DdReceiveDocRecordIDTO idto = new DdReceiveDocRecordIDTO();
        idto.setShopId(100052L);
        idto.setStallId(16L);
        idto.setDocId(41L);
        idto.setCommodityId(864419333914674304L); // 使用测试数据中不存在记录的商品ID
        idto.setRealReceiveQuantity(new BigDecimal("1.000"));
        idto.setStorageArea(1);
        idto.setGoodsAllocationId(1001L);
        idto.setRemark("测试新增记录");

        // 添加图片
        List<SaleReturnOrderPicVO> picList = new ArrayList<>();
        SaleReturnOrderPicVO pic = new SaleReturnOrderPicVO();
        pic.setPicUrl("https://test.com/pic1.jpg");
        picList.add(pic);
        idto.setPicList(picList);

        // 添加视频
        List<SaleReturnOrderPicVO> videoList = new ArrayList<>();
        SaleReturnOrderPicVO video = new SaleReturnOrderPicVO();
        video.setPicUrl("https://test.com/video1.mp4");
        videoList.add(video);
        idto.setVideoList(videoList);

        Boolean result = bigShopReceiveService.recordSingleCommodity(idto);

        // 验证结果
        assertEquals("登记成功", true, result);

        // 验证记录是否插入
        DdReceiveDocRecord record = new DdReceiveDocRecord();
        record.setDocId(41L);
        record.setCommodityId(864419333914674304L);



        List<DdReceiveDocRecord> records = ddReceiveDocRecordMapper.select(record);
        Assertions.assertEquals(1, records.size(), "记录已插入");
        Assertions.assertEquals(0, records.get(0).getRealReceiveQuantity().compareTo(new BigDecimal("1.000")), "实收数量正确");
        Assertions.assertEquals(Integer.valueOf(1), records.get(0).getStorageArea(), "库区正确");
        Assertions.assertEquals(Long.valueOf(1001L), records.get(0).getGoodsAllocationId(), "货位正确");
        Assertions.assertEquals("测试新增记录", records.get(0).getRemark(), "备注正确");

        // 验证图片是否插入
        DdReceiveDocPic picQuery = new DdReceiveDocPic();
        picQuery.setDocId(41L);
        picQuery.setCommodityId(864419333914674304L);
        List<DdReceiveDocPic> pics = ddReceiveDocPicMapper.select(picQuery);
        Assertions.assertEquals(2, pics.size(), "图片和视频已插入");
    }

    @Test
    public void testRecordSingleCommodity_UpdateRecord() {
        // 测试更新已存在记录的情况
        DdReceiveDocRecordIDTO idto = new DdReceiveDocRecordIDTO();
        idto.setShopId(100052L);
        idto.setStallId(16L);
        idto.setDocId(41L);
        idto.setCommodityId(32289764792662004L); // 使用测试数据中已存在记录的商品ID
        idto.setRealReceiveQuantity(new BigDecimal("1.000")); // 更新数量
        idto.setStorageArea(2); // 更新库区
        idto.setGoodsAllocationId(2001L); // 更新货位
        idto.setRemark("测试更新记录");

        // 添加新图片
        List<SaleReturnOrderPicVO> picList = new ArrayList<>();
        SaleReturnOrderPicVO pic = new SaleReturnOrderPicVO();
        pic.setPicUrl("https://test.com/updated_pic.jpg");
        picList.add(pic);
        idto.setPicList(picList);

        Boolean result = bigShopReceiveService.recordSingleCommodity(idto);

        // 验证结果
        assertEquals("更新成功", true, result);

        // 验证记录是否更新
        DdReceiveDocRecord record = new DdReceiveDocRecord();
        record.setDocId(41L);
        record.setCommodityId(32289764792662004L);
        List<DdReceiveDocRecord> records = ddReceiveDocRecordMapper.select(record);
        assertEquals("记录存在", 1, records.size());
        assertEquals("实收数量已更新", 0, records.get(0).getRealReceiveQuantity().compareTo(new BigDecimal("1.000")));
        assertEquals("库区已更新", Integer.valueOf(2), records.get(0).getStorageArea());
        assertEquals("货位已更新", Long.valueOf(2001L), records.get(0).getGoodsAllocationId());
        assertEquals("备注已更新", "测试更新记录", records.get(0).getRemark());

        // 验证旧图片被删除，新图片被插入
        DdReceiveDocPic picQuery = new DdReceiveDocPic();
        picQuery.setDocId(41L);
        picQuery.setCommodityId(32289764792662004L);
        List<DdReceiveDocPic> pics = ddReceiveDocPicMapper.select(picQuery);
        // 应该只有新插入的图片
        boolean hasUpdatedPic = pics.stream().anyMatch(p -> "https://test.com/updated_pic.jpg".equals(p.getPicUrl()));
        assertEquals("新图片已插入", true, hasUpdatedPic);
    }

    @Test
    public void testPdaBigShopReceive_Success() {
        // 准备测试数据 - 模拟货位信息
        List<GoodsAllocationODTO> goodsAllocationList = new ArrayList<>();
        GoodsAllocationODTO allocation1 = new GoodsAllocationODTO();
        allocation1.setId(1001L);
//        allocation1.setGoodsAllocationName("A-01-01");
        goodsAllocationList.add(allocation1);

        GoodsAllocationODTO allocation2 = new GoodsAllocationODTO();
        allocation2.setId(2001L);
//        allocation2.setGoodsAllocationName("B-01-01");
        goodsAllocationList.add(allocation2);

        when(goodsAllocationClient.queryGoodsAllocationByIdList(any())).thenReturn(goodsAllocationList);

        // 创建整单收货请求
        DdReceiveDocBatchRecordIDTO idto = new DdReceiveDocBatchRecordIDTO();
        idto.setDocId(41L);

        Boolean result = bigShopReceiveService.pdaBigShopReceive(idto);

        // 验证结果
        assertEquals("整单收货成功", true, result);

        // 验证收货单状态是否更新为已完成
        DdReceiveDoc doc = ddReceiveDocMapper.selectByPrimaryKey(41L);
        assertEquals("收货单状态已更新", Integer.valueOf(1), doc.getDocStatus());

        // 验证收货日志是否生成
        DdReceiveDocLog logQuery = new DdReceiveDocLog();
        logQuery.setDocId(41L);
        List<DdReceiveDocLog> logs = ddReceiveDocLogMapper.select(logQuery);
        assertEquals("收货日志已生成", true, logs.size() > 0);

        // 验证门店收货单状态
        ShopReceiveOrder shopReceiveOrder = new ShopReceiveOrder();
        shopReceiveOrder.setSubOrderId(197233293L);
        List<ShopReceiveOrder> shopReceiveOrders = shopReceiveOrderMapper.select(shopReceiveOrder);
        assertEquals("门店收货单状态已更新", Integer.valueOf(3), shopReceiveOrders.get(0).getStatus());
    }

    @Test
    public void testRecordSingleCommodity_ValidationFailure() {
        // 测试参数校验失败的情况
        DdReceiveDocRecordIDTO idto = new DdReceiveDocRecordIDTO();
        // 故意不设置必要参数，触发校验失败
        idto.setShopId(100052L);
        idto.setStallId(16L);
        idto.setDocId(41L);
        // 不设置commodityId，应该触发校验失败

        try {
            bigShopReceiveService.recordSingleCommodity(idto);
            // 如果没有抛出异常，测试失败
            assertEquals("应该抛出异常", true, false);
        } catch (Exception e) {
            // 验证抛出了预期的异常
            assertEquals("抛出了预期异常", true, true);
        }
    }

    @Test
    public void testPdaBigShopReceive_DocNotFound() {
        // 测试收货单不存在的情况
        DdReceiveDocBatchRecordIDTO idto = new DdReceiveDocBatchRecordIDTO();
        idto.setDocId(99999L); // 不存在的单据ID

        try {
            bigShopReceiveService.pdaBigShopReceive(idto);
            // 如果没有抛出异常，测试失败
            assertEquals("应该抛出异常", true, false);
        } catch (Exception e) {
            // 验证抛出了预期的异常
            assertEquals("抛出了预期异常", true, true);
        }
    }

    @Test
    public void testRecordSingleCommodity_DocCompleted() {
        // 首先将收货单状态设置为已完成
        DdReceiveDoc doc = ddReceiveDocMapper.selectByPrimaryKey(41L);
        doc.setDocStatus(1); // 设置为已完成
        ddReceiveDocMapper.updateByPrimaryKey(doc);

        DdReceiveDocRecordIDTO idto = new DdReceiveDocRecordIDTO();
        idto.setShopId(100052L);
        idto.setStallId(16L);
        idto.setDocId(41L);
        idto.setCommodityId(32289764792662004L);
        idto.setRealReceiveQuantity(new BigDecimal("5.000"));
        idto.setStorageArea(1);

        try {
            bigShopReceiveService.recordSingleCommodity(idto);
            // 如果没有抛出异常，测试失败
            assertEquals("应该抛出异常", true, false);
        } catch (Exception e) {
            // 验证抛出了预期的异常
            assertEquals("抛出了预期异常", true, true);
        } finally {
            // 恢复收货单状态
            doc.setDocStatus(0);
            ddReceiveDocMapper.updateByPrimaryKey(doc);
        }
    }

    @Test
    public void testPdaBigShopReceive_EmptyDoc() {
        // 创建一个没有商品的收货单进行测试
        // 这种情况下应该返回false或者不执行任何操作

        // 准备空的货位信息
        when(goodsAllocationClient.queryGoodsAllocationByIdList(any())).thenReturn(new ArrayList<>());

        DdReceiveDocBatchRecordIDTO idto = new DdReceiveDocBatchRecordIDTO();
        idto.setDocId(41L);

        // 由于没有未登记的商品，应该不会执行收货操作
        Boolean result = bigShopReceiveService.pdaBigShopReceive(idto);

        // 根据实际业务逻辑，这里可能返回false或true
        // 需要根据具体实现来验证
        assertEquals("处理完成", true, result != null);
    }


}
