package com.pinshang.qingyun.order.service;/**
 * @Author: sk
 * @Date: 2025/8/18
 */

import com.pinshang.qingyun.ApplicationOrderService;
import com.pinshang.qingyun.common.service.DelayMsgClient;
import com.pinshang.qingyun.infrastructure.springcloud.common.ComponentConfigLoad;
import com.pinshang.qingyun.infrastructure.springcloud.common.ComponentManageConfig;
import com.pinshang.qingyun.infrastructure.springcloud.common.event.ComponentListener;
import com.pinshang.qingyun.infrastructure.test.BaseDbUnitTest;
import com.pinshang.qingyun.order.dto.bigShop.DdReceiveDocCommodityODTO;
import com.pinshang.qingyun.order.dto.bigShop.DdReceiveDocODTO;
import com.pinshang.qingyun.order.dto.bigShop.DdReceiveDocSaveIDTO;
import com.pinshang.qingyun.order.mapper.ShopReceiveOrderMapper;
import com.pinshang.qingyun.order.mapper.SubOrderItemMapper;
import com.pinshang.qingyun.order.mapper.bigShop.DdReceiveDocLogMapper;
import com.pinshang.qingyun.order.mapper.bigShop.DdReceiveDocOrderMapper;
import com.pinshang.qingyun.order.mapper.bigShop.DdReceiveDocPicMapper;
import com.pinshang.qingyun.order.model.bigShop.DdReceiveDocLog;
import com.pinshang.qingyun.order.model.bigShop.DdReceiveDocOrder;
import com.pinshang.qingyun.order.model.bigShop.DdReceiveDocPic;
import com.pinshang.qingyun.order.model.order.SubOrderItem;
import com.pinshang.qingyun.order.model.shop.ShopReceiveOrder;
import com.pinshang.qingyun.order.service.bigShop.BigShopReceiveService;
import com.pinshang.qingyun.order.vo.order.SaleReturnOrderPicVO;
import com.pinshang.qingyun.renderer.service.IRenderService;
import com.pinshang.qingyun.shop.service.bigShop.StallClient;
import com.pinshang.qingyun.smm.service.UserStallClient;
import com.pinshang.qingyun.xd.wms.dto.bigShop.GoodsAllocationODTO;
import com.pinshang.qingyun.xd.wms.service.XdStockClient;
import com.pinshang.qingyun.xd.wms.service.bigShop.GoodsAllocationClient;
import org.junit.jupiter.api.Test;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.context.annotation.Import;
import org.springframework.mail.javamail.JavaMailSenderImpl;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

import static org.junit.Assert.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

/**
 * <AUTHOR>
 * @date 2025年08月18日 下午4:26
 */
@SpringBootTest(classes = {ApplicationOrderService.class, BaseDbUnitTest.Application.class},
        properties = {"application.name.switch=unit-test-","pinshang.img-server-url=xxx",
                "pinshang.img-xd-server-url=xxx","print.path.rootPath=xxx","print.path.upPath=xxx",
                "print.path.url=xxx","pinshang.xda.isThTips=xxx"})
@Import(value = {BigShopReceiveService.class})
public class BigShopReceiveServiceTest extends BaseDbUnitTest{

    @MockBean
    IRenderService renderService;
    @MockBean
    RLock lock;
    @MockBean
    RedissonClient redissonClient;
    @MockBean
    private JavaMailSenderImpl javaMailSender;
    @MockBean
    ComponentManageConfig componentManageConfig;
    @MockBean
    ComponentListener componentListener;
    @MockBean
    ComponentConfigLoad componentConfigLoad;
    @MockBean
    XdStockClient xdStockClient;
    @MockBean
    private UserStallClient userStallClient;
    @MockBean
    StallClient stallClient;
    @MockBean
    GoodsAllocationClient goodsAllocationClient;
    @MockBean
    private DelayMsgClient delayMsgClient;


    @Autowired
    private BigShopReceiveService bigShopReceiveService;
    @Autowired
    private DdReceiveDocLogMapper ddReceiveDocLogMapper;
    @Autowired
    private SubOrderItemMapper subOrderItemMapper;
    @Autowired
    private ShopReceiveOrderMapper shopReceiveOrderMapper;
    @Autowired
    private DdReceiveDocOrderMapper ddReceiveDocOrderMapper;
    @Autowired
    private DdReceiveDocPicMapper ddReceiveDocPicMapper;

    @Override
    protected List<String> getInitSqlScripts() {
        // 指定自定义初始化脚本
        return Arrays.asList(
                "db/bigShopReceiveOrder.sql"
        );
    }


    @Test
    public void getReceiveDocCommodityList() {
        List<GoodsAllocationODTO> stallODTOList = new ArrayList<>();
        when(goodsAllocationClient.queryGoodsAllocationByIdList(any())).thenReturn(stallODTOList);

        DdReceiveDocODTO ddReceiveDocODTO = bigShopReceiveService.getReceiveDocCommodityList(41L, null, false);

        assertEquals("收货明细",ddReceiveDocODTO != null && ddReceiveDocODTO.getCommodityList().size() > 0, true);

        ddReceiveDocODTO.getCommodityList().forEach(commodityODTO -> {
            if(commodityODTO.getCommodityId().equals(32289764792662004L)) {
                assertEquals("是否PDA收货",commodityODTO.getPdaStatus() == 1, true);

                assertEquals("图片list",commodityODTO.getPicList().size() > 0, true);
            }
        });
    }

    @Test
    public void addBigShopReceive() {
        Long docId = 41L;
        DdReceiveDocSaveIDTO idto = new DdReceiveDocSaveIDTO();
        idto.setDocId(docId);

        List<DdReceiveDocCommodityODTO> commodityList = new ArrayList<>();
        DdReceiveDocCommodityODTO commodity1 = new DdReceiveDocCommodityODTO();
        commodity1.setCommodityId("32289764792662004");
        commodity1.setCommodityCode("000171");
        commodity1.setCommodityName("大油豆腐");
        commodity1.setStorageArea(1);
        commodity1.setRealReceiveQuantity(new BigDecimal("9"));
        commodity1.setRealDeliveryQuantity(new BigDecimal("10"));

        List<SaleReturnOrderPicVO> picList = new ArrayList<>();
        SaleReturnOrderPicVO pic = new SaleReturnOrderPicVO();
        pic.setPicUrl("https://picsum.photos/200/300");
        picList.add( pic);
        commodity1.setPicList(picList);
        commodityList.add(commodity1);


        DdReceiveDocCommodityODTO commodity2 = new DdReceiveDocCommodityODTO();
        commodity2.setCommodityId("864419333914674304");
        commodity2.setStorageArea(1);
        commodity2.setCommodityCode("000072");
        commodity2.setCommodityName("素肠");
        commodity2.setRealReceiveQuantity(new BigDecimal("1"));
        commodity2.setRealDeliveryQuantity(new BigDecimal("1"));
        commodityList.add(commodity2);

        idto.setCommodityList(commodityList);
        bigShopReceiveService.addBigShopReceive(idto);



        // 验证结果

        DdReceiveDocLog ddReceiveDocLog = new DdReceiveDocLog();
        ddReceiveDocLog.setDocId(docId);
        List<DdReceiveDocLog> logList = ddReceiveDocLogMapper.select(ddReceiveDocLog);
        assertEquals("收货单日志数量等于2",logList.size() == 2, true);

        logList.forEach(log -> {
            if(log.getCommodityId().equals(32289764792662004L)) {
                assertEquals("32289764792662004 实收",log.getRealReceiveQuantity().compareTo(new BigDecimal("9")) == 0, true);
            }

            if(log.getCommodityId().equals(864419333914674304L)) {
                assertEquals("864419333914674304 实收",log.getRealReceiveQuantity().compareTo(new BigDecimal("1")) == 0, true);
            }
        });

        DdReceiveDocOrder ddReceiveDocOrder = new DdReceiveDocOrder();
        ddReceiveDocOrder.setDocId(docId);
        List<DdReceiveDocOrder> ddReceiveDocOrderList = ddReceiveDocOrderMapper.select(ddReceiveDocOrder);
        assertEquals("收货单订单记录表",ddReceiveDocOrderList.size() == 1, true);

        List<String> orderCodeList = ddReceiveDocOrderList.stream().map(item -> item.getOrderCode()).collect(Collectors.toList());
        assertEquals("收货单订单记录表2,订单已记录 ",orderCodeList.contains("1755505872342396283"), true);


        SubOrderItem subOrderItem = new SubOrderItem();
        subOrderItem.setSubOrderId(197233293L);
        List<SubOrderItem> subOrderItems = subOrderItemMapper.select(subOrderItem);
        subOrderItems.forEach(item -> {

            if(item.getCommodityId().equals(32289764792662004L)) {
                assertEquals("子单实收数量 32289764792662004 实收",item.getRealReceiveQuantity().compareTo(new BigDecimal("9")) == 0, true);
            }

            if(item.getCommodityId().equals(864419333914674304L)) {
                assertEquals("子单实收数量 864419333914674304 实收",item.getRealReceiveQuantity().compareTo(new BigDecimal("1")) == 0, true);
            }
        });


        ShopReceiveOrder shopReceiveOrder = new ShopReceiveOrder();
        shopReceiveOrder.setSubOrderId(197233293L);
        List<ShopReceiveOrder> shopReceiveOrderList = shopReceiveOrderMapper.select(shopReceiveOrder);
        assertEquals("子单收货单 已经完成收货 ",shopReceiveOrderList.get(0).getStatus().equals(3), true);

        DdReceiveDocPic ddReceiveDocPic = new DdReceiveDocPic();
        ddReceiveDocPic.setDocId(docId);
        ddReceiveDocPic.setReceiveType(2);
        List<DdReceiveDocPic> pics = ddReceiveDocPicMapper.select(ddReceiveDocPic);
        assertEquals("收货图片",pics.size() == 1, true);

    }
}
