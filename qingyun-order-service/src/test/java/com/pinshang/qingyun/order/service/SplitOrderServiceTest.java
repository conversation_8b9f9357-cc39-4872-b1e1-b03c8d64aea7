package com.pinshang.qingyun.order.service;

import com.pinshang.qingyun.ApplicationOrderService;
import com.pinshang.qingyun.infrastructure.springcloud.common.ComponentConfigLoad;
import com.pinshang.qingyun.infrastructure.springcloud.common.ComponentManageConfig;
import com.pinshang.qingyun.infrastructure.springcloud.common.event.ComponentListener;
import com.pinshang.qingyun.infrastructure.test.BaseDbUnitTest;
import com.pinshang.qingyun.order.mapper.SubOrderItemMapper;
import com.pinshang.qingyun.order.mapper.SubOrderMapper;
import com.pinshang.qingyun.order.model.order.SubOrder;
import com.pinshang.qingyun.order.model.order.SubOrderItem;
import com.pinshang.qingyun.renderer.service.IRenderService;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.context.annotation.Import;
import org.springframework.mail.javamail.JavaMailSenderImpl;

import java.util.List;
import com.pinshang.qingyun.ApplicationOrderService;
import com.pinshang.qingyun.infrastructure.springcloud.common.ComponentConfigLoad;
import com.pinshang.qingyun.infrastructure.springcloud.common.ComponentManageConfig;
import com.pinshang.qingyun.infrastructure.test.BaseDbUnitTest;
import com.pinshang.qingyun.order.mapper.SubOrderItemMapper;
import com.pinshang.qingyun.order.mapper.SubOrderMapper;
import com.pinshang.qingyun.order.model.order.SubOrder;
import com.pinshang.qingyun.order.model.order.SubOrderItem;
import com.pinshang.qingyun.renderer.service.IRenderService;
import org.junit.jupiter.api.Test;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.context.annotation.Import;
import org.springframework.mail.javamail.JavaMailSenderImpl;

import java.util.Arrays;
import java.util.List;

@SpringBootTest(classes = {ApplicationOrderService.class, BaseDbUnitTest.Application.class},
        properties = {"application.name.switch=unit-test-","pinshang.img-server-url=xxx",
                "pinshang.img-xd-server-url=xxx","print.path.rootPath=xxx","print.path.upPath=xxx",
                "print.path.url=xxx","pinshang.xda.isThTips=xxx"})
@Import(value = {SplitOrderService.class})
public class SplitOrderServiceTest extends BaseDbUnitTest {

    @Autowired
    private SplitOrderService splitOrderService;
    @Autowired
    private SubOrderMapper subOrderMapper;
    @Autowired
    private SubOrderItemMapper subOrderItemMapper;

    @MockBean
    IRenderService renderService;
    @MockBean
    RLock lock;
    @MockBean
    RedissonClient redissonClient;
    @MockBean
    private JavaMailSenderImpl javaMailSender;
    @MockBean
    ComponentManageConfig componentManageConfig;
    @MockBean
    ComponentListener componentListener;
    @MockBean
    ComponentConfigLoad componentConfigLoad;
    @Override
    protected List<String> getInitSqlScripts() {
        // 指定自定义初始化脚本
        return Arrays.asList(
                "db/splitOrder.sql"
        );
    }

    @Test
    public void testSaveMtCoupon() {
        splitOrderService.splitOrderByJob("2025-07-07");

        SubOrder subOrder = new SubOrder();
        subOrder.setOrderId(161454897L);
        List<SubOrder> subOrders = subOrderMapper.select( subOrder);
        System.out.println("subOrders大小" + subOrders.size());

        for (SubOrder subOrder1 : subOrders) {
            System.out.println(subOrder1.getId());

            SubOrderItem item = new SubOrderItem();
            item.setSubOrderId(subOrder1.getId());
            List<SubOrderItem> itemList = subOrderItemMapper.select( item);
            System.out.println("itemList大小" + itemList.size());
        }
    }
}
